# Zone Utilisation Analytics Card - Mixed Concept Recommendation

This document outlines the recommended metrics and UI structure for the "Zone Utilisation" analytics card, combining objective space busyness with insights into member engagement, leveraging CCTV computer vision with individual identification.

---

## Card Title: **Zone Utilisation**

### Primary Metric (Most Prominent):

**1. Average Zone Utilisation Rate (%)**

*   **Description:** The average percentage of a zone's **defined human capacity** that is utilized by detected individuals (regardless of specific identity) over a specified time period. This provides a direct measure of how "full" a zone is relative to its physical limits.
*   **Derivation:**
    *   **Zone ROIs & Capacity:** Each zone has a predefined Region of Interest (ROI) and a manually set maximum human capacity (e.g., "Cardio Zone A can hold 15 people").
    *   **Real-time Human Counting:** The computer vision (CV) system continuously counts the number of unique human figures present within each zone's ROI.
    *   **Instantaneous Utilisation:** At frequent intervals (e.g., every 10-60 seconds), calculate `(Current Human Count in Zone / Zone Capacity) * 100%`.
    *   **Time-Weighted Averaging:** These instantaneous percentages are averaged over the selected reporting period (e.g., an hour, a day, a week) to get the overall average for the card.
*   **Why it's Essential:** This is the core metric for managing spatial efficiency, avoiding overcrowding, and making real-time operational decisions based on the "busyness" of the physical space.

### Supporting Metrics (Leveraging Individual Identification):

**2. Unique Visitors / Footfall Count**

*   **Description:** The total number of **distinct members/individuals** who entered or were detected within *any* zone across the facility during the selected time period.
*   **Derivation:** Your existing individual identification and tracking system counts unique member IDs as they enter defined zone ROIs or facility entry points. Each unique ID contributes once to the count for the period.
*   **Why it's Valuable:** Complements the utilisation rate by showing the *breadth* of usage – how many different people are engaging with the zones. It indicates overall member traffic and popularity of the facility's spaces.

**3. Average Member Dwell Time (Across all Zones)**

*   **Description:** The average cumulative duration that unique members spend *across all zones they visit* during their entire facility session for the selected period.
*   **Derivation:** Requires robust individual member tracking (via CV) for entry and exit times for each zone. For each tracked individual, calculate the sum of their durations in all zones they entered within their overall facility visit. Then, average these sums across all unique members for the period.
*   **Why it's Valuable:** An indicator of engagement and satisfaction. Longer average dwell times suggest members are actively using various parts of the facility and finding value in their visit.

**4. Top Zones by Visits**

*   **Description:** A concise list highlighting the most popular zones, ranked by the number of unique visitors they received during the period.
*   **Derivation:** Based on the unique visitor data collected for each individual zone. Sort zones by their unique visitor count in descending order.
*   **Why it's Valuable:** Helps managers quickly identify which areas of the facility are most in demand by visitor count, informing decisions about staffing, equipment distribution, and marketing.

**5. Top Zones by Utilisation**

*   **Description:** A concise list highlighting the zones with the highest utilisation rates, ranked by their average utilisation percentage during the period.
*   **Derivation:** Based on the individual zone utilisation rates calculated for each zone. Sort zones by their average utilisation rate in descending order.
*   **Why it's Valuable:** Identifies which zones are most intensely used relative to their capacity. This can be different from visitor count - a smaller zone might have fewer visitors but higher utilisation rate. Helps identify potential bottlenecks, overcrowding issues, and zones that might benefit from capacity expansion.

---

## Example Card Structure:

```
┌───────────────────────────────────────┐
│ **Zone Utilisation**                  │
│                                       │
│    **73% Average Utilisation**        │
│    _Relative to defined capacities_   │
│                                       │
│    **155 Unique Visitors Today**      │
│    Avg Dwell Time: **1 hr 15 min**    │
│                                       │
│    Top by Visits:                     │
│    1. Cardio Zone (85 unique)         │
│    2. Free Weights (78 unique)        │
│                                       │
│    Top by Utilisation:                │
│    1. Yoga Studio (94%)               │
│    2. Cardio Zone (87%)               │
│                                       │
│    [View Zone Details & Map >]        │
└───────────────────────────────────────┘
``` 