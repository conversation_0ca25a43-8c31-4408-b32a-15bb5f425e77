# Facility Insights Report - Essential Features

This document outlines essential data points and UI components for comprehensive facility monitoring.

## Date Range Filtering

### Filter Options
- **Quick Filters**: Today, Yesterday, Last 7 days, Last 30 days, This month, Last month
- **Custom Date Range**: Start date and end date picker
- **Time Period Presets**: 1 week, 2 weeks, 1 month, 3 months, 6 months
- **Real-time Toggle**: Switch between live data and historical data view

## Real-Time Monitoring & Current Status

### Overall Status
- **Overall Cleanliness Score**: Current facility-wide cleanliness rating (live) or average for selected date range
- **Overall Zone Utilization**: Real-time percentage of facility zones actively in use or average for selected date range.
- **Overall Equipment Activity**: Real-time percentage of equipment currently in use or average for selected date range.

### Facility Map View (Enhanced)
- **Interactive Facility Map**: Showing each camera location, but now also indicating:
    - **Cleanliness Score Overlay**: Individual scores per camera/zone.
    - **Zone Usage Heatmap**: Visual representation of activity density in each zone (live or averaged over date range).
    - **Equipment Status Overlays**: Icons or color coding indicating equipment cleanliness/usage status within zones.
- **Immediate Attention Items**: Priority-ranked list of areas requiring cleaning, or zones/equipment needing attention (filtered by date range).

### Priority Alerts (Expanded)
- 🔴 **HIGH**: Critical issues (cleanliness, overcrowding, equipment malfunction) requiring immediate attention
- 🟡 **MEDIUM**: Areas that need cleaning, zones approaching capacity, or equipment nearing maintenance
- 🟢 **LOW**: Minor tidying required, underutilized zones, routine equipment checks
- **Alert History**: View past alerts within selected date range (with categorization by type: cleanliness, usage, equipment).

## Equipment Insights (Enhanced)

### Equipment Status
- **Equipment Cleanliness Score**: Individual scores for different equipment zones (current or averaged over date range)
- **Equipment Usage Rate**: Percentage of time equipment is actively in use (current or averaged over date range).
- **Disorganized Equipment**: List of equipment areas needing attention (filtered by date range).
- **Equipment Usage Patterns**: When equipment typically becomes disorganized or is most heavily used within selected period.
- **Equipment Health/Maintenance Status**: Indicators for equipment needing maintenance or showing signs of wear.

### Visual Analytics
- **Pie Charts**: Percentage of equipment areas that are clean vs. need attention (for selected date range), and percentage of equipment utilized vs. idle.
- **Equipment Trend**: Organization status and usage patterns over time for specific equipment types (within date filter).
- **Comparative Analysis**: Compare current period with previous period of same duration for cleanliness, usage, and maintenance.
- **Equipment Downtime**: Total time equipment is out of service for maintenance or disorganization.

## Zone Utilization & Traffic Flow (New Section)

### Zone Activity Metrics
- **Zone Occupancy Rate**: Average number of people or percentage capacity for each zone (current or averaged over date range).
- **Peak Usage Times**: Identify the busiest hours/days for each facility zone.
- **Dwell Time**: Average duration users spend in specific zones.
- **Traffic Flow Paths**: Common movement patterns between zones (can be visualized on map).
- **Underutilized Zones**: Identification of zones with consistently low activity.

### Visual Analytics
- **Zone Usage Heatmaps**: Detailed visual representation of traffic density and activity patterns within selected zones over time.
- **Zone Occupancy Charts**: Bar or line charts showing occupancy rates for different zones throughout a selected period.
- **Flow Diagrams**: Visualizing user movement between interconnected zones.
- **Comparative Zone Analysis**: Compare usage patterns of similar zones or a zone's current usage to historical averages.

## CCTV Monitoring (Enhanced)

### Camera Feed Interface
- **Live Feed**: Real-time camera views with cleanliness scores and real-time occupancy estimates.
- **Playback Timeline**: Review past footage with timeline scrubber (within selected date range).
- **Heatmap Overlay**: Visual representation of problem areas (cleanliness) and high-traffic zones (toggle on/off, filtered by date).
- **Issue Detection**: Automated alerts for cleanliness problems, overcrowding, or unusual activity (historical view with date filtering).

### Historical Analysis
- **Date-Filtered Playback**: Jump to specific dates within selected range.
- **Issue Frequency**: How often problems (cleanliness, overcrowding, equipment issues) occurred in each camera zone during selected period.
- **Peak Problem Times**: Identify when issues most commonly occur within date range.
- **Zone Activity Trends**: Historical trends of occupancy and activity levels for specific zones.

## Reporting & Analytics (Enhanced)

### Time-Based Reports
- **Daily Trends**: Score changes throughout selected date range for cleanliness, zone utilization, and equipment activity.
- **Weekly Patterns**: Recurring issues by day/time for cleanliness, zone usage, and equipment problems (within filtered period).
- **Problem Areas**: Cameras/zones/equipment with most frequent issues or highest disorganization during selected timeframe.
- **Trend Comparison**: Compare selected period with previous equivalent period across all metrics.

### Performance Metrics
- **Average Scores**: Mean cleanliness, zone utilization, and equipment uptime scores for selected date range.
- **Improvement Tracking**: Score changes over time within filtered period for all metrics.
- **Issue Resolution Time**: How quickly problems (cleanliness, equipment, etc.) were addressed during selected period.
- **Compliance Rates**: Percentage of time facility met cleanliness/usage/maintenance standards.
- **ROI Metrics**: Potential section for linking insights to operational cost savings or revenue generation.

### Export & Documentation
- **Date-Filtered Reports**: Generate comprehensive reports for selected time period including cleanliness, zone usage, and equipment metrics.
- **Issue History**: Track resolution of all types of issues (cleanliness, equipment, etc.) within date range.
- **Integration**: Coordinate with facility maintenance and scheduling systems (filtered by date).
- **Scheduled Reports**: Automatically generate reports for specific date ranges.

---

*Focus: Essential facility cleanliness monitoring with comprehensive date filtering and historical analysis capabilities.*

Space Utilisation & Equipment Usage Metrics:
Zone Occupancy Trends:

* Track footfall by zone. (Unique visitors in Zone over time)
* Identify peak usage hours and dead zones. (Chart number of visitors in 15/30/60min blocks per zone over a time period) Why: Identify zones that consistently remain unused.
  * Unique Visitors
  * Total Visitors
* Cross-reference "people" and Zones to visualise habitual usage patterns. (How long does an individual spend in a zone? ).
  * Change the text "Seen on these cameras:" to "Seen in these zones:" in the activity log screen
* Cross reference Sentiment and Zones over time

Facility metrics:
  * Facility temperature (Assuming this needs IOT devices)
  * Cleanliness per zone charted
    * when the zone was last cleaned
    * fill bins?
 * technology offline while people training (tv's off when they should always be on if people are in the gym)

Once Equipment can be labeled and identified
Equipment Dwell Time
  * Measure average time individuals spend on key machines.
  * Detect anomalies like excessive usage or extended idle (unused) time.

Equipment Popularity Index
  * Rank equipment by frequency of use.
  * Report underutilised gear for possible relocation or removal.