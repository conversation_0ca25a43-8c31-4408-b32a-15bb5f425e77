---
description: 
globs: 
alwaysApply: false
---
description: Enforce global context awareness, proactive concurrency checks, and DRY validation for all code changes
globs: **/*
alwaysApply: true
---

- **Maintain a Global Perspective Before Coding**
    - Before proposing or editing any file, **summarise the surrounding architecture** (related modules, workers, jobs, endpoints, or DB tables) that the change may interact with.
    - Explicitly note cross-file or cross-service interactions (e.g. two workers sharing the same state column).

- **Proactive Concurrency & Side-Effect Review**
    - For any change touching asynchronous code, background jobs, DB state, or shared resources, add a **"Concurrency Review" subsection** in the implementation plan that lists potential race conditions and mitigation strategies.
    - Example: Updating a data-ingestion job must evaluate how the change affects any downstream processing worker that relies on the same state field.

- **Systematic Validation Passes After Drafting Code**
    1. **DRY / Re-use Check:** Identify duplicated logic and propose abstraction utilities.
    2. **Security Check:** Note auth, input validation, and permission impacts.
    3. **Error-Handling Check:** Ensure graceful failure paths are present.
    4. **Performance Check:** Highlight N+1 queries, blocking awaits, etc.

- **Architectural Nudge Simulation**
    - If the user prompt focuses narrowly on a single file/function, **ask at least one clarifying question** about broader impact *unless* the global context is already evident.
    - This prevents tunnel vision and surfaces hidden architectural concerns early.

- **Implementation Plan Template (minimum sections)**
    1. Local Change Summary
    2. Global Impact Analysis (list affected modules/services)
    3. Concurrency Review (race conditions & locks)
    4. DRY / Abstraction Opportunities
    5. Security & Error-Handling Considerations

- **Anti-Pattern (Do NOT)**
    - Provide only the local file diff without mentioning how other workers interact.
    - Duplicate pre-flight checks in each worker instead of extracting a helper.
