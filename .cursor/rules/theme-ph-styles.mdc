---
description: For UI or style updates
globs: 
alwaysApply: false
---
# `theme-ph.scss` Styling Guidelines

This document outlines the correct and consistent usage of styling variables and classes defined in `wiki-themes/12rnd/scss/theme-ph.scss` for all UI development within the project. Adhering to these guidelines ensures visual consistency and maintainability.

## 🎨 Color Usage

### **`$slate` Colors (For Text and Backgrounds)**
- **Purpose**: Primarily used for text colors and various background levels.
- **Spectrum**: Ranges from `$slate-25` (very light) to `$slate-800` (very dark).
- **Usage**:
    - For general text, use `$primary-text-color` or specific `$slate-*` values like `$slate-600`.
    - For secondary/muted text, use `$secondary-text-color` or `$slate-400`.
    - For layered backgrounds (e.g., card backgrounds, panel backgrounds), use lighter `$slate-*` values like `$slate-25`, `$slate-50`, `$slate-75`, or `white`.
```scss
// ✅ DO: Using slate colors for text and backgrounds
color: $slate-600;
background-color: $slate-50;
```
```scss
// ❌ DON'T: Using other generic gray/dark/light colors
color: #333;
background-color: #f0f0f0;
```

### **`$surface` Colors (For Borders Only)**
- **Purpose**: Exclusively used for borders and lines.
- **Spectrum**: `$surface-25` through `$surface-200`.
- **Key Uses**:
    - `$border-color`: Alias for `$surface-100`, used for most general borders.
    - `$tab-border-color`: Alias for `$surface-75`, used for tab-related borders.
```scss
// ✅ DO: Using surface colors for borders
border: 1px solid $surface-100;
border-bottom: 2px solid $tab-border-color;
```
```scss
// ❌ DON'T: Using surface colors for backgrounds
background-color: $surface-50; // Use $slate-50 or white instead
```

### **`$accent-blue` Colors (For Interactive Elements)**
- **Purpose**: Used for primary interactive elements, highlights, and active states.
- **Spectrum**: `$accent-blue-10` (very light) to `$accent-blue-700` (very dark).
- **Key Uses**: Buttons, links, active menu items, input focus states.
```scss
// ✅ DO: Using accent-blue for interactive elements
color: $accent-blue-500;
background-color: $accent-blue-50;
border-color: $accent-blue-300;
```

## ✨ Shadows and Borders

### **Shadows (`$natural-shadow-*`)**
- **Purpose**: Apply consistent elevation and depth to UI elements.
- **Variables**: `$natural-shadow-lg`, `$natural-shadow-md`, `$natural-shadow-sm`, `$natural-shadow-xs`.
- **Usage**: Apply via `box-shadow` property.
```scss
// ✅ DO: Applying consistent shadows
box-shadow: $natural-shadow-xs;
```

### **Border Radius (`$border_radius-*`)**
- **Purpose**: Ensure consistent rounded corners across components.
- **Variables**: `$border_radius-xs` to `$border_radius-xl`.
- **Key Uses**: `$card-border-radius`, `$border-radius-input`.
```scss
// ✅ DO: Using predefined border radius
border-radius: $border_radius-m;
border-radius: $card-border-radius;
```

## 📦 Component-Specific Styling

- **Cards**:
    - Use `.ph-card`, `.ph-card_header`, `.ph-card_body` classes for structure.
    - Apply `$base-card-padding` and `$card-padding` for spacing.
    - Borders should use `$surface-100` (`$border-color`).
    - Backgrounds should use slate colors or `white`.
- **Forms and Inputs**:
    - Inputs should use `$input-border-color`, `$border-radius-input`, and `$input-shadow`.
    - Labels and small text should use `$slate-450` or `$secondary-text-color`.
- **Buttons**:
    - Use `.ph-btn` class with modifiers like `.ph-btn-warning`, `.btn-outline`.
    - Utilize accent colors (`$accent-blue-*`) for primary actions.

## 🔗 Referencing `theme-ph.scss`

Always import `theme-ph.scss` into any new SCSS files that require its variables or mixins.
```scss
// ✅ DO: Import theme-ph.scss
@import 'wiki-themes/12rnd/scss/theme-ph';
```
