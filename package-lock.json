{"name": "wiki", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"algoliasearch": "^4.20.0", "js-yaml": "^4.1.0", "node-fetch": "^3.3.1"}}, "node_modules/@algolia/cache-browser-local-storage": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/cache-browser-local-storage/-/cache-browser-local-storage-4.20.0.tgz", "integrity": "sha512-uujahcBt4DxduBTvYdwO3sBfHuJvJokiC3BP1+O70fglmE1ShkH8lpXqZBac1rrU3FnNYSUs4pL9lBdTKeRPOQ==", "dependencies": {"@algolia/cache-common": "4.20.0"}}, "node_modules/@algolia/cache-common": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/cache-common/-/cache-common-4.20.0.tgz", "integrity": "sha512-vCfxauaZutL3NImzB2G9LjLt36vKAckc6DhMp05An14kVo8F1Yofb6SIl6U3SaEz8pG2QOB9ptwM5c+zGevwIQ=="}, "node_modules/@algolia/cache-in-memory": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/cache-in-memory/-/cache-in-memory-4.20.0.tgz", "integrity": "sha512-Wm9ak/IaacAZXS4mB3+qF/KCoVSBV6aLgIGFEtQtJwjv64g4ePMapORGmCyulCFwfePaRAtcaTbMcJF+voc/bg==", "dependencies": {"@algolia/cache-common": "4.20.0"}}, "node_modules/@algolia/client-account": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/client-account/-/client-account-4.20.0.tgz", "integrity": "sha512-GGToLQvrwo7am4zVkZTnKa72pheQeez/16sURDWm7Seyz+HUxKi3BM6fthVVPUEBhtJ0reyVtuK9ArmnaKl10Q==", "dependencies": {"@algolia/client-common": "4.20.0", "@algolia/client-search": "4.20.0", "@algolia/transporter": "4.20.0"}}, "node_modules/@algolia/client-analytics": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/client-analytics/-/client-analytics-4.20.0.tgz", "integrity": "sha512-EIr+PdFMOallRdBTHHdKI3CstslgLORQG7844Mq84ib5oVFRVASuuPmG4bXBgiDbcsMLUeOC6zRVJhv1KWI0ug==", "dependencies": {"@algolia/client-common": "4.20.0", "@algolia/client-search": "4.20.0", "@algolia/requester-common": "4.20.0", "@algolia/transporter": "4.20.0"}}, "node_modules/@algolia/client-common": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/client-common/-/client-common-4.20.0.tgz", "integrity": "sha512-P3WgMdEss915p+knMMSd/fwiHRHKvDu4DYRrCRaBrsfFw7EQHon+EbRSm4QisS9NYdxbS04kcvNoavVGthyfqQ==", "dependencies": {"@algolia/requester-common": "4.20.0", "@algolia/transporter": "4.20.0"}}, "node_modules/@algolia/client-personalization": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/client-personalization/-/client-personalization-4.20.0.tgz", "integrity": "sha512-N9+zx0tWOQsLc3K4PVRDV8GUeOLAY0i445En79Pr3zWB+m67V+n/8w4Kw1C5LlbHDDJcyhMMIlqezh6BEk7xAQ==", "dependencies": {"@algolia/client-common": "4.20.0", "@algolia/requester-common": "4.20.0", "@algolia/transporter": "4.20.0"}}, "node_modules/@algolia/client-search": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/client-search/-/client-search-4.20.0.tgz", "integrity": "sha512-zgwqnMvhWLdpzKTpd3sGmMlr4c+iS7eyyLGiaO51zDZWGMkpgoNVmltkzdBwxOVXz0RsFMznIxB9zuarUv4TZg==", "dependencies": {"@algolia/client-common": "4.20.0", "@algolia/requester-common": "4.20.0", "@algolia/transporter": "4.20.0"}}, "node_modules/@algolia/logger-common": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/logger-common/-/logger-common-4.20.0.tgz", "integrity": "sha512-xouigCMB5WJYEwvoWW5XDv7Z9f0A8VoXJc3VKwlHJw/je+3p2RcDXfksLI4G4lIVncFUYMZx30tP/rsdlvvzHQ=="}, "node_modules/@algolia/logger-console": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/logger-console/-/logger-console-4.20.0.tgz", "integrity": "sha512-THlIGG1g/FS63z0StQqDhT6bprUczBI8wnLT3JWvfAQDZX5P6fCg7dG+pIrUBpDIHGszgkqYEqECaKKsdNKOUA==", "dependencies": {"@algolia/logger-common": "4.20.0"}}, "node_modules/@algolia/requester-browser-xhr": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/requester-browser-xhr/-/requester-browser-xhr-4.20.0.tgz", "integrity": "sha512-HbzoSjcjuUmYOkcHECkVTwAelmvTlgs48N6Owt4FnTOQdwn0b8pdht9eMgishvk8+F8bal354nhx/xOoTfwiAw==", "dependencies": {"@algolia/requester-common": "4.20.0"}}, "node_modules/@algolia/requester-common": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/requester-common/-/requester-common-4.20.0.tgz", "integrity": "sha512-9h6ye6RY/BkfmeJp7Z8gyyeMrmmWsMOCRBXQDs4mZKKsyVlfIVICpcSibbeYcuUdurLhIlrOUkH3rQEgZzonng=="}, "node_modules/@algolia/requester-node-http": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/requester-node-http/-/requester-node-http-4.20.0.tgz", "integrity": "sha512-ocJ66L60ABSSTRFnCHIEZpNHv6qTxsBwJEPfYaSBsLQodm0F9ptvalFkHMpvj5DfE22oZrcrLbOYM2bdPJRHng==", "dependencies": {"@algolia/requester-common": "4.20.0"}}, "node_modules/@algolia/transporter": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/@algolia/transporter/-/transporter-4.20.0.tgz", "integrity": "sha512-Lsii1pGWOAISbzeyuf+r/GPhvHMPHSPrTDWNcIzOE1SG1inlJHICaVe2ikuoRjcpgxZNU54Jl+if15SUCsaTUg==", "dependencies": {"@algolia/cache-common": "4.20.0", "@algolia/logger-common": "4.20.0", "@algolia/requester-common": "4.20.0"}}, "node_modules/algoliasearch": {"version": "4.20.0", "resolved": "https://registry.npmjs.org/algoliasearch/-/algoliasearch-4.20.0.tgz", "integrity": "sha512-y+UHEjnOItoNy0bYO+WWmLWBlPwDjKHW6mNHrPi0NkuhpQOOEbrkwQH/wgKFDLh7qlKjzoKeiRtlpewDPDG23g==", "dependencies": {"@algolia/cache-browser-local-storage": "4.20.0", "@algolia/cache-common": "4.20.0", "@algolia/cache-in-memory": "4.20.0", "@algolia/client-account": "4.20.0", "@algolia/client-analytics": "4.20.0", "@algolia/client-common": "4.20.0", "@algolia/client-personalization": "4.20.0", "@algolia/client-search": "4.20.0", "@algolia/logger-common": "4.20.0", "@algolia/logger-console": "4.20.0", "@algolia/requester-browser-xhr": "4.20.0", "@algolia/requester-common": "4.20.0", "@algolia/requester-node-http": "4.20.0", "@algolia/transporter": "4.20.0"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==", "engines": {"node": ">= 12"}}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.1.tgz", "integrity": "sha512-cRVc/kyto/7E5shrWca1Wsea4y6tL9iYJE5FBCius3JQfb/4P4I295PfhgbJQBLTx6lATE4z+wK0rPM4VS2uow==", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/web-streams-polyfill": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.2.1.tgz", "integrity": "sha512-e0MO3wdXWKrLbL0DgGnUV7WHVuw9OUvL4hjgnPkIeEvESk74gAITi5G606JtZPp39cd8HA9VQzCIvA49LpPN5Q==", "engines": {"node": ">= 8"}}}}