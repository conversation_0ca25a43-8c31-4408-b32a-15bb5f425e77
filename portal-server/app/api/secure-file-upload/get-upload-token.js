'use strict';
const Router = require('express').Router;
const { v4: uuidv4 } = require('uuid');
const auth = require('../../shared-functions/auth');
const s3 = require('../../services/s3/secure-upload-service');

/**
 * @swagger
 * /api/admin/secure-upload/token:
 *    get:
 *      description: Returns a signed URL for uploading a file to S3
 *      tags:
 *        - Auth
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      parameters:
 *        - name: fileName
 *          in: query
 *          description: The name of the file to be uploaded including the extension
 *          required: true
 *        - name: public
 *          in: query
 *          description: Whether the file should be uploaded to the public bucket
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of backoffice users.
 */

const videosExtensions = ['avi', 'mkv', 'mp4', 'webm', 'mov', 'mpeg'];
const audioExtensions = ['mp3', 'wav', 'aac', 'ogg', 'm4a'];
const imagesExtensions = ['bmp', 'gif', 'jpg', 'jpeg', 'png', 'webp', 'svg'];

const self = (module.exports = Router({ mergeParams: true }).get(
    '/admin/secure-upload/token',
    auth.checkModuleAccess('file-uploader'),
    async (req, res, next) => {
        const { fileName, public: isPublic } = req.query;
        if (!fileName) return res.status(400).send('fileName is required');

        const fileExtension = fileName.split('.').pop();
        const sanitizedFileExtension = fileExtension.replace(/\s+/g, '_').toLowerCase();
        const fileType = self.getFileType(fileName);
        const key = `${fileType}/${uuidv4()}.${sanitizedFileExtension}`;

        const [status, response] = await s3.generateUploadToken(key, isPublic === 'true');
        res.status(status).send(response);
    }
));

module.exports.getFileType = (fileName) => {
    const fileExtension = fileName.split('.').pop().toLowerCase(); // Make case-insensitive
    let fileType = 'file';
    if (!fileExtension) return fileType;
    if (videosExtensions.includes(fileExtension)) fileType = 'video';
    if (audioExtensions.includes(fileExtension)) fileType = 'audio';
    if (imagesExtensions.includes(fileExtension)) fileType = 'image';
    return fileType;
};
