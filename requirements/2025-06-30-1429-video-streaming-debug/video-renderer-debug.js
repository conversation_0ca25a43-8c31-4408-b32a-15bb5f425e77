/**
 * Enhanced Video Rendering Module with Comprehensive Debugging
 * Provides detailed logging for video transmission analysis
 */

// Global video tracking - shared across all contexts
window.processedVideos = window.processedVideos || new Set();
window.processingVideos = window.processingVideos || new Set();

// Debug logging utility
const VideoDebugLogger = {
    enabled: true,
    prefix: '[VideoRenderer]',
    
    log: function(level, message, data = null) {
        if (!this.enabled) return;
        
        const timestamp = new Date().toISOString();
        const logMessage = `${this.prefix} [${timestamp}] ${level}: ${message}`;
        
        if (data) {
            console.log(logMessage, data);
        } else {
            console.log(logMessage);
        }
    },
    
    info: function(message, data) { this.log('INFO', message, data); },
    warn: function(message, data) { this.log('WARN', message, data); },
    error: function(message, data) { this.log('ERROR', message, data); },
    debug: function(message, data) { this.log('DEBUG', message, data); },
    network: function(message, data) { this.log('NETWORK', message, data); }
};

/**
 * Enhanced VideoRenderer class with comprehensive debugging
 */
class VideoRenderer {
    constructor(options = {}) {
        this.processedVideos = window.processedVideos;
        this.processingVideos = window.processingVideos;
        this.debugLogger = VideoDebugLogger;
        
        this.defaultOptions = {
            containerSelectors: ['.video-embed-container'],
            fileProxySelectors: ['.file-to-proxy'],
            targetContainers: ['body'],
            staggerDelay: 100,
            retryDelay: 200,
            useStreaming: false, // Flag to enable streaming approach
            ...options
        };
        
        this.debugLogger.info('VideoRenderer initialized', {
            options: this.defaultOptions,
            processedVideos: this.processedVideos.size,
            processingVideos: this.processingVideos.size
        });
    }

    /**
     * Main activation function with enhanced debugging
     */
    activateVideoPlayers(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const containers = this._getTargetContainers(config.targetContainers);
        
        this.debugLogger.info('Activating video players', {
            config: config,
            containerCount: containers.length,
            targetContainers: config.targetContainers
        });
        
        if (containers.length === 0) {
            this.debugLogger.warn('No target containers found for video activation');
            return;
        }

        // Process existing video-embed-container elements
        this._processVideoContainers(containers, config);
        
        // Process file-to-proxy elements and convert them
        this._processFileProxyElements(containers, config);
        
        // Process uploaded-media containers with video img tags
        this._processUploadedMediaElements(containers, config);
    }

    /**
     * Enhanced URL processing with debugging
     */
    _processVideoURL(url) {
        this.debugLogger.network('Processing video URL', { originalUrl: url });
        
        let processedUrl = url;
        
        // Check if it's a secure upload URL (like existing implementation)
        if (url.includes('secure-upload')) {
            this.debugLogger.network('Detected secure upload URL');
            
            // Apply URL transformation like existing implementation
            if (url.includes('/private')) {
                processedUrl = url.replace('/private', '');
                this.debugLogger.network('Removed /private from URL', {
                    originalUrl: url,
                    processedUrl: processedUrl
                });
            }
        }
        
        // Log URL pattern analysis
        const urlAnalysis = {
            isSecureUpload: url.includes('secure-upload'),
            hasPrivatePrefix: url.includes('/private'),
            hasSignedParams: url.includes('?') && (url.includes('Signature') || url.includes('signature')),
            hasExpiration: url.includes('Expires') || url.includes('expires'),
            protocol: url.substring(0, url.indexOf(':')),
            domain: url.includes('://') ? url.split('://')[1].split('/')[0] : 'unknown',
            path: url.includes('://') ? '/' + url.split('://')[1].split('/').slice(1).join('/') : url
        };
        
        this.debugLogger.network('URL Analysis', urlAnalysis);
        
        return processedUrl;
    }

    /**
     * Enhanced video loading with comprehensive network debugging
     */
    _loadVideo(container, url) {
        const currentPlaceholder = container.find('.video-loading-placeholder');
        if (currentPlaceholder.length === 0) {
            this.debugLogger.warn('No placeholder found for video loading', { url });
            return;
        }
        
        // Process URL for security and debugging
        const processedUrl = this._processVideoURL(url);
        
        const loadingMsg = $('<div>', {
            html: 'Loading video...',
            'data-video-loading': 'true',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
        currentPlaceholder.replaceWith(loadingMsg);
        
        this.debugLogger.network('Starting video fetch', {
            originalUrl: url,
            processedUrl: processedUrl,
            fetchTime: new Date().toISOString()
        });
        
        // Enhanced fetch with detailed debugging
        const fetchStartTime = performance.now();
        
        fetch(processedUrl, { 
            method: 'GET', 
            credentials: 'include',
            headers: {
                'Accept': 'video/*',
                'User-Agent': navigator.userAgent
            }
        })
        .then(response => {
            const fetchEndTime = performance.now();
            const fetchDuration = fetchEndTime - fetchStartTime;
            
            // Log detailed response information
            const responseInfo = {
                status: response.status,
                statusText: response.statusText,
                ok: response.ok,
                url: response.url,
                redirected: response.redirected,
                type: response.type,
                headers: {},
                fetchDuration: fetchDuration
            };
            
            // Extract headers for debugging
            response.headers.forEach((value, key) => {
                responseInfo.headers[key] = value;
            });
            
            this.debugLogger.network('Fetch response received', responseInfo);
            
            // Log content type analysis
            const contentType = response.headers.get('content-type');
            if (contentType) {
                this.debugLogger.network('Content-Type analysis', {
                    contentType: contentType,
                    isVideo: contentType.startsWith('video/'),
                    isAudio: contentType.startsWith('audio/'),
                    isOctetStream: contentType.includes('application/octet-stream')
                });
            }
            
            // Log content length if available
            const contentLength = response.headers.get('content-length');
            if (contentLength) {
                this.debugLogger.network('Content size', {
                    contentLength: contentLength,
                    contentLengthMB: (parseInt(contentLength) / 1024 / 1024).toFixed(2) + ' MB'
                });
            }
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response.blob();
        })
        .then(blob => {
            const blobCreationTime = performance.now();
            const totalDuration = blobCreationTime - fetchStartTime;
            
            this.debugLogger.network('Blob created successfully', {
                blobSize: blob.size,
                blobSizeMB: (blob.size / 1024 / 1024).toFixed(2) + ' MB',
                blobType: blob.type,
                totalDuration: totalDuration
            });
            
            if (loadingMsg.parent().length === 0) {
                this.debugLogger.warn('Container removed before blob processing', { url });
                URL.revokeObjectURL(URL.createObjectURL(blob));
                return;
            }
            
            const blobUrl = URL.createObjectURL(blob);
            this.debugLogger.network('Blob URL created', {
                originalUrl: url,
                blobUrl: blobUrl,
                blobId: blobUrl.split('/').pop()
            });
            
            const video = this._createVideoElement(url, blobUrl);
            loadingMsg.replaceWith(video);
            
            // Mark this video as processed and remove from processing
            if (url) {
                this.processedVideos.add(url);
                this.processingVideos.delete(url);
                this.debugLogger.info('Video processing completed', {
                    url: url,
                    totalProcessedVideos: this.processedVideos.size,
                    processingVideos: this.processingVideos.size
                });
            }
        })
        .catch(error => {
            const errorTime = performance.now();
            const errorDuration = errorTime - fetchStartTime;
            
            // Enhanced error logging
            const errorInfo = {
                error: error.message,
                errorType: error.name,
                stack: error.stack,
                url: url,
                processedUrl: processedUrl,
                errorDuration: errorDuration,
                networkState: navigator.onLine ? 'online' : 'offline'
            };
            
            this.debugLogger.error('Video loading failed', errorInfo);
            
            if (loadingMsg.parent().length === 0) {
                this.debugLogger.warn('Container removed before error handling', { url });
                return;
            }
            
            const errorMsg = this._createErrorElement(error.message);
            loadingMsg.replaceWith(errorMsg);
            
            // Remove from processing on error
            if (url) {
                this.processingVideos.delete(url);
                this.debugLogger.info('Video removed from processing queue', {
                    url: url,
                    processingVideos: this.processingVideos.size
                });
            }
        });
    }

    /**
     * Enhanced video element creation with debugging
     */
    _createVideoElement(url, blobUrl) {
        this.debugLogger.debug('Creating video element', {
            originalUrl: url,
            blobUrl: blobUrl
        });
        
        const video = $('<video>', {
            controls: true,
            preload: 'metadata',
            'data-video-url': url,
            'data-blob-url': blobUrl,
            'data-debug-timestamp': new Date().toISOString(),
            css: {
                width: '100%',
                maxWidth: '800px',
                margin: '1rem 0',
                display: 'block'
            }
        });
        
        video.attr('src', blobUrl);
        
        // Enhanced event handlers with debugging
        video.on('loadstart', () => {
            this.debugLogger.debug('Video loadstart event', { url, blobUrl });
        });
        
        video.on('loadedmetadata', () => {
            const videoEl = video[0];
            this.debugLogger.debug('Video metadata loaded', {
                url: url,
                duration: videoEl.duration,
                videoWidth: videoEl.videoWidth,
                videoHeight: videoEl.videoHeight,
                readyState: videoEl.readyState
            });
        });
        
        video.on('canplay', () => {
            this.debugLogger.debug('Video can play', { url });
        });
        
        video.on('error', (e) => {
            const videoEl = video[0];
            const error = videoEl.error;
            this.debugLogger.error('Video playback error', {
                url: url,
                errorCode: error ? error.code : 'unknown',
                errorMessage: error ? error.message : 'unknown',
                networkState: videoEl.networkState,
                readyState: videoEl.readyState
            });
        });
        
        // Cleanup blob URL when video is removed
        video.on('remove', () => {
            this.debugLogger.debug('Video element removed, cleaning up blob URL', { url, blobUrl });
            URL.revokeObjectURL(blobUrl);
        });
        
        // Cleanup blob URL on page unload
        $(window).on('beforeunload', () => {
            this.debugLogger.debug('Page unloading, cleaning up blob URL', { url, blobUrl });
            URL.revokeObjectURL(blobUrl);
        });
        
        return video;
    }

    /**
     * Enhanced error element creation with more details
     */
    _createErrorElement(errorMessage = 'Failed to load video') {
        return $('<div>', {
            html: `<strong>Error:</strong> ${errorMessage}<br><small>Check browser console for details</small>`,
            css: {
                padding: '1rem',
                color: '#721c24',
                backgroundColor: '#f8d7da',
                border: '1px solid #f5c6cb',
                borderRadius: '4px',
                textAlign: 'center',
                fontSize: '14px'
            }
        });
    }

    /**
     * Process video containers with enhanced debugging
     */
    _processVideoContainers(containers, config) {
        const videoContainers = containers.find(config.containerSelectors.join(', '));
        
        this.debugLogger.info('Processing video containers', {
            containerCount: videoContainers.length,
            selectors: config.containerSelectors
        });
        
        videoContainers.each((index, element) => {
            const $element = $(element);
            const videoUrl = $element.attr('data-video-url');
            
            this.debugLogger.debug('Processing video container', {
                index: index,
                videoUrl: videoUrl,
                elementId: $element.attr('id'),
                elementClasses: $element.attr('class')
            });
            
            this._processVideoContainer($element, index * config.staggerDelay);
        });
    }

    /**
     * Process individual video container with debugging
     */
    _processVideoContainer(container, delay = 0) {
        const placeholder = container.find('.video-loading-placeholder');
        const existingVideo = container.find('video');
        const videoUrl = container.attr('data-video-url');
        
        this.debugLogger.debug('Processing individual video container', {
            videoUrl: videoUrl,
            hasPlaceholder: placeholder.length > 0,
            hasExistingVideo: existingVideo.length > 0,
            delay: delay,
            isProcessing: this.processingVideos.has(videoUrl),
            isProcessed: this.processedVideos.has(videoUrl)
        });
        
        // Skip if URL is already being processed
        if (videoUrl && this.processingVideos.has(videoUrl)) {
            this.debugLogger.warn('Video already being processed, skipping', { videoUrl });
            return;
        }
        
        // If we have a URL and it's already processed in this session
        if (videoUrl && this.processedVideos.has(videoUrl)) {
            if (existingVideo.length > 0 && 
                existingVideo.attr('data-blob-url') && 
                !existingVideo[0].error &&
                existingVideo[0].readyState !== 0) {
                this.debugLogger.debug('Video already processed and working, skipping', { videoUrl });
                return;
            }
            this.debugLogger.info('Re-processing previously processed video', { videoUrl });
            this.processedVideos.delete(videoUrl);
        }
        
        if (existingVideo.length > 0) {
            this.debugLogger.debug('Removing existing video element', { videoUrl });
            existingVideo.remove();
        }
        
        if (placeholder.length > 0) {
            placeholder.remove();
        }
        
        container.append(this._createPlaceholder());
        
        const url = container.attr('data-video-url');
        const type = container.attr('data-video-type') || 'mp4';
        
        if (url) {
            this.processingVideos.add(url);
            this.debugLogger.info('Added video to processing queue', {
                url: url,
                type: type,
                delay: delay,
                processingCount: this.processingVideos.size
            });
            
            setTimeout(() => {
                this._loadVideo(container, url);
            }, delay);
        } else {
            this.debugLogger.warn('No video URL found in container', {
                containerId: container.attr('id'),
                containerClasses: container.attr('class')
            });
        }
    }

    // Include all other methods from the original implementation with similar debugging enhancements
    _getTargetContainers(selectors) {
        if (typeof $ === 'undefined') {
            this.debugLogger.error('jQuery not available, cannot process videos');
            return { length: 0, find: () => ({ length: 0, each: () => {} }) };
        }
        
        const containers = [];
        selectors.forEach(selector => {
            const elements = $(selector);
            if (elements.length > 0) {
                containers.push(...elements.toArray());
                this.debugLogger.debug('Found containers', { selector, count: elements.length });
            }
        });
        
        this.debugLogger.info('Target containers collected', {
            selectors: selectors,
            totalContainers: containers.length
        });
        
        return $(containers);
    }

    _processFileProxyElements(containers, config) {
        const proxyElements = containers.find(config.fileProxySelectors.join(', '));
        
        this.debugLogger.info('Processing file-to-proxy elements', {
            elementCount: proxyElements.length,
            selectors: config.fileProxySelectors
        });
        
        proxyElements.each((index, element) => {
            const $element = $(element);
            const url = $element.attr('data-url');
            
            this.debugLogger.debug('Processing file-to-proxy element', {
                index: index,
                url: url,
                elementId: $element.attr('id'),
                elementClasses: $element.attr('class')
            });
            
            this._convertFileProxyToVideoContainer($element, config.retryDelay + (index * config.staggerDelay));
        });
    }

    _convertFileProxyToVideoContainer(element, delay = 0) {
        const url = element.attr('data-url');
        
        this.debugLogger.debug('Converting file-to-proxy to video container', {
            url: url,
            delay: delay,
            isProcessed: this.processedVideos.has(url)
        });
        
        if (!url) {
            this.debugLogger.warn('No URL found in file-to-proxy element');
            return;
        }
        
        if (this.processedVideos.has(url)) {
            this.debugLogger.info('File-to-proxy URL already processed, skipping', { url });
            return;
        }
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'mp4',
            'data-converted-from': 'file-to-proxy',
            css: {
                maxWidth: '100%',
                margin: '1rem 0'
            }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        this.debugLogger.info('File-to-proxy converted to video container', { url });
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _processUploadedMediaElements(containers, config) {
        const uploadedMediaElements = containers.find('.uploaded-media');
        
        this.debugLogger.info('Processing uploaded-media elements', {
            elementCount: uploadedMediaElements.length
        });
        
        uploadedMediaElements.each((index, element) => {
            const $element = $(element);
            const $img = $element.find('img');
            
            if ($img.length > 0) {
                const src = $img.attr('src');
                
                this.debugLogger.debug('Processing uploaded-media element', {
                    index: index,
                    src: src,
                    isVideo: this._isVideoFile(src)
                });
                
                if (src && this._isVideoFile(src)) {
                    this._convertUploadedMediaToVideoContainer($element, src, config.retryDelay + (index * config.staggerDelay));
                }
            }
        });
    }

    _isVideoFile(url) {
        const videoExtensions = ['mp4', 'mkv', 'webm', 'avi', 'mpeg', 'mov', 'mp3'];
        const urlWithoutParams = url.split('?')[0];
        const extension = urlWithoutParams.split('.').pop().toLowerCase();
        const isVideo = videoExtensions.includes(extension);
        
        this.debugLogger.debug('Video file check', {
            url: url,
            extension: extension,
            isVideo: isVideo
        });
        
        return isVideo;
    }

    _convertUploadedMediaToVideoContainer(element, url, delay = 0) {
        this.debugLogger.debug('Converting uploaded-media to video container', {
            url: url,
            delay: delay,
            isProcessed: this.processedVideos.has(url)
        });
        
        if (this.processedVideos.has(url)) {
            this.debugLogger.info('Uploaded-media URL already processed, skipping', { url });
            return;
        }
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'mp4',
            'data-converted-from': 'uploaded-media',
            css: {
                maxWidth: '100%',
                margin: '1rem 0'
            }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        this.debugLogger.info('Uploaded-media converted to video container', { url });
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _createPlaceholder() {
        return $('<div>', {
            class: 'video-loading-placeholder',
            html: 'Loading video...',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
    }

    addRetryMechanism(targetContainers, retryDelay = 3000) {
        this.debugLogger.info('Adding retry mechanism', {
            targetContainers: targetContainers,
            retryDelay: retryDelay
        });
        
        setTimeout(() => {
            const selectors = targetContainers.map(selector => 
                `${selector} .video-loading-placeholder`
            ).join(', ');
            
            const failedVideos = $(selectors);
            if (failedVideos.length > 0) {
                this.debugLogger.info('Retrying failed videos', {
                    failedCount: failedVideos.length
                });
                this.activateVideoPlayers({ targetContainers });
            }
        }, retryDelay);
    }

    clearVideoTracking(urlsToRemove = []) {
        this.debugLogger.info('Clearing video tracking', {
            urlsToRemove: urlsToRemove,
            currentProcessed: this.processedVideos.size,
            currentProcessing: this.processingVideos.size
        });
        
        if (urlsToRemove.length === 0) {
            const videosWithErrors = $('video[data-blob-url]');
            videosWithErrors.each((index, video) => {
                if (video.error || video.networkState === 3) {
                    const videoUrl = $(video).attr('data-video-url');
                    if (videoUrl) {
                        this.processedVideos.delete(videoUrl);
                        this.processingVideos.delete(videoUrl);
                        this.debugLogger.debug('Removed error video from tracking', { videoUrl });
                    }
                }
            });
        } else {
            urlsToRemove.forEach(url => {
                this.processedVideos.delete(url);
                this.processingVideos.delete(url);
                this.debugLogger.debug('Removed specific URL from tracking', { url });
            });
        }
        
        this.debugLogger.info('Video tracking cleared', {
            processedCount: this.processedVideos.size,
            processingCount: this.processingVideos.size
        });
    }
}

// Initialize enhanced video renderer
window.VideoRenderer = VideoRenderer;
window.videoRenderer = new VideoRenderer();
window.VideoDebugLogger = VideoDebugLogger;

// Expose debugging controls
window.enableVideoDebug = () => {
    VideoDebugLogger.enabled = true;
    console.log('Video debugging enabled');
};

window.disableVideoDebug = () => {
    VideoDebugLogger.enabled = false;
    console.log('Video debugging disabled');
};

// Expose enhanced functions globally
window.activateAllVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers(options);
};

window.activateArticleVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        ...options
    });
};