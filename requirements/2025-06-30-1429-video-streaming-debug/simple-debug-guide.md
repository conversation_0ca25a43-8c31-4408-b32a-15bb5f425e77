# Simple Video Debug Testing Guide

## 🎯 Quick Testing Process

### Step 1: Start Development Environment
```bash
cd /Volumes/Keen/Desk/Career/Valhalla/UBX/wiki/src
npm run watch
```

### Step 2: Open Article Editor
1. Navigate to your article editor
2. Open **Browser Developer Console** (F12 or right-click → Inspect → Console)
3. Look for this message: `🎥 Video debugging enabled - monitoring all video transmission`

### Step 3: Test Video Processing
1. **Switch to Markdown mode** (if not already)
2. **Show Preview** - This triggers video processing
3. **Watch the console** for detailed debug logs

## 🔍 What You'll See in Console

### Normal Initialization:
```
🎥 Video debugging enabled - monitoring all video transmission
📝 Editor detected, activating video players
[VideoRenderer] [timestamp] INFO: VideoRenderer initialized
[VideoRenderer] [timestamp] INFO: Activating video players
```

### Video Processing (when preview loads):
```
[VideoRenderer] [timestamp] NETWORK: Processing video URL { originalUrl: "your-video-url" }
[VideoRenderer] [timestamp] NETWORK: URL Analysis {
  isSecureUpload: true/false,
  hasPrivatePrefix: true/false,
  hasSignedParams: true/false,
  domain: "your-s3-domain.com",
  processedUrl: "processed-url-if-different"
}
[VideoRenderer] [timestamp] NETWORK: Starting video fetch
[VideoRenderer] [timestamp] NETWORK: Fetch response received { status: 200, contentType: "video/mp4" }
[VideoRenderer] [timestamp] NETWORK: Blob created successfully { blobSizeMB: "X.X MB" }
```

## 🛠️ Debug Commands (Run in Console)

```javascript
// Check current status
debugVideoStatus()

// Force reload all videos
forceVideoReload()

// Enable/disable debug logging
enableVideoDebug()
disableVideoDebug()

// Check video tracking
console.log('Processed videos:', window.processedVideos.size)
console.log('Processing videos:', window.processingVideos.size)
```

## 🎯 Key Things to Look For

### 1. URL Processing
- **Original URL**: Your video URL as it appears in markdown
- **Processed URL**: URL after any transformations
- **Look for**: `/private` removal, domain changes

### 2. Security Analysis
- **isSecureUpload**: Whether URL contains 'secure-upload'
- **hasPrivatePrefix**: Whether URL has '/private' that needs removal
- **hasSignedParams**: Whether URL has S3 signature parameters

### 3. Network Performance
- **fetchDuration**: Time to fetch the video
- **blobSizeMB**: Size of video file
- **status**: HTTP response code (200 = success)

### 4. Error Detection
```
[VideoRenderer] [timestamp] ERROR: Video loading failed {
  error: "HTTP 403: Forbidden",
  url: "failing-url",
  errorDuration: 1250
}
```

## 🚨 Common Issues & Solutions

### Issue: No Debug Logs
**Check:**
- Is the console open?
- Did you see the initial "🎥 Video debugging enabled" message?
- Try refreshing the article editor page

### Issue: Videos Not Loading
**Debug:**
```javascript
// Check status
debugVideoStatus()

// Look for errors in network logs
// Check if URLs are being processed correctly
```

### Issue: `/private` URLs Failing
**Look for:**
- URLs with `/private` in the path
- Whether `processedUrl` shows the prefix removed
- HTTP 403/404 errors

## 📊 Expected Results

### If Current Implementation Works:
- Videos load successfully
- No authentication errors
- Reasonable loading times for your file sizes

### If Streaming Needed:
- Large videos (>20MB) cause memory issues
- Secure URLs fail with current processing
- `/private` prefix needs removal but isn't working

### If URL Processing Issues:
- 403 Forbidden errors
- URLs with `/private` not being processed
- S3 signed URLs expiring

## 🎯 Quick Test Checklist

1. ✅ Console shows debug initialization
2. ✅ Preview triggers video processing logs
3. ✅ URL analysis shows your video patterns
4. ✅ Network logs show fetch success/failure
5. ✅ Videos display correctly in preview

The console logs will tell you everything you need to know about how your secured S3 video URLs are being handled!