# Video Debug Testing Guide

## 🚀 Testing the Enhanced Video Implementation

### Phase 1: Setup and Verification

#### 1. Verify Debug Implementation is Active
```bash
# Start your development server
cd /Volumes/Keen/Desk/Career/Valhalla/UBX/wiki/src
npm run watch  # or npm start
```

#### 2. Open Debug Monitor
- Open `debug-monitor.html` in a browser tab
- This will monitor all video transmission activity in real-time

#### 3. Access Article Editor
- Navigate to your wiki article editor
- Open browser developer console (F12)
- You should see: `🎥 Video debugging enabled - monitoring all video transmission`

### Phase 2: Basic Testing

#### Test 1: Verify Debug Logger is Working
In the browser console, run:
```javascript
// Check if debug functions are available
console.log('Debug functions available:', {
    enableVideoDebug: typeof enableVideoDebug,
    disableVideoDebug: typeof disableVideoDebug,
    debugVideoStatus: typeof debugVideoStatus,
    forceVideoReload: typeof forceVideoReload
});

// Test debug status
debugVideoStatus();
```

Expected output:
```
=== Video Debug Status ===
Processed videos: 0
Processing videos: 0
Video containers: X
File-to-proxy elements: X
Video elements: X
Uploaded media: X
```

#### Test 2: Monitor Video Loading
1. Add a video to your article content (either by uploading or adding video markdown)
2. Watch the debug monitor and browser console for detailed logs
3. Look for these log patterns:

**URL Processing:**
```
[VideoRenderer] NETWORK: Processing video URL { originalUrl: "..." }
[VideoRenderer] NETWORK: URL Analysis { isSecureUpload: true/false, ... }
```

**Network Activity:**
```
[VideoRenderer] NETWORK: Starting video fetch { originalUrl: "...", processedUrl: "..." }
[VideoRenderer] NETWORK: Fetch response received { status: 200, ... }
[VideoRenderer] NETWORK: Blob created successfully { blobSizeMB: "X.X MB" }
```

### Phase 3: Advanced Testing

#### Test 3: Secure Upload URL Analysis
Look for videos with these URL patterns in the logs:
- URLs containing `secure-upload`
- URLs with `/private` prefix
- URLs with signed parameters (`Signature`, `Expires`)
- S3 URLs (`amazonaws.com`, `.s3.`)

**Key Questions to Answer:**
1. Are `/private` prefixes being removed correctly?
2. Do secure uploads need different handling?
3. Are signed URLs expiring causing failures?

#### Test 4: Performance Analysis
Monitor these metrics in the logs:
- `fetchDuration` - Time to fetch video
- `totalDuration` - Total time including blob creation
- `blobSizeMB` - Size of video files

**Performance Benchmarks:**
- Small videos (<5MB): Should load in <2 seconds
- Medium videos (5-20MB): Should load in <10 seconds
- Large videos (>20MB): May need streaming approach

#### Test 5: Error Analysis
Intentionally test with:
1. **Invalid URLs** - Check error handling
2. **Network interruption** - Disable/enable network during loading
3. **Large files** - Test memory usage with blob approach
4. **Expired signed URLs** - If you have temporary S3 URLs

### Phase 4: Real-World Testing

#### Test 6: Upload and Processing Workflow
1. Upload a new video through your upload interface
2. Monitor the debug logs during upload processing
3. Check if the implementation waits for processing completion
4. Verify video plays correctly after processing

#### Test 7: Editor Mode Switching
1. Switch between WYSIWYG and Markdown modes
2. Verify videos are reprocessed correctly
3. Check for memory leaks or duplicate processing

#### Test 8: Session Management
1. Add multiple videos to the same article
2. Refresh the page
3. Verify session tracking works correctly
4. Use `forceVideoReload()` to test reload functionality

### Expected Debug Output Examples

#### Successful Processing:
```
[VideoRenderer] INFO: VideoRenderer initialized
[VideoRenderer] INFO: Activating video players
[VideoRenderer] NETWORK: Processing video URL
[VideoRenderer] NETWORK: URL Analysis {
  isSecureUpload: true,
  hasPrivatePrefix: true,
  processedUrl: "https://example.com/secure-upload/video.mp4"
}
[VideoRenderer] NETWORK: Starting video fetch
[VideoRenderer] NETWORK: Fetch response received { status: 200, contentType: "video/mp4" }
[VideoRenderer] NETWORK: Blob created successfully { blobSizeMB: "15.3 MB" }
[VideoRenderer] INFO: Video processing completed
```

#### Error Scenarios:
```
[VideoRenderer] ERROR: Video loading failed {
  error: "HTTP 403: Forbidden",
  url: "...",
  errorDuration: 1250
}
```

### Troubleshooting Common Issues

#### Issue 1: No Debug Logs Appearing
**Solutions:**
- Check if `enableVideoDebug()` was called
- Verify video-renderer.js was replaced with debug version
- Refresh browser and check console for initialization messages

#### Issue 2: Videos Not Loading
**Debug Steps:**
1. Run `debugVideoStatus()` to check video counts
2. Check network tab for failed requests
3. Look for URL processing logs
4. Try `forceVideoReload()` to reset state

#### Issue 3: Performance Issues
**Analysis:**
1. Check `blobSizeMB` in logs for video sizes
2. Monitor `fetchDuration` for network speed
3. Consider streaming approach for large files
4. Look for memory usage in browser dev tools

### Next Steps Based on Findings

#### If Blob Approach Works Well:
- Videos load quickly
- No memory issues
- URLs process correctly
- **Action:** Keep enhanced debug version

#### If Streaming is Needed:
- Large videos cause memory issues
- Secure URLs need special handling
- Processing status checking required
- **Action:** Implement hybrid approach from `video-renderer-hybrid.js`

#### If URL Processing Issues:
- `/private` prefix not being removed
- Signed URLs failing
- Authentication issues
- **Action:** Fix URL processing logic based on logs

### Debug Commands Reference

```javascript
// Status and monitoring
debugVideoStatus()                    // Show current video status
refreshStatus()                       // Refresh debug monitor

// Control functions
enableVideoDebug()                    // Enable detailed logging
disableVideoDebug()                   // Disable detailed logging
forceVideoReload()                    // Force reload all videos

// Analysis functions
window.videoRenderer.clearVideoTracking()  // Clear session tracking
window.processedVideos.clear()              // Clear processed videos cache
window.processingVideos.clear()             // Clear processing queue
```

### Data Collection for Analysis

The debug implementation will help you collect:

1. **URL Patterns**: Which URLs need special processing
2. **Performance Metrics**: Loading times and file sizes  
3. **Error Patterns**: Common failure points
4. **Network Behavior**: Authentication and headers
5. **Processing Status**: Upload processing workflow

This data will inform whether to:
- Continue with enhanced blob approach
- Switch to streaming for secure uploads
- Implement hybrid solution
- Fix specific URL processing issues