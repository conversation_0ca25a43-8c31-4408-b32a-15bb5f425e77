<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Debug Monitor</title>
    <style>
        body { 
            font-family: 'Courier New', monospace; 
            background: #1a1a1a; 
            color: #00ff00; 
            margin: 0; 
            padding: 20px; 
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            text-align: center; 
            border-bottom: 2px solid #00ff00; 
            padding-bottom: 20px; 
            margin-bottom: 20px; 
        }
        .section { 
            background: #2a2a2a; 
            border: 1px solid #444; 
            margin: 20px 0; 
            padding: 15px; 
            border-radius: 5px; 
        }
        .section h3 { 
            color: #00ccff; 
            margin-top: 0; 
            border-bottom: 1px solid #444; 
            padding-bottom: 10px; 
        }
        .log-entry { 
            margin: 5px 0; 
            padding: 5px; 
            border-left: 3px solid #555; 
            padding-left: 10px; 
        }
        .log-info { border-left-color: #00ff00; }
        .log-warn { border-left-color: #ffff00; color: #ffff00; }
        .log-error { border-left-color: #ff0000; color: #ff0000; }
        .log-network { border-left-color: #00ccff; color: #00ccff; }
        .log-debug { border-left-color: #888; color: #888; }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 15px; 
        }
        .status-card { 
            background: #333; 
            border: 1px solid #555; 
            padding: 15px; 
            border-radius: 5px; 
        }
        .status-value { 
            font-size: 2em; 
            font-weight: bold; 
            color: #00ff00; 
        }
        .button { 
            background: #444; 
            color: #00ff00; 
            border: 1px solid #666; 
            padding: 10px 20px; 
            cursor: pointer; 
            margin: 5px; 
            border-radius: 3px; 
        }
        .button:hover { background: #555; }
        .url-analysis { 
            background: #2a2a3a; 
            border: 1px solid #6666aa; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 3px; 
        }
        .timestamp { color: #888; font-size: 0.9em; }
        .json-data { 
            background: #1a1a1a; 
            padding: 10px; 
            border-radius: 3px; 
            margin: 5px 0; 
            overflow-x: auto; 
            white-space: pre-wrap; 
        }
        #logContainer { 
            max-height: 400px; 
            overflow-y: auto; 
            background: #1a1a1a; 
            padding: 10px; 
            border-radius: 3px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 Video Transmission Debug Monitor</h1>
            <p>Real-time analysis of video streaming and blob transmission</p>
        </div>

        <div class="section">
            <h3>📊 System Status</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-value" id="processedCount">0</div>
                    <div>Processed Videos</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="processingCount">0</div>
                    <div>Processing Videos</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="containerCount">0</div>
                    <div>Video Containers</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="proxyCount">0</div>
                    <div>File-to-Proxy</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🔧 Debug Controls</h3>
            <button class="button" onclick="refreshStatus()">🔄 Refresh Status</button>
            <button class="button" onclick="enableDebug()">🟢 Enable Debug</button>
            <button class="button" onclick="disableDebug()">🔴 Disable Debug</button>
            <button class="button" onclick="clearLogs()">🗑️ Clear Logs</button>
            <button class="button" onclick="exportLogs()">💾 Export Logs</button>
            <button class="button" onclick="forceReload()">⚡ Force Reload Videos</button>
        </div>

        <div class="section">
            <h3>🌐 URL Analysis</h3>
            <div id="urlAnalysis">
                <p>No URL analysis data yet. Videos will be analyzed when processed.</p>
            </div>
        </div>

        <div class="section">
            <h3>📝 Real-time Logs</h3>
            <div id="logContainer">
                <div class="log-entry log-info">Debug monitor initialized - waiting for video activity...</div>
            </div>
        </div>

        <div class="section">
            <h3>📋 Instructions</h3>
            <ol>
                <li><strong>Open the article editor</strong> in another tab/window</li>
                <li><strong>Add or edit videos</strong> in your article content</li>
                <li><strong>Watch this monitor</strong> for detailed network transmission logs</li>
                <li><strong>Check URL Analysis</strong> to see how secure S3 URLs are being processed</li>
                <li><strong>Monitor performance</strong> via fetch times and blob creation logs</li>
                <li><strong>Use Force Reload</strong> if videos get stuck or need reprocessing</li>
            </ol>
            <p><strong>Console Commands:</strong></p>
            <ul>
                <li><code>debugVideoStatus()</code> - Show current video status</li>
                <li><code>forceVideoReload()</code> - Force reload all videos</li>
                <li><code>enableVideoDebug()</code> - Enable debug logging</li>
                <li><code>disableVideoDebug()</code> - Disable debug logging</li>
            </ul>
        </div>
    </div>

    <script>
        let logs = [];
        let urlAnalysisData = {};

        // Intercept console.log to capture debug messages
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            // Check if this is a VideoRenderer debug message
            const message = args[0];
            if (typeof message === 'string' && message.includes('[VideoRenderer]')) {
                addLogEntry(message, args[1]);
            }
        };

        function addLogEntry(message, data = null) {
            const timestamp = new Date().toISOString();
            const logEntry = { timestamp, message, data };
            logs.push(logEntry);
            
            // Determine log type
            let logType = 'log-info';
            if (message.includes('ERROR')) logType = 'log-error';
            else if (message.includes('WARN')) logType = 'log-warn';
            else if (message.includes('NETWORK')) logType = 'log-network';
            else if (message.includes('DEBUG')) logType = 'log-debug';
            
            const logContainer = document.getElementById('logContainer');
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry ${logType}`;
            
            let logContent = `<span class="timestamp">[${timestamp.split('T')[1].split('.')[0]}]</span> ${message}`;
            if (data) {
                logContent += `<div class="json-data">${JSON.stringify(data, null, 2)}</div>`;
                
                // Extract URL analysis data
                if (message.includes('URL Analysis') && data.originalUrl) {
                    urlAnalysisData[data.originalUrl] = data;
                    updateUrlAnalysis();
                }
            }
            
            logDiv.innerHTML = logContent;
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Keep only last 100 log entries
            if (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        function updateUrlAnalysis() {
            const urlAnalysisDiv = document.getElementById('urlAnalysis');
            if (Object.keys(urlAnalysisData).length === 0) return;
            
            let html = '';
            for (const [url, analysis] of Object.entries(urlAnalysisData)) {
                html += `
                    <div class="url-analysis">
                        <strong>URL:</strong> ${url.substring(0, 80)}${url.length > 80 ? '...' : ''}<br>
                        <strong>Type:</strong> ${analysis.isSecureUpload ? '🔒 Secure Upload' : '🌐 Direct URL'}<br>
                        <strong>Domain:</strong> ${analysis.domain}<br>
                        <strong>Has Private Prefix:</strong> ${analysis.hasPrivatePrefix ? '✅' : '❌'}<br>
                        <strong>Has Signed Params:</strong> ${analysis.hasSignedParams ? '✅' : '❌'}<br>
                        <strong>Has Expiration:</strong> ${analysis.hasExpiration ? '✅' : '❌'}<br>
                        ${analysis.processedUrl ? `<strong>Processed URL:</strong> ${analysis.processedUrl.substring(0, 80)}${analysis.processedUrl.length > 80 ? '...' : ''}<br>` : ''}
                    </div>
                `;
            }
            urlAnalysisDiv.innerHTML = html;
        }

        function refreshStatus() {
            // Get status from parent window if available
            try {
                const parentWindow = window.parent !== window ? window.parent : window;
                
                if (parentWindow.processedVideos) {
                    document.getElementById('processedCount').textContent = parentWindow.processedVideos.size;
                }
                if (parentWindow.processingVideos) {
                    document.getElementById('processingCount').textContent = parentWindow.processingVideos.size;
                }
                if (parentWindow.$) {
                    document.getElementById('containerCount').textContent = parentWindow.$('.video-embed-container').length;
                    document.getElementById('proxyCount').textContent = parentWindow.$('.file-to-proxy').length;
                }
                
                addLogEntry('Status refreshed', {
                    processedVideos: parentWindow.processedVideos ? parentWindow.processedVideos.size : 'N/A',
                    processingVideos: parentWindow.processingVideos ? parentWindow.processingVideos.size : 'N/A',
                    containers: parentWindow.$ ? parentWindow.$('.video-embed-container').length : 'N/A'
                });
            } catch (e) {
                addLogEntry('ERROR: Could not access parent window data', { error: e.message });
            }
        }

        function enableDebug() {
            addLogEntry('INFO: Video debugging is auto-enabled in your article editor', {
                note: 'Debug logging starts automatically when you load the article editor',
                instructions: 'Go to your article editor and check the browser console for debug logs'
            });
        }

        function disableDebug() {
            addLogEntry('INFO: To disable debugging, use browser console in article editor', {
                command: 'Run disableVideoDebug() in the article editor console',
                note: 'This monitor shows logs but cannot control the main application'
            });
        }

        function clearLogs() {
            logs = [];
            urlAnalysisData = {};
            document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">Logs cleared</div>';
            document.getElementById('urlAnalysis').innerHTML = '<p>No URL analysis data yet. Videos will be analyzed when processed.</p>';
        }

        function exportLogs() {
            const exportData = {
                timestamp: new Date().toISOString(),
                logs: logs,
                urlAnalysis: urlAnalysisData
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `video-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addLogEntry('Logs exported');
        }

        function forceReload() {
            addLogEntry('INFO: To force video reload, use browser console in article editor', {
                command: 'Run forceVideoReload() in the article editor console',
                alternative: 'Or refresh the article editor page to restart video processing',
                note: 'This monitor displays logs but cannot control the main application'
            });
        }

        // Auto-refresh status every 5 seconds
        setInterval(refreshStatus, 5000);
        
        // Initial status refresh
        setTimeout(refreshStatus, 1000);
        
        // Add welcome message
        setTimeout(() => {
            addLogEntry('🎥 Video Debug Monitor Ready', {
                instructions: 'Open article editor and add/edit videos to see transmission logs',
                availableCommands: ['debugVideoStatus()', 'forceVideoReload()', 'enableVideoDebug()', 'disableVideoDebug()']
            });
        }, 500);
    </script>
</body>
</html>