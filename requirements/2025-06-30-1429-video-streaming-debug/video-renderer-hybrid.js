/**
 * Hybrid Video Rendering Module
 * Combines streaming approach from existing implementation with modular design from new implementation
 * Uses streaming for secure uploads, blob approach for direct videos
 */

// Global video tracking
window.processedVideos = window.processedVideos || new Set();
window.processingVideos = window.processingVideos || new Set();

// Import debug logger
const VideoDebugLogger = window.VideoDebugLogger || {
    enabled: true,
    prefix: '[VideoRenderer-Hybrid]',
    log: function(level, message, data = null) {
        if (!this.enabled) return;
        const timestamp = new Date().toISOString();
        const logMessage = `${this.prefix} [${timestamp}] ${level}: ${message}`;
        if (data) {
            console.log(logMessage, data);
        } else {
            console.log(logMessage);
        }
    },
    info: function(message, data) { this.log('INFO', message, data); },
    warn: function(message, data) { this.log('WARN', message, data); },
    error: function(message, data) { this.log('ERROR', message, data); },
    debug: function(message, data) { this.log('DEBUG', message, data); },
    network: function(message, data) { this.log('NETWORK', message, data); }
};

/**
 * Hybrid VideoRenderer class
 */
class HybridVideoRenderer {
    constructor(options = {}) {
        this.processedVideos = window.processedVideos;
        this.processingVideos = window.processingVideos;
        this.debugLogger = VideoDebugLogger;
        
        this.defaultOptions = {
            containerSelectors: ['.video-embed-container'],
            fileProxySelectors: ['.file-to-proxy'],
            targetContainers: ['body'],
            staggerDelay: 100,
            retryDelay: 200,
            preferStreaming: true, // Prefer streaming for secure uploads
            ...options
        };
        
        this.debugLogger.info('HybridVideoRenderer initialized', {
            options: this.defaultOptions
        });
    }

    /**
     * Enhanced URL analysis and processing
     */
    _analyzeVideoURL(url) {
        const analysis = {
            originalUrl: url,
            isSecureUpload: url.includes('secure-upload'),
            hasPrivatePrefix: url.includes('/private'),
            hasSignedParams: url.includes('?') && (url.includes('Signature') || url.includes('signature')),
            hasExpiration: url.includes('Expires') || url.includes('expires'),
            isS3: url.includes('amazonaws.com') || url.includes('.s3.'),
            protocol: url.substring(0, url.indexOf(':')),
            domain: url.includes('://') ? url.split('://')[1].split('/')[0] : 'unknown',
            recommendStreaming: false
        };
        
        // Determine if streaming is recommended
        analysis.recommendStreaming = analysis.isSecureUpload || analysis.isS3 || analysis.hasSignedParams;
        
        // Process URL like existing implementation
        let processedUrl = url;
        if (analysis.hasPrivatePrefix) {
            processedUrl = url.replace('/private', '');
            analysis.processedUrl = processedUrl;
        }
        
        this.debugLogger.network('URL Analysis Complete', analysis);
        return analysis;
    }

    /**
     * Check if video is still processing (from existing implementation)
     */
    async _checkProcessingStatus(url) {
        if (!url.includes('secure-upload')) {
            return { status: 'ready', progress: 100 };
        }
        
        const processedUrl = url.replace('/private', '');
        
        try {
            this.debugLogger.network('Checking processing status', { url: processedUrl });
            
            const response = await $.ajax({ 
                url: processedUrl, 
                method: 'GET',
                timeout: 10000 // 10 second timeout
            });
            
            this.debugLogger.network('Processing status response', response);
            return response;
        } catch (error) {
            this.debugLogger.error('Processing status check failed', {
                error: error.message,
                status: error.status,
                url: processedUrl
            });
            
            if (error.status === 404) {
                return { status: 'error', error: 'File not found' };
            }
            return { status: 'error', error: error.message };
        }
    }

    /**
     * Enhanced video loading with smart approach selection
     */
    async _loadVideo(container, url) {
        const currentPlaceholder = container.find('.video-loading-placeholder');
        if (currentPlaceholder.length === 0) {
            this.debugLogger.warn('No placeholder found for video loading', { url });
            return;
        }
        
        const analysis = this._analyzeVideoURL(url);
        
        // Update loading message
        const loadingMsg = $('<div>', {
            html: 'Loading video...',
            'data-video-loading': 'true',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
        currentPlaceholder.replaceWith(loadingMsg);
        
        try {
            // Check processing status for secure uploads
            if (analysis.isSecureUpload) {
                this.debugLogger.info('Checking secure upload processing status', { url });
                
                const statusResult = await this._checkProcessingStatus(url);
                
                if (statusResult.status === 'error') {
                    throw new Error(statusResult.error || 'Processing failed');
                }
                
                if (['to process', 'processing'].includes(statusResult.status)) {
                    this._showProcessingStatus(loadingMsg, statusResult.progress || 0);
                    
                    // Retry after delay
                    setTimeout(() => {
                        this.debugLogger.info('Retrying video after processing delay', { url });
                        this._loadVideo(container, url);
                    }, 60000); // Retry in 1 minute
                    return;
                }
            }
            
            // Choose loading approach
            if (analysis.recommendStreaming && this.defaultOptions.preferStreaming) {
                this.debugLogger.info('Using streaming approach', analysis);
                await this._loadVideoStreaming(container, loadingMsg, analysis);
            } else {
                this.debugLogger.info('Using blob approach', analysis);
                await this._loadVideoBlob(container, loadingMsg, analysis);
            }
            
        } catch (error) {
            this.debugLogger.error('Video loading failed', {
                error: error.message,
                url: url,
                analysis: analysis
            });
            
            this._showError(loadingMsg, error.message);
            
            // Remove from processing on error
            if (url) {
                this.processingVideos.delete(url);
            }
        }
    }

    /**
     * Streaming approach (adapted from existing implementation)
     */
    async _loadVideoStreaming(container, loadingMsg, analysis) {
        const url = analysis.processedUrl || analysis.originalUrl;
        
        this.debugLogger.network('Starting streaming approach', { url });
        
        // Determine if it's audio or video
        const isAudio = url.includes('/audio/');
        const posterUrl = url.replace('/video/', '/poster/').split('.')[0];
        
        let player;
        if (isAudio) {
            player = $('<audio controls crossorigin playsinline />');
        } else {
            player = $('<video controls crossorigin playsinline data-poster="' + posterUrl + '" style="width: 100%;" />');
        }
        
        // Create container for the player
        const playerContainer = $('<div>', {
            class: 'video-stream-container',
            css: {
                width: '100%',
                margin: '1rem 0'
            }
        });
        
        playerContainer.append(player);
        loadingMsg.replaceWith(playerContainer);
        
        // Initialize streaming player
        this._initializeStreamPlayer(player, url);
        
        // Mark as processed
        this.processedVideos.add(analysis.originalUrl);
        this.processingVideos.delete(analysis.originalUrl);
        
        this.debugLogger.info('Streaming video initialized', {
            url: analysis.originalUrl,
            isAudio: isAudio
        });
    }

    /**
     * Blob approach (enhanced from new implementation)
     */
    async _loadVideoBlob(container, loadingMsg, analysis) {
        const url = analysis.processedUrl || analysis.originalUrl;
        
        this.debugLogger.network('Starting blob approach', { url });
        
        const fetchStartTime = performance.now();
        
        const response = await fetch(url, { 
            method: 'GET', 
            credentials: 'include',
            headers: {
                'Accept': 'video/*',
                'User-Agent': navigator.userAgent
            }
        });
        
        const fetchEndTime = performance.now();
        
        // Log response details
        const responseInfo = {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            headers: {},
            fetchDuration: fetchEndTime - fetchStartTime
        };
        
        response.headers.forEach((value, key) => {
            responseInfo.headers[key] = value;
        });
        
        this.debugLogger.network('Blob fetch response', responseInfo);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const blob = await response.blob();
        
        this.debugLogger.network('Blob created', {
            blobSize: blob.size,
            blobSizeMB: (blob.size / 1024 / 1024).toFixed(2) + ' MB',
            blobType: blob.type
        });
        
        if (loadingMsg.parent().length === 0) {
            URL.revokeObjectURL(URL.createObjectURL(blob));
            return;
        }
        
        const blobUrl = URL.createObjectURL(blob);
        const video = this._createBlobVideoElement(analysis.originalUrl, blobUrl);
        
        loadingMsg.replaceWith(video);
        
        // Mark as processed
        this.processedVideos.add(analysis.originalUrl);
        this.processingVideos.delete(analysis.originalUrl);
        
        this.debugLogger.info('Blob video created', {
            url: analysis.originalUrl,
            blobUrl: blobUrl
        });
    }

    /**
     * Initialize streaming player (adapted from existing implementation)
     */
    _initializeStreamPlayer(videoElement, src) {
        this.debugLogger.debug('Initializing stream player', { src });
        
        // Check device type and initialize appropriate player
        if (this._isAppleDevice()) {
            this._initializeHLSPlayer(videoElement, src);
        } else {
            this._initializeDASHPlayer(videoElement, src);
        }
    }

    /**
     * Initialize HLS player for Apple devices
     */
    _initializeHLSPlayer(videoElement, src) {
        this.debugLogger.debug('Initializing HLS player', { src });
        
        let firstLoad = true;
        const parent = $(videoElement).parent();
        
        // Default Plyr options (would need to be defined or imported)
        const options = this._getDefaultPlyrOptions();
        
        if (!window.Hls || !window.Hls.isSupported()) {
            this.debugLogger.warn('HLS not supported, falling back to native playback');
            videoElement[0].src = src;
            options.quality = null;
            return new window.Plyr(videoElement[0], options);
        }
        
        const hls = new window.Hls();
        
        options.quality.onChange = function (quality) {
            hls.levels.forEach((level, levelIndex) => {
                if (level.height !== quality) return;
                hls.currentLevel = levelIndex;
            });
        };
        
        const player = new window.Plyr(videoElement[0], options);
        parent.find('.plyr').wrap('<div class="card my-3"></div>');
        
        player.on('play', () => {
            if (!firstLoad) return;
            firstLoad = false;
            hls.loadSource(src);
            hls.attachMedia(videoElement[0]);
        });
        
        return player;
    }

    /**
     * Initialize DASH player for other devices
     */
    _initializeDASHPlayer(videoElement, src) {
        this.debugLogger.debug('Initializing DASH player', { src });
        
        let firstLoad = true;
        const parent = $(videoElement).parent();
        
        if (!window.dashjs) {
            this.debugLogger.error('DASH.js not available, falling back to native playback');
            videoElement[0].src = src;
            return;
        }
        
        const dash = window.dashjs.MediaPlayer().create();
        const options = this._getDefaultPlyrOptions();
        
        options.quality.onChange = function (quality) {
            if (firstLoad) return;
            const t = options.quality.options.findIndex((q) => q === quality);
            dash.setQualityFor("video", t);
        };
        
        const player = new window.Plyr(videoElement[0], options);
        parent.find('.plyr').wrap('<div class="card my-3"></div>');
        
        player.on('play', () => {
            if (!firstLoad) return;
            firstLoad = false;
            dash.initialize(videoElement[0], src, false);
        });
        
        return player;
    }

    /**
     * Create blob-based video element
     */
    _createBlobVideoElement(url, blobUrl) {
        const video = $('<video>', {
            controls: true,
            preload: 'metadata',
            'data-video-url': url,
            'data-blob-url': blobUrl,
            'data-approach': 'blob',
            css: {
                width: '100%',
                maxWidth: '800px',
                margin: '1rem 0',
                display: 'block'
            }
        });
        
        video.attr('src', blobUrl);
        
        // Enhanced event handlers
        video.on('loadedmetadata', () => {
            const videoEl = video[0];
            this.debugLogger.debug('Blob video metadata loaded', {
                url: url,
                duration: videoEl.duration,
                dimensions: `${videoEl.videoWidth}x${videoEl.videoHeight}`
            });
        });
        
        video.on('error', (e) => {
            const videoEl = video[0];
            const error = videoEl.error;
            this.debugLogger.error('Blob video error', {
                url: url,
                errorCode: error ? error.code : 'unknown',
                errorMessage: error ? error.message : 'unknown'
            });
        });
        
        // Cleanup blob URL when video is removed
        video.on('remove', () => {
            this.debugLogger.debug('Cleaning up blob URL', { url, blobUrl });
            URL.revokeObjectURL(blobUrl);
        });
        
        $(window).on('beforeunload', () => {
            URL.revokeObjectURL(blobUrl);
        });
        
        return video;
    }

    /**
     * Show processing status for videos still being processed
     */
    _showProcessingStatus(loadingMsg, progress) {
        const progressHtml = `
            <div class="video-processing-status">
                <p>Processing ${progress}%</p>
                <div class="progress-bar" style="width: 100%; background: #f0f0f0; border-radius: 4px; overflow: hidden;">
                    <div style="width: ${progress}%; height: 20px; background: #007bff; transition: width 0.3s;"></div>
                </div>
                <small>You can save this file and close this tab. Feel free to return later to view the video once processing is complete.</small>
            </div>
        `;
        
        loadingMsg.html(progressHtml);
    }

    /**
     * Show error message
     */
    _showError(loadingMsg, errorMessage) {
        const errorHtml = `
            <div class="video-error-status" style="padding: 1rem; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
                <strong>Error:</strong> ${errorMessage}<br>
                <small>Check browser console for detailed logs</small>
            </div>
        `;
        
        loadingMsg.html(errorHtml);
        loadingMsg.removeClass('video-loading-placeholder').addClass('video-error');
    }

    /**
     * Utility functions
     */
    _isAppleDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }

    _getDefaultPlyrOptions() {
        // Return default Plyr options or fallback
        if (window.defaultPlyrOptions) {
            return { ...window.defaultPlyrOptions };
        }
        
        return {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            quality: {
                default: 720,
                options: [1080, 720, 480, 360],
                forced: true,
                onChange: null
            }
        };
    }

    // Include all the standard methods from the original implementation
    activateVideoPlayers(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const containers = this._getTargetContainers(config.targetContainers);
        
        this.debugLogger.info('Activating hybrid video players', {
            config: config,
            containerCount: containers.length
        });
        
        if (containers.length === 0) {
            return;
        }

        this._processVideoContainers(containers, config);
        this._processFileProxyElements(containers, config);
        this._processUploadedMediaElements(containers, config);
    }

    _getTargetContainers(selectors) {
        if (typeof $ === 'undefined') {
            this.debugLogger.error('jQuery not available');
            return { length: 0, find: () => ({ length: 0, each: () => {} }) };
        }
        
        const containers = [];
        selectors.forEach(selector => {
            const elements = $(selector);
            if (elements.length > 0) {
                containers.push(...elements.toArray());
            }
        });
        return $(containers);
    }

    _processVideoContainers(containers, config) {
        const videoContainers = containers.find(config.containerSelectors.join(', '));
        
        videoContainers.each((index, element) => {
            this._processVideoContainer($(element), index * config.staggerDelay);
        });
    }

    _processVideoContainer(container, delay = 0) {
        const videoUrl = container.attr('data-video-url');
        
        if (videoUrl && this.processingVideos.has(videoUrl)) {
            return;
        }
        
        if (videoUrl && this.processedVideos.has(videoUrl)) {
            const existingVideo = container.find('video');
            if (existingVideo.length > 0 && !existingVideo[0].error) {
                return;
            }
            this.processedVideos.delete(videoUrl);
        }
        
        // Clear existing content
        container.find('video, .video-loading-placeholder, .video-stream-container').remove();
        container.append(this._createPlaceholder());
        
        if (videoUrl) {
            this.processingVideos.add(videoUrl);
            setTimeout(() => {
                this._loadVideo(container, videoUrl);
            }, delay);
        }
    }

    _createPlaceholder() {
        return $('<div>', {
            class: 'video-loading-placeholder',
            html: 'Loading video...',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
    }

    // Add other necessary methods (file proxy processing, etc.)
    _processFileProxyElements(containers, config) {
        const proxyElements = containers.find(config.fileProxySelectors.join(', '));
        
        proxyElements.each((index, element) => {
            this._convertFileProxyToVideoContainer($(element), config.retryDelay + (index * config.staggerDelay));
        });
    }

    _convertFileProxyToVideoContainer(element, delay = 0) {
        const url = element.attr('data-url');
        if (!url || this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'mp4',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _processUploadedMediaElements(containers, config) {
        const uploadedMediaElements = containers.find('.uploaded-media');
        
        uploadedMediaElements.each((index, element) => {
            const $element = $(element);
            const $img = $element.find('img');
            
            if ($img.length > 0) {
                const src = $img.attr('src');
                
                if (src && this._isVideoFile(src)) {
                    this._convertUploadedMediaToVideoContainer($element, src, config.retryDelay + (index * config.staggerDelay));
                }
            }
        });
    }

    _isVideoFile(url) {
        const videoExtensions = ['mp4', 'mkv', 'webm', 'avi', 'mpeg', 'mov', 'mp3'];
        const urlWithoutParams = url.split('?')[0];
        const extension = urlWithoutParams.split('.').pop().toLowerCase();
        return videoExtensions.includes(extension);
    }

    _convertUploadedMediaToVideoContainer(element, url, delay = 0) {
        if (this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'mp4',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    clearVideoTracking(urlsToRemove = []) {
        this.debugLogger.info('Clearing video tracking', {
            urlsToRemove: urlsToRemove
        });
        
        if (urlsToRemove.length === 0) {
            const videosWithErrors = $('video[data-blob-url], video[data-approach]');
            videosWithErrors.each((index, video) => {
                if (video.error || video.networkState === 3) {
                    const videoUrl = $(video).attr('data-video-url');
                    if (videoUrl) {
                        this.processedVideos.delete(videoUrl);
                        this.processingVideos.delete(videoUrl);
                    }
                }
            });
        } else {
            urlsToRemove.forEach(url => {
                this.processedVideos.delete(url);
                this.processingVideos.delete(url);
            });
        }
    }
}

// Initialize hybrid renderer
window.HybridVideoRenderer = HybridVideoRenderer;
window.hybridVideoRenderer = new HybridVideoRenderer();

// Global functions
window.activateAllVideoPlayers = function(options = {}) {
    return window.hybridVideoRenderer.activateVideoPlayers(options);
};

window.activateArticleVideoPlayers = function(options = {}) {
    return window.hybridVideoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        ...options
    });
};