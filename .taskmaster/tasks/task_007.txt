# Task ID: 7
# Title: Enhance Permalink Display
# Status: done
# Dependencies: 2, 5
# Priority: medium
# Description: Implement a new permalink display below the article title and hide existing permalink controls, using the theme-ph.scss design system.
# Details:
1. Modify the title section in src/views/form.hbs to include permalink display
2. Style the permalink display using the theme-ph.scss design system:
   - Use $secondary-text-color and $slate-400 for permalink text
   - Apply .ph-secondary-text-color class for consistent styling
   - Use $surface-100 ($border-color) for any permalink container borders if needed
   - For any background elements, use slate colors ($slate-25, $slate-50, $slate-75) or white
   - Follow existing typography styles from the design system
   - Apply $border_radius-* variables for any containers
   - Use $accent-blue-* colors for interactive elements (e.g., copy button)
3. Hide the existing permalink input field and related buttons
4. Preserve all permalink-related JavaScript functions for future use
5. Implement a copy-to-clipboard functionality for the permalink
6. Add a tooltip to explain the permalink's purpose
7. Ensure the permalink updates dynamically if the title changes

# Test Strategy:
1. Verify permalink displays correctly below the title
2. Confirm the styling matches the theme-ph.scss design system
3. Verify the permalink text uses the correct slate color variables
4. Test that hidden permalink controls don't affect functionality
5. Ensure copy-to-clipboard feature works across browsers
6. Check that tooltip appears and is accessible
7. Validate that permalink updates correctly with title changes
8. Test permalink display on various screen sizes
9. Verify the permalink styling is consistent with other secondary text elements
10. Confirm that any background elements use appropriate slate colors or white, not surface colors
