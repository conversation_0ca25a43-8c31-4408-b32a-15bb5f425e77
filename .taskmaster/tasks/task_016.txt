# Task ID: 16
# Title: Integrate OpenAI API for Structured Output Generation
# Status: done
# Dependencies: None
# Priority: high
# Description: Add OpenAI npm package to the project and create a utility function that uses the gpt-4o-mini model to generate structured JSON output.
# Details:
1. Update src/package.json to include the OpenAI npm package compatible with Node.js 20:
   ```json
   {
     "dependencies": {
       "openai": "^4.0.0",
       "dotenv": "^16.0.0"
     }
   }
   ```

2. Create a new file src/utils/openaiService.js for the OpenAI integration:
   ```javascript
   const OpenAI = require('openai');
   const dotenv = require('dotenv');

   dotenv.config();

   /**
    * Generates structured JSON output using OpenAI's gpt-4o-mini model
    * @param {string} prompt - The prompt to send to the OpenAI API
    * @returns {Object} - Parsed JSON object from the API response
    * @throws {Error} - Various error types with descriptive messages
    */
   async function generateStructuredOutput(prompt) {
     // Input validation
     if (!prompt || typeof prompt !== 'string') {
       throw new Error('Invalid prompt: Must provide a non-empty string');
     }

     // API key validation
     if (!process.env.OPENAI_API_KEY) {
       throw new Error('Missing OpenAI API key: Set OPENAI_API_KEY in .env file');
     }

     const openai = new OpenAI({
       apiKey: process.env.OPENAI_API_KEY
     });

     try {
       const response = await openai.chat.completions.create({
         model: "gpt-4o-mini",
         messages: [{ role: "user", content: prompt }],
         response_format: { type: "json_object" }
       });
       return JSON.parse(response.choices[0].message.content);
     } catch (error) {
       if (error.code === 'insufficient_quota') {
         throw new Error('OpenAI API quota exceeded: Check your billing settings');
       } else if (error.code === 'invalid_api_key') {
         throw new Error('Invalid OpenAI API key: Check your API key configuration');
       } else if (error instanceof SyntaxError) {
         throw new Error('Failed to parse JSON response from OpenAI API');
       } else {
         console.error('Error calling OpenAI API:', error);
         throw new Error('Failed to generate structured output: ' + error.message);
       }
     }
   }

   module.exports = { generateStructuredOutput };
   ```

3. Create a .env file in src/.env and add the OpenAI API key:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

4. Update .gitignore to exclude the .env file:
   ```
   .env
   ```

5. Function usage example:
   ```javascript
   const { generateStructuredOutput } = require('./utils/openaiService');

   // Example usage
   const result = await generateStructuredOutput('Generate a JSON object with user data');
   ```

# Test Strategy:
No testing required for this task. Focus only on implementing the basic utility function.

# Subtasks:
## 16.1. Add OpenAI and dotenv dependencies to package.json [completed]
### Dependencies: None
### Description: 
### Details:


## 16.2. Create openaiService.js with robust error handling [completed]
### Dependencies: None
### Description: 
### Details:


## 16.3. Set up .env file and update .gitignore [completed]
### Dependencies: None
### Description: 
### Details:


## 16.4. Install dependencies with npm install [completed]
### Dependencies: None
### Description: 
### Details:


## 16.5. Replace placeholder API key in .env with actual OpenAI API key [done]
### Dependencies: None
### Description: 
### Details:


