# Task ID: 6
# Title: Implement Conditional Preview Panel
# Status: done
# Dependencies: 2, 5
# Priority: medium
# Description: Modify the preview panel to be visible only when the markdown editor mode is enabled, using the theme-ph.scss design system.
# Details:
1. Update #preview-wrap visibility logic in src/views/form.hbs
2. Modify changeToMarkdown() and changeToWysiwyg() functions in src/public/javascripts/openKB.js
3. Implement a CSS class for hiding/showing the preview panel
4. Apply theme-ph.scss design system variables:
   - Use $slate-* variables for text and panel backgrounds ($slate-25, $slate-50, $slate-75 or white)
   - Use $surface-100 ($border-color) for panel borders
   - Apply $border_radius-* for consistent corner styling
   - Implement $natural-shadow-* for panel elevation if needed
   - Use form styling variables for any controls within the panel
   - Apply $accent-blue-* for interactive elements and highlights
5. Ensure smooth transitions when toggling preview visibility
6. Maintain existing preview synchronization functionality
7. Update any event listeners related to preview panel
8. Implement responsive behavior for preview panel on smaller screens

# Test Strategy:
1. Test switching between Markdown and WYSIWYG modes
2. Verify preview panel appears only in Markdown mode
3. Check that preview synchronization still works correctly
4. Validate design system implementation:
   - Confirm correct color variables are applied (slate colors for backgrounds, surface-100 for borders)
   - Verify border radius consistency with other UI elements
   - Check shadow implementation if used
   - Test that accent colors are properly applied to interactive elements
5. Test preview panel behavior on different screen sizes
6. Ensure no console errors occur during editor mode switches
7. Validate that hiding preview doesn't affect other layout elements
8. Verify the styling is consistent with other components using the theme-ph.scss system
