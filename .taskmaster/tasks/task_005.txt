# Task ID: 5
# Title: Migrate Form Elements to Cards
# Status: deferred
# Dependencies: 4
# Priority: high
# Description: Move existing form elements from their current locations to the appropriate cards in the right column.
# Details:
1. Carefully move each form element to its designated card:
   - Publish Card: frm_kb_published, frm_kb_visible_state, frm_kb_regional_visibility, frm_kb_language, frm_kb_featured
   - Security Card: frm_kb_security_level, frm_kb_password, frm_kb_admin_restricted
   - Topics Card: selectTopics, selectSubtopics
2. Preserve all input names, IDs, and data attributes
3. Maintain all validation attributes and aria labels
4. Update any JavaScript selectors that reference moved elements
5. Ensure Bootstrap form styles are applied correctly in the new location
6. Implement custom styling for multi-select and complex inputs
7. Use CSS Grid or Flexbox for form layout within cards

# Test Strategy:
1. Verify all form elements are present in their new locations
2. Test form submission to ensure all data is correctly sent
3. Validate that all JavaScript event handlers still work
4. Check that validation attributes are functioning
5. Test accessibility of form elements using screen readers
6. Ensure form elements are styled consistently within cards

# Subtasks:
## 1. Integrate Portal Front-End Select Picker Component [pending]
### Dependencies: None
### Description: Reuse the selectpicker function from portal-front-end/__shared-functions.js and copy related styles from theme-ph.scss to maintain consistency across the platform
### Details:
1. Copy the selectpicker function from portal-front-end/src/js/__shared-functions.js (line 1492) to the wiki project\n2. Copy related styles from portal-front-end/src/scss/styles/themes/theme-ph.scss that target the select picker elements\n3. Adapt the function to work with the wiki's existing form structure\n4. Update any form dropdowns in the cards to use the new selectpicker component\n5. Ensure styling consistency between portal and wiki interfaces\n6. Test functionality with existing form validation and submission logic

