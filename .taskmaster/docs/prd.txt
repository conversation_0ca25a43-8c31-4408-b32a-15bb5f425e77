# Wiki Article Editor Modernization - Product Requirements Document

## Project Overview

This project involves modernizing the legacy wiki article editor interface by restructuring the layout, improving user experience, and reorganizing form elements into a more intuitive card-based design. The current editor uses legacy technologies including jQuery, Bootstrap 3/4, and Handlebars templating.

## Current Technology Stack

- **Backend**: Node.js with Express
- **Frontend**: j<PERSON>uery, Bootstrap 3/4, <PERSON>lebars templating
- **Editors**: SimpleMDE (Markdown), <PERSON><PERSON><PERSON>yg (WYSIWYG)
- **Styling**: SCSS with legacy CSS patterns
- **Build**: Basic npm scripts with Docker

## Project Goals

1. **Modernize Layout Structure**: Transform the current editor layout into a clean, card-based interface
2. **Improve User Experience**: Streamline the editing workflow by reorganizing form elements logically
3. **Maintain Functionality**: Preserve all existing JavaScript logic and form submission behavior
4. **Responsive Design**: Ensure the new layout works across all device sizes
5. **Performance**: Maintain or improve current performance metrics

## Detailed Requirements

### 1. Preview Panel Management

**Current State**: Preview panel is always visible alongside the markdown editor
**Required Changes**:
- Preview panel should ONLY be visible when markdown editor mode is enabled
- Preview panel must be completely hidden when WYSIWYG mode is active
- Maintain existing preview synchronization functionality for markdown mode
- Preserve existing editor switching logic between markdown and WYSIWYG modes

**Technical Implementation**:
- Modify the `#preview-wrap` visibility logic in `src/views/form.hbs`
- Update JavaScript in `src/public/javascripts/openKB.js` to handle preview panel toggling
- Ensure `changeToMarkdown()` and `changeToWyiswyg()` functions properly show/hide preview

### 2. Permalink Display Enhancement

**Current State**: Permalink field is located below the editor with validate/generate buttons
**Required Changes**:
- Display the permalink prominently below the article title
- Show as read-only text or styled display element
- Remove or temporarily hide the validate, generate, and slug buttons
- Hide the permalink input field temporarily (for future use)

**Technical Implementation**:
- Modify title section in `src/views/form.hbs` to include permalink display
- Style the permalink display to be visually distinct but not intrusive
- Comment out or hide permalink manipulation buttons and input field
- Preserve all existing permalink-related JavaScript functions for future restoration

### 3. Side Panel Removal

**Current State**: Collapsible right-side panel with settings (`.settings-wrap` class)
**Required Changes**:
- Completely remove the existing right-side panel that slides in/out
- Remove the toggle button (`#toggleNavSmallBtn`) and associated functionality
- Remove related CSS classes: `.settings-wrap`, `.settings-toggle`, `.expanded`
- Clean up JavaScript event handlers for panel toggling

**Technical Implementation**:
- Remove settings panel HTML structure from `src/views/form.hbs`
- Remove related SCSS from `wiki-themes/12rnd/scss/newstyles.scss`
- Clean up JavaScript in `wiki-themes/12rnd/js/edit-article.js`
- Remove localStorage settings persistence for panel state

### 4. New Right Column Implementation

**Current State**: No structured right column for form elements
**Required Changes**:
- Create a new right column alongside the main editor area
- Implement three vertically stacked cards with specific content
- Ensure responsive behavior on smaller screens
- Maintain all existing form functionality and validation

#### Card 1: Publish Settings
**Content**:
- **Title**: "Publish"
- **Form Elements**:
  - Status dropdown (`frm_kb_published`)
  - Visibility dropdown (`frm_kb_visible_state`)
  - Regional Visibility multi-select (`frm_kb_regional_visibility`)
  - Language dropdown (`frm_kb_language`)
  - Featured Article checkbox (`frm_kb_featured`)
- **Action Buttons**:
  - Delete button (existing delete functionality)
  - Save Draft button (save with published=false)
  - Publish/Save button (main save action)

#### Card 2: Security Settings
**Content**:
- **Title**: "Security"
- **Form Elements**:
  - Security Level dropdown (`frm_kb_security_level`)
  - Password field (`frm_kb_password`)
  - "Only admins can edit" checkbox (`frm_kb_admin_restricted`)

#### Card 3: Topic Management
**Content**:
- **Title**: "Topics"
- **Form Elements**:
  - Topic dropdown (`frm_kb_pinned_topic` / `selectTopics`)
  - Subtopic dropdown (`frm_kb_pinned_subtopic` / `selectSubtopics`)

### 5. Layout Structure Requirements

**Main Layout**:
```
┌─────────────────────────────────────┬─────────────────┐
│ Title Input                         │                 │
├─────────────────────────────────────┤                 │
│ Permalink Display                   │                 │
├─────────────────────────────────────┤   Right Column  │
│                                     │   (3 Cards)     │
│ Editor Area                         │                 │
│ (Markdown/WYSIWYG + Preview)        │                 │
│                                     │                 │
└─────────────────────────────────────┴─────────────────┘
```

**Responsive Behavior**:
- Desktop (>992px): Side-by-side layout as shown above
- Tablet (768px-992px): Right column moves below editor
- Mobile (<768px): Single column, cards stack vertically

### 6. Form Preservation Requirements

**Critical**: All existing form elements must be preserved with their current:
- Input names and IDs
- Validation attributes
- JavaScript event handlers
- Data attributes
- Default values and selected states

**Form Elements to Relocate** (from existing locations to new cards):
- `frm_kb_published` → Publish Card
- `frm_kb_visible_state` → Publish Card  
- `frm_kb_regional_visibility` → Publish Card
- `frm_kb_language` → Publish Card
- `frm_kb_featured` → Publish Card
- `frm_kb_security_level` → Security Card
- `frm_kb_password` → Security Card
- `frm_kb_admin_restricted` → Security Card
- `selectTopics` → Topics Card
- `selectSubtopics` → Topics Card

### 7. JavaScript Functionality Preservation

**Required**: Maintain all existing JavaScript functionality:
- Form submission logic (`submitEditArticleForm`, `submitNewArticleForm`)
- Editor switching between Markdown and WYSIWYG
- Preview synchronization and scrolling
- Topic/subtopic cascading dropdowns
- Regional visibility multi-select functionality
- Validation and error handling
- Auto-save functionality (if present)

### 8. Styling Requirements

**Design System**:
- Use existing Bootstrap classes where possible
- Maintain current color scheme and typography
- Cards should have subtle borders and shadows
- Consistent spacing using existing spacing utilities
- Preserve existing button styles and states

**Card Styling**:
- Clean, minimal card design with subtle borders
- Clear section headers with appropriate typography
- Proper spacing between form elements
- Responsive padding and margins
- Hover states for interactive elements

### 9. Browser Compatibility

**Target Browsers**:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

**Legacy Support**:
- Maintain existing IE11 support if currently supported
- Ensure graceful degradation for older browsers

### 10. Performance Requirements

**Metrics to Maintain**:
- Page load time should not increase by more than 10%
- JavaScript execution time should remain similar
- CSS bundle size should not significantly increase
- Maintain existing accessibility standards

## Implementation Phases

### Phase 1: Layout Restructuring
1. Remove existing side panel
2. Create new right column structure
3. Implement basic card layout
4. Ensure responsive behavior

### Phase 2: Form Element Migration
1. Move form elements to appropriate cards
2. Preserve all existing functionality
3. Test form submissions and validation
4. Update JavaScript event bindings if needed

### Phase 3: Preview Panel Logic
1. Implement conditional preview visibility
2. Test editor mode switching
3. Verify preview synchronization

### Phase 4: Permalink Enhancement
1. Add permalink display below title
2. Hide permalink editing controls
3. Style permalink display appropriately

### Phase 5: Testing & Polish
1. Cross-browser testing
2. Responsive design verification
3. Accessibility testing
4. Performance optimization

## Success Criteria

1. **Functional**: All existing editor functionality works without regression
2. **Visual**: New layout matches design requirements and is visually appealing
3. **Responsive**: Layout works properly on all target screen sizes
4. **Performance**: No significant performance degradation
5. **Maintainable**: Code is clean, well-organized, and follows existing patterns

## Risk Mitigation

1. **JavaScript Conflicts**: Thoroughly test all existing JavaScript functionality
2. **CSS Conflicts**: Use specific selectors to avoid styling conflicts
3. **Form Submission**: Extensive testing of form data collection and submission
4. **Browser Compatibility**: Test across all target browsers
5. **Responsive Issues**: Test on various device sizes and orientations

## Technical Constraints

1. Must work within existing Handlebars templating system
2. Cannot modify backend form processing logic
3. Must maintain existing CSS class structure where possible
4. Should minimize changes to existing JavaScript files
5. Must preserve all existing form validation logic

## Future Considerations

1. Potential migration to modern JavaScript framework
2. Implementation of real-time collaborative editing
3. Enhanced accessibility features
4. Mobile-first responsive design improvements
5. Integration with modern build tools and bundlers 