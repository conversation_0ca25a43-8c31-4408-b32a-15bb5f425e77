<p align="center">
  <h3 align="center">12RND Blog</h3>
</p>

<!-- GETTING STARTED -->

## Getting Started

To get a local copy up and running follow these simple example steps.

### Prerequisites

- npm

```sh
https://www.npmjs.com/
```

- Docker Desktop

```sh
https://www.docker.com/products/docker-desktop
```

### Installation

1. Clone project

```sh
<NAME_EMAIL>:12rnd-UBX/wiki.git
```

2. Go to the wiki directory

```sh
cd wiki
```

2. Run project

```sh
npm start
```

3. View in browser

```sh
http://localhost:8080/openkb
```

4. Login at

```sh
http://localhost:8080/openkb/login
```
