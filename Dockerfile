FROM node:20-alpine3.18 as builder
RUN apk --no-cache add python3 make g++
WORKDIR /var/openKB
COPY src/package* /var/openKB/
RUN npm install

FROM node:20-alpine3.18

RUN apk update && apk add curl bash python3 make g++ jq

# Install pm2
RUN npm install pm2 -g

WORKDIR /var/openKB

COPY src/start-prod.sh .
COPY src/package.json .
COPY src/pm2.ecosystem.config.js .
COPY src/locales/ locales/
COPY src/public/ public/
COPY src/routes/ routes/
COPY src/utils/ utils/
COPY src/views/ views/
COPY src/app.js .
COPY src/gulpfile.js .
COPY src/language.yml .
COPY src/csv csv/
COPY src/migrate migrate/
COPY src/lib/ lib/

COPY --from=builder /var/openKB/node_modules node_modules

VOLUME /var/openKB/data

EXPOSE 4444

# Copy in Config & themes...
COPY wiki-themes /var/openKB/public/themes/
COPY wiki-config /var/openKB/config

RUN cd /var/openKB && npm run build

CMD ["bash", "start-prod.sh"]
