const OpenAI = require('openai');
require('dotenv').config();

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

function generateChangesPrompt(changes) {
    return `Article changes summary:
    
    ${changes
        .map(
            (change, index) => `
        Change ${index + 1} - ${change.fieldName}:
        <<<PREVIOUS_${index + 1}>>>
        ${change.oldText}
        <<<END_PREVIOUS_${index + 1}>>>
        
        <<<UPDATED_${index + 1}>>>
        ${change.newText}
        <<<END_UPDATED_${index + 1}>>>
    `
        )
        .join('\n')}`;
}

/**
 * Generate structured JSON output for article changes (handles both single and multiple changes)
 * @param {Array|string} input - Array of change objects OR old text for backward compatibility
 * @param {string} [newText] - New text (only used for backward compatibility)
 * @returns {Promise<Object>} - Parsed JSON response from OpenAI
 * @throws {Error} - If the API call fails or response is invalid
 */
async function generateStructuredOutput(input, newText = null) {
    try {
        if (!process.env.OPENAI_API_KEY) {
            throw new Error('OpenAI API key is not configured');
        }

        let changes = [];

        // Handle backward compatibility: single change with oldText/newText
        if (typeof input === 'string' && newText) {
            changes = [
                {
                    fieldName: 'Content',
                    oldText: input,
                    newText: newText,
                },
            ];
        }
        // Handle new format: array of changes
        else if (Array.isArray(input)) {
            changes = input;
        } else {
            throw new Error(
                'Invalid input: provide either changes array or oldText/newText strings'
            );
        }

        if (changes.length === 0) {
            throw new Error('Changes array cannot be empty');
        }

        const systemPrompt = `You are a precise and structured assistant that analyzes changes made to a wiki article. These changes may include Markdown, HTML, inline code (\`like this\`), and multi-line code blocks (e.g., triple backticks).

        Your task is to generate a comprehensive summary in the following strict JSON format:
        {
            "changes": {
                "type": "modified" | "none",
                "summary": "detailed description of what was changed but concise."
            }
        }

        Guidelines:
        - Provide a concise, coherent summary that covers all the changes as a unified update
        - Use simple, non-technical language that any user can understand
        - Dont include nor generate any unnecessary details like the purpose or the benefit of the changes.
        - Dont include in your assessment the modified/added element tags which are empty or have no content since they're mostly filled in by the editor.
        - Focus on what was actually changed, not technical implementation details. 
        - Group related changes together logically but concise in few words. (e.g., "Updated content and settings")
        - Return only one consolidated summary object for all changes`;

        const userPrompt = generateChangesPrompt(changes);

        const messages = [
            {
                role: 'system',
                content: systemPrompt,
            },
            {
                role: 'user',
                content: userPrompt,
            },
        ];

        const response = await openai.chat.completions.create({
            model: 'gpt-4.1-nano',
            messages,
            response_format: { type: 'json_object' },
            temperature: 0.2,
        });

        if (
            !response.choices ||
            !response.choices[0] ||
            !response.choices[0].message
        ) {
            throw new Error('Invalid response format from OpenAI API');
        }

        const content = response.choices[0].message.content;
        if (!content) {
            throw new Error('Empty response content from OpenAI API');
        }

        return JSON.parse(content);
    } catch (error) {
        console.error('Error calling OpenAI API:', error);

        // Re-throw with more specific error messages
        if (error.name === 'SyntaxError') {
            throw new Error('Failed to parse JSON response from OpenAI API');
        }

        if (error.code === 'insufficient_quota') {
            throw new Error('OpenAI API quota exceeded');
        }

        if (error.code === 'invalid_api_key') {
            throw new Error('Invalid OpenAI API key');
        }

        throw new Error(
            `Failed to generate structured output: ${error.message}`
        );
    }
}

module.exports = {
    generateStructuredOutput,
    generateChangesPrompt,
};
