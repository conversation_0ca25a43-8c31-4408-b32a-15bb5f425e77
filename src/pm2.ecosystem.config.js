const yaml = require('js-yaml');
const fs   = require('fs');

let CONTAINER_VARIABLES = {}
try {
    CONTAINER_VARIABLES = JSON.parse(fs.readFileSync('/tmp/hostvars.json', 'utf8'));
} catch(e) {}

const defaultEnv = (CONTAINER_VARIABLES.NODE_ENV !== undefined) ? CONTAINER_VARIABLES.NODE_ENV : 'development';
const showConfig = (CONTAINER_VARIABLES.SHOW_CONFIG !== undefined) ? CONTAINER_VARIABLES.SHOW_CONFIG : false;
const DEV_CONTAINER = process.env.DEV_CONTAINER || false;

console.log('DEV_CONTAINER', DEV_CONTAINER)

let defaultConfigFile = 'config.dev.yml';
let defaultEnvConfig = {
    NODE_ENV: defaultEnv
}
switch(defaultEnv) {
  case 'stage':
    defaultConfigFile = 'config.stg.yml';
    break;
  case 'staging':
    defaultConfigFile = 'config.stg.yml';
    break;
  case 'production':
    defaultConfigFile = 'config.prd.yml';
    break;
  default:
    defaultConfigFile = 'config.dev.yml';
}

try {
    let ymlFile = `${__dirname}/config/environment/${defaultConfigFile}`;
    console.log(`Starting with configuration file: ${ymlFile}`);

    const configFile = yaml.load(fs.readFileSync(ymlFile, 'utf8'));
    defaultEnvConfig = {...CONTAINER_VARIABLES, ...configFile.environment, ...defaultEnvConfig }
} catch (e) {
    console.error(e);
    process.exit();
}

if(showConfig) console.dir(defaultEnvConfig);

let wikiPM2Config = {
    name: "openkb",
    script: "./app.js",
    autorestart: true,
    exec_mode : 'fork',
    instances : 1,
    watch_delay: 1000,
    ignore_watch : ["node_modules", "*.log", "*.json"],
    env: defaultEnvConfig,
}

if(DEV_CONTAINER) wikiPM2Config['watch'] = ['app.js', 'routes', 'csv', 'language.yml', 'lib'];
console.dir(wikiPM2Config)

module.exports = {
    apps : [
        wikiPM2Config
    ]
}
