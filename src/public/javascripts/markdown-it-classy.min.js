!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).markdownItClassy=e()}}(function(){return function e(t,n,r){function o(f,i){if(!n[f]){if(!t[f]){var u="function"==typeof require&&require;if(!i&&u)return u(f,!0);if(s)return s(f,!0);var c=new Error("Cannot find module '"+f+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[f]={exports:{}};t[f][0].call(l.exports,function(e){var n=t[f][1][e];return o(n||e)},l,l.exports,e,t,n,r)}return n[f].exports}for(var s="function"==typeof require&&require,f=0;f<r.length;f++)o(r[f]);return o}({1:[function(e,t,n){"use strict";function r(e){return e>=c&&e<=l||e>=p&&e<=a||e>=d&&e<=h||e===y||e===g||e===k}function o(e){var t,n,o,s=e.pos,f=s,i="",u=!1;if(e.src.charCodeAt(s)!==v)return!1;for(s+=1;e.src.charCodeAt(s)!==_;){if(!r(e.src.charCodeAt(s)))return!1;i+=e.src.charAt(s),s+=1}if((s+=1)!==e.posMax&&e.src.charCodeAt(s)!==m)return!1;for(e.src.charCodeAt(f-1)===m&&(u=!0),e.pos=s,t=e.tokens.length-1;t>=0&&("em_close"!==e.tokens[t].type&&"strong_close"!==e.tokens[t].type);t-=1)if("em_open"===e.tokens[t].type||"strong_open"===e.tokens[t].type)return e.tokens[t].attrPush(["class",i]),(n=e.pending).charCodeAt(n.length-1)===k&&(e.pending=n.substring(0,n.length-1)),!0;return o=e.push("classy","classy",0),o.content=i,o.hidden=!0,o.preferOuter=u,!0}function s(e){var t,n=e.children,r=n.length;return!n[r-1]||"classy"===n[r-1].type&&1!==n.length?(t=n.pop(),r-=1,n[r-1]&&("softbreak"===n[r-1].type?n.pop(r-1):n[r-1].content=n[r-1].content.trim()),t):null}function f(e,t,n){var r,o,s=n+1;for(e[s].hidden&&(s+=1),t&&e[s+1]&&/_close$/.test(e[s+1].type)&&(s+=1),r=e[s].type.replace("_close","_open"),o=n;o>=0;o-=1)if(e[o].type===r&&e[o].level===e[s].level)return e[o]}function i(e){var t,n;for(t=0;t<e.tokens.length;t+=1)if("inline"===e.tokens[t].type)for(n=s(e.tokens[t]);n;)f(e.tokens,n.preferOuter,t).attrPush(["class",n.content]),n=s(e.tokens[t])}var u,c=48,l=57,p=65,a=90,d=97,h=122,y=95,g=45,k=32,v=123,_=125,m=10;u=function(e){e.inline.ruler.push("classy",o),e.core.ruler.push("classy",i)},t.exports=u},{}]},{},[1])(1)});