!function(){"use strict";var t=function(t){if(!t.getWrapperElement)throw"Invalid CodeMirror object given";this.codeMirror=t};t.prototype.getValue=function(){return this.codeMirror.getValue()},t.prototype.insertValue=function(t){this.codeMirror.replaceSelection(t)},t.prototype.setValue=function(t){var e=this.codeMirror.getCursor();this.codeMirror.setValue(t),this.codeMirror.setCursor(e)},t.attach=function(e,r){r=r||{};var n=new t(e),o=new inlineAttachment(r,n);e.getWrapperElement().addEventListener("paste",function(t){o.onPaste(t)},!1),e.setOption("onDragEvent",function(t,e){if("drop"===e.type)return e.stopPropagation(),e.preventDefault(),o.onDrop(e)})},inlineAttachment.editors.codemirror3=t;var e=function(e){t.call(this,e)};e.attach=function(e,r){r=r||{};var n=new t(e),o=new inlineAttachment(r,n);e.getWrapperElement().addEventListener("paste",function(t){o.onPaste(t)},!1),e.on("drop",function(t,e){return!!o.onDrop(e)&&(e.stopPropagation(),e.preventDefault(),!0)})},inlineAttachment.editors.codemirror4=e}();