

(function ($) {
  'use strict';

  var defaultOptions = {
    placeholder: 'Search/Filter Results',
    title: 'Add panel title',
    body: 'Add panel body',
  };


  var CONFIRM_EVENT = 'tbwconfirm',
    CANCEL_EVENT = 'tbwcancel';

  const getPanelId = () => {
    return Date.now()
  }

  // If the plugin is a button
  function buildButtonIcon() {
    if ($("#trumbowyg-myplugin").length > 0) {
      return;
    }

    /*
    When your button is created, an SVG will be inserted with an xref to a
    symbol living at the fragment "#trumbowyg-myplugin". For Trumbowyg's
    own plugins, this will come from a sprite sheet which is injected into
    the document, built from the icon SVG in "ui/icons" in your plugin's
    source tree. This is how you should organise things if you are
    proposing your plugin to be included in the Trumbowyg main
    distribution, or if you are rolling your own custom build of Trumbowyg
    for your site.

    But, nothing says it *has* to come from Trumbowyg's injected sprite
    sheet; it only requires that this symbol exists in your document. To
    allow stand-alone distribution of your plugin, we're going to insert a
    custom SVG symbol into the document with the correct ID.
    */
    const iconWrap = $(document.createElementNS("http://www.w3.org/2000/svg", "svg"));
    iconWrap.addClass("trumbowyg-custom-icons");

    // For demonstration purposes, we've taken the "File" icon from
    // Remix Icon - https://remixicon.com/
    iconWrap.html(`
        <symbol id="trumbowyg-add-panel-to-first" viewBox="0 0 1920 1920">
        <svg fill="#000000" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M338.824 57.966v222.155H0v1581.29h225.882V506.117h112.942v222.268l406.814-335.21L338.824 57.967Zm1355.407 448.15h225.882V280.122H1694.23v225.996Zm-338.937 0h225.995V280.122h-225.995v225.996Zm-338.823 0h225.882V280.122H1016.47v225.996ZM677.76 957.882h1242.353V732H677.76v225.882Zm0 451.765h1242.353v-225.995H677.76v225.995Zm0 451.765h1242.353v-225.883H677.76v225.883Z" fill-rule="evenodd"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-add-panel-to-end" viewBox="0 0 1920 1920" >
        <svg fill="#000000"  viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M677.647 283.848H1920V57.966H677.647v225.882ZM0 57.966v1581.29h348.65v222.155l441.938-335.097-441.939-335.322v222.268H225.882V57.966H0Zm677.647 677.76H1920V509.844H677.647v225.882Zm0 451.652H1920V961.496H677.647v225.882Zm1016.47 451.878H1920V1413.26h-225.882v225.996Zm-338.823 0h225.995V1413.26h-225.995v225.996Zm-338.823 0h225.882V1413.26H1016.47v225.996Z" fill-rule="evenodd"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-move-panel-up" viewBox="0 0 24 24">
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" mirror-in-rtl="true" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill="#000" d="M14.5 14h-13c-.828 0-1.5.67-1.5 1.5S.672 17 1.5 17h13c.828 0 1.5-.67 1.5-1.5s-.672-1.5-1.5-1.5zM14.5 7h-13C.672 7 0 7.672 0 8.5S.672 10 1.5 10h13c.828 0 1.5-.672 1.5-1.5S15.328 7 14.5 7z"></path> <path fill="#000" d="M23.71 16.29l-2-2c-.2-.19-.45-.29-.71-.29s-.51.1-.71.29l-2 2c-.28.29-.37.72-.21 1.09.15.38.52.62.92.62h.5v1.5c0 .83-.67 1.5-1.5 1.5H1.5c-.83 0-1.5.67-1.5 1.5S.67 24 1.5 24H18c2.48 0 4.5-2.02 4.5-4.5V18h.5c.4 0 .77-.24.92-.62.16-.37.07-.8-.21-1.09zM14.5 0h-13C.672 0 0 .672 0 1.5S.672 3 1.5 3h13c.828 0 1.5-.672 1.5-1.5S15.328 0 14.5 0z"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-move-panel-down" viewBox="0 0 24 24">
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" mirror-in-rtl="true" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill="#000" d="M14.5 10h-13C.672 10 0 9.33 0 8.5S.672 7 1.5 7h13c.828 0 1.5.67 1.5 1.5s-.672 1.5-1.5 1.5zM14.5 17h-13C.672 17 0 16.328 0 15.5S.672 14 1.5 14h13c.828 0 1.5.672 1.5 1.5s-.672 1.5-1.5 1.5z"></path> <path fill="#000" d="M23.71 7.71l-2 2c-.2.19-.45.29-.71.29s-.51-.1-.71-.29l-2-2c-.28-.29-.37-.72-.21-1.09.15-.38.52-.62.92-.62h.5V4.5c0-.83-.67-1.5-1.5-1.5H1.5C.67 3 0 2.33 0 1.5S.67 0 1.5 0H18c2.48 0 4.5 2.02 4.5 4.5V6h.5c.4 0 .77.24.92.62.16.37.07.8-.21 1.09zM14.5 24h-13C.672 24 0 23.328 0 22.5S.672 21 1.5 21h13c.828 0 1.5.672 1.5 1.5s-.672 1.5-1.5 1.5z"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-destroy-accordion" viewBox="0 0 24 24">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M10 12V17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M14 12V17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M4 7H20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M6 10V18C6 19.6569 7.34315 21 9 21H15C16.6569 21 18 19.6569 18 18V10" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5V7H9V5Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-destroy-panel" viewBox="0 0 24 24">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M10 12V17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M14 12V17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M4 7H20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M6 10V18C6 19.6569 7.34315 21 9 21H15C16.6569 21 18 19.6569 18 18V10" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5V7H9V5Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-add-panel-to-prev" viewBox="0 0 1920 1920">
        <svg fill="#000000"  viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M338.818 398.617 745.624 733.82l-406.806 335.204V846.759h-112.94v903.514H0V620.88h338.818V398.617ZM1920 1524.383v225.879H677.669v-225.879H1920Zm0-451.87v225.992H677.669v-225.992H1920ZM1242.365 620.87v225.878h-225.878V620.87h225.878Zm338.817 0v225.878h-225.878V620.87h225.878Zm338.818 0v225.878h-225.878V620.87H1920Zm0-451.87v225.991H677.669V169H1920Z" fill-rule="evenodd"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-add-panel-to-next" viewBox="0 0 24 24">
        <svg fill="#000000" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M1920 1524.407v225.882H677.647v-225.882H1920Zm0-451.878v225.996H677.647v-225.996H1920ZM225.882 169.068v451.877H348.65v-222.38l441.94 335.209-441.94 335.322V846.715H0V169.068h225.882Zm1016.47 451.81V846.76h-225.881V620.878h225.882Zm338.824 0V846.76h-225.882V620.878h225.882Zm338.824 0V846.76h-225.882V620.878H1920ZM1920 169v225.995H677.647V169H1920Z" fill-rule="evenodd"></path> </g></svg>
        </symbol>
        <symbol id="trumbowyg-create" viewBox="0 0 24 24">
        <svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>table-rows</title> <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="icon" fill="#000000" transform="translate(42.666667, 106.666667)"> <path d="M3.55271368e-14,85.3333333 L106.666667,85.3333333 L106.666667,128 L3.55271368e-14,128 L3.55271368e-14,85.3333333 Z M3.55271368e-14,4.26325641e-14 L106.666667,4.26325641e-14 L106.666667,42.6666667 L3.55271368e-14,42.6666667 L3.55271368e-14,4.26325641e-14 Z M3.55271368e-14,170.666667 L106.666667,170.666667 L106.666667,213.333333 L3.55271368e-14,213.333333 L3.55271368e-14,170.666667 Z M3.55271368e-14,256 L106.666667,256 L106.666667,298.666667 L3.55271368e-14,298.666667 L3.55271368e-14,256 Z M149.333333,85.3333333 L426.666667,85.3333333 L426.666667,128 L149.333333,128 L149.333333,85.3333333 Z M149.333333,4.26325641e-14 L426.666667,4.26325641e-14 L426.666667,42.6666667 L149.333333,42.6666667 L149.333333,4.26325641e-14 Z M149.333333,170.666667 L426.666667,170.666667 L426.666667,213.333333 L149.333333,213.333333 L149.333333,170.666667 Z M149.333333,256 L426.666667,256 L426.666667,298.666667 L149.333333,298.666667 L149.333333,256 Z" id="Combined-Shape"> </path> </g> </g> </g></svg>
        </symbol>
    `).appendTo(document.body);
  }


  $.extend(true, $.trumbowyg, {
    langs: {
      // jshint camelcase:false
      en: {
        accordion: 'Accordion',

      },
      az: {
        upload: 'Yüklə',
        file: 'Fayl',
        uploadError: 'Xəta'
      },
      by: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Памылка'
      },
      ca: {
        upload: 'Pujar fitxer',
        file: 'Fitxer',
        uploadError: 'Error'
      },
      cs: {
        upload: 'Nahrát obrázek',
        file: 'Soubor',
        uploadError: 'Chyba'
      },
      da: {
        upload: 'Upload',
        file: 'Fil',
        uploadError: 'Fejl'
      },
      de: {
        upload: 'Hochladen',
        file: 'Datei',
        uploadError: 'Fehler'
      },
      es: {
        upload: 'Subir archivo',
        file: 'Archivo',
        uploadError: 'Error'
      },
      et: {
        upload: 'Lae üles',
        file: 'Fail',
        uploadError: 'Viga'
      },
      fr: {
        upload: 'Envoi',
        file: 'Fichier',
        uploadError: 'Erreur'
      },
      hu: {
        upload: 'Feltöltés',
        file: 'Fájl',
        uploadError: 'Hiba'
      },
      ja: {
        upload: 'アップロード',
        file: 'ファイル',
        uploadError: 'エラー'
      },
      ko: {
        upload: '그림 올리기',
        file: '파일',
        uploadError: '에러'
      },
      pt_br: {
        upload: 'Enviar do local',
        file: 'Arquivo',
        uploadError: 'Erro'
      },
      ru: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Ошибка'
      },
      sl: {
        upload: 'Naloži datoteko',
        file: 'Datoteka',
        uploadError: 'Napaka'
      },
      sk: {
        upload: 'Nahrať',
        file: 'Súbor',
        uploadError: 'Chyba'
      },
      tr: {
        upload: 'Yükle',
        file: 'Dosya',
        uploadError: 'Hata'
      },
      zh_cn: {
        upload: '上传',
        file: '文件',
        uploadError: '错误'
      },
      zh_tw: {
        upload: '上傳',
        file: '文件',
        uploadError: '錯誤'
      },
    },
    // jshint camelcase:true

    plugins: {
      custom_accordion: {
        init: function (trumbowyg) {
          var t = trumbowyg
          trumbowyg.o.plugins.custom_accordion = $.extend(true, {}, defaultOptions, trumbowyg.o.plugins.custom_accordion || {});

          var btnDef = {
            fn: function () {

              const accordionCount = $('.accordion-item').length

              trumbowyg.saveRange();


              var fields = {

                // placeholder: {
                //   label: 'Placeholder',
                //   value: trumbowyg.o.plugins.custom_accordion.placeholder,
                //   required: true
                // },

                title: {
                  type: 'text',
                  label: 'Panel Title',
                  value: trumbowyg.o.plugins.custom_accordion.title,
                  required: true

                },

                body: {
                  type: 'text',
                  label: 'Panel Content',
                  required: true,
                  value: trumbowyg.o.plugins.custom_accordion.body,
                }
              };

              openModalInsert(
                trumbowyg,
                // Title
                "Accordion",

                // Fields
                fields,

                // Callback
                function (values) {
                  const id = accordionCount + 1
                  const panelId = getPanelId()
                  try {
                    var html = `<div id="accordion-${id}" class="accordion-item">
                                        <div class="card">
                                            <div class="card-header accordion-header">
                                                <a class="card-link" data-toggle="collapse" href="#collapse-${panelId}" aria-expanded="true">${values.title}</a>
                                            </div>
                                            <div id="collapse-${panelId}" class="collapse show" data-parent="#${id}">
                                                <div class="card-body">
                        <p>${values.body}</p>
                    </div>
                    </div>
                    </div><p><br /></p>`

                    trumbowyg.execCmd('insertHTML', html);


                    setTimeout(() => { trumbowyg.closeModal(); }, 250)
                    //trumbowyg.$c.trigger('tbwinsertalert', [trumbowyg, html]);
                  } catch (err) {
                    console.log(err);
                  }
                }
              );


            },
            text: 'Create Accordion',
            title: 'Create Accordion',
          
          };

          var mainBtn = {
            text: ' ',
            title: 'Accordion',
            hasIcon: false,
            class: 'fa fa-th-list ' + t.o.prefix + 'open-dropdown',
            fn: () => {
              buildButtonIcon()
              try {
                const documentSelection = t.doc.getSelection()
                const node = documentSelection.focusNode;
                const btnName = 'accordion';
                var dropdownPrefix = t.o.prefix + 'dropdown',
                  dropdownOptions = { // the dropdown
                    class: dropdownPrefix + '-' + btnName + ' ' + dropdownPrefix + ' ' + t.o.prefix + 'fixed-top'
                  };
                dropdownOptions['data-' + dropdownPrefix] = btnName;
                var $dropdown = $('<div/>', dropdownOptions);

                if (t.$box.find('.' + dropdownPrefix + '-' + btnName).length === 0) {

                  t.$box.append($dropdown.hide());
                } else {

                  $dropdown = t.$box.find('.' + dropdownPrefix + '-' + btnName);
                }

                // clear dropdown
                $dropdown.html('');

                t.saveRange();

                const btnGroup = {
                  'panel': [],
                  'accordion': []
                }

                if (t.$box.find('.' + t.o.prefix + 'accordion-button').hasClass(t.o.prefix + 'active-button')) {

                  btnGroup.panel.push('addPanelToFirst');
                  btnGroup.panel.push('addPanelToEnd');
                  btnGroup.panel.push('addPanelToPrev');
                  btnGroup.panel.push('addPanelToNext');
                  btnGroup.panel.push('movePanelUp');
                  btnGroup.panel.push('movePanelDown');
                  btnGroup.panel.push('destroyPanel');

                  btnGroup.accordion.push('destroyAccordion')

                  const tag = $(node).closest('.accordion-item .card');
                  const accordion = $(node).closest('.accordion-item')

                  const movePanelDownIndex = btnGroup.panel.indexOf('movePanelDown');
                  const movePanelUpIndex = btnGroup.panel.indexOf('movePanelUp');
                  const addPanelToPrevIndex = btnGroup.panel.indexOf('addPanelToPrev');
                  const addPanelToNextIndex = btnGroup.panel.indexOf('addPanelToNext');
                  const destroyPanelIndex = btnGroup.panel.indexOf('destroyPanel');

                  if ($(accordion).children().length == 1) {
                    console.log('children', btnGroup.panel)

                    btnGroup.panel.splice(destroyPanelIndex, 1)
                    btnGroup.panel.splice(movePanelDownIndex, 1)
                    btnGroup.panel.splice(movePanelUpIndex, 1)
                    btnGroup.panel.splice(addPanelToNextIndex, 1)
                    btnGroup.panel.splice(addPanelToPrevIndex, 1)

                    console.log('btnGroup.panel', btnGroup.panel)
                  }

                  else {
                    if (tag.index() === 0) {
                      btnGroup.panel.splice(movePanelUpIndex, 1)
                      btnGroup.panel.splice(addPanelToPrevIndex, 1)
                    }
                    else if (tag.index() === $(accordion).children().length - 1) {
                      btnGroup.panel.splice(movePanelDownIndex, 1)
                      btnGroup.panel.splice(addPanelToNextIndex, 1)
                    }
                  }

                } else {
                  btnGroup.accordion.push('create')
                }

                for (var key in btnGroup) {
                  if (btnGroup[key].length === 0) {
                    continue;
                  }
                  if (key === 'panel') {
                    $dropdown.append($('<div/>', {
                      html: "Panel Controls",
                      class: t.o.prefix + 'table-dropdown-title'
                    })).text();
                  }

                  if (key === 'accordion') {
                    $dropdown.append($('<div/>', {
                      html: "Accordion Controls",
                      class: t.o.prefix + 'table-dropdown-title'
                    })).text();
                  }

                  var $buttonGroup = $('<div/>', {
                    class: t.o.prefix + 'dropdown-button-group'
                  });

                  btnGroup[key].forEach(function (def) {
                    $buttonGroup.append(t.buildSubBtn(def));
                  })

                  $dropdown.append($buttonGroup);
                }

                t.dropdown(btnName);
              }
              catch (err) {
                console.log(err)
              }

            }

          }

          var addPanelToFirst = {
            title: "Add Panel to Top",
            text: "Insert Panel To Top",

            hasIcon: true,
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode;
              const panelId = getPanelId()

              if (node && node.nodeName === '#text') {
                try {
                  const tag = $(node).closest('.accordion-item')
                  tag.prepend(`<div class="card">
                  <div class="card-header accordion-header">
                      <a class="card-link" data-toggle="collapse" href="#collapse-${panelId}" aria-expanded="true">Add Panel Title</a>
                  </div>
                  <div id="collapse-${panelId}" class="collapse show" data-parent="#accordion-${tag.attr('id')}">
                      <div class="card-body"><p>Add Panel Body</p></div>
                  </div>`)
                  console.log(tag)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }

          var addPanelToEnd = {
            title: "Add Panel to Bottom",
            text: "Insert Panel To Bottom",

            hasIcon: true,
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode;
              const panelId = getPanelId()

              if (node && node.nodeName === '#text') {
                try {
                  const tag = $(node).closest('.accordion-item')
                  tag.append(`<div class="card">
                  <div class="card-header accordion-header">
                      <a class="card-link" data-toggle="collapse" href="#collapse-${panelId}" aria-expanded="true">Add Panel Title</a>
                  </div>
                  <div id="collapse-${panelId}" class="collapse show" data-parent="#accordion-${tag.attr('id')}">
                      <div class="card-body"><p>Add Panel Body</p></div>
                  </div>`)
                  console.log(tag)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }

          var addPanelToPrev = {
            title: "Add Panel Above",
            text: "Insert Panel Above",
           
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode;
              const panelId = getPanelId()

              if (node && node.nodeName === '#text') {
                try {
                  const tag = $(node).closest('.accordion-item')
                  const parentNode = $(node).closest('.card')
                  const newPanel = $(`<div class="card">
                  <div class="card-header accordion-header">
                      <a class="card-link" data-toggle="collapse" href="#collapse-${panelId}" aria-expanded="true">Add Panel Title</a>
                  </div>
                  <div id="collapse-${panelId}" class="collapse show" data-parent="#accordion-${tag.attr('id')}">
                      <div class="card-body"><p>Add Panel Body</p></div>
                  </div>`)
                  $(newPanel).insertBefore($(parentNode))
                  console.log(tag)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }

          var addPanelToNext = {
            title: "Add Panel After",
            text: "Insert Panel After",
            
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode;
              const panelId = getPanelId()

              if (node && node.nodeName === '#text') {
                try {
                  const tag = $(node).closest('.accordion-item')
                  const parentNode = $(node).closest('.card')
                  const newPanel = $(`<div class="card">
                  <div class="card-header accordion-header">
                      <a class="card-link" data-toggle="collapse" href="#collapse-${panelId}" aria-expanded="true">Add Panel Title</a>
                  </div>
                  <div id="collapse-${panelId}" class="collapse show" data-parent="#accordion-${tag.attr('id')}">
                      <div class="card-body"><p>Add Panel Body</p></div>
                  </div>`)
                  $(newPanel).insertAfter($(parentNode))
                  console.log(tag)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }

          var movePanelUp = {
            title: "Move Panel Up",
            text: "Move Panel Up",
            
            hasIcon: true,
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode;
              const panelId = getPanelId()

              if (node && node.nodeName === '#text') {
                try {
                  console.log('here')
                  const parentNode = $(node).closest('.card')
                  const prevPanel = $(parentNode).prev()
                  console.log('parent', parentNode.html());
                  console.log('prev', prevPanel.html());
                  $(parentNode).insertBefore(prevPanel)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }

          var movePanelDown = {
            title: "Move Panel Down",
            text: "Move Panel Down",
            hasIcon: true,
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode;
              const panelId = getPanelId()

              if (node && node.nodeName === '#text') {
                try {
                  console.log('here')
                  const parentNode = $(node).closest('.card')
                  const prevPanel = $(parentNode).next()
                  console.log('parent', parentNode.html());
                  console.log('prev', prevPanel.html());
                  $(parentNode).insertAfter(prevPanel)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }


          var destroyPanel = {
            title: "Remove Panel",
            text: "Remove Panel",
         
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode,
                text,
                color = trumbowyg.o.plugins.custom_alert.defaultType;


              if (node && node.nodeName === '#text') {
                try {
                  const tag = $(node).closest('.card')
                  tag.remove()
                  console.log(tag)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }

          var destroyAccordion = {
            title: "Remove Accordion",
            text: "Remove Accordion",
           
            fn: () => {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode,
                text,
                color = trumbowyg.o.plugins.custom_alert.defaultType;


              if (node && node.nodeName === '#text') {
                try {
                  const tag = $(node).closest('.accordion-item')
                  tag.remove()
                  console.log(tag)
                } catch (err) {
                  console.log(err)
                }
              }

              t.saveRange();
            }
          }


          trumbowyg.addBtnDef('accordion', mainBtn);
          trumbowyg.addBtnDef('create', btnDef);
          trumbowyg.addBtnDef('movePanelUp', movePanelUp);
          trumbowyg.addBtnDef('movePanelDown', movePanelDown);
          trumbowyg.addBtnDef('addPanelToFirst', addPanelToFirst);
          trumbowyg.addBtnDef('addPanelToEnd', addPanelToEnd);
          trumbowyg.addBtnDef('addPanelToPrev', addPanelToPrev);
          trumbowyg.addBtnDef('addPanelToNext', addPanelToNext);
          trumbowyg.addBtnDef('destroyPanel', destroyPanel);
          trumbowyg.addBtnDef('destroyAccordion', destroyAccordion);

        },
        tagHandler: function (element, trumbowyg) {

          const tags = []
          const parent = $(element)[0].parentNode;
          if ($(parent).hasClass('accordion-header') || $(parent).hasClass('card-body')) {
            tags.push('accordion')
            tags.push('create')
          }

          return tags;
        },
        destroy: function (trumbowyg) {
        }
      }
    }
  });


  // Pre-formatted build and management modal
  const openModalInsert = (t, title, fields, cmd) => {
    var prefix = t.o.prefix,
      lg = t.lang,
      html = '',
      idPrefix = prefix + 'form-' + Date.now() + '-';

    $.each(fields, function (fieldName, field) {
      var l = field.label || fieldName,
        n = field.name || fieldName,
        a = field.attributes || {},
        fieldId = idPrefix + fieldName;

      var attr = Object.keys(a).map(function (prop) {
        return prop + '="' + a[prop] + '"';
      }).join(' ');

      if (typeof field.type === 'function') {
        if (!field.name) {
          field.name = n;
        }

        html += field.type(field, fieldId, prefix, lg);

        return;
      }

      html += '<div class="' + prefix + 'input-row">';
      html += '<div class="' + prefix + 'input-infos"><label for="' + fieldId + '"><span>' + (lg[l] ? lg[l] : l) + '</span></label></div>';
      html += '<div class="' + prefix + 'input-html">';

      if ($.isPlainObject(field.options)) {
        html += '<select name="' + n + '">';
        html += Object.keys(field.options).map((optionValue) => {
          return '<option value="' + optionValue + '" ' + (optionValue === field.value ? 'selected' : '') + '>' + field.options[optionValue] + '</option>';
        }).join('');
        html += '</select>';
      } else {
        html += '<input id="' + fieldId + '" type="' + (field.type || 'text') + '" name="' + n + '" ' + attr;
        html += (field.type === 'checkbox' && field.value ? ' checked="checked"' : '') + ' value="' + (field.value || '').replace(/"/g, '&quot;') + '">';
      }

      html += '</div></div>';
    });

    return t.openModal(title, html)
      .on(CONFIRM_EVENT, function () {
        var $form = $('form', $(this)),
          valid = true,
          values = {};
        console.log('confirm', $form)
        $.each(fields, function (fieldName, field) {
          var n = field.name || fieldName;

          var $field = $(':input[name="' + n + '"], select[name="' + n + '"]', $form),
            inputType = field.type;

          switch (inputType.toLowerCase()) {
            case 'checkbox':
              values[n] = $field.is(':checked');
              break;
            case 'radio':
              values[n] = $field.filter(':checked').val();
              break;
            default:
              values[n] = $.trim($field.val());
              break;
          }
          // Validate value
          if (field.required && values[n] === '') {
            valid = false;
            t.addErrorOnModalField($field, t.lang.required);
          } else if (field.pattern && !field.pattern.test(values[n])) {
            valid = false;
            t.addErrorOnModalField($field, field.patternError);
          }
        });
        console.log(values)
        console.log(valid)
        if (valid) {
          t.restoreRange();

          if (cmd(values, fields)) {
            t.syncCode();
            t.$c.trigger('tbwchange');
            t.closeModal();
            $(this).off(CONFIRM_EVENT);
          }
        }
      })
      .one(CANCEL_EVENT, function () {
        $(this).off(CONFIRM_EVENT);
        t.closeModal();
      });
  }

})(jQuery);