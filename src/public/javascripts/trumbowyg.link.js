
(function ($) {
  'use strict';

  var defaultOptions = {
    defaultType: 'info',
  };

  var CONFIRM_EVENT = 'tbwconfirm',
    CANCEL_EVENT = 'tbwcancel';

  $.extend(true, $.trumbowyg, {
    langs: {
      // jshint camelcase:false
      en: {
        _self: 'Same tab (default)',
        _blank: 'New tab',
      }
    },
    // jshint camelcase:true

    plugins: {
      link: {
        init: function (t) {
          t.o.plugins.custom_alert = $.extend(true, {}, defaultOptions, t.o.plugins.custom_alert || {});

          var link = {
            ico: 'link',
            dropdown: ['createUBXLink', 'unlink']
          }

          const createLink = {
            text: 'Insert Link',
            title: 'Insert Link',
            ico: 'createLink',
            fn: () => {
              var documentSelection = t.doc.getSelection(),
                selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode,
                text = new XMLSerializer().serializeToString(selectedRange.cloneContents()) || selectedRange + '',
                url,
                title,
                target,
                linkDefaultTarget = t.o.linkTargets[0];

              while (['A', 'DIV'].indexOf(node.nodeName) < 0) {
                node = node.parentNode;
              }
              console.log('node', node)
              if (node && node.nodeName === 'A') {
                var $a = $(node);
                text = $a.text();
                url = $a.attr('href');
                if (!t.o.minimalLinks) {
                  title = $a.attr('title');
                  target = $a.attr('target') || linkDefaultTarget;
                }
                var range = t.doc.createRange();
                range.selectNode(node);
                documentSelection.removeAllRanges();
                documentSelection.addRange(range);
              }

              t.saveRange();

              var options = {}



              if ($a && $a.hasClass('card-link')) {
                options = {
                  url: {
                    type: 'hidden',
                    value: url
                  },
                  text: {
                    label: 'Panel Title',
                    value: text.trim()
                  }
                }

              }
              else {
                options = {
                  url: {
                    label: t.lang.linkUrl || 'URL',
                    required: true,
                    value: url
                  },
                  text: {
                    label: t.lang.text,
                    value: text
                  }
                };
                if (!t.o.minimalLinks) {
                  var targetOptions = t.o.linkTargets.reduce(function (options, optionValue) {
                    options[optionValue] = t.lang[optionValue];

                    return options;
                  }, {});

                  $.extend(options, {
                    title: {
                      label: t.lang.title,
                      value: title
                    },
                    target: {
                      label: t.lang.target,
                      value: target,
                      options: targetOptions
                    }
                  });
                }
              }
              
              openModalInsert(t, (options.url.type === 'hidden') ? 'Modify Panel Title' : t.lang.createLink, options, function (v) { // v is value
                var url = prependUrlPrefix(v.url);
                if (!url.length) {
                  return false;
                }
                if (options.url.type === 'hidden') { //assuming this is a link for accordion header
                  var link = $(['<a href="', url, '" class="card-link" data-toggle="collapse" aria-expanded="true">', v.text || v.url, '</a>'].join(''));
                  console.log(link)
                } else {
                  var link = $(['<a href="', url, '">', v.text || v.url, '</a>'].join(''));
                  if (v.target || linkDefaultTarget) {
                    link.attr('target', v.target || linkDefaultTarget);
                  }
                }


                if (v.title) {
                  link.attr('title', v.title);
                }

                t.range.deleteContents();
                t.range.insertNode(link[0]);
                t.syncCode();
                t.$c.trigger('tbwchange');
                return true;
              });
            }
          }
          const prependUrlPrefix = (url) => {
            var t = this;
            if (!t.urlPrefix) {
              return url;
            }

            var VALID_LINK_PREFIX = /^([a-z][-+.a-z0-9]*:|\/|#)/i;
            if (VALID_LINK_PREFIX.test(url)) {
              return url;
            }

            var SIMPLE_EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (SIMPLE_EMAIL_REGEX.test(url)) {
              return 'mailto:' + url;
            }

            return t.urlPrefix + url;
          };

          //buildButtonIcon();
          t.addBtnDef('createUBXLink', createLink);
          // t.addBtnDef('unlink', unlink);
          t.addBtnDef('ubxlink', link);
        },
        tagHandler: function (element, t) {

          // if ($(element).hasClass('card-link')) {
          //   console.log('asdfasdf', element)
          //   return []
          // }

          // else if ($(element).prop("tagName") === 'A') {
          //   return ['ubxlink']
          // }

          if ($(element).prop("tagName") === 'A') {
            console.log('here')
            return ['ubxlink', 'createUBXLink']
          }
          else {
            return []
          }

        },
        destroy: function (t) {
        }
      }
    }
  });

  const openModalInsert = (t, title, fields, cmd) => {
    var prefix = t.o.prefix,
      lg = t.lang,
      html = '',
      idPrefix = prefix + 'form-' + Date.now() + '-';

    $.each(fields, function (fieldName, field) {
      var l = field.label || fieldName,
        n = field.name || fieldName,
        a = field.attributes || {},
        fieldId = idPrefix + fieldName;

      var attr = Object.keys(a).map(function (prop) {
        return prop + '="' + a[prop] + '"';
      }).join(' ');

      if (typeof field.type === 'function') {
        if (!field.name) {
          field.name = n;
        }

        html += field.type(field, fieldId, prefix, lg);

        return;
      }

      if (field.type !== 'hidden') {
        html += '<div class="' + prefix + 'input-row">';
        html += '<div class="' + prefix + 'input-infos"><label for="' + fieldId + '"><span>' + (lg[l] ? lg[l] : l) + '</span></label></div>';
        html += '<div class="' + prefix + 'input-html">';
      }

      if ($.isPlainObject(field.options)) {
        html += '<select name="' + n + '">';
        html += Object.keys(field.options).map((optionValue) => {
          return '<option value="' + optionValue + '" ' + (optionValue === field.value ? 'selected' : '') + '>' + field.options[optionValue] + '</option>';
        }).join('');
        html += '</select>';
      } else if (field.type === 'number') {
        html += '<input id="' + fieldId + '" type="' + (field.type || 'text') + '" name="' + n + '" ' + attr + ' value="' + (field.value || '').replace(/"/g, '&quot;') + '">';
      }

      else {
        html += '<input id="' + fieldId + '" type="' + (field.type || 'text') + '" name="' + n + '" ' + attr;
        html += (field.type === 'checkbox' && field.value ? ' checked="checked"' : '') + ' value="' + (field.value || '').replace(/"/g, '&quot;') + '">';
      }

      if (field.type !== 'hidden') {
        html += '</div></div>';
      }
    });

    return t.openModal(title, html)
      .on(CONFIRM_EVENT, function () {
        var $form = $('form', $(this)),
          valid = true,
          values = {};

        $.each(fields, function (fieldName, field) {
          var n = field.name || fieldName;

          var $field = $(':input[name="' + n + '"], select[name="' + n + '"]', $form),
            inputType = field.type || 'text';
          switch (inputType.toLowerCase()) {
            case 'checkbox':
              values[n] = $field.is(':checked');
              break;
            case 'radio':
              values[n] = $field.filter(':checked').val();
              break;
            default:
              values[n] = $.trim($field.val());
              break;
          }
          // Validate value
          if (field.required && values[n] === '') {
            valid = false;
            t.addErrorOnModalField($field, t.lang.required);
          } else if (field.pattern && !field.pattern.test(values[n])) {
            valid = false;
            t.addErrorOnModalField($field, field.patternError);
          }
        });

        if (valid) {
          t.restoreRange();

          if (cmd(values, fields)) {
            t.syncCode();
            t.$c.trigger('tbwchange');
            t.closeModal();
            $(this).off(CONFIRM_EVENT);
          }
        }
      })
      .one(CANCEL_EVENT, function () {
        $(this).off(CONFIRM_EVENT);
        t.closeModal();
      });
  }

})(jQuery);