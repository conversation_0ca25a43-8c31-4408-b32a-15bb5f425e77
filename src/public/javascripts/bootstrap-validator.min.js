+function(t){"use strict";function e(e){return this.each(function(){var r=t(this),o=t.extend({},a.DEFAULTS,r.data(),"object"==typeof e&&e),i=r.data("bs.validator");(i||"destroy"!=e)&&(i||r.data("bs.validator",i=new a(this,o)),"string"==typeof e&&i[e]())})}var a=function(e,r){this.$element=t(e),this.options=r,r.errors=t.extend({},a.DEFAULTS.errors,r.errors);for(var o in r.custom)if(!r.errors[o])throw new Error("Missing default error message for custom validator: "+o);t.extend(a.VALIDATORS,r.custom),this.$element.attr("novalidate",!0),this.toggleSubmit(),this.$element.on("input.bs.validator change.bs.validator focusout.bs.validator",t.proxy(this.validateInput,this)),this.$element.on("submit.bs.validator",t.proxy(this.onSubmit,this)),this.$element.find("[data-match]").each(function(){var e=t(this),a=e.data("match");t(a).on("input.bs.validator",function(t){e.val()&&e.trigger("input.bs.validator")})})};a.INPUT_SELECTOR=':input:not([type="submit"], button):enabled:visible',a.DEFAULTS={delay:500,html:!1,disable:!1,custom:{},errors:{match:"Passwords do not match",minlength:"Not long enough"},feedback:{success:"glyphicon-ok",error:"glyphicon-remove"}},a.VALIDATORS={native:function(t){var e=t[0];return!e.checkValidity||e.checkValidity()},match:function(e){var a=e.data("match");return!e.val()||e.val()===t(a).val()},minlength:function(t){var e=t.data("minlength");return!t.val()||t.val().length>=e}},a.prototype.validateInput=function(e){var a=t(e.target),r=a.data("bs.validator.errors");if(a.is('[type="radio"]')&&(a=this.$element.find('input[name="'+a.attr("name")+'"]')),this.$element.trigger(e=t.Event("validate.bs.validator",{relatedTarget:a[0]})),!e.isDefaultPrevented()){var o=this;this.runValidators(a).done(function(i){a.data("bs.validator.errors",i),i.length?o.showErrors(a):o.clearErrors(a),r&&i.toString()===r.toString()||(e=i.length?t.Event("invalid.bs.validator",{relatedTarget:a[0],detail:i}):t.Event("valid.bs.validator",{relatedTarget:a[0],detail:r}),o.$element.trigger(e)),o.toggleSubmit(),o.$element.trigger(t.Event("validated.bs.validator",{relatedTarget:a[0]}))})}},a.prototype.runValidators=function(e){function r(t){return e.data(t+"-error")||e.data("error")||"native"==t&&e[0].validationMessage||n.errors[t]}var o=[],i=t.Deferred(),n=this.options;return e.data("bs.validator.deferred")&&e.data("bs.validator.deferred").reject(),e.data("bs.validator.deferred",i),t.each(a.VALIDATORS,t.proxy(function(t,a){if((e.data(t)||"native"==t)&&!a.call(this,e)){var i=r(t);!~o.indexOf(i)&&o.push(i)}},this)),!o.length&&e.val()&&e.data("remote")?this.defer(e,function(){var a={};a[e.attr("name")]=e.val(),t.get(e.data("remote"),a).fail(function(t,e,a){o.push(r("remote")||a)}).always(function(){i.resolve(o)})}):i.resolve(o),i.promise()},a.prototype.validate=function(){var t=this.options.delay;return this.options.delay=0,this.$element.find(a.INPUT_SELECTOR).trigger("input.bs.validator"),this.options.delay=t,this},a.prototype.showErrors=function(e){this.options.html;this.defer(e,function(){var a=e.closest(".form-group"),r=a.find(".help-block.with-errors"),o=a.find(".form-control-feedback"),i=e.data("bs.validator.errors");i.length&&(t.map(i,function(t){a.attr("title","Validation error"),a.attr("data-placement","bottom"),a.attr("data-content",t),a.popover("show")}),void 0===r.data("bs.validator.originalContent")&&r.data("bs.validator.originalContent",r.html()),a.addClass("has-error"),o.length&&o.removeClass(this.options.feedback.success)&&o.addClass(this.options.feedback.error)&&a.removeClass("has-success"))})},a.prototype.clearErrors=function(t){var e=t.closest(".form-group"),a=e.find(".help-block.with-errors"),r=e.find(".form-control-feedback");a.html(a.data("bs.validator.originalContent")),e.removeClass("has-error"),e.popover("hide"),e.removeAttr("title"),e.removeAttr("data-placement"),e.removeAttr("data-content"),r.length&&r.removeClass(this.options.feedback.error)&&r.addClass(this.options.feedback.success)&&e.addClass("has-success")},a.prototype.hasErrors=function(){return!!this.$element.find(a.INPUT_SELECTOR).filter(function(){return!!(t(this).data("bs.validator.errors")||[]).length}).length},a.prototype.isIncomplete=function(){return!!this.$element.find(a.INPUT_SELECTOR).filter("[required]").filter(function(){return"checkbox"===this.type?!this.checked:"radio"===this.type?!t('[name="'+this.name+'"]:checked').length:""===t.trim(this.value)}).length},a.prototype.onSubmit=function(t){this.validate(),(this.isIncomplete()||this.hasErrors())&&t.preventDefault()},a.prototype.toggleSubmit=function(){this.options.disable&&t('button[type="submit"], input[type="submit"]').filter('[form="'+this.$element.attr("id")+'"]').add(this.$element.find('input[type="submit"], button[type="submit"]')).toggleClass("disabled",this.isIncomplete()||this.hasErrors())},a.prototype.defer=function(e,a){if(a=t.proxy(a,this),!this.options.delay)return a();window.clearTimeout(e.data("bs.validator.timeout")),e.data("bs.validator.timeout",window.setTimeout(a,this.options.delay))},a.prototype.destroy=function(){return this.$element.removeAttr("novalidate").removeData("bs.validator").off(".bs.validator"),this.$element.find(a.INPUT_SELECTOR).off(".bs.validator").removeData(["bs.validator.errors","bs.validator.deferred"]).each(function(){var e=t(this),a=e.data("bs.validator.timeout");window.clearTimeout(a)&&e.removeData("bs.validator.timeout")}),this.$element.find(".help-block.with-errors").each(function(){var e=t(this),a=e.data("bs.validator.originalContent");e.removeData("bs.validator.originalContent").html(a)}),this.$element.find('input[type="submit"], button[type="submit"]').removeClass("disabled"),this.$element.find(".has-error").removeClass("has-error"),this};var r=t.fn.validator;t.fn.validator=e,t.fn.validator.Constructor=a,t.fn.validator.noConflict=function(){return t.fn.validator=r,this},t(window).on("load",function(){t('form[data-toggle="validator"]').each(function(){var a=t(this);e.call(a,a.data())})})}(jQuery);