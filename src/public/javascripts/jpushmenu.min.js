!function(s){s.fn.jPushMenu=function(t){var e=s.extend({},s.fn.jPushMenu.defaultOptions,t);s(".toggle-menu").length&&(s("body").addClass(e.bodyClass),s(this).addClass("jPushMenuBtn")),s(this).click(function(){var t="",n="";return s(this).is("."+e.showLeftClass)?(t=".cbp-spmenu-left",n="toright"):s(this).is("."+e.showRightClass)?(t=".cbp-spmenu-right",n="toleft"):s(this).is("."+e.showTopClass)?t=".cbp-spmenu-top":s(this).is("."+e.showBottomClass)&&(t=".cbp-spmenu-bottom"),s(this).toggleClass(e.activeClass),s(t).toggleClass(e.menuOpenClass),s(this).is("."+e.pushBodyClass)&&s("body").toggleClass("cbp-spmenu-push-"+n),!1});var n={close:function(t){s(".jPushMenuBtn,body,.cbp-spmenu").removeClass("active cbp-spmenu-open cbp-spmenu-push-toleft cbp-spmenu-push-toright")}};e.closeOnClickOutside&&(s(document).click(function(){n.close()}),s(".cbp-spmenu,.toggle-menu").click(function(s){s.stopPropagation()}))},s.fn.jPushMenu.defaultOptions={bodyClass:"cbp-spmenu-push",activeClass:"menu-active",showLeftClass:"menu-left",showRightClass:"menu-right",showTopClass:"menu-top",showBottomClass:"menu-bottom",menuOpenClass:"cbp-spmenu-open",pushBodyClass:"push-body",closeOnClickOutside:!0}}(jQuery);