

(function ($) {
  'use strict';

  var defaultOptions = {
    align: 'center',
    height: '200',
    content: 'Add content here',
  };


  var CONFIRM_EVENT = 'tbwconfirm',
  CANCEL_EVENT = 'tbwcancel';


  $.extend(true, $.trumbowyg, {
    langs: {
      // jshint camelcase:false
      en: {
        accordion: 'Accordion',
      
      },
      az: {
        upload: 'Yüklə',
        file: 'Fayl',
        uploadError: 'Xəta'
      },
      by: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Памылка'
      },
      ca: {
        upload: 'Pujar fitxer',
        file: 'Fitxer',
        uploadError: 'Error'
      },
      cs: {
        upload: 'Nahrát obrázek',
        file: 'Soubor',
        uploadError: 'Chyba'
      },
      da: {
        upload: 'Upload',
        file: 'Fil',
        uploadError: 'Fejl'
      },
      de: {
        upload: 'Hochladen',
        file: 'Datei',
        uploadError: 'Fehler'
      },
      es: {
        upload: 'Subir archivo',
        file: 'Archivo',
        uploadError: 'Error'
      },
      et: {
        upload: 'Lae üles',
        file: 'Fail',
        uploadError: 'Viga'
      },
      fr: {
        upload: 'Envoi',
        file: 'Fichier',
        uploadError: 'Erreur'
      },
      hu: {
        upload: 'Feltöltés',
        file: 'Fájl',
        uploadError: 'Hiba'
      },
      ja: {
        upload: 'アップロード',
        file: 'ファイル',
        uploadError: 'エラー'
      },
      ko: {
        upload: '그림 올리기',
        file: '파일',
        uploadError: '에러'
      },
      pt_br: {
        upload: 'Enviar do local',
        file: 'Arquivo',
        uploadError: 'Erro'
      },
      ru: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Ошибка'
      },
      sl: {
        upload: 'Naloži datoteko',
        file: 'Datoteka',
        uploadError: 'Napaka'
      },
      sk: {
        upload: 'Nahrať',
        file: 'Súbor',
        uploadError: 'Chyba'
      },
      tr: {
        upload: 'Yükle',
        file: 'Dosya',
        uploadError: 'Hata'
      },
      zh_cn: {
        upload: '上传',
        file: '文件',
        uploadError: '错误'
      },
      zh_tw: {
        upload: '上傳',
        file: '文件',
        uploadError: '錯誤'
      },
    },
    // jshint camelcase:true

    plugins: {
      container: {
        init: function (trumbowyg) {
          trumbowyg.o.plugins.custom_container = $.extend(true, {}, defaultOptions, trumbowyg.o.plugins.custom_container || {});
          var btnDef = {
            fn: function () {
              var documentSelection = trumbowyg.doc.getSelection(),
                selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode,
                alignment = trumbowyg.o.plugins.custom_container.align,
                height = trumbowyg.o.plugins.custom_container.height,
                content = trumbowyg.o.plugins.custom_container.content;

              
                // console.log('node', node)
                // console.log('node', node.nodeName)
                // console.log('documentSelection', documentSelection)
                // console.log('selectedRange', selectedRange)

              if (node && node.nodeName === '#text') {
                console.log('asdf')
              
                  const wrap = node.parentNode.parentNode;
                  const tag = node.parentNode.parentNode.parentNode;
                  const container = node.parentNode.parentNode.parentNode.parentNode;

                 
                  alignment = $(tag).attr('class').match(/text-(\w+)/)[1];
                  height = $(wrap).attr('style').match(/height:(\w+)px/)[1];
                  content = $(wrap).text();
  
                  // console.log('alignment', alignment)
                  // console.log('height', height)
                  // console.log('content', content)
  
                  var range = trumbowyg.doc.createRange();
                  range.selectNode(container);
                  documentSelection.removeAllRanges();
                  documentSelection.addRange(range);
                
              }
              trumbowyg.saveRange();
              
              var fields = {
             
                align: {
                  type: 'select',
                  label: 'alignment',
                  value: alignment,
                  required: true,
                  options: {
                    left: 'Left',
                    right: 'Right',
                    center: 'Center',
                  }
                },
                
                height: {
                  type: 'number', // input type
                  label: 'Height (px)',
                  value: height,
                  required: true
                  
                },

                content: {
                  type: 'text',
                  label: 'Content',
                  required: true,
                  value: content,
                }
              };

              openModalInsert(
                trumbowyg,
                // Title
                "Container",

                // Fields
                fields,

                // Callback
                function (values) {
                 
                  try{
                    var html = $(`<div class="container"><div class="row text-${values.align}"><div style="height:${values.height}px; width:100%;"><p>${values.content}</p></div></div></div><p><br /></p>`)
                
                    trumbowyg.range.deleteContents();
                    trumbowyg.range.insertNode(html[0]);
                    trumbowyg.syncCode();
                    trumbowyg.$c.trigger('tbwchange');

                    return true;
                  //trumbowyg.$c.trigger('tbwinsertalert', [trumbowyg, html]);
                  } catch (err) {
                    console.log(err);
                  }
                }
              );

              
            },
            text: ' ',
            title: 'Container',
            hasIcon: false,
            class: 'fa fa-archive'
          };

          trumbowyg.addBtnDef('container', btnDef);
        },
        tagHandler: function (element, trumbowyg) {
          const tags = []
          const container = $(element).closest('.container')
          if(container.length) {
            tags.push('container')
          }

          return tags
          
        },
        destroy: function (trumbowyg) {
        }

      }
    }
  });

  const openModalInsert = (t, title, fields, cmd) => {
    var prefix = t.o.prefix,
        lg = t.lang,
        html = '',
        idPrefix = prefix + 'form-' + Date.now() + '-';

    $.each(fields, function (fieldName, field) {
        var l = field.label || fieldName,
            n = field.name || fieldName,
            a = field.attributes || {},
            fieldId = idPrefix + fieldName;

        var attr = Object.keys(a).map(function (prop) {
            return prop + '="' + a[prop] + '"';
        }).join(' ');

        if (typeof field.type === 'function') {
          if (!field.name) {
            field.name = n;
          }

          html += field.type(field, fieldId, prefix, lg);

          return;
        }

        html += '<div class="' + prefix + 'input-row">';
        html += '<div class="' + prefix + 'input-infos"><label for="' + fieldId + '"><span>' + (lg[l] ? lg[l] : l) + '</span></label></div>';
        html += '<div class="' + prefix + 'input-html">';

        if ($.isPlainObject(field.options)) {
            html += '<select name="'+n+'">';
            html += Object.keys(field.options).map((optionValue) => {
                return '<option value="' + optionValue + '" ' + (optionValue === field.value ? 'selected' : '') + '>' + field.options[optionValue] + '</option>';
            }).join('');
            html += '</select>';
        } else if (field.type === 'number') {
            html += '<input id="' + fieldId + '" type="' + (field.type || 'text') + '" name="' + n + '" ' + attr + ' value="' + (field.value || '').replace(/"/g, '&quot;') + '">';
        } else {
            html += '<input id="' + fieldId + '" type="' + (field.type || 'text') + '" name="' + n + '" ' + attr;
            html += (field.type === 'checkbox' && field.value ? ' checked="checked"' : '') + ' value="' + (field.value || '').replace(/"/g, '&quot;') + '">';
        }

        html += '</div></div>';
    });

    return t.openModal(title, html)
        .on(CONFIRM_EVENT, function () {
            var $form = $('form', $(this)),
                valid = true,
                values = {};
            console.log('confirm', $form)
            $.each(fields, function (fieldName, field) {
                var n = field.name || fieldName;

                var $field = $(':input[name="' + n + '"], select[name="'+n+'"]', $form),
                    inputType = field.type;
                   
                switch (inputType.toLowerCase()) {
                    case 'checkbox':
                        values[n] = $field.is(':checked');
                        break;
                    case 'radio':
                        values[n] = $field.filter(':checked').val();
                        break;
                    default:
                        values[n] = $.trim($field.val());
                        break;
                }
                // Validate value
                if (field.required && values[n] === '') {
                    valid = false;
                    t.addErrorOnModalField($field, t.lang.required);
                } else if (field.pattern && !field.pattern.test(values[n])) {
                    valid = false;
                    t.addErrorOnModalField($field, field.patternError);
                }
            });
           
            if (valid) {
                t.restoreRange();

                if (cmd(values, fields)) {
                    t.syncCode();
                    t.$c.trigger('tbwchange');
                    t.closeModal();
                    $(this).off(CONFIRM_EVENT);
                }
            }
        })
        .one(CANCEL_EVENT, function () {
            $(this).off(CONFIRM_EVENT);
            t.closeModal();
        });
}

})(jQuery);