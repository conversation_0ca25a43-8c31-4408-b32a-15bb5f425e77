const appSwal = Swal.mixin({
  showCloseButton: true,
  showCancelButton: true,
  cancelButtonText: t("Cancel"),
  customClass: { popup: "swal2-border-radius" },
  didOpen: function () {
    window.top.postMessage("modalShow", "*");
  },
  didClose: function () {
    window.top.postMessage("modalHide", "*");
  },
  backdrop: `rgba(0,0,0,0.5)`,
  showClass: {
    backdrop: "swal2-noanimation", // disable backdrop animation
    popup: "", // disable popup animation
    icon: "", // disable icon animation
  },
  hideClass: {
    popup: "", // disable popup fade-out animation
  },
  confirmButtonText: t("Save Changes"),
  confirmButtonAriaLabel: t("Proceed to save"),
});
