//as per https://gist.github.com/jakebresnehan/2288330
//overrites JQuery default :contains non-case-sensitivity
//this will make it case insensitive

//will disable this case sensitivity based on request as of Sept 02 2022
jQuery.expr[':'].contains = function (a, i, m) {
 return jQuery(a).text().toUpperCase()
     .indexOf(m[3].toUpperCase()) >= 0;
};

var markdownit = window.markdownit;

var mark_it_down = markdownit({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    xhtmlOut: true
});


var classy = window.markdownItClassy;
var imsize = window['markdown-it-imsize.js'];

mark_it_down.use(classy);
mark_it_down.use(imsize, { autofill: true });

// Function to convert video <img> tags to .file-to-proxy divs for proper video rendering
const convertVideoImgsToFileProxy = (html) => {
    // Match img tags that have video file extensions in their src attribute
    return html.replace(/<img\b[^>]*\bsrc=["']([^"']*\.(?:mp4|mkv|webm|avi|mpeg|mov|mp3)(?:\?[^"']*)?)[^"']*["'][^>]*\/?>/gi, (match, src) => {
        // Extract alt text if present for better user experience
        const altMatch = match.match(/alt=["']([^"']*)["']/);
        const altText = altMatch ? altMatch[1] : '';
        
        return `<div class="file-to-proxy" data-url="${src}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd;">${t('Loading media...')}</div>`;
    });
};

// Make the function globally available
window.convertVideoImgsToFileProxy = convertVideoImgsToFileProxy;

$(document).ready(async function () {
    let previewTimeout = null;
    var simplemde = null;

// Safe helper function to get simplemde value
const getSimplemdeValue = () => {
    try {
        // First try local scope
        if (typeof simplemde !== 'undefined' && simplemde && typeof simplemde.value === 'function') {
            return simplemde.value();
        }
        // Then try window scope
        if (typeof window.simplemde !== 'undefined' && window.simplemde && typeof window.simplemde.value === 'function') {
            return window.simplemde.value();
        }
        // Finally fallback to textarea
        return $('#editor').val() || '';
    } catch (e) {
        console.warn('Error accessing simplemde:', e);
        return $('#editor').val() || '';
    }
};

// Expose getSimplemdeValue to global scope for use by other scripts
window.getSimplemdeValue = getSimplemdeValue;

    await initTranslator();

    const replaceVideoAudioEmbedWithHtml = function(string) {
    
        return string.replace(/!\[(.*?)\]\((.*?)\)/g, (match, alt, src) => {
            const extensions = ["mp4", "mkv", "webm", "avi", "mpeg", "mp3"];
            const extension = src.split(".").pop();
            if (extensions.includes(extension)) {
                
                return `<div class="file-to-proxy" data-url="${src}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd";">${t('Loading media...')}</div>`;
            } else {
                return match;
            }
        });
    }

    // add the responsive image class to all images
    $(".body_text img").each(function () {
        // $(this).addClass("img-fluid");
        // $(this).css('object-fit', 'contain');
        // $(this).addClass("h-100");
    });

    // make all links in articles open in new window/tab
    if (config.links_blank_page === true) {
        $(".body_text a").attr("target", "_blank");
    }

    // setup mermaid charting
    if (typeof mermaid !== "undefined") {
        mermaid.initialize({ startOnLoad: true });
    }

    // add the table class to all tables
    $("table").each(function () {
        $(this).addClass("table table-hover");
    });

    // convert custom markdown text to raw markdown
   
    if($('#is_markdown').val() == "true") {
        if ($(".body_text").length) {
            let processedHtml = convertCustomMarkdown($(".body_text").html());
            // Convert video <img> tags to .file-to-proxy divs for proper video rendering in published articles
            processedHtml = convertVideoImgsToFileProxy(processedHtml);
            $(".body_text").html(processedHtml);
        }
    }

    $('.uploaded-media').each(function () {
        $(this).find('img').on('load', function () {
            $(this).css('opacity', '1');
        });
    });
   
    // When the version dropdown changes
    $(document).on("change", "#kb_versions", function () {
        // get the article from the API
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/api/getArticleJson",
            data: { kb_id: $(this).val() },
        })
            .done(function (article) {
                
                // Update form fields
                $("#frm_kb_title").val(article.kb_title);
                
                // Update keywords field and tokenfield plugin
                const keywordsField = $("#frm_kb_keywords");
                if (keywordsField.data('bs.tokenfield')) {
                    // If tokenfield is initialized, update it properly
                    keywordsField.tokenfield('setTokens', article.kb_keywords || '');
                } else {
                    // Fallback to direct value setting
                    keywordsField.val(article.kb_keywords || '');
                }
                
                // Handle editor content based on version's markdown state
                const versionIsMarkdown = article.kb_is_markdown === true;
                const currentIsMarkdown = $('#is_markdown').val() === 'true';
                
                if (versionIsMarkdown && currentIsMarkdown) {
                    // Both are markdown - direct update
                    if (simplemde) {
                        simplemde.value(article.kb_body);
                    } else {
                        $('#editor').val(article.kb_body);
                    }
                } else if (!versionIsMarkdown && !currentIsMarkdown) {
                    // Both are WYSIWYG - direct update
                    if ($('#editor').data('trumbowyg')) {
                        $('#editor').trumbowyg('html', article.kb_body);
                    } else {
                        $('#editor').val(article.kb_body);
                    }
                } else {
                    // Editor type mismatch - need to switch editor first
                    if (versionIsMarkdown && !currentIsMarkdown) {
                        // Switch to markdown editor
                        $('#editor-switch').prop('checked', true);
                        $('#is_markdown').val('true');
                        changeToMarkdown();
                        // Set content after editor switch
                        setTimeout(() => {
                            if (simplemde) {
                                simplemde.value(article.kb_body);
                            } else {
                                $('#editor').val(article.kb_body);
                            }
                        }, 100);
                    } else {
                        // Switch to WYSIWYG editor
                        $('#editor-switch').prop('checked', false);
                        $('#is_markdown').val('false');
                        changeToWyiswyg();
                        // Set content after editor switch
                        setTimeout(() => {
                            if ($('#editor').data('trumbowyg')) {
                                $('#editor').trumbowyg('html', article.kb_body);
                            } else {
                                $('#editor').val(article.kb_body);
                            }
                        }, 100);
                    }
                }
                
                // Update topics and subtopics dropdowns
                
                // Handle topics and subtopics with proper dependency logic
                if (article.kb_pinned_topic !== undefined && article.kb_pinned_topic !== null) {
                    // Case 1: Topic is specified
                    const topicsSelect = $("#selectTopics");
                    topicsSelect.val(article.kb_pinned_topic);
                    
                    // Trigger change to update subtopics
                    if (window.selectTopicsChange) {
                        try {
                            window.selectTopicsChange();
                        } catch (error) {
                        }
                    }
                    
                    // Set subtopic after topics change
                    setTimeout(() => {
                        if (article.kb_pinned_subtopic !== undefined && article.kb_pinned_subtopic !== null) {
                            $("#selectSubtopics").val(article.kb_pinned_subtopic);
                        }
                    }, 500);
                    
                } else if (article.kb_pinned_subtopic !== undefined && article.kb_pinned_subtopic !== null) {
                    // Case 2: Only subtopic is specified (topic is null) - need to find parent topic
                    $("#selectSubtopics").val(article.kb_pinned_subtopic);
                    
                    // If that didn't work, we need to load all topics and find the right one
                    if ($("#selectSubtopics").val() === null) {
                        $("#selectTopics").val('');
                        $("#selectSubtopics").val('');
                    }
                    
                } else {
                    // Case 3: Neither topic nor subtopic specified
                    $("#selectTopics").val('');
                    $("#selectSubtopics").val('');
                }
                
                // Update regional visibility with proper UI updates
                if (article.kb_regional_visibility !== undefined) {
                    
                    // Update the hidden field
                    $("#frm_kb_regional_visibility").val(article.kb_regional_visibility || '');
                    
                    // Update the visual display text
                    const regionalValue = article.kb_regional_visibility || '';
                    if (regionalValue && regionalValue !== '') {
                        const countries = regionalValue.split(',').map(c => c.trim()).join(', ');
                        $("#optionSelectCountry").text(countries);
                    } else {
                        // Use the appropriate placeholder
                        const is_pseudoadmin = $('#is_pseudoadmin').val() === 'true';
                        const countryDropdownData = $('#selectCountryDropdown').data('options');
                        const pseudoadminCountries = countryDropdownData ? countryDropdownData.split(',').join(', ') : '';
                        const articleId = $('#article_id').val();
                        const selectPlaceholder = is_pseudoadmin && articleId == '' ? pseudoadminCountries : 'Global';
                        $("#optionSelectCountry").text(selectPlaceholder);
                    }
                    
                    // Update country checkboxes to reflect the selection
                    if (window.setAvailableCountries) {
                        window.setAvailableCountries();
                    } else {
                    }
                    
                }
                
                // Update other form elements if they exist
                if (article.kb_published !== undefined) {
                    $("#frm_kb_published").val(article.kb_published.toString());
                }
                if (article.kb_pinned !== undefined) {
                    $("#kb_pinned").prop('checked', article.kb_pinned);
                }
                if (article.kb_featured !== undefined) {
                    $("#frm_kb_featured").prop('checked', article.kb_featured);
                }
                if (article.kb_visible_state !== undefined) {
                    $("#frm_kb_visible_state").val(article.kb_visible_state);
                }
                if (article.kb_security_level !== undefined) {
                    $("#frm_kb_security_level").val(article.kb_security_level);
                }
                if (article.kb_language !== undefined) {
                    $("#frm_kb_language").val(article.kb_language);
                }
                if (article.kb_admin_restricted !== undefined) {
                    $("#frm_kb_admin_restricted").prop('checked', article.kb_admin_restricted);
                }
                
                // Update original data for AI change detection
                if (window.captureCurrentArticleData) {
                    window.originalArticleData = window.captureCurrentArticleData();
                }
                
                $("#btnSettingsMenu").trigger("click");
            })
            .fail(function (msg) {
                show_notification(msg.responseText, "danger");
            });
    });

    // setup the push menu
    if ($(".toggle-menu").length) {
        $(".toggle-menu").jPushMenu({ closeOnClickOutside: false });
    }

    // highlight any code blocks
    $("pre code").each(function (i, block) {
        hljs.highlightBlock(block);
    });

    // add the table class to all tables
    if (config.add_header_anchors === true) {
        $(".body_text > h1, .body_text > h2, .body_text > h3, .body_text > h4, .body_text > h5").each(function () {
            $(this).attr("id", headingSlug($(this).text()));
            //$(this).prepend('<a class="headerAnchor" href="#' + convertToSlug($(this).text()) + '">#</a> ');
        });
    }

    // scroll to hash point
    if (window.location.hash) {
        // if element is found, scroll to it
        if ($(window.location.hash).length) {
            var element = $(window.location.hash);
            $(window).scrollTop(element.offset().top).scrollLeft(element.offset().left);
        }
    }

    // add the token field to the keywords input
    if ($("#frm_kb_keywords").length) {
        $("#frm_kb_keywords").tokenfield();
    }

    const toolbar = [
        "bold",
        "italic",
        "heading",
        "|",
        "quote",
        "unordered-list",
        "ordered-list",
        "|",
        {
            name: 'link',
            action: openLinkDialog,
            className: 'fa fa-link',
            title: t('Insert Link'),
        },
        {
            name: "upload-media",
            action: uploadEditorMediaDialog,
            className: "fa fa-upload",
            title: t("Upload Media"),
        },
        "|",
        "table",
        "horizontal-rule",
        "code",
        {
            name: "alert",
            action: addAlert,
            className: "fa fa-exclamation-triangle ",
            title: t("Alert"),
        },
        {
            name: "accordion",
            action: addAccordion,
            className: "fa fa-th-list ",
            title: t("Accordion"),
        },
        {
            name: "imagewithdimension",
            action: attachImage,
            className: "fa fa-archive",
            title: t("Custom Container"),
      },
      {
          name: "wiki",
          action: openCheatsheet,
          className: "fa fa-question-circle",
          title: "wiki",
      },
    ]

    const convertBodyToHtml = (body) => {
        let kbBody = replaceVideoAudioEmbedWithHtml(body);
    
        kbBody = convertTextAreaToMarkdown(kbBody)
        kbBody = mark_it_down.render(kbBody)
        
        // Convert video <img> tags to .file-to-proxy divs for proper video rendering
        kbBody = convertVideoImgsToFileProxy(kbBody);
        
        kbBody = kbBody.replace(/<img(.*?)class='img-fluid'(.*?)[^>]+>/gm, function (match) {
            const width = match.match(/width="([^"]*)"/) ?? "100%";
            const height = match.match(/height="([^"]*)"/) ?? "100%";
    
            return `<div class="uploaded-media" style="${width ? `max-width: ${width[1]}px;` : ''}${height ? `max-height: ${height[1]}px;` : ''}"><span>Loading Media...</span>${match}</div>`;
        });
    
        let accordionSearchRegex = /\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end/g;
    
    
        kbBody = kbBody.replace(accordionSearchRegex, (match, placeholder) => {
            let place = t("Search/Filter Results");
            if (placeholder.length > 1) {
                place = placeholder;
            }
    
            return `<div class="form-group mr-auto accordion-search">
                        <span class="fa fa-search"></span>
                        <input autofill="off" autocomplete="off" type="text" id="searchAccordion"
                        class="form-control form-control-sm accordion-search-input" placeholder="${place}">
                    </div>`;
        });
    
        // kbBody = kbBody.replace(/<p[^>]*>/gm, '')
        // kbBody = kbBody.replace(/<\/p[^>]*>/gm, '')
        // kbBody = kbBody.replace(/<br\s*[\/]?>/gm, '')
    
        return kbBody;
    }
   

    const initMarkdownEditor = () => {
        

        const html = convertBodyToHtml($('#editor').val());
        
        simplemde = new SimpleMDE({
            element: $("#editor")[0],
            spellChecker: config.enable_spellchecker,
            codeMirror: {
                options: {
                    readOnly: true
                }
            },
            toolbar: $('#is_restricted').val() != 'true' ? toolbar : [],
        });
        simplemde.codemirror.options.readOnly = $('#is_restricted').val() != 'true' ? false : true;
        
        // Expose simplemde to global scope
        window.simplemde = simplemde;
       
        // setup inline attachments
        inlineAttachment.editors.codemirror4.attach(simplemde.codemirror);

        
        // do initial convert on load
        

        // attach to editor changes and update preview when use stops typing
        simplemde.codemirror.on("change", function () {
            previewTimeout && clearTimeout(previewTimeout);
            previewTimeout = setTimeout(() => {
                const newHTML = convertBodyToHtml(simplemde.value());
                $("#preview").html(newHTML);
                
                // Process videos in markdown preview using our improved system
                if (window.activateAllVideoPlayers) {
                    setTimeout(() => {
                        window.activateAllVideoPlayers();
                    }, 100);
                }
            }, 800);
        });


		$("#preview").html(html);

		// re-hightlight the preview
		// $("pre code").each(function (i, block) {
		// 		hljs.highlightBlock(block);
		// });
        
        renderVideoJsFiles()
        
        // Process videos in markdown preview using our improved system
        if (window.activateAllVideoPlayers) {
            setTimeout(() => {
                window.activateAllVideoPlayers();
            }, 100);
        }

        // auto scrolls the simpleMDE preview pane
        var preview = document.getElementById("preview");
        if (preview !== null) {
            // Syncs scroll  editor -> preview
            var cScroll = false;
            var pScroll = false;
            simplemde.codemirror.on("scroll", function (v) {
                if (cScroll) {
                    cScroll = false;
                    return;
                }
                pScroll = true;
                var height = v.getScrollInfo().height - v.getScrollInfo().clientHeight;
                var ratio = parseFloat(v.getScrollInfo().top) / height;
                var move = (preview.scrollHeight - preview.clientHeight) * ratio;
                preview.scrollTop = move;
            });

            // Syncs scroll  preview -> editor
            preview.onscroll = function () {
                if (pScroll) {
                    pScroll = false;
                    return;
                }
                cScroll = true;
                var height = preview.scrollHeight - preview.clientHeight;
                var ratio = parseFloat(preview.scrollTop) / height;
                var move =
                    (simplemde.codemirror.getScrollInfo().height - simplemde.codemirror.getScrollInfo().clientHeight) *
                    ratio;
                simplemde.codemirror.scrollTo(0, move);
            };
        }
    }

    async function renderEditorVideoJsFiles() {
        // wait for translator to initialize
        if (translatorInitializing) {
            return setTimeout(renderEditorVideoJsFiles, 200);
        }
      
        $('.trumbowyg-editor-visible .file-to-proxy').each(renderVideoJsFile);
        // $('.trumbowyg-editor-visible .file-to-proxy').each((index, element) => {
        // });
    }


    const initWyiswygEditor = () => {
        const trumbowygConfig = {
            autogrow: false,
            defaultLinkTarget: '_blank',
            svgPath: '/openkb/themes/12rnd/images/icons.svg',
            semantic: false,
            lang: $('#user_lang').val(),
            btns: [
                ['viewHTML'],
                ['historyUndo', 'historyRedo'],
                ['formatting'],
                ['strong', 'em', 'del'],
                ['unorderedList', 'orderedList'],
                ['foreColor', 'backColor'],
                ['ubxlink', 'table'],
                ['alert', 'accordion', 'container'],
                ['upload'],
            ],
            plugins: {
                upload: {
                    
                },
                link:{},
                table: {},
                custom_alert: {},
            }
        }

        $('#editor').trumbowyg(trumbowygConfig) 
        renderEditorVideoJsFiles();

    }

    if($('#editor').length) {
        if ( $('#is_markdown').val() == "true" ) {
        // setup editors
        
            initMarkdownEditor()
        
        }  else {
        
            initWyiswygEditor()
        }
    }
   

    $(document).on("click", "#frm_create_kb_save", async function (e) {
        e.preventDefault();
        if($('#frm_kb_regional_visibility').val() === ''){
            Swal.fire({
                title: 'Article Regional Visibility',
                text: "Regional Visibility for this article is not set, are you sure you want to save this article?",
               
                showCancelButton: true,
               
                confirmButtonText: 'Continue',
                customClass: {
                    popup: "swal2-border-radius",
                },
                icon: "",
                didOpen: function () {
                    window.top.postMessage("modalShow", "*");
                },
                didClose: function () {
                    window.top.postMessage("modalHide", "*");
                },
                backdrop: `
                    rgba(0,0,0,0.5)
                `,
                showClass: {
                    backdrop: "swal2-noanimation", // disable backdrop animation
                    popup: "", // disable popup animation
                    icon: "", // disable icon animation
                },
                hideClass: {
                    popup: "", // disable popup fade-out animation
                },
                confirmButtonText: t('Continue!'),
                confirmButtonAriaLabel: t("Continue!"),
                confirmButtonColor: "#52ad39",
              }).then((result) => {
                if (result.isConfirmed) {
                    submitNewArticleForm();
                }
              })
        } else {
            submitNewArticleForm();
        }

       
    });

    async function submitNewArticleForm() {
        const formData = {
            frm_kb_body: $('#is_markdown').val() == 'true' ? getSimplemdeValue() : $('#editor').trumbowyg('html'),
            frm_kb_edit_reason: $('#frm_kb_edit_reason').val(),
            frm_kb_id: $('#frm_kb_id').val(),
            frm_kb_keywords: $('#frm_kb_keywords').val(),
            frm_kb_password: $('#frm_kb_password').val(),
            frm_kb_permalink: $('#frm_kb_permalink').val(),
            frm_kb_pinned_subtopic: $('#selectSubtopics').val(),
            frm_kb_pinned_topic: $('#selectTopics').val(),
            frm_kb_published: $('#frm_kb_published').val(),
            frm_kb_regional_visibility: $('#frm_kb_regional_visibility').val(),
            frm_kb_security_level: $('#frm_kb_security_level').val(),
            frm_kb_seo_description: $('#frm_kb_seo_description').val(),
            frm_kb_seo_title: $('#frm_kb_seo_title').val(),
            frm_kb_title: $('#frm_kb_title').val(),
            frm_kb_visible_state: $('#frm_kb_visible_state').val(),
            frm_kb_pinned: $('#kb_pinned').is(':checked') ? 'on' : '',
            frm_kb_language: $('#frm_kb_language').val(),
            frm_kb_featured: $('#frm_kb_featured').is(':checked') ? 'on' : '',
            frm_kb_admin_restricted: $('#frm_kb_admin_restricted').is(':checked') ? 'on' : '',
            frm_kb_is_markdown: $('#editor-switch').is(':checked') ? 'on':''
        }

        Swal.fire({
            html: '<div><h3 style="padding: 40px;">' + t('One Moment Please') + '...</h3></div>',
            width: 'auto',
            showCloseButton: false,
            showCancelButton: false,
            showConfirmButton: false,
        })

        try {
            const result = await axios.post($("#app_context").val() + "/api/insert_kb", formData);

            if (result.data.status !== 'success') {
                show_notification(result.data.message, 'danger');
                Swal.close();
                return;
            }

            Swal.close();
            show_notification(result.data.message, 'success');
            window.location.href = $("#app_context").val() + "/edit/" + result.data.data.id;

        } catch (error) {
            // Error creating article
            show_notification(error.response?.data?.message || 'Failed to create article', 'danger');
            Swal.close();
        }

    }

    // AI-powered article editing functions

    /**
     * Submit article edit form with AI-powered change detection and summary generation
     * 
     * Main article save function that detects changes, generates AI summaries,
     * saves the article, and displays comprehensive change analysis to the user.
     * 
     * @async
     * @returns {Promise<void>}
     */
    async function submitEditArticleForm() {
        // Capture current article data for change detection
        const currentData = window.captureCurrentArticleData ? window.captureCurrentArticleData() : null;
        const originalData = window.originalArticleData;
        
        // Detect changes if we have both original and current data
        let detectedChanges = [];
        if (originalData && currentData) {
            detectedChanges = window.detectArticleChanges ? window.detectArticleChanges(originalData, currentData) : [];
        }
      
        const formData = {
            frm_kb_edit_reason: $('#frm_kb_edit_reason').val(),
            frm_kb_body: $('#is_markdown').val() == 'true' ? getSimplemdeValue() : $('#editor').trumbowyg('html'),
            frm_kb_edit_reason: $('#frm_kb_edit_reason').val(),
            frm_kb_id: $('#frm_kb_id').val(),
            frm_kb_keywords: $('#frm_kb_keywords').val(),
            frm_kb_password: $('#frm_kb_password').val(),
            frm_kb_permalink: $('#frm_kb_permalink').val(),
            frm_kb_pinned_subtopic: $('#selectSubtopics').val(),
            frm_kb_pinned_topic: $('#selectTopics').val(),
            frm_kb_published: $('#frm_kb_published').val(),
            frm_kb_regional_visibility: $('#frm_kb_regional_visibility').val(),
            frm_kb_security_level: $('#frm_kb_security_level').val(),
            frm_kb_seo_description: $('#frm_kb_seo_description').val(),
            frm_kb_seo_title: $('#frm_kb_seo_title').val(),
            frm_kb_title: $('#frm_kb_title').val(),
            frm_kb_visible_state: $('#frm_kb_visible_state').val(),
            frm_kb_pinned: $('#kb_pinned').is(':checked') ? 'on' : '',
            frm_kb_language: $('#frm_kb_language').val(),
            frm_kb_featured: $('#frm_kb_featured').is(':checked') ? 'on' : '',
            frm_kb_admin_restricted: $('#frm_kb_admin_restricted').is(':checked') ? 'on' : '',
            frm_kb_is_markdown: $('#editor-switch').is(':checked') ? 'on':''
        }
        
    
        Swal.fire({
            html: '<div><h3 style="padding: 40px;">' + t('One Moment Please') + '...</h3></div>',
            width: 'auto',
            showCloseButton: false,
            showCancelButton: false,
            showConfirmButton: false,
        })

        try {
            let aiSummaryPromise = null;
            if (detectedChanges.length > 0 && window.generateChangelogSummaries) {
                aiSummaryPromise = window.generateChangelogSummaries(detectedChanges);
            }

            const result = await axios.post($("#app_context").val() + "/api/save_kb", formData);

            if (result.data.status !== 'success') {
                show_notification(result.data.message, 'danger');
                Swal.close();
                return;
            }

            // Wait for AI summary to complete if it was started
            let aiResult = null;
            if (aiSummaryPromise) {
                try {
                    aiResult = await aiSummaryPromise;
                } catch (error) {
                    // AI summary generation failed, but article was saved successfully
                    aiResult = { success: false, error: error.message || 'Unknown error' };
                }
            }

            // Generate AI-powered change summary if article was updated successfully (legacy support)
            if (formData.frm_kb_edit_reason && detectedChanges.length === 0) {
                await generateAndDisplayChangeSummary(formData.frm_kb_edit_reason, formData.frm_kb_title);
            }

            // Display change summary if we have one
            if (aiResult && detectedChanges.length > 0) {
                displayMultipleChangeSummaries(aiResult, detectedChanges);
            }

            Swal.close();
            show_notification(result.data.message, 'success');
            
            // Update original data for next comparison
            if (window.captureCurrentArticleData) {
                window.originalArticleData = window.captureCurrentArticleData();
            }
            
            window.location.reload();

        } catch (error) {
            // Error saving article
            show_notification(error.response?.data?.message || 'Failed to save article', 'danger');
            Swal.close();
        }
    }

    /**
     * Simplified article save function that bypasses AI processing
     * 
     * Direct form submission without change detection or AI analysis.
     * Used after AI processing is already complete or for quick saves.
     * 
     * @async
     * @returns {Promise<void>}
     */
    async function submitEditArticleFormWithoutAI() {
        const formData = {
            frm_kb_edit_reason: $('#frm_kb_edit_reason').val(),
            frm_kb_body: $('#is_markdown').val() == 'true' ? getSimplemdeValue() : $('#editor').trumbowyg('html'),
            frm_kb_edit_reason: $('#frm_kb_edit_reason').val(),
            frm_kb_id: $('#frm_kb_id').val(),
            frm_kb_keywords: $('#frm_kb_keywords').val(),
            frm_kb_password: $('#frm_kb_password').val(),
            frm_kb_permalink: $('#frm_kb_permalink').val(),
            frm_kb_pinned_subtopic: $('#selectSubtopics').val(),
            frm_kb_pinned_topic: $('#selectTopics').val(),
            frm_kb_published: $('#frm_kb_published').val(),
            frm_kb_regional_visibility: $('#frm_kb_regional_visibility').val(),
            frm_kb_security_level: $('#frm_kb_security_level').val(),
            frm_kb_seo_description: $('#frm_kb_seo_description').val(),
            frm_kb_seo_title: $('#frm_kb_seo_title').val(),
            frm_kb_title: $('#frm_kb_title').val(),
            frm_kb_visible_state: $('#frm_kb_visible_state').val(),
            frm_kb_pinned: $('#kb_pinned').is(':checked') ? 'on' : '',
            frm_kb_language: $('#frm_kb_language').val(),
            frm_kb_featured: $('#frm_kb_featured').is(':checked') ? 'on' : '',
            frm_kb_admin_restricted: $('#frm_kb_admin_restricted').is(':checked') ? 'on' : '',
            frm_kb_is_markdown: $('#editor-switch').is(':checked') ? 'on':''
        }
        
        Swal.fire({
            html: '<div><h3 style="padding: 40px;">' + t('Saving Changes') + '...</h3></div>',
            width: 'auto',
            showCloseButton: false,
            showCancelButton: false,
            showConfirmButton: false,
        })

        try {
            // Use axios instead of jQuery AJAX
            const result = await axios.post($("#app_context").val() + "/api/save_kb", formData);

            if (result.data.status !== 'success') {
                show_notification(result.data.message, 'danger');
                Swal.close();
                return;
            }

            Swal.close();
            show_notification(result.data.message, 'success');
            
            // Update original data for next comparison
            if (window.captureCurrentArticleData) {
                window.originalArticleData = window.captureCurrentArticleData();
            }
            
            window.location.reload();

        } catch (error) {
            // Error saving article
            show_notification(error.response?.data?.message || 'Failed to save article', 'danger');
            Swal.close();
        }
    }

    /**
     * Generate AI-powered summary for manual change descriptions (legacy support)
     * 
     * Creates AI analysis for user-entered change descriptions and displays
     * the structured summary in a modal dialog.
     * 
     * @async
     * @param {string} changeReason - User-provided description of the change
     * @param {string} articleTitle - Title of the article being modified
     * @returns {Promise<void>}
     */
    async function generateAndDisplayChangeSummary(changeReason, articleTitle) {
        try {
            const prompt = `Generate a concise JSON summary of this article change:
            
                Article: "${articleTitle}"
                Change Description: "${changeReason}"

                Return a JSON object with the following structure:
                {
                "summary": "Brief 1-2 sentence summary of the change",
                "impact": "What this change means for readers",
                "type": "Type of change (content update, formatting, correction, etc.)"
                }

                Focus on being clear and informative for other editors.`;

            const result = await axios.post($("#app_context").val() + "/api/ai/summarize-changelog", {
                prompt: prompt
            });

            if (result.data && result.data.data) {
                displayChangeSummary(result.data.data, changeReason);
            }
        } catch (error) {
            // AI summary generation failed - fail silently as this is a nice-to-have feature
            // Fail silently - this is a nice-to-have feature
        }
    }

    /**
     * Display AI-generated change summary in a modal dialog
     * 
     * Shows structured AI analysis alongside the original change description.
     * Handles JSON parsing errors gracefully with fallback messages.
     * 
     * @param {Object|string} aiSummary - AI response (JSON object or string to parse)
     * @param {string} originalReason - Original user-provided change description
     */
    function displayChangeSummary(aiSummary, originalReason) {
        try {
            const summary = typeof aiSummary === 'string' ? JSON.parse(aiSummary) : aiSummary;
            
            Swal.fire({
                title: 'Change Summary Generated',
                html: `
                    <div style="text-align: left; margin: 20px 0;">
                        <h4>📝 Your Change:</h4>
                        <p style="font-style: italic; background: #f5f5f5; padding: 10px; border-radius: 4px;">"${originalReason}"</p>
                        
                        <h4>🤖 AI Summary:</h4>
                        <p><strong>Summary:</strong> ${summary.summary || 'N/A'}</p>
                        <p><strong>Impact:</strong> ${summary.impact || 'N/A'}</p>
                        <p><strong>Type:</strong> ${summary.type || 'N/A'}</p>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'Got it!',
                confirmButtonColor: '#52ad39',
                customClass: {
                    popup: "swal2-border-radius",
                },
                didOpen: function () {
                    window.top.postMessage("modalShow", "*");
                },
                didClose: function () {
                    window.top.postMessage("modalHide", "*");
                },
                backdrop: `rgba(0,0,0,0.5)`,
                showClass: {
                    backdrop: "swal2-noanimation",
                    popup: "",
                },
                hideClass: {
                    popup: "",
                },
            });
        } catch (parseError) {
            // Failed to parse AI summary - show fallback success message
            // Still show something useful
            Swal.fire({
                title: 'Change Logged',
                text: 'Your changes have been saved successfully!',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    }

    /**
     * Display AI-generated change summary for multiple detected changes
     * 
     * This function handles the presentation of AI-analyzed change summaries when multiple
     * fields have been modified in an article. It provides a consolidated view of all changes
     * with AI-generated insights about what was modified and why.
     * 
     * @param {Object} aiResult - Result from AI analysis containing success status and summary
     * @param {Array} detectedChanges - Array of change objects with fieldName and change details
     * 
     * Display consolidated AI summary for multiple field changes in a modal dialog.
     * Handles both successful AI analysis and failure cases with appropriate fallbacks.
     * 
     * @param {Object} aiResult - AI analysis result object
     * @param {Array} detectedChanges - Array of detected field changes
     */
    function displayMultipleChangeSummaries(aiResult, detectedChanges) {
        if (!aiResult) {
            return;
        }

        try {
            if (aiResult.success && aiResult.summary) {
                // Successful consolidated summary
                const summary = typeof aiResult.summary === 'string' ? JSON.parse(aiResult.summary) : aiResult.summary;
                const changes = summary.changes || summary;
                
                const summaryHtml = `
                    <div style="margin-bottom: 20px; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background: #f9f9f9;">
                        <h5 style="margin: 0 0 15px 0; color: #333;">🤖 AI Analysis Summary</h5>
                        <div style="margin-bottom: 12px;">
                            <strong>Type:</strong> <span style="text-transform: capitalize; color: #666;">${changes.type || 'Modified'}</span>
                        </div>
                        ${changes.note ? `
                            <div style="margin-bottom: 12px;">
                                <strong>Summary:</strong> <span style="color: #555;">${changes.note}</span>
                            </div>
                        ` : ''}
                        ${changes.summary ? `
                            <div style="margin-bottom: 8px;">
                                <strong>Details:</strong> <span style="color: #555;">${changes.summary}</span>
                            </div>
                        ` : ''}
                    </div>
                `;

                Swal.fire({
                    title: `🤖 AI Change Analysis`,
                    html: `
                        <div style="text-align: left; margin: 20px 0; max-height: 400px; overflow-y: auto;">
                            <p style="margin-bottom: 20px; color: #666; font-size: 14px;">
                                ${detectedChanges.length} field${detectedChanges.length !== 1 ? 's' : ''} changed in this article:
                            </p>
                            <div style="margin-bottom: 15px; padding: 10px; background: #f0f8ff; border-radius: 4px; font-size: 13px;">
                                <strong>Changed fields:</strong> ${detectedChanges.map(c => c.fieldName).join(', ')}
                            </div>
                            ${summaryHtml}
                        </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'Got it!',
                    confirmButtonColor: '#52ad39',
                    customClass: {
                        popup: "swal2-border-radius",
                    },
                    width: '600px',
                    didOpen: function () {
                        window.top.postMessage("modalShow", "*");
                    },
                    didClose: function () {
                        window.top.postMessage("modalHide", "*");
                    },
                    backdrop: `rgba(0,0,0,0.5)`,
                    showClass: {
                        backdrop: "swal2-noanimation",
                        popup: "",
                    },
                    hideClass: {
                        popup: "",
                    },
                });
            } else {
                // Failed AI summary
                Swal.fire({
                    title: '⚠️ AI Analysis Failed',
                    html: `
                        <div style="text-align: left; margin: 20px 0;">
                            <p style="margin-bottom: 15px; color: #666;">
                                ${detectedChanges.length} field${detectedChanges.length !== 1 ? 's' : ''} were changed, but AI analysis failed:
                            </p>
                            <div style="margin-bottom: 15px; padding: 10px; background: #f0f8ff; border-radius: 4px; font-size: 13px;">
                                <strong>Changed fields:</strong> ${detectedChanges.map(c => c.fieldName).join(', ')}
                            </div>
                            <div style="padding: 10px; border: 1px solid #ffcccb; border-radius: 8px; background: #fff5f5;">
                                <p style="margin: 0; color: #d32f2f; font-size: 14px;">
                                    <strong>Error:</strong> ${aiResult.error || 'Unknown error'}
                                </p>
                            </div>
                        </div>
                    `,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#52ad39',
                    customClass: {
                        popup: "swal2-border-radius",
                    },
                    width: '600px',
                    didOpen: function () {
                        window.top.postMessage("modalShow", "*");
                    },
                    didClose: function () {
                        window.top.postMessage("modalHide", "*");
                    },
                    backdrop: `rgba(0,0,0,0.5)`,
                    showClass: {
                        backdrop: "swal2-noanimation",
                        popup: "",
                    },
                    hideClass: {
                        popup: "",
                    },
                });
            }
        } catch (parseError) {
            // Failed to display change summary - show fallback success message
            // Fallback to simple success message
            Swal.fire({
                title: 'Changes Saved',
                text: `Your article has been updated with ${detectedChanges.length} change${detectedChanges.length !== 1 ? 's' : ''}!`,
                icon: 'success',
                timer: 3000,
                showConfirmButton: false
            });
        }
    }

    /**
     * Open AI-powered save dialog with change detection and summary generation
     * 
     * Detects article changes, generates AI summaries, and presents an editable
     * changelog dialog. Falls back gracefully when AI services are unavailable.
     * 
     * @async
     * @returns {Promise<void>}
     */
    async function openSaveDialog() {
        // Capture current article data for change detection
        const currentData = window.captureCurrentArticleData ? window.captureCurrentArticleData() : null;
        const originalData = window.originalArticleData;
        
        let aiGeneratedSummary = '';
        let detectedChanges = [];
        
        // Detect changes if we have both original and current data
        if (originalData && currentData) {
            detectedChanges = window.detectArticleChanges ? window.detectArticleChanges(originalData, currentData) : [];
            
            // Generate AI summary if there are changes
            if (detectedChanges.length > 0 && window.generateChangelogSummaries) {
                try {
                    // Show loading state
                    Swal.fire({
                        title: t('Generating Change Summary'),
                        html: '<div><h3 style="padding: 40px;">' + t('Analyzing your changes...') + '</h3></div>',
                        width: 'auto',
                        showCloseButton: false,
                        showCancelButton: false,
                        showConfirmButton: false,
                        allowOutsideClick: false
                    });
                    
                    const aiResult = await window.generateChangelogSummaries(detectedChanges);
                    
                    if (aiResult && aiResult.success && aiResult.summary) {
                        const summary = typeof aiResult.summary === 'string' ? JSON.parse(aiResult.summary) : aiResult.summary;
                        if (summary.changes.summary) {
                            aiGeneratedSummary = summary.changes.summary;
                        } else if (summary.note) {
                            aiGeneratedSummary = summary.note;
                        } else {
                            aiGeneratedSummary = `Updated ${detectedChanges.length} field${detectedChanges.length !== 1 ? 's' : ''}: ${detectedChanges.map(c => c.fieldName).join(', ')}`;
                        }
                    }
                } catch (error) {
                    // Fallback summary if AI fails
                    aiGeneratedSummary = `Updated ${detectedChanges.length} field${detectedChanges.length !== 1 ? 's' : ''}: ${detectedChanges.map(c => c.fieldName).join(', ')}`;
                }
            }
        }
        
        // Close loading dialog
        Swal.close();
        
        // Show the changelog dialog with AI-generated summary pre-filled
        Swal.fire({
            title: t("Change Log"),
            html: `
                <div style="text-align: left; margin-bottom: 15px;">
                    <p style="margin-bottom: 10px;">${t("What did you add/change in this article?")}</p>
                    ${detectedChanges.length > 0 ? `
                        <div style="margin-bottom: 10px; padding: 10px; background: #f0f8ff; border-radius: 4px; font-size: 13px;">
                            <strong>🤖 AI detected ${detectedChanges.length} change${detectedChanges.length !== 1 ? 's' : ''}:</strong><br>
                            <small>${detectedChanges.map(c => c.fieldName).join(', ')}</small>
                        </div>
                    ` : ''}
                </div>
            `,
            input: "textarea",
            inputValue: aiGeneratedSummary,
            inputPlaceholder: aiGeneratedSummary ? t("AI-generated summary (you can edit this)") : t("Please describe your changes"),
            showCloseButton: true,
            showCancelButton: true,
            cancelButtonText: t('Cancel'),
            customClass: {
                popup: "swal2-border-radius",
            },
            inputValidator: (value) => {
                return new Promise((resolve) => {
                    if (value.length > 5) {
                        resolve();
                    } else {
                        resolve("Please input a longer text.");
                    }
                });
            },
            icon: "",
            didOpen: function () {
                window.top.postMessage("modalShow", "*");
                // Focus and select the textarea content for easy editing
                const textarea = Swal.getInput();
                if (textarea && aiGeneratedSummary) {
                    setTimeout(() => {
                        textarea.focus();
                        textarea.select();
                    }, 100);
                }
            },
            didClose: function () {
                window.top.postMessage("modalHide", "*");
            },
            backdrop: `rgba(0,0,0,0.5)`,
            showClass: {
                backdrop: "swal2-noanimation",
                popup: "",
                icon: "",
            },
            hideClass: {
                popup: "",
            },
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> ' + t('Save Changes'),
            confirmButtonAriaLabel: t("Proceed to save"),
            confirmButtonColor: "#52ad39",
            width: '600px'
        }).then(function (res) {
            if (res.value) {
                // Pass value to hidden input for edit reason
                $("#frm_kb_edit_reason").val(res.value);
                // Proceed to actual save (without additional AI processing since we already did it)
                submitEditArticleFormWithoutAI();
            }   
        });
    }
    
    // Editor save button clicked
    $(document).on("click", "#frm_edit_kb_save", function (e) {
        e.preventDefault();
        
        if($('#frm_kb_regional_visibility').val() === ''){
            Swal.fire({
                title: 'Article Regional Visibility',
                text: "Regional Visibility for this article is not set, are you sure you want to save this article?",
               
                showCancelButton: true,
               
                confirmButtonText: 'Continue',
                customClass: {
                    popup: "swal2-border-radius",
                },
                icon: "",
                didOpen: function () {
                    window.top.postMessage("modalShow", "*");
                },
                didClose: function () {
                    window.top.postMessage("modalHide", "*");
                },
                backdrop: `
                    rgba(0,0,0,0.5)
                `,
                showClass: {
                    backdrop: "swal2-noanimation", // disable backdrop animation
                    popup: "", // disable popup animation
                    icon: "", // disable icon animation
                },
                hideClass: {
                    popup: "", // disable popup fade-out animation
                },
                confirmButtonText: t('Continue!'),
                confirmButtonAriaLabel: t("Continue!"),
                confirmButtonColor: "#52ad39",
              }).then((result) => {
                if (result.isConfirmed) {
                    openSaveDialog();
                }
              })
        } else {
            openSaveDialog();
        }
    });

    function openSaveAsMarkdownDialog() {
        Swal.fire({
            title: t("Switch to Markdown"),
            text: t("Switching to Markdown editor may lead to formatting issues. Please ensure you review the entire article and all formatting if you change to the proceed"),
            showConfirmButton: true,
            confirmButtonText: t('Proceed'),
            showCancelButton: true,
            cancelButtonText: t('Cancel'),
            customClass: {
                popup: "swal2-border-radius",
            },
            
            // inputPlaceholder: "Please input your reason for editing this article",
            icon: "",
            didOpen: function () {
                window.top.postMessage("modalShow", "*");
            },
            didClose: function () {
                window.top.postMessage("modalHide", "*");
            },
            backdrop: `
                rgba(0,0,0,0.5)
            `,
            showClass: {
                backdrop: "swal2-noanimation", // disable backdrop animation
                popup: "", // disable popup animation
                icon: "", // disable icon animation
            },
            hideClass: {
                popup: "", // disable popup fade-out animation
            },
            
            confirmButtonColor: "#52ad39",
        }).then(function (res) {
        
            if(res.isConfirmed === false) {
                $('#editor-switch').prop('checked', false)
            }

            if (res.value) {
                // if($('#frm_kb_id').val() == '') {
                //     submitNewArticleForm()
                // } else {
                //     //pass value to hidden input for edit reason
                //     $("#frm_kb_edit_reason").val('Change editor to Markdown');
                //     // proceed to actual save
                //     submitEditArticleForm();
                // }
                changeToMarkdown()
            }
        });
    }

    function openSaveAsWysiwygDialog() {
        Swal.fire({
            title: t("Switch to WYSIWYG"),
            //text: t("Switching to the WYSIWYG editor may lead to formatting issues. This action is irreversible, and you won't be able to switch back. Are you certain you want to proceed?"),
            text: t("Switching to the WYSIWYG editor may lead to formatting issues. Please ensure you review the entire article and all formatting if you change to the proceed"),
            showConfirmButton: true,
            confirmButtonText: t('Proceed'),
            showCancelButton: true,
            cancelButtonText: t('Cancel'),
            customClass: {
                popup: "swal2-border-radius",
            },
            
            // inputPlaceholder: "Please input your reason for editing this article",
            icon: "",
            didOpen: function () {
                window.top.postMessage("modalShow", "*");
            },
            didClose: function () {
                window.top.postMessage("modalHide", "*");
            },
            backdrop: `
                rgba(0,0,0,0.5)
            `,
            showClass: {
                backdrop: "swal2-noanimation", // disable backdrop animation
                popup: "", // disable popup animation
                icon: "", // disable icon animation
            },
            hideClass: {
                popup: "", // disable popup fade-out animation
            },
            
            confirmButtonColor: "#52ad39",
        }).then(function (res) {
            if(res.isConfirmed === false) {
              
                $('#editor-switch').prop('checked', true)
            }

            if (res.value) {
                // if($('#frm_kb_id').val() == '') {
                //     //submitNewArticleForm()
                // } else {
                //     //pass value to hidden input for edit reason
                //     //$("#frm_kb_edit_reason").val('Change editor to WYSIWYG');
                //     // proceed to actual save
                //     //submitEditArticleForm();

                    
                // }
                changeToWyiswyg();
                
            } 
        });
    }


    const changeToMarkdown = () => {
        $('#is_markdown').val(true);
        
        // Respect user's preview preference instead of forcing show
        const savedPreviewState = localStorage.getItem('ph-editor-show-preview');
        const shouldShowPreview = savedPreviewState !== null ? savedPreviewState === 'true' : true;
        
        $('#editor').trumbowyg('destroy');
     
        const  converter = new window.showdown.Converter();
        //converter.setOption('simpleLineBreaks', true);
        let md = converter.makeMarkdown($('#editor').val(), window.document);

    

        md = md.replace(/<div class="form-group mr-auto accordion-search"><\div>/g, (match) => {
            const replacement = [];
            replacement.push('$accordion-search-start');
        })

        md = md.replace(/<img[^>]+>/im, function (match) {
            const alt = match.match(/alt="([^"]*)"/) != null ? match.match(/alt="([^"]*)"/) : ['',''];
            const src = match.match(/src="([^"]*)"/) != undefined ? match.match(/src="([^"]*)"/) : ['',''];
            const width = match.match(/width="([^"]*)"/) != undefined ? match.match(/width="([^"]*)"/) : ['',''];
            const height = match.match(/height="([^"]*)"/) ?? ['',''];
            
            return `![${alt[1]}](${src[1]} ${(width[1] || height[1]) ?  `=${width[1]}x${height[1]}` : ''})`;

            
        });
        
        
        //image and placeholder conversion to markdown
        md = md.replace(/<div class="uploaded-media" (.*?)><span>Loading media...<\/span>(.*?)<\/div>/gm, (match, div, img) => {
            return img
        })

        //alert to markdown
        md = md.replace(/<div class="alert alert-(.*?)" role="alert">(.*?)<\/div>/g, (match, className, content) => {
            return `$alert ${className} ${content.replace('<p></p>', '')}\n`
        })

        //media to markdown
        md = md.replace(/<div class="media">(.*?)<\/div>/g, (match, img) => {
            return img
        })

        
        //video to markdown
        md = md.replace(/<div class="file-to-proxy" data-url="(.*?)" (.*?)>Loading media...<\/div>/g, (match, url, others) => {
            return `![](${url})`
        })

        /**
         * 
    <div class="form-group mr-auto accordion-search"><span class="fa fa-search"></span><input autofill="off" autocomplete="off" type="text" id="searchAccordion" class="form-control form-control-sm accordion-search-input" placeholder=" test
    "></div>

    <div id="accordion-325" class="accordion-item"><div class="card"><div class="card-header accordion-header"><a class="card-link" data-toggle="collapse" href="#collapse-325" aria-expanded="true"> Add panel title </a></div><div id="collapse-325" class="collapse show" data-parent="#accordion-325"><div class="card-body"><p></p><p>Add panel body</p><p></p></div></div></div></div>
        */

        md = md.replace(/<div class="form-group mr-auto accordion-search"><span class="fa fa-search"><\/span><input autofill="off" autocomplete="off" type="text" id="searchAccordion" class="form-control form-control-sm accordion-search-input" placeholder="([\s\S]*?)"><\/div>\n\n<div id="(.*?)" class="accordion-item"><div class="card"><div class="card-header accordion-header"><a class="card-link" data-toggle="collapse" href="#(.*?)" aria-expanded="true">([\s\S]*?)<\/a><\/div><div id="(.*?)" class="collapse show" data-parent="#(.*?)"><div class="card-body"><p>([\s\S]*?)<\/p><\/div><\/div><\/div><\/div>/g, 
        (match, placeholder, id1, id2, title, id3, id4, body) => {
            body = body.replace('<p>', '')
            body = body.replace('</p>', '')
            return `$accordion-start\n$accordion-search-start\n$placeholder:${placeholder}\n$accordion-search-end\n$panel-start\n$title: ${title}\n$body:\n\n${body}\n\n$panel-end\n$accordion-end`
        })


        md = md.replace(/<div id="(.*?)" class="accordion-item">((.|\n)*)<\/div>/gm, 
        //(match, id1, id2, title, id3, id4, body) => {
        (match, id, content) => {    
            const parts  = []
            parts.push('$accordion-start')
            parts.push(content)
            parts.push('$accordion-end')

            return parts.join('\n')
            //return `$accordion-start\n$panel-start\n$title: ${title}\n$body:\n\n${body}\n\n$panel-end\n$accordion-end`
        })

       
        md = md.replace(/<div class="card"><div class="card-header accordion-header"><a (.*?)>([\s\S]*?)<\/a><\/div><div id="(.*?)" class="collapse show" data-parent="#(.*?)"><div class="card-body">([\s\S]*?)<\/div><\/div><\/div><\/div>/gm, 
            (match, href, title, collapseId, parentId, body) => {

                const parts = []
                parts.push('$panel-start')
                parts.push(`$title: ${title}`)
                parts.push(`$body:\n\n${body}\n`)
                parts.push('$panel-end')

                return parts.join('\n')
            })
       
        md = md.replace(/<div class="container"><div class="row text-(.*?)"><div style="height:(.*?); width:(.*?);">(.*?)<\/div><\/div>/gm, (match, align, height, width, item) => {
            return `$container-start\n$item:${item}\n$height:${height}\n$width:${width}\n$align:${align}\n$container-end`
        
        })

        md = md.replace(/<p[^>]*>/gm, '')
        md = md.replace(/<\/p[^>]*>/gm, '')
        md = md.replace(/<br\s*[\/]?>/gm, '')
    
    

        $('#editor').val(md)

        initMarkdownEditor()
        
        // Update preview toggle button visibility and sync preview state after markdown editor is initialized
        if (typeof updatePreviewToggleButtonVisibility === 'function' && typeof syncPreviewVisibility === 'function') {
            updatePreviewToggleButtonVisibility(true);
            syncPreviewVisibility(shouldShowPreview);
            updateEditorLabel();
        }
    }

    const changeToWyiswyg = () => {
        $('#is_markdown').val(false);
        
        simplemde.toTextArea();
        simplemde = null;
        window.simplemde = null;

        const body = $('#editor').val()
       
        let kbBody = replaceVideoAudioEmbedWithHtml(body);

        //kbBody = sanitizeHTML(mark_it_down.render(kbBody));
        kbBody = convertTextAreaToMarkdown(kbBody)
        
        // Only wrap images that are not already inside uploaded-media containers
        kbBody = kbBody.replace(/<img[^>]+>/g, function (match) {
            // Check if this img is already inside an uploaded-media div
            // by looking for uploaded-media pattern before this img in the same context
            const beforeImg = kbBody.substring(0, kbBody.indexOf(match));
            const lastUploadedMediaStart = beforeImg.lastIndexOf('<div class="uploaded-media"');
            const lastUploadedMediaEnd = beforeImg.lastIndexOf('</div>');
            
            // If there's an uploaded-media div that hasn't been closed, skip wrapping
            if (lastUploadedMediaStart > lastUploadedMediaEnd && lastUploadedMediaStart !== -1) {
                // Just add the handlers to the existing img
                return match.replace('<img', '<img onerror="this.classList.add(\'img-error\'); this.parentElement.classList.add(\'media-error\'); this.parentElement.querySelector(\'span\').textContent=\'Image not available\';" onload="this.classList.add(\'img-fluid\'); this.parentElement.querySelector(\'span\').style.display=\'none\';"');
            }
            
            const width = match.match(/width="([^"]*)"/);
            const height = match.match(/height="([^"]*)"/);

            const imgWithHandlers = match.replace('<img', '<img onerror="this.classList.add(\'img-error\'); this.parentElement.classList.add(\'media-error\'); this.parentElement.querySelector(\'span\').textContent=\'Image not available\';" onload="this.classList.add(\'img-fluid\'); this.parentElement.querySelector(\'span\').style.display=\'none\';"');
            return `<div class="uploaded-media" style="${width ? `max-width: ${width[1]}px;` : ''}${height ? `max-height: ${height[1]}px;` : ''}"><span>Loading Media...</span>${imgWithHandlers}</div>`;
        });

        //let accordionSearchRegex = /\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end/g;
        let accordionSearchRegex = /\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end/g;

        kbBody = kbBody.replace(accordionSearchRegex, (match, placeholder) => {
            let place = t("Search/Filter Results");
            if (placeholder.length > 1) {
                place = placeholder;
            }

            return `<div class="form-group mr-auto accordion-search">
                        <span class="fa fa-search"></span>
                        <input autofill="off" autocomplete="off" type="text" id="searchAccordion"
                        class="form-control form-control-sm accordion-search-input" placeholder="${place}">
                    </div>`;
        });

        kbBody = kbBody.replace('<p>', '')
        kbBody = kbBody.replace('</p>', '')


        //kbBody = mark_it_down.render(kbBody);

        $('#editor').val(kbBody)

        initWyiswygEditor()

        if (typeof updatePreviewToggleButtonVisibility === 'function' && typeof syncPreviewVisibility === 'function') {
            updatePreviewToggleButtonVisibility(false);
            syncPreviewVisibility(false);
            localStorage.setItem('ph-editor-markdown-enabled', false);
            updateEditorLabel();
        }
    }

    $('#editor-switch').on('change', function() {
        // if($('#frm_kb_id').val() == '') {
        //     return;
        // }
        if($(this).is(':checked')) {
            openSaveAsMarkdownDialog()
        } else {
            openSaveAsWysiwygDialog()
        }
    })

    // Version edit button clicked
    $(document).on("click", ".btnEditVersion", function (e) {
        const versionId = $(this).parent().attr("id");
        
        $("#btnVersionMenu").trigger("click");
        $.LoadingOverlay("show", { zIndex: 9999 });
        
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/api/getArticleJson",
            data: { kb_id: versionId },
        })
            .done(function (article) {
                $.LoadingOverlay("hide");
                
                // Debug: Log all article fields to see what's available
                
                // Update all form fields from the selected version
                $("#frm_kb_title").val(article.kb_title);
                
                // Update keywords field and tokenfield plugin
                const keywordsField = $("#frm_kb_keywords");
                if (keywordsField.data('bs.tokenfield')) {
                    // If tokenfield is initialized, update it properly
                    keywordsField.tokenfield('setTokens', article.kb_keywords || '');
                } else {
                    // Fallback to direct value setting
                    keywordsField.val(article.kb_keywords || '');
                }
                
                $("#frm_kb_permalink").val(article.kb_permalink || '');
                $("#frm_kb_seo_title").val(article.kb_seo_title || '');
                $("#frm_kb_seo_description").val(article.kb_seo_description || '');
                $("#frm_kb_password").val(article.kb_password || '');
                
                // Handle editor content based on version's markdown state
                const versionIsMarkdown = article.kb_is_markdown === true;
                const currentIsMarkdown = $('#is_markdown').val() === 'true';
                
                if (versionIsMarkdown && currentIsMarkdown) {
                    // Both are markdown - direct update
                    if (simplemde) {
                        simplemde.value(article.kb_body);
                    } else {
                        $('#editor').val(article.kb_body);
                    }
                } else if (!versionIsMarkdown && !currentIsMarkdown) {
                    // Both are WYSIWYG - direct update
                    if ($('#editor').data('trumbowyg')) {
                        $('#editor').trumbowyg('html', article.kb_body);
                    } else {
                        $('#editor').val(article.kb_body);
                    }
                } else {
                    // Editor type mismatch - need to switch editor first
                    if (versionIsMarkdown && !currentIsMarkdown) {
                        // Switch to markdown editor
                        $('#editor-switch').prop('checked', true);
                        $('#is_markdown').val('true');
                        changeToMarkdown();
                        // Set content after editor switch
                        setTimeout(() => {
                            if (simplemde) {
                                simplemde.value(article.kb_body);
                            } else {
                                $('#editor').val(article.kb_body);
                            }
                        }, 100);
                    } else {
                        // Switch to WYSIWYG editor
                        $('#editor-switch').prop('checked', false);
                        $('#is_markdown').val('false');
                        changeToWyiswyg();
                        // Set content after editor switch
                        setTimeout(() => {
                            if ($('#editor').data('trumbowyg')) {
                                $('#editor').trumbowyg('html', article.kb_body);
                            } else {
                                $('#editor').val(article.kb_body);
                            }
                        }, 100);
                    }
                }
                
                // Update topics and subtopics dropdowns
                
                // Handle topics and subtopics with proper dependency logic
                if (article.kb_pinned_topic !== undefined && article.kb_pinned_topic !== null) {
                    // Case 1: Topic is specified
                    const topicsSelect = $("#selectTopics");
                    topicsSelect.val(article.kb_pinned_topic);
                    
                    // Trigger change to update subtopics
                    if (window.selectTopicsChange) {
                        try {
                            window.selectTopicsChange();
                        } catch (error) {
                        }
                    }
                    
                    // Set subtopic after topics change
                    setTimeout(() => {
                        if (article.kb_pinned_subtopic !== undefined && article.kb_pinned_subtopic !== null) {
                            $("#selectSubtopics").val(article.kb_pinned_subtopic);
                        }
                    }, 500);
                    
                } else if (article.kb_pinned_subtopic !== undefined && article.kb_pinned_subtopic !== null) {
                    // Case 2: Only subtopic is specified (topic is null) - need to find parent topic
                    
                    // Try to find which topic contains this subtopic by checking all topics
                    const topicsSelect = $("#selectTopics");
                    const topicOptions = topicsSelect.find('option');
                    let foundTopic = null;
                    
                    
                    // We need to check each topic to find which one contains our subtopic
                    // This is a bit complex, so let's try a different approach
                    // For now, let's try to set the subtopic directly and see what happens
                    $("#selectSubtopics").val(article.kb_pinned_subtopic);
                    
                    // If that didn't work, we need to load all topics and find the right one
                    if ($("#selectSubtopics").val() === null) {
                        
                        // TODO: We would need to make API calls to find which topic contains this subtopic
                        // For now, just clear both selections
                        topicsSelect.val('');
                        $("#selectSubtopics").val('');
                    }
                    
                    
                } else {
                    // Case 3: Neither topic nor subtopic specified
                    $("#selectTopics").val('');
                    $("#selectSubtopics").val('');
                }
                
                
                // Update regional visibility with proper UI updates
                if (article.kb_regional_visibility !== undefined) {
                    
                    // Update the hidden field
                    $("#frm_kb_regional_visibility").val(article.kb_regional_visibility || '');
                    
                    // Update the visual display text
                    const regionalValue = article.kb_regional_visibility || '';
                    if (regionalValue && regionalValue !== '') {
                        const countries = regionalValue.split(',').map(c => c.trim()).join(', ');
                        $("#optionSelectCountry").text(countries);
                    } else {
                        // Use the appropriate placeholder
                        const is_pseudoadmin = $('#is_pseudoadmin').val() === 'true';
                        const countryDropdownData = $('#selectCountryDropdown').data('options');
                        const pseudoadminCountries = countryDropdownData ? countryDropdownData.split(',').join(', ') : '';
                        const articleId = $('#article_id').val();
                        const selectPlaceholder = is_pseudoadmin && articleId == '' ? pseudoadminCountries : 'Global';
                        $("#optionSelectCountry").text(selectPlaceholder);
                    }
                    
                    // Update country checkboxes to reflect the selection
                    if (window.setAvailableCountries) {
                        window.setAvailableCountries();
                    } else {
                    }
                    
                } else {
                }
                
                // Update other form elements if they exist
                if (article.kb_published !== undefined) {
                    $("#frm_kb_published").val(article.kb_published.toString());
                }
                if (article.kb_pinned !== undefined) {
                    $("#kb_pinned").prop('checked', article.kb_pinned);
                }
                if (article.kb_featured !== undefined) {
                    $("#frm_kb_featured").prop('checked', article.kb_featured);
                }
                if (article.kb_visible_state !== undefined) {
                    $("#frm_kb_visible_state").val(article.kb_visible_state);
                }
                if (article.kb_regional_visibility !== undefined) {
                    $("#frm_kb_regional_visibility").val(article.kb_regional_visibility);
                    
                    // Update regional visibility dropdown if it uses a plugin
                    const regionalSelect = $("#frm_kb_regional_visibility");
                    if (regionalSelect.hasClass('selectpicker') || regionalSelect.data('select2')) {
                        // Bootstrap Select or Select2 plugin
                        regionalSelect.trigger('change');
                    }
                }
                if (article.kb_security_level !== undefined) {
                    $("#frm_kb_security_level").val(article.kb_security_level);
                }
                if (article.kb_language !== undefined) {
                    $("#frm_kb_language").val(article.kb_language);
                }
                
                // Update original data for AI change detection
                if (window.captureCurrentArticleData) {
                    window.originalArticleData = window.captureCurrentArticleData();
                }
                
                show_notification("Version loaded successfully", "success");
            })
            .fail(function (msg) {
                $.LoadingOverlay("hide");
                show_notification(msg.responseText || "Failed to load version", "danger");
            });
    });

    // Version delete button clicked
    $(document).on("click", ".btnDeleteVersion", function (e) {
        var groupElement = $(this).closest(".versionWrapper");
        $("#btnVersionMenu").trigger("click");
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/api/deleteVersion",
            data: { kb_id: $(this).parent().attr("id") },
        })
            .done(function (article) {
                // remove the version elements from DOM
                groupElement.remove();
                show_notification("Version removed successfully", "success");
            })
            .fail(function (msg) {
                show_notification(JSON.parse(msg.responseText).message, "danger");
            });
    });

    // if in the editor, trap ctrl+s and cmd+s shortcuts and save the article
    if ($("#frm_editor").val() === "true") {
        $(window).bind("keydown", function (event) {
            if (event.ctrlKey || event.metaKey) {
                if (String.fromCharCode(event.which).toLowerCase() === "s") {
                    event.preventDefault();
                    $("#frm_edit_kb_save").click();
                }
            }
        });
    }

    // Call to API for a change to the published state of a KB
    $("input[class='published_state']").change(function () {
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/published_state",
            data: { id: this.id, state: this.checked },
        })
            .done(function (msg) {
                show_notification(msg, "success");
            })
            .fail(function (msg) {
                show_notification(msg.responseText, "danger");
            });
    });


    $("#searchAccordion").on("keyup", function () {
        var keyword = $(this).val();
        if (keyword.length > 0) {
            $(".accordions .card").hide();
            $(".accordions")
                .find(".card:contains(" + keyword + ")")
                .css("display", "block");
            
            //un-collapse to show contents
            $(".accordions")
                .find(".card:contains(" + keyword + ")")
                .find(".collapse").addClass("show")
                .closest("a").removeClass('collapsed')
                ;

            //attempt to highlight keyword - we need a plugin
            //var replaceKeywords = "<span style='background-color: #FFFDD0;'>" + keyword + "</span>";
            //var replaceKeywords = "<mark>" + keyword + "</mark>";
            // $(".card-body").each(function() {
            // $(".accordions")
            //     .find(".card:contains(" + keyword + ")")
            //     .find('.card-body')
            //     .each(function () {
            //             var text = $(this).text();
            //             text = text.replaceAll(keyword, replaceKeywords);
            //         $(this).html(text);
            //     }
            // );
        } else {
            $(".accordions .card").show();
            //back to collapsed view
            $(".accordions")
                .find(".collapse").removeClass("show")
                .closest("a").addClass('collapsed')
                ;
            
        }
    });
	
    function attachImage(editor) {
                editor.codemirror.replaceSelection(
                `\n$container-start\n$item:\n$height:200px\n$width:100%\n$align:center\n$container-end\n\n`
        );
    }

    // convert custom markdown to raw markdown
    function convertCustomMarkdown(custom_markdown, editor = false) {
        let alertRegex = /\$alert (.*?) (.*?)\n/g;
        let accordionRegex = /\$accordion-start([\s\S]*?)\$accordion-end/g;
        let accordionSearchRegex = /\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end/g;
        let panelRegex = /\$panel-start\n\$title:([\s\S]*?)\n\$body:([\s\S]*?)\$panel-end/g;
        let panelRegexClose = /\$panel-start \$close\n\$title:([\s\S]*?)\n\$body:([\s\S]*?)\$panel-end/g;
        
        let containerRegex = /\$container-start\n([\s\S]*?)\$container-end/g;
		let customContainerRegex = /\$item:([\s\S]*?)\n\$height:([\s\S]*?)\n\$width:([\s\S]*?)\n\$align:([\s\S]*?)\n/g;

		let imageHTML = '';	
			
        if (editor) {
                    containerRegex = /\$container-start<br>\n([\s\S*?])\$container-end/g;
					accordionRegex = /<p>\$accordion-start<br>\n([\s\S]*?)\$accordion-end<\/p>/g;
					accordionSearchRegex = /<p>\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end<\/p>/g;
					panelRegex = /\$panel-start<br>\n\$title:([\s\S]*?)<br>\n\$body:([\s\S]*?)\$panel-end<br>/g;
					customContainerRegex = /\$container-start<br>\n\$item:([\s\S]*?)<br>\n\$height:([\s\S]*?)<br>\n\$width:([\s\S]*?)<br>\n\$align:([\s\S]*?)<br>\n\$container-end<br>\n/g;
                    panelRegexClose = /\$panel-start \$close<br>\n\$title:([\s\S]*?)<br>\n\$body:([\s\S]*?)\$panel-end<br>/g;
				}
			

                custom_markdown = custom_markdown.replace(
                    containerRegex, (match, item) => {
                      
                        return `<div class="container"><p>${item}</p></div>`
                    }
                    
                );
			// convert container
			custom_markdown = custom_markdown.replace(customContainerRegex,
				(whole, item, height, width, align) => {
				
					return `<div class="row text-${align}">
										<div style="height:${height}; width:${width};"><p>${item}</p></div>
									</div>`
					}
			);
			
        // add alert infos
				custom_markdown = custom_markdown.replace(alertRegex, `<div class="alert alert-$1" role="alert"><p>$2</p></div>`);
          
        // add accordion
        custom_markdown = custom_markdown.replace(
            accordionRegex,
            `<div class="panel-group grey accordions mb-4">$1</div>`
        );

       

        // custom_markdown = custom_markdown.replace(accordionRegex,
        //     `<div class="panel-group grey" id="accordion" role="tablist" aria-multiselectable="true">$1</div>`
        // )

        custom_markdown = custom_markdown.replace(accordionSearchRegex, (match, placeholder) => {
            let place = "Search/Filter Results";
            if (placeholder.length > 1) {
                place = placeholder;
            }

            return `<div class="form-group mr-auto accordion-search">
                        <span class="fa fa-search"></span>
                        <input autofill="off" autocomplete="off" type="text" id="searchAccordion"
                        class="form-control form-control-sm accordion-search-input" placeholder="${place}">
                    </div>`;
        });

        // add accordion panels

        custom_markdown =  replaceVideoAudioEmbedWithHtml(custom_markdown);

        custom_markdown = custom_markdown.replace(
            panelRegex,
            (match, title, body, index) =>
                `<div id="accordion-${index}" class="accordion-item">
                    <div class="card">
                        <div class="card-header accordion-header">
                            <a class="card-link" data-toggle="collapse" href="#collapse-${index}"  aria-expanded="true">
                                ${title}
                            </a>
                        </div>
                        <div id="collapse-${index}" class="collapse show" data-parent="#accordion-${index}">
                            <div class="card-body"><p>${body}</p></div>
                        </div>
                    </div>
                </div>`
				);
			
        custom_markdown = custom_markdown.replace(
            panelRegexClose,
            (match, title, body, index) =>
                `<div id="accordion-${index}">
                    <div class="card">
                        <div class="card-header accordion-header">
                            <a class="card-link" data-toggle="collapse" href="#collapse-${index}">
                                ${title}
                            </a>
                        </div>
                        <div id="collapse-${index}" class="collapse" data-parent="#accordion-${index}">
                            <div class="card-body"><p>${body}</p></div>
                        </div>
                    </div>
                </div>`
                );    

        //keeping for reference
        // `<div class="panel panel-default">
        //             <div class="panel-heading" role="tab" id="faq_l${index}">
        //                 <h4 class="panel-title">
        //                     <a role="button" data-toggle="collapse" data-parent="#accordion" href="#faq${index}" aria-expanded="false" aria-controls="faq${index}">
        //                         ${title}
        //                     </a>
        //                 </h4>
        //             </div>
        //             <div id="faq${index}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="faq_l${index}">
        //                 <div class="panel-body">
        //                     ${body}
        //                 </div>
        //             </div>
        //         </div>`
			

        return custom_markdown;
    }

    function replaceTemporaryMarkdown(rawString) {
        let accordionList = [];
        let containerList = [];
    
        //replace accordion markdown with a temporary markdown
        let string = rawString.replace(/\$accordion-start([\s\S]*?)\$accordion-end/g, (_, rawAccordionString) => {
            const accordionString = convertCustomMarkdown(rawAccordionString);
            accordionList = [...accordionList, accordionString];
            return "{{-- ACCORDION --}}";
        });
    
        string = string.replace(/\$container-start([\s\S]*?)\$container-end/g, (whole, rawContainerString) => {
            const containerString = convertCustomMarkdown(whole);
            containerList = [...containerList, containerString];
            return "{{-- CONTAINER --}}";
        });
    
        return { string, accordionList, containerList };
            
		}
	
		// function extractContainer(rawString) {
		// 			let containerList = [];
		// 			const cString = rawString.replace(/\$container-start([\s\S]*?)\$container-end/g, (_, rawContainerString) => {
		// 					const containerString = convertCustomMarkdown(rawContainerString);
		// 					containerList = [...containerList, containerString];
		// 					return "{{-- CONTAINER --}}";
		// 			});

		// 			return { cString, containerList };
		// 	}

		//replace temporary markdowns
    function createTemporaryMarkdown(string, accordionList, containerList) {
			let accordionIndex = 0;
			let containerIndex = 0;

			//replace accordion
			string = string.replace(/\{\{-- ACCORDION --\}\}/g, () => {
					const accordionString = accordionList[accordionIndex];
					accordionIndex += 1;
					return accordionString;
			});
			
			//replace container
			string = string.replace(/\{\{-- CONTAINER --\}\}/g, () => {
            const containerString = containerList[containerIndex];
            containerIndex += 1;
            return containerString;
				});
			
			return string;
		}
	

	// convert editor markdown to HTML and display in #preview div
    function convertTextAreaToMarkdown(body) {
		let { string, accordionList, containerList } = replaceTemporaryMarkdown(body);

        // replace ![]() that are videos with video elements
        string = string.replace(/!\[(.*?)\]\((.*?)\)/g, (match, alt, src) => {
            const extensions = ["mp4", "mkv", "webm", "avi", "mpeg", "mp3"];
            const extension = src.split(".").pop();
            if (extensions.includes(extension)) {
                return `<div class="file-to-proxy" data-url="${src}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd";">${t('Loading media...')}</div>`;
            } else {
                return match;
            }
        });


        var html = mark_it_down.render(string);

        // add responsive images and tables	
        var fixed_html = html.replace(/<img/g, "<img class='img-fluid'");
        fixed_html = fixed_html.replace(/<table/g, "<table class='table table-hover' ");

    
        let alertRegex = /\$alert (.*?) (.*?)\n/g;
            fixed_html = fixed_html.replace(alertRegex, `<div class="alert alert-$1" role="alert"><p>$2</p></div>`);


        fixed_html = createTemporaryMarkdown(fixed_html, accordionList, containerList);
    
        
        return fixed_html
	}

    // user up vote clicked
    $(document).on("click", "#btnUpvote", function () {
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/vote",
            data: { doc_id: $("#doc_id").val(), vote_type: "upvote" },
        })
            .done(function (msg) {
                show_notification(msg, "success", true);
            })
            .fail(function (msg) {
                show_notification(msg.responseText, "danger");
            });
    });

    // user down vote clicked
    $(document).on("click", "#btnDownvote", function () {
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/vote",
            data: { doc_id: $("#doc_id").val(), vote_type: "downvote" },
        })
            .done(function (msg) {
                show_notification(msg, "success", true);
            })
            .fail(function (msg) {
                show_notification(msg.responseText, "danger");
            });
    });

    // Call to API to check if a permalink is available
    $("#validate_permalink").click(function () {
        if ($("#frm_kb_permalink").val() !== "") {
            $.ajax({
                method: "POST",
                url: $("#app_context").val() + "/api/validate_permalink",
                data: {
                    permalink: $("#frm_kb_permalink").val(),
                    doc_id: $("#frm_kb_id").val(),
                },
            })
                .done(function (msg) {
                    show_notification(msg, "success");
                })
                .fail(function (msg) {
                    show_notification(msg.responseText, "danger");
                });
        } else {
            show_notification("Please enter a permalink to validate", "danger");
        }
    });

    // generates a random permalink
    $("#generate_permalink").click(function () {
        var min = 100000;
        var max = 999999;
        var num = Math.floor(Math.random() * (max - min + 1)) + min;
        $("#frm_kb_permalink").val(num);
    });

    // function to slugify strings
    function slugify(str) {
        var $slug = "";
        var trimmed = $.trim(str);
        $slug = trimmed
            .replace(/[^a-z0-9-æøå]/gi, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "")
            .replace(/æ/gi, "ae")
            .replace(/ø/gi, "oe")
            .replace(/å/gi, "a");
        return $slug.toLowerCase();
    }

    // generates a permalink from title with form validation
    $("#frm_kb_title").change(function () {
        var title = $(this).val();
        if (title && title.length > 5) {
            $("#generate_permalink_from_title").removeClass("disabled");

            if($('#frm_kb_permalink').val() === '') {
                generatePermalinkFromTitle();
            }
        } else {
            $("#generate_permalink_from_title").addClass("disabled");
        }
    });

    //from frm_kb_title change event - purposely created this event to enable button slug on edit mode
    $("#generate_permalink_from_title").click(function () {
        generatePermalinkFromTitle()
    });

    function generatePermalinkFromTitle() {
        const title = $("#frm_kb_title").val();
        const id  = $('#frm_kb_id').val();

        if (title && title.length <= 5) {
            return;
        }

        const data = {title: title}
        if(id) { 
            data.id = id;
        }

        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/api/articles/generate-slug",
            data: data,
        })
            .done(function (resp) {
                $('#frm_kb_permalink').val(resp.slug);
            })
            .fail(function (resp) {
                show_notification(resp, "danger");
            });
    }

    // search button click event
    $("#btn_searchArticles").click(function (event) {
        if ($("#frm_search").val() === "") {
            show_notification("Please enter a search value", "danger");
            event.preventDefault();
        }
    });

    if ($("#input_notify_message").val() !== "") {
        // save values from inputs
        var message_val = $("#input_notify_message").val();
        var message_type_val = $("#input_notify_message_type").val();

        // clear inputs
        $("#input_notify_message").val("");
        $("#input_notify_message_type").val("");

        // alert
        show_notification(message_val, message_type_val, false);
    }

    
    $('.upload-media').css('maxWidth', $('#article-body').width() + 'px')
});

// Calls the API to delete a file
$(document).on("click", ".file_delete_confirm", function (e) {
    e.preventDefault();
    var fileId = $(this).attr("data-id");
    var filePath = $(this).attr("data-path");

    if (window.confirm(t("Are you sure you want to delete the file?"))) {
        $.ajax({
            method: "POST",
            url: $("#app_context").val() + "/file/delete",
            data: { img: filePath },
        })
            .done(function (msg) {
                $("#file-" + fileId).remove();
                show_notification(msg, "success");
            })
            .fail(function (msg) {
                show_notification(msg, "danger");
            });
    }
});

// show notification popup
function show_notification(msg, type, reload_page) {
    // defaults to false
    reload_page = reload_page || false;

    $("#notify_message").removeClass();
    $("#notify_message").addClass("notify_message-" + type);
    $("#notify_message").html(msg);
    $("#notify_message")
        .slideDown(600)
        .delay(1200)
        .slideUp(600, function () {
            if (reload_page === true) {
                location.reload();
            }
        });
}

function search_form(id) {
    $("form#" + id).submit();
}

function convertToSlug(text) {
    return text
        .toLowerCase()
        .replace(/ /g, "-")
        .replace(/[^\w-]+/g, "");
}

function headingSlug(text) {
    return text
        .toLowerCase()
        .replace(/(<([^>]+)>)/ig, '')
        .replace(/ /g, "-")
        .replace('&amp;', '')
        .replace(/[`~!@#$%^&*()_|+=?;:'",.<>\{\}\[\]\\\/]/g, "");
}

function addAlert(editor) {
    const text = editor.codemirror.getSelection() || "Insert text here!";
    editor.codemirror.replaceSelection(`\n$alert info ${text}\n\n`);
}

function addAccordion(editor) {
    editor.codemirror.replaceSelection(
        `\n$accordion-start\n$accordion-search-start\n$placeholder:\n$accordion-search-end\n$panel-start\n$title: Add panel title\n$body:\n\nAdd panel body\n\n$panel-end\n$accordion-end\n\n`
    );
}

function openCheatsheet() {
    //$('#markdownWiki').modal()
    showMarkDown();
}

function showMarkDown() {
	const htmlContents = `
		<button class="btn btn-sm btn-danger float-right pr-2 pl-2" onClick="(function(){
    swal.close();
		})()">
		x
		</button>
    <div class="row" style="font-family: 'Khula', sans-serif!important; font-size: 14px; margin: 8px;">
		
		<div class="col-12 text-center">
		<h4> ${t('Markdown Guide')} </h4>
		</div>

    <div class="col-12 text-left">
		<hr>
		<div class="row">
			<div class="col-2">
				<h5>${t('Emphasis')}</h5>
			</div>
			<div class="col-4 mt-1">
			<pre>
**<strong>${t('bold')}</strong>**
*<em>${t('italics')}</em>*
~~<strike>${t('strikethrough')}</strike>~~
			</pre>
			</div>

			<div class="col-2">
				<h5>${t('Lists')}</h5>
			</div>
			<div class="col-4 mt-1">
				<pre>
* ${t('Generic list item')}
* ${t('Generic list item')}
* ${t('Generic list item')}

1. ${t('Numbered list item')}
2. ${t('Numbered list item')}
3. ${t('Numbered list item')}
        </pre>
			</div>

			
		</div>
    <hr>
		<div class="row">
			<div class="col-2">
				<h5>${t('Headers')}</h5>
			</div>
			<div class="col-4 mt-1">
				<pre>
# ${t('Big header')}
## ${t('Medium header')}
### ${t('Small header')}
#### ${t('Tiny header')}
        </pre>
			</div>

			<div class="col-2">
				<h5>${t('Quotes')}</h5>
			</div>
			<div class="col-4 mt-1">
				<pre>
&gt; ${t('This is a quote.')}
&gt; ${t('It can span multiple lines!')}
				</pre>
			</div>
		</div>

		<hr>
		<div class="row">
			<div class="col-2">
				<h5>${t('Links')}</h5>
			</div>
			<div class="col-4 mt-2">
				<pre>
[${t('Link text')}](http://www.example.com)
				</pre>
			</div>

			<div class="col-2">
				<h5>${t('Images')}</h5>
			</div>
			<div class="col-4 mt-2">
				${t('<small>Need to upload an image? <a href="https://imgur.com/" target="_blank">Imgur</a> has a great interface.</small>')}
				<pre>
![](http://www.example.com/image.jpg)</pre>
				</pre>
			</div>

		</div>

		<hr>
		<div class="row">
			<div class="col-2">
				<h5>${t('Tables')}</h5>
			</div>
			<div class="col-4 mt-1">
			<pre>
| Column 1 | Column 2 | Column 3 |
| -------- | -------- | -------- |
| John     | Doe      | ${t('Male')}     |
| Mary     | Smith    | ${t('Female')}   |

<em>${t('Or without aligning the columns...')}</em>

| Column 1 | Column 2 | Column 3 |
| -------- | -------- | -------- |
| John | Doe | ${t('Male')} |
| Mary | Smith | ${t('Female')} |
        </pre>
			</div>

			<div class="col-2">
				<h5>${t('Displaying code')}</h5>
			</div>
			<div class="col-4 mt-1">
				<pre>
'var example = "hello!";'
<em>${t('Or spanning multiple lines...')}</em>

---
var example = "hello!";
alert(example);
---
        </pre>
			</div>

		</div>


		<hr>
		<div class="row">
			<div class="col-2">
				<h5>${t('Alerts')}</h5>
			</div>
			<div class="col-10">
				<div class="row">
					<div class="col-6">
					<pre>
$alert ${t('success This is a success alert')}
        	</pre>
					</div>
					<div class="col-6">
						<div class="alert alert-success" role="alert" style="margin-bottom:5px">
							${t('This is a success alert')}
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-6">
					<pre>
$alert ${t('danger This is a danger alert')}
        	</pre>
					</div>
					<div class="col-6">
						<div class="alert alert-danger" role="alert" style="margin-bottom:5px">
							${t('This is a danger alert')}
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-6">
					<pre>
$alert ${t('warning This is a warning alert')}
        	</pre>
					</div>
					<div class="col-6">
						<div class="alert alert-warning" role="alert" style="margin-bottom:5px">
							${t('This is a warning alert')}
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-6">
					<pre>
$alert ${t('info This is an info alert')}
        	</pre>
					</div>
					<div class="col-6">
						<div class="alert alert-info" role="alert" style="margin-bottom:5px">
							${t('This is an info alert')}
						</div>
					</div>
				</div>
			
			</div>
		</div>

		<hr>
		<div class="row">
			<div class="col-2">
				 <h5>${t('Accordion Panels')}</h5>
			</div>
			<div class="col-10">
				<div class="row">
					<div class="col-6">
						<pre>
$accordion-start

$accordion-search-start
$placeholder:
$accordion-search-end

$panel-start
$title: ${t('Panel 1')}
$body:

${t('Panel 1 content')}

$panel-end
$panel-start
$title: ${t('Panel 2')}
$body:

${t('Panel 2 content')}

$panel-end
$accordion-end
        		</pre>
					</div>
					<div class="col-6">
						<div class="panel-group grey accordions mb-4">
							<div class="form-group mr-auto accordion-search">
									<span class="fa fa-search"></span>
									<input autofill="off" autocomplete="off" type="text" id="searchAccordion"
									class="form-control form-control-sm accordion-search-input" placeholder="${t('Placeholder Text')}">
							</div>
							<div id="accordion-1">
									<div class="card">
											<div class="card-header accordion-header">
													<a class="card-link" data-toggle="collapse" href="#collapse-1">
															${t('Panel 1')}
													</a>
											</div>
											<div id="collapse-1" class="collapse show" data-parent="#accordion-1">
													<div class="card-body">
															<p>
																	${t('Panel 1 Contents')}
															</p>
													</div>
											</div>
									</div>
							</div>
							<div id="accordion-2">
									<div class="card">
											<div class="card-header accordion-header">
													<a class="card-link" data-toggle="collapse" href="#collapse-2">
															${t('Panel 2')}
													</a>
											</div>
											<div id="collapse-2" class="collapse show" data-parent="#accordion-2">
													<div class="card-body">
															<p>
																	${t('Panel 2 Contents')}
															</p>
													</div>
											</div>
									</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<hr>
		<div class="row">
			<div class="col-2">
				 <h5>${t('Custom Container')}</h5>
			</div>
			<div class="col-10">
				<div class="row">
					<div class="col-6">
						<pre>
$container-start
$item:![${t('Boxing Gloves')}](https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80)
$height:200px
$width:100%
$align:center
$container-end
        		</pre>
					</div>
					<div class="col-6">
						<div class="row text-center">
							<div style="height:200px; width:100%;">
								<img src="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80" alt="Boxing Gloves" class="img-fluid h-100">
							</div>
						</div>
					</div>
					
				</div>
			</div>
		</div>
   
    </div>

    `;

    Swal.fire({
			//title: "Markdown Guide",
			html: htmlContents,
				width: 1100,
			icon: "",
				//showConfirmButton: false,
			confirmButtonText: t("Close"),
			confirmButtonColor: '#3085d6',
			didOpen: function () {
					window.top.postMessage("modalShow", "*");
			},
			didClose: function () {
					window.top.postMessage("modalHide", "*");
			},
			backdrop: `
			rgba(0,0,0,0.5)
		`,
        showClass: {
            backdrop: "swal2-noanimation", // disable backdrop animation
            popup: "", // disable popup animation
            icon: "", // disable icon animation
        },
        hideClass: {
            popup: "", // disable popup fade-out animation
        },
    });
}

async function renderVideoJsFiles() {
    // wait for translator to initialize
    if (translatorInitializing) {
        return setTimeout(renderVideoJsFiles, 200);
    }
    
    $('.file-to-proxy').each(renderVideoJsFile);
}

async function renderVideoJsFile(index, element) {
    const { url } = $(element).data();
    if (!url.includes('secure-upload')) {
        return renderVideoJsElement(url);
    }
    
    const newUrl = url.replace('/private', '');

    try {
        const { status, progress } = await $.ajax({ url: newUrl, method: 'GET' });
        if (status === 'error') return setVideoJsError(url, '<p>Failed to process video</p><small>Please upload another file and try again later</small>');
        if (['to process', 'processing'].includes(status)) {
            setVideoJsError(url, `<p>Processing ${progress}%</p> <small>You can save this file and close this tab. Feel free to return later to view the video/audio once the processing is complete.</small>`);
            return setTimeout(() => renderVideoJsFile(index, element), 1000 * 60);
        }
    } catch (error) {
        console.error(error);
        if (error.status === 404) return setVideoJsError(url, '<p>File not found.</p><small>Please check URL and try again.</small>');
        return setVideoJsError(url, '<p>Something went wrong.</p><small>Please try again later.<small>');
    }

    renderVideoJsElement(url);
}

function renderVideoJsElement(src) {
    const [posterUrl] = src.replace('/video/', '/poster/').split('.');
    const element = $(document).find(`.file-to-proxy[data-url="${src}"]`);

    $(element).removeClass('file-to-proxy').addClass('media-file-loaded');
    $(element).css('padding', '0');

    let player = $(`<video controls crossorigin playsinline data-poster="${posterUrl}" style="width: 100%;" />`)
    const isAudio = src.includes('/audio/');

    if (isAudio) {
        player = $(`<audio controls crossorigin playsinline />`)
    }

    $(element).html(player);
    streamPlayer(player, src);
}

function setVideoJsError(url, msg = 'Error loading video') {
    const element = $(`.file-to-proxy[data-url="${url}"]`);
    $(element).html(msg);
    $(element).removeClass('file-to-proxy').addClass('media-file-error');
}

function uploadEditorMediaDialog(editor) {
    let filesToUpload = [];
    const uploadedFiles = [];
    const html = document.createElement('div');
    $(html).addClass('upload-dialog');
    $(html).append(`
        <fieldset>
            <legend>Upload Media</legend>
            <p class="mb-0"><b>Supported file types:</b> avi, bmp, gif, jpg, mkv, mp3, mp4, png, webm, webp</p>
            <em><small>All other file types will be uploaded without preview functionality.</small></em>
            <div class="custom-dz mt-1">
                <p>${t('Drop files here or click this to upload')}</p>
            </div>
            <form id="upload-form" enctype="multipart/form-data" class="d-none">
                <input type="file" name="file" accept=".avi,.bmp,.gif,.jpg,.mkv,.mp3,.mp4,.png,.webm,.webp" multiple />
                <button type="submit">Upload</button>
            </form>
            <div class="upload-status-list d-flex flex-column gap-1"></div>
        </fieldset>
    `);

    $(html).on('click', '.custom-dz', () => {
        $('#upload-form input[name=file]').trigger('click');
    });

    $(html).on('dragenter', '.custom-dz', (e) => e.preventDefault());
    $(html).on('dragover', '.custom-dz', (e) => e.preventDefault());

    $(html).on('drop', '.custom-dz', (e) => {
        e.preventDefault();
        const files = e.originalEvent.dataTransfer.files;
        handleFileDrop(files);
    });

    $(html).on('change', '#upload-form input[name=file]', (e) => {
        handleFileDrop(e.target.files);
    });
    
    const updateUploadStatus = (name) => () => {
        const xhr = new window.XMLHttpRequest();

        xhr.upload.onprogress = function (evt) {
            if (!evt.lengthComputable) return;
            const percentComplete = Math.floor((evt.loaded / evt.total) * 100);
            $(html).find(`.upload-status-item[data-name="${name}"]`).find('.status').text(`${percentComplete}%`);
        };

        return xhr;
    };

    const setErrorStatus = (name) => {
        $(html).find(`.upload-status-item[data-name="${name}"]`).find('.status').text('Error');
    };

    function handleFileDrop(filelist) {
        $('.swal2-confirm').prop('disabled', true);
        
        $.each(filelist, function(index, file) {
            filesToUpload.push(file);
            renderFile(file);
            uploadFile(file, updateUploadStatus(file.name)).then((response) => {
                filesToUpload = filesToUpload.filter((f) => f.name !== file.name);
                if (!response[0]) return setErrorStatus(file.name);
                if(file.type.includes('image')){
                    uploadedFiles.push({ name: file.name, url: response[1], dimensions: { width: file.width, height: file.height } });
                } else {
                    uploadedFiles.push({ name: file.name, url: response[1] });
                }

                if (filesToUpload.length === 0) $('.swal2-confirm').prop('disabled', false);
            });
        });
    }

    function renderFile(file) {
        const reader = new FileReader();

        reader.onload = function (entry) {
           
            const icon = file.type.includes('image')
                ? 'fa fa-image'
                : file.type.includes('video')
                ? 'fa fa-video'
                : 'fa fa-file';

            $(html).find('.upload-status-list').append(`
                <div class="upload-status-item" data-name="${file.name}">
                    <span class="d-flex gap-1 align-center">
                        <i class="${icon}"></i>
                        <span>${file.name}</span>
                    </span>
                    <span class="d-flex gap-1 align-center">
                        <span class="status" style="padding-top: 6px; padding-bottom: 6px;">0%</span>
                    </span>
                </div>
            `);

            if(file.type.includes('image')){
                let image = new Image(); 
                image.src = entry.target.result;
                image.onload = function() {
                    file.width = this.width
                    file.height = this.height;
                };
            }
        };
    
        reader.readAsDataURL(file);
        
    }

    appSwal.fire({ html, width: 800 }).then(({ isConfirmed }) => {
        if (!isConfirmed) return;
        const uploadedFilesString = uploadedFiles.map((f) => {
            if(f.dimensions){
                return `![${f.name}](${f.url} =${f.dimensions.width}x${f.dimensions.height})`;
            } else {
                return `![${f.name}](${f.url})`;
            }
        }).join('\n\n');
        editor.codemirror.replaceSelection(uploadedFilesString);
    });
}

function openLinkDialog(editor) {
    const html = document.createElement('div');
    $(html).addClass('upload-dialog');
    $(html).append(`
        <fieldset>
            <legend>${t('Add Link')}</legend>
            <div class="form-group">
                <input type="text" name="url" class="form-control input-normal" required placeholder="https://portal.hub.gymsystems.co/secure-uploader...">
            </div>

            <div class="div">
                <div class="checkbox">
                    <label style="margin: 0">
                        <input type="checkbox" name="is-media" style="transform: translateY(2px);">
                        <small>${t('Is Playable Media (Image, Video, Audio)')}</small>
                    </label>
                </div>
            </div>
        </fieldset>
    `);

    $(html).find('input[name=url]').on('input', (e) => {
        const value = $(e.target).val();
        const extension = value.split('.').pop();
        const isMedia = ['avi', 'bmp', 'gif', 'jpg', 'mkv', 'mp3', 'mp4', 'png', 'webm', 'webp']
            .includes(extension);

        $(html).find('input[name=is-media]').prop('checked', isMedia);

    });

    appSwal.fire({ html }).then(({ isConfirmed }) => {
        if (!isConfirmed) return;
        const url = $(html).find('input[name=url]').val();
        const isMedia = $(html).find('input[name=is-media]').is(':checked');
        editor.codemirror.replaceSelection(isMedia ? `![](${url})` : `[${url}](${url})`);
    });
}

function isAppleDevice() {
    return /Macintosh|iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
}

function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

const defaultPlyrOptions = {
    captions: { active: true, update: true, language: 'en' },
    quality: {
        default: isMobileDevice() ? 720 : 1080,
        options: [1080, 720, 360],
        forced: true,
    }
};

function streamPlayer(video, src) {
    if (isAppleDevice()) return hlsPlayer(video, src);
    dashPlayer($(video)[0], src);
}

function dashPlayer(video, src) {
    let firstLoad = true;
    const dash = dashjs.MediaPlayer().create();
    const parent = $(video).parent();

    const options = { ...defaultPlyrOptions };
    options.quality.onChange = function (quality) {
        if (firstLoad) return;
        const t = options.quality.options.findIndex((q) => q === quality);
        dash.setQualityFor("video", t);
    };

    const player = new Plyr($(video)[0], options);
    parent.find('.plyr').wrap('<div class="card my-3"></div>');

    player.on('play', () => {
        if (!firstLoad) return;
        firstLoad = false;
        dash.initialize($(video)[0], src, false);
    });

}

function hlsPlayer(video, src) {
    let firstLoad = true;
    const options = { ...defaultPlyrOptions };
    const parent = $(video).parent();

    if (!Hls.isSupported()) {
        $(video)[0].src = src;
        options.quality = null;
        return new Plyr($(video)[0], options);
    }

    const hls = new Hls();

    options.quality.onChange = function (quality) {
        hls.levels.forEach((level, levelIndex) => {
            if (level.height !== quality) return;
            hls.currentLevel = levelIndex;
        });
    };

    const player = new Plyr($(video)[0], options);
    parent.find('.plyr').wrap('<div class="card my-3"></div>');
    
    player.on('play', () => {
        if (!firstLoad) return;
        firstLoad = false;
        hls.loadSource(src);
        hls.attachMedia($(video)[0]);
    });
}

