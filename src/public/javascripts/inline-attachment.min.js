!function(e,t){"use strict";var a=function(e,t){this.settings=a.util.merge(e,a.defaults),this.editor=t,this.filenameTag="{filename}",this.lastValue=null};a.editors={},a.util={merge:function(){for(var e={},t=arguments.length-1;t>=0;t--){var a=arguments[t];for(var i in a)a.hasOwnProperty(i)&&(e[i]=a[i])}return e},appendInItsOwnLine:function(e,t){return(e+"\n\n[[D]]"+t).replace(/(\n{2,})\[\[D\]\]/,"\n\n").replace(/^(\n*)/,"")},insertTextAtCursor:function(t,a){var i,n=t.scrollTop,o=0,s=!1;t.selectionStart||"0"===t.selectionStart?s="ff":e.selection&&(s="ie"),"ie"===s?(t.focus(),(i=e.selection.createRange()).moveStart("character",-t.value.length),o=i.text.length):"ff"===s&&(o=t.selectionStart);var r=t.value.substring(0,o),l=t.value.substring(o,t.value.length);t.value=r+a+l,o+=a.length,"ie"===s?(t.focus(),(i=e.selection.createRange()).moveStart("character",-t.value.length),i.moveStart("character",o),i.moveEnd("character",0),i.select()):"ff"===s&&(t.selectionStart=o,t.selectionEnd=o,t.focus()),t.scrollTop=n}},a.defaults={uploadUrl:"upload_attachment.php",uploadMethod:"POST",uploadFieldName:"file",defaultExtension:"png",jsonFieldName:"filename",allowedTypes:["image/jpeg","image/png","image/jpg","image/gif","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/pdf"],progressText:"![Uploading file...]()",urlText:"![file]({filename})",errorText:"Error uploading file",extraParams:{},extraHeaders:{},beforeFileUpload:function(){return!0},onFileReceived:function(){},onFileUploadResponse:function(){return!0},onFileUploadError:function(){return!0},onFileUploaded:function(){}},a.prototype.uploadFile=function(e){var t=this,a=new FormData,i=new XMLHttpRequest,n=this.settings,o=n.defaultExtension||n.defualtExtension;if("function"==typeof n.setupFormData&&n.setupFormData(a,e),e.name){var s=e.name.match(/\.(.+)$/);s&&(o=s[1])}var r="image-"+Date.now()+"."+o;if("function"==typeof n.remoteFilename&&(r=n.remoteFilename(e)),a.append(n.uploadFieldName,e,r),"object"==typeof n.extraParams)for(var l in n.extraParams)n.extraParams.hasOwnProperty(l)&&a.append(l,n.extraParams[l]);if(i.open("POST",n.uploadUrl),"object"==typeof n.extraHeaders)for(var p in n.extraHeaders)n.extraHeaders.hasOwnProperty(p)&&i.setRequestHeader(p,n.extraHeaders[p]);return i.onload=function(){200===i.status||201===i.status?t.onFileUploadResponse(i):t.onFileUploadError(i)},!1!==n.beforeFileUpload(i)&&i.send(a),i},a.prototype.isFileAllowed=function(e){return"string"!==e.kind&&(0===this.settings.allowedTypes.indexOf("*")||this.settings.allowedTypes.indexOf(e.type)>=0)},a.prototype.getFileType=function(e){var t="";switch(e.substr(e.lastIndexOf(".")+1).toLowerCase()){case"pdf":case"doc":case"docx":case"xls":case"xlsx":t="file";break;default:t="image"}return t},a.prototype.onFileUploadResponse=function(e){if(!1!==this.settings.onFileUploadResponse.call(this,e)){var t=JSON.parse(e.responseText),a=t[this.settings.jsonFieldName];if(t&&a){var i,n=this.settings.urlText;"file"==this.getFileType(a)&&(n=this.settings.fileText),i="function"==typeof n?n.call(this,a,t):n.replace(this.filenameTag,a);var o=this.editor.getValue().replace(this.lastValue,i);this.editor.setValue(o),this.settings.onFileUploaded.call(this,a)}}},a.prototype.onFileUploadError=function(e){if(!1!==this.settings.onFileUploadError.call(this,e)){var t=this.editor.getValue().replace(this.lastValue,"");this.editor.setValue(t)}},a.prototype.onFileInserted=function(e){!1!==this.settings.onFileReceived.call(this,e)&&(this.lastValue=this.settings.progressText,this.editor.insertValue(this.lastValue))},a.prototype.onPaste=function(e){var t,a=!1,i=e.clipboardData;if("object"==typeof i){t=i.items||i.files||[];for(var n=0;n<t.length;n++){var o=t[n];this.isFileAllowed(o)&&(a=!0,this.onFileInserted(o.getAsFile()),this.uploadFile(o.getAsFile()))}}return a&&e.preventDefault(),a},a.prototype.onDrop=function(e){for(var t=!1,a=0;a<e.dataTransfer.files.length;a++){var i=e.dataTransfer.files[a];this.isFileAllowed(i)&&(t=!0,this.onFileInserted(i),this.uploadFile(i))}return t},t.inlineAttachment=a}(document,window);