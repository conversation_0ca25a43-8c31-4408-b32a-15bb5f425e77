"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var translations = [];
var defaultLanguage = 'en';
var init = function (_jsonFile, callback) {
    return __awaiter(this, void 0, void 0, function* () {
        const response = yield fetch(_jsonFile);
        const data = yield response.json();
        translations = data;
        defaultLanguage = yield callback();
    });
};
var t = function (_phrase) {
    var phrase = translations.find(function (p) {
        return p.en.toLowerCase() === _phrase.toLowerCase();
    });
    if (phrase === undefined) {
        return _phrase;
    }

    return phrase[defaultLanguage] === undefined
        ? _phrase
        : phrase[defaultLanguage];
};

let translatorInitializing = false;


// Localisation initialization...
const getDefaultLanguage = function () {
    return $('#user_lang').val();
};

const defineMomentLocale = async () => {
    moment.defineLocale(getDefaultLanguage(), {
        months: [
            t('January'),
            t('February'),
            t('March'),
            t('April'),
            t('May'),
            t('June'),
            t('July'),
            t('August'),
            t('September'),
            t('October'),
            t('November'),
            t('December'),
        ],
        monthsShort: [
            t('Jan'),
            t('Feb'),
            t('Mar'),
            t('Apr'),
            t('May'),
            t('Jun'),
            t('Jul'),
            t('Aug'),
            t('Sep'),
            t('Oct'),
            t('Nov'),
            t('Dec'),
        ],
        weekdays: [t('Sunday'), t('Monday'), t('Tuesday'), t('Wednesday'), t('Thursday'), t('Friday'), t('Saturday')],
        weekdaysShort: [t("Sun"), t("Mon"), t("Tue"), t("Wed"), t("Thu"), t("Fri"), t("Sat")],
        weekdaysMin: [t("Su"), t("Mo"), t("Tu"), t("We"), t("Th"), t("Fr"), t("Sa")],
        meridiem: function (hour, minute, isLowercase) {
            if (hour < 12) {
                return t('AM')
            } else {
                return t('PM')
            }
        }
    });

    moment().locale(getDefaultLanguage())
}


const initTranslator = async function () {
    translatorInitializing = true;
    if(getDefaultLanguage() === 'en' || getDefaultLanguage() === undefined || getDefaultLanguage() === ''){
        return translatorInitializing = false;
    }
    await init($('#app_context').val()+'/translations/' + getDefaultLanguage() + '.json', getDefaultLanguage);

    defineMomentLocale()
    translatorInitializing = false;
}
