
(function ($) {
  'use strict';

  var defaultOptions = {
    defaultType: 'info',
  };

  var CONFIRM_EVENT = 'tbwconfirm',
    CANCEL_EVENT = 'tbwcancel';

  $.extend(true, $.trumbowyg, {
    langs: {
      // jshint camelcase:false
      en: {
        alert: 'Alert',
        file: 'File',
        uploadError: 'Error'
      },
      az: {
        upload: 'Yüklə',
        file: 'Fayl',
        uploadError: 'Xəta'
      },
      by: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Памылка'
      },
      ca: {
        upload: 'Pujar fitxer',
        file: 'Fitxer',
        uploadError: 'Error'
      },
      cs: {
        upload: 'Nahrát obrázek',
        file: 'Soubor',
        uploadError: 'Chyba'
      },
      da: {
        upload: 'Upload',
        file: 'Fil',
        uploadError: 'Fejl'
      },
      de: {
        upload: 'Hochladen',
        file: 'Datei',
        uploadError: 'Fehler'
      },
      es: {
        upload: 'Subir archivo',
        file: 'Archivo',
        uploadError: 'Error'
      },
      et: {
        upload: 'Lae üles',
        file: 'Fail',
        uploadError: 'Viga'
      },
      fr: {
        upload: 'Envoi',
        file: 'Fichier',
        uploadError: 'Erreur'
      },
      hu: {
        upload: 'Feltöltés',
        file: 'Fájl',
        uploadError: 'Hiba'
      },
      ja: {
        upload: 'アップロード',
        file: 'ファイル',
        uploadError: 'エラー'
      },
      ko: {
        upload: '그림 올리기',
        file: '파일',
        uploadError: '에러'
      },
      pt_br: {
        upload: 'Enviar do local',
        file: 'Arquivo',
        uploadError: 'Erro'
      },
      ru: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Ошибка'
      },
      sl: {
        upload: 'Naloži datoteko',
        file: 'Datoteka',
        uploadError: 'Napaka'
      },
      sk: {
        upload: 'Nahrať',
        file: 'Súbor',
        uploadError: 'Chyba'
      },
      tr: {
        upload: 'Yükle',
        file: 'Dosya',
        uploadError: 'Hata'
      },
      zh_cn: {
        upload: '上传',
        file: '文件',
        uploadError: '错误'
      },
      zh_tw: {
        upload: '上傳',
        file: '文件',
        uploadError: '錯誤'
      },
    },
    // jshint camelcase:true

    plugins: {
      alert: {
        init: function (trumbowyg) {
          trumbowyg.o.plugins.custom_alert = $.extend(true, {}, defaultOptions, trumbowyg.o.plugins.custom_alert || {});

          var btnDef = {
            fn: function () {
              var documentSelection = trumbowyg.doc.getSelection(),
                //selectedRange = documentSelection.getRangeAt(0),
                node = documentSelection.focusNode,
                text,
                color = trumbowyg.o.plugins.custom_alert.defaultType;
              if (node && node.nodeName === '#text') {
                const tag = node.parentNode.parentNode
                color = $(tag).attr('class').match(/alert-(\w+)/)[1];
                text = $(tag).text();
                var range = trumbowyg.doc.createRange();
                range.selectNode(tag);
                documentSelection.removeAllRanges();
                documentSelection.addRange(range);
              }

              trumbowyg.saveRange();

              var fields = {
                type: {
                  type: 'select',
                  label: 'Color',
                  value: color,
                  required: true,
                  options: {
                    primary: 'Blue',
                    secondary: 'Grey',
                    success: 'Green',
                    danger: 'Red',
                    warning: 'Yellow',
                    info: 'Light Blue',
                    light: 'White',
                    dark: 'Dark'
                  }
                },

                content: {
                  type: 'text',
                  label: 'Content',
                  required: true,
                  value: text
                }
              };

              openModalInsert(
                trumbowyg,
                // Title
                "Alert",
                // Fields
                fields,
                // Callback
                function (values) {
                  try {

                    var html = $(`<div class="alert alert-${values.type}" role="alert"><p>${values.content}</p></div><p><br /></p>`)
                    trumbowyg.range.deleteContents();
                    trumbowyg.range.insertNode(html[0]);
                    trumbowyg.syncCode();
                    trumbowyg.$c.trigger('tbwchange');

                    return true;
                  } catch (err) {
                    console.log(err);
                  }
                }
              );
            },
            text: ' ',
            title: 'Alert',
            hasIcon: false,
            class: 'fa fa-exclamation-triangle'
          };



          //buildButtonIcon();
          trumbowyg.addBtnDef('alert', btnDef);
        },
        tagHandler: function (element, trumbowyg) {

          try {
            const tag = $(element).closest('div.alert');

            if (tag.length > 0) {
              console.log('asdfadfs')
              return ['alert']
            }
          } catch (err) {
            console.log(err)
          }
        },
        destroy: function (trumbowyg) {
        }
      }
    }
  });

  // Pre-formatted build and management modal
  const openModalInsert = (t, title, fields, cmd) => {
    var prefix = t.o.prefix,
      lg = t.lang,
      html = '',
      idPrefix = prefix + 'form-' + Date.now() + '-';

    $.each(fields, function (fieldName, field) {
      var l = field.label || fieldName,
        n = field.name || fieldName,
        a = field.attributes || {},
        fieldId = idPrefix + fieldName;

      var attr = Object.keys(a).map(function (prop) {
        return prop + '="' + a[prop] + '"';
      }).join(' ');

      if (typeof field.type === 'function') {
        if (!field.name) {
          field.name = n;
        }

        html += field.type(field, fieldId, prefix, lg);

        return;
      }

      html += '<div class="' + prefix + 'input-row">';
      html += '<div class="' + prefix + 'input-infos"><label for="' + fieldId + '"><span>' + (lg[l] ? lg[l] : l) + '</span></label></div>';
      html += '<div class="' + prefix + 'input-html">';

      if ($.isPlainObject(field.options)) {
        html += '<select name="' + n + '">';
        html += Object.keys(field.options).map((optionValue) => {
          return '<option value="' + optionValue + '" ' + (optionValue === field.value ? 'selected' : '') + '>' + field.options[optionValue] + '</option>';
        }).join('');
        html += '</select>';
      } else {
        html += '<input id="' + fieldId + '" type="' + (field.type || 'text') + '" name="' + n + '" ' + attr;
        html += (field.type === 'checkbox' && field.value ? ' checked="checked"' : '') + ' value="' + (field.value || '').replace(/"/g, '&quot;') + '">';
      }

      html += '</div></div>';
    });

    return t.openModal(title, html)
      .on(CONFIRM_EVENT, function () {
        var $form = $('form', $(this)),
          valid = true,
          values = {};
        console.log('confirm', $form)
        $.each(fields, function (fieldName, field) {
          var n = field.name || fieldName;

          var $field = $(':input[name="' + n + '"], select[name="' + n + '"]', $form),
            inputType = field.type;
          console.log('field', $field)
          switch (inputType.toLowerCase()) {
            case 'checkbox':
              values[n] = $field.is(':checked');
              break;
            case 'radio':
              values[n] = $field.filter(':checked').val();
              break;
            default:
              values[n] = $.trim($field.val());
              break;
          }
          // Validate value
          if (field.required && values[n] === '') {
            valid = false;
            t.addErrorOnModalField($field, t.lang.required);
          } else if (field.pattern && !field.pattern.test(values[n])) {
            valid = false;
            t.addErrorOnModalField($field, field.patternError);
          }
        });
        console.log(values)
        console.log(valid)
        if (valid) {
          t.restoreRange();

          if (cmd(values, fields)) {
            t.syncCode();
            t.$c.trigger('tbwchange');
            t.closeModal();
            $(this).off(CONFIRM_EVENT);
          }
        }
      })
      .one(CANCEL_EVENT, function () {
        $(this).off(CONFIRM_EVENT);
        t.closeModal();
      });
  }

})(jQuery);