
.val-error{
	color: #d9534f;
}

@media (max-width: 979px) {
	body {
		padding-bottom: 20px;
	}

    /* .divArticleContainer {
        height: 400px;
        overflow: auto;
    } */

	.view_count, #preview-wrapper {
		display: none;
	}

    #editor-wrapper {
        position: relative;
    }

    .CodeMirror {
        max-height: 450px;
    }

    /* .permalink_buttons{
        padding-top: 15px;
    } */

    .input-lg {
        height: 50px;
        padding-left: 15px;
        font-size: 16px;
        line-height: 1.2;
        border-radius: 3px;
    }

    /* .btn-lg, .btn-group-lg>.btn {
        height: 50px;
        padding-left: 0px 5px;
        font-size: 16px;
        line-height: .8;
        border-radius: 3px;
    } */
}

html, body {
    font-size: 16px;
    background-color: #f8fafb;
}

html{
    position: relative;
    min-height: 100%;
}

.hljs{
    background: transparent;
}

#header {
    height: 50px;
}

.brand-image{
    padding: 12px 10px 0 30px;
}

.navbar-brand{
    padding-left: 30px;
}

.view_count{
    font-size: 14px;
}

.list-group-heading{
    color: #2c3e50;
    background-color: #ecf0f1;
    height: 45px;
}

.list-group-sidemenu li{
    background-color: transparent;
    border: none;
    padding-left: 0px;
    padding-right: 0px;
}

#searchResult{
    right: -2px;
    left: 0px;
    min-height: 200px;
    position: absolute;
    padding-top: 5px;
    z-index: 1000;
}

.searchResultList li{
    background-color: #ecf0f1;
    border: 1px solid #dce4ec;
}

.CodeMirror {
    height: calc(100vh - 110px - 175px - 130px) !important;
    /* height: calc(100vh - 110px - 175px - 70px) !important; */
    overflow: hidden;
}

.btnSettingsUpdate{
    padding-top: 25px;
}

#editor-wrapper-suggest > .CodeMirror{
    height: calc(100vh - 110px - 175px - 110px) !important;
    overflow: auto;
}

.editor-toolbar, .CodeMirror{
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.editor-toolbar{
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-bottom: 0px;
}

.CodeMirror{
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

#preview {
    flex-grow: 1;
    overflow: auto;
}

.suggest-preview{
    height: calc(100vh - 110px - 235px) !important;
}

.show_footer{
    margin-bottom: 60px;
}

.tokenfield .token .close{
    color: black;
}

.search_bar{
    margin-top: 10px;
}

.dropup-button{
    height: 34px;
    padding: 0px;
    color: white !important;
}

.dropdown-menu>li>a{
    padding-top: 5px !important;
}

.pad-bottom{
    padding-bottom: 25px;
}

.keywords{
    padding-bottom: 30px;
    padding-top: 20px;
    font-size: 19px;
}

.file-form{
    padding-left: 15px;
    padding-right: 15px;
}

.dropup-list{
    padding-bottom: 5px;
    margin-right: 10px;
    margin-left: 10px;
    margin-top: 10px;
}

#notify_message{
    position: fixed;
    display: none;
    z-index: 9999;
    padding-top: 10px;
    height: 50px;
    bottom: 0px;
    width: 100%;
    text-align: center;
    font-size: 22px;
    color: white;
}

.notify_message-success{
    background-color: #18bc9c;
}

.notify_message-danger{
    background-color: #e74c3c;
}

.popover-title{
    background-color: #e74c3c;
    color: white;
}

.bg-danger, .bg-warning, .bg-primary, .bg-success, .bg-info{
    color: #ffffff;
}

.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 60px;
    text-align: center;
    padding-top: 15px;
    margin-top: 35px;
    background-color: #f5f5f5;
}

.btn-file {
    position: relative;
    overflow: hidden;
}
.btn-file input[type=file] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    font-size: 100px;
    text-align: right;
    filter: alpha(opacity=0);
    opacity: 0;
    outline: none;
    background: white;
    cursor: inherit;
    display: block;
}

#preview-wrapper {
    height: 100%;
    margin: 0px;
    padding-bottom: 50px;
    display: flex;
    flex-direction: column;
}
