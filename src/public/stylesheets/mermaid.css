/* Flowchart variables */
/* Sequence Diagram variables */
/* Gantt chart variables */
.mermaid .label {
  color: white;
}
.node rect,
.node circle,
.node polygon {
  fill: #d62828;
  stroke: #d62828;
  stroke-width: 1px;
}
.edgePath .path {
  stroke: #333333;
}
.cluster rect {
  fill: #ffffde !important;
  rx: 4 !important;
  stroke: #aaaa33 !important;
  stroke-width: 1px !important;
}
.cluster text {
  fill: white;
}
.actor {
  stroke: #d62828;
  fill: #ececff;
}
text.actor {
  fill: white;
  stroke: none;
}
.actor-line {
  stroke: grey;
}
.messageLine0 {
  stroke-width: 1.5;
  stroke-dasharray: "2 2";
  marker-end: "url(#arrowhead)";
  stroke: #333333;
}
.messageLine1 {
  stroke-width: 1.5;
  stroke-dasharray: "2 2";
  stroke: #333333;
}
#arrowhead {
  fill: #333333;
}
#crosshead path {
  fill: #333333 !important;
  stroke: #333333 !important;
}
.messageText {
  fill: #333333;
  stroke: none;
}
.labelBox {
  stroke: #d62828;
  fill: #d62828;
}
.labelText {
  fill: white;
  stroke: none;
}
.loopText {
  fill: white;
  stroke: none;
}
.loopLine {
  stroke-width: 2;
  stroke-dasharray: "2 2";
  marker-end: "url(#arrowhead)";
  stroke: #d62828;
}
.note {
  stroke: #337ab7;
  fill: #337ab7;
}
.noteText {
  fill: white;
  stroke: none;
  font-size: 14px;
}
/** Section styling */
.section {
  stroke: none;
  opacity: 0.2;
}
.section0 {
  fill: #449d44;
}
.section2 {
  fill: #31b0d5;
}
.section1,
.section3 {
  fill: #ec971f;
  opacity: 0.2;
}
.sectionTitle0 {
  fill: white;
}
.sectionTitle1 {
  fill: white;
}
.sectionTitle2 {
  fill: white;
}
.sectionTitle3 {
  fill: white;
}
.sectionTitle {
  text-anchor: start;
  font-size: 11px;
  text-height: 14px;
}
/* Grid and axis */
.grid .tick {
  stroke: lightgrey;
  opacity: 0.3;
  shape-rendering: crispEdges;
}
.grid path {
  stroke-width: 0;
}
/* Today line */
.today {
  fill: none;
  stroke: red;
  stroke-width: 2px;
}
/* Task styling */
/* Default task */
.task {
  stroke-width: 2;
}
.taskText {
  text-anchor: middle;
  font-size: 11px;
}
.taskTextOutsideRight {
  fill: black;
  text-anchor: start;
  font-size: 11px;
}
.taskTextOutsideLeft {
  fill: black;
  text-anchor: end;
  font-size: 11px;
}
/* Specific task settings for the sections*/
.taskText0,
.taskText1,
.taskText2,
.taskText3 {
  fill: white;
}
.task0,
.task1,
.task2,
.task3 {
  fill: #c9302c;
  stroke: #c9302c;
}
.taskTextOutside0,
.taskTextOutside2 {
  fill: black;
}
.taskTextOutside1,
.taskTextOutside3 {
  fill: black;
}
/* Active task */
.active0,
.active1,
.active2,
.active3 {
  fill: #c9302c;
  stroke: #c9302c;
}
.activeText0,
.activeText1,
.activeText2,
.activeText3 {
  fill: white !important;
}
/* Completed task */
.done0,
.done1,
.done2,
.done3 {
  stroke: grey;
  fill: lightgrey;
  stroke-width: 2;
}
.doneText0,
.doneText1,
.doneText2,
.doneText3 {
  fill: black !important;
}
/* Tasks on the critical line */
.crit0,
.crit1,
.crit2,
.crit3 {
  stroke: #c9302c;
  fill: #c9302c;
  stroke-width: 2;
}
.activeCrit0,
.activeCrit1,
.activeCrit2,
.activeCrit3 {
  stroke: #c9302c;
  fill: #c9302c;
  stroke-width: 2;
}
.doneCrit0,
.doneCrit1,
.doneCrit2,
.doneCrit3 {
  stroke: #c9302c;
  fill: #c9302c;
  stroke-width: 2;
  cursor: pointer;
  shape-rendering: crispEdges;
}
.doneCritText0,
.doneCritText1,
.doneCritText2,
.doneCritText3 {
  fill: white !important;
}
.activeCritText0,
.activeCritText1,
.activeCritText2,
.activeCritText3 {
  fill: white !important;
}
.titleText {
  text-anchor: middle;
  font-size: 18px;
  fill: white;
}
/*


*/
text {
  font-size: 14px;
}

.mermaid {
  width:100%;
}