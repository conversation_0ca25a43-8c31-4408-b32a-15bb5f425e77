<div class="row searchBar">
    {{> twitter/views/partials/navbar}}
    <div class="">
        <h1>How can we help?</h1>
    </div>
	<form action="{{app_context}}/search" id="search_form" method="post">
        {{#if config.settings.show_featured_articles}}
		    <div class="col-md-4 col-md-offset-4 search_bar">
        {{else}}
            <div class="col-md-8 col-md-offset-2 col-lg-8 col-lg-offset-2 search_bar">
        {{/if}}
			<div class="input-group">
				<input type="text" name="frm_search" id="frm_search" class="form-control input-lg" placeholder="search">
				<span class="input-group-btn">
					<button class="btn btn-default btn-lg" id="btn_search" type="submit"><i class="fa fa-search" aria-hidden="true"></i></button>
				</span>
			</div>
            {{#ifCond config.settings.typeahead_search '===' true}}
            <div id="searchResult" class="hidden col-lg-12">
                <ul class="list-group searchResultList"></ul>
            </div>
            {{/ifCond}}
		</div>
	</form>
</div>
<div class="row">
    <div class="col-md-8 col-md-offset-2 col-lg-8 col-lg-offset-2">
        {{#if config.settings.show_featured_articles}}
            <div class="col-md-3 col-lg-3">
                <div class="panel panel-default" style="margin-top: 30px;">
                    <div class="panel-heading">Featured articles</div>
                    <div class="panel-body">
                        <ul class="list-group">
                            {{#each featured_results}}
                                {{#if this.kb_permalink}}
                                    <li class="list-group-item"><a href="/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a></li>
                                {{else}}
                                    <li class="list-group-item"><a href="/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a></li>
                                {{/if}}
                            {{/each}}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-8 col-lg-8">
        {{else}}
            <div class="col-md-8 col-lg-8">
        {{/if}}
            <h1 style="padding-bottom: 25px;padding-top: 15px;">{{result.kb_title}}</h1>
            <div class="body_text">
                {{{kb_body}}}
            </div>
            {{#if config.settings.allow_voting}}
                <div class="row">
                    <div class="voting text-right col-lg-12">
                        <span>{{__ "Was this article helpful?"}}</span>
                        <button id="btnUpvote" class="btn btn-sm btn-default"><i class="fa fa-thumbs-o-up" aria-hidden="true"></i></button>
                        <button id="btnDownvote" class="btn btn-sm btn-default"><i class="fa fa-thumbs-o-down" aria-hidden="true"></i></button>
                        <span>
                            <strong>{{__ "Votes"}}:</strong>
                            {{#if result.kb_votes}}
                                {{result.kb_votes}}
                            {{else}}
                                0
                            {{/if}}
                        </span>
                    </div>
                </div>
            {{/if}}
            {{#if config.settings.show_kb_meta}}
                <div class="panel panel-primary" style="margin-top: 35px;">
                    <div class="panel-heading">Article details:</div>
                    <div class="panel-body metaData">
                        <h5 class="col-sm-12 col-lg-12">
                            <strong>Published date:</strong> {{format_date result.kb_published_date}}
                        </h5>
                        <h5 class="col-sm-12 col-lg-12">
                            <strong>Last updated:</strong> {{format_date result.kb_last_updated}}
				{{#if result.kb_last_update_user}}
                                    <span class="text-info">
                                        {{#ifCond config.settings.show_author_email '===' false}}
                                            ({{removeEmail result.kb_last_update_user}})
                                        {{else}}
                                            ({{result.kb_last_update_user}})
                                        {{/ifCond}}
                                    </span>
                                {{/if}}
                        </h5>
                        {{#if result.kb_permalink}}
                            <h5 class="col-sm-12 col-lg-12">
                                <strong>Share article:</strong>&nbsp;<a href="mailto:?subject={{result.kb_title}}&body={{encodeURI current_url}}/{{@root.config.settings.route_name}}/{{encodeURI result.kb_permalink}}" target="_top"><i class="fa fa-envelope" aria-hidden="true"></i></a>
                            </h5>
                        {{else}}
                            <h5 class="col-sm-12 col-lg-12">
                                <strong>Share article:</strong>&nbsp;<a href="mailto:?subject={{result.kb_title}}&body={{encodeURI current_url}}/{{@root.config.settings.route_name}}/{{result._id}}" target="_top"><i class="fa fa-envelope" aria-hidden="true"></i></a>
                            </h5>
                        {{/if}}
                        {{#if result.kb_author}}
                            <h5 class="col-sm-12 col-lg-12">
                                <strong>Author:</strong> <span class="text-info">{{result.kb_author}} {{#if config.settings.show_author_email}}({{result.kb_author_email}}){{/if}}</span>
                            </h5>
                        {{/if}}
                        {{#if result.kb_permalink}}
                        <h5 class="col-sm-12 col-lg-12">
                            <strong>Permalink:</strong> <a href="{{current_url}}/{{@root.config.settings.route_name}}/{{result.kb_permalink}}" target="_blank">{{current_url}}/{{@root.config.settings.route_name}}/{{result.kb_permalink}}</a>
                        </h5>
                        {{/if}}
                    </div>
                </div>
            {{/if}}
            <h5 class="keywords">
                {{{split_keywords result.kb_keywords}}}
            </h5>
        </div>
    </div>
</div>
