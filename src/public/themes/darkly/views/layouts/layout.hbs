<!DOCTYPE html>
<html lang="{{config.settings.locale}}">
  <head>
    {{#if result.kb_seo_title}}
      <title>{{result.kb_seo_title}}</title>
      <meta property="og:title" content="{{result.kb_seo_title}}" />
    {{else}}
      {{#if result.kb_title}}
        <title>{{result.kb_title}}</title>
        <meta property="og:title" content="{{result.kb_title}}" />
      {{else}}
        <title>{{title}}</title>
        <meta property="og:title" content="{{title}}" />
      {{/if}}
    {{/if}}
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    {{#if result.kb_seo_description}}
      <meta name="description" content="{{result.kb_seo_description}}">
      <meta property="og:description" content="{{result.kb_seo_description}}" />
    {{else}}
      {{#if result.kb_body}}
        <meta name="description" content="{{substring (strip_md result.kb_body) 160}}">
        <meta property="og:description" content="{{substring (strip_md result.kb_body) 160}}" />
      {{else}}
        <meta name="description" content="{{__ "Powered by"}} openKB">
        <meta property="og:description" content="openKB is an Open Source Node.js Markdown based knowledge base/FAQ/Wiki app" />
      {{/if}}
    {{/if}}
    {{#if result.kb_keywords}}
      <meta name="keywords" content="{{result.kb_keywords}}">
    {{else}}
      <meta name="keywords" content="openKB">
    {{/if}}
    <meta property="og:type" content="article" />
    <meta property="og:image" content="{{current_url}}/logo.png" />
    <meta property="og:url" content="{{fullUrl}}" />
    <link rel="canonical" href="{{fullUrl}}" />
    <link rel="stylesheet" href="/themes/darkly/css/darkly.min.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/github.css">
    <link rel="stylesheet" href="/themes/darkly/css/style.css">
    <link rel="stylesheet" href="{{app_context}}/font-awesome/css/font-awesome.min.css">
    <script src="{{app_context}}/jquery/jquery.min.js"></script>
    <script src="{{app_context}}/bootstrap/js/bootstrap.min.js"></script>
    {{#ifCond config.settings.mermaid '===' true}}
      <script src="{{app_context}}/javascripts/mermaid.min.js"></script>
    {{/ifCond}}
    {{#ifCond config.settings.mathjax '===' true}}
      <script type="text/x-mathjax-config">
        MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']], processEscapes: true}});
      </script>
      <script async src="https://cdn.mathjax.org/mathjax/latest/MathJax.js?config={{config.settings.mathjax_input_mode}}"></script>
    {{/ifCond}}
    <script src="{{app_context}}/javascripts/markdown-it-classy.js"></script>
    <script src="{{app_context}}/javascripts/highlight.min.js"></script>
    <script src="{{app_context}}/markdown-it/markdown-it.min.js"></script>
    <script src="{{app_context}}/javascripts/openKB.js"></script>
    {{#ifCond user_page '===' true}}
      {{#if config.settings.google_analytics}}
        {{{config.settings.google_analytics}}}
      {{/if}}
    {{/ifCond}}
  </head>
  <body class="{{show_footer}}">
    <!-- Static navbar -->
      <nav class="navbar navbar-default navbar-static-top">
      <div class="container-fluid">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="{{app_context}}/"><img alt="Brand" class="img-responsive" height="110" width="110" src="{{app_context}}/themes/darkly/images/logo.png"></a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
          <ul class="nav navbar-nav navbar-right">
            {{#if session.user}}
              {{#if result._id}}
                <li><a href="{{app_context}}/edit/{{result._id}}">Edit</a></li>
              {{/if}}
              <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Users <span class="caret"></span></a>
                <ul class="dropdown-menu">
                  {{#ifCond session.is_admin '==' 'true'}}
                    <li><a href="{{app_context}}/users/new"><i class="fa fa-user-plus fa-fw"></i>&nbsp; New</a></li>
                    <li><a href="{{app_context}}/users"><i class="fa fa-users fa-fw"></i>&nbsp; Edit</a></li>
                  {{/ifCond}}
                  <li><a href="{{app_context}}/user/edit/{{session.user_id}}"><i class="fa fa-user fa-fw"></i>&nbsp; My account</a></li>
                </ul>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Article <span class="caret"></span></a>
                <ul class="dropdown-menu">
                  <li><a href="{{app_context}}/insert"><i class="fa fa-plus fa-fw"></i>&nbsp; New</a></li>
                  <li><a href="{{app_context}}/articles"><i class="fa fa-pencil fa-fw"></i>&nbsp; Edit</a></li>
                </ul>
              </li>
              {{#ifCond session.is_admin '==' 'true'}}
                <li class="dropdown">
                  <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Admin <span class="caret"></span></a>
                  <ul class="dropdown-menu">
                    <li><a href="{{app_context}}/settings"><i class="fa fa-cog fa-fw"></i>&nbsp; {{__ "Settings"}}</a></li>
                    <li><a href="{{app_context}}/files"><i class="fa fa-files-o fa-fw"></i>&nbsp; {{__ "Files"}}</a></li>
                    <li><a href="{{app_context}}/import"><i class="fa fa-upload fa-fw"></i>&nbsp; {{__ "Import"}}</a></li>
                    <li><a href="{{app_context}}/export"><i class="fa fa-file-archive-o fa-fw"></i>&nbsp; {{__ "Export"}}</a></li>
                    <li><a href="{{app_context}}/file_cleanup"><i class="fa fa-trash fa-fw"></i>&nbsp; {{__ "Cleanup files"}}</a></li>
                  </ul>
                </li>
              {{/ifCond}}
              <li><a href="{{app_context}}/logout">Logout</a></li>
            {{else}}
              {{#if config.settings.suggest_allowed}}
                <li><a href="{{app_context}}/suggest">Suggest</a></li>
              {{/if}}
              <li><a href="{{app_context}}/login">Login</a></li>
            {{/if}}
          </ul>
        </div>
        </div>
      </nav>
    <div class="container-fluid">
      {{{body}}}
    </div>
    <input type="hidden" id="input_notify_message" value="{{message}}">
    <input type="hidden" id="input_notify_message_type" value="{{message_type}}">
    <input type="hidden" value="{{app_context}}" id="app_context">
    <input type="hidden" value="{{config.settings.links_blank_page}}" id="blank_links">
    <div id="notify_message"></div>
    {{#if show_footer}}
      <footer class="footer">
        <div class="container">
          <p>{{__ "Powered by"}} <a href="http://openkb.mrvautin.com/" target="_blank">openKB</a></p>
        </div>
      </footer>
    {{/if}}
  </body>
</html>
