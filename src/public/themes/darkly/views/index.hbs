<div class="row searchBar">
    <h1 class="text-center">Welcome to {{config.settings.website_title}}</h1>
	<form action="{{app_context}}/search" id="search_form" method="post">
        {{#if config.settings.show_featured_articles}}
		    <div class="col-md-6 col-md-offset-3 col-lg-6 col-lg-offset-3 search_bar">
        {{else}}
            <div class="col-md-6 col-md-offset-3 col-lg-6 col-lg-offset-3 search_bar">
        {{/if}}
			<div class="input-group">
				<input type="text" name="frm_search" id="frm_search" class="form-control input-lg" placeholder="Search the knowledge base">
				<span class="input-group-btn">
					<button class="btn btn-success btn-lg" id="btn_search" type="submit">Search</button>
				</span>
			</div>
		</div>
	</form>
</div>
<div class="row">
    {{#if config.settings.show_featured_articles}}
        <div class="col-md-2 col-md-offset-2 col-lg-2 col-lg-offset-2">
            <div style="margin-top: 30px;">
                <ul class="list-group">
                    <li class="list-group-item list-group-heading">Featured articles</li>
                    {{#each featured_results}}
                        {{#if this.kb_permalink}}
                            <li class="list-group-item"><h5><a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a></h5></li>
                        {{else}}
                            <li class="list-group-item"><h5><a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a></h5></li>
                        {{/if}}
                    {{/each}}
                </ul>
            </div>
        </div>
        <div class="col-md-6 col-lg-6">
    {{else}}
        <div class="col-md-6 col-md-offset-3 col-lg-6 col-lg-offset-3">
    {{/if}}
		{{#if search_term}}
			<div style="margin-top: 30px;">
                <ul class="list-group">
                    {{#ifCond routeType '===' 'topic'}}
                        <li class="list-group-item list-group-heading">{{__ "Topic"}}: {{search_term}}</li>
                    {{else}}
                        <li class="list-group-item list-group-heading">{{__ "Results for"}}: {{search_term}}</li>
                    {{/ifCond}}
                    {{#each search_results}}
                        {{#if this.kb_permalink}}
                            <li class="list-group-item">
                                <h5>
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a>
                                    {{#if @root.config.settings.show_published_date}}
                                        {{#if @root.config.settings.show_view_count}}
                                            <span class="pull-right view_count">&nbsp; / <strong>Date:</strong> {{format_date this.kb_published_date}}</span>
                                        {{else}}
                                            <span class="pull-right view_count"><strong>Date:</strong> {{format_date this.kb_published_date}}</span>
                                        {{/if}}
                                    {{/if}}
                                    {{#if @root.config.settings.show_view_count}}
                                        <span class="pull-right view_count"><strong>View count:</strong> {{view_count this.kb_viewcount}}</span>
                                    {{/if}}
                                </h5>
                            </li>
                        {{else}}
                            <li class="list-group-item">
                                <h5>
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a>
                                    {{#if @root.config.settings.show_published_date}}
                                        {{#if @root.config.settings.show_view_count}}
                                            <span class="pull-right view_count">&nbsp; / <strong>Date:</strong> {{format_date this.kb_published_date}}</span>
                                        {{else}}
                                            <span class="pull-right view_count"><strong>Date:</strong> {{format_date this.kb_published_date}}</span>
                                        {{/if}}
                                    {{/if}}
                                    {{#if @root.onfig.settings.show_view_count}}
                                        <span class="pull-right view_count"><strong>View count: </strong>{{view_count this.kb_viewcount}}</span>
                                    {{/if}}
                                </h5>
                            </li>
                        {{/if}}
                    {{else}}
                        <li class="list-group-item">
                            <h5>No results found</h5>
                        </li>
                    {{/each}}
                </ul>
			</div>
		{{else}}
			<div style="margin-top: 30px;">
                <ul class="list-group">
                    <li class="list-group-item list-group-heading">Trending articles</li>
                    {{#each top_results}}
                            <li class="list-group-item col-md-6">
                                <h5>
                                {{#if kb_permalink}}
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a>
                                {{else}}
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a>
                                {{/if}}
                                </h5>
                            </li>
                    {{/each}}
                </ul>
			</div>
		{{/if}}
	</div>
</div>