{"Toggle navigation": "Toggle navigation", "Users": "Brugere", "New": "Ny", "Edit": "<PERSON><PERSON>", "My account": "<PERSON> konto", "Article": "Artikel", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Files": "Filer", "Export": "Eksporter", "Cleanup files": "Ryd op i filer", "Logout": "Logu<PERSON>", "Powered by": "Drevet af", "Home": "<PERSON><PERSON><PERSON>", "Articles": "<PERSON><PERSON><PERSON>", "Filter articles": "<PERSON><PERSON><PERSON> artikler", "Filter": "Filter", "Reset": "Nulstil", "All": "Alle", "Published": "Udgivet", "Recent": "Nyeste", "Are you sure you want to delete this article?": "Er du sikker på at du vil slette denne artikel?", "Search": "<PERSON><PERSON><PERSON>", "Featured articles": "<PERSON><PERSON><PERSON><PERSON><PERSON> artikler", "Top articles": "Top artikler", "Suggest": "Forslå", "Login": "Log ind", "Please sign in": "Venligst logud", "email address": "email adresse", "Password": "Adgangskode", "Sign in": "Log ind", "Website": "Website", "Display": "V<PERSON><PERSON>", "Update": "Opdater", "Website title": "Side titel", "The title of your website": "Titlen på din side", "Website description": "Side beskrivelse", "A short website description when listing the homepage URL in search engines": "En kort beskrivelse af din side når søgemaskienr indeksere forsiden", "Show website logo": "Vis logo", "true": "true", "false": "false", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "Styrer om der skal vises 'Side titel' tekst eller et logo placeret: '/public/logo.png' (som standard)", "Website context/base URL": "Side kontekst/base URL", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Tillader at siden kan køres fra en ikke root-sti. Eksempel: http://127.0.0.1:4444/openkb/", "Allow API access": "Tillad API adgang", "Whether to allow API access to insert articles - See documentation for further information": "Tillad API adgang til at oprette artikler - Se dokumenationen for mere info", "API access token": "API adgangstoken", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Krævet at 'Tillad API adgang' er slået til. Værdien er din adgangstoken, som er påkrævet for at tilgå den offentlige API. Vælg noget som er svært at gætte. Eksempel: clwbkMMGa01k", "Password protect": "Beskyt med adgangskode", "Setting to 'true' will require a user to login before viewing any pages": "<PERSON><PERSON><PERSON> den til 'true' kræver det at brugeren logger ind for at se siden", "Index article body": "Indekser artikel", "Whether to add the body of your articles to the search index (requires restart)": "Om din artikel skal indekseres i søgefunktionen (kræver genstart)", "Select a theme": "<PERSON><PERSON><PERSON><PERSON> et tema", "The theme to use for public facing pages. Leave blank for default": "<PERSON><PERSON><PERSON> på din side. <PERSON><PERSON> væ<PERSON> blankt for standard", "Show logon link": "Vis log ind", "Whether to show/hide the logon link in the top right of screen": "Om log ind/log ud link skal vises", "Date format": "Dato format", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Indstiller det globale dato format. Bruger moment.js dato formarttering, se mere her: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "Godkend artikel foreslag", "If enabled non authenticated users can submit article suggestions for approval": "<PERSON><PERSON> sl<PERSON>et til kan ikke godkendte brugere indsende forslag til artikler", "Google analytics code": "Google Analytics kode", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Tilføjer Google Analytics til alle offentlige sider. Inkluder HELE koden fra Google inklusiv &lt;script&gt; tags", "Allow voting": "Tillad at stemme", "Whether to allow users to vote on an article": "Giver brugere adgang til at stemme på en artikel", "Show article meta data": "Vis artikel meta data", "Whether to show article meta data including published date, last updated date, author etc": "Viser artiel meta data, som inkludere udgivelses dato, sidst opdateret, forfatter osv.", "Show author email": "Vis forfatter email", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Kontrollere om forfatterens email adresse skal vises. Kræver 'Vis artikel meta data' er sat til true", "Article links open new page": "Artikel links åbner ny side", "Controls whether links within articles open a new page (tab)": "Kontrollere om links i en artikel skal åbne i en ny side (tab)", "Add header anchors": "Tilføj <PERSON> ankre", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Skal der tilføjes HTML ankre til alle heading tags for at linke intern i artiklen eller direkte link fra andre artikler", "Enable editor spellchecker": "Aktiver stavekontrol i editoren", "Controls whether to enable the editor spellchecker": "Kontrollere om der er stavekontrol i editoren", "Allow article versioning": "Tillad artickle versionering", "Whether to track article versions with each save of the editor": "Holde øje med article version ved hver gemning", "Number of top articles shown on homepage": "<PERSON><PERSON> a top artikler vist på forsiden", "Sets the number of results shown on the home page": "Bestemmer antallet af resultater vist på forsiden", "Show published date": "<PERSON>is udgi<PERSON>", "Shows the published date next to the results on the homepage and search": "Viser udgivelsesdatoen ved siden af resultaterne på forsiden og i søgning", "Show view count": "Vis visningstal", "Shows the view count next to the results on the homepage and search": "Vis visningsantallet ved siden af resultaterne på forsiden og i søgning", "Update view count when logged in": "Opdtaer visningsantallet når logget ind", "Updates the view count also when users are logged in": "Opdatere visningsantallet selvom brugere er logget ind", "Show featured articles": "<PERSON><PERSON> frem<PERSON>æ<PERSON>e artikler", "Whether to show any articles set to featured in a sidebar": "Skal der vises en sidebar med fremhævede artikler", "Show featured articles when viewing article": "<PERSON>is fremhævede artikler når der ses en anden artikel", "Whether to show any articles set to featured in a sidebar when viewing an article": "Skal der vises fremhævede artikler i sidebaren når man ser en anden artikel", "Featured article count": "Fremhævede artikel antal", "The number of featured articles shown": "Antallet af fremhævede artikler vist", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Tilføjer Google Analytics til alle offentlige sider. Inkluder hele koden fra Google inklusiv <script> tags", "New user": "<PERSON><PERSON> bruger", "Users name": "Brugerens navn", "User email": "Bruger email", "User password": "<PERSON><PERSON>er adgangsko<PERSON>", "Password confirm": "Godkend adgangskode", "Create": "<PERSON><PERSON>", "A user with that email address already exists": "En bruger med den email eksistere allerede", "User": "<PERSON><PERSON><PERSON>", "Role": "<PERSON><PERSON>", "Admin": "Administrator", "Are you sure you want to delete?": "Er du sikker på at du vil slette?", "User edit": "<PERSON>iger bruger", "Password values do not match": "Adgangskoderne matcher ikke", "Date": "Da<PERSON>", "View count": "Visningstal", "Settings successfully updated.": "Settings successfully updated.", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value", "Setting to \"true\" will require a user to login before viewing any pages": "Setting to \"true\" will require a user to login before viewing any pages", "Select a language": "Select a language", "The language to use for public facing pages. Leave blank for default (English)": "The language to use for public facing pages. Leave blank for default (English)", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true", "Import": "Import"}