{"Toggle navigation": "Vä<PERSON><PERSON> navigering", "Users": "Användare", "New": "Ny", "Edit": "Rediger<PERSON>", "My account": "<PERSON><PERSON> konto", "Article": "Artikel", "Settings": "Inställningar", "Files": "Filer", "Export": "Export", "Cleanup files": "<PERSON><PERSON> filer", "Logout": "Logga ut", "Powered by": "Drivs av", "Home": "<PERSON><PERSON>", "Articles": "<PERSON><PERSON><PERSON>", "Filter articles": "Filtrera artiklar", "Filter": "Filter", "Reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "All": "<PERSON>a", "Published": "Publicerad", "Recent": "Nyligen", "Are you sure you want to delete this article?": "Är du säker på att du vill radera den här artikeln?", "Search": "<PERSON>ö<PERSON>", "Featured articles": "<PERSON><PERSON><PERSON><PERSON>", "Top articles": "Toppartiklar", "Suggest": "Föreslå", "Login": "Logga in", "Please sign in": "Vänligen logga in", "email address": "E-postadress", "Password": "L<PERSON>senord", "Sign in": "logga in", "Website": "<PERSON><PERSON><PERSON>", "Display": "Visa", "Update": "Uppdatera", "Website title": "Webbsidans titel", "The title of your website": "Titeln på din webbplats", "Website description": "Beskrivning av webbsidan", "A short website description when listing the homepage URL in search engines": "En kort beskrivning av webbplatsen som visas när man får träffar på sökmotorer", "Show website logo": "Visa webbsidans logotyp", "true": "sant", "false": "falskt", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "styr ifall 'webbplatsens titel' , 'text' eller om en logotyp skall visas, plats'/public/logo.png' (som standard)", "Website context/base URL": "Webbplatsens kontext/root URL", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "<PERSON><PERSON><PERSON> att webbplatsen körs från en icke-root-sökväg. Ex: http://127.0.0.1:4444/openkb/", "Allow API access": "Tillåt API-åtkomst", "Whether to allow API access to insert articles - See documentation for further information": "Om du vill tillåta API-åtkomst för att infoga artiklar - Se dokumentation för ytterligare information", "API access token": "API access token", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Kräver att inställningen 'Tillåt API-åtkomst sätts till 'sant'. Värdet som sätts är 'access token' som krävs för att komma åt den offentliga API: n. Ange ett komplext värde", "Password protect": "Lösenordsskydd", "Setting to 'true' will require a user to login before viewing any pages": "Om du sätter den här inställningen till 'sant' krävs det att användaren loggar in innan det går att se några sidor.", "Index article body": "Indexera artikelns text", "Whether to add the body of your articles to the search index (requires restart)": "Om du vill lägga till artikelns text i sökfunktionen.", "Select a theme": "<PERSON><PERSON><PERSON><PERSON> ett tema", "The theme to use for public facing pages. Leave blank for default": "Temat som ska användas för publika sidor. Lämna blank om du vill använda standardtemat", "Show logon link": "Visa inloggningslänk", "Whether to show/hide the logon link in the top right of screen": "Huruvida du vill visa/döl<PERSON> inloggninslänken länst upp i högra hörnet på skärmen", "Date format": "Datumformat", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Sätter globalt datum format på webbplatsen. Använd moment.js datumformatering, se mer här: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "Tillåt Artikelförslag", "If enabled non authenticated users can submit article suggestions for approval": "Om du aktiverar den här inställningen kan icke autentiserade användare skicka artikelförslag för godkännande(Drafts).", "Google analytics code": "Google Analytics-kod", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Lägger till Google Analytics på publika sidor. Inkluderar hela koden från Google inklusive &lt;script&gt; tags", "Allow voting": "<PERSON><PERSON><PERSON>", "Whether to allow users to vote on an article": "Styr om användaren ska kunna rösta på artiklar", "Show article meta data": "Visa artikelns metadata", "Whether to show article meta data including published date, last updated date, author etc": "Styr huruvuda artikelns metadata skall visas(publicerat datum, senast uppdaterat datum, författare etc", "Show author email": "Visa e-postadress till författaren", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Styr huruvida författarens e-postadress ska visas i metan. Kräver att 'Visa artikelns metadata' är satt på 'sant'", "Article links open new page": "Artikel-länkar öppnas i ny sida (flik)", "Controls whether links within articles open a new page (tab)": "Styr huruvida länkar i artiklar öppnar en ny sida(flik)", "Add header anchors": "<PERSON><PERSON><PERSON> till rubrik an<PERSON>e", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Om du vill lägga till HTML-ankare på alla rubriktaggar för att länka internt i artiklar alternativt direktlänk från andra artiklar", "Enable editor spellchecker": "Aktivera stavningskontroll i editorn", "Controls whether to enable the editor spellchecker": "Styr om stavningskontroll i editorn ska aktiveras", "Allow article versioning": "<PERSON><PERSON><PERSON>tering på artikel", "Whether to track article versions with each save of the editor": "Om du vill versionshantera artikeln varje gång du gör ändringar", "Number of top articles shown on homepage": "Antal toppartiklar som visas på förstasidan", "Sets the number of results shown on the home page": "<PERSON><PERSON><PERSON> in antalet toppartiklar som ska visas på förstasidan", "Show published date": "Visa publicerat datum", "Shows the published date next to the results on the homepage and search": "Visar datum som artikeln publicerades bredvid toppartiklarna på förstasidan & vid sökning", "Show view count": "Visa antal visningar", "Shows the view count next to the results on the homepage and search": "Visar visningsantalet för en artikel bredvid resultat på hemsidan & vid sökning", "Update view count when logged in": "Uppdatera visningsräkning när du är inloggad", "Updates the view count also when users are logged in": "Styr om visningsräknaren för en artikel ska uppdateras när man är inloggad", "Show featured articles": "Visa utvalda artiklar", "Whether to show any articles set to featured in a sidebar": "Bestämmer om du vill visa 'utvalda' artiklar i sidofältet", "Show featured articles when viewing article": "Visa utvalda artiklar när du tittar på andra artiklar", "Whether to show any articles set to featured in a sidebar when viewing an article": "Bestämmer om du vill visa 'utvalda' artiklar när du tittar på andra artiklar.", "Featured article count": "antal ut<PERSON><PERSON> artik<PERSON>", "The number of featured articles shown": "<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON>' art<PERSON><PERSON> som ska visas", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "<PERSON><PERSON><PERSON> till Google Analytics på publika sidor. Inklusive hela koden från Google även <script> tags", "New user": "Ny Användare", "Users name": "Fullständigt namn (För- och Efternamn)", "User email": "E-postadress", "User password": "L<PERSON>senord", "Password confirm": "Bekräfta lösenord", "Create": "<PERSON><PERSON><PERSON>", "A user with that email address already exists": "En användare med e-postadressen finns redan", "User": "Användare", "Role": "Roll", "Admin": "Admin", "Are you sure you want to delete?": "Är du säker på att du vill radera?", "User edit": "<PERSON>igera an<PERSON>", "Password values do not match": "'Lösenord' och 'Bekräfta lösenord' matchar inte", "Date": "Datum", "View count": "<PERSON><PERSON> v<PERSON>", "Article title": "Artikelns titel", "Status": "Status", "Draft": "Draft", "Insert": "Infoga", "Article body": "Artikelns text", "Preview": "Förhandsvisning", "comma, separated, list": "komma, avgränsad, lista", "Permalink for the article": "Permalink till artikeln", "Validate": "<PERSON><PERSON><PERSON>", "Generate": "<PERSON><PERSON>", "Slug from title": "Slug from title", "Search the knowledge base": "Sök i kunskapsbasen", "Was this article helpful?": "Var den här artikeln till hjälp?", "Votes": "<PERSON><PERSON><PERSON>", "Article details": "Artikelns <PERSON>jer", "Published date": "Publicerat datum", "Last updated": "Senast uppdaterad", "Share article": "Dela artikel", "Author": "Författare", "keyword": "sökord", "permalink": "permalink", "Delete": "<PERSON> bort", "Reset views": "Återställ visningar", "Reset votes": "Återställ röster", "Featured article": "<PERSON><PERSON><PERSON><PERSON>", "SEO title": "SEO-titel", "SEO description": "SEO-beskrivning", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "styr ifall 'webbplatsens titel' , 'text' eller om en logotyp skall visas, plats'/public/logo.png' (som standard)", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "Kräver att inställningen 'Tillåt API-åtkomst sätts till 'sant'. Värdet som sätts är 'access token' som krävs för att komma åt den offentliga API: n. Ange ett komplext värde", "Setting to \"true\" will require a user to login before viewing any pages": "Om du sätter den här inställningen till 'sant' krävs det att användaren loggar in innan det går att se några sidor", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Styr huruvida författarens e-postadress ska visas i metan. Kräver att 'Visa artikelns metadata' är satt på 'sant'", "Not Found": "<PERSON><PERSON>", "Select file": "<PERSON><PERSON><PERSON><PERSON> fil", "Upload directory": "Ladda upp katalog", "Upload file": "Ladda upp fil", "Tip": "Tips", "To insert an image right click the link, copy the link address and add it to your Markdown": "<PERSON><PERSON><PERSON> att infoga en bild, högerklicka på länken, kopiera länkadressen och lägg till den i din markdown", "New directory": "<PERSON><PERSON>", "Multiple directories can be created using a \"/\" separator": "Flera kataloger kan skapas med en \"/\" separator", "Article successfully deleted": "<PERSON><PERSON><PERSON> togs bort", "Keywords": "sökord", "Import": "Importera", "New article successfully created": "<PERSON><PERSON> art<PERSON> s<PERSON>", "Successfully saved": "Sparad", "Permalink": "Permalink", "Access denied. Check password and try again.": "Åtkomst nekad. Kontrollera lösenordet och försök igen.", "Select a language": "<PERSON><PERSON><PERSON><PERSON> ett språk", "The language to use for public facing pages. Leave blank for default (English)": "Språket som ska användas för publika sidor. Lämna blankt för <PERSON> (Engelska)", "Settings successfully updated.": "Inställningar uppdaterade.", "Results for": "Resultat för", "No results found": "Inga resultat hittades", "Access denied": "Åtkomst nekad", "User account inserted": "användarkonto skapat", "Directory successfully created": "Katalog skapad", "File uploaded successfully": "Fil uppladdad", "Allow Mermaid": "<PERSON><PERSON><PERSON> Mermaid", "Whether to enable ": "<PERSON>m du vill aktivera Mermaid, för mer info: https://knsv.github.io/mermaid/", "Allow MathJax": "<PERSON><PERSON><PERSON>, för mer info: https://github.com/mathjax/MathJax", "MathJax input mode": "MathJax input", "Configure the mode MathJax operates in - ": "Konfigurerar läget MathJax fungerar i - ", "Setup": "Installation", "Email address": "E-postadress", "Confirm password": "Bekräfta lösenord", "Complete setup": "Slutför <PERSON>", "404 - Page not found": "404 - sidan kan inte visas", "Versions": "Versioner", "Edit reason": "<PERSON><PERSON><PERSON> an<PERSON>ning", "Previous versions": "Föregående version", "Topic": "Ämne", "Suggestion successfully processed": "F<PERSON>rs<PERSON>t har skickats!", "Style": "Style", "User account updated.": "Användarkontot uppdaterat", "User is admin?": "Är användaren admin?", "User deleted.": "Användaren borttagen."}