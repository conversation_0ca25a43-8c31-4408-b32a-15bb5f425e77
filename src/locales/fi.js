{"Toggle navigation": "Näytä/pii<PERSON>a navigo<PERSON>i", "Users": "Käyttäjät", "New": "<PERSON>us<PERSON>", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "My account": "<PERSON><PERSON><PERSON>", "Article": "<PERSON><PERSON><PERSON><PERSON>", "Settings": "Asetukset", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Export": "Vienti", "Cleanup files": "Siivo<PERSON> tied<PERSON>", "Logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Powered by": "Powered by", "Home": "<PERSON><PERSON>", "Articles": "Artikkelit", "Filter articles": "Suoda<PERSON> art<PERSON>", "Filter": "<PERSON><PERSON><PERSON>", "Reset": "<PERSON><PERSON><PERSON>", "All": "<PERSON><PERSON><PERSON>", "Published": "Julkaistu", "Recent": "Viimeisimmät", "Are you sure you want to delete this article?": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> pois<PERSON>a art<PERSON>?", "Search": "Etsi", "Featured articles": "Suositellut artikkelit", "Top articles": "Suosituimmat artikkelit", "Suggest": "<PERSON><PERSON><PERSON><PERSON> art<PERSON>", "Login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Please sign in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email address": "Sähköpostiosoite", "Password": "<PERSON><PERSON><PERSON>", "Sign in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Website": "<PERSON><PERSON><PERSON>", "Display": "<PERSON><PERSON><PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON>", "Website title": "<PERSON><PERSON><PERSON> o<PERSON>", "The title of your website": "<PERSON><PERSON><PERSON> o<PERSON>", "Website description": "<PERSON><PERSON><PERSON> kuvaus", "A short website description when listing the homepage URL in search engines": "<PERSON>y<PERSON><PERSON>, joka näkyy hakukonei<PERSON>", "Show website logo": "Näytä sivuston logo", "true": "On", "false": "<PERSON>i", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "Määrittää näytetäänkö sivuston otsikko vai logo (sijainnissa '/public/logo.png')", "Website context/base URL": "Sivuston osoite", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "<PERSON><PERSON><PERSON> si<PERSON> näkymisen ei-juuri<PERSON>, esim http://127.0.0.1/openkb/", "Allow API access": "Salli API käyttö", "Whether to allow API access to insert articles - See documentation for further information": "Sallittaanko artikkelien lisääminen APIn kautta - katso dokumentaatiosta lisätiedot", "API access token": "API-käyttöavain", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Tällä käyttöavaimella hallitaan API-käyttöä. Käytä vahvaa avainta.", "Password protect": "Salasanasuo<PERSON><PERSON>", "Setting to 'true' will require a user to login before viewing any pages": "<PERSON><PERSON><PERSON><PERSON> 'kyll<PERSON>', vaatii kir<PERSON><PERSON> sis<PERSON><PERSON><PERSON>n katsele<PERSON>i", "Index article body": "Indeksoi artik<PERSON> te<PERSON>", "Whether to add the body of your articles to the search index (requires restart)": "Lisätäänkö artikkelin teksti hakuindeksiin (vaatii uudelleenkäynnistyksen)", "Select a theme": "<PERSON><PERSON><PERSON> teema", "The theme to use for public facing pages. Leave blank for default": "Valitse julkisen sivuston teema. Jätä tyhjäksi mikä<PERSON> oletus", "Show logon link": "Näytä kirjautumislinkki", "Whether to show/hide the logon link in the top right of screen": "Näytetäänkö/piiloitetaanko kirjautumislink<PERSON> sivun oikeassa ylälaidassa", "Date format": "Päivämä<PERSON><PERSON><PERSON><PERSON> muoto", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Määrittää päivämä<PERSON><PERSON><PERSON><PERSON> muo<PERSON>, esimerkiksi 'DD.MM.YYYY hh:mm', noudattaa moment.js syntaksia, katso lis<PERSON> http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "<PERSON><PERSON> artikkeleiden ehdotuksia", "If enabled non authenticated users can submit article suggestions for approval": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>täj<PERSON> voi ehdottaa artikkeleita", "Google analytics code": "Google analytics -koodi", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Lisää Google Analytics julkisille sivuille. Sisällytä tähän koko Google Analyticsin antama koodi, mukaanlukien &lt;script&gt; tagit", "Allow voting": "<PERSON><PERSON>", "Whether to allow users to vote on an article": "<PERSON><PERSON> käyttäjien antaa ääniä artikkeleille", "Show article meta data": "Näytä artikkelin metadata", "Whether to show article meta data including published date, last updated date, author etc": "Näytetäänkö artikkelin metadata: julkaisupäivämäärä, vii<PERSON><PERSON> muokkaus, kirjoitt<PERSON> jne", "Show author email": "Näytä kirjoittajan sähköpostiosoite", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Määrittää, näytetäänkö metadatatiedoissa kirjoittajan sähköpostiosoite", "Article links open new page": "Artikkelin linkit uuteen ikkunaan", "Controls whether links within articles open a new page (tab)": "Määrittää avataanko artikkelin sisältämät linkit uuteen ikkunaan/välilehteen", "Add header anchors": "Lisää ankkurit o<PERSON>", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Määrittää lisätäänkö otsikoille html-<PERSON><PERSON><PERSON><PERSON>, ma<PERSON><PERSON><PERSON><PERSON><PERSON> linkitykset suoraan tie<PERSON> ots<PERSON>on", "Enable editor spellchecker": "Käytä o<PERSON>", "Controls whether to enable the editor spellchecker": "Määrittää, käytetäänkö tekstieditorissa o<PERSON>ua", "Allow article versioning": "<PERSON><PERSON> art<PERSON> ve<PERSON>", "Whether to track article versions with each save of the editor": "Mä<PERSON><PERSON>tää, säilytetäänkö jokainen uudelleentallennus omana versionaan", "Number of top articles shown on homepage": "Suosituimpien artikkelien määrä etusivulla", "Sets the number of results shown on the home page": "Määrittää kuinka monta suosituinta artikkelia näytetään etusivulla", "Show published date": "Näytä julkaisupäivämäärä", "Shows the published date next to the results on the homepage and search": "Näytä julkaisupäivämäärä hakutuloksissa ja etusivulla", "Show view count": "Näytä ka<PERSON>", "Shows the view count next to the results on the homepage and search": "Näyttää katseluke<PERSON>t hakutuloksissa ja etusivulla", "Update view count when logged in": "Ka<PERSON><PERSON>a katselukertaa kirja<PERSON>iden käyttäjien katseluista", "Updates the view count also when users are logged in": "", "Show featured articles": "Näytä suositellut artikkelit", "Whether to show any articles set to featured in a sidebar": "Määrittää, näytetäänkö suositeltuja artikkeleita etusivun sivuvalikossa", "Show featured articles when viewing article": "Näytä suositellut artikkelisivulla", "Whether to show any articles set to featured in a sidebar when viewing an article": "Näytetäänkö suositellut sivut artikkelisivulla sivuvalikossa", "Featured article count": "Suositeltujen artikkelien määrä", "The number of featured articles shown": "<PERSON><PERSON><PERSON> monta suositeltua artikkelia näytetään", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Lisää Google Analytics julkisille sivuille. Sisällytä tähän koko Google Analyticsin antama koodi, muka<PERSON><PERSON><PERSON> <script> tagit", "New user": "<PERSON><PERSON><PERSON>j<PERSON>", "Users name": "K<PERSON><PERSON>täjä<PERSON> nimi", "User email": "Käyttäjän sähköpostiosoite", "User password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Password confirm": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Create": "<PERSON><PERSON>", "A user with that email address already exists": "Käyttäjä tällä sähköpostiosoitteella on jo o<PERSON><PERSON>a", "User": "Käyttäjä", "Role": "<PERSON><PERSON><PERSON>", "Admin": "Ylläpito", "Are you sure you want to delete?": "<PERSON><PERSON><PERSON>, ett<PERSON> haluat poistaa?", "User edit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muokkaus", "Password values do not match": "Salasanat eivät täsmää", "Date": "Päivämäärä", "View count": "Kat<PERSON><PERSON><PERSON><PERSON>", "Article title": "<PERSON><PERSON><PERSON><PERSON>", "Status": "Tila", "Draft": "Vedos", "Insert": "Lisää artikkeli", "Article body": "<PERSON><PERSON><PERSON><PERSON>", "Preview": "Esikatsele", "comma, separated, list": "pilkku, eroteltu, lista", "Permalink for the article": "<PERSON><PERSON><PERSON><PERSON>", "Validate": "Validoi", "Generate": "Generoi", "Slug from title": "Slug from title", "Search the knowledge base": "Etsi <PERSON>", "Was this article helpful?": "<PERSON>liko artikkelista a<PERSON>a?", "Votes": "<PERSON><PERSON><PERSON>", "Article details": "<PERSON><PERSON><PERSON><PERSON>", "Published date": "Julkaistu", "Last updated": "Viimeksi päiv<PERSON>tty", "Share article": "<PERSON>aa artik<PERSON>i", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyword": "a<PERSON><PERSON><PERSON>", "permalink": "ikilink<PERSON>", "Delete": "Poista", "Reset views": "<PERSON><PERSON><PERSON>", "Reset votes": "<PERSON><PERSON><PERSON>", "Featured article": "Suositeltu artikkeli", "SEO title": "SEO otsikko", "SEO description": "SEO kuvaus", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "Määrittää näytetäänkö \"Sivuston otsikko\" -teksti vai logo sijainnista: \"/public/logo.png\"", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "<PERSON><PERSON><PERSON><PERSON> \"Salli API-käyttö\" -asetuksen päälläolon. Tällä avaimella autentikoidutaan APIin. Käytä vahvaa merkkijonoa.", "Setting to \"true\" will require a user to login before viewing any pages": "<PERSON><PERSON>, vaati<PERSON> käyttäjän kirjautumisen ennen sisällön näyttämistä", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Määrittää, näytetäänkö kirjoittajan sähköpostiosoite metadatassa.", "Not Found": "ei l<PERSON>yt", "Select file": "Valitse tiedosto", "Upload directory": "<PERSON><PERSON><PERSON> hake<PERSON>o", "Upload file": "Lataa <PERSON>", "Tip": "<PERSON><PERSON><PERSON>", "To insert an image right click the link, copy the link address and add it to your Markdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> linkkiä hiiren o<PERSON>, kopioi linkin osoite ja lisää se <PERSON>n", "New directory": "<PERSON><PERSON><PERSON> hake<PERSON>", "Multiple directories can be created using a \"/\" separator": "Useampia hakemistoja voi luoda käyttäen \"/\" -erotinta", "Article successfully deleted": "<PERSON><PERSON><PERSON><PERSON> pois<PERSON>u", "Keywords": "<PERSON><PERSON><PERSON><PERSON>", "Import": "<PERSON><PERSON>", "New article successfully created": "<PERSON><PERSON><PERSON> art<PERSON><PERSON> luotu", "Successfully saved": "<PERSON><PERSON><PERSON>", "Permalink": "<PERSON><PERSON>link<PERSON>", "Access denied. Check password and try again.": "Pää<PERSON> estetty. Tarkista salasana ja yritä uudelleen.", "Select a language": "Valitse kieli", "The language to use for public facing pages. Leave blank for default (English)": "<PERSON><PERSON><PERSON> puolen kieli, j<PERSON><PERSON> tyhjäksi jos englanti", "Settings successfully updated.": "Asetuk<PERSON> tallennettu.", "Results for": "Tulokset haulle", "No results found": "<PERSON><PERSON> tuloksia", "Access denied": "Käyttö estetty", "User account inserted": "Käyttäjä lis<PERSON>", "Directory successfully created": "<PERSON><PERSON><PERSON><PERSON> luotu ", "File uploaded successfully": "<PERSON>ied<PERSON><PERSON>", "Allow Mermaid": "<PERSON><PERSON> Mermaid", "Whether to enable ": "<PERSON><PERSON><PERSON><PERSON> ", "Allow MathJax": "<PERSON><PERSON>", "MathJax input mode": "MathJ<PERSON><PERSON>", "Configure the mode MathJax operates in - ": "Määritä MathJaxin toimintatila - ", "Setup": "<PERSON><PERSON><PERSON>", "Email address": "Sähköpostiosoite", "Confirm password": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Complete setup": "Viimeistele määritys", "404 - Page not found": "404 - <PERSON><PERSON><PERSON> ei <PERSON>y", "Versions": "<PERSON><PERSON><PERSON>", "Edit reason": "Mu<PERSON><PERSON><PERSON>sen syy", "Previous versions": "<PERSON><PERSON><PERSON>", "Topic": "<PERSON><PERSON><PERSON><PERSON>", "Suggestion successfully processed": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> käsiteltäväksi", "Style": "<PERSON><PERSON><PERSON>"}