{"Toggle navigation": "Navigations ändern", "Users": "<PERSON><PERSON><PERSON>", "New": "<PERSON>eu", "Edit": "Ändern", "My account": "<PERSON><PERSON>", "Article": "Artikel", "Settings": "Einstellungen", "Files": "<PERSON><PERSON>", "Export": "Exportieren", "Cleanup files": "<PERSON><PERSON> aufrä<PERSON>n", "Logout": "Abmelden", "Powered by": "Powered by", "Home": "Home", "Articles": "Artikel", "Filter articles": "Artikel filtern", "Filter": "Filter", "Reset": "Z<PERSON>ücksetzen", "All": "Alle", "Published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Recent": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure you want to delete this article?": "Sind Sie sich sicher, dass Sie den Artikel löschen wollen?", "Search": "<PERSON><PERSON>", "Featured articles": "gefeaturete Artikel", "Top articles": "Top-Artikel", "Suggest": "Vorschlagen", "Login": "Anmelden", "Please sign in": "Bitte melden Sie sich an", "email address": "<PERSON><PERSON>", "Password": "Passwort", "Sign in": "Anmelden", "Website": "Website", "Display": "Anzeige", "Update": "Aktualisieren", "Website title": "Websitetitel", "The title of your website": "Der Titel der Webseite", "Website description": "Beschreibung der Webseite", "A short website description when listing the homepage URL in search engines": "Eine kurze Beschreibung die bei Einträgen in Suchmaschinen (z.B. Google) angezeigt wird", "Show website logo": "Zeige das Webseite-Logo", "true": "<PERSON><PERSON><PERSON>", "false": "falsch", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "Steuert ob der Text 'Website title' oder das Logo (Standard) von '/public/logo.png' angezeigt wird", "Website context/base URL": "Website context/base URL (meist Domain-Name)", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Erlaubt der Applikation aus einem nicht-administrativen Pfad aus zu starten. z.B.: http://127.0.0.1:4444/openkb/", "Allow API access": "Erlaube zugriff auf die API", "Whether to allow API access to insert articles - See documentation for further information": "Erlaubt das Hinzufügen von Artikeln über die API - Weiterführende Informationen in der Dokumentation", "API access token": "API Zugriffstoken", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Benötigt 'Allow API access' gleich 'wahr'. Der Wert ist das das Zugriffstoken das notwendig ist um Änderungen über die API zu machen. Bitte auf einen komplizierten wert setzen", "Password protect": "Mit Passwort sichern", "Setting to 'true' will require a user to login before viewing any pages": "Nur angemeldete Benutzer können Artikel sehen, wenn der Wert auf 'true' gesetzt ist", "Index article body": "Indiziere den Inhalt des Artikels für eine schnellere Suche", "Whether to add the body of your articles to the search index (requires restart)": "Soll der Inhalt der Artikel dem Suchindex hinzugefügt werden? (Benötigt einen Neustart der Applikation)", "Select a theme": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Anwendungsstil", "The theme to use for public facing pages. Leave blank for default": "Der Stil, der für öffentliche Seiten verwendet werden soll. Wenn keines ausgewählt ist wird der Standardstil verwendet", "Show logon link": "Zeige den Link zur Anmeldung", "Whether to show/hide the logon link in the top right of screen": "Zeigt oder Versteckt den Link zur Anmeldung in der oberen rechten Ecke", "Date format": "Datumsformat", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Setzt das globale Datumsformat mit Hilfe von 'moment.js'. (Weitere Informationen http://momentjs.com/docs/#/displaying)", "Article suggestions allowed": "Erlaube Artikel vorzuschlagen", "If enabled non authenticated users can submit article suggestions for approval": "<PERSON><PERSON> akt<PERSON><PERSON>, ist es nicht registrierten Benutzern mögliche Artikel vorzuschlagen, die dann bestätigt werden müssen.", "Google analytics code": "Google analytics Code", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags", "Allow voting": "Erlaube Bewertung", "Whether to allow users to vote on an article": "Ist es Benutzern möglich Artikel zu Bewerten", "Show article meta data": "Zeige Metadaten eines Artikels", "Whether to show article meta data including published date, last updated date, author etc": "Sollen Metadaten eines Artikels, wie Veröffentlichungsdatum, Aktualisierungsdatum, Autor etc., angezeigt werden", "Show author email": "Show author email", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true", "Article links open new page": "Article links open new page", "Controls whether links within articles open a new page (tab)": "Controls whether links within articles open a new page (tab)", "Add header anchors": "Add header anchors", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles", "Enable editor spellchecker": "Enable editor spellchecker", "Controls whether to enable the editor spellchecker": "Controls whether to enable the editor spellchecker", "Allow article versioning": "Allow article versioning", "Whether to track article versions with each save of the editor": "Whether to track article versions with each save of the editor", "Number of top articles shown on homepage": "Number of top articles shown on homepage", "Sets the number of results shown on the home page": "Sets the number of results shown on the home page", "Show published date": "Show published date", "Shows the published date next to the results on the homepage and search": "Shows the published date next to the results on the homepage and search", "Show view count": "Show view count", "Shows the view count next to the results on the homepage and search": "Shows the view count next to the results on the homepage and search", "Update view count when logged in": "Update view count when logged in", "Updates the view count also when users are logged in": "Updates the view count also when users are logged in", "Show featured articles": "Show featured articles", "Whether to show any articles set to featured in a sidebar": "Whether to show any articles set to featured in a sidebar", "Show featured articles when viewing article": "Show featured articles when viewing article", "Whether to show any articles set to featured in a sidebar when viewing an article": "Sollen gefeaturete Artikel bei der Ansicht eines Artikels in einer Seitenleiste angezeigt werden", "Featured article count": "Anzahl der gefeatureten Artikel", "The number of featured articles shown": "Die Anzahl der sichtbaren Feature-Artikel", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Fügt Google Analyticszu öffentlichen Seiten hinzu. Fügen Sie den kompletten Quellcode von Google ein (inkl. <script>-Tag)", "New user": "<PERSON><PERSON><PERSON>", "Users name": "<PERSON><PERSON><PERSON><PERSON>", "User email": "<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "User password": "Benutzer-Passwort", "Password confirm": "Passwort bestätigen", "Create": "<PERSON><PERSON><PERSON><PERSON>", "A user with that email address already exists": "Ein Benutzer mit dieser Email-Adresse existiert bereits", "User": "<PERSON><PERSON><PERSON>", "Role": "<PERSON><PERSON>", "Admin": "Administrator", "Are you sure you want to delete?": "Sind Si<PERSON> sicher, dass sie das löschen wollen?", "User edit": "Benutzer editieren", "Password values do not match": "Die Passwörter stimmen nicht überein", "Date": "Datum", "View count": "<PERSON><PERSON><PERSON><PERSON>", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "Steuert ob der Text 'Website title' oder das Logo (Standard) von '/public/logo.png' angezeigt wird", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "Benö<PERSON><PERSON> \"Erlaube zugriff auf die API\". Der Wert ist das das Zugriffstoken das notwendig ist um Änderungen über die API zu machen. Bitte auf einen komplizierten wert setzen", "Setting to \"true\" will require a user to login before viewing any pages": "Ein Benutzer muss angemeldet sein bevor er <PERSON>iten sehen/lesen kann.", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "<PERSON><PERSON><PERSON> ob die Email des Autors in den Metadaten des Artikels angezeigt wird oder nicht. (Benötigt \"Zeige Artikel-Metadaten\")", "Article title": "Titel", "Status": "Status", "Draft": "<PERSON><PERSON><PERSON><PERSON>", "Insert": "Einfügen", "Article body": "Artikelinhalt", "Preview": "Vorschau", "comma, separated, list": "Durch Komma separierte Liste", "Permalink for the article": "Permalink für den Artikel", "Validate": "Überprüfen", "Generate": "<PERSON><PERSON><PERSON>", "Select file": "<PERSON>i ausw<PERSON>hlen", "Upload directory": "Verzeichnis hochladen", "Upload file": "<PERSON><PERSON> ho<PERSON>n", "Tip": "<PERSON><PERSON><PERSON>", "To insert an image right click the link, copy the link address and add it to your Markdown": "Um ein Bild hinzuzufügen kann die Bild-URL in den Markdown bereich eingefügt werden", "New directory": "Neues Verzeichnis", "Multiple directories can be created using a \"/\" separator": "Mehrere Verzeichnisse können erstellt werden, indem sie durch einen \"/\" getrennt sind", "All unused files have been removed": "Alle nicht mehr benötigten Datein wurden entfernt", "New article successfully created": "Der neue Artikel wurde erfolgreich erstellt", "keyword": "Schlüsselwort", "permalink": "<PERSON><PERSON>", "Delete": "Löschen", "Reset views": "Ansi<PERSON><PERSON> zurücksetzen", "Reset votes": "Bewertungen zurücksetzen", "Featured article": "Vorgestellte Artikel", "SEO title": "SEO-Titel", "SEO description": "SEO-Beschreibung", "Successfully saved": "Erfolgreich gespeichert", "Filtered term": "Filter", "Settings successfully updated.": "Die Einstellungen wurden erfolgreich aktualisiert", "Setup": "Setup", "Email address": "<PERSON>ail<PERSON><PERSON><PERSON><PERSON>", "Confirm password": "Passwort bestätigen", "Complete setup": "Setup vervollständigen", "User account inserted": "Das Benutzerkonto wurde erstellt", "Search the knowledge base": "Durchsuchen...", "Was this article helpful?": "War dieser <PERSON><PERSON><PERSON> hilf<PERSON>?", "Votes": "Bewertungen", "Article details": "Artikeldetails", "Published date": "Veröffentlichungsdatum", "Last updated": "Zuletzt aktualisiert", "Share article": "<PERSON><PERSON><PERSON> teilen", "Author": "Autor", "Keywords": "Keywords", "Import": "Import", "Select a language": "Sprache auswählen", "The language to use for public facing pages. Leave blank for default (English)": "Die Sprache für öffentlich zugängliche Seiten. <PERSON><PERSON>, um die Standardeinstellung (Englisch) zu verwenden"}