{"Toggle navigation": "Conmutar <PERSON>", "Users": "Kullanıcı", "New": "<PERSON><PERSON>", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "My account": "He<PERSON>b<PERSON>m", "Article": "Makale", "Settings": "<PERSON><PERSON><PERSON>", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Export": "Dışarı aktar", "Cleanup files": "Dosyaları temizle", "Logout": "Çıkış", "Powered by": "'den besleniyor", "Home": "<PERSON>", "Articles": "<PERSON><PERSON><PERSON><PERSON>", "Filter articles": "Makalelerı süz", "Filter": "Süz", "Reset": "Sıfırla", "All": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Published": "Yayımlandı", "Recent": "En son", "Are you sure you want to delete this article?": "<PERSON>u makaleyi silmek istediğinizden emin misiniz", "Search": "Ara", "Featured articles": "Özellikli makaleler", "Top articles": "En iyi makaleler", "Suggest": "<PERSON><PERSON>", "Login": "<PERSON><PERSON><PERSON>", "Please sign in": "Lütfen girin", "email address": "E-posta adresi", "Password": "Şifre", "Sign in": "Oturum aç", "Website": "Web sitesi", "Display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Website title": "Web sitesi adı", "The title of your website": "Web sitenizin başlığı", "Website description": "Web sitesi tanımı", "A short website description when listing the homepage URL in search engines": "Arama motorlarında ana sayfanın BKK'sı  listelendiğinde web sitenin bir kısa tanımı", "Show website logo": "Web sitenin logosu göster", "true": "<PERSON><PERSON><PERSON><PERSON>", "false": "Yanlış", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "'Web sitesi başlığı' metnini veya '/public/logo.png' klasörde (varsayılan olarak) bulunan bir logo bulunan bir logo gösterip göstermeyeceğini kontrol eder", "Website context/base URL": "Web sitesinin ortam/temel BBK'sı", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Web sayfasının ana klasörden farklı bir dosyadan yürütülmesine izin verir. Örneği: http://127.0.0.1:4444/openkb/", "Allow API access": "API erişimine izin verir", "Whether to allow API access to insert articles - See documentation for further information": "Makaleleri eklemek için API erişimine izin verip vermeyeceği - Daha fazla bilgi için belgelere bakın", "API access token": "API erişim jetonu", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "'API erişimine izin verir'nin doğru'ya olmasını gerektir. Requiere 'Permitir acceso a la API' activada. El valor es el token de acceso necesario para acceder a la API pública. Por favor, establece uno dificil para invitados", "Password protect": "<PERSON><PERSON><PERSON>", "Setting to 'true' will require a user to login before viewing any pages": "'Gerçek' <PERSON><PERSON><PERSON>, her<PERSON><PERSON> bir say<PERSON><PERSON><PERSON> görüntülemeden önce bir kullanıcının oturum açmasını gerektirir", "Index article body": "Makale gövdesi dizin et", "Whether to add the body of your articles to the search index (requires restart)": "Makalelerinizin gövdesini arama dizinine ekleyip eklememek (yeniden başlatma gerektirir)", "Select a theme": "<PERSON><PERSON> tema seçin", "The theme to use for public facing pages. Leave blank for default": "Halka açık sayfalar için kullanılacak tema. Varsayılan olarak boş bırakın", "Show logon link": "Logo bağlantısını göster", "Whether to show/hide the logon link in the top right of screen": "Ekranın sağ üst köşesindeki logo bağlantısını göstermek/gizlemek", "Date format": "<PERSON><PERSON><PERSON> formatı", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "<PERSON>l tarih biçimlendirmesini ayar. Moment.js tarih biçimlendirmesini kullanır, daha fazla bilgi i<PERSON>in: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "Makale önerileri izin verdi", "If enabled non authenticated users can submit article suggestions for approval": "<PERSON><PERSON>kinleş<PERSON>rilirse,kimliği doğrulanmayan kullanıcılar makale önerilerini onay için gönderebilirler", "Google analytics code": "Google analytics kodu", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Google Analytics'i herkese açık sayfalara ekler. &lt;script&gt; etiketlerini de içeren Google'ın tüm kodunu dahil eder", "Allow voting": "<PERSON><PERSON><PERSON><PERSON> izin ver", "Whether to allow users to vote on an article": "Kullanıcılara bir makalede oylamaya izin verilir mi", "Show article meta data": "Makale meta verilerini g<PERSON>", "Whether to show article meta data including published date, last updated date, author etc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tari<PERSON>, son <PERSON><PERSON><PERSON><PERSON><PERSON> tari<PERSON>, yazar falan falan makale meta verilerinde dahil olması gösterme", "Show author email": "Yazarın e-postasını göster", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Yazarın e-posta adresinin meta içinde görüntülenip görüntülenmediğini kontrol eder. 'Makale meta verilerini göster' gerçek olmasını gerektirir", "Article links open new page": "Makale bağlantıları yeni say<PERSON> açar", "Controls whether links within articles open a new page (tab)": "Makaleler içindeki bağlantıları yeni bir sayfa açıp açmadığını kontrol eder (sekme)", "Add header anchors": "Başlık çapaları ekle", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Makaleler arasında bağlantı kurmak için tüm başlık etiketlerine veya diğer makalelerden doğrudan bağlanmak için HTML çapa ekleyip eklemediğini", "Enable editor spellchecker": "Düzenleyici yazım denetleyicisini etkinleştir", "Controls whether to enable the editor spellchecker": "Düzenleyici yazım denetleyicisini etkinleştirip etkinleştirmeyeceğini kontrol eder", "Allow article versioning": "<PERSON><PERSON><PERSON> sürümüne izin ver", "Whether to track article versions with each save of the editor": "Düzenleyicinden her kaydetme ile makale sürümlerini izleyip izleme", "Number of top articles shown on homepage": "Anasayfada gösterilen en iyi makaleler sayısı", "Sets the number of results shown on the home page": "Ana sayfada gösterilen sonuç sayısını ayar", "Show published date": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Shows the published date next to the results on the homepage and search": "Ya<PERSON>ı<PERSON>lanan tarihi ana sayfadaki ve aramasıdaki sonuçların yanında gösterir", "Show view count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>", "Shows the view count next to the results on the homepage and search": "Görüntü sayısını ana sayfadaki ve aramasıdakı sonuçların yanında gösterir", "Update view count when logged in": "Oturum açıldığını zaman görüntü sayısı<PERSON>ı günceller", "Updates the view count also when users are logged in": "kullanıcılar giriş yaptığını zaman da görüntü sayı<PERSON>ın<PERSON> günceller", "Show featured articles": "Özellikli makalelerı göster", "Whether to show any articles set to featured in a sidebar": "Kenar çubuğunda özellikli olarak ayarlanmış herhangi bir makale gösterilip gösterilmemesi", "Show featured articles when viewing article": "Makale görüntülendiğinde özellikli makaleleri göster", "Whether to show any articles set to featured in a sidebar when viewing an article": "Bir makaleyi görüntülerken kenar çubuğunda özellikli olarak ayarlanmış herhangi bir makaleyi gösterip göstermeyeceğiniz", "Featured article count": "Öne çıkan makale sayısı", "The number of featured articles shown": "Gösterilen özellikli makalelerin sayısı", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Google Analytics'i herkese açık sayfalara ekler. <script> etiketlerini de içeren Google'ın tüm kodunu dahil eder", "New user": "<PERSON>ni kullanıcı", "Users name": "Kullanıcının adı", "User email": "Kullanıcının e-postası", "User password": "Kullanıcının şif<PERSON>i", "Password confirm": "<PERSON><PERSON><PERSON>", "Create": "<PERSON><PERSON>", "A user with that email address already exists": "Bu e-posta adresine sahip bir kullanıcı zaten var", "User": "Kullanıcı", "Role": "Rol", "Admin": "Yönetici", "Are you sure you want to delete?": "<PERSON><PERSON><PERSON> is<PERSON> emin misin", "User edit": "Kullanıcı düzenleme", "Password values do not match": "<PERSON><PERSON><PERSON>leri uyuşmuyor", "Date": "Tarıhı", "View count": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings successfully updated.": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>.", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "\"Web sitesi başlığı\" metnini veya yer alan bir logosunun gösterilip gösterilmeyeceğini kontrol eder: \"/public/logo.png\" (varsayılan olarak)", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "\"gerçek\" olarak ayarlanacak \"API erişimine izin ver\" gerektirir. <PERSON><PERSON><PERSON>, genel API'ya erişmek için gereken erişim jeton. Lütfen değeri tahmin etmek zora ayarlayın.", "Setting to \"true\" will require a user to login before viewing any pages": "\"gerçek\" o<PERSON><PERSON>, her<PERSON><PERSON> bir say<PERSON><PERSON><PERSON> görüntülemeden önce bir kullanıcının oturum açmasını gerektirir", "Select a language": "<PERSON>ir dil seçin", "The language to use for public facing pages. Leave blank for default (English)": "Halka açık sayfalar için kullanılacak dil. Varsayılan değer için boş bırakın (İngilizce)", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Yazarların e-posta adresinin meta içinde görüntülenip görüntülenmediğini kontrol eder. \"Makale meta verilerini göster\" gerçek olması gerekir.", "Import": "İçeri aktar"}