{"Toggle navigation": "トグル ナビゲーション", "Users": "ユーザー", "New": "新規追加", "Edit": "編集", "My account": "マイページ", "Article": "記事", "Settings": "設定", "Files": "ファイル", "Export": "エクスポート", "Cleanup files": "ファイルのクリーンアップ", "Logout": "ログアウト", "Powered by": "Powered by", "Home": "ホーム", "Articles": "記事", "Filter articles": "フィルター", "Filter": "フィルター", "Reset": "リセット", "All": "全て", "Published": "公開済み", "Recent": "最近", "Are you sure you want to delete this article?": "記事を削除しようとしています。本当によろしいですか？", "Search": "検索", "Featured articles": "関連記事", "Top articles": "人気の記事", "Suggest": "記事の提案", "Login": "ログイン", "Please sign in": "サインインしてください", "email address": "メールアドレス", "Password": "パスワード", "Sign in": "サインイン", "Website": "ウェブサイト", "Display": "表示", "Update": "アップデート", "Website title": "ウェブサイトのタイトル", "The title of your website": "ウェブサイトのタイトルを設定します", "Website description": "ウェブサイトの概要", "A short website description when listing the homepage URL in search engines": "検索エンジン等で探したときに表示される、ウェブサイトの簡単な概要を設定します", "Show website logo": "ウェブサイトのロゴを表示", "true": "有効", "false": "無効", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "'ウェブサイトのタイトル'を表示させるか、'/public/logo.png'(デフォルト値)に存在するロゴを表示するかを設定します", "Website context/base URL": "URL体系の設定", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "サイトのURLをルートパス以外に設定したい場合(http://localhost/openkb 等)", "Allow API access": "APIのアクセスを許可", "Whether to allow API access to insert articles - See documentation for further information": "APIアクセスによる記事の更新等を許可するかどうかの設定です - 詳しくは、ドキュメンテーションを参照してください", "API access token": "APIアクセストークン", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "'APIのアクセスを許可する'が有効になっている必要があります。この値に指定されているトークンを使用してAPIにアクセスするため、できるだけ予測できない値を設定して下さい", "Password protect": "パスワードによる保護", "Setting to 'true' will require a user to login before viewing any pages": "この値を有効にすると、全てのページを閲覧するためにログインが必要になります", "Index article body": "記事本文のインデックス", "Whether to add the body of your articles to the search index (requires restart)": "作成した記事を検索用インデックスに追加します(アプリの再起動が必要です)", "Select a theme": "テーマを選択", "The theme to use for public facing pages. Leave blank for default": "サイトのテーマを設定します. 空欄の場合はデフォルトのテーマが使用されます", "Show logon link": "ログイン用リンクの表示", "Whether to show/hide the logon link in the top right of screen": "サイト右上部にログインページへのリンクを表示するかどうかを設定できます", "Date format": "日付フォーマット", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "表示する日付のフォーマットを設定できます. moment.jsを使っているため、書式については右記URLを参照してください: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "記事の提案を許可", "If enabled non authenticated users can submit article suggestions for approval": "この値を有効にすると、ログインしていない状態で記事の提案を行うことが出来るようになります", "Google analytics code": "Googleアナリティクス トラッキングコード", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Googleアナリティクスをページに追加するための設定です. &lt;script&gt; タグを含めて挿入してください", "Allow voting": "投票機能", "Whether to allow users to vote on an article": "記事の質に関する投票ができるようになります", "Show article meta data": "記事のメタデータを表示", "Whether to show article meta data including published date, last updated date, author etc": "記事の投稿日時、最終更新日時、著者等のメタデータが表示されるようになります", "Show author email": "著者のメールアドレスを表示", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "メタデータの著者情報にメールアドレスを含むかどうかを設定できます. '記事のメタデータを表示する'が有効である必要があります", "Article links open new page": "記事内リンクを新しいタブで開く", "Controls whether links within articles open a new page (tab)": "記事内にリンクされているページを新規タブで開くかどうかを設定できます", "Add header anchors": "ヘッダー用アンカーの追加", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "記事のMarkDown内にある特定の行に直接リンクを貼るためのアンカーを追加します", "Enable editor spellchecker": "エディタのスペルチェッカーを有効にする", "Controls whether to enable the editor spellchecker": "スペルチェッカーを有効にするかどうかの設定です", "Allow article versioning": "記事のバージョン管理", "Whether to track article versions with each save of the editor": "記事の編集履歴をバージョン管理するための設定です", "Number of top articles shown on homepage": "トップページに表示する記事の数", "Sets the number of results shown on the home page": "検索結果に表示する記事の数", "Show published date": "公開日時を表示", "Shows the published date next to the results on the homepage and search": "トップページ及び検索結果の記事一覧に、記事の公開日時を表示します", "Show view count": "閲覧回数を表示", "Shows the view count next to the results on the homepage and search": "ップページ及び検索結果の記事一覧に、記事の閲覧回数を表示します", "Update view count when logged in": "ログイン時に閲覧回数を更新", "Updates the view count also when users are logged in": "ユーザーがログインした際に閲覧回数を更新します", "Show featured articles": "関連記事を表示", "Whether to show any articles set to featured in a sidebar": "サイドバーに関連記事を表示します", "Show featured articles when viewing article": "記事を閲覧中に関連記事を表示", "Whether to show any articles set to featured in a sidebar when viewing an article": "記事を閲覧中、サイドバーに関連記事を表示します", "Featured article count": "関連記事の表示数", "The number of featured articles shown": "関連記事の表示数を設定します", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Googleアナリティクスをページに追加するための設定です. <script>タグを含めて挿入してください", "New user": "新規ユーザー", "Users name": "ユーザー名", "User email": "メールアドレス", "User password": "パスワード", "Password confirm": "パスワード(確認)", "Create": "作成", "A user with that email address already exists": "ご指定のメールアドレスに紐付いたユーザーが既に存在します", "User": "ユーザー", "Role": "ロール", "Admin": "管理", "Are you sure you want to delete?": "本当に削除してよろしいですか？", "User edit": "編集", "Password values do not match": "入力されたパスワードが一致しません", "Date": "日時", "View count": "閲覧回数", "Article title": "記事のタイトル", "Status": "記事のステータス", "Draft": "下書き", "Insert": "投稿", "Article body": "記事の本文", "Preview": "プレビュー", "comma, separated, list": "キーワードは, コンマで, 区切られます", "Permalink for the article": "記事のパーマリンクを生成", "Validate": "リンク名の検証", "Generate": "ランダムに生成", "Slug from title": "記事のタイトルを使用する", "Search the knowledge base": "ナレッジベース内を検索", "Was this article helpful?": "この記事は役に立ちましたか？", "Votes": "投票", "Article details": "記事の詳細", "Published date": "公開日時", "Last updated": "最終更新日時", "Share article": "記事のシェア", "Author": "著者", "keyword": "キーワード", "permalink": "パーマリンク", "Delete": "削除", "Reset views": "ビューのリセット", "Reset votes": "投票のリセット", "Featured article": "関連記事", "SEO title": "SEO用のタイトル", "SEO description": "SEO用の記事概要", "Not Found": "見つかりませんでした", "Select file": "ファイルを選択", "Upload directory": "アップロード先ディレクトリ", "Upload file": "ファイルをアップロード", "Tip": "Tip", "To insert an image right click the link, copy the link address and add it to your Markdown": "アップロード済みの画像を記事に挿入する場合は、URLをコピーし、MarkDownに追加して下さい", "New directory": "新規ディレクトリ", "Multiple directories can be created using a \"/\" separator": "複数階層のディレクトリを作成する場合は\"/\"でディレクトリ名を区切って下さい", "Article successfully deleted": "記事が正常に削除されました", "Keywords": "キーワード", "Import": "インポート", "New article successfully created": "新規の記事が作成されました", "Successfully saved": "記事が正常に保存されました", "Permalink": "パーマリンク", "Access denied. Check password and try again.": "アクセスが拒否されました。パスワードを確認し、再度ログインしてください。", "Select a language": "言語を選択", "The language to use for public facing pages. Leave blank for default (English)": "このサイトで使用する言語を設定します。空欄を選択すると英語が自動選択されます。", "Settings successfully updated.": "設定が正常に更新されました。", "Results for": "検索結果", "No results found": "結果が見つかりませんでした", "Access denied": "アクセスが拒否されました", "User account inserted": "ユーザーアカウントが挿入されました", "Directory successfully created": "ディレクトリが正常に作成されました", "File uploaded successfully": "ファイルが正常にアップロードされました", "Allow Mermaid": "Mermaidを有効にする", "Whether to enable ": "次のモジュールを有効にします -", "Allow MathJax": "MathJaxを有効にする", "MathJax input mode": "MathJaxの入力方式", "Configure the mode MathJax operates in - ": "MathJaxで使用する演算子のモードを設定します - ", "Setup": "セットアップ", "Email address": "メールアドレス", "Confirm password": "パスワードの確認", "Complete setup": "セットアップ完了", "404 - Page not found": "404 - Page not found", "Versions": "バージョン", "Edit reason": "編集理由", "Previous versions": "以前のバージョン", "Topic": "トピック", "Suggestion successfully processed": "提案が正常に送信されました", "Style": "スタイル", "All unused files have been removed": "使用されていない全てのファイルが削除されました", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "\"ウェブサイトのタイトル\"を表示させるか、\"/public/logo.png\"(デフォルト値)に存在するロゴを表示するかを設定します", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "\"APIのアクセスを許可する\"が有効になっている必要があります。この値に指定されているトークンを使用してAPIにアクセスするため、できるだけ予測できない値を設定して下さい", "Setting to \"true\" will require a user to login before viewing any pages": "この値を有効にすると、全てのページを閲覧するためにログインが必要になります", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "メタデータの著者情報にメールアドレスを含むかどうかを設定できます. \"記事のメタデータを表示する\" が有効である必要があります"}