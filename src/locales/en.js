{" Powered by": " Powered by", "Parse error on line 34:\n...                    {{/if}}            \n-----------------------^\nExpecting 'EOF', got 'OPEN_ENDBLOCK'": "Parse error on line 34:\n...                    {{/if}}            \n-----------------------^\nExpecting 'EOF', got 'OPEN_ENDBLOCK'", "string-strip-html/stripHtml(): [THROW_ID_01] Input must be string! Currently it's: undefined, equal to:\nundefined": "string-strip-html/stripHtml(): [THROW_ID_01] Input must be string! Currently it's: undefined, equal to:\nundefined", "Not Found": "Not Found", "Parse error on line 64:\n...les/{{this.subtopic.replace(/\\s/g, \"\")}}\n-----------------------^\nExpecting 'ID', got 'INVALID'": "Parse error on line 64:\n...les/{{this.subtopic.replace(/\\s/g, \"\")}}\n-----------------------^\nExpecting 'ID', got 'INVALID'", "Parse error on line 64:\n...les/{{this.subtopic.replace(/\\s/g, '')}}\n-----------------------^\nExpecting 'ID', got 'INVALID'": "Parse error on line 64:\n...les/{{this.subtopic.replace(/\\s/g, '')}}\n-----------------------^\nExpecting 'ID', got 'INVALID'", "Missing helper: \"nospace\"": "Missing helper: \"nospace\"", "Missing helper: \"noSpace\"": "Missing helper: \"noSpace\"", "Missing helper: \"traslate\"": "Missing helper: \"traslate\"", "string.replace is not a function": "string.replace is not a function", "Search Club is not set. Please set the club in your profile.": "Search Club is not set. Please set the club in your profile.", "Topic not found or not available for your club.": "Topic not found or not available for your club.", "(lookupProperty(...) || (depth0 && lookupProperty(...)) || alias4).call is not a function": "(lookupProperty(...) || (depth0 && lookupProperty(...)) || alias4).call is not a function", "Missing helper: \"club\"": "Missing helper: \"club\"", "ENOENT: no such file or directory, open '/var/openKB/views/layouts/wiki-themes/12rnd/views/layouts/layout.hbs'": "ENOENT: no such file or directory, open '/var/openKB/views/layouts/wiki-themes/12rnd/views/layouts/layout.hbs'", "ENOENT: no such file or directory, open '/wiki-themes/12rnd/views/layouts/layout.hbs'": "ENOENT: no such file or directory, open '/wiki-themes/12rnd/views/layouts/layout.hbs'", "ENOENT: no such file or directory, open '/public/themes/12rnd/views/layouts/layout.hbs'": "ENOENT: no such file or directory, open '/public/themes/12rnd/views/layouts/layout.hbs'", "ENOENT: no such file or directory, open '/var/openKB/views/layouts/public/themes/12rnd/views/layouts/layout.hbs'": "ENOENT: no such file or directory, open '/var/openKB/views/layouts/public/themes/12rnd/views/layouts/layout.hbs'", "Assignment to constant variable.": "Assignment to constant variable.", "Missing helper: \"relative\"": "Missing helper: \"relative\"", "Access denied": "Access denied", "Parse error on line 86:\n...er.min.js\"></script>\n-----------------------^\nExpecting 'OPEN_INVERSE_CHAIN', 'INVERSE', 'OPEN_ENDBLOCK', got 'EOF'": "Parse error on line 86:\n...er.min.js\"></script>\n-----------------------^\nExpecting 'OPEN_INVERSE_CHAIN', 'INVERSE', 'O<PERSON>EN_ENDBLOCK', got 'EOF'", "each doesn't match if - 76:27": "each doesn't match if - 76:27", "Parse error on line 83:\n...ternary this.search == '' this.search \"(\n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'OPEN_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'EQUALS'": "Parse error on line 83:\n...ternary this.search == '' this.search \"(\n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'O<PERSON><PERSON>_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'EQUALS'", "Parse error on line 83:\n...ernary (this.search == '') this.search \"\n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'OPEN_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'EQUALS'": "Parse error on line 83:\n...ernary (this.search == '') this.search \"\n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'O<PERSON><PERSON>_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'EQUALS'", "Parse error on line 83:\n...{ternary this.search=='' this.search \"(b\n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'OPEN_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'EQUALS'": "Parse error on line 83:\n...{ternary this.search=='' this.search \"(b\n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'O<PERSON><PERSON>_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'EQUALS'", "Parse error on line 89:\n....count / totalCount * 100}}</div>      \n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'OPEN_SEXPR', 'CLOSE_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'INVALID'": "Parse error on line 89:\n....count / totalCount * 100}}</div>      \n-----------------------^\nExpecting 'CLOSE_RAW_BLOCK', 'CLOSE', 'CLOSE_UNESCAPED', 'O<PERSON><PERSON>_SEXPR', 'CL<PERSON><PERSON>_SEXPR', 'ID', 'OPEN_BLOCK_PARAMS', 'STRING', 'NUMBER', 'BOOLEAN', 'UNDEFINED', 'NULL', 'DATA', 'SEP', got 'INVALID'", "Parse error on line 82:\n...{#if result.console.log(updated)}}     \n-----------------------^\nExpecting 'ID', got 'INVALID'": "Parse error on line 82:\n...{#if result.console.log(updated)}}     \n-----------------------^\nExpecting 'ID', got 'INVALID'", "Parse error on line 96:\n...                    {{/if}}            \n-----------------------^\nExpecting 'EOF', got 'OPEN_ENDBLOCK'": "Parse error on line 96:\n...                    {{/if}}            \n-----------------------^\nExpecting 'EOF', got 'OPEN_ENDBLOCK'", "getaddrinfo ENOTFOUND openkb-db": "getaddrinfo ENOTFOUND openkb-db", "Settings successfully updated.": "Settings successfully updated.", "Topic not found or not available for your facility or user. Please contact your administrator to gain access to this resource.": "Topic not found or not available for your facility or user. Please contact your administrator to gain access to this resource."}