{"Toggle navigation": "Activar / Desactivar navegación", "Users": "Usuarios", "New": "Nuevo", "Edit": "<PERSON><PERSON>", "My account": "Mi cuenta", "Article": "<PERSON><PERSON><PERSON><PERSON>", "Settings": "Configuración", "Files": "Archivos", "Export": "Exportar", "Cleanup files": "Limpiar archivos", "Logout": "Desconectar", "Powered by": "Potenciado por", "Home": "<PERSON><PERSON>o", "Articles": "<PERSON><PERSON><PERSON>", "Filter articles": "<PERSON><PERSON><PERSON> articulos", "Filter": "Filtrar", "Reset": "Recargar", "All": "Todos", "Published": "Publicado", "Recent": "Reciente", "Are you sure you want to delete this article?": "¿Está seguro de que quiere eliminar este artículo?", "Search": "Buscar", "Featured articles": "Articulos destacados", "Top articles": "<PERSON><PERSON><PERSON> artí<PERSON>", "Suggest": "Sugerir", "Login": "Acceder", "Please sign in": "Por favor indentifíquese", "email address": "correo eletrónico", "Password": "Contraseña", "Sign in": "Registrarse", "Website": "Pagina web", "Display": "Presentación", "Update": "Actualizar", "Website title": "<PERSON><PERSON><PERSON><PERSON> de la Página", "The title of your website": "El título de tu página", "Website description": "Descripción de la página", "A short website description when listing the homepage URL in search engines": "Una breve descripción de la pagina para cuando la URL de la página de inicio se liste en motores de búsqueda", "Show website logo": "Mostrar logo de la página", "true": "activo", "false": "inactivo", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "Controla si se muestra el 'Título de la web' o el logo localizado en '/public/logo.png' (por defecto)", "Website context/base URL": "URL contexto/base del Sitio Web", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Permite que la web se ejecute en un directorio direfente al raiz. Ej: http://127.0.0.1:4444/openkb/", "Allow API access": "Permitir acceso a la API", "Whether to allow API access to insert articles - See documentation for further information": "Permitir insertar articulos a través de la API - Lee la documentación para más información", "API access token": "Toquen de acceso a la API", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Requiere 'Permitir acceso a la API' activada. El valor es el token de acceso necesario para acceder a la API pública. Por favor, establece una dificil de adivinar", "Password protect": "Protección con contraseña", "Setting to 'true' will require a user to login before viewing any pages": "Establecerlo como 'activo' requiere que un usuario acceda antes de ver ninguna página", "Index article body": "Indexar contenido del artículo", "Whether to add the body of your articles to the search index (requires restart)": "Añadir el contenido del artículo al índice de búsqueda (Requiere reinicio)", "Select a theme": "Seleccionar un tema", "The theme to use for public facing pages. Leave blank for default": "El tema para mostrar las página públicas. Dejalo en blanco para usar el por defecto", "Show logon link": "Mostrar enlace de acceso", "Whether to show/hide the logon link in the top right of screen": "Mostrar/esconder el enlace para acceder en la esquina superior derecha de la pantalla", "Date format": "Formato de fecha", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Establece el formato de fecha global. Usa el formato de fecha de moment.js, más información aqui: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "<PERSON><PERSON><PERSON>ger<PERSON> artículos", "If enabled non authenticated users can submit article suggestions for approval": "Si se activa, los usuarios no autenticados pueden enviar sugenrecias de artículos", "Google analytics code": "Código de Google analytics", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Añade Google Analytics a las página públicas. Incluye el código completo de google junto con las etiquetas &lt;script&gt;", "Allow voting": "<PERSON><PERSON><PERSON> votar", "Whether to allow users to vote on an article": "Permitir a los usuarios votar un artículo", "Show article meta data": "Motrar metadatos del artículo", "Whether to show article meta data including published date, last updated date, author etc": "Motrar metadatos del artículo incluyendo fecha de publicación, fecha de última actualización, autor etc...", "Show author email": "Mostrar correo eletrónico del autor", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Controla si se muestra el email de los artículos en los metadatos. Necesita  que 'Mostrar metadatos del artículo' esté activo", "Article links open new page": "<PERSON><PERSON><PERSON> enlaces de un artículo en una nueva pestaña", "Controls whether links within articles open a new page (tab)": "Controla si se abren los enlaces en una nueva pestaña", "Add header anchors": "<PERSON><PERSON><PERSON> cabecera", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "<PERSON><PERSON><PERSON> anchors de HTML a todas las etiqutas heading para enlazar entre artículos o enlaces director desde o<PERSON>s artículos", "Enable editor spellchecker": "Activar comprobación ortográfica del editor", "Controls whether to enable the editor spellchecker": "Controla si se activa la comprobación ortográfica del editor", "Allow article versioning": "Permitir <PERSON> artículos", "Whether to track article versions with each save of the editor": "Llevar un histórico de las versiones de un artículo con cada guardado del editor", "Number of top articles shown on homepage": "Número de artículos motrados en la página de inicio", "Sets the number of results shown on the home page": "Establece el número de resultados motrados en la página de inicio", "Show published date": "Mostrar fecha de publicación", "Shows the published date next to the results on the homepage and search": "Muestra la fecha de publicación junto con los resultados en la pagína de incio y en las búsquedas", "Show view count": "Mostrar número de visitas", "Shows the view count next to the results on the homepage and search": "Muestra el número de visitas junto con los resultados en la página de inicio y en las búsquedas", "Update view count when logged in": "Actualizar contador de visitas cuando han accedido", "Updates the view count also when users are logged in": "Actualizar contador de visitas también cuando los usuarios han accedido", "Show featured articles": "Mostrar artículos <PERSON>", "Whether to show any articles set to featured in a sidebar": "Mostrar artículos marcados como destacados en la barra lateral", "Show featured articles when viewing article": "Mostrar artículos destacados incluso desde la página de un artículo", "Whether to show any articles set to featured in a sidebar when viewing an article": "Mostrar artículos marcados como destacados incluso desde la página de un artículo", "Featured article count": "Número de artículos destacados", "The number of featured articles shown": "Número de artículos destacados que se van a mostrar", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Añade Google Analytics a las página públicas. Incluye el código completo de google junto con las etiquetas <script>", "New user": "Nuevo usuario", "Users name": "Nombre de usuario", "User email": "Correo electrónico de usuario", "User password": "Contraseña de usuario", "Password confirm": "Confirmar con<PERSON>", "Create": "<PERSON><PERSON><PERSON>", "A user with that email address already exists": "Ya existe un usuario con ese correo electrónico", "User": "Usuario", "Role": "Rol", "Admin": "Administrador", "Are you sure you want to delete?": "¿Estás seguro de que quieres eliminarlo?", "User edit": "<PERSON>ar usuario", "Password values do not match": "Las contraseñas no son iguales", "Date": "<PERSON><PERSON>", "View count": "Contador de <PERSON>as", "Settings successfully updated.": "Ajuestes actualizados exitosamente.", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "Controla si se muestra el texto \"Título del Sitio\" o un logo ubicado en: \"/public/logo.png\" (por defecto)", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "Requiere \"Permitir acceso a la API\" para establecer el valor \"Sí\". El valor es el toquen de acceso para ingresar a la API. Por favor establezca un valor dificil de adivinar", "Setting to \"true\" will require a user to login before viewing any pages": "Establecer su valor en \"Sí\" requerirá que el usuario inicie sesión antes de ver cualquier página", "Select a language": "Seleccionar un idioma", "The language to use for public facing pages. Leave blank for default (English)": "El idioma a utilizar en las páginas de cara al público. Déjelo en blanco por defecto(Inglés)", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Controla si el correo electrónico del autor es mostrado en los metadatos. Requiere que \"Mostrar lor metadatos del artículo\" sea verdadero", "Import": "Importar"}