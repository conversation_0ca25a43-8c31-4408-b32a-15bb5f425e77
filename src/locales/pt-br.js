{"Toggle navigation": "Toggle navigation", "Users": "Usuários", "New": "Novo", "Edit": "<PERSON><PERSON>", "My account": "Minha conta", "Article": "Artigo", "Settings": "Configurações", "Files": "<PERSON>r<PERSON><PERSON>", "Export": "Exportar", "Cleanup files": "Limpar arqui<PERSON>", "Logout": "<PERSON><PERSON>", "Powered by": "Powered by", "Home": "Início", "Articles": "Artigos", "Filter articles": "Filtrar artigos", "Filter": "Filtrar", "Reset": "Limpar", "All": "Todos", "Published": "Publicado", "Recent": "<PERSON><PERSON>", "Are you sure you want to delete this article?": "Você realmente quer excluir este artigo?", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Featured articles": "Artigos em destaque", "Top articles": "Art<PERSON>s mais vistos", "Suggest": "Sugerir", "Login": "Entrar", "Please sign in": "Por favor entre", "email address": "endereço de email", "Password": "<PERSON><PERSON>", "Sign in": "Entrar", "Website": "Website", "Display": "<PERSON><PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Website title": "Título do site", "The title of your website": "O titulo do seu site openBK", "Website description": "Descrição do site", "A short website description when listing the homepage URL in search engines": "Uma descrição curta para exibição do seu site em sites de busca", "Show website logo": "Exibir logotipo", "true": "sim", "false": "não", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)", "Website context/base URL": "URL Base / Contexto", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Permite que o site execute em um diretório diferente do raiz. Ex: http://127.0.0.1:4444/openkb/", "Allow API access": "Permitir acesso via API", "Whether to allow API access to insert articles - See documentation for further information": "Indica se permite utilizar a API para inserir artigos. Veja detalhes na documentação para maiores informações", "API access token": "Token de acesso da <PERSON>", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value", "Password protect": "<PERSON>tegi<PERSON> por senha", "Setting to 'true' will require a user to login before viewing any pages": "Definindo como 'sim' irá exigir que o usuário entre no sistema antes de visualizar qualquer página", "Index article body": "Indexar conteúdo do artigo", "Whether to add the body of your articles to the search index (requires restart)": "Whether to add the body of your articles to the search index (requires restart)", "Select a theme": "Selecione um tema", "The theme to use for public facing pages. Leave blank for default": "O tema para usar nas páginas publicas. Deixe em branco para usar o padrão", "Show logon link": "Mostrar link de login", "Whether to show/hide the logon link in the top right of screen": "Indica se o link de login deve ser mostrado ou não no canto superior direito da tela", "Date format": "Formato para data", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "<PERSON><PERSON><PERSON> de artigos", "If enabled non authenticated users can submit article suggestions for approval": "Se habilitado usuários não autenticados podem enviar artigos para aprovação", "Google analytics code": "Código do Google analytics", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags", "Allow voting": "Allow voting", "Whether to allow users to vote on an article": "Whether to allow users to vote on an article", "Show article meta data": "Mostrar informações do artigo", "Whether to show article meta data including published date, last updated date, author etc": "Whether to show article meta data including published date, last updated date, author etc", "Show author email": "Mostrar email do autor", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true", "Article links open new page": "<PERSON><PERSON>r links de artigos em uma nova página", "Controls whether links within articles open a new page (tab)": "Controls whether links within articles open a new page (tab)", "Add header anchors": "Add header anchors", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles", "Enable editor spellchecker": "Enable editor spellchecker", "Controls whether to enable the editor spellchecker": "Controls whether to enable the editor spellchecker", "Allow article versioning": "Permitir versionamento de artigos", "Whether to track article versions with each save of the editor": "Whether to track article versions with each save of the editor", "Number of top articles shown on homepage": "Número de artigos mais visualizados na página inicial", "Sets the number of results shown on the home page": "Define o número de resultados mostrados na página inicial", "Show published date": "Mostrar data da publicação", "Shows the published date next to the results on the homepage and search": "Mostra a data da publicação junto aos resultados na pesquisa da página inicial", "Show view count": "Mostrar número de visualizações", "Shows the view count next to the results on the homepage and search": "Shows the view count next to the results on the homepage and search", "Update view count when logged in": "Atualizar número de visualizações quando estiver logado", "Updates the view count also when users are logged in": "Atualiza o número de visualizações mesmo quando usuários estão logados", "Show featured articles": "Mostrar artigos em destaque", "Whether to show any articles set to featured in a sidebar": "Whether to show any articles set to featured in a sidebar", "Show featured articles when viewing article": "Show featured articles when viewing article", "Whether to show any articles set to featured in a sidebar when viewing an article": "Whether to show any articles set to featured in a sidebar when viewing an article", "Featured article count": "Featured article count", "The number of featured articles shown": "The number of featured articles shown", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags", "New user": "Novo usuário", "Users name": "Nome do usuário", "User email": "Email do usuário", "User password": "Senha do usuário", "Password confirm": "Confirmar <PERSON><PERSON><PERSON>", "Create": "<PERSON><PERSON><PERSON>", "A user with that email address already exists": "Um usuário com este e-mail já existe", "User": "<PERSON><PERSON><PERSON><PERSON>", "Role": "Perfil", "Admin": "Administrador", "Are you sure you want to delete?": "Tem certeza que deseja excluir?", "User edit": "<PERSON><PERSON>", "Password values do not match": "Senha não confere", "Date": "Data", "View count": "Ver contagem", "Article title": "Título do artigo", "Status": "Estado", "Draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Insert": "Inserir", "Article body": "Corpo do artigo", "Preview": "Pré Visualizar", "comma, separated, list": "lista, separada, por, virgulas", "Permalink for the article": "Link permanente do artigo", "Validate": "Validar", "Generate": "<PERSON><PERSON><PERSON>", "Slug from title": "Slug from title", "Search the knowledge base": "Pesquisar na base de conhecimento", "Was this article helpful?": "Este artigo foi útil?", "Votes": "Votos", "Article details": "Detalhes do artigo", "Published date": "Publicado em", "Last updated": "Última atualização", "Share article": "Compartilhar artigo", "Author": "Autor", "keyword": "palavra chave", "permalink": "link permanente", "Delete": "Excluir", "Reset views": "Reiniciar visualizações", "Reset votes": "Reiniciar votos", "Featured article": "Artigo em destaque", "SEO title": "Título SEO", "SEO description": "Descrição SEO", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "Exige que a opção \"Permitir acesso via API\" seja definida como \"sim\". O valor é o token requerido para realizar o acesso através da API publica. Por favor, defina um valor seguro", "Setting to \"true\" will require a user to login before viewing any pages": "Configurar como \"sim\" irá exigir que o usuário entre no sistema para visualizar qualquer página", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Controla onde o endereço de email do autor é exibido nas informações. Requer a opção \"Mostrar informações do artigo\" habilitada", "Not Found": "Não Encontrado", "Select file": "Selecionar arquivo", "Upload directory": "Diretório de upload", "Upload file": "Enviar arquivo", "Tip": "Dica", "To insert an image right click the link, copy the link address and add it to your Markdown": "Para inserir uma imagem clique com o botão direito no link, copie o endereço e adicione ao Markdown", "New directory": "Novo diretório", "Multiple directories can be created using a \"/\" separator": "Multiplos diretórios podem ser criados usando uma \"/\" como separador", "Article successfully deleted": "Artigo excluído com sucesso", "Keywords": "Palavras Chave", "Import": "Importar", "New article successfully created": "Novo artigo criado com sucesso", "Successfully saved": "Salvo com sucesso", "Permalink": "<PERSON>e", "Access denied. Check password and try again.": "Acesso proibido. Verifique a senha e tente novamente.", "Select a language": "Selecione um idioma", "The language to use for public facing pages. Leave blank for default (English)": "O idioma a ser usado para páginas publicas. Deixe em branco para usar o padrão (Inglês)", "Settings successfully updated.": "Configurações atualizadas com sucesso.", "Results for": "Resultados para", "No results found": "Nenhum resultado encontrado", "Access denied": "<PERSON><PERSON> proibido", "User account inserted": "Conta de usuário inserida", "Directory successfully created": "Diretório criado com sucesso", "File uploaded successfully": "Arquivo enviado com sucesso", "Allow Mermaid": "<PERSON><PERSON><PERSON>", "Whether to enable ": "Whether to enable ", "Allow MathJax": "Allow MathJax", "MathJax input mode": "MathJax input mode", "Configure the mode MathJax operates in - ": "Configure the mode MathJax operates in - ", "Setup": "Configurar", "Email address": "Endereço de Email", "Confirm password": "Confirmar <PERSON><PERSON><PERSON>", "Complete setup": "Finalizar configuração", "This article is locked": "Este artigo está travado", "Please enter the password to gain access": "Por favor informe a senha para acessar", "Authenticate": "Autenticar", "Password incorrect. Please try again.": "Senha inválida. Por favor tente novamente.", "Versions": "Versõ<PERSON>", "Edit reason": "Editar motivo", "Previous versions": "Versões anteriores"}