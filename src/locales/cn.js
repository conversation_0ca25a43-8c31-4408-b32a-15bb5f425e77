{"Toggle navigation": "切换导航", "Users": "用户", "New": "新建", "Edit": "编辑", "My account": "我的账户", "Article": "文章", "Settings": "设置", "Files": "文件", "Export": "导出", "Cleanup files": "清除文件", "Logout": "注销", "Powered by": "Powered by", "Home": "首页", "Articles": "文章", "Filter articles": "过滤器", "Filter": "过滤", "Reset": "重置", "All": "全部", "Published": "已发布", "Recent": "近期", "Are you sure you want to delete this article?": "确定删除文章?", "Search": "搜索", "Featured articles": "推荐文章", "Top articles": "热门文章", "Suggest": "建议", "Login": "登录", "Please sign in": "请登录", "email address": "邮箱", "Password": "密码", "Sign in": "登录", "Website": "网站", "Display": "显示", "Update": "更新", "Website title": "网站标题", "The title of your website": "您的网站标题", "Website description": "网站简介", "A short website description when listing the homepage URL in search engines": "在搜索引擎列表中显示的网站简介", "Show website logo": "显示网站LOGO", "true": "开", "false": "关", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "控制显示网站抬头文字或是显示位于网站文件夹: '/public/logo.png' (默认)的网站图标", "Website context/base URL": "Website context/base URL", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/", "Allow API access": "允许API链接", "Whether to allow API access to insert articles - See documentation for further information": "是否允许使用API插入文章 - 更多信息请查看文档", "API access token": "API链接Token", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "需要 '允许API链接' 项设置为 '开'. 值为访问接口所需的token。 请设置一个难以猜测的值。", "Password protect": "需要密码访问网站", "Setting to 'true' will require a user to login before viewing any pages": "当设置为 '开' 时，用户需要登入后才能浏览页面", "Index article body": "索引文章内文", "Whether to add the body of your articles to the search index (requires restart)": "是否将文章内文加入索引（需要重启openKB生效）", "Select a theme": "选择主题", "The theme to use for public facing pages. Leave blank for default": "公用界面使用的主题，留白以使用默认主题", "Show logon link": "显示登入链接", "Whether to show/hide the logon link in the top right of screen": "是否在页面右上角显示登入链接", "Date format": "日期格式", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "设置全局时间格式。 使用 moment.js 日期格式, 详情请见: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "允许建议文章", "If enabled non authenticated users can submit article suggestions for approval": "是否允许非注册用户提交对新内容的建议", "Google analytics code": "Google analytics code", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags", "Allow voting": "允许投票", "Whether to allow users to vote on an article": "是否允许用户对文章进行投票", "Show article meta data": "显示文章信息", "Whether to show article meta data including published date, last updated date, author etc": "显示文章的发布时间、最后更新时间、作者以及其他信息", "Show author email": "显示作者邮箱", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "控制是否在文章底部显示文章作者的邮箱。需要 '显示文章信息' 设置为开", "Article links open new page": "文章在新页面开启", "Controls whether links within articles open a new page (tab)": "控制文章是否在新页面（选项卡）中开启", "Add header anchors": "Add header anchors", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles", "Enable editor spellchecker": "开启编辑者拼写检查", "Controls whether to enable the editor spellchecker": "是否开启拼写检查", "Allow article versioning": "开启文章版本追踪", "Whether to track article versions with each save of the editor": "当编辑者更新保存文章时，将文档版本号更新", "Number of top articles shown on homepage": "首页显示文章数量", "Sets the number of results shown on the home page": "首页显示的文章数量设置", "Show published date": "显示文章发布时间", "Shows the published date next to the results on the homepage and search": "是否在首页以及搜索结果后面显示每篇文章发布时间", "Show view count": "显示阅读数", "Shows the view count next to the results on the homepage and search": "是否在首页以及搜索结果后面显示每篇文章的阅读数", "Update view count when logged in": "登入后更新阅读数", "Updates the view count also when users are logged in": "在用户登入后同时也更新阅读数", "Show featured articles": "显示专题文章", "Whether to show any articles set to featured in a sidebar": "在侧边栏显示任何设置为专题的文章", "Show featured articles when viewing article": "在文章页阅读页面显示专题文章", "Whether to show any articles set to featured in a sidebar when viewing an article": "在浏览文章页面的侧边栏显示设置为专题的文章", "Featured article count": "专题文章数", "The number of featured articles shown": "首页专题文章显示数目", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "添加Google分析脚本到公开页面，包含<script>标签的所有脚本", "New user": "新用户", "Users name": "用户名", "User email": "用户邮箱", "User password": "密码", "Password confirm": "确认密码", "Create": "创建用户", "A user with that email address already exists": "已有用户使用此邮箱地址", "User": "用户", "Role": "角色", "Admin": "管理员", "Are you sure you want to delete?": "确定删除?", "User edit": "编辑用户", "Password values do not match": "输入的密码不一致", "Date": "日期", "View count": "查看统计", "Article title": "文档标题", "Status": "状态", "Draft": "草稿", "Insert": "插入", "Article body": "内容", "Preview": "预览", "comma, separated, list": "关键字请用英文逗号隔开，可以有多个关键字", "Permalink for the article": "文章静态链接", "Validate": "验证链接", "Generate": "生成链接", "Slug from title": "从标题生成", "Search the knowledge base": "从知识库搜索", "Was this article helpful?": "这个文章是否有帮助?", "Votes": "投票数", "Article details": "文章详情", "Published date": "发布日期", "Last updated": "最后发布时间", "Share article": "共享文章", "Author": "作者", "keyword": "关键字", "permalink": "静态链接", "Delete": "删除文章", "Reset views": "重置浏览数", "Reset votes": "重置投票数", "Featured article": "主题文章", "SEO title": "SEO标题", "SEO description": "SEO简介", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "显示\"网站抬头文字\" 或是默认位置为: \"/public/logo.png\" 的网站LOGO", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "需要 \"允许接口接入\" 项设置为 \"开\". 值为访问接口所需的token。 请设置一个难以猜测的值。", "Setting to \"true\" will require a user to login before viewing any pages": "当设置为 \"开\" 时，用户需要登入后才能浏览页面", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "控制是否在文章底部显示文章撰写者的邮箱。需要 \"显示文章信息\" 项设置为 \"开\"", "Not Found": "文件未找到", "Select file": "选择文件", "Upload directory": "上传资料架", "Upload file": "上传文件", "Tip": "提示", "To insert an image right click the link, copy the link address and add it to your Markdown": "要在文档中加入图片，右键点击图片链接，选择'复制链接网址'并将其粘贴到你的 Markdown", "New directory": "新建文件夹", "Multiple directories can be created using a \"/\" separator": "建立多个文件夹请用 \"/\" 作为两个文件夹的分隔符.", "Article successfully deleted": "文章成功删除", "Keywords": "关键字群", "Import": "导入文件", "New article successfully created": "新文章成功建立", "Successfully saved": "文章保存成功", "Permalink": "静态链接", "Access denied. Check password and try again.": "访问拒绝。 请检查密码并重试。", "Select a language": "选择语言", "The language to use for public facing pages. Leave blank for default (English)": "公共页面的显示语言，留白即为默认语言（英语）", "Settings successfully updated.": "设置成功", "Results for": "关键字的搜索结果", "No results found": "未找到相关文章", "Access denied": "访问被拒绝", "Setup": "设置", "Email address": "邮箱地址", "Confirm password": "确认密码", "Complete setup": "完成设置", "User account inserted": "用户账户已创建", "Style": "样式", "Allow Mermaid": "Allow Mermaid", "Whether to enable ": "Whether to enable ", "Allow MathJax": "Allow MathJax", "MathJax input mode": "MathJax input mode", "Configure the mode MathJax operates in - ": "Configure the mode MathJax operates in - ", "Directory successfully created": "文件夹创建成功", "All unused files have been removed": "所有未使用的文件已删除"}