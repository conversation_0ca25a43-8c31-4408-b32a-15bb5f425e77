{"Toggle navigation": "Переключение навигации", "Users": "Пользователи", "New": "Новый", "Edit": "Изменить", "My account": "Моя учетная запись", "Article": "Статья", "Settings": "Настройки", "Files": "Файлы", "Export": "Экспорт", "Logout": "Выход", "Powered by": "Сделано в", "Home": "Главная", "Articles": "Статьи", "Filter articles": "Фильтровать статьи", "Filter": "Фильтр", "Reset": "Сброс", "All": "Все", "Published": "Опубликовано", "Recent": "Последние", "Are you sure you want to delete this article?": "Вы уверены, что хотите удалить эту статью?", "Search": "Поиск", "Featured articles": "Избранные статьи", "Top articles": "Луч<PERSON>ие статьи", "Suggest": "Предложить", "Login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Please sign in": "Пожалуйста, войдите", "email address": "адрес электронной почты", "Password": "Пароль", "Sign in": "Войти", "Website": "Веб-сайт", "Display": "Дис<PERSON><PERSON>ей", "Update": "Обновить", "Website title": "Название веб-сайта", "The title of your website": "Название вашего сайта", "Website description": "Описание веб-сайта", "A short website description when listing the homepage URL in search engines": "Краткое описание веб-сайта при перечислении URL главной страницы в поисковых системах", "Show website logo": "Показать логотип сайта", "true": "true", "false": "false", "Controls whether to show the 'Website title' text or a logo located: '/public/logo.png' (by default)": "Управляет отображением текста «Название сайта» или логотипа, расположенного: '/public/Logo.png' (по умолчанию) ", "Website context/base URL": "Базовый URL сайта", "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/": "Позволяет запускать веб-сайт с другого пути, например: http: // 127.0.0.1:4444/openkb/", "Allow API access": "Разрешить доступ к API", "Whether to allow API access to insert articles - See documentation for further information": "Разрешить ли API доступ к статьям вставки - см. Документацию для получения дополнительной информации»", "API access token": "Токен доступа API", "Requires 'Allow API access' to be set to 'true'. The value is the access token required to access the public API. Please set to a hard to guess value": "Если активировано «Разрешить доступ API», то требуется значение - токен доступа. Установите значение посложнее", "Password protect": "Защита паролем", "Setting to 'true' will require a user to login before viewing any pages": "При установке значения «Да» пользователю необходимо будет войти в систему перед просмотром каких-либо страниц", "Index article body": "Добавление содержимого статьи в индекс", "Whether to add the body of your articles to the search index (requires restart)": "Добавлять ли тело ваших статей к индексу поиска (требуется перезагрузка)", "Select a theme": "Выберите тему", "The theme to use for public facing pages. Leave blank for default": "Тема для публичных страниц. Оставьте поле пустым", "Show logon link": "Показать ссылку для входа в систему", "Whether to show/hide the logon link in the top right of screen": "Показывать/скрывать ссылку входа в систему в правом верхнем углу экрана", "Date format": "Формат даты", "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying": "Устанавливает форматирование глобальной даты. Использует форматирование даты в формате moment.js, см. здесь: http://momentjs.com/docs/#/displaying", "Article suggestions allowed": "Предлагаемые статьи разрешены", "If enabled non authenticated users can submit article suggestions for approval": "Если разрешенные пользователи, не прошедшие проверку подлинности, могут отправлять предложения на утверждение", "Google analytics code": "Код Google Analytics", "Adds Google Analytics to public facing pages. Include the entire code from Google including the &lt;script&gt; tags": "Добавляет Google Analytics к общедоступным страницам. Включите весь код от Google, включая теги &lt;script&gt;", "Allow voting": "Разрешить голосование", "Whether to allow users to vote on an article": "Разрешить пользователям голосовать за статью", "Show article meta data": "Показывать метаданные", "Whether to show article meta data including published date, last updated date, author etc": "Показывать метаданные статьи, включая опубликованную дату, дату последнего обновления, автора и т.д.", "Show author email": "Показать автора электронной почты", "Controls whether the authors email address is displayed in the meta. Requires 'Show article meta data' to be true": "Управляет отображением электронного адреса авторов в мета-представлении.  Переключатель 'Показывать метаданные' должен быть включен", "Article links open new page": "Ссылки на статьи открывают новую страницу", "Controls whether links within articles open a new page (tab)": "Открывают ли ссылки внутри статей новую страницу (вкладку)", "Add header anchors": "Добавить привязки заголовка", "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles": "Следует ли добавлять HTML якоря ко всем тегам заголовков для связывания внутри статей или прямой ссылки с других статей", "Enable editor spellchecker": "Включить проверку орфографии в редакторе", "Controls whether to enable the editor spellchecker": "Управляет включением проверки орфографии редактора", "Allow article versioning": "Разрешить управление версиями статьи", "Whether to track article versions with each save of the editor": "Следует ли отслеживать версии статей при каждом сохранении редактора", "Number of top articles shown on homepage": "Количество лучших статей, отображаемых на главной странице", "Sets the number of results shown on the home page": "Устанавливает количество результатов, отображаемых на домашней странице", "Show published date": "Показать опубликованную дату", "Shows the published date next to the results on the homepage and search": "Показывает опубликованную дату рядом с результатами на главной странице и поиске", "Show view count": "Показывать количество просмотров", "Shows the view count next to the results on the homepage and search": "Показывает количество просмотров рядом с результатами на главной странице и поиске", "Update view count when logged in": "Обновить число просмотров при входе в систему", "Updates the view count also when users are logged in": "Обновляет количество просмотров также, когда пользователи вошли в систему", "Show featured articles": "Показывать избранные статьи", "Whether to show any articles set to featured in a sidebar": "Показывать ли какие-либо статьи, настроенные на боковую панель", "Show featured articles when viewing article": "Показывать избранные статьи при просмотре статьи", "Whether to show any articles set to featured in a sidebar when viewing an article": "Показывать ли какие-либо статьи, настроенные на боковую панель при просмотре статьи", "Featured article count": "Количество рекомендованных статей", "The number of featured articles shown": "Количество показанных статей", "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags": "Добавляет Google Analytics к общедоступным страницам. Включите весь код из Google, включая теги <script>", "New user": "Новый пользователь", "Users name": "Имя пользователя", "User email": "Электронная почта пользователя", "User password": "Пароль пользователяd", "Password confirm": "Подтверждение пароля", "Create": "Создать", "A user with that email address already exists": "Пользователь с таким адресом электронной почты уже существуе", "User": "Пользователь", "Role": "Роль", "Admin": "Администратор", "Are you sure you want to delete?": "Вы уверены, что хотите удалить?", "User edit": "Редактирование пользователя", "Password values do not match": "Значения пароля не совпадают", "Date": "Дата", "View count": "Количество просмотров", "Article title": "Название статьи", "Status": "Статус", "Draft": "Черновик", "Insert": "Вставить", "Article body": "Статья", "Preview": "Предпросмотр", "comma, separated, list": "список разделенный запятыми", "Permalink for the article": "Постоянная ссылка для статьи", "Validate": "Проверить", "Generate": "Сгенерировать", "Slug from title": "Метка", "Search the knowledge base": "Поиск по базе знаний", "Was this article helpful?": "Была ли эта статья полезной?", "Votes": "Голосование", "Article details": "Сведения о статье", "Published date": "Дата публикации", "Last updated": "Последнее обновление", "Share article": "Поделиться статьей", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyword": "ключевое слово", "permalink": "постоянная ссылка", "Delete": "Удалить", "Reset views": "Сброс просмотров", "Reset votes": "Сброс голосования", "Featured article": "Избранная статья", "SEO title": "SEO заголовок", "SEO description": "SEO описание", "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)": "Управляет отображением текста или логотипа веб-сайта расположенным по умолчанию: \"/public/logo.png\"", "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value": "Требуется \"Разрешить доступ к API \" установить в \"true\". Значение для токена доступа, необходимого для доступа к общедоступному API", "Setting to \"true\" will require a user to login before viewing any pages": "При установке \"true\" потребуется, чтобы пользователь входил в систему перед просмотром каких-либо страниц", "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true": "Отображается ли адрес электронной почты авторов в метанных статьи. Переключатель \"Показывать метаданные\" должен быть включен", "Not Found": "Не найдено", "Select file": "Выбрать файл", "Upload directory": "Загрузить каталог", "Upload file": "Загрузить файл", "Tip": "Совет", "To insert an image right click the link, copy the link address and add it to your Markdown": "Чтобы вставить изображение, щелкните правой кнопкой мыши по ссылке, скопируйте адрес ссылки и добавьте его в свой Markdown", "New directory": "Новая директория", "Multiple directories can be created using a \"/\" separator": "Несколько каталогов могут быть созданы с использованием разделителя a \"/\" separator", "Article successfully deleted": "Статья успешно удалена", "Keywords": "Ключевые слова", "Import": "Импорт", "New article successfully created": "Новая статья создана", "Successfully saved": "Успешно сохранено", "Permalink": "Постоянная ссылка", "Access denied. Check password and try again.": "Отказано в доступе. Проверьте пароль и попробуйте еще раз.", "Select a language": "Выберите язык", "The language to use for public facing pages. Leave blank for default (English)": "Язык, используемый для открытых страниц для обсуждения. Оставьте пустым для значения по умолчанию (английский язык)", "Settings successfully updated.": "Настройки сохранены.", "Results for": "Результаты", "No results found": "Результаты не найдены", "Access denied": "Доступ запрещен", "User account inserted": "Учетная запись пользователя добавлена", "Directory successfully created": "Каталог успешно создан", "File uploaded successfully": "Файл загружен успешно", "Allow Mermaid": "Разре<PERSON><PERSON><PERSON>ь Mermaid", "Whether to enable ": "Включен ли ", "Allow MathJax": "Разре<PERSON>и<PERSON>ь MathJax", "MathJax input mode": "Тип ввода данных MathJax", "Configure the mode MathJax operates in - ": "Настройте режим MathJax operates in - ", "Setup": "Настройка", "Email address": "Адрес электронной почты", "Confirm password": "Подтвердить пароль", "Complete setup": "Настройка завершена", "User is admin?": "пользователь является Администратором?", "User account updated.": "Учетная запись пользователя обновлена.", "All unused files have been removed": "Все неиспользуемые файлы были удалены", "Cleanup files": "Cleanup files"}