
exports.run = async (db) => {

  const articles = await db.kb.find({ kb_versioned_doc: { $ne: true } }).toArray();

  for (const article of articles) {
    if (!article.kb_pinned_topic) continue;

    const topic = await db.topics.findOne({ keyword: article.kb_pinned_topic });
    const regions = [];

    article.kb_regional_visibility.split(/(?:,)\s*/i).forEach(region => {
      
      if (topic && topic.topic_regional_visibility && topic.topic_regional_visibility.split(/(?:,)\s*/i).includes(region)) {
        regions.push(region);
      }
    });

    db.kb.update({ _id: article._id }, {
      $set: {
        kb_regional_visibility: regions.join(', '),
      }
    }, (err, numReplaced) => {
      if (err) {
        console.log(`ERROR in updating ${article._id}`, numReplaced);
      } else {
        console.log(`Updated ${article._id}`);
      }
    });
  }
}
