const TRANSLATION_API = 'https://89jppvocp8.execute-api.ap-southeast-2.amazonaws.com/prd/';
const axios = require('axios');
const fs = require('fs');
const yaml  = require('js-yaml');

exports.run = async(db) => {

  const articles = await db.kb.find({ kb_translation_title: { $exists: false }, kb_versioned_doc: {$ne: true} }, {
    projection: {
			kb_title: 1,
    
		}
  }).toArray();
  
  console.log('count', articles.length)

  for(const article of articles) {
    //article.kb_translation_title = await translateTitle(article.kb_title)
    const titleTranslations = await translateTitle(article.kb_title);
    console.log('translations', titleTranslations);
    const update = {...article, ...{ kb_translation_title:  titleTranslations}}
    delete update._id;
  
    const isUpdated = await db.kb.update(
      { _id: article._id },
      { $set: update },
      {});
  }
}

const translateTitle = async (_title) => {
  const languages = loadConfig().languages;
  console.log(languages)
  const titleTranslations = await axios.post(TRANSLATION_API, {
      phrases: [
          { phrase: _title, languages: languages }
      ]
  })

  return titleTranslations.data[0];
}

const loadConfig = () => {
  const config = fs.readFileSync(__dirname+'/../language.yml', 'utf8');
  return yaml.load(config);
}