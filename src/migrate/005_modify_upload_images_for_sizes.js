
const sizeOf = require('image-size');
const fetch = require('node-fetch');
const url = require('url')
const http = require('http');
const { ObjectId } = require('mongodb');

exports.run = async (db) => {

  const articles = await db.kb.find().toArray();
  console.log(process.env)
  for (const article of articles) {
    let kbBody = article.kb_body;
    
    kbBody = await replaceAsync(kbBody, /!\[(.*?)\]\((.*?)\)/g, async (match, alt, src) => {
      const extensions = ["mp4", "mkv", "webm", "avi", "mpeg", "mov", "mp3"];
      const extension = src.split(".").pop();
      const data = src.split(" ");
      let imageUrl = data[0].startsWith('/api') || data[0].startsWith('/asset-uploader') ? `${getBaseUrl()}${data[0]}` : data[0];
        
      if(isBlockedDomain(imageUrl)) {
        console.log('ID: ', article._id);
          console.log('URL: ', imageUrl);1
      }

      imageUrl = imageUrl.replace('https://portal.hub.gymsystems.co', ['production', 'staging'].includes(process.env.NODE_ENV) ? 'http://portal-server:8010' : 'http://localhost:8010')
     
      if (!extensions.includes(extension) && data.length === 1 && isValidUrl(imageUrl) && !isBlockedDomain(imageUrl)) {
       
        try{
          console.log(imageUrl)

          const response = await fetch(imageUrl, {
            method: 'GET',
            headers: {
              'x-api-key': '32aa84c47da94a65b71a0269f6960859'
            },
          });
          
          const buffer = await response.buffer();
          const details = await sizeOf(buffer);

          const updatedSrc = `${src} =${details.width}x${details.height}`;
          buffer.fill()
          return `![${alt}](${updatedSrc})`;
         
        } catch (e) {
          console.log('ID: ', article._id);
          console.log('URL: ', imageUrl);1
          return match;
        }
          
      } else {
        return match;
      }
    });
    
    const updated = await db.kb.update({ _id: article._id }, {$set: {kb_body: kbBody}});
    //console.log(updated)
  }
 
}

const replaceAsync = async(str, regex, asyncFn) => {
  const promises = [];
  str.replace(regex, (match, ...args) => {
    const promise = asyncFn(match, ...args);
    promises.push(promise);
  });
  const data = await Promise.all(promises);
  return str.replace(regex, () => data.shift());
}

const isBlockedDomain = (_url) => {
  const options = url.parse(_url);
  const blockedDomains = ['instagram.com', 'www.instagram.com'];

  return blockedDomains.includes(options.hostname); 
}

const isValidUrl = urlString => {
  var urlPattern = new RegExp(/http(s)?:\/\/(((www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-z]{2,63})|(localhost)|(portal-server))\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/i); // validate fragment locator
  return !!urlPattern.test(urlString);
}

const getBaseUrl = () =>{
  return 'https://portal.hub.gymsystems.co'
 
}
