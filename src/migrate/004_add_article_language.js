
exports.run = async (db) => {

  const articles = await db.kb.find().toArray();

  for (const article of articles) {
    // if visibility includes Japan, then set the article language
    // this is only a logic for this migration and not a logic for the app
    //let regExpr = new RegExp(".*" + Japan + ".*", "i");
    const language = article.kb_regional_visibility && article.kb_regional_visibility === 'Japan' ? "ja" : "en";

    db.kb.update({ _id: article._id }, {
      $set: {
        kb_language: language
      }
    }, (err, numReplaced) => {
      if (err) {
        console.log(`ERROR in updating ${article._id}`, numReplaced);
      } else {
        console.log(`Updated Lanuage update for:  ${article._id}`);
      }
    });
  }
}
