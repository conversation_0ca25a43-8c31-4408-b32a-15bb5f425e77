
exports.run = async (db) => {

  const articles = await db.kb.find().toArray();

  for (const article of articles) {
    if (!article.kb_pinned_topic) continue;

    const topic = await db.topics.findOne({ keyword: article.kb_pinned_topic });
    let subtopic = null;
    
    if(article.kb_pinned_subtopic && topic && topic.subtopics) {
      subtopic = topic.subtopics.find(subtopic => subtopic.subtopic.replace(/\s/g, "") === article.kb_pinned_subtopic)
    
    }
  

    db.kb.update({ _id: article._id }, {
      $set: {
        kb_pinned_topic: topic ? topic._id: null,
        kb_pinned_subtopic: subtopic ? subtopic.id : null
      }
    }, (err, numReplaced) => {
      if (err) {
        console.log(`ERROR in updating ${article._id}`, numReplaced);
      } else {
        console.log(`Updated ${article._id}`);
      }
    });
  }
}
