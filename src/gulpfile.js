// see video explanation: https://youtu.be/ubHwScDfRQA

const { src, dest, watch, series } = require('gulp')
const sass = require('gulp-sass')(require('sass')) // This is different from the video since gulp-sass no longer includes a default compiler. Install sass as a dev dependency `npm i -D sass` and change this line from the video.
const prefix = require('gulp-autoprefixer')
const minify = require('gulp-clean-css')
const browsersync = require('browser-sync').create()
const run = require('gulp-run');

const dir = 'public/themes/12rnd'

//compile, prefix, and min scss
function compilescss() {
  console.log('............................. from gulp compile css')
  return src(`${dir}/scss/*.scss`) // change to your source directory
    .pipe(sass())
    .pipe(prefix('last 2 versions'))
    .pipe(minify())
    .pipe(dest(`${dir}/css`)) // change to your final/public directory
}

// //optimize and move images
// function optimizeimg() {
//   console.log('.............................gulp compile images');

//   return src('wiki-themes/12rnd/*.{jpg,png}') // change to your source directory
//     .pipe(imagemin([
//       imagemin.mozjpeg({ quality: 80, progressive: true }),
//       imagemin.optipng({ optimizationLevel: 2 }),
//     ]))
//     .pipe(dest('src/public/images')) // change to your final/public directory
// };

// //optimize and move images
// function webpImage() {
//   console.log('.............................gulp compile webpImage');

//   return src('src/public/images/*.{jpg,png}') // change to your source directory
//     .pipe(imagewebp())
//     .pipe(dest('src/public/images')) // change to your final/public directory
// };

// minify js
// function jsmin(){
//   console.log('............................. from gulp compile js');

//   return src('../../wiki-themes/12rnd/js/*.js') // change to your source directory
//     .pipe(terser())
//     .pipe(dest('../../wiki-themes/12rnd/js')); // change to your final/public directory
// }

//watchtask
function watchTask() {
  console.log('............................. from gulp watch')

  //detect changes in scss
  watch(`${dir}/scss/**/*.scss`, series(compilescss, browsersyncReload))
  //detect changes in views
  watch(`${dir}/views/layouts/*.hbs`, series(compilescss, browsersyncReload))
  //watch('../../wiki-themes/12rnd/js/*.js', jsmin); // change to your source directory
  //watch('wiki-themes/12rnd/images/*', optimizeimg); // change to your source directory
  //watch('src/public/images/*.{jpg,png}', webpImage); // change to your source directory

  watch(['language.yml','csv/*.csv'], series(compilescss, buildTranslations, browsersyncReload))
}

// Browsersync Tasks
function browsersyncServe(cb) {
  browsersync.init({
    port: 7000, // this will be the port we should access in our browser
    proxy: 'http://localhost:4444/', // this is the url our app is running
    ui: { port: 7001 }, // port to access browsersync ui
  })
  cb()
}

function browsersyncReload(cb) {
  console.log('reloading browser............................................')
  browsersync.reload()
  cb()
}

function buildTranslations() {
  return run('npx build-translations').exec();
}

const build = series(compilescss, buildTranslations)
const dev = series(compilescss, buildTranslations, browsersyncServe, watchTask)

// Default Gulp task
exports.default = build

exports.watch = dev
