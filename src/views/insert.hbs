<div class="main-content">
  <nav id="wikiAdminNav" data-navtoopen="{{nav}}">
    <div class="nav nav-tabs" id="nav-tab" role="tablist" style="background-color: white;">
      
      <button class="nav-link" id="navSettingsDashboard">
        
          <span class="d-none d-sm-block pt-1">{{translate "Dashboard"}}</span>
        
      </button>
      <button class="nav-link" id="navSettingsArticles">
        
          <span class="d-none d-sm-block pt-1">{{translate "Articles"}}</span>
        
      </button>
      <button class="nav-link disabled active" id="nav-contact-tab">
        
          <span class="d-none d-sm-block pt-1" id="">{{translate "New Article"}}</span>
        
      </button>
      <div class="ml-auto custom-nav-right text-secondary" title="{{translate "Wiki Configuration"}}">
        <i class="fa fa-cog" id="navSettingsConfiguration"></i>
      </div>
    </div>
  </nav>

<div class="mr-4 ml-4">
  {{!-- <nav aria-label="breadcrumb">
    <ol class="breadcrumb mt-2">
      <li class="breadcrumb-item"><a href="{{app_context}}">Home</a></li>
      <li class="breadcrumb-item"><a href="{{app_context}}/articles">Articles</a></li>
      <li class="breadcrumb-item active" aria-current="page">New Article</li>
    </ol>
  </nav> --}}

  <div class="mt-4 col-sm-12">
    <form method="post" id="edit_form" action="{{app_context}}/insert_kb" data-toggle="validator">
      <div class="row">
        <div class="col-xs-12 col-lg-6">
          <div class="form-group">
            <label for="frm_kb_title">{{translate "Article title"}} *</label><br />
            <input
              type="text"
              name="frm_kb_title"
              id="frm_kb_title"
              class="form-control input-normal"
              minlength="5"
              maxlength="200"
              value="{{kb_title}}"
              required
            />
          </div>
        </div>
        <div class="col-xs-12 col-sm-4 col-md-4 col-lg-2">
          <label for="frm_kb_password">{{translate "Password"}}</label><br />
          <input
            id="frm_kb_password"
            class="form-control"
            name="frm_kb_password"
            type="text"
            value="{{kb_password}}"
          />
        </div>
        <div class="col-xs-6 col-sm-4 col-md-4 col-lg-2">
          <label for="frm_kb_published">{{translate "Status"}}</label><br />
          <select class="form-control" id="frm_kb_published" name="frm_kb_published">
            <option value="true" selected>{{translate "Published"}}</option>
            <option value="false">{{translate "Draft"}}</option>
          </select>
        </div>
        <div class="col-xs-6 col-sm-4 col-md-4 col-lg-2">
          <label for="frm_kb_published">&nbsp;</label><br />
          <div class="btn-group pull-right" role="group" aria-label="...">
            <button
              id="frm_create_kb_save"
              class="btn btn-success editor_btn_action pull-right"
              type="submit"
            >{{translate "Insert"}} <i class="fa fa-plus"></i></button>
          </div>
        </div>
      </div>
      <div class="row divArticleContainer">
        <div class="col-xs-12 col-md-12 col-lg-6">
          <div class="form-group" id="editor-wrapper">
            <label for="editor">{{translate "Article body (Markdown)"}} *</label>
            <textarea
              id="editor"
              minlength="5"
              name="frm_kb_body"
              data-provide="markdown"
              
              data-iconlibrary="fa"
              class="form-control"
              required
            >{{kb_body}}</textarea>
          </div>
        </div>
        <div class="col-xs-12 col-md-6 col-lg-6">
          <div class="form-group" id="preview-wrapper">
            <label for="preview">{{translate "Preview"}}</label>
            <div id="preview" name="preview" class="form-control" rows="10"></div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-4 col-lg-6">
          <div class="form-group">
            <input
              type="text"
              class="tokenfield form-control"
              name="frm_kb_keywords"
              id="frm_kb_keywords"
              placeholder="{{translate "comma, separated, list"}}"
              value={{kb_keywords}}
            />
          </div>
        </div>
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
          <input
            type="text"
            class="form-control"
            name="frm_kb_permalink"
            id="frm_kb_permalink"
            placeholder="{{translate "Permalink for the article"}}"
            value={{kb_permalink}}
          />
        </div>
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
          <div
            class="btn-group btn-group-justified pull-right permalink_buttons"
            role="group"
            aria-label="..."
          >
            <a href="#" class="btn btn-success" id="validate_permalink" type="button">{{translate "Validate"}}</a>
            <a href="#" class="btn btn-warning" id="generate_permalink" type="button">{{translate "Generate"}}</a>
            <a
              href="#"
              class="btn btn-info disabled"
              id="generate_permalink_from_title"
              type="button"
            >Slug</a>
          </div>
        </div>
      </div>

    </form>
  </div>
</div>
</div>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/articles-insert-script.js"></script>