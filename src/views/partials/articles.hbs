<div class="search-wrap">
  <div class="search-content">
    <div class="buttons">
      <a id="btn-new-article" class="btn waves-effect" href="{{app_context}}/insert" type="button" form="settingsForm">
        <span>{{translate "New Article"}}</span>
        <svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_17_36)">
            <path
              d="M15.8333 10.8333H10.8333V15.8333H9.16667V10.8333H4.16667V9.16666H9.16667V4.16666H10.8333V9.16666H15.8333V10.8333Z"
              fill="currentColor" />
          </g>
          <defs>
            <clipPath id="clip0_17_36">
              <rect width="20" height="20" fill="currentColor" />
            </clipPath>
          </defs>
        </svg>
      </a>
      <a id="csv" class="btn waves-effect" type="button">
        <span>{{translate "Export CSV"}}</span>
        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
          x="0" y="0" viewBox="0 0 500 500" xml:space="preserve" fill="currentColor">
          <style></style>
          <defs>
            <path id="SVGID_1_" d="M0 0h500v500H0z"></path>
          </defs>
          <clipPath id="SVGID_00000151524302526006783760000009387503102392374920_">
            <use xlink:href="#SVGID_1_" overflow="visible"></use>
          </clipPath>
          <g clip-path="url(#SVGID_00000151524302526006783760000009387503102392374920_)">
            <defs>
              <path id="SVGID_00000133489503278807473390000009400609450605903026_" d="M0 0h500v500H0z"></path>
            </defs>
            <clipPath id="SVGID_00000168829252607399499490000003918191479402967182_">
              <use xlink:href="#SVGID_00000133489503278807473390000009400609450605903026_" overflow="visible"></use>
            </clipPath>
            <g clip-path="url(#SVGID_00000168829252607399499490000003918191479402967182_)">
              <path
                d="M417.3 82.8h-63.1c-11.5 0-20.8 9.4-20.8 20.8s9.4 20.8 20.8 20.8l62.5-.6.6 250.6-334 .6-.6-250.6h63.1c11.5 0 20.8-9.4 20.8-20.8s-9.4-20.8-20.8-20.8h-63c-22.7 0-41 18.3-41 41v251.3c0 22.7 18.3 41 41 41h334.6c22.5 0 41-18.3 41-40.8V123.9c0-22.7-18.6-41.1-41.1-41.1z">
              </path>
            </g>
            <path
              d="M173.2 247.2l62.5 59.2c1.6 1.6 3.6 2.6 5.6 *******.******* 2.6 1 5.3 1.6 8 1.6s5.3-.5 7.8-1.6c2.9-1.2 5.3-3 7.4-5.2l61.5-58.2c8.3-7.9 8.8-21 .8-29.4-7.9-8.3-21-8.8-29.4-.8l-27.3 25.9v-139c0-11.5-9.4-20.8-20.8-20.8s-20.8 9.4-20.8 20.8v139.2L201.7 217c-8.3-7.9-21.5-7.5-29.4.8-7.9 8.4-7.5 21.5.9 29.4z"
              clip-path="url(#SVGID_00000168829252607399499490000003918191479402967182_)"></path>
          </g>
        </svg>
      </a>
    </div>
    <div class="search-title-wrap">
      <input type="text" name="article_filter" id="article_filter" class="form-control form-control-sm mr-1"
        placeholder="{{translate " Filter article using title"}}" />
    </div>
  </div>
  <div class="filters-wrap">
    <div class="filters">
      <div class="filter-grp" id="filter-regions">
        <p class="filter-label">{{translate "Region"}}</p>
        <span class="filter-grp-divider"></span>
        <div class="filter">
          <button type="button" class="filter-btn filter-selected">{{translate "All"}}</button>
          <ul class="filter-options" id="filter-region-options">
            <li class="filter-option filter__option--active region" data-filter="">{{translate "All"}}</li>
            <li class="filter-option filter__option region" data-filter="Global">{{translate "Global"}}</li>
            {{#each countries}}
            <li class="filter-option region" data-filter="{{this.full_name}}">{{this.full_name}}</li>
            {{/each}}
          </ul>
        </div>
      </div>
      <div class="filter-grp" id="filter-status">
        <p class="filter-label">{{translate "Status"}}</p>
        <span class="filter-grp-divider"></span>
        <div class="filter">
          <button type="button" class="filter-btn filter-selected">{{translate "All"}}</button>
          <ul class="filter-options">
            <li class="filter-option filter__option--active" data-filter="">{{translate "All"}}</li>
            <li class="filter-option" data-filter="false">{{translate "Draft"}}</option>
            <li class="filter-option" data-filter="true">{{translate "Published"}}</option>
          </ul>
        </div>
      </div>
      <div class="filter-grp" id="filter-topics">
        <p class="filter-label">{{translate "Topics"}}</p>
        <span class="filter-grp-divider"></span>
        <div class="filter">
          <button type="button" class="filter-btn filter-selected">{{translate "All"}}</button>
          <ul class="filter-options">
            <li class="filter-option filter__option--active" data-filter="">{{translate "All"}}</li>
            {{#each topics}}
            <li class="filter-option" data-filter="{{this._id}}" data-visibility="{{this.topic_regional_visibility}}" >{{this.topic}} {{this.topicTranslation}}</li>
            {{/each}}
          </ul>
        </div>
      </div>
    </div>

  </div>
</div>
<div class="main-content">
  <div class="row">
    <div class="col-12">
      <table id="articles" class="table data-table">
        <thead>
          <tr>
            <th>
              <div class="articles-table__header">
                <span>{{translate "TITLE"}}</span>
                <span class="articles-table__header__icon"></span>
              </div>
            </th>
            <th>
              <div class="articles-table__header">
                <span>{{translate "Topic"}}</span>
                <span class="articles-table__header__icon"></span>
              </div>
            </th>
            <th>
              <div class="articles-table__header">
                <span>{{translate "Sub Topic"}}</span>
                <span class="articles-table__header__icon"></span>
              </div>
            </th>
            <th>
              <div class="articles-table__header">
                <span>{{translate "DATE PUBLISHED"}}</span>
                <span class="articles-table__header__icon"></span>
              </div>
            </th>
            <th class="no-sort">
              <div class="articles-table__header">
                <span>{{translate "VISIBILITY"}}</span>
                <span class="articles-table__header__icon"></span>
              </div>
            </th>
            <th>
              <div class="articles-table__header">
                <span>{{translate "STATUS"}}</span>
                <span class="articles-table__header__icon"></span>
              </div>
            </th>
            <th class="no-sort">
              <div class="articles-table__header">
                <span>{{translate "ACTIONS"}}</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {{#each data.articles}}
          <tr id="article-{{this._id}}" class="article" data-id="{{this._id}}">
            <td class="article-cell title searchable" data-search="{{this.kb_title}}" data-export="{{this.kb_title}}">
              <div class="article-cell__title">{{this.kb_title}}</div>
              <div class="article-cell__translation">Translation: {{getValueOfIndex @root.data.userLanguage
                this.kb_translation_title }}</small></div>
            </td>
            <td class="article-cell searchable" data-search="{{this.kb_pinned_topic}}"
              data-export="{{this.readableTopic}}">
              <div class="article-cell__topic">{{this.readableTopic}}</div>
            </td>
            <td class="article-cell" data-export="{{this.readableSubTopic}}">
              <div class="article-cell__sub-topic">{{this.readableSubTopic}} </div>
            </td>
            <td class="article-cell" data-order="{{dateFormat " x" this.kb_published_date}}"
              data-export="{{this.kb_published_date}}">
              <div class="article-cell__published_date">{{dateFormat "MMM DD, YYYY - h:mm A" this.kb_published_date}}
              </div>
            </td>
            <td class="article-cell no-sort searchable" data-search="{{ternary this.kb_regional_visibility.length this.kb_regional_visibility 'Global'}}"
              data-export="{{this.kb_regional_visibility}}">
              <div class="article-cell__visibility">
                {{{visibility this.visibilityWithIso}}}
              </div>
            </td>
            <td class="article-cell searchable no-sort" data-search="{{this.kb_published}}"
              data-export="{{this.kb_published}}">
              {{{generateStatus this.kb_published}}}
            </td>
            <td class="article-cell article-cell--center">
              <span role="button" title="Duplicate" data-id="{{this._id}}" class="article-duplicate">
                <i class="fa fa-copy"></i>
              </span>
            </td>
          </tr>
          {{/each}}
        </tbody>
      </table>
    </div>
  </div>
</div>