<nav id="wikiAdminNav" data-navtoopen="{{nav}}">
	<div class="nav nav-tabs" id="nav-tab" role="tablist">
		<button class="nav-link {{#isNull nav}}active{{/isNull}}" id="navSettingsDashboard">
			<span class="d-none d-sm-block pt-1">{{translate "Dashboard"}}</span>
		</button>
		<button class="nav-link {{#ifCond nav '===' 'articles'}}active{{/ifCond}}" id="navSettingsArticles">
			<span class="d-none d-sm-block pt-1">{{translate "Articles"}}</span>
		</button>
		{{#ifCond hideConfig '==' true}}
		{{else}}
		<div class="ml-auto custom-nav-right text-secondary" title="{{translate " Wiki Configuration"}}">
			<i class="fa fa-cog" id="navSettingsConfiguration"></i>
		</div>
		{{/ifCond}}

	</div>
</nav>
