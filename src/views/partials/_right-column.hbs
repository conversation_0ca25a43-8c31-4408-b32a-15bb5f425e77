{{!-- Right Column Cards for Article Editor --}}

{{!-- Publish Settings Card --}}
<div class="ph-card ph-settings-card ph-text" id="publish-settings-card">
    <div class="ph-card_header">
        <h3>{{translate "Publish Settings"}}</h3>
    </div>
    <div class="ph-card_body">
        <div class="form-group ph-form-group">
            <div class="form-field-container form-field-select">
                <label for="frm_kb_published">
                    {{translate "Status"}}
                    <img src="../images/help-icon.svg" class="ph-tooltip help-icon"
                        title="{{#ifCond result.kb_published '==' 'true'}}{{translate 'This article is live and visible to users.'}}{{else}}{{translate 'This article is saved as a draft and not visible to users.'}}{{/ifCond}}"
                        data-toggle="tooltip" data-placement="top" alt="Help">
                </label>
                <select class="form-control" id="frm_kb_published" name="frm_kb_published" {{#if
                    is_restricted}}disabled{{/if}}>
                    <option value="true" {{#ifCond result.kb_published '==' 'true' }}selected{{/ifCond}}>{{translate
                        "Published"}}</option>
                    <option value="false" {{#ifFalseOrNull result.kb_published}}selected{{/ifFalseOrNull}}>{{translate
                        "Draft"}}</option>
                </select>
            </div>
        </div>

        {{#if result.kb_published_date}}
        <div class="form-group ph-form-group">
            <label for="frm_kb_published">
                <i class="fa fa-calendar" aria-hidden="true"></i>
                {{translate "Published"}}
            </label>
            <div class="form-field-text">
                <span>{{format_date result.kb_published_date}}</span>
            </div>
        </div>
        {{/if}}
        {{#if result.kb_last_updated}}
        <div class="form-group ph-form-group">
            <label for="frm_kb_published" class="form-label--actions">
                <i class="fa fa-calendar" aria-hidden="true"></i>
                {{translate "Last Updated"}}
                <a class="ml-auto ph-btn ph-btn-ghost ph-btn-xs offset-internal-padding-right" href="#" id="btnHistory">
                    <i class="fa fa-code-fork" aria-hidden="true"></i>
                    View History
                </a>
            </label>
            <div class="form-field-text">
                <span>{{format_date result.kb_last_updated}}</span>
            </div>
        </div>
        <hr class="ph-hr" />
        {{/if}}

        <div class="form-group">
            <div class="checkbox">
                <label>
                    <input type="checkbox" class="ph-checkbox" id="frm_kb_featured" name="frm_kb_featured"
                        {{checked_state result.kb_featured}} {{#if is_restricted}}disabled{{/if}}> {{translate "Featured
                    Article"}}
                </label>
            </div>
        </div>

        <hr class="ph-hr" />

        <div class="d-flex gap-2 justify-between w-full">
            {{#if result._id}}
            <a href="{{app_context}}/delete/{{result._id}}" id="btnDeleteArticle"
                class="btn btn-danger ph-btn btn-surface {{#if is_restricted}}btn-disabled{{/if}}" {{#if
                is_restricted}}disabled{{/if}}>
                <i class="fa fa-trash" aria-hidden="true"></i>
            </a>
            <button type="button" id="frm_edit_kb_save"
                class="btn btn-primary ph-btn {{#if is_restricted}}btn-disabled{{/if}}" {{#if
                is_restricted}}disabled{{/if}}><i class="fa fa-floppy-o" aria-hidden="true"></i> {{translate
                "Save"}}
            </button>
            {{else}}
            <div class="d-flex gap-2 justify-between w-full">
                <button id="frm_create_kb_save" class="btn btn-primary ph-btn ml-auto editor_btn_action" type="submit">
                    {{translate "Insert"}} <i class="fa fa-plus"></i>
                </button>
            </div>
            {{/if}}
        </div>
    </div>
</div>

{{!-- Cards Row Container for Tablet Layout --}}
<div class="ph-cards-row">
    {{!-- Security Settings Card --}}
    <div class="ph-card ph-settings-card ph-text" id="security-settings-card">
        <div class="ph-card_header">
            <h3>{{translate "Security & Privacy"}}</h3>
        </div>
        <div class="ph-card_body">
            {{!-- Visibility State --}}
            <div class="form-group ph-form-group">
                <div class="form-field-container form-field-select">
                    <label for="frm_kb_visible_state">
                        {{translate "Visible State"}}
                        <img class="ph-tooltip help-icon"
                            title="{{#ifCond result.kb_visible_state '==' 'private'}}{{translate 'This article is private and only visible to authorized users'}}{{else}}{{translate 'This article is publicly accessible'}}{{/ifCond}}"
                            data-toggle="tooltip" data-placement="top" alt="Help">
                    </label>
                    <select class="form-control" id="frm_kb_visible_state" name="frm_kb_visible_state" {{#if
                        is_restricted}}disabled{{/if}}>
                        <option {{select_state result.kb_visible_state 'public' }} value="public">{{translate "Public"}}
                        </option>
                        <option {{select_state result.kb_visible_state 'private' }} value="private">{{translate
                            "Private"}}
                        </option>
                    </select>
                </div>
            </div>

            {{!-- Security Level --}}
            <div class="form-group ph-form-group">
                <div class="form-field-container form-field-select">
                    <label for="frm_kb_security_level">
                        {{translate "Security Level"}}
                        <img src="./../images/help-icon.svg" class="ph-tooltip help-icon"
                            title="{{translate 'Higher levels require more privileges to access'}}"
                            data-toggle="tooltip" data-placement="top" alt="Help">
                    </label>
                    <select class="form-control" id="frm_kb_security_level" name="frm_kb_security_level" {{#if
                        is_restricted}}disabled{{/if}}>
                        <option {{select_state result.kb_security_level 100}} value="100">{{translate "100 Standard
                            User"}}
                        </option>
                        <option {{select_state result.kb_security_level 200}} value="200">{{translate "200 Club
                            Manager"}}
                        </option>
                        <option {{select_state result.kb_security_level 300}} value="300">{{translate "300 Club Owner"}}
                        </option>
                        <option {{select_state result.kb_security_level 400}} value="400">{{translate "400 Admin Only"}}
                        </option>
                    </select>
                </div>
            </div>

            {{!-- Regional Visibility --}}
            <div class="form-group ph-form-group relative">
                <label for="selectCountryDropdown">
                    {{translate "Regional Visibility"}}
                    <img class="ph-tooltip"
                        title="{{translate 'Articles without any regions configured will be globally visible'}}"
                        data-toggle="tooltip" data-placement="top" alt="Help">
                </label>
                <div class="relative">
                    <div class="selectBox relative">
                        <input type="hidden" id="frm_kb_regional_visibility" name="frm_kb_regional_visibility"
                            value="{{result.kb_regional_visibility}}" />
                        <select class="form-control" id="selectCountryDropdown" data-options="{{result.countries}}"
                            data-possible-options="{{result.possibleCountries}}" {{#if is_restricted}}disabled{{/if}}>
                            <option value="" id="optionSelectCountry">{{translate "Select regions..."}}</option>
                        </select>
                        <div class="overSelect" id="overSelect"></div>
                    </div>
                    <div id="countryCheckboxes" class="ph-country-checkboxes"></div>
                </div>
            </div>

            <hr class="ph-hr mt-2" />

            {{!-- Password Protection --}}
            <div class="form-group ph-form-group">
                <div class="form-field-container">
                    <label for="frm_kb_password">
                        {{translate "Password"}}
                        <img src="../images/help-icon.svg" class="ph-tooltip help-icon"
                            title="{{translate 'Leave blank for no password protection'}}" data-toggle="tooltip"
                            data-placement="top" alt="Help">
                    </label>
                    <input id="frm_kb_password" class="form-control" name="frm_kb_password" type="text"
                        value="{{result.kb_password}}" {{#if is_restricted}}disabled{{/if}} />
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <label>
                        <input id="frm_kb_admin_restricted" class="ph-checkbox" name="frm_kb_admin_restricted"
                            {{checked_state result.kb_admin_restricted}} {{#if is_restricted}}disabled{{/if}}
                            type="checkbox">
                        {{translate "Only admins can edit"}}
                    </label>
                </div>
            </div>
        </div>
    </div>

    {{!-- Topic Management Card --}}
    <div class="ph-card ph-settings-card" id="topic-management-card">
        <div class="ph-card_header">
            <h3>{{translate "Topic Management"}}</h3>
        </div>
        <div class="ph-card_body">
            <div class="form-group ph-form-group">
                <div class="checkbox d-flex align-items-center">
                    <label>
                        <input id="kb_pinned" type="checkbox" class="ph-checkbox" name="frm_kb_pinned" {{checked_state
                            result.kb_pinned}} {{#if is_restricted}}disabled{{/if}}>
                        {{translate "Associate with Topic"}}
                    </label>
                    <a class="ml-auto ph-btn ph-btn-ghost ph-btn-xs offset-internal-padding-right"
                        href="{{app_context}}/settings/configuration/topicsettings">
                        <i class="fa fa-cog" aria-hidden="true"></i>
                        {{translate "Configure"}}
                    </a>
                </div>
            </div>

            <div class="pinDiv ph-topic-selection-group d-flex flex-column flex-gap-2"
                style="{{#unless (checked_state result.kb_pinned)}}display: none;{{/unless}}">
                <div class="form-group ph-form-group">
                    <div class="form-field-container form-field-select">
                        <label for="selectTopics">{{translate "Topic"}}</label>
                        <select class="form-control" data-topic="{{result.kb_pinned_topic}}" id="selectTopics"
                            name="frm_kb_pinned_topic" {{#if is_restricted}}disabled{{/if}}>
                            <option value="">{{translate "Select Topic"}}</option>
                        </select>
                    </div>
                </div>

                <div class="form-group ph-form-group">
                    <div class="form-field-container form-field-select">
                        <label for="selectSubtopics">{{translate "Sub-topic"}}</label>
                        <select class="form-control" data-subtopic="{{result.kb_pinned_subtopic}}" id="selectSubtopics"
                            name="frm_kb_pinned_subtopic" {{#if is_restricted}}disabled{{/if}}>
                            <option value="">{{translate "Select Subtopic"}}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>



    {{!-- Article Feedback Stats Card --}}
    <div class="ph-card ph-settings-card" id="feedback-stats-card">
        <div class="ph-card_header">
            <h3>{{translate "Article Feedback"}}</h3>
        </div>
        <div class="ph-card_body">
            {{#if result._id}}
            <div class="ph-feedback-stats" id="feedbackStatsContainer">
                <div class="ph-stats-loading">
                    <i class="fa fa-spinner fa-spin"></i>
                    {{translate "Loading feedback data..."}}
                </div>
            </div>
            {{else}}
            <div class="ph-feedback-placeholder">
                <p class="text-muted">{{translate "Save article to see feedback statistics"}}</p>
            </div>
            {{/if}}
        </div>
    </div>
</div>
