<nav id="wikiAdminNav">
  <div class="nav nav-tabs" id="nav-tab" role="tablist" style="background-color: white;">
    <button class="nav-link" id="navSettingsDashboard">
      <span class="d-none d-sm-block pt-1">{{translate "Dashboard"}}</span>
    </button>
    <button class="nav-link" id="navSettingsArticles">
      <span class="d-none d-sm-block pt-1">{{translate "Articles"}}</span>
    </button>
    <button class="nav-link disabled active" id="nav-contact-tab">
      {{#if title}}
      <span class="d-none d-sm-block pt-1" id="spanArticleTitle">{{title}}</span>
      {{else}}
      <span class="d-none d-sm-block pt-1" id="spanArticleTitle">{{translate "New Article"}}</span>
      {{/if}}
    </button>
    {{#ifCond hideConfig '==' true}}
			{{else}}
			<div class="ml-auto custom-nav-right text-secondary" title="{{translate "Wiki Configuration"}}">
				<i class="fa fa-cog" id="navSettingsConfiguration"></i>
			</div>
			{{/ifCond}}
  </div>
</nav>