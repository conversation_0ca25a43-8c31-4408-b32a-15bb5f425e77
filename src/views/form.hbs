{{> form_nav title=result.kb_title hideConfig=is_pseudoadmin}}
<div id="article-form-wrapper" class="ph-scrollbar">
    {{#if result._id}}
    <form method="post" id="edit_form" class="ph-form" action="{{app_context}}/save_kb" data-toggle="validator">
        {{else}}
        <form method="post" id="edit_form" class="ph-form" action="{{app_context}}/insert_kb" data-toggle="validator">
            {{/if}}

            <input type="hidden" name="frm_kb_edit_reason" id="frm_kb_edit_reason" />
            <input type="hidden" name="is_restricted" id="is_restricted" value="{{is_restricted}}" />
            <input type="hidden" name="is_pseudoadmin" id="is_pseudoadmin" value="{{session.is_pseudoadmin}}" />
            <input type="hidden" name="is_markdown" id="is_markdown" value="{{result.kb_is_markdown}}" />
            <input type="hidden" name="article_id" id="article_id" value="{{result._id}}" />

            <!-- New Two-Column Layout Structure -->
            <div class="ph-editor-layout">
                <!-- Main Editor Area -->
                <div class="ph-editor-main">
                    <div class="d-flex flex-column flex-gap-2">
                        <!-- Article Header Section -->
                        <div class="ph-article-header">
                            <div class="form-group ph-form-group mb-0 editor-title-container">
                                <label for="frm_kb_title" class="ph-text-label">{{translate "Title"}}</label>
                                <input type="text" name="frm_kb_title" id="frm_kb_title"
                                    class="form-control input-normal" minlength="5" maxlength="200"
                                    placeholder="{{translate 'Article title'}}"
                                    value="{{#if result._id}}{{result.kb_title}}{{else}}New Article Title{{/if}}"
                                    required {{#if is_restricted}}disabled{{/if}} />
                                <i class="material-icons">edit</i>
                            </div>
                        </div>

                        <!-- Enhanced Permalink Display Section -->
                        <div class="d-flex flex-gap-2 align-center">
                            <span class="ph-text-label">{{translate "Permalink"}}:</span>
                            <div class="ph-permalink-container">
                                <span class="ph-permalink-url"
                                    id="permalink-display">{{app_context}}/{{result.kb_permalink}}</span>
                                <div class="ph-permalink-actions">
                                    <button type="button" id="btnEditPermalink" title="{{translate 'Edit permalink'}}"
                                        class="ph-btn ph-btn-ghost ph-btn-xs">
                                        <i class="material-icons">edit</i>
                                    </button>
                                    <button type="button" id="generate_permalink_from_title"
                                        title="{{translate 'Generate from title'}}"
                                        class="ph-btn ph-btn-ghost ph-btn-xs">
                                        <i class="material-icons">refresh</i>
                                    </button>
                                    <button type="button" id="btnCopyArticleLink"
                                        data-clipboard-target="#permalink-display" data-routename="{{app_context}}"
                                        data-articleid="{{result._id}}" title="{{translate 'Copy permalink'}}"
                                        class="ph-btn ph-btn-ghost ph-btn-xs">
                                        <i class="material-icons">content_copy</i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Permalink Edit Mode (Initially Hidden) -->
                        <div class="ph-permalink-edit ph-form" id="permalink-edit" style="display: none;">
                            <div class="d-flex flex-gap-2 align-center">
                                <span class="ph-text-label">{{translate "Edit Permalink"}}:</span>
                                <div class="ph-permalink-input-container">
                                    <input type="text" {{#if is_restricted}}disabled{{/if}}
                                        class="form-control ph-permalink-input" name="frm_kb_permalink"
                                        id="frm_kb_permalink" data-perma="{{result}}"
                                        placeholder="{{translate 'permalink'}}" value="{{result.kb_permalink}}"
                                        required>
                                    <div class="ph-permalink-edit-actions">
                                        <button type="button" id="validate_permalink"
                                            class="ph-btn ph-btn-xs ph-btn-success"
                                            title="{{translate 'Validate permalink'}}">
                                            <i class="fa fa-check-circle"></i>
                                        </button>
                                        <button type="button" id="generate_permalink"
                                            class="ph-btn ph-btn-xs ph-btn-warning"
                                            title="{{translate 'Generate random permalink'}}">
                                            <i class="material-icons">shuffle</i>
                                        </button>
                                        <button type="button" id="btnCancelEditPermalink"
                                            class="ph-btn ph-btn-ghost ph-btn-xs" title="{{translate 'Cancel edit'}}">
                                            <i class="material-icons">close</i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Editor Section -->
                    <div class="ph-editor-section">
                        <div class="form-group {{#if is_restricted}}disabled{{/if}}" id="editor-wrapper">
                            <div class="ph-editor-container">
                                <div class="ph-editor-area">
                                    <div class="ph-editor-header">
                                        <div class="ph-editor-label">
                                            <label for="editor">
                                                {{#if result.kb_is_markdown}}{{translate "Article body (Markdown)"}}
                                                *{{else}}{{translate "Article body (WYSIWYG)"}}{{/if}}
                                            </label>
                                        </div>
                                        <div class="ph-editor-controls">
                                            <label for="editor-switch" class="ph-switch">
                                                <input {{#if result.kb_is_markdown}}checked{{/if}} type="checkbox"
                                                    id="editor-switch" class="ph-switch-input" value="1"
                                                    name="frm_markdown_switch" />
                                                <div class="lever"></div>
                                                {{translate "Markdown Editor"}}
                                            </label>
                                            <button type="button" id="btnTogglePreview"
                                                class="ph-btn ph-btn-ghost ph-btn-sm ph-btn-secondary"
                                                style="{{#unless result.kb_is_markdown}}display: none;{{/unless}}">
                                                <i class="material-icons md-18">chevron_left</i>
                                                <span class="button-text">{{translate "Show Preview"}}</span>
                                            </button>
                                        </div>
                                    </div>
                                    <textarea class="frm_kb_body" {{#if is_restricted}}disabled{{/if}} id="editor"
                                        minlength="5" name="frm_kb_body" data-provide="markdown"
                                        data-hidden-buttons="cmdPreview" data-iconlibrary="fa" class="form-control"
                                        required>{{{result.kb_body }}}</textarea>
                                </div>

                                <!-- Conditional Preview Panel -->
                                <div class="ph-preview-area hidden" id="preview-wrap">
                                    <div class="form-group" id="preview-wrapper">
                                        <label for="preview">{{translate "Preview"}}</label>
                                        <div id="preview" name="preview" class="form-control" rows="5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Keywords Section -->
                        <div class="ph-keywords-section">
                            <div class="form-group ph-form-group d-flex align-items-center flex-gap-2">
                                <label for="frm_kb_keywords" class="ph-text-label">{{translate "Keywords"}}</label>
                                <input {{#if is_restricted}}disabled{{/if}} type="text" name="frm_kb_keywords"
                                    id="frm_kb_keywords" class="form-control" placeholder="{{translate " Enter
                                    keywords"}}" value="{{result.kb_keywords}}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column for Card-based Settings -->
                <div class="ph-editor-sidebar">
                    {{> _right-column }}
                </div>
            </div>





            <input type="hidden" name="frm_kb_id" id="frm_kb_id" value="{{result._id}}" />
            <input type="hidden" id="frm_editor" value="true" />

            <nav class="cbp-spmenu cbp-spmenu-vertical cbp-spmenu-right">
                <div class="cbp-spmenu-header">
                    <div class="pull-left">{{translate "Settings"}}</div><button id="btnSettingsMenu"
                        class="btn btn-default pull-right toggle-menu menu-right push-body"><i class="fa fa-cog"></i>
                </div></button>
                <ul class="list-group list-group-sidemenu">

                </ul>
            </nav>

            {{!-- Now always enable article versioning --}}
            {{!-- {{#ifCond config.settings.article_versioning '===' true}} --}}

            <div class="modal-content" id="articleHistory">

                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">{{translate "Article History"}}</h5>
                    <button type="button" id="btnCloseHistory" class="close" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="list-group list-group-sidemenu">
                        {{!-- <li class="list-group-item">
                            <label for="frm_kb_edit_reason">{{__ "Edit reason"}}</label>
                            <textarea class="form-control" name="frm_kb_edit_reason" id="frm_kb_edit_reason"
                                rows="3"></textarea>
                        </li> --}}
                        <li class="list-group-item article-history-item">
                            {{#if versions}}
                            <p>{{translate "Previous versions"}}</p>
                            {{#each versions}}
                            <div class="versionWrapper">
                                <small>{{translate "Date"}}: {{format_date this.kb_last_updated}}</small>
                                <textarea class="form-control versionArea" readonly
                                    rows="2">{{this.kb_edit_reason}}</textarea>
                                <div id="{{this._id}}" class="btn-group btn-group-justified">
                                    <button type="button"
                                        class="btnDeleteVersion btn btn-sm btn-outline-danger">{{translate
                                        "Delete"}}</a>
                                        <button type="button"
                                            class="btnEditVersion btn btn-sm btn-outline-warning">{{translate
                                            "Edit"}}</a>
                                            <button type="button"
                                                href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}/version"
                                                class=" btn btn-sm btn-outline-info">{{translate
                                                "Preview"}}</a>
                                </div>
                                <hr>
                            </div>
                            {{/each}}
                            </select>
                            {{/if}}
                        </li>
                    </ul>

                </div>

            </div>
            {{!-- {{/ifCond}} --}}

        </form>

</div>

<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/video-renderer.js"></script>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/edit-article.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct"
    crossorigin="anonymous"></script>
