<div class="mr-4 ml-4">

  <nav aria-label="breadcrumb">
    <ol class="breadcrumb mt-2">
      <li class="breadcrumb-item"><a href="{{app_context}}/articles">{{translate "Home"}}</a></li>
      <li class="breadcrumb-item active" aria-current="page">{{translate "Articles"}}</li>
    </ol>
  </nav>

	<h4 class="mt-3">{{translate "Articles"}}
		<div class="pull-right">
			<a href="{{app_context}}/insert" class="btn btn-sm btn-outline-primary" type="button" form="settingsForm">
				<i class="fa fa-plus" aria-hidden="true"></i>
				{{translate "New Article"}}
			</a>
		</div>
	</h4>
				
	<div class="row mt-4 search_bar pad-bottom">
		<div class="col-12">
			<div class="input-group">
				<input
					type="text"
					name="article_filter"
					id="article_filter"
					class="form-control form-control-sm mr-1"
					placeholder="{{translate "Filter article using title"}}"
				/>
				<select class="form-control form-control-sm mr-1" id="articleTopicOptions">
					<option value="">{{translate "All"}}</option>
				</select>
				<select class="form-control form-control-sm mr-1" id="articleSubTopicOptions">
					<option value="">{{translate "All"}}</option>
				</select>
        <select class="form-control form-control-sm mr-1" id="articleRegionOptions">
					<option value="">{{translate "All"}}</option>
				</select>

				<span class="input-group-btn">
					<button class="btn btn-sm btn-outline-success" id="btn_articles_filter">{{translate "Filter"}}</button>
					<button class="btn btn-sm btn-outline-secondary" id="btn_articles_reset">{{translate "Reset Filters"}}</button>
					{{!-- <a class="btn btn-sm btn-outline-info article-btn" href="{{app_context}}/articles/all">{{__ "All"}}</a> --}}
				</span>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-12">
			<ul class="list-group" id="articleSearchResults">
				<li class="list-group-item">
					<strong>{{translate "Articles"}}</strong>
				</li>
			</ul>
		</div>
	</div>
  
  {{!-- {{#if results}}
    <div class="row">
      <div class="col-12">
        <ul class="list-group">
          <li class="list-group-item">
            <span class="pull-right"><strong>{{__ "Published"}}</strong></span>
            <strong>{{__ "Articles"}}
              -
              <span class="text-danger">{{__ "Filtered term"}}: {{search_term}} </span></strong>
          </li>
          {{#each results}}
            <li class="list-group-item">
              <div class="row">
                <div class="col-lg-8"><a href="{{app_context}}/edit/{{this._id}}">{{this.kb_title}}</a></div>
                <div class="col-lg-4 text-right">
                  <input
                    id="{{this._id}}"
                    class="published_state"
                    type="checkbox"
                    {{checked_state this.kb_published}}
                  />
                  <a
                    href="{{app_context}}/delete/{{this._id}}"
                    onclick="return confirm('{{__ 'Are you sure you want to delete this article?'}}');"
                  > <i class="fa fa-trash-o"></i></a>
                </div>
              </div>
            </li>
          {{/each}}
        </ul>
      </div>
    </div>
  {{else}}
    <div class="row articles-container">
      <div class="col-12">
        <ul class="list-group">
          <li class="list-group-item">
            <span class="pull-right"><strong>{{__ "Published"}}</strong></span>
            <strong>{{__ "Article"}} - {{__ "Recent"}}</strong>
          </li>
          {{#each articles}}
            <li class="list-group-item">
              <div class="row">
                <div class="col-lg-8"><a href="{{app_context}}/edit/{{this._id}}">{{this.kb_title}}</a></div>
                <div class="col-lg-4 text-right">
                  <input
                    id="{{this._id}}"
                    class="published_state"
                    type="checkbox"
                    {{checked_state this.kb_published}}
                  />
                  <a
                    href="{{app_context}}/delete/{{this._id}}"
                    onclick="return confirm('{{__ 'Are you sure you want to delete this article?'}}');"
                  > <i class="fa fa-trash-o"></i></a>
                </div>
              </div>
            </li>
          {{/each}}
        </ul>
      </div>
    </div>
  {{/if}} --}}
</div>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/articles-script.js"></script>