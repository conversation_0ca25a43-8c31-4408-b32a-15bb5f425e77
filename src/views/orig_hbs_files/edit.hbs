
    <div class="col-sm-12 mt-3" style="overflow: hidden;">
        <form method="post" id="edit_form" action="{{app_context}}/save_kb" data-toggle="validator">
            {{!-- <div class="row mb-2">
                <div class="col-sm-12 text-right">
                    <button type="button" id="frm_edit_kb_save" class="btn btn-success editor_btn_action"><i class="fa fa-floppy-o" aria-hidden="true"></i> Save Changes</button>
                </div>
            </div> --}}
            <div class="row">

                <div class="col-xs-12 col-lg-6">
                    <div class="form-group">
                        <label for="frm_kb_title">{{__ "Article title"}} *</label><br/>
                        <input type="text" name="frm_kb_title" id="frm_kb_title" class="form-control input-normal" minlength="5" maxlength="200" value="{{result.kb_title}}" required/>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-4 col-md-4 col-lg-2">
                    <label for="frm_kb_password">{{__ "Password"}}</label><br/>
                    <input id="frm_kb_password" class="form-control" name="frm_kb_password" type="text" value="{{result.kb_password}}" />
                </div>
                <div class="col-xs-12 col-sm-4 col-md-4 col-lg-2">
                    <label for="frm_kb_published">{{__ "Status"}}</label><br/>
                    <select class="form-control" id="frm_kb_published" name="frm_kb_published">
                        {{#ifCond result.kb_published "==" "true"}}
                            <option value="true" selected>{{__ "Published"}}</option>
                            <option value="false">{{__ "Draft"}}</option>
                        {{else}}
                            <option value="true">{{__ "Published"}}</option>
                            <option value="false" selected>{{__ "Draft"}}</option>
                        {{/ifCond}}
                    </select>
                </div>
                <div class="col-xs-12 col-sm-4 col-md-4 col-lg-2 text-right">
                    <label for="">&nbsp;</label><br/>
                    <div class="btn-group btn-group-justified" role="group" aria-label="..." style="width: 100%;">
                        {{#ifCond config.settings.article_versioning '===' true}}
                            <a href="#" class="btn btn-warning toggle-menu menu-left push-body"><i class="fa fa-code-fork" aria-hidden="true"></i></a>
                        {{/ifCond}}
                        <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#settingsModal" data-whatever="{{result}}"><i class="fa fa-cog" aria-hidden="true"></i> Settings</button>
                        <button type="button" id="frm_edit_kb_save" class="btn btn-success editor_btn_action"><i class="fa fa-floppy-o" aria-hidden="true"></i> Save</button>

                        {{!-- <a href="#" class="btn btn-default toggle-menu menu-right push-body"><i class="fa fa-cog" aria-hidden="true"></i></a> --}}
                        {{!-- <a href="#" id="frm_edit_kb_save" class="btn btn-success editor_btn_action"><i class="fa fa-floppy-o" aria-hidden="true"></i></a> --}}
                    </div>
                </div>

                {{!-- ADDITIONAL SETTINGS MODAL --}}
                <div class="modal fade" id="settingsModal" tabindex="-1" role="dialog" aria-labelledby="settingsModalLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="settingsModalLabel">Article Settings</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mr-2 ml-2">

                                    <div class="row mb-2">
                                        <div class="col-6 mr-auto">
                                            <div class="form-group">
                                                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                                    <a class="btn btn-sm btn-warning" target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/{{result._id}}">{{__ "Preview"}}</a>
                                                    <a class="btn btn-sm btn-danger" href="{{app_context}}/delete/{{result._id}}" id="del_post" onclick="return confirm('Are you sure you want to delete this article?');">{{__ "Delete"}}</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6 text-right">
                                            <div class="form-group">
                                                <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                                    <a class="btn btn-sm btn-info" target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/resetviewCount/{{result._id}}">{{__ "Reset views"}}</a>
                                                    <a class="btn btn-sm btn-secondary" target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/resetvoteCount/{{result._id}}">{{__ "Reset votes"}}</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox" style="margin: 0">
                                            <label>
                                                <input type="checkbox" name="frm_kb_dashboard" {{checked_state result.kb_dashboard}}> {{__ "Pin to dashboard"}}
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="frm_kb_featured" {{checked_state result.kb_featured}}> {{__ "Featured article"}}
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox">
                                            <label>
                                                <input id="kb_pinned" type="checkbox" name="frm_kb_pinned" {{checked_state result.kb_pinned}}> {{__ "Pin to topic"}}
                                            </label>
                                        </div>
                                        <div class="pinDiv">
                                            <select class="form-control mb-3" name="frm_kb_pinned_topic" aria-expanded="true">
                                                <option
                                                    {{select_state result.kb_pinned_topic 'ExceedExpectations'}}
                                                    value="ExceedExpectations"
                                                    >Exceed Member Expectations</option>
                                                <option
                                                    {{select_state result.kb_pinned_topic 'MemberGrowth'}}
                                                    value="MemberGrowth"
                                                    >Member Growth</option>
                                                <option
                                                    {{select_state result.kb_pinned_topic 'EngagementCulture'}}
                                                    value="EngagementCulture"
                                                    >Engagement and Culture</option>
                                                <option
                                                    {{select_state result.kb_pinned_topic 'BusinessOptimisation'}}
                                                    value="BusinessOptimisation"
                                                    >Business Optimisation</option>
                                                <option
                                                    {{select_state result.kb_pinned_topic 'OperatingProcedures'}}
                                                    value="OperatingProcedures"
                                                    >Operating Procedures</option>
                                            </select>
                                        </div>
                                        <div id="kb_pinned_topic" class="collapse {{#if (checked_state result.kb_pinned)}}in{{/if}}">

                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="frm_kb_visible_state" >{{__ "Visible state"}}</label>
                                        <select class="form-control" id="frm_kb_visible_state" name="frm_kb_visible_state">
                                            <option {{select_state result.kb_visible_state 'public'}} value="public">{{__ "Public"}}</option>
                                            <option {{select_state result.kb_visible_state 'private'}} value="private">{{__ "Private"}}</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="frm_kb_security_level">Security Level</label>
                                        <select class="form-control" id="frm_kb_security_level" name="frm_kb_security_level">
                                            <option {{select_state result.kb_security_level 100}} value="100">100 Standard User</option>
                                            <option {{select_state result.kb_security_level 200}} value="200">200 Club Manager</option>
                                            <option {{select_state result.kb_security_level 300}} value="300">300 Club Owner</option>
                                            <option {{select_state result.kb_security_level 400}} value="400">400 Admin Only</option>
                                        </select>
                                    </div>

                                    {{!-- <div class="form-group">
                                        <label>Regional Visibility</label>
                                        <select class="form-control" id="frm_kb_regional_visibility" name="frm_kb_regional_visibility"
                                            data-options="{{result.countries}}"></select>
                                        </select>
                                    </div> --}}

                                    <div class="form-group">
                                         {{!-- onclick="showCheckboxes()" --}}
                                        <div class="selectBox">
                                            <label>Regional Visibility</label>
                                            <input type="hidden" id="frm_kb_regional_visibility" name="frm_kb_regional_visibility"
                                                value="{{result.kb_regional_visibility}}">
                                            <select class="form-control" id="selectCountryDropdown" data-options="{{result.countries}}">
                                                <option value="" id="optionSelectCountry"></option>
                                            </select>
                                            <div class="overSelect" id="overSelect"></div>
                                        </div>
                                        <div id="countryCheckboxes">
                                        </div>
                                    </div>

                                    <div class="form-group d-none">
                                        <label for="frm_kb_seo_title">{{__ "SEO title"}}</label>
                                        <textarea class="form-control" name="frm_kb_seo_title" rows="3">{{if_null result.kb_seo_title result.kb_title}}</textarea>
                                    </div>
                                    <div class="form-group d-none">
                                        <label for="frm_kb_seo_title">{{__ "SEO description"}}</label>
                                        <textarea class="form-control" name="frm_kb_seo_description" rows="3">{{if_null result.kb_seo_description (substring (strip_md result.kb_body) 160)}}</textarea>
                                    </div>
                                </div>

                            </div>
                            {{!-- <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary">Send message</button>
                            </div> --}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-md-12 col-lg-6">
                    <div class="form-group" id="editor-wrapper" >
                        <label for="editor">{{__ "Article body"}} (Markdown) *</label>
                        <textarea id="editor" minlength="5" name="frm_kb_body" data-provide="markdown"
                            data-hidden-buttons="cmdPreview" data-iconlibrary="fa" class="form-control" required>{{{result.kb_body}}}</textarea>
                    </div>
                </div>
                <div class="col-xs-12 col-md-6 col-lg-6">
                    <div class="form-group" id="preview-wrapper">
                        <label for="preview">{{__ "Preview"}}</label>
                        <div id="preview" name="preview" class="form-control" rows="5"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-4 col-lg-6">
                    <div class="form-group">
                        <input type="text" class="form-control" name="frm_kb_keywords" id="frm_kb_keywords"
                            placeholder="{{__ "keyword"}}" value="{{result.kb_keywords}}">
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                    <input type="text" class="form-control" name="frm_kb_permalink" id="frm_kb_permalink"
                        placeholder="{{__ "permalink"}}" value="{{result.kb_permalink}}">
                </div>
                <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="btn-group btn-group-justified pull-right " role="group" aria-label="...">
                        <a href="#" class="btn btn-success" id="validate_permalink" type="button">{{__ "Validate"}}</a>
                        <a href="#" class="btn btn-warning" id="generate_permalink" type="button">{{__ "Generate"}}</a>
                        <a href="#" class="btn btn-info disabled" id="generate_permalink_from_title" type="button">Slug</a>
                        {{!-- <a href="#" class="btn btn-info disabled" id="generate_permalink_from_title" type="button">{{__ "Slug from title"}}</a> --}}
                    </div>
                </div>
            </div>
            {{!-- <footer>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <input type="text" class="form-control" name="frm_kb_keywords" id="frm_kb_keywords" placeholder="{{__ "keyword"}}" value="{{result.kb_keywords}}">
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-6 col-md-6 col-lg-3">
                        <input type="text" class="form-control" name="frm_kb_permalink" id="frm_kb_permalink" placeholder="{{__ "permalink"}}" value="{{result.kb_permalink}}">
                    </div>
                    <div class="col-xs-12 col-sm-6 col-md-6 col-lg-3">
                        <div class="btn-group btn-group-justified pull-right " role="group" aria-label="...">
                            <a href="#" class="btn btn-success" id="validate_permalink" type="button">{{__ "Validate"}}</a>
                            <a href="#" class="btn btn-warning" id="generate_permalink" type="button">{{__ "Generate"}}</a>
                            <a href="#" class="btn btn-info disabled" id="generate_permalink_from_title" type="button">{{__ "Slug from title"}}</a>
                        </div>
                    </div>
                </div>
            </footer> --}}

            <input type="hidden" name="frm_kb_id" id="frm_kb_id" value="{{result._id}}" />
            <input type="hidden" id="frm_editor" value="true" />

            <nav class="cbp-spmenu cbp-spmenu-vertical cbp-spmenu-right">
                <div class="cbp-spmenu-header"><div class="pull-left">{{__ "Settings"}}</div><button id="btnSettingsMenu" class="btn btn-default pull-right toggle-menu menu-right push-body"><i class="fa fa-cog"></i></div></button>
                <ul class="list-group list-group-sidemenu">
                    {{!-- <li class="list-group-item">
                        <div class="btn-group btn-group-justified" role="group" aria-label="...">
                            <a class="btn btn-sm btn-warning" target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/{{result._id}}">{{__ "Preview"}}</a>
                            <a class="btn btn-sm btn-danger" href="{{app_context}}/delete/{{result._id}}" id="del_post" onclick="return confirm('Are you sure you want to delete this article?');">{{__ "Delete"}}</a>
                        </div>
                        &nbsp;
                        <div class="btn-group btn-group-justified" role="group" aria-label="...">
                            <a class="btn btn-sm btn-info" target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/resetviewCount/{{result._id}}">{{__ "Reset views"}}</a>
                            <a class="btn btn-sm btn-default" target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/resetvoteCount/{{result._id}}">{{__ "Reset votes"}}</a>
                        </div>
                    </li>
                    <li class="list-group-item" style="padding-bottom: 0">
                        <div class="checkbox" style="margin: 0">
                            <label>
                                <input type="checkbox" name="frm_kb_dashboard" {{checked_state result.kb_dashboard}}> {{__ "Pin to dashboard"}}
                            </label>
                        </div>
                    </li>

                    <li class="list-group-item">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="frm_kb_featured" {{checked_state result.kb_featured}}> {{__ "Featured article"}}
                            </label>
                        </div>
                    </li>


                    <li class="list-group-item" style="padding-bottom: 0">
                        <div class="checkbox">
                            <label>
                                <input id="kb_pinned" type="checkbox" name="frm_kb_pinned" {{checked_state result.kb_pinned}}> {{__ "Pin to topic"}}
                            </label>
                        </div>
                        <div id="kb_pinned_topic" class="collapse {{#if (checked_state result.kb_pinned)}}in{{/if}}">
                            <select class="form-control mb-3" name="frm_kb_pinned_topic" aria-expanded="true">
                                <option
                                    {{select_state result.kb_pinned_topic 'ExceedExpectations'}}
                                    value="ExceedExpectations"
                                    >Exceed Member Expectations</option>
                                <option
                                    {{select_state result.kb_pinned_topic 'MemberGrowth'}}
                                    value="MemberGrowth"
                                    >Member Growth</option>
                                <option
                                    {{select_state result.kb_pinned_topic 'EngagementCulture'}}
                                    value="EngagementCulture"
                                    >Engagement and Culture</option>
                                <option
                                    {{select_state result.kb_pinned_topic 'BusinessOptimisation'}}
                                    value="BusinessOptimisation"
                                    >Business Optimisation</option>
                                <option
                                    {{select_state result.kb_pinned_topic 'OperatingProcedures'}}
                                    value="OperatingProcedures"
                                    >Operating Procedures</option>
                            </select>
                        </div>
                    </li> --}}



                    {{!-- <li class="list-group-item">
                        <label>{{__ "Visible state"}}</label>
                        <select class="form-control" id="frm_kb_visible_state" name="frm_kb_visible_state">
                            <option {{select_state result.kb_visible_state 'public'}} value="public">{{__ "Public"}}</option>
                            <option {{select_state result.kb_visible_state 'private'}} value="private">{{__ "Private"}}</option>
                        </select>
                    </li> --}}

                    {{!-- <li class="list-group-item">
                        <label>Security Level</label>
                        <select class="form-control" id="frm_kb_security_level" name="frm_kb_security_level">
                            <option {{select_state result.kb_security_level '100'}} value="100">Standard User</option>
                            <option {{select_state result.kb_security_level '200'}} value="200">Club Manager</option>
                            <option {{select_state result.kb_security_level '300'}} value="300">Club Owner</option>
                            <option {{select_state result.kb_security_level '400'}} value="400">Admin Only</option>
                        </select>
                    </li> --}}

                    {{!-- <li class="list-group-item">
                        <label for="frm_kb_seo_title">{{__ "SEO title"}}</label>
                        <textarea class="form-control" name="frm_kb_seo_title" rows="3">{{if_null result.kb_seo_title result.kb_title}}</textarea>
                    </li>
                    <li class="list-group-item">
                        <label for="frm_kb_seo_title">{{__ "SEO description"}}</label>
                        <textarea class="form-control" name="frm_kb_seo_description" rows="3">{{if_null result.kb_seo_description (substring (strip_md result.kb_body) 160)}}</textarea>
                    </li> --}}
                </ul>
            </nav>
            {{!-- {{#ifCond config.settings.article_versioning '===' true}}
                <nav id="versionSidebar" class="cbp-spmenu cbp-spmenu-vertical cbp-spmenu-left" style="overflow-y: auto;">
                    <div class="cbp-spmenu-header"><div class="pull-left">{{__ "Versions"}}</div><button id="btnVersionMenu" class="btn btn-warning pull-right toggle-menu menu-left push-body"><i class="fa fa-code-fork" aria-hidden="true"></i></div></button>
                    <ul class="list-group list-group-sidemenu">
                        <li class="list-group-item">
                            <label for="frm_kb_edit_reason">{{__ "Edit reason"}}</label>
                            <textarea class="form-control" name="frm_kb_edit_reason" id="frm_kb_edit_reason" rows="3"></textarea>
                        </li>
                        <li class="list-group-item">
                            {{#if versions}}
                                <h4>{{__ "Previous versions"}}</h4>
                                    {{#each versions}}
                                        <div class="versionWrapper">
                                            <span>{{__ "Date"}}: {{format_date this.kb_last_updated}}</span>
                                            <textarea class="form-control versionArea" readonly rows="2">{{this.kb_edit_reason}}</textarea>
                                            <div id="{{this._id}}" class="btn-group btn-group-justified">
                                                <a href="#" class="btnDeleteVersion btn btn-xs btn-danger">{{__ "Delete"}}</a>
                                                <a href="#"  class="btnEditVersion btn btn-xs btn-warning">{{__ "Edit"}}</a>
                                                <a target="_blank" href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}/version" class=" btn btn-xs btn-info">{{__ "Preview"}}</a>
                                            </div>
                                            <hr>
                                        </div>
                                    {{/each}}
                                </select>
                            {{/if}}
                        </li>
                    </ul>
                </nav>
            {{/ifCond}} --}}
        </form>
    </div>

<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/edit-article.js"></script>