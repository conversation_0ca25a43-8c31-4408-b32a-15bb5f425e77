<!DOCTYPE html>
<html lang="{{config.settings.locale}}">
  <head>
    {{#if result.kb_seo_title}}
      <title>{{result.kb_seo_title}}</title>
      <meta property="og:title" content="{{result.kb_seo_title}}" />
    {{else}}
      {{#if result.kb_title}}
        <title>{{result.kb_title}}</title>
        <meta property="og:title" content="{{result.kb_title}}" />
      {{else}}
        <title>{{title}}</title>
        <meta property="og:title" content="{{title}}" />
      {{/if}}
    {{/if}}
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    {{#if result.kb_seo_description}}
      <meta name="description" content="{{result.kb_seo_description}}">
      <meta property="og:description" content="{{result.kb_seo_description}}" />
    {{else}}
      {{#if result.kb_body}}
        <meta name="description" content="{{substring (strip_md result.kb_body) 160}}">
        <meta property="og:description" content="{{substring (strip_md result.kb_body) 160}}" />
      {{else}}
        {{#ifCond homepage '===' true}}
          <meta name="description" content="{{config.settings.website_description}}">
          <meta property="og:description" content="{{config.settings.website_description}}" />
        {{else}}
          <meta name="description" content="{{__ "Powered by"}} openKB">
          <meta property="og:description" content="openKB is an Open Source Node.js Markdown based knowledge base/FAQ/Wiki app" />
        {{/ifCond}}
      {{/if}}
    {{/if}}
    {{#if result.kb_keywords}}
      <meta name="keywords" content="{{result.kb_keywords}}">
    {{else}}
      <meta name="keywords" content="openKB">
    {{/if}}
    <meta property="og:type" content="article" />
    <meta property="og:image" content="{{current_url}}/logo.png" />
    <meta property="og:url" content="{{fullUrl}}" />
    <link rel="canonical" href="{{fullUrl}}" />
    {{!-- <link rel="stylesheet" href="{{app_context}}/stylesheets/flatly.bootstrap.min.css"> --}}
     <link rel="stylesheet" href="{{app_context}}/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{app_context}}/simplemde/simplemde.min.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/github{{config.settings.env}}.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/style{{config.settings.env}}.css">
    <link rel="stylesheet" href="{{app_context}}/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/mermaid{{config.settings.env}}.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/bootstrap-tokenfield{{config.settings.env}}.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/jasny-bootstrap.min.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/jpushmenu{{config.settings.env}}.css">
    <link rel="stylesheet" href="{{app_context}}/themes/12rnd/css/style.css">
    <link rel="stylesheet" href="{{app_context}}/themes/12rnd/css/newstyles.css">

    <!--Import Google Icon Font-->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Khula:wght@400;600&display=swap" rel="stylesheet">


    <link rel='icon' href='{{app_context}}/favicon.png' />
    <script src="{{app_context}}/jquery/jquery.min.js"></script>
    <script>{{{state}}}</script>
    <script src="{{app_context}}/bootstrap/js/bootstrap.min.js"></script>
    {{!-- {{#ifCond title '===' 'Settings'}}
        <script src="{{app_context}}/bootstrapTabs/tab.js"></script>
    {{/ifCond}} --}}
    <script src="{{app_context}}/javascripts/bootstrap-validator{{config.settings.env}}.js"></script>
    {{#ifCond config.settings.mermaid '===' true}}
        <script src="{{app_context}}/javascripts/mermaid.min.js"></script>
    {{/ifCond}}
    {{#ifCond config.settings.mathjax '===' true}}
        <script type="text/x-mathjax-config">
            MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']], processEscapes: true}});
        </script>
        <script async src="https://cdn.mathjax.org/mathjax/latest/MathJax.js?config={{config.settings.mathjax_input_mode}}"></script>
    {{/ifCond}}
    <!-- Popper JS -->
    {{!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js" integrity="sha384-b/U6ypiBEHpOf/4+1nzFpr53nxSS+GLCkfwBdFNTxtclqqenISfwAzpKaMNFNmj4" crossorigin="anonymous"></script> --}}
    {{!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/esm/popper.min.js"></script> --}}
    {{!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js" integrity="sha512-2rNj2KJ+D8s1ceNasTIex6z4HWyOnEYLVC3FigGOmyQCZc2eBXKgOxQmo3oKLHyfcj53uz4QMsRCWNbLd32Q1g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script> --}}

    <script src="{{app_context}}/javascripts/markdown-it-classy{{config.settings.env}}.js"></script>
    <script src="{{app_context}}/javascripts/bootstrap-tokenfield.min.js"></script>
    <script src="{{app_context}}/javascripts/inline-attachment{{config.settings.env}}.js"></script>
    <script src="{{app_context}}/javascripts/codemirror.inline-attachment.js"></script>
    <script src="{{app_context}}/javascripts/highlight.min.js"></script>
    <script src="{{app_context}}/javascripts/loadingoverlay.min.js"></script>
    <script src="{{app_context}}/simplemde/simplemde.min.js"></script>
    <script src="{{app_context}}/javascripts/jasny-bootstrap.min.js"></script>
    <script src="{{app_context}}/markdown-it/markdown-it.min.js"></script>
    <script src="{{app_context}}/javascripts/sanitizer.min.js"></script>
    <script src="{{app_context}}/javascripts/jpushmenu{{config.settings.env}}.js"></script>
    <script src="{{app_context}}/javascripts/openKB{{config.settings.env}}.js"></script>
    <script src="{{app_context}}/themes/12rnd/js/overrides.js"></script>
    {{#ifCond user_page '===' true}}
      <style>{{{simpleCSS config}}}</style>
      {{#if config.settings.google_analytics}}
        {{{config.settings.google_analytics}}}
      {{/if}}
    {{/ifCond}}
  </head>
  <body class="{{show_footer}}">
      {{!-- {{> 12rnd/views/partials/navbar}} --}}

      <div class="body-min">
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N9RB97L"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
          {{{body}}}
      </div>

    <input type="hidden" id="input_notify_message" value="{{message}}">
    <input type="hidden" id="input_notify_message_type" value="{{message_type}}">
    <input type="hidden" value="{{app_context}}" id="app_context">
    <input type="hidden" value="{{config.settings.links_blank_page}}" id="blank_links">
    <div class="clearfix" id="notify_message"></div>
    {{#if show_footer}}
      <footer class="footer">
        <div class="container">
          <p class="text-muted">{{__ "Powered by"}} <a href="http://openkb.mrvautin.com/" target="_blank">openKB</a></p>
        </div>
      </footer>
    {{/if}}
  </body>
</html>
