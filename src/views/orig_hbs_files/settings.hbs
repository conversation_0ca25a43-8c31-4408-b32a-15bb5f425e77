
            {{!-- <ol class="breadcrumb">
                <li><a href="{{app_context}}/">{{__ "Home"}}</a></li>
                <li class="active">{{__ "Settings"}}</li>
            </ol> --}}
            {{!-- <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{app_context}}/">{{__ "Home"}}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{__ "Settings"}}</li>
                </ol>
			</nav> --}}
<div class="container">


<form method="post" action="{{app_context}}/update_settings" id="settingsForm">
    <h4 class=" mt-4">{{__ "Settings"}}
        <div class="pull-right">
            <button class="btn btn-success" type="submit" form="settingsForm">Save Changes</button>
            {{!-- <button class="btn btn-success" type="submit">{{__ "Update"}}</button> --}}
        </div>
    </h4>

    <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
            <a class="nav-item nav-link active" id="nav-website-tab" data-toggle="tab" href="#nav-website" role="tab"
            aria-controls="nav-website" aria-selected="true">{{__ "Website"}}</a>

            <a class="nav-item nav-link" id="nav-article-tab" data-toggle="tab" href="#nav-article" role="tab"
                aria-controls="nav-article" aria-selected="false">{{__ "Article"}}</a>

            <a class="nav-item nav-link" id="nav-display-tab" data-toggle="tab" href="#nav-display" role="tab"
                aria-controls="nav-display" aria-selected="false">{{__ "Display"}}</a>

            <a class="nav-item nav-link" id="nav-style-tab" data-toggle="tab" href="#nav-style" role="tab"
                aria-controls="nav-style" aria-selected="false">{{__ "Style"}}</a>
        </div>
    </nav>

    <div class="tab-content" id="nav-tabContent">
        {{!-- Website Tab Contents --}}
        <div class="container tab-pane fade show active tab-panel-container" id="nav-website"
            role="tabpanel" aria-labelledby="nav-home-tab" >
            {{!-- <h5 class="pull-right mb-4">Website</h5> --}}

            <div class="form-group mt-4">
                <label class="control-label" for="website_title">{{__ "Website title"}}:</label>
                <input type="text" class="form-control" name="website_title" value="{{config.settings.website_title}}" required>
                {{!-- <p class="help-block">
                    {{__ "The title of your website"}}.
                </p> --}}
                <small>{{__ "The title of your website"}}.</small>
            </div>
            <div class="form-group">
                <label class="control-label" for="website_description">{{__ "Website description"}}:</label>
                <input type="text" class="form-control" name="website_description" value="{{config.settings.website_description}}" required>
                <small class="help-block">
                    {{__ "A short website description when listing the homepage URL in search engines"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_website_logo">{{__ "Show website logo"}}:</label>
                <select class="form-control" name="show_website_logo">
                    <option {{select_state config.settings.show_website_logo true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_website_logo false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Controls whether to show the \"Website title\" text or a logo located: \"/public/logo.png\" (by default)"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="app_context">{{__ "Website context/base URL"}}:</label>
                <input type="text" class="form-control" name="app_context" value="{{config.settings.app_context}}" >
                <small class="help-block">
                    {{__ "Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="api_allowed">{{__ "Allow API access"}}:</label>
                <select class="form-control" name="api_allowed">
                    <option {{select_state config.settings.api_allowed true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.api_allowed false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to allow API access to insert articles - See documentation for further information"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="api_auth_token">{{__ "API access token"}}:</label>
                <input type="text" class="form-control" name="api_auth_token" value="{{config.settings.api_auth_token}}" >
                <small class="help-block">
                    {{__ "Requires \"Allow API access\" to be set to \"true\". The value is the access token required to access the public API. Please set to a hard to guess value"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="password_protect">{{__ "Password protect"}}:</label>
                <select class="form-control" name="password_protect">
                    <option {{select_state config.settings.password_protect true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.password_protect false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Setting to \"true\" will require a user to login before viewing any pages"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="index_article_body">{{__ "Index article body"}}:</label>
                <select class="form-control" name="index_article_body">
                    <option {{select_state config.settings.index_article_body true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.index_article_body false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to add the body of your articles to the search index (requires restart)"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_kb_meta">{{__ "Select a theme"}}:</label>
                <select class="form-control" name="theme">
                    <option></option>
                    {{#each themes}}
                        <option {{select_state @root.config.settings.theme this}}>{{this}}</option>
                    {{/each}}
                </select>
                <small class="help-block">
                    {{__ "The theme to use for public facing pages. Leave blank for default"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_kb_meta">{{__ "Select a language"}}:</label>
                <select class="form-control" name="locale">
                    <option></option>
                    {{#each locale}}
                        <option {{select_state @root.config.settings.locale this}}>{{this}}</option>
                    {{/each}}
                </select>
                <small class="help-block">
                    {{__ "The language to use for public facing pages. Leave blank for default (English)"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_logon">{{__ "Show logon link"}}:</label>
                <select class="form-control" name="show_logon">
                    <option {{select_state config.settings.show_logon true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_logon false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to show/hide the logon link in the top right of screen"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="date_format">{{__ "Date format"}}:</label>
                <input type="text" class="form-control" name="date_format" value="{{config.settings.date_format}}" required>
                <small class="help-block">
                    {{__ "Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="suggest_allowed">{{__ "Article suggestions allowed"}}:</label>
                <select class="form-control" name="suggest_allowed">
                    <option {{select_state config.settings.suggest_allowed true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.suggest_allowed false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "If enabled non authenticated users can submit article suggestions for approval"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="links_blank_page">{{__ "Google analytics code"}}:</label>
                <textarea rows="5" class="form-control" name="google_analytics">{{config.settings.google_analytics}}</textarea>
                <small class="help-block">
                    {{__ "Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags"}}.
                </small>
            </div>

        </div>

        {{!-- Article Tab Contents --}}
        <div class="container tab-pane fade tab-panel-container" id="nav-article" role="tabpanel" aria-labelledby="nav-article-tab">
            <div class="form-group mt-4">
                <label class="control-label" for="allow_voting">{{__ "Allow voting"}}:</label>
                <select class="form-control" name="allow_voting">
                    <option {{select_state config.settings.allow_voting true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.allow_voting false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to allow users to vote on an article"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_kb_meta">{{__ "Show article meta data"}}:</label>
                <select class="form-control" name="show_kb_meta">
                    <option {{select_state config.settings.show_kb_meta true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_kb_meta false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to show article meta data including published date, last updated date, author etc"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_author_email">{{__ "Show author email"}}:</label>
                <select class="form-control" name="show_author_email">
                    <option {{select_state config.settings.show_author_email true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_author_email false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Controls whether the authors email address is displayed in the meta. Requires \"Show article meta data\" to be true"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="links_blank_page">{{__ "Article links open new page"}}:</label>
                <select class="form-control" name="links_blank_page">
                    <option {{select_state config.settings.links_blank_page true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.links_blank_page false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Controls whether links within articles open a new page (tab)"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="add_header_anchors">{{__ "Add header anchors"}}:</label>
                <select class="form-control" name="add_header_anchors">
                    <option {{select_state config.settings.add_header_anchors true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.add_header_anchors false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="enable_spellchecker">{{__ "Enable editor spellchecker"}}:</label>
                <select class="form-control" name="enable_spellchecker">
                    <option {{select_state config.settings.enable_spellchecker true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.enable_spellchecker false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Controls whether to enable the editor spellchecker"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="article_versioning">{{__ "Allow article versioning"}}:</label>
                <select class="form-control" name="article_versioning">
                    <option {{select_state config.settings.article_versioning true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.article_versioning false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to track article versions with each save of the editor"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="mermaid">{{__ "Allow Mermaid"}}:</label>
                <select class="form-control" name="mermaid">
                    <option {{select_state config.settings.mermaid true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.mermaid false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to enable "}} <a href="http://knsv.github.io/mermaid/" target="_blank">mermaid</a>.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="mathjax">{{__ "Allow MathJax"}}:</label>
                <select class="form-control" name="mathjax">
                    <option {{select_state config.settings.mathjax true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.mathjax false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to enable "}} <a href="https://www.mathjax.org/" target="_blank">MathJax</a>.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="mathjax_input_mode">{{__ "MathJax input mode"}}:</label>
                <input type="text" class="form-control" name="mathjax_input_mode" value="{{config.settings.mathjax_input_mode}}" required>
                <small class="help-block">
                    {{__ "Configure the mode MathJax operates in - "}} <a href="http://docs.mathjax.org/en/latest/configuration.html#configuring-mathjax" target="_blank">Configure MathJax</a>.
                </small>
            </div>
        </div>

        {{!-- Display Tab Contents --}}
        <div class="container tab-pane fade tab-panel-container" id="nav-display" role="tabpanel" aria-labelledby="nav-display-tab">
            <div class="form-group mt-4">
                <label class="control-label" for="num_top_results">{{__ "Number of top articles shown on homepage"}}:</label>
                <input type="number" class="form-control" name="num_top_results" value="{{config.settings.num_top_results}}" required>
                <small class="help-block">
                    {{__ "Sets the number of results shown on the home page"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_published_date">{{__ "Show published date"}}:</label>
                <select class="form-control" name="show_published_date">
                    <option {{select_state config.settings.show_published_date true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_published_date false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Shows the published date next to the results on the homepage and search"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_view_count">{{__ "Show view count"}}:</label>
                <select class="form-control" name="show_view_count">
                    <option {{select_state config.settings.show_view_count true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_view_count false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Shows the view count next to the results on the homepage and search"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="update_view_count_logged_in">{{__ "Update view count when logged in"}}:</label>
                <select class="form-control" name="update_view_count_logged_in">
                    <option {{select_state config.settings.update_view_count_logged_in true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.update_view_count_logged_in false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Updates the view count also when users are logged in"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_featured_articles">{{__ "Show featured articles"}}:</label>
                <select class="form-control" name="show_featured_articles">
                    <option {{select_state config.settings.show_featured_articles true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_featured_articles false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to show any articles set to featured in a sidebar"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="show_featured_in_article">{{__ "Show featured articles when viewing article"}}:</label>
                <select class="form-control" name="show_featured_in_article">
                    <option {{select_state config.settings.show_featured_in_article true}} value="true">{{__ "true"}}</option>
                    <option {{select_state config.settings.show_featured_in_article false}} value="false">{{__ "false"}}</option>
                </select>
                <small class="help-block">
                    {{__ "Whether to show any articles set to featured in a sidebar when viewing an article"}}.
                </small>
            </div>
            <div class="form-group">
                <label class="control-label" for="featured_articles_count">{{__ "Featured article count"}}:</label>
                <input type="number" class="form-control" name="featured_articles_count" value="{{config.settings.featured_articles_count}}" required>
                <small class="help-block">
                    {{__ "The number of featured articles shown"}}.
                </small>
            </div>
        </div>

        {{!-- Style Tab Contents --}}
        <div class="tab-pane fade" id="nav-style" role="tabpanel" aria-labelledby="nav-style-tab">
            <div class="container mt-4 tab-panel-container">
                <div class="row">
                    <p class="col-md-12">
                        You can add simple styling to various elements of your website below:
                    </p>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssHeaderBackgroundColor">Header background color (HEX):</label>
                            <input type="text" class="form-control" name="style.cssHeaderBackgroundColor" value="{{config.settings.style.cssHeaderBackgroundColor}}" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssHeaderTextColor">Header text color (HEX):</label>
                            <input type="text" class="form-control" name="style.cssHeaderTextColor" value="{{config.settings.style.cssHeaderTextColor}}" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssFooterBackgroundColor">Footer background color (HEX):</label>
                            <input type="input" class="form-control" name="style.cssFooterBackgroundColor" value="{{config.settings.style.cssFooterBackgroundColor}}"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssFooterTextColor">Footer text color (HEX):</label>
                            <input type="input" class="form-control" name="style.cssFooterTextColor" value="{{config.settings.style.cssFooterTextColor}}"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssButtonBackgroundColor">Button background color (HEX):</label>
                            <input type="input" class="form-control" name="style.cssButtonBackgroundColor" value="{{config.settings.style.cssButtonBackgroundColor}}"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssButtonTextColor">Button text color (HEX):</label>
                            <input type="input" class="form-control" name="style.cssButtonTextColor" value="{{config.settings.style.cssButtonTextColor}}"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssLinkColor">Link color (HEX):</label>
                            <input type="text" class="form-control" name="style.cssLinkColor" value="{{config.settings.style.cssLinkColor}}"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssTextColor">Page text color (HEX):</label>
                            <input type="text" class="form-control" name="style.cssTextColor" value="{{config.settings.style.cssTextColor}}"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="cssFontFamily">Page font:</label>
                            <input type="text" class="form-control" name="style.cssFontFamily" value="{{config.settings.style.cssFontFamily}}"/>
                        </div>
                    </div>
                </div>
                <!-- Simple styling -->
            </div>
        </div>
    </div>
</form>
</div>


            {{!-- <ul class="nav nav-tabs " role="tablist" id="settingsTabs"> --}}
                {{!-- <li role="presentation" class="active"><a href="#website" aria-controls="website" role="tab" data-toggle="tab">{{__ "Website"}}</a></li> --}}
                {{!-- <li role="presentation"><a href="#article" aria-controls="article" role="tab" data-toggle="tab">{{__ "Article"}}</a></li> --}}
                {{!-- <li role="presentation"><a href="#display" aria-controls="display" role="tab" data-toggle="tab">{{__ "Display"}}</a></li> --}}
                {{!-- <li role="presentation"><a href="#style" aria-controls="style" role="tab" data-toggle="tab">{{__ "Style"}}</a></li> --}}

                {{!-- <a class="nav-item nav-link active" id="nav-website-tab" data-toggle="tab" href="#website" role="tab"
                    aria-controls="nav-website" aria-selected="true">{{__ "Website"}}</a>

                <a class="nav-item nav-link" id="nav-article-tab" data-toggle="tab" href="#article" role="tab"
                    aria-controls="nav-article" aria-selected="false">{{__ "Article"}}</a>

                <a class="nav-item nav-link" id="nav-display-tab" data-toggle="tab" href="#display" role="tab"
                    aria-controls="nav-display" aria-selected="false">{{__ "Display"}}</a>

                <a class="nav-item nav-link" id="nav-style-tab" data-toggle="tab" href="#style" role="tab"
                    aria-controls="nav-style" aria-selected="false">{{__ "Style"}}</a>
            </ul>
            <form method="post" action="{{app_context}}/update_settings">

                    <div class="tab-content mt-4 overflow-auto" id="navTabContent">
                        <!-- Website settings -->
                        <div class="tab-pane active" role="tabpanel" id="website">

                        </div>
                        <!-- Website settings -->

                        <!-- Article settings -->
                        <div role="tabpanel" class="tab-pane" id="article">

                        </div>
                        <!-- Article settings -->

                        <!-- Dislay settings -->
                        <div role="tabpanel" class="tab-pane" id="display">

                        </div>
                        <!-- Dislay settings -->
                        <!-- Simple styling -->
                        <div role="tabpanel" class="tab-pane" id="style">

                        </div>
                    </div>
            </form> --}}


        {{!-- </div>
    </div>
</div> --}}
{{!-- <script type="text/javascript">
    $(document).ready(function(){
        console.log('ready')
        $('#settingsForm').on('submit',function(){

            console.log('submitting', $(this).serializeArray())
        });
    });
</script> --}}