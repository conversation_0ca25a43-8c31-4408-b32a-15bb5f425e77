{{!-- <div class="col-md-12">
	<div class="row">
		<div class="col-md-8 col-md-offset-2">
			<ol class="breadcrumb">
	  			<li><a href="{{app_context}}/">{{__ "Home"}}</a></li>
	 			<li class="active">{{__ "Articles"}}</li>
			</ol>
            <div class="col-lg-12">

            </div> --}}

			{{!-- <nav aria-label="breadcrumb">
				<ol class="breadcrumb">
					<li class="breadcrumb-item"><a href="{{app_context}}/">{{__ "Home"}}</a></li>
					<li class="breadcrumb-item active" aria-current="page">{{__ "Articles"}}</li>
				</ol>
			</nav> --}}
		<div class="container">
			<h3 class="mt-3">{{__ "Articles"}}</h3>
			<div class="row search_bar pad-bottom">
				<div class="col-12">
					<div class="input-group">
						<input type="text" name="article_filter" id="article_filter" class="form-control" placeholder='{{__ "Filter articles"}}'>
						<span class="input-group-btn">
							<button class="btn btn-success" id="btn_articles_filter">{{__ "Filter"}}</button>
							<button class="btn btn-warning" id="btn_articles_reset">{{__ "Reset"}}</button>
							<a class="btn btn-info" href="{{app_context}}/articles/all">{{__ "All"}}</a>
						</span>
					</div>
				</div>
			</div>
			{{#if results}}
				<div class="row">
					<div class="col-12">
						<ul class="list-group">
							<li class="list-group-item">
								<span class="pull-right"><strong>{{__ "Published"}}</strong></span>
								<strong>{{__ "Articles"}} - <span class="text-danger">{{__ "Filtered term"}}: {{search_term}} </span></strong>
							</li>
							{{#each results}}
								<li class="list-group-item">
									<div class="row">
										<div class="col-lg-8"><a href="{{app_context}}/edit/{{this._id}}">{{this.kb_title}}</a></div>
										<div class="col-lg-4 text-right">
											<input id="{{this._id}}" class="published_state" type="checkbox" {{checked_state this.kb_published}}>
											<a href="{{app_context}}/delete/{{this._id}}" onclick="return confirm('{{__ "Are you sure you want to delete this article?"}}');"> <i class="fa fa-trash-o"></i></a>
										</div>
									</div>
								</li>
							{{/each}}
						</ul>
					</div>
				</div>
			{{else}}
				<div class="row articles-container">
					<div class="col-12">
						<ul class="list-group">
							<li class="list-group-item">
								<span class="pull-right"><strong>{{__ "Published"}}</strong></span>
								<strong>{{__ "Article"}} - {{__ "Recent"}}</strong>
							</li>
							{{#each articles}}
								<li class="list-group-item">
									<div class="row">
										<div class="col-lg-8"><a href="{{app_context}}/edit/{{this._id}}">{{this.kb_title}}</a></div>
										<div class="col-lg-4 text-right">
											<input id="{{this._id}}" class="published_state" type="checkbox" {{checked_state this.kb_published}}>
											<a href="{{app_context}}/delete/{{this._id}}" onclick="return confirm('{{__ "Are you sure you want to delete this article?"}}');"> <i class="fa fa-trash-o"></i></a>
										</div>
									</div>
								</li>
							{{/each}}
						</ul>
					</div>
				</div>
			{{/if}}
		</div>
		{{!-- </div>
	</div>
</div> --}}
