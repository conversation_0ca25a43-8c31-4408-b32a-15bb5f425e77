<div class="row">
    <div class="col-sm-12">
        <form action="{{app_context}}/search" id="search_form" method="post">
            {{#if config.settings.show_featured_articles}}
                <div class="col-md-10 col-lg-10 search_bar">
            {{else}}
            <div class="search_bar">
                {{/if}}
                <div class="input-group mb-3 pull-right" style="max-width: 500px;">
                    <input type="text" class="form-control" placeholder={{__ "Search the knowledge base"}}
                        name="frm_search" id="frm_search" autocomplete="off"
                        aria-label={{__ "Search the knowledge base"}}>
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" id="btn_search"
                        type="submit" style="right: 0;">{{__ "Search"}}</button>
                    </div>
                </div>

                {{!-- <div class="input-group">
                    <input type="text" name="frm_search" id="frm_search" autocomplete="off" class="form-control"
                        placeholder="{{__ "Search the knowledge base"}}">

                    <span class="input-group-btn">
                        <button class="btn btn-success btn-lg" id="btn_search" type="submit">{{__ "Search"}}</button>
                    </span>
                </div> --}}
                {{!-- {{#ifCond config.settings.typeahead_search '===' true}}
                <div id="searchResult" class="hidden col-lg-12 mt-4">
                    <ul class="list-group searchResultList"></ul>
                </div>
                {{/ifCond}} --}}


            </div>
        </form>
    </div>
</div>
<div class="row">
    {{#if config.settings.show_featured_articles}}
        <div class="col-md-2 col-md-offset-1 col-lg-2 col-lg-offset-1 mt-3">

                <ul class="list-group">
                    <li class="list-group-item list-group-heading">Featured</li>
                    {{!-- <li class="list-group-item list-group-heading">{{__ "Featured articles"}}</li> --}}
                    {{#each featured_results}}
                        {{#if this.kb_permalink}}
                            <li class="list-group-item"><h5><a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a></h5></li>
                        {{else}}
                            <li class="list-group-item"><h5><a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a></h5></li>
                        {{/if}}
                    {{/each}}
                </ul>

        </div>

        <div class="col-md-8 col-lg-8">
    {{else}}
        <div class="col-md-8 col-md-offset-2 col-lg-8 col-lg-offset-2">
    {{/if}}
        {{#if search_term}}
			<div class="mt-3">
                <ul class="list-group">
                    {{#ifCond routeType '===' 'topic'}}
                    <li class="list-group-item list-group-heading">{{__ "Topic"}}: {{search_term}}</li>
                    {{else}}
                    <li class="list-group-item list-group-heading">{{__ "Results for"}}: {{search_term}}</li>
                    {{/ifCond}}
                    {{#each search_results}}
                        {{#if this.kb_permalink}}
                            <li class="list-group-item">
                                <h5>
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a>
                                    {{#if @root.config.settings.show_published_date}}
                                        {{#if @root.config.settings.show_view_count}}
                                            <span class="pull-right view_count">&nbsp; / <strong>{{__ "Date"}}:</strong> {{format_date this.kb_published_date}}</span>
                                        {{else}}
                                            <span class="pull-right view_count"><strong>{{__ "Date"}}:</strong> {{format_date this.kb_published_date}}</span>
                                        {{/if}}
                                    {{/if}}
                                    {{#if @root.config.settings.show_view_count}}
                                        <span class="pull-right view_count"><strong>{{__ "View count"}}:</strong> {{view_count this.kb_viewcount}}</span>
                                    {{/if}}
                                </h5>
                            </li>
                        {{else}}
                            <li class="list-group-item">
                                <h5>
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a>
                                    {{#if @root.config.settings.show_published_date}}
                                        {{#if @root.config.settings.show_view_count}}
                                            <span class="pull-right view_count">&nbsp; / <strong>{{__ "Date"}}:</strong> {{format_date this.kb_published_date}}</span>
                                        {{else}}
                                            <span class="pull-right view_count"><strong>{{__ "Date"}}:</strong> {{format_date this.kb_published_date}}</span>
                                        {{/if}}
                                    {{/if}}
                                    {{#if @root.config.settings.show_view_count}}
                                        <span class="pull-right view_count"><strong>{{__ "View count"}}: </strong>{{view_count this.kb_viewcount}}</span>
                                    {{/if}}
                                </h5>
                            </li>
                        {{/if}}
                    {{else}}
                        <li class="list-group-item">
                            <h5>{{__ "No results found"}}</h5>
                        </li>
                    {{/each}}
                </ul>
			</div>
		{{else}}
			<div class="mt-3">
                <ul class="list-group">
                    <li class="list-group-item list-group-heading">{{__ "Top articles"}}</li>
                    {{#each top_results}}
                            <li class="list-group-item">
                                <h5>
                                {{#if kb_permalink}}
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a>
                                {{else}}
                                    <a href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a>
                                {{/if}}
                                {{#if @root.config.settings.show_published_date}}
                                    {{#if @root.config.settings.show_view_count}}
                                        <span class="pull-right view_count">&nbsp; / <strong>{{__ "Date"}}:</strong> {{format_date this.kb_published_date}}</span>
                                    {{else}}
                                        <span class="pull-right view_count"><strong>{{__ "Date"}}:</strong> {{format_date this.kb_published_date}}</span>
                                    {{/if}}
                                {{/if}}
                                {{#if @root.config.settings.show_view_count}}
                                    <span class="pull-right view_count"><strong>{{__ "View count"}}:</strong> {{view_count this.kb_viewcount}}</span>
                                {{/if}}
                                {{#if this.kb_password}}
                                    <span class="pull-right view_count" style="padding-right: 5px;"><i class="fa fa-key"></i></span>
                                {{/if}}
                                </h5>
                            </li>
                    {{/each}}
                </ul>
			</div>
		{{/if}}
	</div>
</div>
