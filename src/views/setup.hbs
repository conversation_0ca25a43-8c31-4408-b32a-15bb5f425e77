<div class="col-md-offset-4 col-md-4 col-lg-offset-4 col-lg-4" style="padding-top: 50px">
	<form class="form-signin" action="{{app_context}}/user_insert" method="post" role="form" data-toggle="validator">
		<h2 class="form-signin-heading text-center">openKB {{translate "Setup"}}</h2>
        <div class="form-group">
	    	<label for="frm_kb_title">{{translate "Users name"}} *</label>
	    	<input type="text" class="form-control" name="users_name" placeholder="{{translate "Users name"}}" required>
	  	</div>
		<div class="form-group">
	    	<label for="frm_kb_title">{{translate "User email"}} *</label>
	    	<input type="email" class="form-control" name="user_email" placeholder="{{translate "Email address"}}" required>
	  	</div>
	  	<div class="form-group">
		    <label for="frm_kb_body">{{translate "User password"}} *</label>
		    <input type="password" class="form-control" id="user_password" name="user_password" placeholder="{{translate "Password"}}" required>
	  	</div>
	  	<div class="form-group">
		    <label for="frm_kb_body">{{translate "Password confirm"}} *</label>
		    <input type="password" data-match="#user_password" placeholder="{{translate "Confirm password"}}" class="form-control" name="frm_user_password_confirm" required>
	  	</div>
		<button class="btn btn-lg btn-primary btn-block" type="submit">{{translate "Complete setup"}}</button>
	</form>
</div>
