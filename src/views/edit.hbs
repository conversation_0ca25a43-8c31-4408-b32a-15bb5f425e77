<div class="main-content">
    <nav id="wikiAdminNav">
        <div class="nav nav-tabs" id="nav-tab" role="tablist" style="background-color: white;">
            <button class="nav-link" id="navSettingsDashboard">
                <span class="d-none d-sm-block pt-1">{{translate "Dashboard"}}</span>
            </button>
            <button class="nav-link" id="navSettingsArticles">
                <span class="d-none d-sm-block pt-1">{{translate "Articles"}}</span>
            </button>
            <button class="nav-link disabled active" id="nav-contact-tab">
                <span class="d-none d-sm-block pt-1" id="spanArticleTitle"></span>
            </button>
            <div class="ml-auto custom-nav-right text-secondary" title="{{translate "Wiki Configuration"}}">
                <i class="fa fa-cog" id="navSettingsConfiguration"></i>
            </div>
        </div>
    </nav>
    <div class="mr-4 ml-4">

        {{!-- <nav aria-label="breadcrumb">
            <ol class="breadcrumb mt-2">
                <li class="breadcrumb-item"><a href="{{app_context}}/articles">Home</a></li>
                <li class="breadcrumb-item"><a href="{{app_context}}/articles">Articles</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{result.kb_title}}</li>
            </ol>
        </nav> --}}

        <div class="mt-4 col-sm-12" style="overflow: hidden;">
            <form method="post" id="edit_form" action="{{app_context}}/save_kb" data-toggle="validator">
                {{!-- <div class="row mb-2">
                    <div class="col-sm-12 text-right">
                        <button type="button" id="frm_edit_kb_save" class="btn btn-success editor_btn_action"><i class="fa fa-floppy-o" aria-hidden="true"></i> Save Changes</button>
                    </div>
                </div> --}}
                <input type="hidden" name="frm_kb_edit_reason" id="frm_kb_edit_reason"></input>
               
                <div class="main-form">
                    <div id="form-wrap" class="with-sidebar" >
                        <div class="row">
                
                            <div class="col-xs-12 col-lg-6">
                                <div class="form-group">
                                    <label for="frm_kb_title">{{translate "Article title"}} *</label><br />
                                    <input type="text" name="frm_kb_title" id="frm_kb_title" class="form-control input-normal"
                                        minlength="5" maxlength="200" value="{{result.kb_title}}" required />
                                </div>
                            </div>
                            {{!-- <div class="col-xs-12 col-sm-4 col-md-4 col-lg-2">
                                <label for="frm_kb_password">{{__ "Password"}}</label><br />
                                <input id="frm_kb_password" class="form-control" name="frm_kb_password" type="text"
                                    value="{{result.kb_password}}" />
                            </div> --}}
                            <div class="col-xs-12 col-sm-4 col-md-4 col-lg-2">
                                <label for="frm_kb_published">{{translate "Status"}}</label><br />
                                <select class="form-control" id="frm_kb_published" name="frm_kb_published">
                                    {{#ifCond result.kb_published "==" "true"}}
                                    <option value="true" selected>{{translate "Published"}}</option>
                                    <option value="false">{{translate "Draft"}}</option>
                                    {{else}}
                                    <option value="true">{{translate "Published"}}</option>
                                    <option value="false" selected>{{translate "Draft"}}</option>
                                    {{/ifCond}}
                                </select>
                            </div>
                            <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4 text-right">
                                <label for="">&nbsp;</label><br />
                                <div class="btn-group btn-group-justified" role="group" aria-label="..." style="width: 100%;">
                                    {{!-- Now always show the Edit Reason / Article history button --}}
                                    {{!-- {{#ifCond config.settings.article_versioning '===' true}} --}}
                                    {{!-- <a href="#" class="btn btn-warning toggle-menu menu-left push-body"><i class="fa fa-code-fork"
                                            aria-hidden="true"></i></a> --}}
                                    <button type="button" class="btn btn-warning" id="btnHistory" title="{{translate " Article
                                        History"}}">
                                        <i class="fa fa-code-fork" aria-hidden="true"></i> {{translate "Article History"}}</a>
                                        {{!-- {{/ifCond}} --}}
                                        {{!-- <button type="button" class="btn btn-secondary" id="btnSettings" data-whatever="{{result}}"
                                            title="{{translate " Article Settings"}}"><i class="fa fa-cog" aria-hidden="true"></i>
                                            {{translate "Settings"}}</button> --}}
                                        <button type="button" id="frm_edit_kb_save" class="btn btn-success editor_btn_action"><i
                                                class="fa fa-floppy-o" aria-hidden="true"></i> {{translate "Save"}}</button>
                                        {{!-- <button type="button" class="btn btn-secondary" data-toggle="modal"
                                            data-target="#settingsModal" data-whatever="{{result}}"><i class="fa fa-cog"
                                                aria-hidden="true"></i> Settings</button> --}}
                
                                        {{!-- <a href="#" class="btn btn-default toggle-menu menu-right push-body"><i class="fa fa-cog"
                                                aria-hidden="true"></i></a> --}}
                                        {{!-- <a href="#" id="frm_edit_kb_save" class="btn btn-success editor_btn_action"><i
                                                class="fa fa-floppy-o" aria-hidden="true"></i></a> --}}
                                </div>
                            </div>
                
                
                            {{!-- ADDITIONAL SETTINGS MODAL --}}
                            {{!-- <div class="modal fade" id="settingsModal" tabindex="-1" role="dialog"
                                aria-labelledby="settingsModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document"> I copied the settingsModal name to the div below so I won't
                                    change much of the code --}}
                
                                
                                    {{!--
                                </div>
                            </div> --}}
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-md-12 col-lg-6">
                                <div class="form-group" id="editor-wrapper">
                                    <label for="editor">{{translate "Article body (Markdown)"}} *</label>
                                    <textarea id="editor" minlength="5" name="frm_kb_body" data-provide="markdown"
                                        data-hidden-buttons="cmdPreview" data-iconlibrary="fa" class="form-control"
                                        required>{{{result.kb_body}}}</textarea>
                                </div>
                            </div>
                            <div class="col-xs-12 col-md-6 col-lg-6">
                                <div class="form-group" id="preview-wrapper">
                                    <label for="preview">{{translate "Preview"}}</label>
                                    <div id="preview" name="preview" class="form-control" rows="5"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-6">
                                <div class="form-group">
                                    <input type="text" name="frm_kb_keywords" id="frm_kb_keywords" placeholder="{{translate "
                                        keyword"}}" value="{{result.kb_keywords}}">
                                </div>
                            </div>
                            <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                                <input type="text" class="form-control" name="frm_kb_permalink" id="frm_kb_permalink"
                                    data-perma="{{result}}" placeholder="{{translate " permalink"}}" value="{{result.kb_permalink}}">
                            </div>
                            <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
                                <div class="btn-group btn-group-justified pull-right " role="group" aria-label="...">
                                    <a href="#" class="btn btn-success" id="validate_permalink" type="button">{{translate
                                        "Validate"}}</a>
                                    <a href="#" class="btn btn-warning" id="generate_permalink" type="button">{{translate
                                        "Generate"}}</a>
                                    <a href="#" class="btn btn-info disabled" id="generate_permalink_from_title"
                                        type="button">{{translate
                                        "Slug"}}</a>
                                    {{!-- <a href="#" class="btn btn-info disabled" id="generate_permalink_from_title"
                                        type="button">{{__
                                        "Slug from title"}}</a> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="settings-wrap expanded">
                        <div class="menu">
                            <h5>Settings Menu</h5>
                        </div>
                        <div class="settings-content" id="settingsContent">
                            <div class="mr-2 ml-2">
                        
                                <div class="row mb-2">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <div class="btn-group btn-group-justified" role="group" aria-label="...">
                                                <a class="btn btn-sm btn-outline-primary" href="#"
                                                    data-routename="{{app_context}}/{{@root.config.settings.route_name}}"
                                                    data-articleid="{{result._id}}" id="btnCopyArticleLink">{{translate
                                                    "Copy Page URL"}}</a>
                                                <a class="btn btn-sm btn-outline-info" target="_blank"
                                                    href="{{app_context}}/{{@root.config.settings.route_name}}/{{result._id}}">{{translate
                                                    "Preview"}}</a>
                                                <a class="btn btn-sm btn-outline-secondary" target="_blank"
                                                    href="{{app_context}}/{{@root.config.settings.route_name}}/resetviewCount/{{result._id}}">{{translate
                                                    "Reset Views"}}</a>
                                                <a class="btn btn-sm btn-outline-secondary" target="_blank"
                                                    href="{{app_context}}/{{@root.config.settings.route_name}}/resetvoteCount/{{result._id}}">{{translate
                                                    "Reset Votes"}}</a>
                                                <a class="btn btn-sm btn-outline-danger" href="{{app_context}}/delete/{{result._id}}"
                                                    id="del_post" onclick="return confirm('{{translate " Are you sure you want to delete this
                                                    article?"}}');">{{translate "Delete"}}</a>
                                            </div>
                                        </div>
                                    </div>
                                   
                                </div>
                                <div class="form-group mb-4">
                                    <label for="frm_kb_password">{{translate "Password"}}</label><br />
                                    <input id="frm_kb_password" class="form-control form-control-sm" name="frm_kb_password" type="text"
                                        value="{{result.kb_password}}" />
                                </div>
                        
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="frm_kb_featured" {{checked_state result.kb_featured}}> {{translate
                                            "Featured article"}}
                                        </label>
                                        <br>
                                        <small>{{translate "Featured articles will appear in Sub-topic search results &
                                            Important Guides"}}</small>
                                    </div>
                                </div>
                        
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label>
                                            <input id="kb_pinned" type="checkbox" name="frm_kb_pinned" {{checked_state result.kb_pinned}}>
                                            {{translate "Associate with Topic"}}
                                        </label>
                                        <a href="{{app_context}}/settings/configuration/topicsettings" class="pull-right">{{translate "Topics
                                            Settings"}}</a>
                                    </div>
                                    <div class="pinDiv">
                                        <select class="form-control mb-3" data-topic="{{result.kb_pinned_topic}}" id="selectTopics"
                                            name="frm_kb_pinned_topic" aria-expanded="true">
                                            <option value="">Select Topic</option>
                                        </select>
                                        <label>{{translate "Sub-topic"}}</label>
                                        <select class="form-control mb-3" data-subtopic="{{result.kb_pinned_subtopic}}" id="selectSubtopics"
                                            name="frm_kb_pinned_subtopic" aria-expanded="true">
                                             <option value="">Select Subtopic</option>
                                        </select>
                                    </div>
                                    <div id="kb_pinned_topic" class="collapse {{#if (checked_state result.kb_pinned)}}in{{/if}}">
                        
                                    </div>
                                </div>
                        
                                <div class="form-group">
                                    <label for="frm_kb_visible_state">{{translate "Visible state"}}</label>
                                    <select class="form-control" id="frm_kb_visible_state" name="frm_kb_visible_state">
                                        <option {{select_state result.kb_visible_state 'public' }} value="public">
                                            {{translate "Public"}}</option>
                                        <option {{select_state result.kb_visible_state 'private' }} value="private">
                                            {{translate "Private"}}</option>
                                    </select>
                                </div>
                        
                                <div class="form-group">
                                    <label for="frm_kb_security_level">{{translate "Security Level"}}</label>
                                    <select class="form-control" id="frm_kb_security_level" name="frm_kb_security_level">
                                        <option {{select_state result.kb_security_level 100}} value="100">{{translate
                                            "100 Standard User"}}</option>
                                        <option {{select_state result.kb_security_level 200}} value="200">{{translate
                                            "200 Club Manager"}}</option>
                                        <option {{select_state result.kb_security_level 300}} value="300">{{translate
                                            "300 Club Owner"}}</option>
                                        <option {{select_state result.kb_security_level 400}} value="400">{{translate
                                            "400 Admin Only"}}</option>
                                    </select>
                                </div>
                        
                                {{!-- <div class="form-group">
                                    <label>Regional Visibility</label>
                                    <select class="form-control" id="frm_kb_regional_visibility" name="frm_kb_regional_visibility"
                                        data-options="{{result.countries}}"></select>
                                    </select>
                                </div> --}}
                        
                                <div class="form-group" style="margin-bottom: 50px;">
                                    {{!-- onclick="showCheckboxes()" --}}
                                    <div class="selectBox">
                                        <label>{{translate "Regional Visibility"}}</label>
                        
                                        <input type="hidden" id="frm_kb_regional_visibility" name="frm_kb_regional_visibility"
                                            value="{{result.kb_regional_visibility}}">
                                        <select class="form-control" id="selectCountryDropdown" data-options="{{result.countries}}"  data-possible-options="{{result.possibleCountries}}">
                                            <option value="" id="optionSelectCountry"></option>
                                        </select>
                                        <div class="overSelect" id="overSelect"></div>
                                    </div>
                        
                                    <div id="countryCheckboxes">
                                    </div>
                                    <small class="font-italic">{{translate "Articles without any regions configured will
                                        be globally visible."}}</small>
                                </div>
                        
                                <div class="form-group d-none">
                                    <label for="frm_kb_seo_title">{{translate "SEO title"}}</label>
                                    <textarea class="form-control" name="frm_kb_seo_title"
                                        rows="3">{{if_null result.kb_seo_title result.kb_title}}</textarea>
                                </div>
                                <div class="form-group d-none">
                                    <label for="frm_kb_seo_title">{{translate "SEO description"}}</label>
                                    <textarea class="form-control" name="frm_kb_seo_description"
                                        rows="3">{{if_null result.kb_seo_description (substring (strip_md result.kb_body) 160)}}</textarea>
                                </div>
                            </div>
                        
                        </div>
                        <div class="settings-toggle" style="">
                            <hr class="hide-tablet">
                            <div class="toggle-nav-small hide-tablet"><button id="toggleNavSmallBtn" type="button"
                                    class="btn btn-default btn-circle waves-effect waves-circle waves-float" data-toggle="tooltip"
                                    data-placement="right" title="Article Settings"><i
                                        class="material-icons">chevron_left</i></button>
                            </div>
                        </div>
                    </div>
                </div>
               
                <input type="hidden" name="frm_kb_id" id="frm_kb_id" value="{{result._id}}" />
                <input type="hidden" id="frm_editor" value="true" />

                <nav class="cbp-spmenu cbp-spmenu-vertical cbp-spmenu-right">
                    <div class="cbp-spmenu-header"><div class="pull-left">{{translate "Settings"}}</div><button id="btnSettingsMenu" class="btn btn-default pull-right toggle-menu menu-right push-body"><i class="fa fa-cog"></i></div></button>
                    <ul class="list-group list-group-sidemenu">

                    </ul>
                </nav>

                {{!-- Now always enable article versioning --}}
                {{!-- {{#ifCond config.settings.article_versioning '===' true}} --}}

                <div class="modal-content"  id="articleHistory" style="display: none;">

                    <div class="modal-header">
                        <h5 class="modal-title" id="settingsModalLabel">{{translate "Article History"}}</h5>
                        <button type="button" id="btnCloseHistory" class="close" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <ul class="list-group list-group-sidemenu">
                            {{!-- <li class="list-group-item">
                                <label for="frm_kb_edit_reason">{{__ "Edit reason"}}</label>
                                <textarea class="form-control" name="frm_kb_edit_reason" id="frm_kb_edit_reason" rows="3"></textarea>
                            </li> --}}
                            <li class="list-group-item article-history-item">
                                {{#if versions}}
                                    <p>{{translate "Previous versions"}}</p>
                                        {{#each versions}}
                                            <div class="versionWrapper">
                                                <small>{{translate "Date"}}: {{format_date this.kb_last_updated}}</small>
                                                <textarea class="form-control versionArea" readonly rows="2">{{this.kb_edit_reason}}</textarea>
                                                <div id="{{this._id}}" class="btn-group btn-group-justified">
                                                    <button type="button" class="btnDeleteVersion btn btn-sm btn-outline-danger">{{translate "Delete"}}</a>
                                                    <button type="button"  class="btnEditVersion btn btn-sm btn-outline-warning">{{translate "Edit"}}</a>
                                                    <button type="button" href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}/version" class=" btn btn-sm btn-outline-info">{{translate "Preview"}}</a>
                                                </div>
                                                <hr>
                                            </div>
                                        {{/each}}
                                    </select>
                                {{/if}}
                            </li>
                        </ul>

                    </div>

                </div>
                {{!-- {{/ifCond}} --}}

            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct" crossorigin="anonymous"></script>