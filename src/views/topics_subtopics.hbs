
<div class="mr-4 ml-4">

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mt-2">
            <li class="breadcrumb-item"><a href="{{app_context}}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{app_context}}/settings">Settings</a></li>
            <li class="breadcrumb-item active" aria-current="page">Topics Management</li>
        </ol>
    </nav>


        <h4 class=" mt-3">Topics and Sub-topics Management
            {{!-- <button class="btn btn-outline-primary btn-sm" id="btnSetRegionUserLevel"> Set Articles Default Region and User Level Once</button> --}}
            <button class="btn btn-outline-primary btn-sm pull-right mr-1" type="button" id="btnNewTopic">
                <i class="fa fa-plus"></i> New Topic
            </button>
        </h4>

        <div class="row mt-3 topic-settings-container">
            <div class="col-xs-12 col-sm-5">
                <label for="">Topics list</label>
                 <div id="topics-contents">
                    <div class="list-group topic-list" id="topicsListGroup">

                    </div>
                 </div>
            </div>
            <div class="col-xs-12 col-sm-7"  id="topics-card">
                <label for="">Topic details</label>

                <div id="topics-contents">
                    <div class="card card-body">

                        <form method="post" action="" id="topicDetailsForm">
                            <input type="hidden" value="" name="_id" id="hiddenTopicId">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-inline">
                                        <small for="display_order">Order&nbsp;&nbsp;</small>
                                        <input class="text-center mr-auto form-control form-control-sm" type="text" id="displayOrder" name="display_order" value="" style="max-width: 50px;">
                                        <div class="custom-control custom-switch pull-right">
                                            <input type="checkbox" class="custom-control-input" id="enableSwitch" value="">
                                            <label class="custom-control-label" id="enableSwitchLabel" for="enableSwitch">Disabled</label>
                                        </div>
                                    </div>
                                    <br>
                                    <small>Topic </small>
                                    <input class="form-control" type="text" name="topic" id="topic" value="">
                                    <br>
                                    <small>Keyword </small>
                                    <input class="form-control" type="text" name="keyword" id="keyword" value="">
                                    <br>
                                    <small>SVG Icon</small>
                                    <textarea class="form-control" type="text" name="svg" id="svg"></textarea>
                                    <br>

                                </div>

                            </div>
                            <div class="row mt-2">
                                <div class="col-sm-12">

                                        <label class="mt-2">Sub-topics</label>

                                        <button class="btn btn-outline-primary btn-sm pull-right" id="btnNewSubtopic" type="button">&nbsp;<i class="fa fa-plus"></i>&nbsp;
                                        </button>

                                        <div class="card card-body" id="cardSubtopics">

                                        </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <div class="selectBox">
                                            <input type="hidden" id="topic_regional_visibility" name="topic_regional_visibility"
                                                value="">
                                            <small>Regional Visibility</small>
                                            <br>
                                            <small class="font-italic">Topics without any regions configured will be globally visible</small>
                                            <select class="form-control" id="selectTopicCountry"
                                                data-options="{{countries}}">
                                                <option value="" id="optionSelectTopicCountry"></option>
                                            </select>

                                            <div class="overSelect" id="overSelect"></div>
                                        </div>
                                        <div id="countryCheckboxes">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-sm-12 mt-4">
                                    <button  type="button" class="btn btn-outline-danger btn-sm" id="btnDeleteTopic">
                                        <i class="fa fa-trash"></i> Delete Permanently</button>

                                    <div class="pull-right">
                                        <button type="button" class="btn btn-outline-success btn-sm" id="btnSaveTopicChanges">
                                            <i class="fa fa-save"></i> Save</button>
                                    </div>
                                </div>
                            </div>
                        </form>

                    </div>

                </div>
            </div>
        </div>
</div>

<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/topic-settings.js"></script>
