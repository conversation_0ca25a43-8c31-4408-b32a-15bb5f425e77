<div class="main-content">

    <div id="search-result-wrap">


        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mt-2">
                <li class="breadcrumb-item"><a href="{{app_context}}">{{translate "Home"}}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{translate "Search"}} {{searchfor}}</li>
            </ol>
        </nav>

        <div class="mt-2">
            <div class="search-results panel-default">

                <span class="results-text">
                    {{search_results.length}}
                </span>
                <span class="results-text-normal">
                    {{translate "results for the term(s):"}}"
                </span>
                <span class="results-text">
                    &quot;{{search_term}}&quot;
                </span>

                <div class="search-results-container mt-2" id="resultsDiv" data-routetype="{{routeType}}">

                    {{#ifCond routeType '===' 'topic'}}
                    {{#if featured_results.length}}
                    <div class="card card-body mt-3 mb-4 featured-card">
                        <div class="row featured-row">
                            {{!-- {{#each featured_results}}
                            {{{split_keywords2 this}}}
                            {{/each}} --}}
                            {{#each featured_results}}
                            {{{produce_article_links this}}}
                            {{/each}}
                        </div>
                    </div>
                    {{/if}}
                    {{/ifCond}}


                    {{#if search_results.length}}
                    {{#each search_results}}

                    <div class="card card-body mt-4 search-result-card" data-articleid="{{this._id}}">
                        <div class="row search-card-title">
                            <div class="col-sm-12">
                                <a class="search-result-title"
                                    href="{{app_context}}/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a>
                            </div>
                        </div>
                        <div class="row search-card-sub-title mb-4">
                            <div class="col-sm-12">
                                <i class="icon">
                                    <svg width="12" height="14" viewBox="0 0 12 14" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M9.383 6.125C10.4845 6.4455 12 7.531 12 11.086C12 12.6955 10.8045 14 9.336 14H2.664C1.195 14 0 12.6955 0 11.086C0 7.5315 1.5155 6.4455 2.617 6.125C2.2265 5.508 2 4.781 2 4C2 1.797 3.797 0 6 0C8.203 0 10 1.797 10 4C10 4.7815 9.7735 5.508 9.383 6.125ZM6 1C4.3435 1 3 2.344 3 4C3 5.656 4.344 7 6 7C7.656 7 9 5.656 9 4C9 2.344 7.656 1 6 1ZM9.336 13C10.25 13 11 12.1485 11 11.086C11 8.625 10.172 7.086 8.625 7.008C7.922 7.625 7.008 8 6 8C4.992 8 4.078 7.625 3.375 7.008C1.828 7.086 1 8.625 1 11.086C1 12.1485 1.75 13 2.664 13H9.336V13Z"
                                            fill="#939393" />
                                    </svg>
                                </i>
                                &nbsp;{{this.kb_author}}&nbsp;&nbsp;&nbsp;
                                <i class="icon">
                                    <svg width="13" height="14" viewBox="0 0 13 14" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M1 13H12V5H1V13ZM4 3.5V1.25C4 1.1095 3.8905 1 3.75 1H3.25C3.1095 1 3 1.1095 3 1.25V3.5C3 3.6405 3.1095 3.75 3.25 3.75H3.75C3.8905 3.75 4 3.6405 4 3.5ZM10 3.5V1.25C10 1.1095 9.8905 1 9.75 1H9.25C9.1095 1 9 1.1095 9 1.25V3.5C9 3.6405 9.1095 3.75 9.25 3.75H9.75C9.8905 3.75 10 3.6405 10 3.5ZM13 3V13C13 13.547 12.547 14 12 14H1C0.453 14 0 13.547 0 13V3C0 2.453 0.453 2 1 2H2V1.25C2 0.5625 2.5625 0 3.25 0H3.75C4.4375 0 5 0.5625 5 1.25V2H8V1.25C8 0.5625 8.5625 0 9.25 0H9.75C10.4375 0 11 0.5625 11 1.25V2H12C12.547 2 13 2.453 13 3Z"
                                            fill="#939393" />
                                    </svg>
                                </i>
                                &nbsp;{{format_date this.kb_published_date}}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <p class="search-result-body">{{{ trimStringStripHtml this.kb_body }}} &hellip; </p>
                            </div>
                        </div>

                        {{#if this.kb_keywords}}
                        <div class="row ml-1">
                            <i class="icon">
                                <svg width="19" height="16" viewBox="0 0 19 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M4.25 3.74995C4.25 3.07663 3.70904 2.53566 3.03571 2.53566C2.36239 2.53566 1.82143 3.07663 1.82143 3.74995C1.82143 4.42327 2.36239 4.96423 3.03571 4.96423C3.70904 4.96423 4.25 4.42327 4.25 3.74995ZM14.3723 9.21423C14.3723 9.53663 14.2393 9.84991 14.0214 10.0679L9.36336 14.7356C9.13568 14.9536 8.82239 15.0865 8.5 15.0865C8.17761 15.0865 7.86432 14.9536 7.64636 14.7356L0.863357 7.94288C0.379464 7.4687 0 6.54827 0 5.87495V1.92852C0 1.2643 0.550071 0.714233 1.21429 0.714233H5.16071C5.83404 0.714233 6.75446 1.0937 7.23836 1.57759L14.0214 8.35088C14.2393 8.57855 14.3723 8.89184 14.3723 9.21423V9.21423ZM18.0151 9.21423C18.0151 9.53663 17.8822 9.84991 17.6642 10.0679L13.0062 14.7356C12.7785 14.9536 12.4652 15.0865 12.1429 15.0865C11.6499 15.0865 11.4027 14.8588 11.0804 14.5267L15.5392 10.0679C15.7572 9.84991 15.8901 9.53663 15.8901 9.21423C15.8901 8.89184 15.7572 8.57855 15.5392 8.35088L8.75621 1.57759C8.27232 1.0937 7.3525 0.714233 6.67857 0.714233H8.80357C9.47689 0.714233 10.3973 1.0937 10.8812 1.57759L17.6642 8.35088C17.8822 8.57855 18.0151 8.89184 18.0151 9.21423V9.21423Z"
                                        fill="#308CEA" />
                                </svg>
                            </i>
                            &nbsp;{{{split_keywords2 this.kb_keywords}}}
                        </div>
                        {{/if}}
                    </div>

                    {{/each}}
                    {{/if}}
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/search-results.js"></script>