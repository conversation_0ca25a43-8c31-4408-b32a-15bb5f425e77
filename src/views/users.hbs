
{{!-- <ol class="breadcrumb">
	<li><a href="{{app_context}}/">{{__ "Home"}}</a></li>
	<li class="active">{{__ "Users"}}</li>
</ol> --}}
{{!-- <nav aria-label="breadcrumb">
	<ol class="breadcrumb">
		<li class="breadcrumb-item"><a href="{{app_context}}/">{{__ "Home"}}</a></li>
		<li class="breadcrumb-item active" aria-current="page">{{__ "Users"}}</li>
	</ol>
</nav> --}}
<div class="container">
	<h3 class="mt-3">{{translate "Users"}} <span class="pull-right">
		{{!-- <a href="{{app_context}}/users/new" class="btn btn-primary">{{__ "New user"}}</a></span> --}}
		</h3>
	<ul class="list-group">
		{{#each users}}
			<li class="list-group-item">
				<strong>{{translate "User"}}:</strong> {{this.users_name}} - ({{this.user_email}})
				<span class="pull-right">
					<small class="font-weight-bold">{{translate "Role"}}: </small>
					{{#is_an_admin this.is_admin}}
						<span class="mr-2">{{translate "Admin"}}</span>
					{{else}}
						<span class="mr-2">{{translate "User"}}</span>
					{{/is_an_admin}}

	{{!-- <small class="font-weight-bold ml-1">Level:</small><span>{{../session.user_level}}</span> --}}


					{{#is_an_admin ../session.is_admin}}
						<a href="{{app_context}}/user/edit/{{this._id}}"><i class="fa fa-pencil" aria-hidden="true"></i></a>
						<a href="{{app_context}}/user/delete/{{this._id}}" onclick="return confirm('{{translate "Are you sure you want to delete?"}}')"><i class="fa fa-trash-o" aria-hidden="true"></i></a>
					{{/is_an_admin}}
				</span>
			</li>
		{{/each}}
	</ul>
</div>