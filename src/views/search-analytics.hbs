<nav id="wikiAdminNav">
        <div class="nav nav-tabs" id="nav-tab" role="tablist" style="background-color: white;">
            <button class="nav-link" id="navSettingsDashboard">
                <span class="d-none d-sm-block pt-1">{{translate "Dashboard"}}</span>
            </button>
            <button class="nav-link" id="navSettingsArticles">
                <span class="d-none d-sm-block pt-1">{{translate "Articles"}}</span>
            </button>
            <button class="nav-link disabled active" id="nav-contact-tab">
                <span class="d-none d-sm-block pt-1" id="spanArticleTitle">{{translate title}}</span>
            </button>
            <div class="ml-auto custom-nav-right text-secondary" title="{{translate "Wiki Configuration"}}">
                <i class="fa fa-cog" id="navSettingsConfiguration"></i>
            </div>
        </div>
    </nav>
<div id="analyticsContent" class="tab">
    <input type="hidden" value="{{type}}" id="analytics-type" />
    <div class="main-content">
        <div class="search-wrap">
            <div class="search-content">
                <div class="buttons">
                    <a href="#" id="top-searches-csv" class="btn waves-effect" type="button">
                        <span>{{translate "Export CSV"}}</span>
                        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" viewBox="0 0 500 500"
                            xml:space="preserve" fill="currentColor">
                            <style></style>
                            <defs>
                                <path id="SVGID_1_" d="M0 0h500v500H0z"></path>
                            </defs>
                            <clipPath id="SVGID_00000151524302526006783760000009387503102392374920_">
                                <use xlink:href="#SVGID_1_" overflow="visible"></use>
                            </clipPath>
                            <g clip-path="url(#SVGID_00000151524302526006783760000009387503102392374920_)">
                                <defs>
                                    <path id="SVGID_00000133489503278807473390000009400609450605903026_"
                                        d="M0 0h500v500H0z"></path>
                                </defs>
                                <clipPath id="SVGID_00000168829252607399499490000003918191479402967182_">
                                    <use xlink:href="#SVGID_00000133489503278807473390000009400609450605903026_"
                                        overflow="visible"></use>
                                </clipPath>
                                <g clip-path="url(#SVGID_00000168829252607399499490000003918191479402967182_)">
                                    <path
                                        d="M417.3 82.8h-63.1c-11.5 0-20.8 9.4-20.8 20.8s9.4 20.8 20.8 20.8l62.5-.6.6 250.6-334 .6-.6-250.6h63.1c11.5 0 20.8-9.4 20.8-20.8s-9.4-20.8-20.8-20.8h-63c-22.7 0-41 18.3-41 41v251.3c0 22.7 18.3 41 41 41h334.6c22.5 0 41-18.3 41-40.8V123.9c0-22.7-18.6-41.1-41.1-41.1z">
                                    </path>
                                </g>
                                <path
                                    d="M173.2 247.2l62.5 59.2c1.6 1.6 3.6 2.6 5.6 *******.******* 2.6 1 5.3 1.6 8 1.6s5.3-.5 7.8-1.6c2.9-1.2 5.3-3 7.4-5.2l61.5-58.2c8.3-7.9 8.8-21 .8-29.4-7.9-8.3-21-8.8-29.4-.8l-27.3 25.9v-139c0-11.5-9.4-20.8-20.8-20.8s-20.8 9.4-20.8 20.8v139.2L201.7 217c-8.3-7.9-21.5-7.5-29.4.8-7.9 8.4-7.5 21.5.9 29.4z"
                                    clip-path="url(#SVGID_00000168829252607399499490000003918191479402967182_)"></path>
                            </g>
                        </svg>
                    </a>
                </div>
                <div class="search-title-wrap">
                    <input type="text" name="result_filter" id="result_filter" class="form-control form-control-sm mr-1"
                        placeholder="{{translate "Filter results"}}" />
                </div>
            </div>
            <div class="filters-wrap">
                <div class="filters">
                    <div class="filter-grp" id="filter-regions">
                        <p class="filter-label">{{translate "Start Date"}}</p>
                        <span class="filter-grp-divider"></span>
                        <div class="filter">
                            <button type="button" class="filter-btn filter-selected">{{start}}</button>
                            

                        </div>
                    </div>
                    <div class="filter-grp" id="filter-status">
                        <p class="filter-label">{{translate "End Date"}}</p>
                        <span class="filter-grp-divider"></span>
                        <div class="filter">
                            <button type="button" class="filter-btn filter-selected">{{end}}</button>
                            {{!-- <ul class="filter-options">
                                <li class="filter-option filter__option--active" data-filter="">{{translate "All"}}</li>
                                <li class="filter-option" data-filter="false">{{translate "Draft"}}</option>
                                <li class="filter-option" data-filter="true">{{translate "Published"}}</option>
                            </ul> --}}
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <div class="row">
            <div class="col-12">
                {{#if topSearches}}
                <table id="top-searches-table" class="table data-table mt-4">
                    <thead>
                        <tr>
                            <th>
                                <div class="articles-table__header">

                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Query"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Count"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "% Total Searches"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Avg. Click Pos."}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "CTR"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "CVR"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header no-sort">
                                    <span>{{translate "Opportunities"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each topSearches}}
                        <tr id="search-item-{{this._id}}" class="search-item" data-id="{{this._id}}">
                            <td class="search-cell" data-export="{{@key}}">
                                <div class="search-cell__title">{{@key}}</div>

                            </td>
                            <td class="search-cell search searchable" data-search="{{ternary this.search this.search "
                                (blank / no search terms provided)"}}" data-export="{{ternary this.search this.search "
                                (blank / no search terms provided)"}}">
                                <div class="search-cell__title">{{ternary this.search this.search "(blank / no search
                                    terms provided)"}}</div>
                            </td>
                            <td class="search-cell count" data-search="{{this.count}}" data-export="{{this.count}}">
                                <div class="search-cell__center">{{this.count}}</div>
                            </td>
                            <td class="search-cell percentage" data-search="{{this.percentage}}"
                                data-export="{{this.percentage}}">
                                <div class="search-cell__center">{{this.percentage}}%</div>
                            </td>
                            <td class="search-cell avgClickPosition" data-search="{{this.averageClickPosition}}"
                                data-export="{{this.averageClickPosition}}">
                                <div class="search-cell__center">{{this.averageClickPosition}}</div>
                            </td>
                            <td class="search-cell ctr" data-search="{{this.clickThroughRate}}"
                                data-export="{{this.clickThroughRate}}">
                                <div class="search-cell__center">{{this.clickThroughRate}}</div>
                            </td>
                            <td class="search-cell cvr" data-search="{{this.conversionRate}}"
                                data-export="{{this.conversionRate}}">
                                <div class="search-cell__center">{{this.conversionRate}}</div>
                            </td>
                            <td class="search-cell opportunities">
                                <div class="search-cell__center">-</div>
                            </td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
                {{/if}}
                {{#if topResults}}
                <table id="top-results-table" class="table data-table">
                    <thead>
                        <tr>
                            <th>
                                <div class="articles-table__header">

                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Query"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Count"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>

                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "CTR"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "CVR"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>

                        </tr>
                    </thead>
                    <tbody>
                        {{#each topResults}}
                        <tr id="search-item-{{this._id}}" class="search-item" data-id="{{this._id}}">
                            <td class="search-cell" data-export="{{@key}}">
                                <div class="search-cell__title">{{@key}}</div>

                            </td>
                            <td class="search-cell search searchable" data-search="{{this.title}}" data-export="{{this.title}}">
                                <div class="search-cell__title"><a href="{{this.url}}" target="_self">{{this.title}}</a></div>
                            </td>
                            <td class="search-cell count" data-search="{{this.count}}" data-export="{{this.count}}">
                                <div class="search-cell__center">{{this.count}}</div>
                            </td>
                            </td>
                            <td class="search-cell ctr" data-export="{{this.clickThroughRate}}"
                                data-export="{{this.clickThroughRate}}">
                                <div class="search-cell__center">{{this.clickThroughRate}}</div>
                            </td>
                            <td class="search-cell cvr" data-export="{{this.conversionRate}}"
                                data-export="{{this.conversionRate}}">
                                <div class="search-cell__center">{{this.conversionRate}}</div>
                            </td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
                {{/if}}
                {{#if topNoResults}}
                <table id="top-no-results-table" class="table data-table">
                    <thead>
                        <tr>
                            <th>
                                <div class="articles-table__header">
                                    <div>&nbsp;</div>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Query"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Count"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "% Total Searches"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                           

                            <th>
                                <div class="articles-table__header">
                                    <span>{{translate "Opportunities"}}</span>
                                    <span class="articles-table__header__icon"></span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each topNoResults}}
                        <tr id="search-item-{{this._id}}" class="search-item" data-id="{{this._id}}">
                            <td class="search-cell search-cell--center">
                                <div class="search-cell__title">{{@key}}</div>
                            </td>
                            <td class="search-cell search searchable" data-search="{{this.search}}" data-export="{{ternary this.search this.search "(blank / no search
                                    terms provided)"}}</span>&nbsp;{{#if this.withFilterCount}}of which {{this.withFilterCount}} is filtered{{/if}}">
                                <div class="search-cell__title"><span class="highlight">{{ternary this.search this.search "(blank / no search
                                    terms provided)"}}</span>&nbsp;{{#if this.withFilterCount}}of which {{this.withFilterCount}} is filtered{{/if}}</div>
                            </td>
                            <td class="search-cell count search-cell--center" data-search="{{this.count}}" data-export="{{this.count}}">
                                <div class="search-cell__center">{{this.count}}</div>
                            </td>
                            <td class="search-cell percentage search-cell--center" data-search="{{this.percentage}}"
                                data-export="{{this.percentage}}">
                                <div class="search-cell__center">{{this.percentage}}%</div>
                            </td>
                            </td>
                           
                            <td class="search-cell opportunities">
                                <div class="search-cell__center">-</div>
                            </td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
                {{/if}}
            </div>
        </div>
    </div>
</div>


<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/js/bootstrap-select.min.js"></script>

<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/search-analytics-pages.js"></script>