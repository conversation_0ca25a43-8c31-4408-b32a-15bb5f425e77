
<div class="ml-4 mr-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mt-2">
            <li class="breadcrumb-item"><a href="{{app_context}}">{{translate "Home"}}</a></li>
            <li class="breadcrumb-item"><a href="{{app_context}}/settings">{{translate "Settings"}}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{translate "Import"}}</li>
        </ol>
    </nav>

    <h4 class="mt-3 file-form mt-3">{{translate "Import"}}</h4>
    <div class="col-md-12">
        <p class="text-muted">
            {{translate "You are able to bulk import articles by uploading a Zip file containing files with Markdown content. When new articles are created, the name of the file will become the title/permalink. All imported articles will be set to a draft status."}}"
        </p>
        <form class="form-horizontal" role="form" method="post" action="{{app_context}}/importer" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6">
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <span class="btn btn-default btn-file"><span class="fileinput-new">{{translate "Select zip"}}</span><span class="fileinput-exists">{{translate "Change"}}</span><input type="file" name="import_file" id="import_file"></span>
                        <span class="fileinput-filename"></span>
                        <a href="#" class="close fileinput-exists" data-dismiss="fileinput" style="float: none">&times;</a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group file-form">
                        <button type="submit" class="btn btn-success pull-right">{{translate "Import zip"}}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
