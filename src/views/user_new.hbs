{{!-- <div class="col-md-12">
	<div class="row">
		<div class="col-md-6 col-md-offset-3"> --}}
			{{!-- <ol class="breadcrumb">
	  			<li><a href="{{app_context}}/">{{__ "Home"}}</a></li>
	 			<li><a href="{{app_context}}/Users">{{__ "Users"}}</a></li>
	  			<li class="active">{{__ "New user"}}</li>
			</ol> --}}
			{{!-- <nav aria-label="breadcrumb">
				<ol class="breadcrumb">
					<li class="breadcrumb-item"><a href="{{app_context}}/">{{__ "Home"}}</a></li>
					<li class="breadcrumb-item active" aria-current="page"><a href="{{app_context}}/Users">{{__ "Users"}}</li>
					<li class="breadcrumb-item active" aria-current="page">{{__ "New user"}}</li>
				</ol>
			</nav> --}}
			<h3 class="mt-3">{{translate "New user"}}</h3>
			<form method="post" id="edit_form" action="{{app_context}}/user_insert" data-toggle="validator">
            <div class="form-group">
		    	<label>{{translate "Users name"}} *</label>
		    	<input type="text" class="form-control" name="users_name" value="{{user.users_name}}" required>
		  	</div>
			<div class="form-group">
		    	<label>{{translate "User email"}} *</label>
		    	<input type="email" class="form-control" name="user_email" value="{{user.user_email}}" required>
		  	</div>
		  	<div class="form-group">
			    <label>{{translate "User password"}} *</label>
			    <input type="password" class="form-control" id="user_password" name="user_password" required>
		  	</div>
		  	<div class="form-group">
			    <label>{{translate "Password confirm"}} *</label>
			    <input type="password" data-match="#user_password"  class="form-control" name="frm_user_password_confirm" required>
		  	</div>
		  	<div class="form-group">
    			<div class="pull-right">
      				<button type="submit" class="btn btn-primary">{{translate "Create"}}</button>
    			</div>
  			</div>
		{{!-- </div>
	</div>
</div> --}}
