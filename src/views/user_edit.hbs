{{!-- <div class="col-md-12">
	<div class="row">
		<div class="col-md-6 col-md-offset-3"> --}}
			{{!-- <ol class="breadcrumb">
	  			<li><a href="{{app_context}}/">{{__ "Home"}}</a></li>
	 			<li><a href="{{app_context}}/Users">{{__ "Users"}}</a></li>
	  			<li class="active">{{__ "User edit"}}</li>
			</ol> --}}
			{{!-- <nav aria-label="breadcrumb">
				<ol class="breadcrumb">
					<li class="breadcrumb-item"><a href="{{app_context}}/">{{__ "Home"}}</a></li>
					<li class="breadcrumb-item active" aria-current="page"><a href="{{app_context}}/Users">{{__ "Users"}}</a></li>
					<li class="breadcrumb-item active" aria-current="page">{{__ "User edit"}}</li>
				</ol>
			</nav> --}}
		<div class="container">
			<h3 class="mt-3">{{translate "User edit"}}</h3>
			<form method="post" id="edit_form" action="{{app_context}}/user_update" data-toggle="validator">
			<input type="hidden" name="user_id" value="{{user._id}}" />
            <div class="form-group">
		    	<label>{{translate "Users name"}}</label>
		    	<input type="text" class="form-control" name="users_name" value="{{user.users_name}}" required>
		  	</div>
			<div class="form-group">
		    	<label>{{translate "User email"}}</label>
		    	<input type="text" class="form-control" name="user_email" value="{{user.user_email}}" readonly>
		  	</div>
		  	<div class="form-group">
			    <label>{{translate "User password"}} {{#ifCond session.user '==' user.user_email}}*{{/ifCond}}</label>
			    <input type="password" class="form-control" name="user_password" {{#ifCond session.user '==' user.user_email}}required{{/ifCond}}>
		  	</div>
		  	<div class="form-group">
			    <label>{{translate "Password confirm"}} {{#ifCond session.user '==' user.user_email}}*{{/ifCond}}</label>
			    <input type="password" data-validation-match-match="user_password" data-validation-match-message="{{translate "Password values do not match"}}" class="form-control" name="frm_user_password_confirm" {{#ifCond session.user '==' user.user_email}}required{{/ifCond}}>
		  	</div>
            {{#is_an_admin session.is_admin}}
                {{#ifCond session.user '!=' user.user_email}}
                    <div class="checkbox">
                        <label>
                            <input name="user_admin" {{#checked_state user.is_admin}}{{/checked_state}} type="checkbox"> {{translate "User is admin?"}}
                        </label>
                    </div>
                {{/ifCond}}
            {{/is_an_admin}}
		  	<div class="form-group">
    			<div class="pull-right">
      				<button type="submit" class="btn btn-primary">{{translate "Update"}}</button>
    			</div>
  			</div>
		</div>
		{{!-- </div>
	</div>
</div> --}}
