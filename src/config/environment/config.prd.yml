environment:

  # DB Config
  MONGO_CONN: 'mongodb://dbadmin:<EMAIL>:27017/'
  MONGO_OPTIONS: '?tls=true&readPreference=secondaryPreferred&retryWrites=false'
  MONGO_DBNAME: 'wiki'

  WIKI_ADMIN_EMAIL: '<EMAIL>'

  # Internal docker container name for performance hub
  PERF_HUB_URL: 'http://portal-server:8010'

  # Performance hub 'internal API key'
  PERF_HUB_API_KEY: '32aa84c47da94a65b71a0269f6960859'

  # BASIC AUTH FOR PUBLIC EXPOSED APIs
  BASIC_USER: 'algolia'
  BASIC_PASS: '8RGtkIPUueBnSMjoE5Fs'

  OPENAI_API_KEY: ***************************************************