{"name": "openkb", "version": "1.0.23", "description": "openKB is an Open Source Nodejs Markdown based knowledge base/FAQ/Wiki app", "private": false, "scripts": {"start": "node app.js", "dbUpgrade": "node data/mongodbUpgrade.js", "uglify": "node bin/uglify.js", "build": "gulp", "watch": "gulp watch"}, "engines": {"node": ">=8.0.0"}, "dependencies": {"algoliasearch": "^5.23.0", "application-translator": "https://content.gymsystems.co/npms/application-translator/prod/application-translator-latest.tgz", "async": "^2.6.2", "axios": "^1.3.3", "bcrypt": "5.1.0", "body-parser": "^1.19.0", "bootstrap": "^4.6.0", "compression": "^1.7.4", "connect-mongo": "4.6.0", "cookie-parser": "~1.3.3", "debug": "~2.6.7", "express": "^4.17.1", "express-basic-auth": "^1.2.1", "express-handlebars": "^3.1.0", "express-session": "^1.16.1", "express-state": "^1.4.0", "extract-zip": "^1.6.7", "font-awesome": "^4.6.3", "glob": "^5.0.14", "gulp-run": "^1.7.1", "hbs": "^4.0.4", "i18n-2": "^0.6.3", "image-size": "^1.0.2", "instantsearch.js": "^4.57.0", "jquery": "^3.4.1", "js-yaml": "^4.1.0", "jsdom": "^23.0.1", "jsonschema": "^1.2.4", "jszip": "^2.6.1", "junk": "^2.0.0", "lodash": "^4.17.11", "markdown-it": "^13.0.2", "markdown-it-image-size": "^13.3.1", "markdown-it-imsize": "^2.0.1", "materialize-css": "^1.0.0-rc.2", "mime-types": "^2.1.24", "mkdirp": "^0.5.1", "moment": "^2.24.0", "mongodb": "^4.1.0", "mongodb-uri": "^0.9.7", "morgan": "^1.9.1", "multer": "^1.4.1", "node-cron": "^3.0.2", "node-fetch": "^2.6.6", "node-waves": "^0.7.6", "openai": "^4.0.0", "pm2": "^5.3.0", "popper.js": "^1.16.1", "remove-markdown": "^0.1.0", "require-main-filename": "^1.0.1", "rimraf": "^2.6.3", "sanitize-html": "^1.20.1", "sass": "1.63.6", "showdown": "^2.1.0", "simplemde": "^1.11.2", "sitemap": "^1.8.1", "slug": "^8.2.3", "speakingurl": "^10.0.0", "string-strip-html": "^4.5.0", "uglify-js": "^3.5.15", "uglifycss": "0.0.27", "walk": "^2.3.14"}, "main": "app.js", "devDependencies": {"browser-sync": "^2.27.7", "concurrently": "^7.0.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-changed": "^4.0.3", "gulp-clean-css": "^4.3.0", "gulp-cli": "^2.3.0", "gulp-concat": "^2.6.1", "gulp-imagemin": "^8.0.0", "gulp-line-ending-corrector": "^1.0.3", "gulp-sass": "5.1.0", "gulp-sourcemaps": "^3.0.0", "gulp-terser": "^2.1.0", "gulp-uglify": "^3.0.2", "gulp-webp": "^4.0.1", "node-sass": "9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/mrvautin/openKB.git"}, "keywords": ["knowledge base", "markdown", "express", "documentation", "platform", "nodejs", "kb", "documentation", "faq", "wiki", "openkb", "mongodb"], "license": "MIT"}