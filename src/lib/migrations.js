const fs = require('fs');
const path = require('path');
const { getDb } = require('./db');

module.exports.run = async () => {
    const db = await getDb();

    const migratePath = path.join(__dirname, '../migrate');
    const dirents = fs.readdirSync(migratePath, { withFileTypes: true });

    const migrationFiles = dirents
        .filter((dirent) => dirent.isFile())
        .map((dirent) => dirent.name);

    for (const file of migrationFiles) {
        const count = await db.migration.count({ migration: file });
        if (count > 0) {
            continue;
        }

        const scriptPath = path.join(__dirname, '../migrate', file);
        const script = require(scriptPath);
        const isCompleted = await script.run(db);
        console.info('--> is Completed -', isCompleted + ':', file);

        const migration = { createdAt: Date.now(), migration: file };
        await db.migration.insert(migration);
    }
};
