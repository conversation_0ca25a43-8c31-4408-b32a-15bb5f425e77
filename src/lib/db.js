const MongoClient = require('mongodb').MongoClient;
const mongodbUri = require('mongodb-uri');
const LRU = require('lru-cache');
const globalStore = require('./globalStore');

const defaultURI = `${process.env.MONGO_CONN}${process.env.MONGO_DBNAME}${process.env.MONGO_OPTIONS}`;

const clientCache = new LRU({
    max: 500, // Max number of tenant DBs to keep active
    ttl: 1000 * 60 * 10, // 10 min TTL
    dispose: (key, db) => db.client?.close(),
});

async function getDb(uri) {
    try {
        if (!uri) {
            const tenantId = await globalStore.getStore().get('tenantId');
            if (tenantId) {
                const defaultURIObj = mongodbUri.parse(defaultURI);
                defaultURIObj.database = tenantId;
                uri = mongodbUri.format(defaultURIObj);
            }
        }

        if (!uri) {
            console.error('No uri provided');
            return null;
        }

        const { database: name } = mongodbUri.parse(uri);

        if (clientCache.has(name)) {
            return clientCache.get(name);
        }

        const client = await MongoClient.connect(uri, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        const db = client.db(name);
        db.client = client;
        const allCollections = await db.listCollections().toArray();

        if (defaultURI === uri) {
            db.sessions = await createCollectionIfNotExist(
                db,
                'sessions',
                allCollections
            );
            db.tenants = await createCollectionIfNotExist(
                db,
                'tenants',
                allCollections
            );
        }

        if (defaultURI !== uri) {
            db.users = await createCollectionIfNotExist(
                db,
                'users',
                allCollections
            );
            db.kb = await createCollectionIfNotExist(db, 'kb', allCollections);
            db.votes = await createCollectionIfNotExist(
                db,
                'votes',
                allCollections
            );
            db.topics = await createCollectionIfNotExist(
                db,
                'topics',
                allCollections
            );
            db.helpfulornot = await createCollectionIfNotExist(
                db,
                'helpfulornot',
                allCollections
            );
            db.searchtotals = await createCollectionIfNotExist(
                db,
                'searchtotals',
                allCollections
            );
            db.pageviews = await createCollectionIfNotExist(
                db,
                'pageviews',
                allCollections
            );
            db.comments = await createCollectionIfNotExist(
                db,
                'comments',
                allCollections
            );
            db.migration = await createCollectionIfNotExist(
                db,
                'migration',
                allCollections
            );
        }

        console.info(`Connected to DB ${name}`);
        clientCache.set(name, db);
        return db;
    } catch (err) {
        console.error('Failed to get DB', err);
        return null;
    }
}

async function createCollectionIfNotExist(db, collectionName, allCollections) {
    const collectionExists = allCollections.some(
        (c) => c.name === collectionName
    );

    if (!collectionExists) {
        await new Promise((resolve) =>
            db.createCollection(collectionName, (err, res) => {
                if (err) {
                    console.error(
                        `Failed to create collection ${collectionName}`,
                        err
                    );
                    return resolve(null);
                }

                console.info(`Created collection ${collectionName}`);
                resolve(res);
            })
        );
    }

    return db.collection(collectionName);
}

function getDbUri(dbUrl) {
    const dbUriObj = mongodbUri.parse(dbUrl);
    return mongodbUri.format(dbUriObj);
}

module.exports = {
    getDb,
    getDbUri,
    defaultURI,
};
