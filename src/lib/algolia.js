const { algoliasearch } = require('algoliasearch');
const globalStore = require('../lib/globalStore');
const { getDb } = require('./db');

const APP_ID = process.env.ALGOLIA_APP_ID;
const READ_KEY = process.env.ALGOLIA_API_READ_KEY;
const WRITE_KEY = process.env.ALGOLIA_API_WRITE_KEY;
const TENANT_ID_PREFIX = process.env.TENANT_ID_PREFIX || 'wiki-dev-';
const KB_INDEX_PREFIX = process.env.KB_INDEX_PREFIX || `${TENANT_ID_PREFIX}kb-`;

const getAlgoliaIndexName = async () => {
    const dbName = await globalStore.getStore().get('tenantId');

    if (!dbName) {
        console.error('Database name is required to get Algolia index.');
        return `${KB_INDEX_PREFIX}unknown`;
    }

    return dbName.replace(TENANT_ID_PREFIX, KB_INDEX_PREFIX);
};

const getAlgoliaConfig = async (type = 'read') => {
    const indexName = await getAlgoliaIndexName();

    return {
        appId: APP_ID,
        apiKey: type === 'write' ? WRITE_KEY : READ_KEY,
        index: indexName,
    };
};

const getAlgoliaClient = async (type = 'read') => {
    const config = await getAlgoliaConfig(type);
    return algoliasearch(config.appId, config.apiKey);
};

const initAlgoliaArticlesIndex = async () => {
    const indexName = await getAlgoliaIndexName();
    const client = await getAlgoliaClient('write');

    try {
        await client.setSettings({
            indexName,
            indexSettings: {
                searchableAttributes: [
                    '_id',
                    'kb_regional_visibility',
                    'kb_body',
                    'kb_title',
                    'unordered(kb_published)',
                    'unordered(kb_security_level)',
                ],
                attributesForFaceting: ['kb_regional_visibility'],
                customRanking: ['desc(kb_viewcount)'],
                attributesToSnippet: ['kb_body:50'],
            },
        });
        console.info(`${indexName} index initialized.`);
    } catch (err) {
        console.error(`Error initializing ${indexName} index:`, err);
    }
};

const syncArticlesToAlgolia = async () => {
    try {
        const db = await getDb();

        const articles = await db.kb
            .find({
                kb_versioned_doc: { $ne: true },
                kb_published: { $in: [true, 'true'] },
            })
            .toArray();

        const objects = articles.map((article) => ({
            objectID: article._id.toString(),
            ...article,
        }));

        const indexName = await getAlgoliaIndexName();
        const client = await getAlgoliaClient('write');

        const { hits } = await client.browseObjects({ indexName });
        const recordsToRemove = hits.filter(
            (hit) => !objects.some((obj) => obj.objectID === hit.objectID)
        );

        const requests = recordsToRemove.map((hit) => ({
            action: 'deleteObject',
            indexName,
            objectID: hit.objectID,
        }));

        articles.forEach((article) => {
            const regions = (
                article.kb_regional_visibility?.split(',') || []
            ).filter(Boolean);
            if (regions.length === 0) regions.push('global');

            requests.push({
                action: 'updateObject',
                indexName,
                body: {
                    objectID: article._id.toString(),
                    ...article,
                    kb_regional_visibility: regions,
                },
            });
        });

        if (requests.length === 0) {
            console.info('No articles to sync to Algolia.');
            return [200, { message: 'No articles to sync to Algolia.' }];
        }

        await client.multipleBatch({ requests });

        console.info(
            `Synced ${requests.length} articles to Algolia index: ${indexName}`
        );
    } catch (error) {
        console.error('Error syncing articles to Algolia:', error);
    }
};

module.exports = {
    getAlgoliaConfig,
    getAlgoliaClient,
    getAlgoliaIndexName,
    initAlgoliaArticlesIndex,
    syncArticlesToAlgolia,
};
