const express = require('express');
const path = require('path');
const logger = require('morgan');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const session = require('express-session');
const bcrypt = require('bcrypt');
const markdownit = require('markdown-it')({ html: true, linkify: true, typographer: true });
const moment = require('moment');
const fs = require('fs');
const remove_md = require('remove-markdown');
const MongoStore = require('connect-mongo');
const expstate = require('express-state');
const compression = require('compression');
const stripHtml = require("string-strip-html");
let handlebars = require('express-handlebars');

const { t } = require('application-translator/dist/translate/translate.node');
const index = require('./routes/index');
const api = require('./routes/api');
const mods = require('./routes/mods');
const common = require('./routes/common');
const { getDb, defaultURI } = require('./lib/db');
const globalStore = require('./lib/globalStore');
const migrations = require('./lib/migrations');
const { syncArticlesToAlgolia, initAlgoliaArticlesIndex } = require('./lib/algolia');

const app = express();
const config = common.read_config();

// setup the translation
const i18n = new (require('i18n-2'))({
    locales: ['en', 'de', 'da', 'es', 'cn', 'ru', 'pt-br', 'jp', 'fi', 'sv', 'tr'],
    directory: path.join(__dirname, 'locales/'),
    defaultLocale: 'en',
    cookieName: 'locale'
});

if(config.settings.locale){
    i18n.setLocale(config.settings.locale);
    i18n.setLocaleFromCookie();
}

app.use((req, res, next) => {
    console.log('header', req.headers["accept-language"])
    common.initTranslations(req)
    
    next()
  })


// compress all requests
app.use(compression());

// setup express-state
expstate.extend(app);

// add the config items we want to expose to the client
common.config_expose(app);

// check theme directory exists if set
if(config.settings.theme){
    if(!fs.existsSync(path.join(__dirname, '/public/themes/', config.settings.theme))){
        console.error('Theme folder does not exist. Please check theme in /config/config.json');
        process.exit();
    }
}

// view engine setup
app.set('views', path.join(__dirname, '/views'));
app.engine('hbs', handlebars({
    extname: 'hbs',
    layoutsDir: path.join(__dirname, '/views/layouts'),
    defaultLayout: 'layout.hbs',
    partialsDir: __dirname+'/views/partials',
    helpers: {
        trimString: function (passedString) {
            return passedString.substring(0,350);
        },
        stripHtml: function (passedString) {
            return stripHtml(passedString);
        },
        trimStringStripHtml: function (passedString) {
            return stripHtml(passedString).substring(0,350);
        },
        json: function (passedString) {
            return JSON.stringify(passedString, null, 4);
        },
    }
}));
app.set('view engine', 'hbs');


// helpers for the handlebar templating platform
handlebars = handlebars.create({
    helpers: {
        __: function (value){
            return i18n.__(value);
        },
        split_normal: function (keywords){
            let app_context = config.settings.app_context;
            if(app_context !== ''){
                app_context = '/' + app_context;
            }
            if(keywords){
                let array = keywords.split(','); let links = '';
                for(let i = 0; i < array.length; i++){
                    if(array[i].trim() !== ''){
                        links += '<a class="related-article" data-item="'+array[i].trim()+'" href="' + app_context + '/search/' + array[i].trim() + '">' + array[i].trim() + '</a><br>';
                    }
                }return links.substring(0, links.length - 1);
            }
            return keywords;
        },
        keywords_list: function (keywords = ''){
            const keywordArray = keywords.split(',').map(keyword => keyword.trim());
            let html = '';
            keywordArray.forEach(keyword => {
                html += `<li class="article-tags" data-item="${keyword}"><a href="#">${keyword}</a></li>`
            })

            return html;
        },

        combine_words: function (keywords){
            return keywords.replace(/\s/g, '');
        },
        split_keywords: function (keywords){
            let app_context = config.settings.app_context;
            if(app_context !== ''){
                app_context = '/' + app_context;
            }
            if(keywords){
                let array = keywords.split(','); let links = '';
                for(let i = 0; i < array.length; i++){
                    if(array[i].trim() !== ''){
                        links += '<a href="' + app_context + '/search/' + array[i].trim() + '">' + array[i].trim() + '</a> <span class="keywordSeparator">&nbsp;&bull;&nbsp;</span> ';
                    }
                }return links.substring(0, links.length - 1);
            }
            return keywords;
        },
        split_keywords2: function (keywords){
            let app_context = config.settings.app_context;
            if(app_context !== ''){
                app_context = '/' + app_context;
            }
            if(keywords){
                let array = keywords.split(',');
                let links = '';
                for (let i = 0; i < array.length; i++){
                    if (array[i].trim() !== '') {
                        //replace '/' with empty string
                        let cleanedLink = array[i].trim().replace(/\//g," ");
                        links += '<a class="search-results-tags mb-2" href="' + app_context + '/search/' + cleanedLink + '">' + array[i].trim() + '</a> <span class="keywordSeparator">&nbsp;</span> ';
                    }
                }
                return links.substring(0, links.length - 1);
            }
            return keywords;
        },
        produce_article_links: function (article){
            let app_context = config.settings.app_context;
            let links = '';
            if(app_context !== ''){
                app_context = '/' + app_context;
            }
            
            return '<a class="search-results-tags mb-2" href="' + app_context + '/kb/' + article._id + '">' + article.kb_title +'</a> <span class="keywordSeparator">&nbsp;</span> ';
        },
        encodeURI: function (url){
            return encodeURI(url);
        },
        removeEmail: function (user){
            return user.replace(/ - ([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/, '');
        },
        checked_state: function (state){
            if(state === true || state === 'true'){
                return 'checked';
            }
            return '';
        },
        isAssociatedWithTopic: function (article){
            if(article.kb_pinned === undefined && article._id === undefined) {
                return ''
            }

            if(article.kb_pinned === true || article.kb_pinned === 'true' || article.kb_pinned_topic !== ''){
                return'checked';
            }
            return '';
        },
        ternary: function (test, yes, no){
            return test ? t(yes) : t(no);
        },
        select_state: function (value, option){
            if(value === option){
                return'selected';
            }
            return '';
        },
        active: function (value, option){
            console.log(value, '-asdf-', option)
            if(value == option){
                return'active';
            }
            return '';
        },
        if_null: function (val1, val2){
            if(val1){
                return val1;
            }
            return val2;
        },
        substring: function (val, length){
            if(val.length > length){
                return val.substring(0, length);
            }
            return val;
        },
        strip_md: function (md){
            if(md !== null && md !== ''){
                return remove_md(md);
            }
            return md;
        },
        view_count: function (value){
            if(value === '' || value === undefined){
                return'0';
            }
            return value;
        },
        ifBoth: function (val1, val2, options){
            if(val1 && val2){
                return options.fn(this);
            }
            return options.inverse(this);
        },
        format_date: function (date){
            if(config.settings.date_format){
                return moment(date).format(config.settings.date_format);
            }
            return moment(date).format('DD/MM/YYYY h:mmA');
        },
        app_context: function (){
            if(config.settings.app_context !== undefined && config.settings.app_context !== ''){
                return'/' + config.settings.app_context;
            }
            return'';
        },
        simpleCSS: function (config){
            let cssString = '';
            if(typeof config.settings.style.cssHeaderBackgroundColor !== 'undefined' && config.settings.style.cssHeaderBackgroundColor !== ''){
                cssString = cssString + '.navbar-default, .headerText h1 {background-color:' + config.settings.style.cssHeaderBackgroundColor + ';}';
            }
            if(typeof config.settings.style.cssHeaderTextColor !== 'undefined' && config.settings.style.cssHeaderTextColor !== ''){
                cssString = cssString + '.navbar-default .navbar-brand, .headerText h1 {color:' + config.settings.style.cssHeaderTextColor + ';}';
                cssString = cssString + '.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus, .brand-text, .contactLink {color:' + config.settings.style.cssHeaderTextColor + ' !important;}';
            }
            if(typeof config.settings.style.cssFooterBackgroundColor !== 'undefined' && config.settings.style.cssFooterBackgroundColor !== ''){
                cssString = cssString + '.footer{background-color:' + config.settings.style.cssFooterBackgroundColor + ';}';
            }
            if(typeof config.settings.style.cssFooterTextColor !== 'undefined' && config.settings.style.cssFooterTextColor !== ''){
                cssString = cssString + '.footer p{color:' + config.settings.style.cssFooterTextColor + ';}';
            }
            if(typeof config.settings.style.cssButtonBackgroundColor !== 'undefined' && config.settings.style.cssButtonBackgroundColor !== ''){
                cssString = cssString + '#btn_search, .btn-default{background-color:' + config.settings.style.cssButtonBackgroundColor + ';border-color:' + config.settings.style.cssButtonBackgroundColor + ';}';
            }
            if(typeof config.settings.style.cssButtonTextColor !== 'undefined' && config.settings.style.cssButtonTextColor !== ''){
                cssString = cssString + '#btn_search, .btn-default{color:' + config.settings.style.cssButtonTextColor + ';}';
            }
            if(typeof config.settings.style.cssLinkColor !== 'undefined' && config.settings.style.cssLinkColor !== ''){
                cssString = cssString + 'a, footer a, a:hover, a:focus, .contactLink a{color:' + config.settings.style.cssLinkColor + ' !important;}';
            }
            if(typeof config.settings.style.cssTextColor !== 'undefined' && config.settings.style.cssTextColor !== ''){
                cssString = cssString + 'body, .panel-primary>.panel-heading, .list-group-heading{color:' + config.settings.style.cssTextColor + ';}';
            }
            if(typeof config.settings.style.cssFontFamily !== 'undefined' && config.settings.style.cssFontFamily !== ''){
                cssString = cssString + 'body, h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6{font-family:' + config.settings.style.cssFontFamily + ';}';
            }
            return cssString;
        },
        ifCond: function (v1, operator, v2, options){
            switch(operator){
                case'==':
                    return(v1 == v2) ? options.fn(this) : options.inverse(this);
                case'!=':
                    return(v1 !== v2) ? options.fn(this) : options.inverse(this);
                case'===':
                    return(v1 === v2) ? options.fn(this) : options.inverse(this);
                case'<':
                    return(v1 < v2) ? options.fn(this) : options.inverse(this);
                case'<=':
                    return(v1 <= v2) ? options.fn(this) : options.inverse(this);
                case'>':
                    return(v1 > v2) ? options.fn(this) : options.inverse(this);
                case'>=':
                    return(v1 >= v2) ? options.fn(this) : options.inverse(this);
                case'&&':
                    return(v1 && v2) ? options.fn(this) : options.inverse(this);
                case'||':
                    return(v1 || v2) ? options.fn(this) : options.inverse(this);
                default:
                    return options.inverse(this);
            }
        },
        ifOrNull: function (v1, v2, options){
          
            return (v1 || !v2) ? options.fn(this) : options.inverse(this);
        },
        ifFalseOrNull: function (v1, options){
            
            return (!v1 || v1 === '' || v1 === 'false') ? options.fn(this) : options.inverse(this);
        },
        is_an_admin: function (value, options){
            if(value === 'true'){
                return options.fn(this);
            }
            return options.inverse(this);
        },
        not_empty_array: function (arr, options){
            if(Array.isArray(arr) && arr.length){
                return options.fn(this);
            }
            return options.inverse(this);
        },
        translate: function(phrase) {
          
            return t(phrase);
        },
        isNull: function (value, options) {
            if(value === null || value === undefined || value === ''){
                return options.fn(this)
            }
            return options.inverse(this);
        },
        visibility: function (visibilityWithIso = []) {
            const flagsDirectory = `${config.settings.app_context}/flags/`;
            const countries = visibilityWithIso;

            let flags = [];
            
        
         
            if(countries.length === 0) {
                return `<ul><li><label title="Global">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="16" viewBox="0 0 420 420" stroke="#000" fill="none">
                    <path stroke-width="26" d="M209,15a195,195 0 1,0 2,0z"></path>
                    <path stroke-width="18" d="m210,15v390m195-195H15M59,90a260,260 0 0,0 302,0 m0,240 a260,260 0 0,0-302,0M195,20a250,250 0 0,0 0,382 m30,0 a250,250 0 0,0 0-382"></path>
                </svg>
                </label><li></ul>`;
            }

            countries.forEach(country => {
                flags.push(`<li><img class="country-flag-icon" title="${country.country}" data-country="${country.country}" src="/${flagsDirectory}${country.iso}.png" /></li>`)
            });

            return `<ul>${flags.join('')}</ul>`;
        },
        getValueOfIndex: function (index, array) {
            if(!index || !array) return '';
            
            return array[index];
        },
        dateFormat: function (format, value) {
            return moment(value).format(format);
        },
        topicLikeSplit: function (w) {
            if([null, undefined, ''].includes(w)) {
                return '';
            } 

            return w;
        },
        generateStatus : function (status) {
            if(status === 'true') {
                return `<div class="article-cell__status article-cell__status--published">
                            <span>Published</span>
                            <i></i>
                        </div>`
            } else {
                return `<div class="article-cell__status article-cell__status--draft">
                            <span>Draft</span>
                            <i></i>
                        </div>`
            }
        },
        noSpace: function(string) {
            return string.replace(/\s/g, '');
        }
    }
});

app.enable('trust proxy');
app.set('port', process.env.PORT || 4444);
app.set('bind', process.env.BIND || '0.0.0.0');
app.use(logger('dev'));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(cookieParser('5TOCyfH3HuszKGzFZntk'));

//new session implementation for mongodb
const sessionStore = MongoStore.create({
	// mongoUrl: process.env.MONGO_CONN + "/openkb",
	mongoUrl: defaultURI,
	ttl: 20000,
});

app.use(session({
	resave: false,
    saveUninitialized: true,
    secret: 'pAgGxo8Hzg7PFlv1HpO8Eg0Y6xtP7zYx',
    cookie: {
        path: '/',
        httpOnly: true,
        maxAge: 3600000 * 24
    },
	store: sessionStore
}));

app.use((req, res, next) => {
    globalStore.run(new Map(), () => {
        const tenantId = req.session.tenantId;
        if (!tenantId) return next();
        globalStore.getStore().set('tenantId', tenantId);
        return next();
    });
});

// setup the app context
let app_context = '';
if(config.settings.app_context !== undefined && config.settings.app_context !== ''){
    app_context = '/' + config.settings.app_context;
}

// frontend modules loaded from NPM
app.use(app_context + '/static', express.static(path.join(__dirname, 'public/')));
app.use(app_context + '/uploads', express.static(path.join(__dirname, 'public', 'uploads')));
app.use(app_context + '/font-awesome', express.static(path.join(__dirname, 'node_modules/font-awesome/')));
app.use(app_context + '/jquery', express.static(path.join(__dirname, 'node_modules/jquery/dist/')));
app.use(app_context + '/bootstrap', express.static(path.join(__dirname, 'node_modules/bootstrap/dist/')));
// app.use(app_context + '/bootstrapTabs', express.static(path.join(__dirname, 'node_modules/bootstrap/js/')));
app.use(app_context + '/simplemde', express.static(path.join(__dirname, 'node_modules/simplemde/dist/')));
app.use(app_context + '/markdown-it', express.static(path.join(__dirname, 'node_modules/markdown-it/dist/')));
app.use(app_context + '/stylesheets', express.static(path.join(__dirname, 'public/stylesheets')));
app.use(app_context + '/fonts', express.static(path.join(__dirname, 'public/fonts')));
app.use(app_context + '/javascripts', express.static(path.join(__dirname, 'public/javascripts')));
app.use(app_context + '/favicon.png', express.static(path.join(__dirname, 'public/favicon.png')));

// serving static content
app.use(express.static(path.join(__dirname, 'public')));
app.use(app_context + '/', express.static(path.join(__dirname, 'public')));

// Make stuff accessible to our router
app.use((req, res, next) => {
    req.markdownit = markdownit;
    req.handlebars = handlebars.helpers;
    req.bcrypt = bcrypt;
    req.i18n = i18n;
    req.app_context = app_context;
    req.i18n.setLocaleFromCookie();

    next();
});

// setup the routes
if(app_context !== ''){
    app.use(app_context, index);
	app.use(app_context, api);
	app.use(app_context, mods);
}else{
    app.use('/', index);
    app.use('/', api);
    app.use('/', mods);
}

// Healthcheck URL
app.get('/healthcheck', function (req, res) {
    res.send(200, {"message": "System is online"});
})

// catch 404 and forward to error handler
app.use((req, res, next) => {
    let err = new Error('Not Found');
    err.status = 404;
    next(err);
});

// === Error handlers ===

// development error handler
// will print stacktrace
if(app.get('env') === 'development'){
    app.use((err, req, res, next) => {
        console.error(err.stack);
        res.status(err.status || 500);
        res.render('error', {
            message: err.message,
            error: err,
            helpers: handlebars.helpers,
            config: config
        });
    });
}

// production error handler
// no stacktraces leaked to user
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(err.status || 500);
    res.render('error', {
        message: err.message,
        error: {},
        helpers: handlebars.helpers,
        config: config
    });
});

getDb(defaultURI)
    .then(initializeTenantDbs)
    .catch((err) => {
        console.error('Error connecting to databases', err);
        process.exit(2);
    }
);

async function initializeTenantDbs(defaultDb) {
    const tenants = await defaultDb.tenants.find({}).toArray();

    // initialize all tenant dbs and run migrations
    await Promise.all(
        tenants.map(({ _id }) => {
            return globalStore.run(new Map(), async () => {
                globalStore.getStore().set('tenantId', _id);

                await migrations.run();
                await initAlgoliaArticlesIndex();
                await syncArticlesToAlgolia();
            });
        })
    );

    const port = app.get('port');
    const bind = app.get('bind');

    app.listen(port, bind, () => {
        console.info(`openKB running on host: http://${bind}:${port}`);
        app.emit('openKBstarted');
    });
}

function exitHandler(options, err) {
    if (err) {
        console.log(err.stack);
    }
    if (options.exit) {
        process.exit();
    }
}

//do something when app is closing
process.on('exit', exitHandler.bind(null,{cleanup:true}));

//catches ctrl+c event
process.on('SIGINT', exitHandler.bind(null, {exit:true}));

// catches "kill pid" (for example: nodemon restart)
process.on('SIGUSR1', exitHandler.bind(null, {exit:true}));
process.on('SIGUSR2', exitHandler.bind(null, {exit:true}));

//catches uncaught exceptions
process.on('uncaughtException', exitHandler.bind(null, {exit:true}));

module.exports = app;
