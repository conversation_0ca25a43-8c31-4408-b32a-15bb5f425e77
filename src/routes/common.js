const path = require('path');
const fs = require('fs');
const ObjectID = require('mongodb').ObjectId;
const mongodbUri = require('mongodb-uri');
const sanitizeHtml = require('sanitize-html');
const axios = require('axios');
const {init, t, clear} = require('application-translator/dist/translate/translate.node');
const moment = require('moment');
const yaml  = require('js-yaml');
const slug = require('slug');
const classy = require("../public/javascripts/markdown-it-classy");
const showdown  = require('showdown');
const jsdom = require('jsdom');
const { getDb, defaultURI } = require('../lib/db');
const { phAPI } = require('../lib/phAPI');
const migrations = require('../lib/migrations');
const globalStore = require('../lib/globalStore');
const { syncArticlesToAlgolia, initAlgoliaArticlesIndex } = require('../lib/algolia');
const markdownit = require('markdown-it')({html: true,
    linkify: true,
    typographer: true,
    breaks: false,}).use(classy).use(require('markdown-it-imsize'));

const TRANSLATION_API = process.env.TRANSLATOR_API;
const TENANT_ID_PREFIX = process.env.TENANT_ID_PREFIX || 'wiki-dev-';

exports.clear_session_value = function (session, session_var){
    let temp = session[session_var];
    session[session_var] = null;
    return temp;
};

exports.read_config = function(){
    // preferred path
    let configFile = path.join(__dirname, '..', 'config', 'config.json');

    // depreciated path
    let defaultConfigFile = path.join(__dirname, 'config.js');
    if(fs.existsSync(defaultConfigFile) === true){
        // create config dir if doesnt exist
        let dir = path.join(__dirname, '..', 'config');
        if(!fs.existsSync(dir)){
            fs.mkdirSync(dir);
        }

        // if exists, copy our config from /routes to /config
        let tempconfig = fs.readFileSync(defaultConfigFile, 'utf8');
        fs.writeFileSync(configFile, tempconfig, 'utf8');
        // remove old file
        fs.unlinkSync(defaultConfigFile);
    }
    // load config file
    let rawData = fs.readFileSync(configFile, 'utf8');
    let loadedConfig = JSON.parse(rawData);

    if(loadedConfig.settings.database.type === 'mongodb'){
        loadedConfig.settings.database.connection_string = process.env.MONGODB_CONNECTION_STRING || loadedConfig.settings.database.connection_string;
    }

    if(typeof loadedConfig.settings.route_name === 'undefined' || loadedConfig.settings.route_name === ''){
        loadedConfig.settings.route_name = 'kb';
    }

    // set the environment depending on the NODE_ENV
    let environment = '.min';
    if(process.env.NODE_ENV === 'development' || process.env.NODE_ENV === undefined){
        environment = '';
    }
    loadedConfig.settings.env = environment;

    return loadedConfig;
};

// This is called on the suggest url. If the value is set to false in the config
// a 403 error is rendered.
exports.suggest_allowed = function (req, res, next){
    let config = exports.read_config();
    if(config.settings.suggest_allowed === true){
        next();
        return;
    }
    res.render('error', { message: '403 - Forbidden', helpers: req.handlebars });
};

exports.validate_permalink = function (db, data, callback){
    // only validate permalink if it exists
    if(typeof data.kb_permalink === 'undefined' || data.kb_permalink === ''){
        callback(null, 'All good');
    }else{
        db.kb.count({ 'kb_permalink': data.kb_permalink }, (err, kb) => {
            if(kb > 0){
                callback('Permalink already exists', null); // eslint-disable-line
            }else{
                callback(null, 'All good');
            }
        });
    }
};


//gets the countries from list of clubs in Perf Hub API -used in articles Edit
exports.getCountries = async () => {

    const API_URL = process.env.PERF_HUB_URL + '/internal/clubs/list-clubs';
    const API_KEY = process.env.PERF_HUB_API_KEY;

    let countries = [];
    try {
        await axios.get( API_URL, { headers: { 'x-api-key': API_KEY } } )
        .then(function(response){
            let data = response.data;
    
            //code snippet from holiday management
            if(data) {
                countries = data.reduce((acc, club) => {
                    const country = club.localisation.country;
                    if (country !== false && !acc.some(a => a.iso_code === country.iso_code)) {
                        return [...acc, {
                            ...country,
                            // prefer english name if available
                            full_name: country.en_full_name ?? country.full_name,
                        }];
                    }
                    return acc;
                }, []);
                countries = countries.sort((a, b) => a.iso_code.localeCompare(b.iso_code))
            } else {
                console.log('There are no clubs and cannot get countries')
            }
        });
        return countries;
    } catch (error) {
        console.log('Error occured while getting list of countries', error);
        return [];
    }
}

exports.getCountryOfClub = async (clubId = "") => {
    try {
        const API_CLUB_URL = process.env.PERF_HUB_URL + '/internal/clubs/club-data/' + clubId;
        const API_KEY = process.env.PERF_HUB_API_KEY;

        const response = await axios.get(API_CLUB_URL, { headers: { 'x-api-key': API_KEY } });
        const countryObj = response.data?.placeData?.country;
        const country = countryObj?.en_full_name ?? countryObj?.full_name;
        console.info(' ---> Found Country: ', country);

        return country;
    } catch (error) {
        console.error(`Error occured while checking country of club: ${clubId}`, error);
    }

}

//Checks the user if admin, if not, set session user_levels
//this always runs every time the user browses a page in the wiki
async function checkPerfHubUser(req, res, next) {
    req.session.prev_page = '';
    req.session.tenantId = null;
    req.session.organisationId = null;

    if (req.get('referrer')) {
        let splittedPrevURL = req.get('referrer').split(req.app_context);
        if(splittedPrevURL[1]) {
            req.session.prev_page = req.app_context + splittedPrevURL[1];
        } else {
            req.session.prev_page = req.app_context;
        }
    }

    //when visited, store the clubId in session
    if(req.query.club && req.query.club !== '') {
        req.session.clubId = req.query.club;
    }

    const forwardedEmail = req.headers["x-forwarded-email"] || req.headers["x-forwarded-user"];
    if ( !forwardedEmail || !forwardedEmail.length) {
        //note: you cannot use the admin navbar in localhost:8080/openkb if this else block is here
        //clear user
        req.session.user = null;
        req.session.users_name = null;
        req.session.is_admin = null;
        req.session.pw_validated = null;
        req.session.message = null;
        req.session.message_type = null;
        //set default user level
        req.session.user_level = 100;
        //set default user region
        req.session.user_region = [];
        return;
    }

    function clearUserSession() {
        //clear user
        req.session.user = null;
        req.session.users_name = null;
        req.session.is_admin = null;
        req.session.pw_validated = null;
        req.session.message = null;
        req.session.message_type = null;
    }

    //initial user details in session
    req.session.user = forwardedEmail;
    req.session.needs_setup = "false";
    req.session.pw_validated = null;
    req.session.user_regions = [];
    req.session.userLanguage = await getPreferredLang(req);

    try {
        const saltRounds = 10;
        let bcrypt = req.bcrypt;

        if (!req.session.clubId) {
            throw new Error('Club ID is not set in session');
        }

        const [{ data: access }, { data: facility }] = await Promise.all([
            phAPI.get(`/internal/check-user-access?email=${forwardedEmail}&moduleId=wiki&moduleId=wiki-admin`),
            phAPI.get(`/internal/clubs/club-data/${req.session.clubId}`),
        ]);
        
        if (!access.wiki.hasAccess && !access['wiki-admin'].hasAccess) {
            //USER NOT REGISTERED IN UBX database
            req.session.is_admin = "false";
            //clearUserSession();
            //set default user level
            req.session.user_level = 100;
            //set default user region
            req.session.user_region = '';

            return;
        }

        const tenantId = TENANT_ID_PREFIX + facility.organisationId;

        // initialize the tenant db
        let tenantURI = null;
        const defaultDb = await getDb(defaultURI);
        const foundTenant = await defaultDb.tenants.findOne({ _id: tenantId });

        if (foundTenant) {
            tenantURI = foundTenant.uri;
        } else {
            const tenantURIObj = mongodbUri.parse(defaultURI);
            tenantURIObj.database = tenantId;
            tenantURI = mongodbUri.format(tenantURIObj);
            const createdAt = moment().unix();
            await defaultDb.tenants.insertOne({ _id: tenantId, uri: tenantURI, createdAt });
        }

        req.session.tenantId = tenantId;
        req.session.organisationId = facility.organisationId;
		globalStore.getStore().set('tenantId', tenantId);

        if (!foundTenant) {
            await migrations.run();
            await initAlgoliaArticlesIndex();
            await syncArticlesToAlgolia();
        }

        const db = await getDb();
        let email = '';
        let name = '';

        if (access.email) {
            email = access.email;
            name = email.substring(0, email.indexOf('@'));
            //capitalize first letter
            name = name.charAt(0).toUpperCase() + name.slice(1);

            req.session.user = email;
            req.session.users_name = name;
        }
        
        //check if admin or not
        if (access.isGlobalAdmin) {
            req.session.is_admin = "true";
            req.session.is_pseudoadmin = false;
            //set session user level = 400 = admin
            req.session.user_level = 400;
            //set default user region
            //user regions should be set to null for admins - they can access all regions and all articles
            req.session.user_regions = null;
            console.log(' ---> User is an admin');
            //check if it exists in wiki users database
            db.users.findOne({ 'user_email': email }, (err, user) => {
                if(user){
                    // user already exists with that email address
                    //make sure to update user_id in the user session
                    req.session.user_id = user._id;

                }else{
                    //user did not exists yet, so insert that user - ONLY ADMINS will be stored
                    //default password is the user's name
                    let doc = {
                        users_name: name,
                        user_email: email,
                        user_password: bcrypt.hashSync(name, saltRounds),
                        is_admin: "true"
                    };
                    db.users.insertOne(doc, (err, newUser) => {
                        if (err) {
                            //remove frontend notification after user insert
                            console.log("ERROR WHILE INSERTING NEW USER");
                            // req.session.message = req.i18n.__('User exists');
                            // req.session.message_type = 'danger';
                            // res.redirect(req.app_context + '/user/edit/' + doc._id);
                        } else {
                            console.log("INSERTED NEW USER", newUser);
                            // req.session.message = req.i18n.__('User account inserted');
                            // req.session.message_type = 'success';

                            //make sure to update user_id in the user session
                            req.session.user_id = newUser._id;
                        }
                    });
                }
            });
        } else if (access['wiki-admin'].hasAccess) {
            req.session.is_admin = "true";
            req.session.is_pseudoadmin = true;
            //set session user level = 400 = admin
            req.session.user_level = 400;
            
            console.log(' ---> User is a pseudo admin');

            req.session.user_regions = [];

            access['wiki-admin'].regions.forEach(({ iso_code, full_name }) =>{
                req.session.user_regions.push({ code: iso_code, country: full_name })
            })

            //check if it exists in wiki users database
            db.users.findOne({ 'user_email': email }, (err, user) => {
                if(user){
                    // user already exists with that email address
                    //make sure to update user_id in the user session
                    req.session.user_id = user._id;

                }else{
                    //user did not exists yet, so insert that user - ONLY ADMINS will be stored
                    //default password is the user's name
                    let doc = {
                        users_name: name,
                        user_email: email,
                        user_password: bcrypt.hashSync(name, saltRounds),
                        is_admin: "true"
                    };
                    db.users.insertOne(doc, (err, newUser) => {
                        if (err) {
                            //remove frontend notification after user insert
                            console.log("ERROR WHILE INSERTING NEW USER");
                            // req.session.message = req.i18n.__('User exists');
                            // req.session.message_type = 'danger';
                            // res.redirect(req.app_context + '/user/edit/' + doc._id);
                        } else {
                            console.log("INSERTED NEW USER", newUser);
                            // req.session.message = req.i18n.__('User account inserted');
                            // req.session.message_type = 'success';

                            //make sure to update user_id in the user session
                            req.session.user_id = newUser._id;
                        }
                    });
                }
            });
        } else {
            //USER NOT ADMIN
            req.session.is_admin = "false";
            
            //evaluate user level
            req.session.user_level = 300;
            console.log(' ---> User is NOT Admin but has access to wiki');

            access.wiki.regions.forEach(({ iso_code, full_name }) =>{
                req.session.user_regions.push({code: iso_code, country: full_name })
            })

            //just checking if the user exists in users db before and was an admin before
            //this will update the user is_admin field to false
            db.users.findOne({ 'user_email': forwardedEmail }, (err, user) => {
                if (!user || user.is_amin === "false") return;
                const newUserRole = { is_admin: "false" };

                db.users.update(
                    { _id: user._id },
                    { $set: newUserRole },
                    { multi: true },
                    (err, numReplaced) => console.log('User demoted from being an admin', numReplaced)
                );
            });
        }
    } catch (error) {
        clearUserSession();
        console.error("Failed PH user auth", error);
    }
}


// This is called on all URL's. If the "password_protect" config is set to true
// we check for a login on thsoe normally public urls. All other URL's get
// checked for a login as they are considered to be protected. The only exception
// is the "setup", "login" and "login_action" URL's which is not checked at all.
exports.restrict = function (req, res, next){
    //check the user level first before proceeding
    checkPerfHubUser(req, res, next).then(() => {
        console.log(' ---> User Level = ', req.session.user_level);

        // // if the "needs_setup" session variable is set, we allow as
        // // this means there is no user existing
        if(req.session.needs_setup === true){
            res.redirect(req.app_context + '/setup');
            return;
        }

        //basically just allow all routes - admincheck is done in protected routes
        next();
    });
};

// does the actual login check
exports.check_login = function (req, res, next){

    // set template dir
    exports.setTemplateDir('admin', req);

    if(req.session.user){
        next();
    }else{
        //don't show login anymore
        // res.redirect(req.app_context + '/login');

        console.log(' ---> User is not Admin, redirecting to Index...')
        res.redirect(req.app_context + '/');
    }
};

// exposes select server side settings to the client
exports.config_expose = function (app){
    let config = exports.read_config();
    let clientConfig = {};
    clientConfig.route_name = config.settings.route_name !== undefined ? config.settings.route_name : 'kb';
    clientConfig.add_header_anchors = config.settings.add_header_anchors !== undefined ? config.settings.add_header_anchors : false;
    clientConfig.links_blank_page = config.settings.links_blank_page !== undefined ? config.settings.links_blank_page : true;
    clientConfig.typeahead_search = config.settings.typeahead_search !== undefined ? config.settings.typeahead_search : true;
    clientConfig.enable_spellchecker = config.settings.enable_spellchecker !== undefined ? config.settings.enable_spellchecker : true;
    app.expose(clientConfig, 'config');
};

exports.setTemplateDir = function (type, req){

    let config = exports.read_config();
    if(type !== 'admin'){
        // if theme selected, override the layout dir
        let layoutDir = config.settings.theme
            ? path.join(__dirname, '../public/themes/', config.settings.theme, '/views/layouts/layout.hbs')
            : path.join(__dirname, '../views/layouts/layout.hbs');
        let viewDir = config.settings.theme ? path.join(__dirname, '../public/themes/', config.settings.theme, '/views') : path.join(__dirname, '../views');

        // set the views dir
        req.app.locals.settings.views = viewDir;
        req.app.locals.layout = layoutDir;
    }else{
        // set the views dir
        req.app.locals.settings.views = path.join(__dirname, '../views/');
        req.app.locals.layout = path.join(__dirname, '../views/layouts/layout.hbs');
    }

};

const getId = function (id){
    // let config = exports.read_config();
    // if(config.settings.database.type === 'embedded'){
    //     return id;
    // }
    // if(id.length !== 24){
    //     return id;
    // }

    let returnID = '';
    try{
        returnID = ObjectID(id);
        return returnID;
    }catch(ex){
        return id;
    }
};

exports.getId = getId;

const sanitizeHTML = exports.sanitizeHTML = function(html){
    // eslint-disable-next-line no-return-assign
    return sanitizeHtml(html, {
        allowedTags: [ 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'p', 'a', 'ul', 'ol',
            'nl', 'li', 'b', 'i', 'strong', 'em', 'strike', 'code', 'hr', 'br', 'div',
            'table', 'thead', 'caption', 'tbody', 'tr', 'th', 'td', 'pre', 'img', 'iframe', 'video', 'source'
        ],
        allowedAttributes: false
    });
};

const replaceVideoAudioEmbedWithHtml = exports.replaceVideoAudioEmbedWithHtml = function(string) {
    
    return string.replace(/!\[(.*?)\]\((.*?)\)/g, (match, alt, src) => {
        const extensions = ["mp4", "mkv", "webm", "avi", "mpeg", "mov", "mp3"];
        const extension = src.split(".").pop();
        console.log('src', src)
        if (extensions.includes(extension)) {
            return `<div class="file-to-proxy" data-url="${src}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd";">${t('Loading media...')}</div>`;
        } else {
            return match;
        }
    });
}

exports.dbQuery = function (db, query, sort, limit, callback) {
	
    let config = exports.read_config();
    //commented this out because we're not using embedded now
	// if(config.settings.database.type === 'embedded'){
    //     if(sort && limit){
    //         db.find(query).sort(sort).limit(parseInt(limit)).exec((err, results) => {
    //             callback(null, results);
    //         });
    //     }else{
    //          db.find(query).exec((err, results) => {
    //         //db.find(query, (err, results) => {
    //             callback(null, results);
    //         });
    //     }
    // }else{
        if(sort && limit){
            db.find(query).sort(sort).limit(parseInt(limit)).toArray((err, results) => {
                callback(null, results);
            });
        }else{
            db.find(query).toArray((err, results) => {
                callback(null, results);
            });
        }
    //}
};

exports.safe_trim = function (str){
    if(str !== undefined){
        return str.trim();
    }
    return str;
};

const getPreferredLang = async function (req) {
    try {
        const API_KEY = process.env.PERF_HUB_API_KEY;
        
        const forwardedEmail = req.headers["x-forwarded-email"] || req.headers["x-forwarded-user"];
        const headers = { 'x-api-key': API_KEY}

        if (!forwardedEmail) return 'en';
        headers['x-user-email'] =  forwardedEmail;
        
        const userPref = await axios.get(
            process.env.PERF_HUB_URL + '/internal/user-preferences/get-user-preferences',
            { headers: headers }
        );

        return userPref?.data?.preferredLanguage ?? 'en'
    } catch (error) {
        console.error('Error fetching user preferences:', error);
        return 'en';
    }
};

const setUserLanguage = async function (_app, _lang) {
    _app.locals.userLanguage = _lang;
}

const processClientLanguages = function (_languages = '') {
    //en-US,en;q=0.9,ja;q=0.8,ru;q=0.7,ko;q=0.6
    const languages = _languages.split(';')
    const prefLanguage = languages[0].split(',')
    
    return prefLanguage[0] === undefined ? 'en' : prefLanguage[0] === 'en-US' ? 'en' : prefLanguage[0]
}
 

const defineMomentLocale = (_lang = 'en') => {

    moment.defineLocale(_lang, {
        months: [
            t('January'),
            t('February'),
            t('March'),
            t('April'),
            t('May'),
            t('June'),
            t('July'),
            t('August'),
            t('September'),
            t('October'),
            t('November'),
            t('December'),
        ],
        monthsShort: [
            t('Jan'),
            t('Feb'),
            t('Mar'),
            t('Apr'),
            t('May'),
            t('Jun'),
            t('Jul'),
            t('Aug'),
            t('Sep'),
            t('Oct'),
            t('Nov'),
            t('Dec'),
        ],
        weekdays: [t('Sunday'), t('Monday'), t('Tuesday'), t('Wednesday'), t('Thursday'), t('Friday'), t('Saturday')],
        weekdaysShort: [t("Sun"), t("Mon"), t("Tue"), t("Wed"), t("Thu"), t("Fri"), t("Sat")],
        weekdaysMin: [t("Su"), t("Mo"), t("Tu"), t("We"), t("Th"), t("Fr"), t("Sa")],
        meridiem: function (hour, minute, isLowercase) {
            if (hour < 12) {
                return t('AM')
            } else {
                return t('PM')
            }
        }
    });

    moment().locale(_lang)
}

exports.initTranslations = async function (req) {
    clear()
    const userLanguage = await getPreferredLang(req) ?? processClientLanguages(req.headers['accept-language']);

    if (userLanguage && userLanguage !== 'en') {
        const file = process.cwd() + '/public/translations/' + userLanguage + '.json'
        await init(file, () => { return userLanguage });
    }

    setUserLanguage(req.app, userLanguage)

    defineMomentLocale(userLanguage)
};

exports.getPreferredLang = getPreferredLang

//exports.setUserLanguage = setUserLanguage

const getLanguagesOptions = async function (req) {
    const userLanguage = req.session.userLanguage ?? processClientLanguages(req.headers['accept-language']);
    const config = loadConfig();
    const languages = [{ value: 'en', label: getLanguageNameFromCode('en', userLanguage) }];

    config.languages.forEach((lang) => {
        languages.push({ value: lang, label: getLanguageNameFromCode(lang, userLanguage) });
    });

    return languages;
}

exports.getLanguagesOptions = getLanguagesOptions;

const loadConfig = () => {
    const config = fs.readFileSync('language.yml', 'utf8');
    return yaml.load(config);
    
}

const getLanguageNameFromCode = (code, langOrigin) => {
    const displayNames = new Intl.DisplayNames([langOrigin || 'en'], { type: 'language' });
    return displayNames.of(code || 'en');
}

exports.translateTitle = async (req, _title) => {
    const languages = await getLanguagesOptions(req);

    const data = [{ 
        phrase: _title, 
        languages: languages.map((lang) => { if (lang.value && lang.value !== null) return lang.value }).filter(Boolean)
    }];

    try {
        const titleTranslations = await axios.post(
            TRANSLATION_API,
            {
                file: Buffer.from(JSON.stringify(data))
            },
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                maxBodyLength: Infinity,
                maxContentLength: Infinity,
                timeout: 5000 // 5-second timeout (5000 milliseconds)
            }
        );

        return titleTranslations.data[0] || null;
    } catch (error) {
        console.error('Translation failed:', error.message);
        return null; // Return null if translation fails
    }
};

getTranslation = async (text, lang) => {
    console.log('translationg', text)
    const data = {
        phrases: [
            {phrase: text, languages: ['en',lang]}
        ]
    }   

    try{
        const res =  await axios.post(TRANSLATION_API, data)

        return res.data[0];
    } catch (error) {
        
        console.log('error in title translation', error)
    }
}

exports.getTranslation = getTranslation;
//gets the countries from list of clubs in Perf Hub API -used in articles Edit
exports.getCountries = async () => {

    const API_URL = process.env.PERF_HUB_URL + '/internal/clubs/list-clubs';
    const API_KEY = process.env.PERF_HUB_API_KEY;

    let countries = [];
    try {
        await axios.get( API_URL, { headers: { 'x-api-key': API_KEY } } )
        .then(function(response){
            let data = response.data;
    
            //code snippet from holiday management
            if(data) {
                countries = data.reduce((acc, club) => {
                    const country = club.localisation.country;
                    if (country !== false && !acc.some(a => a.iso_code === country.iso_code)) {
                        return [...acc, {
                            ...country,
                            // prefer english name if available
                            full_name: country.en_full_name ?? country.full_name,
                        }];
                    }
                    return acc;
                }, []);
                countries = countries.sort((a, b) => a.iso_code.localeCompare(b.iso_code))
            } else {
                console.log('There are no clubs and cannot get countries')
            }
        });
        return countries;
    } catch (error) {
        console.log('Error occured while getting list of countries', error);
        return [];
    }
}

exports.getTopics = async(db, lang = 'en') => {
    try{
        const topics = await db.topics.find({}, {
            projection: {
                topic: 1,
                keyword: 1,
                topicTranslations: 1,
                subtopics: 1,
                topic_regional_visibility: 1
            }
        }).sort({ display_order: 1 })
        .toArray();

        topics.map(topic => {
            topic.topicTranslation = (topic.topicTranslations && topic.topic !== topic.topicTranslations[lang]) ? '('+topic.topicTranslations[lang]+')' : '';

            return topic;
        })

        return topics;
    } catch (err) {
        console.log('Error occured while getting list of topics', err);
        return [];
    }
}

exports.generateSlug = async(db, title, id = null) => {
    let uniqueFlag = false;

    //generate slug from title
    let generatedSlug = slug(title);
    let generatedSlug2 = slug(title);
    let flag = 2; 
    
   
    //check if generated slug is unique
    do {
        const condition = { kb_permalink: generatedSlug2 };
        if(id) {
            condition._id = { $ne: getId(id) };
        }
        
        const count = await db.kb.count(condition);
        console.log('count', count)
        if(count === 0) {
            uniqueFlag = true;
            generatedSlug = generatedSlug2;
        } else {
            generatedSlug2 =  generatedSlug + '-' + flag;
            flag++;
        }
    } while (!uniqueFlag) 

    return generatedSlug;
}


// convert custom markdown to raw markdown
function convertCustomMarkdown(custom_markdown, editor = false) {
    let alertRegex = /\$alert (.*?) (.*?)\n/g;
    let accordionRegex = /\$accordion-start([\s\S]*?)\$accordion-end/g;
    let accordionSearchRegex = /\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end/g;
    let panelRegex = /\$panel-start\n\$title:([\s\S]*?)\n\$body:([\s\S]*?)\$panel-end/g;
    let panelRegexClose = /\$panel-start \$close\n\$title:([\s\S]*?)\n\$body:([\s\S]*?)\$panel-end/g;

    let containerRegex = /\$container-start\n([\s\S]*?)\$container-end/g;
		let customContainerRegex = /\$item:([\s\S]*?)\n\$height:([\s\S]*?)\n\$width:([\s\S]*?)\n\$align:([\s\S]*?)\n/g;
    let imageHTML = '';

    if (editor) {
        containerRegex = /\$container-start<br>\n([\s\S*?])\$container-end/g;
        accordionRegex = /<p>\$accordion-start<br>\n([\s\S]*?)\$accordion-end<\/p>/g;
        accordionSearchRegex = /<p>\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end<\/p>/g;
        panelRegex = /\$panel-start<br>\n\$title:([\s\S]*?)<br>\n\$body:([\s\S]*?)\$panel-end<br>/g;
        customContainerRegex = /\$container-start<br>\n\$item:([\s\S]*?)<br>\n\$height:([\s\S]*?)<br>\n\$width:([\s\S]*?)<br>\n\$align:([\s\S]*?)<br>\n\$container-end<br>\n/g;
        panelRegexClose = /\$panel-start \$close<br>\n\$title:([\s\S]*?)<br>\n\$body:([\s\S]*?)\$panel-end<br>/g;
    }

    custom_markdown = custom_markdown.replace(
        containerRegex, (match, item) => {
            console.log('match',match)
            return `<div class="container">${item}</div>`
        }
        
    );
    // convert image
    custom_markdown = custom_markdown.replace(customContainerRegex,
        (whole, item, height, width, align) => {


            return `<div class="row text-${align}">
                                    <div style="height:${height}; width:${width};">
                                        ${item}
                                    </div>
                                </div>`
        }
    );

    // add alert infos
    custom_markdown = custom_markdown.replace(alertRegex, `<div class="alert alert-$1" role="alert">$2</div>`);

    // add accordion
    custom_markdown = custom_markdown.replace(
        accordionRegex,
        `<div class="panel-group grey accordions mb-4">$1</div>`
    );
    // custom_markdown = custom_markdown.replace(accordionRegex,
    //     `<div class="panel-group grey" id="accordion" role="tablist" aria-multiselectable="true">$1</div>`
    // )

    custom_markdown = custom_markdown.replace(accordionSearchRegex, (match, placeholder) => {
        let place = "Search/Filter Results";
        if (placeholder.length > 1) {
            place = placeholder;
        }

        return `<div class="form-group mr-auto accordion-search">
                    <span class="fa fa-search"></span>
                    <input autofill="off" autocomplete="off" type="text" id="searchAccordion"
                    class="form-control form-control-sm accordion-search-input" placeholder="${place}">
                </div>`;
    });

    // add accordion panels
    custom_markdown = custom_markdown.replace(
        panelRegex,
        (match, title, body, index) =>
            `<div id="accordion-${index}" class="accordion-item">
                <div class="card">
                    <div class="card-header accordion-header">
                        <a class="card-link" data-toggle="collapse" href="#collapse-${index}"  aria-expanded="true">
                            ${title}
                        </a>
                    </div>
                    <div id="collapse-${index}" class="collapse show" data-parent="#accordion-${index}">
                        <div class="card-body">
                            ${body}
                        </div>
                    </div>
                </div>
            </div>`
    );

    custom_markdown = custom_markdown.replace(
        panelRegexClose,
        (match, title, body, index) =>
            `<div id="accordion-${index}">
                <div class="card">
                    <div class="card-header accordion-header">
                        <a class="card-link" data-toggle="collapse" href="#collapse-${index}">
                            ${title}
                        </a>
                    </div>
                    <div id="collapse-${index}" class="collapse" data-parent="#accordion-${index}">
                        <div class="card-body">
                            ${body}
                        </div>
                    </div>
                </div>
            </div>`
    );


    return custom_markdown;
}

function replaceTemporaryMarkdown(rawString) {
    let accordionList = [];
    let containerList = [];

    //replace accordion markdown with a temporary markdown
    let string = rawString.replace(/\$accordion-start([\s\S]*?)\$accordion-end/g, (_, rawAccordionString) => {
        const accordionString = convertCustomMarkdown(rawAccordionString);
        accordionList = [...accordionList, accordionString];
        return "{{-- ACCORDION --}}";
    });

    string = string.replace(/\$container-start([\s\S]*?)\$container-end/g, (whole, rawContainerString) => {
        console.log('adfasdf',  rawContainerString)
        const containerString = convertCustomMarkdown(whole);
        containerList = [...containerList, containerString];
        return "{{-- CONTAINER --}}";
    });

    return { string, accordionList, containerList };
}


function createTemporaryMarkdown(string, accordionList, containerList) {
    let accordionIndex = 0;
    let containerIndex = 0;

    //replace accordion
    string = string.replace(/\{\{-- ACCORDION --\}\}/g, () => {
        const accordionString = accordionList[accordionIndex];
        accordionIndex += 1;
        return accordionString;
    });

    //replace container
			string = string.replace(/\{\{-- CONTAINER --\}\}/g, () => {
                const containerString = containerList[containerIndex];
                containerIndex += 1;
                return containerString;
                    });
                

    return string;
}

function convertTextAreaToMarkdown(body) {
    let { string, accordionList, containerList } = replaceTemporaryMarkdown(body);

    // replace ![]() that are videos with video elements
    string = string.replace(/!\[(.*?)\]\((.*?)\)/g, (match, alt, src) => {
        const extensions = ["mp4", "mkv", "webm", "avi", "mpeg", "mp3"];
        const extension = src.split(".").pop();
        if (extensions.includes(extension)) {
            return `<div class="file-to-proxy" data-url="${src}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd";">${t('Loading media...')}</div>`;
        } else {
            return match;
        }
    });


    var html = markdownit.render(string);

    // add responsive images and tables	
    var fixed_html = html.replace(/<img/g, "<img class='img-fluid'");
    //var fixed_html = html;
    fixed_html = fixed_html.replace(/<table/g, "<table class='table table-hover' ");


    // fixed_html = fixed_html.replace(/<img[^>]+>/im, function (match) {
    //     const width = match.match(/width="([^"]*)"/);

    //     const height = match.match(/height="([^"]*)"/);

    //     //return `<div class="media" style="${width ? `width: ${width[1]}px` : ''}">${match}</div>`;
    //     return `<div class="uploaded-media" style="${width ? `width: ${width[1]}px` : ''}">${match}</div>`;
    // });

    let alertRegex = /\$alert (.*?) (.*?)\n/g;
        fixed_html = fixed_html.replace(alertRegex, `<div class="alert alert-$1" role="alert">$2</div>`);

    //replace temporary markdown with permanent html
    fixed_html = createTemporaryMarkdown(fixed_html, accordionList, containerList);
    //fixed_html = addContainerString(fixed_html, containerList);

    // var cleanHTML = sanitizeHtml(fixed_html, {
    //     allowedTags: [
    //         "h1",
    //         "h2",
    //         "h3",
    //         "h4",
    //         "h5",
    //         "h6",
    //         "blockquote",
    //         "p",
    //         "a",
    //         "ul",
    //         "ol",
    //         "nl",
    //         "li",
    //         "b",
    //         "i",
    //         "strong",
    //         "em",
    //         "strike",
    //         "code",
    //         "hr",
    //         "br",
    //         "div",
    //         "table",
    //         "thead",
    //         "caption",
    //         "tbody",
    //         "tr",
    //         "th",
    //         "td",
    //         "pre",
    //         "img",
    //         "iframe",
    //         "video",
    //         "source",
    //     ],
    //     allowedAttributes: false,
    //     allowedStyles: { '*': {} },
    // });

    
    return fixed_html
}



exports.convertBodyToHtml = (body) => {
    let kbBody = replaceVideoAudioEmbedWithHtml(body);

    kbBody = sanitizeHTML(markdownit.render(kbBody));
    console.log('kbBody', kbBody)
    kbBody = convertTextAreaToMarkdown(kbBody)
    
    kbBody = kbBody.replace(/<img(.*?)class='img-fluid'(.*?)[^>]+>/gm, function (match) {
        const width = match.match(/width="([^"]*)"/);
        const height = match.match(/height="([^"]*)"/);
        console.log('match', match)
        return `<div class="uploaded-media" style="${width ? `max-width: ${width[1]}px;` : ''}${height ? `max-height: ${height[1]}px;` : ''}"><span>Loading Media...</span>${match}</div>`;
    });

    let accordionSearchRegex = /\$accordion-search-start\n\$placeholder:([\s\S]*?)\$accordion-search-end/g;

    kbBody = kbBody.replace(accordionSearchRegex, (match, placeholder) => {
        console.log('placeholder for regex match', match)
        let place = t("Search/Filter Results");
        if (placeholder.length > 1) {
            place = placeholder;
        }

        return `<div class="form-group mr-auto accordion-search">
                    <span class="fa fa-search"></span>
                    <input autofill="off" autocomplete="off" type="text" id="searchAccordion"
                    class="form-control form-control-sm accordion-search-input" placeholder="${place}">
                </div>`;
    });

    kbBody = kbBody.replace(/<div ([\s\S]*?) class="media-file-uploaded" data-url="([\s\S]*?)">([\s\S]*?)<\/div>/, (match, div, url, content) => {
        return `<div class="file-to-proxy" data-url="${url}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd">Loading media...</div>`
    })

    kbBody = kbBody.replace(/<div ([\s\S]*?) class="media-file-error" data-url="([\s\S]*?)">([\s\S]*?)<\/div>/, (match, div, url, content) => {
        return `<div class="file-to-proxy" data-url="${url}" style="width: 100%; text-align: center; padding: 2rem; background: #ddd">Loading media...</div>`
    })

    // kbBody = kbBody.replace('<p>', '')
    // kbBody = kbBody.replace('</p>', '')


    return kbBody;
}

exports.convertBodyToMarkdown = (body) => {
    const dom = new jsdom.JSDOM();
    const  converter = new showdown.Converter();
    //converter.setOption('simpleLineBreaks', true);
    let md = converter.makeMarkdown(body, dom.window.document);

   

    md = md.replace(/<div class="form-group mr-auto accordion-search"><\div>/g, (match) => {
        const replacement = [];
        replacement.push('$accordion-search-start');
    })

    md = md.replace(/<img[^>]+>/im, function (match) {
        const alt = match.match(/alt="([^"]*)"/);
        const src = match.match(/src="([^"]*)"/);
        const width = match.match(/width="([^"]*)"/);
        const height = match.match(/height="([^"]*)"/);

        return `![${alt[1]}](${src[1]} =${width[1]}x${height[1]})`;
    });
    
    //image and placeholder conversion to markdown
    md = md.replace(/<div class="uploaded-media" (.*?)><span>Loading media...<\/span>(.*?)<\/div>/g, (match, div, img) => {
        console.log('match', match)
        console.log('div', div)
        console.log('img', img)
        return img
    })

    //alert to markdown
    md = md.replace(/<div class="alert alert-(.*?)" role="alert">(.*?)<\/div>/g, (match, className, content) => {
        return `$alert ${className} ${content.replace('<p></p>', '')}\n`
    })

    //media to markdown
    md = md.replace(/<div class="media">(.*?)<\/div>/g, (match, img) => {
        console.log('match', match)
        console.log('img', img)
        return img
    })

    
    //video to markdown
    md = md.replace(/<div class="file-to-proxy" data-url="(.*?)" (.*?)>Loading media...<\/div>/g, (match, url, others) => {
        return `![](${url})`
    })

    /**
     * 
<div class="form-group mr-auto accordion-search"><span class="fa fa-search"></span><input autofill="off" autocomplete="off" type="text" id="searchAccordion" class="form-control form-control-sm accordion-search-input" placeholder=" test
"></div>

<div id="accordion-325" class="accordion-item"><div class="card"><div class="card-header accordion-header"><a class="card-link" data-toggle="collapse" href="#collapse-325" aria-expanded="true"> Add panel title </a></div><div id="collapse-325" class="collapse show" data-parent="#accordion-325"><div class="card-body"><p></p><p>Add panel body</p><p></p></div></div></div></div>
     */

    md = md.replace(/<div class="form-group mr-auto accordion-search"><span class="fa fa-search"><\/span><input autofill="off" autocomplete="off" type="text" id="searchAccordion" class="form-control form-control-sm accordion-search-input" placeholder="([\s\S]*?)"><\/div>\n\n<div id="(.*?)" class="accordion-item"><div class="card"><div class="card-header accordion-header"><a class="card-link" data-toggle="collapse" href="#(.*?)" aria-expanded="true">([\s\S]*?)<\/a><\/div><div id="(.*?)" class="collapse show" data-parent="#(.*?)"><div class="card-body"><p>([\s\S]*?)<\/p><\/div><\/div><\/div><\/div>/g, 
    (match, placeholder, id1, id2, title, id3, id4, body) => {
        body = body.replace('<p>', '')
        body = body.replace('</p>', '')
        return `$accordion-start\n$accordion-search-start\n$placeholder:${placeholder}\n$accordion-search-end\n$panel-start\n$title: ${title}\n$body:\n\n${body}\n\n$panel-end\n$accordion-end`
    })


    md = md.replace(/<div class="container"><div class="row text-([\s\S]*?)"><div style="height:([\s\S]*?); width:([\s\S]*?);">([\s\S]*?)<\/div><\/div><\/div>/g, (match, align, height, width, item) => {
        return `$container-start\n$item:${item}\n$height:${height}\n$width:${width}\n$align:${align}\n$container-end`
    
    })

    md = md.replace('<p>', '')
    md = md.replace('</p>', '')
    
    return md
}

/**
 * $accordion-start
$accordion-search-start
$placeholder:
$accordion-search-end
$panel-start
$title: Add panel title
$body:

Add panel body

$panel-end
$accordion-end
 */