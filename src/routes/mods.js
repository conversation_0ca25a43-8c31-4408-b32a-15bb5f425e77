const express = require('express');
const moment = require('moment');
const common = require("./common");
const { getDb } = require('../lib/db');
const {adminGetTopicDetails} = require('./services/admin/getTopicDetailsByIdService');

const router = express.Router();


//feedback comments
router.post("/comments", common.restrict, async (req, res) => {
	// only allow logged-in users
	if (!req.session.user) {
			console.log('Must be logged in')
			return;
	}

	const db = await getDb();

	// get the new settings
	let { articleId, comment } = req.body;
	
	//find the comments first
	db.comments.findOne({user: req.session.user, articleId: articleId}, (err, foundEntry) => {
		if (err) {
			console.log('Error while finding Comment');
			return res.status(500).json({ message: `Error while finding Comment` });
		}

		if (foundEntry) {
			//update comment
			console.log('   ----> Updating Comment');
			
			db.comments.update(
				{ _id: common.getId(foundEntry._id) },
				{
					$set: {
						comment: comment,
						updatedAt: new Date()
					},
				},
				{},
				(err, numReplaced) => {
					if (err) {
						console.error("Failed to save changes: " + err);
						return res.status(400).json({ message: "fail" });
					} else {
						console.error("Changes saved! : ", numReplaced);
						return res.status(200).json({ message: "success" });
					}
				}
			);
    		
		} else {
			//insert new 
			console.log('   ----> Inserting new feedback');
			let newEntry = {
				user: req.session.user,
				articleId,
				comment,
				createdAt: new Date(),
				updatedAt: new Date(),
			};

			db.comments.insert(newEntry, (err, comment) => {
				if (err) {
					console.error("Failed to save new feedback: " + err);
					return res.status(400).json({ message: "fail" });
				} else {
					console.log('Inserted new feedback', comment);
					return res.status(200).json({ message: "success" });
				}
			});
		}
	});
});

router.post("/helpfulornot", common.restrict, async (req, res) => {
	// only allow logged-in users
	if (!req.session.user) {
			console.log('Must be logged in')
			return;
	}
	const db = await getDb();

	// get the new settings
	let dt = req.body;
	console.log('helpfulornot', dt.helpful);

/*
the schema
_id
userId
articleId
datetime
helpful
*/
	
	//find the article first
	db.helpfulornot.findOne({user: req.session.user, articleId: dt.articleId}, (err, foundEntry) => {
		if (err) {
			console.log('Error while finding Article');
			return res.status(500).json({ message: `Error while finding Article` });
		}

		if (foundEntry) {
			//update helpful or not
			console.log('   ----> Updating feedback');
			
			db.helpfulornot.update(
				{ _id: common.getId(foundEntry._id) },
				{
					$set: {
						helpful: dt.helpful,
						updatedAt: new Date()
					},
				},
				{},
				(err, numReplaced) => {
					if (err) {
						console.error("Failed to save changes: " + err);
						return res.status(400).json({ message: "fail" });
					} else {
						console.error("Changes saved! : ", numReplaced);
						return res.status(200).json({ message: "success" });
					}
				}
			);
    		
		} else {
			//insert new 
			console.log('   ----> Inserting new feedback');
			let newEntry = {
				user: req.session.user,
				articleId: dt.articleId,
				createdAt: new Date(),
				updatedAt: new Date(),
				helpful: dt.helpful
			};

			db.helpfulornot.insert(newEntry, (err, helpfulornot) => {
				if (err) {
					console.error("Failed to save new feedback: " + err);
					return res.status(400).json({ message: "fail" });
				} else {
					console.log('Inserted new feedback', helpfulornot);
					return res.status(200).json({ message: "success" });
				}
			});
		}

	});
  
});

//retrieves all comments
router.get('/comments/all/:daterange?', common.restrict, async (req, res) => {
	// only allow admin
	if (req.session.is_admin !== "true") {
			return res.status(401).json({ message: "Access Denied" });
	}
	
	const db = await getDb();
	let dateFrom = null;
	let dateTo = null;

	let query = {};

	if (req.params.daterange) {
		console.log(`comments Params: `, req.params.daterange);
		
		let splittedDateRange = req.params.daterange.split('to');
		
		dateFrom = new Date(splittedDateRange[0]);
		dateTo = new Date(splittedDateRange[1]);
	
		query = {
			updatedAt: {
				$gte: dateFrom,
				$lte: dateTo
			}
		}
	}

	/*
		2021 Notes: nedb does not support aggregation so we'll just get all data based on date range then work it out in frontend
		TODO: we're now using mongodb, so we can use aggregation here instead of just getting all data
	*/
	common.dbQuery(db.comments, query, { date: 1 }, null, (err, results) => {
		if (err) {
			console.log('Error while getting comments', err);
			return res.status(200).json({ message: "fail", data: null });
		}
		console.log('comments results: ', results.length);
		//format the date here
		if (results) {
			results.forEach(c => {
				c.updatedAtFormatted = new moment(c.updatedAt).format('Do [of] MMMM YYYY');
			});
		}
		return res.status(200).json({ message: "success", data: results });
	});
});

//retrieves all pageviews
router.get('/pageviews/all/:from?/:to?', common.restrict, async (req, res) => {
	// only allow admin
	if (req.session.is_admin !== "true") {
			return res.status(401).json({ message: "Access Denied" });
	}
	
	const db = await getDb();
	
	const query = {
		datetime: {
			$gte: req.params.from ? new Date(parseInt(req.params.from)) : new Date(0),
			$lte: req.params.to ? new Date(parseInt(req.params.to)) : new Date()
		}
	};
	
	/*
		2021 Notes: nedb does not support aggregation so we'll just get all data based on date range then work it out in frontend
		2025 TODO: we're now using mongodb, so we can use aggregation here instead of just getting all data
	*/
	common.dbQuery(db.pageviews, query, { date: 1 }, null,
		// db.pageviews.find(query).sort({ date: 1 }).exec(
		async (err, results) => {
		if (err) {
			console.log('Error while getting Pageviews', err);
			return res.status(200).json({ message: "fail", data: null });
		}

		//process region
		// check if session has user regions
		if (req.session.user_regions) {
			const articleIds = results.map(view => view.articleId).filter((val, index, array) => {
				return array.indexOf(val) === index;
			});

			const regionsQuery = [{kb_regional_visibility: ''}, {kb_regional_visibility: null} ];

			req.session.user_regions.map(r => r.country).forEach(country => {
				const regExpr = new RegExp(".*" + country + ".*", "i");
				regionsQuery.push({ kb_regional_visibility: {$regex: regExpr} });
			})

			const articlesOfRegion = await db.kb.find({ _id: { $in: articleIds }, $or: regionsQuery }).toArray();

			results = results.filter(view => {
				const index =  articlesOfRegion.findIndex(a => a._id === view.articleId);
				return index !== -1
			})
		}

		return res.status(200).json({ message: "success", data: results });
	});
});

//retrieves all searches
router.get('/searches/all/:daterange?', common.restrict, async (req, res) => {
	// only allow admin
	if (req.session.is_admin !== "true") {
			return res.status(401).json({ message: "Access Denied" });
	}
	
	const db = await getDb();
	let dateFrom = null;
	let dateTo = null;

	let query = {};

	if (req.params.daterange) {
		console.log(`Searches Params: `, req.params.daterange);
		
		let splittedDateRange = req.params.daterange.split('to');
		
		dateFrom = new Date(splittedDateRange[0]);
		dateTo = new Date(splittedDateRange[1]);
	
		query = {
			datetime: {
				$gte: dateFrom,
				$lte: dateTo
			}
		}
	}
	
	/*
		2021 Notes: nedb does not support aggregation so we'll just get all data based on date range then work it out in frontend
		2025 TODO: we're now using mongodb, so we can use aggregation here instead of just getting all data
	*/
	common.dbQuery(db.searchtotals, query, { date: 1 }, null,

	// db.searchtotals.find(query).sort({ date: 1 }).exec(
			(err, results) => {
		if (err) {
			console.log('Error while getting searchtotals', err);
			return res.status(200).json({ message: "fail", data: null });
		}
		console.log('Searches results: ', results.length);
		return res.status(200).json({ message: "success", data: results });
	});


});

//retrieves all feedbacks
router.get('/feedbacks/all/:daterange?', common.restrict, async (req, res) => {
	// only allow admin
	if (req.session.is_admin !== "true") {
			return res.status(401).json({ message: "Access Denied" });
	}
	
	const db = await getDb();
	let dateFrom = null;
	let dateTo = null;

	let query = {};

	if (req.params.daterange) {
		console.log(`Feedbacks Params: `, req.params.daterange);
		
		let splittedDateRange = req.params.daterange.split('to');
		
		dateFrom = new Date(splittedDateRange[0]);
		dateTo = new Date(splittedDateRange[1]);
	
		query = {
			updatedAt: {
				$gte: dateFrom,
				$lte: dateTo
			}
		}
	}
	
	/*
		2021 Notes: nedb does not support aggregation so we'll just get all data based on date range then work it out in frontend
		2025 TODO: we're now using mongodb, so we can use aggregation here instead of just getting all data
	*/
	common.dbQuery(db.helpfulornot, query, { date: 1 }, null, (err, results) => {
		if (err) {
			console.log('Error while getting feedbacks', err);
			return res.status(200).json({ message: "fail", data: null });
		}
		if (results) {
			console.log('Feedbacks results ', results.length);
			return res.status(200).json({ message: "success", data: results });
		} else {
			console.log('Feedbacks has no results');
			return res.status(200).json({ message: "success", data: [] });
		}
	});
});

// ----------------------- TOPICS ---------------------------------------------------
router.get("/article/topic_settings", common.restrict, (req, res, next) => {
    common.config_expose(req.app);

    //get the countries first before showing page
    common.getCountries().then((countries) => {
        //include the countries in the result
        let countriesOnly = [];
        countries.forEach((c) => {
            countriesOnly.push(c.full_name);
        });

        res.render("topics_subtopics", {
            title: "Topic Settings",
            user_page: true,
            countries: countriesOnly,
            config: config,
            session: req.session,
            current_url: req.protocol + "://" + req.get("host") + req.app_context,
            fullUrl: req.protocol + "://" + req.get("host") + req.originalUrl,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            helpers: req.handlebars,
            show_footer: "show_footer",
        });
    });
});

router.get("/topic_details/:id", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();

    db.topics.findOne({ _id: parseInt(req.params.id) }, (err, topic) => {
        if (err) {
            console.error("Failed to get details: " + err);
            return res.status(400).json({ message: "fail" });
        } else {
            return res.status(200).json({ message: "success", data: topic });
        }
    });
});

router.get("/topic/:id/details", common.restrict, async (req, res) => {
	// only allow admin
	if (req.session.is_admin !== "true") {
			res.render("error", {
					message: "Access denied",
					helpers: req.handlebars,
					config: config,
			});
			return;
	}

	const db = await getDb();
	const topic = await adminGetTopicDetails(req.params.id, db);
	return res.status(200).json({ message: "success", data: topic });
});



router.get("/topics_list/:show?", common.restrict, async (req, res) => {
	const db = await getDb();

	//the selected club id in perfhub
	let showAll = req.params.show;
	let search_club = req.session.clubId;

	if (showAll == "all") {
		//called in topic settings

		//show all topics
		common.dbQuery(db.topics, {}, { display_order: 1 }, 99999, 
			function (err, topics) {
				if (err) {
					return res.status(400).json({ message: "fail" });
				} else {
					return res.status(200).json({ message: "success", data: topics });
				}
			});
	} else {
		//used in Articles Regional Visibitity tagging
		//check if there are clubs
		if (search_club && search_club !== "") {
			
			//get the country of selected club before doing the actual query
			common.getCountryOfClub(search_club).then((country) => {
				console.log(' ---> TOPICS Search for country: ' + country)
				let regExpr = new RegExp(".*" + country + ".*", "i");
				
				common.dbQuery(db.topics, {
					$or: [{ topic_regional_visibility: "" }, { topic_regional_visibility: { $regex: regExpr } }],
					enabled: "true",
				}, { display_order: 1 }, null,

					// db.topics
					//     .find({
					//         $or: [{ topic_regional_visibility: "" }, { topic_regional_visibility: { $regex: regExpr } }],
					//         enabled: "true",
					//     })
					//     .sort({ display_order: 1 })
					// 	.exec(
					function (err, topics) {
						if (err) {
							console.log(err);
							return res.status(400).json({ message: "fail" });
						} else {
							return res.status(200).json({ message: "success", data: topics });
						}
					});
			});
		} else {
			//console.log(' ---> TOPICS Search without country');
		
			if(req.session.is_pseudoadmin) {
		
				const topicQuery = [{ topic_regional_visibility: "" }];
				req.session.user_regions.forEach(r => {
					const regExpr = new RegExp(".*" + r.country + ".*", "i");
					topicQuery.push({ topic_regional_visibility: { $regex: regExpr } });
				})
				console.log('topicquery',topicQuery)
					
					common.dbQuery(db.topics, {
						$or: topicQuery,
						enabled: "true",
					}, { display_order: 1 }, null,
	
						// db.topics
						//     .find({
						//         $or: [{ topic_regional_visibility: "" }, { topic_regional_visibility: { $regex: regExpr } }],
						//         enabled: "true",
						//     })
						//     .sort({ display_order: 1 })
						// 	.exec(
						function (err, topics) {
							if (err) {
								console.log(err);
								return res.status(400).json({ message: "fail" });
							} else {
								return res.status(200).json({ message: "success", data: topics });
							}
						});
			} else {
				common.dbQuery(db.topics, { enabled: "true" }, { display_order: 1 }, null,

				// db.topics
				//     .find({ topic_regional_visibility: "", enabled: "true" })
				//     .sort({ display_order: 1 })
				// 	.exec(
				function (err, topics) {
					if (err) {
						return res.status(400).json({ message: "fail" });
					} else {
						return res.status(200).json({ message: "success", data: topics });
					}
				});
			}
			
		}
	}
});

router.post("/update_topic", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();

    // get the new settings
    let topicsData = req.body;
    let topicId = parseInt(req.body._id);
		console.log('topicdata', topicsData)
    db.topics.findOne({ _id: topicId}, async (err, topic) => {
        // update our old doc
				
				const subTopics  = [];
				for(const s of JSON.parse(topicsData.subtopics)){
					subTopics.push({
						id: s.id,
						subtopic: s.subtopic,
						subtopicTranslations: await common.translateTitle(req, s.subtopic),
					})
				}
				
        db.topics.update(
            { _id: topicId },
            {
                $set: {
                    topic: topicsData.topic,
										topicTranslations: await common.translateTitle(req, topicsData.topic),
                    svg: topicsData.svg,
                    enabled: topicsData.enabled,
                    keyword: topicsData.keyword,
                    topic_regional_visibility: topicsData.topic_regional_visibility,
                    display_order: parseInt(topicsData.display_order),
                    subtopics: subTopics,
                },
            },
            {},
            (err, numReplaced) => {
                if (err) {
                    console.error("Failed to save changes: " + err);
                    return res.status(400).json({ message: "fail" });
                } else {
                    console.error("Changes saved! : ", numReplaced);
                    return res.status(200).json({ message: "success" });
                }
            }
        );
    });
});

router.post("/create_topic", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();

    // get the new settings
    let topicsData = req.body;

		const subTopics  = [];
		for(const s of JSON.parse(topicsData.subtopics)){
			subTopics.push({
				id: s.id,
				subtopic: s.subtopic,
				subtopicTranslations: await common.translateTitle(req, s.subtopic),
			})
		}

    let newTopic = {
        topic: topicsData.topic,
				topicTranslations: await common.translateTitle(req, topicsData.topic),
        svg: topicsData.svg,
        enabled: topicsData.enabled,
        keyword: topicsData.keyword,
        topic_regional_visibility: topicsData.topic_regional_visibility,
        display_order: parseInt(topicsData.display_order),
        subtopics: subTopics,
        _id: 0,
    };

    //get the last entry to get the ID to be used in the next entry
		try{
			const lastEntry = await db.topics.find().sort({ _id: -1 }).limit(1).toArray();
			newTopic._id = lastEntry[0] ? parseInt(lastEntry[0]._id) + 1 : 1;
			
			db.topics.insert(newTopic, (err, topic) => {
				if (err) {
					console.error("Failed to save new topic: " + err);
					return res.status(400).json({ message: "fail" });
				} else {
					return res.status(200).json({ message: "success" });
				}
			});
		} catch (err) {
			console.log('Error while getting last entry', err)
		}
});

router.post("/delete_topic/:id", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();

    // remove the article
    db.topics.remove({ _id: parseInt(req.params.id) }, {}, (err, numRemoved) => {
        if (err) {
            console.error("Failed to delete topic: " + err);
            return res.status(400).json({ message: "fail" });
        } else {
            console.error("Topic Deleted! : ", numRemoved);
            return res.status(200).json({ message: "success" });
        }
    });
});



module.exports = router;