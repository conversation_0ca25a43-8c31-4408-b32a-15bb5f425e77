const common = require('../../common');

exports.adminGetTopicDetails = async (id, db) => {

  const possibleContries = await common.getCountries();


  const topic = await db.topics.findOne({ _id: parseInt(id) });

  if (topic) {
    const candidateRegions = [];
    
    //remove regions/countries that are not part of the list retrieved from PH
    topic.topic_regional_visibility.split(/(?:,)\s*/i).forEach(r => {
      if (possibleContries.findIndex(c => c.full_name === r.trim()) !== -1) {
        candidateRegions.push(r)
      }

    })

    topic.topic_regional_visibility = candidateRegions.join(', ');
  }

  return topic;
}