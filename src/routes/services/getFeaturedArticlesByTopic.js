const common = require('../common');

exports.getFeaturedArticlesByTopicId = async (topicId, subtopicId = null, region, featuredCount = 4, db) => {
  const regExpr = new RegExp(".*" + region + ".*", "i");

  const articlesFilter = {
    '$or': [{ kb_regional_visibility: "" }, { kb_regional_visibility: { $regex: regExpr } }],
    kb_published: "true",
    kb_pinned: "true",
    '$or': [{kb_pinned_topic: parseInt(topicId)}, {kb_pinned_topic: topicId}],
    kb_versioned_doc: { $ne: true },
    kb_featured: "true"
  }
  const sortBy = { kb_published_date: -1 };

  if (subtopicId) {
    articlesFilter.kb_pinned_subtopic = parseInt(subtopicId);
  }
  console.log(articlesFilter)

  const articles = db.kb.find(articlesFilter).sort(sortBy).limit(featuredCount).toArray();

  return articles;
}