const { getDb } = require('../../lib/db');

exports.getTopicDetails = async (topicId, region) => {
    try {
        const db = await getDb();
        const regExpr = new RegExp('.*' + region + '.*', 'i');

        const topic = await db.topics.findOne({
            _id: parseInt(topicId),
            $or: [
                { topic_regional_visibility: '' }, // Global visibility
                { topic_regional_visibility: { $regex: regExpr } },
            ],
        });

        return topic;
    } catch (error) {
        console.error('Error fetching topic details:', error);
        return null;
    }
};
