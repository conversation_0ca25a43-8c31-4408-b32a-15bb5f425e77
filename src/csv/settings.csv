en
Topic Settings
Website title
The title of your website
Website description
A short website description when listing the homepage URL in search engines
Show website logo
"Controls whether to show the ""Website title"" text or a logo located: ""/public/logo.png"" (by default)"
Website context/base URL
Allows for the website to be run from a non root path. Eg: http://127.0.0.1:4444/openkb/
Allow API access
Whether to allow API access to insert articles - See documentation for further information
API access token
Requires "Allow API access" to be set to "true". The value is the access token required to access the public API. Please set to a hard to guess value
Password protect
"Setting to ""true"" will require a user to login before viewing any pages"
Index article body
Whether to add the body of your articles to the search index (requires restart)
Select a theme
The theme to use for public facing pages. Leave blank for default
Select a language
The language to use for public facing pages. Leave blank for default (English)
Show logon link
"Whether to show/hide the logon link in the top right of screen"
Date format
Sets the global date formatting. Uses moment.js date formatting, see more here: http://momentjs.com/docs/#/displaying
Article suggestions allowed
If enabled non authenticated users can submit article suggestions for approval
Google analytics code
Adds Google Analytics to public facing pages. Include the entire code from Google including the <script> tags
Allow voting
Whether to allow users to vote on an article
Show article meta data
"Whether to show article meta data including published date, last updated date, author etc"
Show author email
"Controls whether the authors email address is displayed in the meta. Requires ""Show article meta data"" to be true"
Article links open new page
Controls whether links within articles open a new page (tab)
Add header anchors
Whether to add HTML anchors to all heading tags for linking within articles or direct linking from other articles
Enable editor spellchecker
Controls whether to enable the editor spellchecker
Allow article versioning
Whether to track article versions with each save of the editor
Allow Mermaid
"Whether to enable <a href=""http://knsv.github.io/mermaid/"" target=""_blank"">mermaid</a>"
Allow MathJax
"Whether to enable <a href=""https://www.mathjax.org/"" target=""_blank"">MathJax</a>"
"Configure the mode MathJax operates in - <a href=""http://docs.mathjax.org/en/latest/configuration.html#configuring-mathjax"" target="_blank">Configure MathJax</a>"
MathJax input mode
Number of top articles shown on homepage
Sets the number of results shown on the home page
Show published date
Shows the published date next to the results on the homepage and search
Show view count
Shows the view count next to the results on the homepage and search
Update view count when logged in
Updates the view count also when users are logged in
Show featured articles
Whether to show any articles set to featured in a sidebar
Show featured articles when viewing article
Whether to show any articles set to featured in a sidebar when viewing an article
Featured article count
The number of featured articles shown
You can add simple styling to various elements of your website below:
Header background color (HEX):
Header text color (HEX):
Footer background color (HEX):
Footer text color (HEX):
Button background color (HEX):
Button text color (HEX):
Link color (HEX):
Page text color (HEX):
Page font:
Topics and Sub-topics Management
Topics list
Topic details
Sub-topics
Regional Visibility
Topics without any regions configured will be globally visible
Delete Permanently
Select file
Upload file
Upload directory
"To insert an image right click the link, copy the link address and add it to your Markdown"
New directory
"Multiple directories can be created using a ""/"" separator"
new/directory
"You are able to bulk import articles by uploading a Zip file containing files with Markdown content. When new articles are created, the name of the file will become the title/permalink. All imported articles will be set to a draft status."
Select zip
Import zip
New Article
Export CSV
Region
All
Status
Topics
TITLE
Topic
Sub Topic
VISIBILITY
STATUS
ACTIONS