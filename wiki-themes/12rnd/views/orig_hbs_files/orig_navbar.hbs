{{#if session.user}}
<nav class="navbar navbar-default navbar-static-top">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
                <span class="sr-only">{{__ "Toggle navigation"}}</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="{{app_context}}/">{{config.settings.website_title}}</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav navbar-right">
                    {{#if result._id}}
                        <li><a href="{{app_context}}/edit/{{result._id}}">{{__ "Edit"}}</a></li>
                    {{/if}}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Users"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            {{#ifCond session.is_admin '==' 'true'}}
                            <li><a href="{{app_context}}/users/new"><i class="fa fa-user-plus fa-fw"></i>&nbsp; {{__ "New"}}</a></li>
                            <li><a href="{{app_context}}/users"><i class="fa fa-users fa-fw"></i>&nbsp; {{__ "Edit"}}</a></li>
                            {{/ifCond}}
                            <li><a href="{{app_context}}/user/edit/{{session.user_id}}"><i class="fa fa-user fa-fw"></i>&nbsp; {{__ "My account"}}</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Article"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{app_context}}/insert"><i class="fa fa-plus fa-fw"></i>&nbsp; {{__ "New"}}</a></li>
                            <li><a href="{{app_context}}/articles"><i class="fa fa-pencil fa-fw"></i>&nbsp; {{__ "Edit"}}</a></li>
                        </ul>
                    </li>
                    {{#ifCond session.is_admin '==' 'true'}}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Admin"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{app_context}}/settings"><i class="fa fa-cog fa-fw"></i>&nbsp; {{__ "Settings"}}</a></li>
                            <li><a href="{{app_context}}/files"><i class="fa fa-files-o fa-fw"></i>&nbsp; {{__ "Files"}}</a></li>
                            <li><a href="{{app_context}}/import"><i class="fa fa-upload fa-fw"></i>&nbsp; {{__ "Import"}}</a></li>
                            <li><a href="{{app_context}}/export"><i class="fa fa-file-archive-o fa-fw"></i>&nbsp; {{__ "Export"}}</a></li>
                            <li><a href="{{app_context}}/file_cleanup"><i class="fa fa-trash fa-fw"></i>&nbsp; {{__ "Cleanup files"}}</a></li>
                        </ul>
                    </li>
                    {{/ifCond}}
                    {{!-- <li><a href="{{app_context}}/logout">{{__ "Logout"}}</a></li> --}}
            </ul>
        </div>
    </div>
</nav>
{{/if}}