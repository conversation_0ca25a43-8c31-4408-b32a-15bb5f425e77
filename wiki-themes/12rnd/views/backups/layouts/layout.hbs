<!DOCTYPE html>
<html lang="{{config.settings.locale}}">
  <head>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-N9RB97L');</script>
    <!-- End Google Tag Manager -->

    {{#if result.kb_seo_title}}
      <title>Club Wiki - {{result.kb_seo_title}}</title>
      <meta property="og:title" content="Club Wiki - {{result.kb_seo_title}}" />
    {{else}}
      {{#if result.kb_title}}
        <title>Club Wiki - {{result.kb_title}}</title>
        <meta property="og:title" content="Club Wiki - {{result.kb_title}}" />
      {{else}}
        <title>Club Wiki</title>
        <meta property="og:title" content="Club Wiki" />
      {{/if}}
    {{/if}}
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    {{#if result.kb_seo_description}}
      <meta name="description" content="{{result.kb_seo_description}}">
      <meta property="og:description" content="{{result.kb_seo_description}}" />
    {{else}}
      {{#if result.kb_body}}
        <meta name="description" content="{{substring (strip_md result.kb_body) 160}}">
        <meta property="og:description" content="{{substring (strip_md result.kb_body) 160}}" />
      {{else}}
        {{#ifCond homepage '===' true}}
          <meta name="description" content="{{config.settings.website_description}}">
          <meta property="og:description" content="{{config.settings.website_description}}" />
        {{else}}
          <meta name="description" content="{{__ "Powered by"}} openKB">
          <meta property="og:description" content="openKB is an Open Source Node.js Markdown based knowledge base/FAQ/Wiki app" />
        {{/ifCond}}
      {{/if}}
    {{/if}}
    {{#if result.kb_keywords}}
      <meta name="keywords" content="{{result.kb_keywords}}">
    {{else}}
      <meta name="keywords" content="openKB">
    {{/if}}
    <meta property="og:type" content="article" />
    <meta property="og:image" content="{{current_url}}/logo.png" />
    <meta property="og:url" content="{{fullUrl}}" />
    <link rel="stylesheet" href="{{app_context}}/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{app_context}}/simplemde/simplemde.min.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/github.css">
    <link rel="stylesheet" href="{{app_context}}/themes/12rnd/css/style.css">
    <link rel="stylesheet" href="{{app_context}}/themes/12rnd/css/newstyles.css">
    <link rel="stylesheet" href="{{app_context}}/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/mermaid.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/bootstrap-tokenfield.css">
    <link rel="stylesheet" href="{{app_context}}/stylesheets/jpushmenu.css">
    <link rel='icon' href='{{app_context}}/favicon.png' />

    <!--Import Google Icon Font-->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Khula:wght@400;600&display=swap" rel="stylesheet">

    <script src="{{app_context}}/jquery/jquery.min.js"></script>
    <script src="{{app_context}}/themes/12rnd/js/overrides.js"></script>

    <script>{{{state}}}</script>
    <script src="{{app_context}}/bootstrap/js/bootstrap.min.js"></script>
    <script src="{{app_context}}/javascripts/bootstrap-validator.js"></script>
    {{#ifCond config.settings.mermaid '===' true}}
      <script src="{{app_context}}/javascripts/mermaid.min.js"></script>
    {{/ifCond}}
    {{#ifCond config.settings.mathjax '===' true}}
      <script type="text/x-mathjax-config">
        MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']], processEscapes: true}});
      </script>
      <script async src="https://cdn.mathjax.org/mathjax/latest/MathJax.js?config={{config.settings.mathjax_input_mode}}"></script>
    {{/ifCond}}
    <script src="{{app_context}}/javascripts/markdown-it-classy.js"></script>
    <script src="{{app_context}}/javascripts/bootstrap-tokenfield.min.js"></script>
    <script src="{{app_context}}/javascripts/inline-attachment.js"></script>
    <script src="{{app_context}}/javascripts/codemirror.inline-attachment.js"></script>
    <script src="{{app_context}}/javascripts/highlight.min.js"></script>
    <script src="{{app_context}}/simplemde/simplemde.min.js"></script>
    <script src="{{app_context}}/markdown-it/markdown-it.min.js"></script>
    <script src="{{app_context}}/javascripts/jpushmenu.js"></script>
    <script src="{{app_context}}/javascripts/openKB.js"></script>
    {{#ifCond user_page '===' true}}
      {{#if config.settings.google_analytics}}
        {{{config.settings.google_analytics}}}
      {{/if}}
    {{/ifCond}}
  </head>
  <body class="{{show_footer}}">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N9RB97L"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Static navbar -->
    <div class="">
        {{!-- {{> 12rnd/views/partials/navbar}} --}}
        {{{body}}}
    </div>
    <input type="hidden" value="{{app_context}}" id="app_context">
    <input type="hidden" value="{{config.settings.links_blank_page}}" id="blank_links">
    <input type="hidden" id="input_notify_message" value="{{message}}">
    <input type="hidden" id="input_notify_message_type" value="{{message_type}}">
    <div class="clearfix" id="notify_message"></div>
  </body>
</html>
