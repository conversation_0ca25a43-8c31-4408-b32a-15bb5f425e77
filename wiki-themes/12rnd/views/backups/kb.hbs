<div class="row searchBar kb">
    {{!-- <div class="">
        <h1>&nbsp;</h1>
    </div> --}}
    <div class="container-fluid" style="margin-top: 110px;">
        <form action="{{app_context}}/search" id="search_form" method="post">
            <div class="mr-auto ml-auto" style="max-width: 500px;">
                <div class="input-group">
                    <input autofill="off" autocomplete="off" type="text" name="frm_search" id="frm_search"
                    class="form-control input-lg" placeholder="To search, start typing..."
                    {{#if search_results.length}}value="{{search_term}}"{{/if}}>

                    <span class="btn-search-container">
                        <button class="btn btn-default" id="btn_search" type="submit">
                            <i class="fa fa-search" aria-hidden="true"></i>
                        </button>
                        <a href="/openkb/" class="btn btn-default link-home">
                            <i class="fa fa-home" aria-hidden="true"></i>
                        </a>
                    </span>
                </div>
                {{#ifCond config.settings.typeahead_search '===' true}}
                {{!-- <div id="searchResult" class="hidden col-lg-12">
                    <ul class="list-group searchResultList"></ul>
                </div> --}}
                {{/ifCond}}
            </div>
        </form>
    </div>
</div>
<div class="row">

        <div class="d-none d-sm-block col-sm-5 col-md-4 col-lg-3 side-article-bar">
            {{#if config.settings.show_featured_articles}}
                <div class="col-md-12">
                    <div class="panel panel-default" style="margin-top: 30px;">
                        <div class="panel-heading" style="padding: 8px;">Important Guides</div>
                        <div class="panel-body">
                            <ul class="list-group">
                                {{#each featured_results}}
                                    {{#if this.kb_permalink}}
                                        <li class="list-group-item">
                                            <a href="/openkb/{{@root.config.settings.route_name}}/{{this.kb_permalink}}">{{this.kb_title}}</a></li>
                                    {{else}}
                                        <li class="list-group-item">
                                            <a href="/openkb/{{@root.config.settings.route_name}}/{{this._id}}">{{this.kb_title}}</a></li>
                                    {{/if}}
                                {{/each}}
                            </ul>
                        </div>
                    </div>
                </div>
            {{/if}}

            {{#if config.settings.show_kb_meta}}
                <div class="col-md-12 mt-3">
                    <div class="panel panel-primary article-details">
                        <div class="panel-heading" style="padding: 8px;">Details</div>
                        <div class="panel-body metaData" >
                            <!-- <h5 class="col-sm-12 col-lg-12">
                                <strong>Published:</strong> {{format_date result.kb_published_date}}
                            </h5> -->
                            <p class="mt-2 col-sm-12 col-lg-12">
                                <strong>Author:</strong>
                                    {{#if result.kb_last_update_user}}
                                        <span class="text-info">
                                            {{#ifCond config.settings.show_author_email '===' false}}
                                                ({{removeEmail result.kb_last_update_user}})
                                            {{else}}
                                                ({{result.kb_last_update_user}})
                                            {{/ifCond}}
                                        </span>
                                    {{/if}}
                            </p>
                            <p class="col-sm-12 col-lg-12">
                                <strong>Updated:</strong> {{format_date result.kb_last_updated}}
                            </p>

                            <div class="col-sm-12 col-lg-12">
                                <p>
                                    <strong>Other Key Searches:</strong>
                                </p>
                                <p class="kb-key-searches">
                                    {{{split_keywords result.kb_keywords}}}
                                </p>
                            </div>

                        </div>
                    </div>
                </div>
            {{/if}}

        </div>
        <div class="col-xs-12 col-sm-7 col-md-8 col-lg-9 kb-content">
            <h2 style="padding-bottom: 25px;padding-top: 15px;">{{result.kb_title}}</h2>
            <div class="body_text">
                {{{kb_body}}}
            </div>
            {{#if config.settings.allow_voting}}
                <div class="row">
                    <div class="voting text-right col-lg-12">
                        <span>{{__ "Was this article helpful?"}}</span>
                        <button id="btnUpvote" class="btn btn-sm btn-default"><i class="fa fa-thumbs-o-up" aria-hidden="true"></i></button>
                        <button id="btnDownvote" class="btn btn-sm btn-default"><i class="fa fa-thumbs-o-down" aria-hidden="true"></i></button>
                        <span>
                            <strong>{{__ "Votes"}}:</strong>
                            {{#if result.kb_votes}}
                                {{result.kb_votes}}
                            {{else}}
                                0
                            {{/if}}
                        </span>
                    </div>
                </div>
            {{/if}}
        </div>

</div>
