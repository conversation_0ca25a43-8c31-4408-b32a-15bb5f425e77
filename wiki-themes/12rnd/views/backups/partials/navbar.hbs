{{#if session.user}}
<nav class="navbar navbar-expand bg-light navbar-light admin-nav">

    <a class="navbar-brand d-flex align-items-center" href="{{app_context}}/">
        <i class="material-icons mr-2 ml-2">help_outline</i> <span class="menu-font-large"> Wiki: How To Resources</span>
    </a>

    {{!-- <button class="navbar-toggler" type="button" data-toggle="collapse"
        data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup"
        aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button> --}}

    <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
        <ul class="navbar-nav nav-mod">
            {{!-- <li class="nav-item dropdown active ml-4">
                <div class="d-flex align-items-center flex-column">
                    <i class="material-icons">home</i>
                    <a class="nav-link menu-font-small" href="#">
                        Home
                    </a>
                </div>
            </li> --}}

            <li class="nav-item dropdown ml-4 px-2 pt-0">
                <div class="d-flex align-items-center flex-column">
                    <i class="material-icons">event_note</i>
                    <a class="nav-link dropdown-toggle menu-font-small" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-expanded="false">
                        Article
                    </a>
                    <div class="dropdown-menu menu-font-small" aria-labelledby="navbarDropdownMenuLink">
                        <a class="dropdown-item" href="{{app_context}}/insert">New</a>
                        <a class="dropdown-item" href="{{app_context}}/articles">Edit</a>
                    </div>
                </div>
            </li>
            <li class="nav-item dropdown px-2">
                <div class="d-flex align-items-center flex-column">
                    <i class="material-icons">assignment_ind</i>
                    <a class="nav-link dropdown-toggle menu-font-small" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-expanded="false">
                        Admin
                    </a>
                    <div class="dropdown-menu menu-font-small" aria-labelledby="navbarDropdownMenuLink">
                        <a class="dropdown-item" href="{{app_context}}/settings">Settings</a>
                        <a class="dropdown-item" href="{{app_context}}/files">Files</a>
                        <a class="dropdown-item" href="{{app_context}}/import">Import</a>
                        <a class="dropdown-item" href="{{app_context}}/export">Export</a>
                        <a class="dropdown-item" href="{{app_context}}/file_cleanup">Cleanup Files</a>
                        <a class="dropdown-item" href="{{app_context}}/user/edit/{{session.user_id}}">My Account</a>
                        <a class="dropdown-item" href="{{app_context}}/users">Users</a>
                    </div>
                </div>
            </li>


        </ul>
        {{!-- Right aligned account --}}
        {{!-- <div class="d-flex align-items-center dropdown ml-auto">
            <i class="material-icons">account_circle</i>
            <span class="dropdown-toggle menu-font-small" id="navbarDropdownMenuLink"
                role="button" data-toggle="dropdown" aria-expanded="false">
                <small class="ml-1">
                    {{{session.user}}}
                </small>
            </span>
            <div class="dropdown-menu menu-font-small mt-1" aria-labelledby="navbarDropdownMenuLink">
                <a class="dropdown-item" href="#">My Account</a>
                <a class="dropdown-item" href="#">New</a>
                <a class="dropdown-item" href="#">Edit</a>
            </div>
        </div> --}}


    </div>

</nav>



{{!--


<nav class="navbar bg-light">
    <div class="container-fluid">
        <div class="navbar-header">

            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
                <span class="sr-only">{{__ "Toggle navigation"}}</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <a class="navbar-brand" href="{{app_context}}/">
                {{config.settings.website_title}}
            </a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav navbar-right">
                    {{#if result._id}}
                        <li><a href="{{app_context}}/edit/{{result._id}}">{{__ "Edit"}}</a></li>
                    {{/if}}
                    <li class="dropdown">
                        <i class="material-icons">check</i>
                        <span class="glyphicon glyphicon-search" aria-hidden="true"></span>
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Users"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            {{#ifCond session.is_admin '==' 'true'}}
                            <li><a href="{{app_context}}/users/new"><i class="fa fa-user-plus fa-fw"></i>&nbsp; {{__ "New"}}</a></li>
                            <li><a href="{{app_context}}/users"><i class="fa fa-users fa-fw"></i>&nbsp; {{__ "Edit"}}</a></li>
                            {{/ifCond}}
                            <li><a href="{{app_context}}/user/edit/{{session.user_id}}"><i class="fa fa-user fa-fw"></i>&nbsp; {{__ "My account"}}</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Article"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{app_context}}/insert"><i class="fa fa-plus fa-fw"></i>&nbsp; {{__ "New"}}</a></li>
                            <li><a href="{{app_context}}/articles"><i class="fa fa-pencil fa-fw"></i>&nbsp; {{__ "Edit"}}</a></li>
                        </ul>
                    </li>
                    {{#ifCond session.is_admin '==' 'true'}}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Admin"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{app_context}}/settings"><i class="fa fa-cog fa-fw"></i>&nbsp; {{__ "Settings"}}</a></li>
                            <li><a href="{{app_context}}/files"><i class="fa fa-files-o fa-fw"></i>&nbsp; {{__ "Files"}}</a></li>
                            <li><a href="{{app_context}}/import"><i class="fa fa-upload fa-fw"></i>&nbsp; {{__ "Import"}}</a></li>
                            <li><a href="{{app_context}}/export"><i class="fa fa-file-archive-o fa-fw"></i>&nbsp; {{__ "Export"}}</a></li>
                            <li><a href="{{app_context}}/file_cleanup"><i class="fa fa-trash fa-fw"></i>&nbsp; {{__ "Cleanup files"}}</a></li>
                        </ul>
                    </li>
                    {{/ifCond}}
                    {{!-- <li><a href="{{app_context}}/logout">{{__ "Logout"}}</a></li>
            </ul>
        </div>
    </div>
</nav> --}}
{{/if}}