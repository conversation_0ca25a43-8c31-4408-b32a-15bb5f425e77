{{#if session.user}}
<nav class="navbar navbar-expand navbar-light admin-nav"  style="background-color: #f8fafb; height: 75px; padding: 0px;">

    <a class="navbar-brand d-flex align-items-center" href="{{app_context}}/">
        {{!-- <i class="material-icons mr-2 ml-2">help_outline</i>  --}}
        <span class="menu-font-large"> Wiki: How To Resources</span>
    </a>

    {{!-- <button class="navbar-toggler" type="button" data-toggle="collapse"
        data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup"
        aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button> --}}

    <div class="" id="navbarNavAltMarkup">
        <ul class="navbar-nav">
            {{!-- <li class="nav-item dropdown active ml-4">
                <div class="d-flex align-items-center flex-column">
                    <i class="material-icons">home</i>
                    <a class="nav-link menu-font-small" href="#">
                        Home
                    </a>
                </div>
            </li> --}}

            <li class="nav-item dropdown ml-4 px-3 py-2">
                <div class="d-flex align-items-center flex-column">
                    {{!-- <i class="material-icons">event_note</i> --}}
                    <i class="nav-icon">
                        <svg style="width:24px;height:24px" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M19 5V19H5V5H19M21 3H3V21H21V3M17 17H7V16H17V17M17 15H7V14H17V15M17 12H7V7H17V12Z" />
                        </svg>
                    </i>
                    <a class="nav-link dropdown-toggle menu-font-small" href="#" id="navbarDropdownMenuLink"
                        role="button" data-toggle="dropdown" aria-expanded="false" style="padding-bottom: 0px; padding-top: 1px; padding-right: 0px; " >
                        Article
                    </a>
                    <div class="dropdown-menu menu-font-small" aria-labelledby="navbarDropdownMenuLink">
                        <a class="dropdown-item" href="{{app_context}}/insert">New</a>
                        <a class="dropdown-item" href="{{app_context}}/articles">Edit</a>
                    </div>
                </div>
            </li>
            <li class="nav-item dropdown px-3 py-2">
                <div class="d-flex align-items-center flex-column">
                    {{!-- <i class="material-icons">assignment_ind</i> --}}
                    <i class="nav-icon">
                        <svg style="width:24px;height:24px" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M10 4A4 4 0 0 0 6 8A4 4 0 0 0 10 12A4 4 0 0 0 14 8A4 4 0 0 0 10 4M10 6A2 2 0 0 1 12 8A2 2 0 0 1 10 10A2 2 0 0 1 8 8A2 2 0 0 1 10 6M17 12C16.84 12 16.76 12.08 16.76 12.24L16.5 13.5C16.28 13.68 15.96 13.84 15.72 14L14.44 13.5C14.36 13.5 14.2 13.5 14.12 13.6L13.16 15.36C13.08 15.44 13.08 15.6 13.24 15.68L14.28 16.5V17.5L13.24 18.32C13.16 18.4 13.08 18.56 13.16 18.64L14.12 20.4C14.2 20.5 14.36 20.5 14.44 20.5L15.72 20C15.96 20.16 16.28 20.32 16.5 20.5L16.76 21.76C16.76 21.92 16.84 22 17 22H19C19.08 22 19.24 21.92 19.24 21.76L19.4 20.5C19.72 20.32 20.04 20.16 20.28 20L21.5 20.5C21.64 20.5 21.8 20.5 21.8 20.4L22.84 18.64C22.92 18.56 22.84 18.4 22.76 18.32L21.72 17.5V16.5L22.76 15.68C22.84 15.6 22.92 15.44 22.84 15.36L21.8 13.6C21.8 13.5 21.64 13.5 21.5 13.5L20.28 14C20.04 13.84 19.72 13.68 19.4 13.5L19.24 12.24C19.24 12.08 19.08 12 19 12H17M10 13C7.33 13 2 14.33 2 17V20H11.67C11.39 19.41 11.19 18.77 11.09 18.1H3.9V17C3.9 16.36 7.03 14.9 10 14.9C10.43 14.9 10.87 14.94 11.3 15C11.5 14.36 11.77 13.76 12.12 13.21C11.34 13.08 10.6 13 10 13M18.04 15.5C18.84 15.5 19.5 16.16 19.5 17.04C19.5 17.84 18.84 18.5 18.04 18.5C17.16 18.5 16.5 17.84 16.5 17.04C16.5 16.16 17.16 15.5 18.04 15.5Z" />
                        </svg>
                    </i>
                    <a class="nav-link dropdown-toggle menu-font-small" href="#" id="navbarDropdownMenuLink"
                    role="button" data-toggle="dropdown" aria-expanded="false" style="padding-bottom: 0px; padding-top: 1px; padding-right: 0px;" >
                        Admin
                    </a>
                    <div class="dropdown-menu menu-font-small" aria-labelledby="navbarDropdownMenuLink">
                        <a class="dropdown-item" href="{{app_context}}/settings">Settings</a>
                        <a class="dropdown-item" href="{{app_context}}/files">Files</a>
                        <a class="dropdown-item" href="{{app_context}}/import">Import</a>
                        <a class="dropdown-item" href="{{app_context}}/export">Export</a>
                        <a class="dropdown-item" href="{{app_context}}/file_cleanup">Cleanup Files</a>
                        {{!-- <a class="dropdown-item" href="{{app_context}}/user/edit/{{session.user_id}}">My Account</a> --}}
                        {{!-- <a class="dropdown-item" href="{{app_context}}/users">Users</a> --}}
                    </div>
                </div>
            </li>


        </ul>
        {{!-- Right aligned account --}}
        {{!-- <div class="d-flex align-items-center dropdown ml-auto">
            <i class="material-icons">account_circle</i>
            <span class="dropdown-toggle menu-font-small" id="navbarDropdownMenuLink"
                role="button" data-toggle="dropdown" aria-expanded="false">
                <small class="ml-1">
                    {{{session.user}}}
                </small>
            </span>
            <div class="dropdown-menu menu-font-small mt-1" aria-labelledby="navbarDropdownMenuLink">
                <a class="dropdown-item" href="#">My Account</a>
                <a class="dropdown-item" href="#">New</a>
                <a class="dropdown-item" href="#">Edit</a>
            </div>
        </div> --}}


    </div>

</nav>



{{!--


<nav class="navbar bg-light">
    <div class="container-fluid">
        <div class="navbar-header">

            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
                <span class="sr-only">{{__ "Toggle navigation"}}</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <a class="navbar-brand" href="{{app_context}}/">
                {{config.settings.website_title}}
            </a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav navbar-right">
                    {{#if result._id}}
                        <li><a href="{{app_context}}/edit/{{result._id}}">{{__ "Edit"}}</a></li>
                    {{/if}}
                    <li class="dropdown">
                        <i class="material-icons">check</i>
                        <span class="glyphicon glyphicon-search" aria-hidden="true"></span>
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Users"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            {{#ifCond session.is_admin '==' 'true'}}
                            <li><a href="{{app_context}}/users/new"><i class="fa fa-user-plus fa-fw"></i>&nbsp; {{__ "New"}}</a></li>
                            <li><a href="{{app_context}}/users"><i class="fa fa-users fa-fw"></i>&nbsp; {{__ "Edit"}}</a></li>
                            {{/ifCond}}
                            <li><a href="{{app_context}}/user/edit/{{session.user_id}}"><i class="fa fa-user fa-fw"></i>&nbsp; {{__ "My account"}}</a></li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Article"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{app_context}}/insert"><i class="fa fa-plus fa-fw"></i>&nbsp; {{__ "New"}}</a></li>
                            <li><a href="{{app_context}}/articles"><i class="fa fa-pencil fa-fw"></i>&nbsp; {{__ "Edit"}}</a></li>
                        </ul>
                    </li>
                    {{#ifCond session.is_admin '==' 'true'}}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">{{__ "Admin"}} <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{app_context}}/settings"><i class="fa fa-cog fa-fw"></i>&nbsp; {{__ "Settings"}}</a></li>
                            <li><a href="{{app_context}}/files"><i class="fa fa-files-o fa-fw"></i>&nbsp; {{__ "Files"}}</a></li>
                            <li><a href="{{app_context}}/import"><i class="fa fa-upload fa-fw"></i>&nbsp; {{__ "Import"}}</a></li>
                            <li><a href="{{app_context}}/export"><i class="fa fa-file-archive-o fa-fw"></i>&nbsp; {{__ "Export"}}</a></li>
                            <li><a href="{{app_context}}/file_cleanup"><i class="fa fa-trash fa-fw"></i>&nbsp; {{__ "Cleanup files"}}</a></li>
                        </ul>
                    </li>
                    {{/ifCond}}
                    {{!-- <li><a href="{{app_context}}/logout">{{__ "Logout"}}</a></li>
            </ul>
        </div>
    </div>
</nav> --}}
{{/if}}