@import 'waves';
@import 'partials/_utils';
@import 'partials/theme-ph';
@import 'partials/editor-ui';

$blue-font: $accent-blue-500;
$dark-font: $slate-500;
$base-font-size: 14px;
 


@keyframes fadeInUp {
    from {
        -webkit-transform: translateY(32px);
        transform: translateY(32px);
        opacity: 0
    }

    to {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }
}

* {
    font-family: 'Source Sans Pro', sans-serif;
    outline: none !important;
}

body{
    background: #fff;
    overflow: hidden;
    height: calc(100vh)
}


.swal2-border-radius { -webkit-border-radius: 0 !important; -moz-border-radius: 0 !important; border-radius: 0 !important; }

.border-none {
    border: 0;
}



.analytics-tables-container {
    height: calc(45vh - 85px);
}

.tbl-sort-icon {
	color: gray;
	font-size: 11px;

	:hover {
		font-size: 12px;
		color: $blue-font;
		// cursor: hand
	}
}

.active-sort-btn {
	color: $blue-font;
}


.daterange-analytics {
	font-size: 13px;
	color: $dark-font
	
	input {
		border: none
	}
}

.tbl-pageviews {
	font-size: 13px;
	
	overflow-y: auto!important;
	overflow-x: hidden!important;	
	padding-right: 10px;
    padding-bottom: 10px;
	// ::-webkit-scrollbar-thumb {
	// 	width: 3px;
	// }
	// ::-webkit-scrollbar-track {
	// 	width: 3px;
	// }

	// ::-webkit-scrollbar {
	// 	width: 3px;
	// }

	// tbody {
	// 	display: block; 
	// 	height: calc(100vh - 595px)!important; 
	// 	width: auto;
	// 	//width: 100%!important;
	// 	overflow-y: auto!important;
	// 	overflow-x: hidden!important;	
	// }
	
	// th tbody tr {
	// 	display: table;
	// 	width: 100%;
	// 	table-layout: fixed;
	// }
	
}

.helpful-section {
	font-weight: 450;
	margin-top: 40px;
	margin-bottom: 40px;

	#feedbackContainer a:hover {
	background-color: $blue-font; 
	}

	.feedback-comments-box {
		padding: 10px;
		margin-top: 10px;
		border: none;
		width: calc(100% - 30px);
        border: 1px solid lightgrey;
        background-color: white !important;
        border-radius: 5px;
	}

	.feedback-comments-box:focus{
		// border: 0 none #FFF;
		overflow: hidden;
		outline:none;
	}

	.comment-submitted {
		font-style: italic;
		font-size: 14px;
	}

	.comment-submitted-on {
		font-size: 11px;
	}
}

#settingsAnalyticsContainer {
	height: calc(100vh - 50px);
	overflow-y: auto;
	overflow-x: hidden;
}

.wrapper {
    padding-top: 24px;
}


.accordions .card-body {
  a {
    color: $blue-font !important;
  }
  a:visited {
    color: $blue-font !important;
  }
  a:hover {
    color: $blue-font !important;
    text-decoration: underline;
  }
}

//will be useful when backdrop is false
// .swal2-container {

// 	.swal2-popup {
//         border: 1px solid #ced4da;
//         border-radius: 0.25rem;
//         box-shadow: 0 0 11px rgba(33, 33, 33, 0.2);
//     }
// }

.country-flag-icon {
  margin-top: 1px;
  height: 15px;
  width: auto;
}

.topic-tree {
  color: rgb(85,85,85);
  text-align: left;
  font-weight: normal;
  margin-top: -13px;
  padding-top: 0px!important;

  ul, li {
    list-style: none;
    margin: 0;
    padding: 0;
    padding-left: 20px;
  }

  li {
    padding-left: 10px;
    border: 1px dotted black;
    border-width: 0 0 1px 1px;
    line-height: 6px;
  }

  li p {
    margin: 0;
    padding-left: 2px;
    background: white;
    position: relative;
    top: 6px;
  }
}

.badge {
  margin-top: 1px;
	padding-top: 5px;
	line-height: 1.1;
}

.badge-outline-dark {
	background: none;
  border: 1px solid rgb(85,85,85);
  color: rgb(85,85,85);
}

.badge-outline-none {
	background: none;
  border: none;
  color: #343a40;
}

.badge-green {
	// padding-top: 4px;
	// margin-top: 2px;
	background: #2b982b;
	line-height: 1.2;
	color: #f8fafb;

}

.badge-orange {
	// padding-top: 4px;
	// margin-top: 2px;
	background: #ffa500;
	line-height: 1.2;
	color: #f8fafb;
	width: 63px;
}


#settingsModalBody {
    max-height: calc(100vh - 170px);
    overflow-y: auto;
    overflow-x: hidden;
}

#divAllTopics {
    margin-top: 38px;
}

#articleTopicOptions {
    max-width: 220px;
    //width: fit-content;
}
#articleSubTopicOptions, #articleStatusOptions {
    max-width: 220px;
    //width: fit-content;
}
#articleSearchResults {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
}

.article-history-item {
    max-height: calc(100vh - 280px);
    overflow-y: auto;
    overflow-x: hidden;
}

.featured-card {
    padding: 10px;
    max-height: 60px;
    overflow-x: hidden;
    overflow-y: auto;
}

.featured-row {
    padding-left: 23px;
    padding-right: 23px;
    margin-bottom: -8px;
}

.tokenfield.tokenfield {
    display: flex;
    width: 100%;
    font-size: 12px;
    font-weight: 400;
    color: $slate-450;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;

    .token {
        height: 100%;
        border: 1px solid $slate-200;
        background-color: $slate-50;
        display: flex;
        align-items: center;
        border-radius: $border_radius-s;
        font-size: 12px;

        span {
            color: $slate-500;
        }
    
        a {
            color: $slate-450;
        }
    }

    input.token-input {
        width: fit-content !important;
        padding: 2px 6px;
        min-height: 26px;
        font-size: 12px;
    }
}

.search-result-card:hover {
    border-color: #afafaf;
    box-shadow: 0 0 11px rgba(33, 33, 33, 0.2);

    cursor: pointer;

    .search-result-title {
        text-decoration: none;
        color: $blue-font;
    }
}

#settingsModal {
    position: absolute;
    max-width: 500px;
    padding: 10px;
    z-index: 999;
    right: 15px;
}

#articleHistory {
    position: absolute;
    max-width: 500px;
    padding: 10px;
    z-index: 999;
    top: 20px;
    right: 15px;
}

.tabcontent-settings {
    padding: 20px 0 50px;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: white;
}

.article-btn {
    width: 70px;
}

ol.breadcrumb {
    margin-left: -15px;
    font-size: 14px;
    background-color: transparent;

    a {
        text-decoration: none;
        color: $dark-font;
        font-weight: bold;
    }
}

#article_filter:focus{
    border: solid 2px #0094f7 !important;
}

.view_article_container {
    overflow-x: hidden;
    overflow-y: visible;
    height: calc(100vh);
}

#articleContents{
    padding: 0 0 20px;
}

.topic-settings-container {
    overflow-x: hidden;
    overflow-y: auto;
    height: calc(100vh - 130px);
}

.topic-text {
    font-size: 16px;
}

#topicsListGroup {
    height: calc(100vh - 300px);
    overflow: auto;
}

.related-key-searches {
    font-size: $base-font-size;
    line-height: 32px;

    a:link {
        color: $dark-font;
        text-decoration: none;
    }
    a:visited {
        color: $dark-font;
        text-decoration: none;
    }
    a:active {
        color: $dark-font;
        text-decoration: none;
    }
    a:hover {
        font-weight: bold;
    }
}

.important-guides-title {
    font-size: $base-font-size + 2px;
    font-weight: bold;
}

.important-guides {
    font-size: $base-font-size;

    a:link {
        color: $dark-font;
        text-decoration: none;
    }
    a:visited {
        color: $dark-font;
        text-decoration: none;
    }
    a:active {
        color: $dark-font;
        text-decoration: none;
    }

    a {
        font-weight: 500;
        background-color: #e9f4ff;
        color: $blue-font !important;
        padding-right: 8px;
        padding-left: 8px;
    }
    a:hover {
        background-color: #ddedfd;
    }
}

.search-result-body {
    font-size: $base-font-size;
}

#topics-contents {
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.search-card-title {
    font-size: $base-font-size + 9px;
    font-weight: bold;
}

.card-body {
    a:link {
        color: $dark-font;
        text-decoration: none;
    }

    a:visited {
        color: $dark-font;
        text-decoration: none;
    }

    a:active {
        color: $dark-font;
        text-decoration: none;
    }
}

#search-result-wrap{
    max-width: 1600px;
}

.search-results-tags {
    text-decoration: none;
    background-color: #e9f4ff;
    padding-right: 8px;
    padding-left: 8px;
    border-radius: 4px;
    color: #308cea !important;
}
.search-results-tags:hover {
    background-color: #ddedfd;
}

.scrollable-container {
    overflow-y: scroll;
    height: calc(100vh - 400px);
    padding-bottom: 30px;
    width: 100%;
}

.search-card-sub-title {
    font-size: $base-font-size - 1px;
}

.results-text {
    font-size: $base-font-size + 5px;
    color: $blue-font;
    font-weight: bold;
}

.results-text-normal {
    font-size: $base-font-size + 1px;
    font-weight: bold;
}

// .accordion-search {
//     max-width: 300px;

//     .accordion-search-input {
//         padding-left: 30px;
//         padding-top: 5px;
//         padding-bottom: 5px;
//     }

//     .fa-search {
//         position: absolute;
//         z-index: 2;
//         display: block;
//         width: 30px;
//         height: 30px;
//         line-height: 30px;
//         text-align: center;
//         pointer-events: none;
//         color: #aaa;
//     }
// }



.in-article-search-box {
    
    .form-search-input {
        padding-left: 45px;
        padding-top: 20px;
        padding-bottom: 20px;
        width: 100% !important;
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-top-right-radius: 7px;
        border-bottom-right-radius: 7px;
        line-height: 38px;
    }

    .form-search-icon {
        position: absolute;
        z-index: 2;
        display: block;
        width: 50px;
        height: 50px;
        line-height: 38px;
        text-align: center;
        pointer-events: none;
        color: #aaa;
    }
}
.back-btn {
    background-color: white;
    color: rgb(116, 116, 116);
    max-height: 42px;
    width: 100px !important;
    margin-right: 10px;
    //margin-left: 10px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    border-top-left-radius: 7px;
    border-bottom-left-radius: 7px;
    border: 1px solid #ced4da;
    padding-top: 10px;
    padding-left: 8px;
    padding-right: 8px;
    padding-bottom: 8px;
    //   box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;

    :hover {
        color: #3f3f3f;
    }
}

.in-article-search-box, .has-seach {
    .shortcut-icon {
        position: absolute;
        right: 10px; // Adjust accordingly
        top: 50%;
        transform: translateY(-50%);
        // pointer-events: none; // To ensure the icon doesn't block the input

        &:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
    }

    .shortcut-key {
        display: inline-block;
        padding: 2px 6px;
        border: 1px solid #aaa;
        border-radius: 4px;
        background-color: #f7f7f7;
        font-size: 0.8rem;
        color: $slate-400;
        opacity: 0.8;
    }

    .tooltip-text {
        visibility: hidden;  // By default, the tooltip is hidden
        opacity: 0;
        position: absolute;
        bottom: 100%;  // Position it above the shortcut icon
        left: 50%;
        transform: translateX(-50%) translateY(-5px);  // Center the tooltip above the icon and a little offset above
        padding: 8px 12px;
        background-color: #333;
        color: #fff;
        font-size: 0.75rem;
        border-radius: 4px;
        white-space: nowrap;  // Keeps the tooltip text on a single line
        pointer-events: none;  // Ensure it doesn't interfere with other interactions
        transition: opacity 0.2s ease-in-out;  // Smooth fade-in effect

        // Arrow pointing towards the shortcut icon
        &::after {
            content: '';
            position: absolute;
            top: 100%;  // Position at the bottom of the tooltip box
            left: 50%;
            transform: translateX(-50%);
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
    }
}

.has-seach {
    position: relative; // Ensuring child absolute positioning refers to this parent

    .form-search-input {
        padding-left: 45px;
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .form-search-icon {
        position: absolute;
        z-index: 2;
        display: block;
        width: 50px;
        height: 50px;
        line-height: 40px;
        text-align: center;
        pointer-events: none;
        color: #aaa;
    }
}


.main-title {
    font-size: $base-font-size + 17px;
    font-weight: bolder;
    color: $blue-font;
}

.subtopic-small {
    font-size: $base-font-size - 1px;
}

.subtopic-text {
    font-size: $base-font-size + 1px;
    margin-bottom: 0px;
    text-decoration: none;

    li a:hover {
        color: $blue-font;
    }
    a:link {
        color: $dark-font;
    }
    a:visited {
        color: $dark-font;
    }
    a:active {
        color: $dark-font;
    }
}

.subtitle-text {
    padding: 20px;
    color: #585858;
    font-size: $base-font-size + 3px;
    //line-height: 1.2;
}

.countryLabel {
    padding: 10px;
    margin: 0px;
}

.selectBox {
    position: relative;
}

.selectBox select {
    width: 100%;
}

.overSelect {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
    z-index: 999;
    width: 100%;
    height: 100%;
}

#settingsContent{
    height: calc(100vh - 110px);
    overflow-y: auto;
}

#countryCheckboxes {
    position: absolute;
    background-color: #fff;
    display: none;
    overflow-x: hidden;
    z-index: 99;
    height: fit-content;
    overflow-y: auto;
    top: 40px;
}


@media (min-width: 1900px) { 
    #countryCheckboxes {
        height: auto;
        overflow: visible;
        
    }
 }

#countryCheckboxes label {
    display: block;
}

#countryCheckboxes label:hover {
    background-color: #e4fde2;
}

.nav-icon {
    color: rgb(99, 99, 99);
    padding-top: 10px;
}

.menu-font-small {
    font-size: $base-font-size - 2px;
}

.menu-font-large {
    font-size: $base-font-size + 5px;
}


.body-min {
    min-height: 400px;
}

.hero-title {
    color: whitesmoke;
    background: rgb(34, 34, 34);
    padding-top: 10px;
    padding-right: 10px;
    padding-left: 10px;
}
.filterMainContainer {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.filterContainer {
    display: relative;
}

.topics-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
}

.topic-item {
    margin-bottom: 30px;
}

.card-body.topic-item:hover {
    border-color: #afafaf;
    box-shadow: 0 0 11px rgba(33, 33, 33, 0.2);

    cursor: pointer;

    .icon {
        fill: $blue-font;
    }
}

.tab-panel-container {
    padding-left: 20px;
    padding-right: 40px;
    background-color: white;
    height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
}

.body-row {
    min-height: 300px;
}
.testclass {
    height: 110px;
}

.article-container {
    overflow-y: scroll;
    height: calc(100vh - 100px);
}

.panel-heading {
    color: #333;
    background: #f5f8fa;
    border-color: #e3eaee;
}

.articles-container {
    height: calc(100vh - 220px);
    overflow: auto;
}


.main-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    background: #fff;
    max-height: calc(100vh);
    overflow: auto;

}

.search-wrap{
    padding: 0 20px;
    .search-content {
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        background: #f6f8fa;
        border-radius: 4px;
        margin-bottom: 16px;
    }

    .search-title-wrap {
        width: 400px;
    }

    // a {
    //     display: inline-block;
    //     line-height: 31px;
    //     font-size: 12px;
    //     font-weight: 600;
    //     color: rgb(48, 49, 61);
    //     text-transform: uppercase;
    // }
}

.tab {
    padding: 16px 0 0;
}

#wikiAdminNav { 
	
    padding: 20px 20px 0;
	.nav-link {
		font-weight: 600;
		color: rgb(106,115,131);
		font-size: 16px;
		
		padding-right: 15px;
		padding-left: 15px;
		padding-top: 5px;
		padding-bottom: 5px;
        background: none;
		border: none;
	}

	.active {
		border-bottom: 2px solid $blue-font;
        color: $blue-font;
        background: none;
	}
	.custom-nav-title {
		
		display: flex;
		justify-content: left;
		font-size: 20px;
		font-weight: 500;
		align-items: center;
		padding-right: 50px;
		padding-left: 20px;

		:hover {
			border-color: white;
			border-right: none;
			border-left: none;
		}
	}

	.custom-nav-right {
		display: flex;
		justify-content: right;
		font-size: 20px;
		font-weight: 500;
		align-items: center;
		padding-right: 28px;
		padding-left: 20px;

		:hover {
			cursor: pointer;
			border-color: white;
			border-right: none;
			border-left: none;
		}
	}

}
.search-container {
    background: #f8fafb;
    padding: 0px !important;
}

.filters-wrap {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -webkit-align-items: start;
    -ms-flex-align: start;
    align-items: start;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%;
    padding: 0 0 10px;

    .filters {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        gap: 10px;

        .filter-grp {
            height: 24px;
            border-radius: 24px;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            border: 1px dashed rgba(48, 49, 61, .2);
            line-height: 24px;
            font-size: 12px;
            font-weight: 600;
            color: #6a7383;

            p.filter-label {
                margin: 0;
                padding: 0 10px;
                line-height: 24px;
                font-size: 12px;
            }

            .filter-grp-divider {
                width: 1px;
                height: 12px;
                border-radius: 12px;
                background-color: #c1c9d2;
            }

            .filter {
                position: relative;
                height: 26px;
                .filter-selected {
                    border: none;
                    outline: 0;
                    color: #0094f7;
                    padding-right: 22px;
                    background: url(../images/chevron-down-sm.svg) no-repeat calc(100% - 8px) center
                }
            }
        }
    }

    .filter-options {

        &.filter__options--active {
            -webkit-animation: fadeInUp .1s ease-in-out 0s forwards;
            animation: fadeInUp .1s ease-in-out 0s forwards;
            pointer-events: auto;
           
        }

        position: absolute;
        left: 0;
        top: 100%;
        list-style: none;
        padding: 0;
        background: #fff;
        border-radius: 6px;
        -webkit-box-shadow: 0 3px 8px rgba(0, 0, 0, .1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, .1);
        z-index: 2000;
        min-width: fit-content;
        width: 100%;
        margin: 4px 0 0;
        opacity: 0;
      
        transition: .75s ease-in-out;
        pointer-events: none;

        .filter-option {
            padding: 3px 10px;
            cursor: pointer;
            background: rgba(0, 0, 0, 0);
            white-space: nowrap;
            &:hover{
                background: rgb(0,0,0,.05)
            }
            &.filter__option--active {
                color: #0094f7;
            }
        }
    }
}

.article-duplicate  {
    i:hover {
        color: #308cea;
    }
    input[type=text] {
        height: 33px;
        color: #30313d;
        border: 1px solid #ebeef1;
    }
    .btn:not(.btn-link):not(.btn-circle).btn-primary {
        background: #0094f7
    }
    .swal2-actions button {
        text-transform: uppercase;
        opacity: 0.65;
        font-size: 14px;
    }
}

.main-form {

    h5 {
        display: none;
    }

    position: relative;

    #form-wrap {
        margin-right: 90px;
        &.with-sidebar {
            margin-right: 520px;
        }
    }


}

table#articles {
    width: 100% !important;
    margin-bottom: 200px;
    thead {
        position: sticky;
        left: 0;
        top: 0;
        z-index: 99;
        background: #fff;
        th {
            padding-left: 20px;
            padding-right: 20px;

            &:first-child {
                padding-left: 16px;
            }

            &:last-child {
                padding-right: 16px;
            }

            &.no-sort {
                .articles-table__header__icon {
                    display: none;
                }
            }

            .articles-table {
                &__header {
                    display: flex;
                    white-space: nowrap;
                    align-items: center;
                    font-size: .6875rem;
                    text-transform: uppercase;
                    line-height: 20px;
                    font-weight: 600;
                    color: rgb(48, 49, 61);

                    &--justify-end {
                        justify-content: flex-end;
                    }

                    span:first-child {
                        padding-right: 4px;
                    }

                    &__icon {
                        width: 18px;
                        height: 18px;
                        background: url(./../images/arrow-down-icon.svg) no-repeat center center;
                        background-size: cover;
                        opacity: 0.2;
                    }
                }
            }

            &.sorting {

                &_asc,
                &_desc {
                    .articles-table__header__icon {
                        opacity: 1;
                    }
                }

                &_desc {
                    .articles-table__header__icon {
                        transform: rotate(180deg);
                    }
                }
            }

            .sorting &:after {
                display: none;
            }
        }
    }

    tbody {
       
        td {
            padding: 18px 6px;
            border-top: none;

            &:first-child {
                padding-left: 16px !important;
            }

            &:last-child {
                padding-right: 16px !important;
            }
        }

        tr {
            &.article {
                cursor: pointer;
                &.d-status-false {
                    background-color: rgba(0, 0, 0, 0.02);
                }
                &.alarm-triggered {
                    animation: bgPulse 1s linear 0s infinite alternate-reverse;
                }
            }
        }
    }
}

// cells
.article-cell {
    color: #6a7383;
    font-size: 14px;

    &.title {
        width: 100%;
    }

    &--right {
        text-align: right;
    }
    &--center {
        text-align: center;
    }



    &__title {
        width: 100%;
        display: flex;
        line-height: 20px;
        color: #30313d;
        font-size: 14px;
    }
    &__translation {
      
        font-size: 10px;
    }    

    &__published_date {
        min-width: 200px;
        color: rgb(106, 115, 131);
        font-size: 14px;
    }

    &__visibility {
        width: 110px;    
        ul{
            list-style-type: none;
            padding: 0;
            display: flex;
            -webkit-column-count: 3;
            -moz-column-count: 3;
            column-count: 3;
            column-width: 33%;
            li{
                margin-right: 10px;
            }
            flex-wrap: wrap;
            align-content: left;
        }
    }
    
    &__status {
        border-radius: 4px;
        padding: 1px 4px 1px 6px;
        margin-right: 4px;
        font-size: 12px;
        line-height: 16px;
        display: inline-flex;
        white-space: nowrap;
        height: 18px;

        &--published {
            background: #d7f7c2;
            color: #006908;
        }

        &--draft {
            background: #f8fafb;
            color: #6a7383;
        }
    }
}



.table-hover>tbody>tr:hover {
    background-color: #f5f7f9
}

.table th{
    border-top: none;
}

.table tbody tr td,.table tbody tr th {
    padding: 10px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}


.table tbody tr {
    &:hover{
        background: #f9f9f9;
    }
    td {
        color: #6a7383
    }
}

.table tbody tr.primary td,.table tbody tr.primary th {
    background-color: #1f91f3;
    color: #fff
}

.table tbody tr.success td,.table tbody tr.success th {
    background-color: #2b982b;
    color: #fff
}

.table tbody tr.info td,.table tbody tr.info th {
    background-color: #00b0e4;
    color: #fff
}

.table tbody tr.warning td,.table tbody tr.warning th {
    background-color: #ff9600;
    color: #fff
}

.table tbody tr.danger td,.table tbody tr.danger th {
    background-color: #fb483a;
    color: #fff
}

.table thead tr th {
    padding: 10px;
    border-bottom: 1px solid #eee
}

.table-bordered {
    border-top: 1px solid #eee
}

.table-bordered tbody tr td,.table-bordered tbody tr th {
    padding: 10px;
    border: 1px solid #eee
}

.table-bordered thead tr th {
    padding: 10px;
    border: 1px solid #eee
}


// .dt-buttons {
//     display: none;
// }


#navContentArticles{
    
    .buttons {
        .btn {
            height: 30px;
            font-size: 12px;
            color: #30313d;
            padding: 6px 2px 6px 6px;
            text-transform: uppercase;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            border-radius: 4px;
            justify-content: center;
            line-height: 1.1;
            &:focus {
                outline: none !important;
                box-shadow: none !important;
            }
            span {
                margin-right: 4px;
            }
            svg {
                width: 20px;
                height: 20px;
                margin: 0;
                color: #6a7383
            }
        }
    }
}

.article-duplicate{
    font-size: 16px;
}

.accordion-header a{
        display: inline-block;
        width: 100%;
        height: 100%;
        background: url(./../images/chevron-down-sm.svg) no-repeat right center;
        background-size: 18px;
        background-position: right center;
    &[aria-expanded="true"]{
        background: url(./../images/chevron-up-sm.svg) no-repeat right center;
        background-size: 18px;
    }
}


.home-view {
    height: 100%;
    overflow-y: visible;
    overflow-x: visible;
    max-width: 1600px;
    margin: 0 auto;
}

.show_footer {
    margin-bottom: 0;
}

#wikipage-wrap {
    margin: 1.25rem 1.25rem 0;
}

#article-form-wrap{
    margin: 1.25rem 1.25rem 0;
    height: 100%;
    overflow-y: visible;
    overflow-x: visible;
}

.search-results-container {
    overflow-y: visible;
    height: auto;
    padding-bottom: 30px;
    width: 100%;
  }

  .CodeMirror, .CodeMirror-scroll {
	min-height: 200px;
    //height: auto !important;
    height: calc(100vh - 110px - 175px - 96px) !important;
    //height: calc(100vh - 110px - 104px - 86px - 70px) !important;
    //overflow: visible;
}



#article-form-wrapper{
    padding: 0 20px;
    height: calc(100vh - 60px);
}

.view_article_container{
    padding: 0 20px;
}

.kb-search-container {
    padding: 1.25rem 0 0;
    width: 100%;
    top: 0;
    position: sticky;
    z-index: 100;
    // background: #fff;
    // background-image: linear-gradient(white, white calc(100% - 10px), transparent);
}

.kb-search-container:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: -10px;
    background-image: linear-gradient(white, white calc(100% - 20px), transparent);
    pointer-events: none; /* ensure the pseudo-element doesn't interfere with any interactions */
    z-index: -1; /* place it below the content of the .your-element */
}

#article-view-content-wrap{
    height: 100%
}

.overflow-auto{
    overflow: auto;
}

#articleRightSideMenu{
 
    &.sticky {
        position: fixed;
        top: 20px;
        // width: 100%;
        z-index: 999;
    }
}


.search-result-item-wrap{
    display: flex;
    padding-bottom: 10px;
    .search{
        &--file-icon {
            width: 16px;
            height: 16px;
            margin-right: 20px;
            align-self: center;
        }
        &--article-title {
             color: #308cea;
             font-weight: 600;
             margin: 0;
             
        }
        &--article-body{
            color: #585858;
            font-size: 18px;
            line-height: 27px;
            padding-bottom: 10px;
        }
    }
}

.body-min{
    position: relative;
}

// Algolia Overlay (background)
.aa-DetachedOverlay{
    z-index: 999999;
    position: absolute;
    background: rgb(0,0,0,.5);
}

// Algolia search element on dashboard/articles...
.aa-Autocomplete {
    .aa-DetachedSearchButton:focus {
        border-color: #0094f7 !important;
        box-shadow: #0094f721 0 0 0 3px, inset #0094f724 0 0 0 2px !important;
    }
    .aa-DetachedSearchButtonIcon {
        color: #308cea!important;
    }
}

// Algolia Widget
.aa-DetachedContainer {
    .aa-SourceHeaderTitle {
        color: #308cea!important;
    }

    .aa-Label {
        margin-top: 8px;
        svg {
            color: #308cea!important;
        }
    }

    .aa-SourceHeaderLine {
        border-bottom: 1px solid #308cea!important;
    }

    .aa-ItemLink{
        &:hover {
            text-decoration: none;
        }
    }

    .aa-ItemIcon{
        height: 16px;
        width: 16px;
        box-shadow: none;
    }

    .aa-ItemContentTitle {
        mark {
            color: #308cea;
        }
    }
    .aa-Item[aria-selected=true] {
        background-color: rgb(173 193 214 / 21%) !important;
    }


    .aa-ItemContentDescription{

        overflow-y: hidden;
        overflow-x: hidden;

        mark {
            color: #308cea;
            background: #e9f4ff;
        }
    }

    .aa-no-results {
        padding: 20px;
    }


    .aa-Form {
        margin: 1px;
        &:focus{
            border: solid 2px #0094f7!important;
            margin: 0px;
        }
        &:focus-within{
            border: solid 2px #0094f7!important;
            outline: 0 !important;
            box-shadow: none;
            margin: 0px;
        }

    }
}

.aa-DetachedSearchButton{
    &:focus {
        box-shadow: none;
        border: solid 2px #0094f7!important;
    }
}


//topics page
#search-result-wrap {
    #articles-list-wrap {
        margin-bottom: 16px;
    }

    h3{
        font-weight: 700;
        font-size: 17px;
    }

    .search-result-card {
        border-radius: 0;
        padding: 10px;
        box-shadow: none;
        &:hover{
            background-color: rgb(173 193 214 / 21%) !important;
        }
    }

    #featured-articles{
        padding: 0;
        list-style: none;
        li {
            padding: 0;
        }
        a{
        
            font-weight: 600;
        
        }
    }
}


.important-guides{
    a {
        word-break: keep-all;
    }
}

#editor-wrapper {
    position: relative;
    &.disabled {
        .CodeMirror-wrap {
            background: #e9ecef;
        }
    }
}

#analytics-overview {
    
    width: 100%;
}



h2.chart-header{
    font-size: 24px;
    margin: 0 0 50px;
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding-right: 24px;
}





#analytics-charts{
    display: flex;
    flex-direction: column;
    max-width: 100%;
    gap: 16px;
    
    #analytics-overview-container {
        grid-column: span 2 / span 2;
        grid-row: span 2 / span 2;
        border: 1px solid #d5dbe1;
        border-radius: 4px;
        
        padding: 24px;
    }
    .chart-wrap-mini{
        border: 1px solid #d5dbe1;
        border-radius: 4px;
        
        padding: 24px;
    }
}

@media (min-width: 720px) { 
    #analytics-charts {
        display: grid;
        grid-template-columns: repeat(3,minmax(0,1fr));
        gap: 24px;
        
    }
 }

.chart-container{
    border: 1px solid #d5dbe1;
    border-radius: 4px;
    
    padding: 24px;
}

.overview-wrap{
    max-height: 400px;
}

#analytics-total-searches, #analytics-no-results{
    max-height: 150px;

}

.info-tooltip{
    position: absolute;
    width: 16px;
    height: 16px;
    background: url(./../images/help-icon.svg) no-repeat center;
    right: 0;
    top: 50%;
    margin-top: -8px;
    cursor: pointer;
    display: block
}



.overview-statistics{
    padding: 32px 0 0;
    h2{
        &.heading-w-badge {
            font-size: 20px;
            line-height: 1;
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin: 0 0 16px;
            font-weight: 600;
        }
    }
    .custom-badge {
        padding: 0 6px;
        font-size: 11px;
        line-height: 16px;
        border-radius: 4px;
        text-transform: uppercase;
        font-weight: 600;
        background: #ebeef1
    }
    
    .custom-badge--success {
        color: #006908;
        background: #d7f7c2
    }

    .overview-stats {
    
        display: flex;
      
        align-items: stretch;
    
        flex-wrap: wrap;
        row-gap: 20px;
        margin: 0 auto;
        width: auto;
        max-width: 100%;
        padding: 10px 0 20px;

        .overview-stat {
            flex: 1 0 150px;
            text-align: center;
            padding: 0 16px;
            border-right: 1px solid #ebeef1;
            dt {
                font-size: 11px;
                line-height: 20px;
                color: rgb(106, 115, 131);
                font-weight: 600;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 4px;
                margin-bottom: 4px;
            }
            dd{
                font-size: 24px;
                line-height: 1;
                color: rgb(106, 115, 131);
                text-transform: lowercase;
            }
        }
        .overview-stat:last-child {
            border-right: none
        }
    }
}

.search-data{
   
    margin-bottom: 40px;
    .search-data-table{
        border: 1px solid #d5dbe1;
        border-radius: 4px;
        
        padding: 24px;
    }
    table {
        font-size: 12px;
        color: #6A7383;
        a{
            color: #308cea;
            &:hover{
                text-decoration: none;
            }
        }
        thead{
            background: #f9f9f9;
            font-weight: 500;
        }
    }
}

#topicsSubtopicsChart{
    height: 340px !important;
}




.page-topics-analytics{
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 30px;
    .chart-container {
        //min-height: 340px;
        //min-width: 680px;
        width: 100%
    }
}


@media (min-width: 720px) { 
    .page-topics-analytics {
        display: flex;
        flex-direction: row;
        width: 100%;
        gap: 30px;
        .chart-container {
            //min-height: 340px;
            //min-width: 680px;
            width: 50%
        }
        
    }
 }


#analyticsContent{
    .buttons {
        .btn {
            height: 30px;
            font-size: 12px;
            color: #30313d;
            padding: 6px 2px 6px 6px;
            text-transform: uppercase;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            border-radius: 4px;
            justify-content: center;
            line-height: 1.1;
            &:focus {
                outline: none !important;
                box-shadow: none !important;
            }
            span {
                margin-right: 4px;
            }
            svg {
                width: 20px;
                height: 20px;
                margin: 0;
                color: #6a7383
            }
        }
    }
}

table#top-searches-table, table#top-results-table, table#top-no-results-table {
    width: 100% !important;
    margin-bottom: 200px;
    thead {
        position: sticky;
        left: 0;
        top: 0;
        z-index: 99;
        background: #fff;
        th {
            padding-left: 20px;
            padding-right: 20px;

            &:first-child {
                padding-left: 16px;
            }

            &:last-child {
                padding-right: 16px;
            }

            &.no-sort {
                .articles-table__header__icon {
                    display: none;
                }
            }

            .center{
                text-align: center;
                display: inline-block;
            }

            .articles-table {
                &__header {
                    display: flex;
                    white-space: nowrap;
                    align-items: center;
                    font-size: .6875rem;
                    text-transform: uppercase;
                    line-height: 20px;
                    font-weight: 600;
                    color: rgb(48, 49, 61);

                    &--justify-end {
                        justify-content: flex-end;
                    }

                    span:first-child {
                        padding-right: 4px;
                    }

                    &__icon {
                        width: 18px;
                        height: 18px;
                        background: url(./../images/arrow-down-icon.svg) no-repeat center center;
                        background-size: cover;
                        opacity: 0.2;
                    }
                }
            }

            &.sorting {

                &_asc,
                &_desc {
                    .articles-table__header__icon {
                        opacity: 1;
                    }
                }

                &_desc {
                    .articles-table__header__icon {
                        transform: rotate(180deg);
                    }
                }
            }

            .sorting &:after {
                display: none;
            }
        }
    }

    tbody {
       
        td {
            padding: 18px 6px;
            border-top: none;

            &:first-child {
                padding-left: 16px !important;
            }

            &:last-child {
                padding-right: 16px !important;
            }
        }

        tr {
            &.article {
                cursor: pointer;
                &.d-status-false {
                    background-color: rgba(0, 0, 0, 0.02);
                }
                &.alarm-triggered {
                    animation: bgPulse 1s linear 0s infinite alternate-reverse;
                }
            }
        }
    }
}



// cells
.search-cell {
    color: #6a7383;
    font-size: 14px;

    &.title {
        width: 100%;
    }

    &--right {
        text-align: right;
    }
    &--center {
        text-align: center;
    }

    .highlight {
        color: #0094f7;
    }

    &__title {
        width: 100%;
        display: flex;
        line-height: 20px;
        color: #30313d;
        font-size: 14px;
    }
    &__center {
        width: 100%;
        line-height: 20px;
        color: #30313d;
        font-size: 14px;
        display: inline-block;
        text-align: center;
    }
    &__translation {
      
        font-size: 10px;
    }    

    &__published_date {
        min-width: 200px;
        color: rgb(106, 115, 131);
        font-size: 14px;
    }

    &__visibility {
        width: 110px;    
        ul{
            list-style-type: none;
            padding: 0;
            display: flex;
            -webkit-column-count: 3;
            -moz-column-count: 3;
            column-count: 3;
            column-width: 33%;
            li{
                margin-right: 10px;
            }
            flex-wrap: wrap;
            align-content: start;
        }
    }
    
    &__status {
        border-radius: 4px;
        padding: 1px 4px 1px 6px;
        margin-right: 4px;
        font-size: 12px;
        line-height: 16px;
        display: inline-flex;
        white-space: nowrap;
        height: 18px;

        &--published {
            background: #d7f7c2;
            color: #006908;
        }

        &--draft {
            background: #f8fafb;
            color: #6a7383;
        }
    }
}

#analyticsContent .filter-selected{
    background: none !important;
    cursor: text;
    padding-right: 11px;
}

.analytics-view-all{
    border: solid 1px #6d7382;
    font-size: 11px;
    height: 16px;
    line-height: 16px;
    min-width: 1px;
    padding: 0 6px;
    color: #6d7382;
    background: #fff;
    border-radius: 4px;
    &:hover {
        text-decoration: none;
    }
}

.trumbowyg-box{
    height: calc(100vh - 110px - 175px )!important;
}


.uploaded-media{
    position: relative;
    /* background: rgb(106, 115, 131, 0.4) ; */
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto;
    min-height: 120px; /* Ensure minimum height for placeholder */
    max-height: 200px; /* Limit maximum height to prevent oversized placeholders */
    /* border: 2px dashed #ccc; */
    border-radius: 8px;
    
    &.media-error {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        min-height: 120px;
        max-height: 120px;
        
        span {
            color: #6c757d;
            font-size: 14px;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            &:before {
                content: '🖼️';
                font-size: 32px;
                opacity: 0.5;
            }
        }
    }
    
    span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        background: rgba(255, 255, 255, 0.9);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        color: #666;
    }

    img {
        position: relative;
        height: auto !important;
        opacity: 0;
        max-width: 100%;
        transition: opacity 1s linear;
        z-index: 2;
        
        &.img-fluid{
            opacity: 1;
        }
        
        &.img-error {
            display: none; /* Hide broken images completely */
        }
    }
}

.accordion-search {
    position: relative;
    .fa-search{
        position: absolute;
        top: 50%;
        left: 0;
        margin: -8px 0 0 7px
    }

    input{
        padding-left: 24px;
    }

     > div{
        margin-bottom: 10px;
    }
}
/* Base styles for Trumbowyg */
.trumbowyg-box {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    /* subtle shadow */
}

.trumbowyg-box .trumbowyg-editor {
    border-radius: 0 0 8px 8px;
    /* Rounded corners for the bottom of the editor */
    border-top: none;
    /* Remove top border to merge toolbar and editor seamlessly */
    padding: 15px;
}

/* Toolbar styles */
.trumbowyg-box .trumbowyg-button-pane {
    background-color: $slate-50;
    /* Soft background color */
    border-bottom: none;
    /* Remove bottom border to merge toolbar and editor */
    border-radius: 0;
    /* Rounded corners for the top of the toolbar */
}

.trumbowyg-box .trumbowyg-button-pane button {
    margin: 0 5px;
    border-radius: 4px;
    /* Rounded corners for buttons */
    transition: background-color 0.2s, box-shadow 0.2s;
    /* Smooth transitions for hover and focus */
}

.trumbowyg-box .trumbowyg-button-pane button:hover {
    background-color: #e1e7ed;
    /* Change color on hover */
}

.trumbowyg-box .trumbowyg-button-pane button:active,
.trumbowyg-box .trumbowyg-button-pane .trumbowyg-active-button {
    background-color: #c4ccd3;
    /* Active state color */
}

.trumbowyg-box .trumbowyg-button-pane button:focus {
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    /* Focus outline, a soft blue glow */
    outline: none;
}


.trumbowyg-modal {

    *,
    *::before,
    *::after {
        box-sizing: none;
        border: none;
    }
}

.editor-wrap,
#preview-wrap {
    flex-grow: 1;
}

:root {
    --tbw-cell-vertical-padding: 4px;
    --tbw-cell-horizontal-padding: 8px;
    --tbw-cell-line-height: 1.5em;
}

.trumbowyg-box table {
    margin-bottom: var(--tbw-cell-line-height);
}

.trumbowyg-box th,
.trumbowyg-box td {
    height: calc(var(--tbw-cell-vertical-padding) * 2 + var(--tbw-cell-line-height));
    min-width: calc(var(--tbw-cell-horizontal-padding) * 2);
    padding: var(--tbw-cell-vertical-padding) var(--tbw-cell-horizontal-padding);
    border: 1px solid #e7eaec;
}

.trumbowyg-box {
    .accordion-item .card-body {
        a {
            color: $blue-font !important;
        }

        a:visited {
            color: $blue-font !important;
        }

        a:hover {
            color: $blue-font !important;
            text-decoration: underline;
        }
    }

    .accordion-header a {
        display: inline-block;
        width: 100%;
        height: 100%;
        background: url(./../images/chevron-down-sm.svg) no-repeat right center;
        background-size: 18px;
        background-position: right center;

        &[aria-expanded="true"] {
            background: url(./../images/chevron-up-sm.svg) no-repeat right center;
            background-size: 18px;
        }
    }

    .accordion-item,
    .alert {
        width: 100%;
    }

    .alert {
        p {
             margin: 0;
        }
    }

    .uploaded-media img {
        opacity: 1 !important;
    }

    .plyr button {
      
        height: 48px;
    }
}

.mb-100px {
    margin-bottom: 100px;
}