html,
body {
  //font-family: 'Khula', sans-serif;
  font-size: 16px;
  //overflow: hidden;
  // overflow-x: hidden;
}

html {
  position: relative;
  min-height: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: inherit;
  font-weight: 300;
  line-height: 1.65;
  color: #292f33;
}

h2,
.h2 {
  font-size: 38px;
}

.hljs {
  background: transparent;
}

.container-fluid {
  padding-right: 15px;
  padding-left: 15px;
}

.searchBar {
  height: 300px;
  background: transparent url(/openkb/themes/12rnd/images/headerBg.jpg)
    no-repeat top left;
  background-size: cover;
  text-align: center;
  /*margin-bottom: 10px;*/
}

.searchBar.kb {
  height: 200px;
  margin-top: -50px;
}
.search-title {
  display: -webkit-inline-box;
}
.searchBar h1 {
  /*    font-size: 40px;
    font-weight: 300;
    margin: 75px 0 10px;
    color: #fff;
    line-height: 1;*/

  font-size: 40px;
  font-weight: 300;
  margin: 75px 0 10px;
  color: #fff;
  line-height: 1;
  background-color: black;
  display: table;
  padding: 5px;
  padding-right: 20px;
  padding-left: 20px;
}

.searchBar a {
  color: #fff;
}

p.subheading {
  padding: 20px;
  color: #899aa6;
  font-size: 25px;
  line-height: 1.2;
}

.search-results-container {
  overflow-y: auto;
  height: calc(100vh - 200px);
  padding-bottom: 30px;
  width: 100%;
}

//#frm_search {
  // border-top-left-radius: 30px;
  // border-bottom-left-radius: 30px;
  //padding: 20px;
//}

.search-results {
  border: 0px !important;
}
.search-results h3 {
  padding-left: 20px;
}
.search-results h4 a {
  color: #337ab7;
  font-weight: 500;
}
.search-results .keywords a {
  color: #337ab7;
  font-weight: 200;
}
.search-results .keywords .keywordSeparator:last-child {
  display: none;
}
.search-results .summary-snip {
  max-width: calc(100% - 130px);
  font-size: 14px;
}
.featuredPanel {
  height: 150px;
  text-align: center;
}

.kb-key-searches {
  font-weight: bold;
}
.kb-key-searches .keywordSeparator:last-child {
  display: none;
}
.panel-default {
  border: 1px solid #343a40;
  border-radius: 0px;
}

.featuredArticles {
  margin-top: 20px;
}

.featuredArticlesFooter {
  background-color: #0c85d0;
  border-color: #0b7fc6;
  text-align: center;
  color: #fff;
}

.featuredArticlesFooter a {
  color: #fff;
}
/* .navbar {
    background-color: black !important;
    margin-bottom: 0px;
}

.navbar-default .navbar-brand {
    color: #fff;
}

.navbar-default .navbar-nav>li>a {
    color: #fff;
}

.navbar-default {
    border: none;
}

.navbar-default .navbar-collapse, .navbar-default .navbar-form {
    border: none;
} */

/* .navbar-default {
    background-color: transparent;
} */

.list-group-item a {
  color: #555;
  border-radius: 0px;
}
.list-group-item,
.list-group,
.panel-body,
.list-group-item:first-child,
.list-group-item:last-child {
  border-radius: 0px;
}

.view_count {
  font-size: 14px;
}

#notify_message {
  position: fixed;
  display: none;
  z-index: 9999;
  padding-top: 10px;
  height: 50px;
  bottom: 0px;
  width: 100%;
  text-align: center;
  font-size: 22px;
  color: white;
}

.notify_message-success {
  background-color: #18bc9c;
}

.notify_message-danger {
  background-color: #e74c3c;
}

#searchResult {
  right: -2px;
  left: 0px;
  min-height: 200px;
  position: absolute;
  padding-top: 5px;
  z-index: 1000;
}
#searchResult ul {
  margin-top: -6px;
  margin-left: 20px;
  margin-right: 51px;
  max-height: 50vh;
  box-shadow: 0px 3px 5px 0px #c5c5c5;
  overflow-y: scroll;
  background-color: white;
}
#searchResult .list-group-heading {
  display: none;
}
#searchResult li {
  padding: 0px;
  border: none;
}
#searchResult li a {
  padding: 10px;
  display: block;
  text-align: left;
  color: #464646;
}
#searchResult li a:hover {
  background-color: #efefef;
  text-decoration: none;
}
.kb-content {
  overflow-y: scroll;
  height: calc(100vh - 150px);
  padding-bottom: 30px;
}
// .kb-container {
//   padding-right: 0px;
//   padding-left: 0px;
// }

.kb-page h1 {
  margin-top: 50px;
}

.accordion-header {
  background-color: #ffffff;
  font-weight: 450;
  a {
    color: #040404;
  }
}

.panel-default > .panel-heading {
  color: #fff;
  border-radius: 0px;
  background: #343a40;
  border-color: #343a40;
}

.panel-group.grey .panel-default > .panel-heading {
  background-color: #ffffff;
  color: #040404;
}

.panel-group.grey .panel-default > .panel-heading .panel-title {
  font-weight: 400;
}
.panel-group.grey .panel-default {
  border: 1px solid #a2a2a2;
}

.panel-primary .panel-heading {
  background: #343a40;
  color: #fff;
  border: none;
  border-radius: 0px;
}
.panel-primary {
  border-radius: 0px;
  border: 1px solid #343a40;
}

.article-details {
  margin-top: 0px !important;
}

.show_footer {
  /*margin-bottom: 80px;*/
}

.metaData h5 {
  padding-bottom: 0px;
  line-height: 1;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 80px;
  text-align: center;
  padding-top: 25px;
  margin-top: 35px;
  background: #f5f8fa;
  border-top: 1px solid #e3eaee;
}
.form-control:focus {
  border-color: inherit;
  box-shadow: none;
}

.home-icons {
  padding-top: 20px;
}
.home-icons a {
  padding: 20px;
}
.home-icons a:hover {
  background-color: #f5f5f5;
  text-decoration: none;
}
.home-icons a:hover h4 {
  font-weight: 400;
}
.home-icons a:hover .icon svg {
  fill: black;
}
.home-icons .panelOuter {
  text-align: center;
}
.home-icons .icon {
  font-size: 40px;
  margin: 15px;
}
.home-icons .icon svg {
  height: 80px;
  fill: #343a40;
}

.home-view {
  height: calc(100vh - 170px);
  overflow-y: auto;
  overflow-x: hidden;
}

.side-article-bar {
  overflow-x: hidden;
  height: calc(100vh - 150px);
  overflow-y: scroll;
}



.searchbar-sm {
  height: 220px;
}

.btn-search-container #btn_search {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  width: 50px;
  /* border-right: none; */
}

.btn-return-home {
  position: absolute;
  right: 0px;
  top: 0px;
  height: 46px;
  padding-top: 8px;
  background-color: white;
  border-color: #cccccc;
  color: grey;
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
  color: #333 !important;
  font-size: 20px;
  width: 50px;
}
.btn-return-home:hover,
.btn-return-home:focus,
.btn-return-home:active {
  background-color: #e6e6e6 !important;
  border-color: #adadad !important;
}

.nav-item:hover {
  background-color: #e9e9e9;
  text-decoration: none;
}

.hero-title {
  color: whitesmoke;
  background: rgb(34, 34, 34);
  padding-top: 10px;
  padding-right: 10px;
  padding-left: 10px;
}
.filterMainContainer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.filterContainer {
  display: relative;
}

.link-home {
  position: absolute;
  right: 0px;
  top: 0px;
  /* height: 46px; */
  /* padding-top: 8px; */
  background-color: whitesmoke;
  border-color: #cccccc;
  color: grey;
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
  color: #333 !important;
  /* font-size: 20px; */
  width: 50px;

}
#btn_search {
  position: relative;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  right: 50px;
  /* border-top-right-radius: 30px;
    border-bottom-right-radius: 30px; */
  // background-color: whitesmoke;
  z-index: 999;
}

.btn-search-container {
  position: relative;
  font-size: 0;
  white-space: nowrap;
  /* right: 50px;
    z-index: 2; */
}

.gap-1 {
  gap: 1rem;
}

@import 'components/tooltip';
