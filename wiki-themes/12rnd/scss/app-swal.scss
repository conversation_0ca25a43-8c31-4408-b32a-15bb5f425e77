.swal2-container {
  .swal2-html-container {
    font-size: 14px;
    line-height: 1.25;
    margin: 0px;
  }
  .swal2-popup {
    padding: 17px;
  }
  fieldset {
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    border-top: 1px solid lightgrey;
    margin: 2px;
    padding: 10px;

    label {
      font-weight: 400;
    }
    legend {
      padding-left: 5px;
      padding-right: 5px;
      font-weight: bold;
      width: auto;
      padding: inherit;
      margin-bottom: auto;
      font-size: 15px;
      line-height: 1px;
      border: none;
      border-bottom: none;
    }
  }
  .swal2-close {
    margin: 0px !important;
    position: absolute;
    background-color: #fff;
    font-family: serif;
  }
}

.upload-dialog {
  .custom-dz {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    background-color: #f5f5f5;
    border: 2px dashed #ccc;
    cursor: pointer;
    margin-bottom: 1rem;
  }

  .upload-status-list {
    display: flex;
    flex-direction: column;
    gap: 3px;
  }

  .upload-status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #ccc;
    background-color: #fff;
  }
}


