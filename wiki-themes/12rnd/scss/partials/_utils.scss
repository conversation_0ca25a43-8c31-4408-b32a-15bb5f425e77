/* -------------------------------------------------------------------------- */
/*                                   Mixins                                   */
/* -------------------------------------------------------------------------- */

// Breakpoints map for utility classes
$breakpoints: (
    xs: 576px,
    sm: 768px,
    md: 992px,
    lg: 1200px,
    xl: 1400px
);

@mixin respond-above($breakpoint) {

    // If the breakpoint exists in the map.
    @if map-has-key($breakpoints, $breakpoint) {
        // Get the breakpoint value.
        $breakpoint-value: map-get($breakpoints, $breakpoint);
        // Write the media query.
        @media (min-width: $breakpoint-value) {
            @content;
        }
        // If the breakpoint doesn't exist in the map.
    }

    @else {
        // Log a warning.
        @warn 'Invalid breakpoint: #{$breakpoint}.';
    }
}

@mixin respond-below($breakpoint) {
    // If the breakpoint exists in the map.
    @if map-has-key($breakpoints, $breakpoint) {
        // Get the breakpoint value.
        $breakpoint-value: map-get($breakpoints, $breakpoint);
        // Write the media query.
        @media (max-width: ($breakpoint-value - 1)) {
            @content;
        }
        // If the breakpoint doesn't exist in the map.
    }

    @else {
        // Log a warning.
        @warn 'Invalid breakpoint: #{$breakpoint}.';
    }
}

.d-none {
    display: none;
}

.relative {
    position: relative;
}

.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden;
}

.justify- {
    &between {
        justify-content: space-between;
    }
    &center {
        justify-content: center;
    }
    &around {
        justify-content: space-around;
    }
    &end {
        justify-content: flex-end;
    }
    &stretch {
        justify-content: stretch;
    }
}

.align- {
    &center {
        align-items: center;
    }
    &start {
        align-items: flex-start;
    }
    &end {
        align-items: flex-end;
    }
    &stretch {
        align-items: stretch;
    }
}

.align-self-center {
    align-self: center;
}

.text-right {
    text-align: right;
}

.text-start {
    text-align: start;
}

.text-end {
    text-align: end;
}

.break-all {
    word-break: break-all;
}

.flex-column {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.align-items-center {
    align-items: center;
}

.align-items-start {
    align-items: flex-start;
}

.flex-wrap {
    flex-wrap: wrap;
}
.flex-end {
    justify-content: flex-end;
}
.basis-100 {
    flex-basis: 100%;
}
.basis-0 {
    flex-basis: 0;
}

.ml-auto {
    margin-left: auto;
}

.mr-auto {
    margin-right: auto;
}

.w-fit {
    width: fit-content;
}

.w-px {
    width: 1px;
}

.h-px {
    height: 1px;
}

.w-full {
    width: 100%;
}

.w-auto {
    width: auto;
}

.h-full {
    height: 100%;
}

.h-auto {
    height: auto;
}

.flex-auto {
    flex: 1 1 auto;
}

.max-w-xl {
    max-width: 80rem;
}

.min-w-max {
    min-width: max-content;
}

@include respond-below(sm) {
    .sm\: {
        &flex-column {
            flex-direction: column;
        }
        &d-none {
            display: none;
        }
        &w-min {
            width: min-content;
        }
    }
}

@include respond-below(md) {
    .md\: {
        &flex-column {
            flex-direction: column;
        }

        &justify-start {
            justify-content: flex-start;
        }

        &justify-center {
            justify-content: center;
        }

        &align-start {
            align-items: flex-start;
        }
    }
}

// ─── Max Width ───────────────────────────────────────────────────────────────

@mixin generate-max-width-classes($breakpoints) {
    @each $breakpoint, $value in $breakpoints {
        .max-w-#{$breakpoint} {
            max-width: $value;
        }
    }
}


// ─── Css Grid ────────────────────────────────────────────────────────────────

@mixin generateGridClasses($max: 6) {
    $grid-properties: (
        cols: grid-template-columns,
        rows: grid-template-rows,
    );

    @each $type, $property in $grid-properties {
        @for $i from 1 through $max {
            .grid-#{$type}-#{$i} {
                #{$property}: repeat($i, minmax(0, 1fr));
            }
        }
    }
}

.d-grid {
    display: grid;
}

@mixin generate-spacing-classes {
    $space-map: (
        m: margin,
        p: padding,
    );
    $direction-map: (
        r: right,
        l: left,
        t: top,
        b: bottom,
    );

    @each $type, $css-type in $space-map {
        @each $direction, $css-direction in $direction-map {
            @for $i from 1 through 16 {
                .#{$type}#{$direction}-#{$i} {
                    #{$css-type}-#{$css-direction}: #{$i * 0.25}rem;
                }
            }
        }
        @for $i from 1 through 16 {
            .#{$type}-#{$i} {
                #{$css-type}: #{$i * 0.25}rem;
            }
        }
    }
}

@mixin generate-flex-gap-classes {
    @for $i from 0 through 12 {
        @if $i == 0 {
            .flex-gap-#{$i} {
                gap: 0;
            }
        } @else {
            .flex-gap-#{$i} {
                gap: #{$i * 0.25}rem;
            }
        }
    }

    // Additional variants for row and column gaps
    @each $type in (row, column) {
        @for $i from 0 through 12 {
            @if $i == 0 {
                .flex-#{$type}-gap-#{$i} {
                    #{$type}-gap: 0;
                }
            } @else {
                .flex-#{$type}-gap-#{$i} {
                    #{$type}-gap: #{$i * 0.25}rem;
                }
            }
        }
    }
}

@mixin generate-directional-spacing-classes {
    $space-map: (
        m: margin,
        p: padding,
    );

    $direction-map: (
        y: (
            top,
            bottom,
        ),
        x: (
            left,
            right,
        ),
    );

    @each $type, $css-type in $space-map {
        @each $axis, $directions in $direction-map {
            @for $i from 0 through 16 {
                .#{$type}#{$axis}-#{$i} {
                    #{$css-type}-#{nth($directions, 1)}: #{$i * 0.25}rem;
                    #{$css-type}-#{nth($directions, 2)}: #{$i * 0.25}rem;
                }
            }
        }
    }
}

@include generateGridClasses; // max: 6; grid-cols-1, grid-rows-3, etc...
@include generate-directional-spacing-classes;
@include generate-flex-gap-classes;
@include generate-max-width-classes($breakpoints);
@include generate-spacing-classes; // ml-2, mt-4, pr-12, pb-1, etc..

.single-line {
    display: inline-block;
    max-width: 100%; /* or set a specific width like 300px */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle; /* optional, aligns nicely with text */
}

.cursor-pointer {
    cursor: pointer;
}