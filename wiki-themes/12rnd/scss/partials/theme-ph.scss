/* -------------------------------------------------------------------------- */
/*                                   Sizings                                  */
/* -------------------------------------------------------------------------- */

$border_radius-xs: 2px;
$border_radius-s: 4px;
$border_radius-m: 6px;
$border_radius-lg: 8px;
$border_radius-xl: 12px;

$base-card-padding: 12px;
$inner-card-padding: 4px;
$card-padding: $base-card-padding + $inner-card-padding;

$card-border-radius: $border-radius-m;
$border-radius-input: $border-radius-m;

/* -------------------------------------------------------------------------- */
/*                                   Colors                                   */
/* -------------------------------------------------------------------------- */

$surface-25: #f9fbfe;
$surface-50: #f4f8fb;
$surface-75: #eff5f8;
$surface-100: #e4eff5;
$surface-200: #cbe3ec;

$accent-blue-10: #f7fbff;
$accent-blue-25: #eef7ff;
$accent-blue-50: #e5f5ff;
$accent-blue-75: #def0ff;
$accent-blue-100: #ccebff;
$accent-blue-150: #b0e0ff;
$accent-blue-200: #94d4ff;
$accent-blue-300: #61c0ff;
$accent-blue-400: #2eabff;
$accent-blue-500: #2196f3;
$accent-blue-600: #0077c7;
$accent-blue-700: #005994;

$accent-color: $accent-blue-500;

$slate-25: #fcfdfe;
$slate-50: #f8fafc;
$slate-75: #f4f7fa;
$slate-100: #f1f5f9;
$slate-150: #edf2f7;
$slate-200: #e2e8f0;
$slate-300: #cbd5e1;
$slate-350: #b0bfcc;
$slate-400: #94a3b8;
$slate-450: #7c8ba2;
$slate-500: #64748b;
$slate-600: #475569;
$slate-700: #334155;
$slate-800: #1e293b;

$red-50: #fff6f6;
$red-75: #ffecec;
$red-100: #fee2e2;
$red-200: #fecaca;
$red-300: #fca5a5;
$red-400: #f87171;
$red-500: #ef4444;
$red-600: #dc2626;
$red-700: #b91c1c;
$red-800: #952020;
$red-900: #7f1d1d;

// $rose-50: oklch(0.969 0.015 12.422);
// $rose-100: oklch(0.941 0.03 12.58);
// $rose-200: oklch(0.892 0.058 10.001);
// $rose-300: oklch(0.81 0.117 11.638);
// $rose-400: oklch(0.712 0.194 13.428);
// $rose-500: oklch(0.645 0.246 16.439);
// $rose-600: oklch(0.586 0.253 17.585);
// $rose-700: oklch(0.514 0.222 16.935);
// $rose-800: oklch(0.455 0.188 13.697);
// $rose-900: oklch(0.41 0.159 10.272);
// $rose-950: oklch(0.271 0.105 12.094);

$orange-25: #fffaf7;
$orange-50: #fff5ed;
$orange-100: #ffeee0;
$orange-200: #ffe2ca;
$orange-250: #ffd4b7;
$orange-300: #ffc5a3;
$orange-400: #ffa97c;
$orange-500: #f98c3e;
$orange-600: #f07c22;
$orange-700: #e66c06;
$orange-800: #b55404;
$orange-900: #a64800;

$amber-50: #fffbeb;
$amber-100: #fef3c7;
$amber-200: #fde68a;
$amber-300: #fde047;
$amber-400: #fbbf24;
$amber-500: #f59e0b;
$amber-600: #d97706;
$amber-700: #b45309;

$green-25: #f7fcf7;
$green-50: #f0faf0;
$green-100: #d7f3bf;
$green-200: #bbebb1;
$green-300: #90d293;
$green-400: #7cc57f;
$green-500: #69b86c;
$green-600: #3c9a40;
$green-700: #15803d;
$green-800: #166534;

$yellow-50: #fefce8;
$yellow-75: #fefbd6;
$yellow-100: #fef9c3;
$yellow-200: #fef08a;
$yellow-600: #ca8a04;

// ─── Colors - Config ─────────────────────────────────────────────────────────

$border-color: $surface-100;
$primary-text-color: $slate-600;
$secondary-text-color: $slate-400;
$input-border-color: $slate-200;
$container-color: $slate-75;
$icon-color: $slate-500;

/* -------------------------------------------------------------------------- */
/*                                Global Config                               */
/* -------------------------------------------------------------------------- */

$natural-shadow-lg:
    0px 1px 2px 0px rgba(175, 178, 206, 0.45),
    1px 4px 10px -1px rgba(62, 78, 87, 0.12),
    1px 3px 16px -3px rgba(62, 78, 87, 0.05);
$natural-shadow-md:
    0px 0px 2px 0px rgba(175, 178, 206, 0.5),
    1px 4px 9px -1px rgba(62, 78, 87, 0.16);
$natural-shadow-sm: 1px 2px 8px 0px rgba(62, 78, 87, 0.07);
$natural-shadow-xs:
    0px 0px 2px 0px rgba(175, 178, 206, 0.5),
    2px 3px 7px -1px rgba(62, 78, 87, 0.08);

$input-shadow: 1px 2px 4px rgba(62, 78, 87, 0.06);

/* -------------------------------------------------------------------------- */
/*                                Global Styles                               */
/* -------------------------------------------------------------------------- */

.ph-hr {
    width: 100%;
    margin-bottom: 0;
}

.ph-hr-vertical {
    height: 100%;
    width: 1px;
}

.ph-hr-vertical,
.ph-hr {
    border-color: $border-color;
    background-color: $border-color;
    margin-top: 0;
}

.ph-secondary-text-color {
    color: $secondary-text-color;
}

.ph-icon-color {
    color: $icon-color;
}

.ph-tooltip {
    color: $slate-300;
    cursor: pointer;
    width: 14px;
    height: 14px;
    content: url("../images/help-icon.svg");
}

.ph-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid $accent-blue-400;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

// One off UI updates (only one instance of this classes were found; safe to globally update UI with no side effects)

.bootstrap-tagsinput.bootstrap-tagsinput {
    border: 1px solid $input-border-color !important;
    border-radius: $border-radius-input;
    box-shadow: $input-shadow !important;
    gap: 2px;

    .tag.label {
        padding: 4px 8px;
        display: flex;
        align-items: center;
        border-radius: $border_radius-s;
        font-size: 12px;
        font-weight: 600;
    }

    .label-info {
        background-color: $accent-blue-100;
        color: $accent-blue-500;
        border: 1px solid $accent-blue-200;
    }
}

// ─── Table ───────────────────────────────────────────────────────────────────

.ph-table.table {
    th,
    td {
        border-bottom: 1px solid $border-color !important;
    }

    tbody tr:last-child {
        th {
            border-bottom: none !important;
        }
    }

    th {
        color: $slate-500;
        text-align: left;

        &:nth-child(1) {
            white-space: nowrap;
        }

        &.sorting {
            &::after {
                color: $slate-450;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }

    td {
        color: $slate-500;
        border-top: none;
    }

    &.table-bordered {
        border: 1px solid $border-color;

        thead tr th {
            border: 1px solid $border-color;
        }
    }
}

.ph-table-body.ph-table-body {
    overflow-y: auto;
    border-bottom: 1px solid $border-color;

    &:has(+ .ph-table-footer) {
        table tbody {
            tr:last-child {
                td {
                    border-bottom: none !important;
                }
            }
        }
    }
}

.ph-table-footer {
    .dataTables_info.dataTables_info {
        color: $slate-400;
        padding-top: 0;
        font-size: 14px;
        font-weight: 600;
    }

    .dataTables_paginate {
        display: flex;
        width: 100%;
        justify-content: end;
    }

    .dataTables_paginate.dataTables_paginate {
        li {
            &.active {
                a {
                    color: $accent-blue-500;
                    background-color: $accent-blue-50;
                }
            }

            &.disabled {
                opacity: 0.6;
                cursor: default;

                a {
                    color: $slate-450;

                    &:hover {
                        background-color: transparent;
                        color: $slate-450;
                    }
                }
            }

            a {
                border-radius: 4px;
                font-weight: 600;
                color: $slate-450;
                font-size: 14px;
                height: auto;
                margin: 0 2px;
            }

            &:not(.disabled) {
                a {
                    &:hover {
                        background-color: $accent-blue-25 !important;
                        color: $accent-blue-500;
                    }
                }
            }

            &:not(:first-child):not(:last-child) a {
                margin: 0 2px;
            }
        }

        .page-results {
            font-size: 14px;
            color: $slate-450;
        }
    }
}

.ph-table-container {
    padding: 0.5rem 0;
    border-radius: $border_radius-m;
    border: 1px solid $border-color;
    background-color: white;
    display: flex;

    & > div {
        width: 100%;
    }

    &.--borderless {
        border: none;
    }

    .dataTables_wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 1rem;
        overflow-y: hidden;
    }
}

.ph-table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-top: auto;
}

// ─── Card ────────────────────────────────────────────────────────────────────

.ph-card.ph-card {
    border-radius: $border_radius-lg !important;
    box-shadow: $natural-shadow-xs !important;
    border: none !important;
    overflow: inherit;
    background-color: white;

    .ph-card_body {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
}

.ph-card_header {
    color: $primary-text-color;
    padding: 8px 12px;
    border-bottom: 1px solid $border-color;

    h3 {
        font-size: 14px;
        color: $slate-400;
        font-weight: 500;
        margin-bottom: 0;
    }

    &.--header-surface {
        background-color: $slate-50;
        border-bottom: 1px solid $border-color;
        display: flex;
        align-items: start;
        justify-content: space-between;
        padding: $base-card-padding;
        gap: 4rem;

        h3 {
            margin: auto 0;
        }
    }
}

.ph-card_body {
    padding: $base-card-padding $card-padding;
}

// ─── Typography ──────────────────────────────────────────────────────────────

.ph_text,
.ph-text.ph-text {
    &.text-danger {
        color: $red-600;
    }

    .text-muted {
        color: $slate-400 !important;
    }

    .text-secondary {
        color: $slate-450;
    }
}

.ph-text-label.ph-text-label.ph-text-label {
    font-size: 12px;
    color: $slate-450;
    font-weight: 500;
}

// ─── Alert Box ───────────────────────────────────────────────────────────────

.alert {
    border-radius: 5px;

    &.alert-info {
        background-color: #ecf6fc;
        color: #28244c;
    }

    &.alert-danger {
        background-color: #fcecf4;
        color: #47464f;
    }

    &.alert-warning {
        background-color: #fff3cd;
        color: #4e4f46;
    }
}

.ph-alert {
    border-radius: 6px;
    border: 1px solid $slate-200;
    font-weight: 500;
    padding: 0;
    overflow: hidden;
    white-space: wrap;
    display: flex;
    flex-direction: column;
    background-color: $slate-75;
    color: $slate-500;

    .ph-alert__description {
        font-size: 14px;
        color: $slate-500;
        text-align: start;

        &.--no_title {
            border-top: none !important;
        }
    }

    &.--borderless {
        border: none;
    }

    &.--size-sm {
        .ph-alert__description {
            font-size: 12px;
            font-weight: 500;
            padding: 6px 12px;
        }
    }

    &.ph-alert_slim {
        min-width: 8rem;

        .ph-alert__title {
            gap: 2px;
            flex-direction: column;
            text-align: center;
            font-size: 12px;
            padding: 4px 8px;

            .__title-icon {
                display: none;
            }
        }
    }

    &.ph-alert_info {
        background-color: $accent-blue-50;
        color: $accent-blue-700;
        border-color: $accent-blue-300;

        .ph-alert__title {
            background-color: $accent-blue-100;
            color: $accent-blue-700;
        }

        .ph-alert__description {
            border-top: 1px solid $accent-blue-300;
            color: $accent-blue-700;
        }

        .__title-icon.--icon-warning {
            display: none;
        }
    }

    &.ph-alert_danger {
        background-color: $red-50 !important;
        color: $red-800;
        border-color: $red-200;

        .ph-alert__title {
            background-color: $red-100;
            color: $red-700;
        }

        .ph-alert__description {
            border-top: 1px solid $red-200;
            color: $red-700;
        }

        .icon-fill-primary {
            fill: $red-400;
        }

        .icon-stroke-primary {
            stroke: $red-400;
        }

        .__title-icon.--icon-info {
            display: none;
        }
    }

    &.ph-alert_warning {
        background-color: $orange-50;
        color: $orange-800;
        border-color: $orange-200;

        .ph-alert__title {
            background-color: $orange-100;
            color: $orange-800;
        }

        .ph-alert__description {
            border-top: 1px solid $orange-200;
            color: $orange-800;
        }

        .icon-fill-primary {
            fill: $orange-400;
        }

        .icon-stroke-primary {
            stroke: $orange-400;
        }

        .__title-icon.--icon-info {
            display: none;
        }
    }

    &.ph-alert_success {
        background-color: $green-50;
        color: $green-800;
        border-color: $green-200;

        .ph-alert__title {
            background-color: $green-100;
            color: $green-800;
        }

        .ph-alert__description {
            border-top: 1px solid $green-200;
            color: $green-800;
        }

        .icon-fill-primary {
            fill: $green-400;
        }

        .icon-stroke-primary {
            stroke: $green-400;
        }

        .__title-icon.--icon-info {
            display: none;
        }
    }

    .ph-alert__title {
        font-weight: 600;
        padding: 6px 12px;
        display: flex;
        gap: 6px;
        justify-content: start;
        align-items: center;
        font-size: 14px;
        background-color: $slate-150;
        border-bottom: 1px solid $slate-200;

        .__title-icon,
        .material-icons {
            display: inline-flex;
            width: 16px;
            height: 16px;
            font-size: 18px;
            flex-shrink: 0;
            align-items: center;
        }
    }

    .ph-alert__description {
        padding: 8px 12px;
    }

    p {
        margin: 0;
        margin-bottom: 5px;
    }
}

// ─── Badge ───────────────────────────────────────────────────────────────────

.ph-badge {
    display: inline-flex;
    padding: 2px 8px;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 600;
    color: $slate-450;
    border-radius: 4px;
    border: 1px solid $border-color;
    background-color: $slate-75;
    line-height: 1.3em;

    .material-icons {
        font-size: 16px;
        opacity: 0.75;
    }

    &.ph-badge--small {
        font-size: 10px;
        padding: 2px 6px;
    }

    &.ph-badge--xsmall {
        padding: 2px 8px;
        background-color: $slate-75;
        border: none;
        font-weight: 600;
        min-height: 20px;
        line-height: 0.8em;

        i {
            font-size: 10px; // for icons
        }
    }

    &.badge-blue {
        background-color: $accent-blue-50;
        border: 1px solid $accent-blue-300;
        color: $accent-blue-500;
    }

    &.badge-green {
        border: 1px solid $green-400;
        background-color: $green-100;
        color: $green-600;
    }

    &.badge-yellow {
        border: 1px solid $amber-500;
        background-color: $amber-100;
        color: $amber-600;
    }

    &.badge-orange {
        border: 1px solid $orange-400;
        background-color: $orange-100;
        color: $orange-800;
    }

    &.badge-orange--old {
        background-color: #ffe2ca;
        color: #e66c06;
    }

    &.badge-amber {
        border: 1px solid $amber-400;
        background-color: $amber-100;
        color: $amber-600;
    }

    &.badge-red {
        border: 1px solid $red-300;
        background-color: $red-100;
        color: $red-600;
    }

    // &.badge-red-light {
    //     border: 1px solid $rose-300;
    //     background-color: $rose-100;
    //     color: $rose-600;
    // }

    &.badge-light {
        border: 1px solid $slate-200;
        background-color: white;
        color: $slate-400;
    }

    &.ph-badge--borderless {
        border: none;
    }

    &.ph-badge--bgless {
        background-color: transparent;
    }

    &.ph-badge--opaque {
        opacity: 0.8;
    }
}

// ─── Quote Block ────────────────────────────────────────────────────────────

.ph-quote-block {
    display: block;
    background-color: $slate-50;
    border-left: 3px solid $slate-150;
    padding: 4px 10px;
    color: $slate-450;
    font-size: 14px;
    font-weight: 500;
}

// ─── Form Inputs ─────────────────────────────────────────────────────────────

.ph-form.ph-form {
    input[type='text'],
    input[type='tel'],
    textarea,
    .input-group .multiple_emails-container,
    select {
        border-radius: $border-radius-input !important;
        border-color: $input-border-color !important;
        border: 1px solid $input-border-color !important;
        color: $slate-500;
        box-shadow: $input-shadow !important;
        min-height: 34px;

        &:focus {
            border-color: $accent-blue-500 !important;
            outline: 1px solid $accent-blue-500 !important;
        }

        &::placeholder {
            color: $slate-350;
        }
    }

    button {
        min-height: 30px;
    }

    .form-control {
        font-size: 14px;
        color: $slate-500;
        cursor: pointer;
    }

    label {
        color: $slate-400;
        font-size: 14px;
        font-weight: 500;
    }

    h6 {
        color: $slate-500;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    small {
        color: $slate-450;
    }

    .dropdown-toggle {
        box-shadow: $input-shadow !important;
        border: 1px solid $input-border-color;
    }

    .multiple_emails-container {
        border: 1px solid $input-border-color !important;
        box-shadow: $input-shadow !important;
        padding: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: $border-radius-input;

        .multiple_emails-email {
            margin: 0;
            width: 100%;
            border-radius: $border_radius-s !important;
            border: none;
            outline: 1px solid $accent-blue-150;
        }

        .multiple_emails-ul {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            width: 100%;

            &:has(li) + input {
                margin-top: 6px;
            }
        }

        .multiple_emails-input.multiple_emails-input {
            border: none !important;
            min-height: 32px;
            box-shadow: none !important;
            color: $slate-600;
            height: 24px;
            min-height: 24px;
            border-radius: $border_radius-s !important;
        }
    }
}

.ph-form-check {
    margin-bottom: 0.5rem;

    input[type='radio'] + label.ph-form-check-label {
        padding-left: 2.6rem !important;
        font-size: 13px;
        font-weight: 600;
        color: $slate-500;
    }
}

.ph-form.ph-form .ph-form-group select,
.ph-form-group select {
    text-indent: 6px !important;
    padding-right: 40px !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-image: url('../images/chevron-down.svg') !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 8px !important;
}

.ph-checkbox-accent[type='checkbox'] {
    accent-color: $accent-blue-500;
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border: 1px solid $slate-200;
    border-radius: 3px;
    background-color: white;
    position: relative;
    cursor: pointer;

    // &:checked {
    //     background-color: $accent-blue-500;
    //     border-color: $accent-blue-500;

    //     &::after {
    //         content: '✓';
    //         position: absolute;
    //         top: 50%;
    //         left: 50%;
    //         transform: translate(-50%, -50%);
    //         color: white;
    //         font-size: 12px;
    //         font-weight: bold;
    //     }
    // }
}

.ph-checkbox-text {
    cursor: pointer;
}

// ─── Buttons ─────────────────────────────────────────────────────────────────

.ph-btn.ph-btn {
    padding: 4px 12px;
    width: fit-content;
    font-size: 14px;
    border-radius: $border_radius-m;
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
    transition: background-color 0.25s ease-out;

    a {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;

        i {
            margin-right: 0;
        }
    }

    .material-icons {
        font-size: 14px;
    }

    &.btn-primary {
        background-color: $accent-blue-500;
        border: 1px solid rgba(0, 119, 199, 0.5);

        &:hover {
            background-color: $accent-blue-400;
        }
    }

    &.ph-btn-warning {
        background-color: $amber-100;
        color: $amber-700;
        border: 1px solid $amber-300 !important;

        i {
            color: $amber-700 !important;
        }

        &:hover {
            background-color: $amber-200;
        }
    }

    &.btn-default.btn-default {
        &:hover {
            color: $slate-600;
        }

        a {
            color: $slate-450;
        }

        &.btn-outline {
            border: 1px solid $slate-200;
            color: $slate-500;

            &:hover {
                border: 1px solid $slate-300;
            }
        }
    }

    &.ph-btn-sm.ph-btn-sm {
        padding: 4px 8px;
        font-size: 12px;
        gap: 6px;

        i {
            font-size: 12px;
        }
    }

    &.ph-btn-xs.ph-btn-xs {
        $horizontal-padding: 10px;
        color: $slate-450;
        font-size: 12px;
        font-weight: 500;
        padding: 2px $horizontal-padding;

        i {
            font-size: 11px;
            font-weight: 600;
            color: $slate-400;
        }

        &.offset-internal-padding {
            &-right {
                margin-right: -$horizontal-padding;
            }
        }
    }

    &.ph-btn-md {
        padding: 4px 8px;
        font-size: 14px;
        gap: 6px;

        i {
            font-size: 14px;
            margin-right: 0;
        }
    }

    &.btn-secondary {
        &:focus,
        &:hover {
            box-shadow: none !important;
        }
    }

    &.btn-outline {
        border: 1px solid $slate-200;
        color: $slate-500;

        &.ph-btn-accent-hover:hover {
            background-color: $accent-blue-50;
            border: 1px solid $accent-blue-300;

            i {
                color: $accent-blue-500;
            }
        }

        &.btn-warning.btn-warning {
            background-color: transparent !important;
            border: 1px solid $amber-300;

            i {
                color: $amber-600;
            }

            &:hover {
                background-color: $amber-50 !important;
            }
        }

        &:hover {
            border: 1px solid $slate-300;
        }
    }

    &.ph-btn-ghost {
        background-color: transparent;
        border: none;
        color: $slate-400;
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover,
        &:focus {
            color: $slate-450;
            background-color: $slate-50;
            text-decoration: none;
        }
    }

    &.ph-btn-accent-hover:hover {
        color: $accent-blue-500;
    }

    &.ph-btn-success {
        background-color: $green-100;
        color: $green-700;
        border: 1px solid $green-300 !important;

        i {
            color: $green-700 !important;
        }

        &:hover {
            background-color: $green-200;
        }
    }

    &.btn-danger.btn-danger {
        transition: background-color 0.25s ease-out;
        color: $red-500 !important;

        &.btn-surface {
            background-color: $red-50 !important;
            border: 1px solid $red-300;
            color: $red-500;

            &:hover {
                background-color: $red-100 !important;
            }
        }

        a {
            &.remove-user {
                i {
                    color: $red-400;
                }
            }
        }

        &.btn-outline {
            border: 1px solid $red-300 !important;
        }

        &:hover {
            background-color: $red-50 !important;
        }
    }
}

// ─── Filters ─────────────────────────────────────────────────────────────────

.ph-filters-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: start;
    margin-bottom: 1rem;

    .filter-grp {
        border: 1px dashed $slate-300;
        color: $slate-450;
        width: fit-content;

        button {
            color: $accent-blue-400;
        }
    }

    &.isLoading {
        margin-top: 24px !important;
    }
}

.isLoading {
    .use-skeleton {
        &.filter-grp {
            width: 8rem;
            height: 2rem;
            border-radius: 1rem !important;
        }
    }

    // .ph-filters-container {
    //     margin-top: 24px !important;
    // }
}

// ─── Page ──────────────────────────────────────────────────────────────────────

.ls-closed {
    .ph-page {
        margin-left: 0 !important;
    }
}

.inner-scroll-page {
    position: fixed;
    height: 100%;
    width: 100%;
}

.stage-environment {
    .ph-page.ph-page {
        min-height: calc(100vh - 70px - 36px);
    }
}

.ph-page.ph-page {
    margin-top: 70px;
    margin-right: 0 !important;
    height: 100%;
    background-color: white;
    color: $slate-500;
    overflow-y: auto;
    margin-left: 300px;
    padding: 16px 32px;
    min-height: calc(100vh - 70px);
    transition: margin-left 0.45s cubic-bezier(0.2, 1.31, 0.58, 0.96); // Custom easeOutBack from https://easings.net/ then modified in https://cubic-bezier.com/#.2,1.31,.58,.96

    hr {
        border-top: 1px solid $border-color;
    }

    &.smallSidebarContent {
        margin-left: 70px !important;
        transition: margin-left 0.75s cubic-bezier(0.19, 1, 0.22, 1); // the curve is easeOutExpo from https://www.cssportal.com/css-cubic-bezier-generator/
    }

    .content {
        margin-right: 0;
        padding: 32px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        padding-bottom: 32px;
        background-color: white;

        &.--compact {
            padding-bottom: 8px;
        }
    }

    .body {
        height: 100%;
        margin-top: 2rem;
        overflow-y: hidden;
    }

    .block-header {
        padding: 0;
        margin-bottom: 1rem;

        h2 {
            color: $slate-450 !important;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
        }
    }

    .breadcrumbs-container {
        padding: 0 1rem;
    }
}

.ph-block-header {
    h2 {
        color: $slate-450 !important;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        margin-top: 0;
        margin-bottom: 1rem;
    }
}

// ─── Horizontal Range Bar ────────────────────────────────────────────────────
$range-bar-height: 4px;

.ph-range-bar_container {
    position: relative;
    width: 100%;
    height: $range-bar-height;
    background-color: $slate-100;
    border-radius: $range-bar-height;

    .ph-range-bar_active {
        position: absolute;
        height: 100%;
        background-color: $slate-300;
        border-radius: $range-bar-height;
    }

    .ph-range-bar_goal {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        width: $range-bar-height + 6px;
        height: $range-bar-height + 6px;
        background-color: $green-400;
        border-radius: 50%;
        border: 2px solid white;
    }
}

.ph-range-bar_label {
    font-size: 10px;
    color: $slate-450;
}

// ─── Select Dropdown ────────────────────────────────────────────────────────

.ph-select-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.ph-select-label {
    font-size: 14px;
    margin-bottom: 0;
    color: $slate-450;
}

.ph-select {
    position: relative;
    min-width: 64px;
    user-select: none;
}

.ph-select-picker.ph-select-picker {
    .btn:not(.btn-link):not(.btn-circle).btn-default.dropdown-toggle {
        border-color: $input-border-color;
    }

    .select2-selection {
        color: $slate-500;
        border-bottom: 1px solid $input-border-color;
    }

    .select2-selection--multiple {
        padding: 0 !important;

        ul {
            margin-bottom: 0;
        }
    }

    .select2-container--focus {
        .select2-selection--multiple {
            border-bottom: 1px solid $accent-blue-400;
            box-shadow: 0 1px 0 0 $accent-blue-400;
        }
    }

    input.select2-search__field {
        color: $slate-600;
    }
}

.ph-select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 10px;
    padding-right: 4px;
    border: 1px solid $border-color;
    border-radius: $border_radius-m;
    background: white;
    cursor: pointer;
    font-size: 14px;
    color: $slate-500;

    .material-icons {
        font-size: 18px;
        color: $slate-350;
    }
}

.ph-select.opened .ph-select-trigger {
    border-color: $accent-blue-500;
    box-shadow: 0 0 0 1px $accent-blue-200;
}

.ph-select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid $slate-200;
    border-top: none;
    border-radius: 0 0 $border_radius-m $border_radius-m;
    box-shadow: $natural-shadow-sm;
    display: none;
    z-index: 10;
    max-height: 200px;
    overflow-y: auto;
}

.ph-select.opened .ph-select-options {
    display: block;
}

.ph-select-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    color: $slate-500;
    transition: background-color 0.2s ease;
}

.ph-select-option:hover {
    background: $slate-50;
}

.ph-select-option.selected {
    font-weight: 600;
    color: $accent-blue-400;
    background: $accent-blue-25;
}

.ph-select-value {
    color: $accent-blue-400;
    font-weight: 600;
}

// ─── Searchbar Header ───────────────────────────────────────────────────────

.ph-searchbar-header {
    .searchbar-header,
    &.searchbar-header {
        background-color: $slate-50;
        border-radius: $border_radius-m;

        button {
            transition: background-color 0.2s ease;
            color: $primary-text-color !important;

            span {
                color: $slate-500;
                transition: color 0.2s ease;
            }

            i {
                color: $slate-400;
                transition:
                    color,
                    transform 0.25s ease;
            }

            &:hover {
                span {
                    color: $slate-600;
                }

                i {
                    color: $slate-450;
                }
            }
        }
    }

    .search-container-wrapper {
        max-width: 26rem !important;
    }

    .search-container {
        margin-left: auto;

        input.search-bar {
            border-radius: $border_radius-m !important;
            border: 1px solid $border-color;
            color: $slate-600;
            // color: red;

            &::placeholder {
                color: $slate-400;
            }

            &::-webkit-search-cancel-button {
                -webkit-appearance: none;
                cursor: pointer;
                height: 12px;
                width: 12px;
                margin-right: 8px;
                opacity: 0.75;

                background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>');
            }
        }

        .material-icons {
            cursor: pointer;
            background-color: $slate-50;
            height: 100%;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            right: 0;
            border-radius: 0 $border_radius-m $border_radius-m 0;
            border: 1px solid $border-color;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: $slate-100;
            }
        }
    }
}

// ─── Scrollbar ───────────────────────────────────────────────────────────────

.ph-scrollbar,
.enable-scroll {
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-gutter: auto;
    scrollbar-color: $slate-200 transparent;

    /* WebKit browsers (Chrome, Safari, Edge) */
    &::-webkit-scrollbar {
        width: 12px;
        height: 12px;
    }

    &::-webkit-scrollbar-track {
        background: white;
        /* White track */
        border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #e2e8f0;
        /* Thumb color */
        border-radius: 6px;
        border: 3px solid white;
        /* Space around thumb matching track color */
    }

    &::-webkit-scrollbar-thumb:hover {
        background-color: #c9ccd1;
        /* Darker thumb on hover */
    }
}

// ─── Icons ───────────────────────────────────────────────────────────────────

.icon-primary,
.icon-secondary {
    .icon-stroke--none {
        stroke: none;
    }

    .icon-fill--none {
        fill: none;
    }
}

// ─── Nav Tabs ────────────────────────────────────────────────────────────────

$tab-border-color: $surface-75;

.ph-nav-tabs.ph-nav-tabs {
    border-bottom: 2px solid $tab-border-color !important;

    li {
        a {
            color: $slate-450 !important;
        }
    }

    .hide-active-bar {
        a {
            &::before {
                transition: none !important;
            }
        }

        &.active {
            a {
                &::before {
                    border-color: $tab-border-color !important;
                    transition: none;
                    transform: none;
                }
            }
        }
    }
}

// ─── Switch Lever ────────────────────────────────────────────────────────────

// Editor switch styling
.ph-switch {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: $slate-600;

    input[type='checkbox'].ph-switch-input {
        position: relative;
        width: 40px;
        height: 20px;
        appearance: none;
        background: $slate-200;
        border-radius: 10px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:checked {
            background: $accent-blue-500;
        }

        &::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
        }

        &:checked::before {
            transform: translateX(20px);
        }
    }

    .lever {
        display: none; // Hide the old lever if it exists
    }
}

// ─── Dialog ──────────────────────────────────────────────────────────────────

.ph-dialog-v2 {
    color: $slate-600;

    .ph-faux-dialog-footer {
        display: flex;
        justify-content: end;
        gap: 1rem;
        border-top: 1px solid $border-color;
        padding: 1rem;

        button {
            width: fit-content;
        }

        @include respond-below(sm) {
            flex-direction: column;

            button {
                width: 100%;
            }
        }
    }
}

.swal2-container.swal2-container {
    .swal2-actions,
    .ph-faux-dialog-footer {
        button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1em;

            i.fa {
                font-size: 1.5rem;
            }

            &.btn-secondary {
                background-color: $slate-100;
                color: $slate-500;

                &:hover {
                    background-color: $slate-200;
                }
            }
        }
    }
}

.ph-dialog-v2-body {
    padding: 2.25rem 2rem;
}

// ─── Form Fieldset ───────────────────────────────────────────────────────────

.ph-fieldset {
    border: 1px solid $border-color;
    border-radius: $border_radius-m;

    legend {
        color: $slate-600;
    }
}

// ─── Input Chips ────────────────────────────────────────

.ph-input-chip {
    display: inline-flex;
    padding: 6px 12px;
    border: 1px solid $border-color;
    border-radius: $border_radius-m;
    background-color: $slate-50;
    color: $slate-500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    font-weight: 600;
    user-select: none;

    &:hover {
        background-color: $slate-100;
        color: $accent-blue-500;
        border-color: $accent-blue-300;
        box-shadow: 0 2px 8px -2px rgba(0, 123, 255, 0.3);
    }

    &.selected {
        // background-color: $accent-blue-400;
        border-color: $accent-blue-500;
        color: $accent-blue-500;
        font-weight: 600;

        &:hover {
            background-color: $accent-blue-50;
            border-color: $accent-blue-500;
            box-shadow: 0 2px 8px -2px rgba(0, 123, 255, 0.3);
        }
    }
}

// ─── Country Selector ───────────────────────────────────────────────────────

.ph-country-checkboxes {
    position: relative;
    background-color: white;
    width: 100%;
    display: none;
    border-radius: $border_radius-m;
    box-shadow: $natural-shadow-md;
    overflow: hidden;
    z-index: 99;
    max-height: 300px;
    overflow-y: auto;

    @media (min-width: 1900px) {
        max-height: none;
        overflow: visible;
    }

    label {
        display: block;
        padding: 8px 12px;
        margin: 0;
        color: $slate-600;
        cursor: pointer;
        border-bottom: 1px solid $surface-50;
        transition: background-color 0.2s ease;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background-color: $accent-blue-25;
            color: $accent-blue-600;
        }

        input[type='checkbox'] {
            margin-right: 8px;
            accent-color: $accent-blue-600; // Using darker blue for white checkmark
        }
    }
}

input[type='checkbox'].ph-checkbox {
    accent-color: $accent-blue-600;
}

.selectBox {
    position: relative;

    select {
        width: 100%;
    }
}
