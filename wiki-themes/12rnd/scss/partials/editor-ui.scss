$card-border-radius: 8px;

// Override conflicting old styles when using the new layout
.ph-form .main-form {
    position: static !important;

    #form-wrap {
        margin-right: 0 !important;
    }
}

// Stronger selectors to ensure visibility
.ph-editor-layout {
    display: grid !important;
    grid-template-columns: 1fr 340px !important;
    gap: 2rem !important;
    margin-top: 1.5rem !important;
    width: 100% !important;
    padding-bottom: 1rem;

    @media (max-width: 1200px) {
        grid-template-columns: 1fr 300px !important;
        gap: 1.5rem !important;
    }

    @media (max-width: 992px) {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
}

.ph-form-group {
    margin-bottom: 0;

    .form-label--actions {
        a {
            text-decoration: none;
            color: $slate-450;
            margin-left: auto;
            display: flex;
            gap: 4px;
        }
    }

    // Style for inline action links (like Configure button)
    .form-field-container--inline {
        a.ml-auto {
            text-decoration: none;
            color: $slate-450;
            display: flex;
            gap: 4px;
            align-items: center;
        }
    }

    .form-field-container {
        display: flex;
        flex-direction: column;
        align-items: start;
        gap: 2px;

        &.form-field-container--inline {
            flex-direction: row;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        // Tablet-specific styling for select fields
        &.form-field-select {
            @media (max-width: 992px) and (min-width: 768px) {
                width: fit-content;

                select {
                    text-indent: 0px !important;
                }
            }
        }
    }

    label {
        margin-bottom: 0;
        display: flex;
        gap: 6px;
        align-items: center;
        justify-content: start;
    }

    .form-field-text {
        font-size: 14px;
        font-weight: 600;
        color: $slate-500;
    }
}

.ph-editor-main {
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
    min-width: 0 !important;
}

.ph-editor-sidebar {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;

    .ph-cards-row {
        gap: 1rem;
        display: flex;
        flex-direction: column;
    }

    @media (max-width: 992px) {
        order: 1 !important;
    }

    @media (max-width: 992px) and (min-width: 768px) {
        display: grid !important;
        grid-template-columns: 1fr !important;
        grid-template-rows: auto auto !important;
        gap: 1rem !important;

        #publish-settings-card {
            grid-column: 1 / -1;
            grid-row: 1;
        }

        .ph-cards-row {
            grid-column: 1 / -1;
            grid-row: 2;
            display: grid;
            grid-template-columns: 1fr 1fr;
        }
    }
}

// ─── Enhanced Permalink Display ──────────────────────────────────────────────

.ph-permalink-container {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    background-color: $slate-50;
    border: 1px solid $slate-200;
    border-radius: $border_radius-s;
    color: $slate-450;

    span {
        padding: 2px 6px;
    }

    .ph-permalink-url {
        flex: 1;
        flex-basis: 100%;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .ph-permalink-actions {
        display: flex;
        align-items: stretch;
        gap: 8px;
        padding: 8px;
        flex-shrink: 0;
        height: 100%;
    }

    button {
        background-color: transparent;
        border: none;
        display: flex;
        align-items: center;
        flex: 1;
        padding: 0 !important;
        border-radius: $border_radius-xs;
        transition: background-color 0.2s ease-in-out;
        min-height: 100% !important;

        &:hover {
            background-color: $slate-100;
        }

        i {
            font-size: 12px;
            color: $slate-450;
            transition: color 0.2s ease-in-out;
        }

        &:hover i {
            color: $slate-500;
        }
    }
}

// ─── Permalink Edit Mode ─────────────────────────────────────────────────────

.ph-permalink-edit {
    margin-top: 8px;

    .ph-permalink-input-container {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .ph-permalink-input {
            flex: 1;
            font-size: 12px;
            padding: 4px 8px;
        }

        .ph-permalink-edit-actions {
            display: flex;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;

            .ph-btn {
                padding: 4px;
                min-height: auto;

                i {
                    font-size: 12px;
                }
            }
        }
    }
}

// ─── Editor Section ──────────────────────────────────────────────────────────

.ph-editor-section {
    position: sticky;
    top: 1rem;
}

.ph-editor-container {
    display: flex;
    gap: 0;
    border-radius: $border_radius-m;
    overflow: hidden;
    box-shadow: $natural-shadow-xs;
    min-height: 0;

    .trumbowyg-box {
        box-shadow: none;
        border: none !important;
        border-radius: 0;
        height: 100%;
        display: flex;
        flex-direction: column;

        .trumbowyg-button-group {
            button {
                color: $slate-500;
                svg {
                    color: $slate-500;
                    fill: $slate-500;
                }
            }
        }
    }

    .trumbowyg-editor-visible {
        display: flex;
        flex-direction: column;
    }

    // Ensure content area fills available height and allows scrolling
    .trumbowyg-editor-box,
    .trumbowyg-editor {
        flex: 1;
        height: 100% !important;
        overflow-y: auto;
    }

    .trumbowyg-button-pane.trumbowyg-button-pane {
        &::before,
        .trumbowyg-button-group::after {
            background-color: $slate-150;
        }

        .trumbowyg-open-dropdown::after {
            border-top-color: $slate-350;
        }
    }

    @media (max-width: 768px) {
        flex-direction: column;
        height: 60vh;
        max-height: 60vh;
    }

    @media (max-width: 576px) {
        height: 50vh;
        max-height: 50vh;
    }
}

.ph-editor-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    background: white;
    height: 100%;

    .frm_kb_body {
        border: none;
        border-radius: 0;
        resize: none;
        min-height: 0; // Remove fixed min-height
        height: 100%; // Fill available space
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 14px;
        line-height: 1.6;
        padding: $card-padding;
        color: $slate-700;
        overflow-y: auto; // Make scrollable

        &:focus {
            box-shadow: none;
            outline: none;
        }
    }
}

.ph-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px $card-padding;
    background: white;
    border-bottom: 1px solid $slate-150;
    flex-wrap: wrap;
    gap: 6px;

    label {
        margin-bottom: 0;
    }

    .ph-editor-controls {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-left: auto;

        .ph-toggle-button {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;

            i {
                font-size: 18px;
                line-height: 1;
            }
        }

        @media (max-width: 768px) {
            gap: 1rem;
        }

        @media (max-width: 576px) {
            flex-direction: column;
            align-items: flex-end;
            gap: 0.5rem;
        }
    }
}

// ─── Preview Area ────────────────────────────────────────────────────────────

.ph-preview-area {
    flex: 1;
    border-left: 1px solid $slate-200;
    display: flex;
    flex-direction: column;
    background-color: $slate-25;
    min-width: 0;

    @media (max-width: 768px) {
        border-left: none;
        border-top: 1px solid $slate-200;
    }

    .form-group {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }

    label {
        color: $slate-600;
        font-weight: 600;
        font-size: 14px;
        padding: 12px $card-padding;
        margin: 0;
        background-color: $slate-50;
        border-bottom: 1px solid $slate-200;
    }

    #preview {
        flex: 1;
        border: none;
        border-radius: 0;
        background-color: white;
        margin: 0;
        padding: $card-padding;
        overflow-y: auto;
        font-size: 14px;
        line-height: 1.6;
        color: $slate-700;
        min-height: 0; // Remove fixed min-height
        height: 100%; // Fill available space

        &:focus {
            box-shadow: none;
            outline: none;
        }
    }

    // Remove explicit heights on editor area editors to allow flex fill
    min-height: 0 !important;
    height: 100% !important;
}

// ─── Settings Cards ──────────────────────────────────────────────────────────

.ph-settings-card {
    margin-bottom: 0 !important;
    display: block !important;

    hr {
        margin: 0.25rem 0;
    }
}

// ─── Hidden Elements ─────────────────────────────────────────────────────────

.ph-hidden-permalink-controls,
.ph-legacy-settings {
    display: none !important;
}

// ─── Mobile Responsiveness ───────────────────────────────────────────────────

@media (max-width: 576px) {
    .ph-editor-layout {
        gap: 1rem;
        margin-top: 1rem;
    }

    .ph-permalink-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;

        .ph-permalink-url {
            width: 100%;
        }
    }

    .ph-editor-container {
        min-height: 300px;
    }

    .ph-editor-area .frm_kb_body {
        min-height: 250px;
    }
}

// ─── Focus States and Interactions ───────────────────────────────────────────

.ph-editor-layout {
    input:focus,
    select:focus,
    textarea:focus {
        outline: none;
        border-color: $accent-blue-500;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.15);
    }
}

// ─── Markdown Editor (SimpleMDE / CodeMirror) ────────────────────────────────

.ph-editor-area {
    .editor-toolbar {
        background: $slate-50;
        border: none;
        border-bottom: 1px solid $slate-200;
        border-radius: 0;
        padding: 8px 12px;
        margin: 0;

        .fa {
            color: $slate-600;
        }

        a {
            color: $slate-600 !important;
            border-radius: 4px;

            &:hover {
                background: $slate-100;
                color: $slate-700 !important;
            }

            &.active {
                background: $slate-200;
                color: $slate-800 !important;
            }
        }
    }

    .CodeMirror,
    .CodeMirror-scroll {
        min-height: 0; // Remove fixed min-height
        height: 100% !important; // Fill available space
        width: 100%;
        flex: 1;
        font-family: 'Monaco', 'Consolas', 'SF Mono', monospace;
        font-size: 14px;
        line-height: 1.6;
        color: $slate-700;
        border: none;
        border-radius: 0;
        background: white;
    }

    .CodeMirror-wrap {
        border: none;
        border-radius: 0;
        height: 100%;
    }

    .CodeMirror-lines {
        padding: $card-padding;
    }

    .CodeMirror-cursor {
        border-left: 1px solid $slate-700;
    }

    .CodeMirror-selected {
        background: rgba(33, 150, 243, 0.15);
    }
}

// @supports (field-sizing: content) {
//     .editor-title-container {
//         width: auto;
//         margin-left: -0.75rem;
//         cursor: pointer;
//         display: flex;
//         align-items: center;
//         border-radius: $border-radius-lg;
//         transition: box-shadow 0.25s ease-in;

//         input#frm_kb_title {
//             field-sizing: content;
//             border: none !important;
//             box-shadow: none !important;
//             font-size: 20px;
//             color: $slate-600;
//         }

//         i {
//             font-size: 16px;
//             color: $slate-400;
//             transition: color 0.25s ease-in-out;
//             margin-right: 0.75rem;
//             display: inline-block !important;
//         }

//         &:hover {
//             i {
//                 color: $slate-500;
//             }
//         }

//         &:focus-within {
//             box-shadow: $natural-shadow-md !important;

//             input#frm_kb_title {
//                 outline: none !important;
//                 box-shadow: none !important;
//             }
//         }
//     }

//     @supports (max-width: -webkit-fill-available) {
//         .ph-article-header {
//             display: flex;
//         }

//         .editor-title-container {
//             max-width: -webkit-fill-available;
//         }
//     }

//     @supports (width: -webkit-fill-available) {
//         .input#frm_kb_title {
//             width: -webkit-fill-available;
//         }
//     }

// }

.editor-title-container {
    input {
        width: 100%;
    }

    i {
        display: none;
    }
}

// ─── Feedback Stats Styles ──────────────────────────────────────────────────

.ph-feedback-stats {
    .ph-stats-loading {
        text-align: center;
        padding: 1rem;
        color: $slate-500;

        i {
            margin-right: 0.5rem;
        }
    }

    .ph-stats-error {
        text-align: center;
        padding: 1rem;
        color: $slate-500;

        i {
            color: $orange-500;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
    }

    .fb-stats-summary {
        .fb-stats-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;

            .fb-stats-label {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                font-size: 10px;
                font-weight: 600;
                color: $slate-500;
                text-transform: uppercase;
                letter-spacing: 0.025em;

                i {
                    font-size: 1rem;
                }

                &--positive i {
                    color: $accent-blue-500;
                }

                &--negative i {
                    color: $slate-400;
                }
            }
        }

        .fb-stats-progress-unified {
            height: 8px;
            background-color: $slate-100;
            border-radius: 4px;
            display: flex;
            overflow: hidden;
            margin-bottom: 0.5rem;

            .fb-stats-bar-helpful {
                background-color: $accent-blue-500;
                height: 100%;
                transition: width 0.6s ease;
            }

            .fb-stats-bar-not-helpful {
                background-color: $slate-300;
                height: 100%;
                transition: width 0.6s ease;
            }
        }

        .fb-stats-count-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .fb-stats-label {
            font-weight: 500;
            font-size: 12px;
            color: $slate-500;
            line-height: 1;
        }

        .fb-stats-count {
            font-weight: 700;
            font-size: 14px;
            color: $slate-600;
            line-height: 1;
            text-align: center;

            &--total {
                color: $slate-450;
                font-weight: 500;
            }
        }
    }
}


.fb-no-data {
    text-align: center;
    padding: 1rem;

    p {
        color: $slate-350;
        font-size: 14px;
        margin: 0;
        font-style: italic;
    }
}

.ph-feedback-placeholder {
    text-align: center;
    padding: 1.5rem;
    color: $slate-400;
    font-style: italic;
}
