.custom-tooltip {
    $tooltip-border-color: #1e293b; // slate-800 equivalent
    $tooltip-text-color: #f8fafc;   // slate-50 equivalent
    $tooltip-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1); // natural-shadow-sm equivalent
    $tbw: 6px; // tooltip border width (arrow size)
    
    position: fixed !important;
    padding: 8px 12px !important;
    color: $tooltip-text-color !important;
    border-radius: 8px !important;
    background: $tooltip-border-color !important;
    box-shadow: $tooltip-shadow !important;
    max-width: 320px !important;
    font-size: 13px !important;
    text-align: left !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
    border: none !important;
    font-family: 'Source Sans Pro', sans-serif !important;
    cursor: pointer;
    
    &.show {
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    // Base arrow styles
    &::before,
    &::after {
        content: '' !important;
        position: absolute !important;
        width: 0 !important;
        height: 0 !important;
        border-style: solid !important;
        border-color: transparent !important;
    }
    
    // Arrow pointing up (tooltip is below target)
    &.tooltip-bottom {
        &::before {
            bottom: 100% !important;
            left: var(--arrow-offset, 50%) !important;
            transform: translateX(-50%) !important;
            border-width: 0 $tbw $tbw $tbw !important;
            border-color: transparent transparent $tooltip-border-color transparent !important;
        }
    }
    
    // Arrow pointing down (tooltip is above target)
    &.tooltip-top {
        &::before {
            top: 100% !important;
            left: var(--arrow-offset, 50%) !important;
            transform: translateX(-50%) !important;
            border-width: $tbw $tbw 0 $tbw !important;
            border-color: $tooltip-border-color transparent transparent transparent !important;
        }
    }
    
    // Arrow pointing left (tooltip is right of target)
    &.tooltip-right {
        &::before {
            right: 100% !important;
            top: var(--arrow-offset, 50%) !important;
            transform: translateY(-50%) !important;
            border-width: $tbw $tbw $tbw 0 !important;
            border-color: transparent $tooltip-border-color transparent transparent !important;
        }
    }
    
    // Arrow pointing right (tooltip is left of target)
    &.tooltip-left {
        &::before {
            left: 100% !important;
            top: var(--arrow-offset, 50%) !important;
            transform: translateY(-50%) !important;
            border-width: $tbw 0 $tbw $tbw !important;
            border-color: transparent transparent transparent $tooltip-border-color !important;
        }
    }
}

[data-tooltip] {
    cursor: pointer !important;
}

.custom-tooltip.tooltip-large {
    max-width: 400px !important;
    font-size: 14px !important;
}

.custom-tooltip.tooltip-small {
    max-width: 200px !important;
    font-size: 12px !important;
    padding: 6px 10px !important;
}

.custom-tooltip.tooltip-fade {
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out, transform 0.3s ease-in-out !important;
    transform: translateY(5px);
    
    &.show {
        transform: translateY(0);
    }
    
    &.tooltip-top {
        transform: translateY(-5px);
        
        &.show {
            transform: translateY(0);
        }
    }
    
    &.tooltip-left {
        transform: translateX(-5px);
        
        &.show {
            transform: translateX(0);
        }
    }
    
    &.tooltip-right {
        transform: translateX(5px);
        
        &.show {
            transform: translateX(0);
        }
    }
}