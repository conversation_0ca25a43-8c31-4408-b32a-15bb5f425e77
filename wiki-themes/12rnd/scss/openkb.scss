$blue-font: #308cea;
$dark-font: #333333;
$base-font-size: 15px;

$primary: #30313D;
$secondary: #6A7383;
$disabled: #A3ACBA;
$container: #F6F8FA;
$phBlue: #2196F3;

$border: #EBEEF1;


// Small tablets and large smartphones (landscape view)
$screen-sm-min: 576px;

// Small tablets (portrait view)
$screen-md-min: 768px;

// Tablets and small desktops
$screen-lg-min: 992px;

// Large tablets and desktops
$screen-xl-min: 1200px;

// Small devices
@mixin sm {
    @media (min-width: #{$screen-sm-min}) {
        @content;
    }
}

// Medium devices
@mixin md {
    @media (min-width: #{$screen-md-min}) {
        @content;
    }
}

// Large devices
@mixin lg {
    @media (min-width: #{$screen-lg-min}) {
        @content;
    }
}

// Extra large devices
@mixin xl {
    @media (min-width: #{$screen-xl-min}) {
        @content;
    }
}


* {
    font-family: 'Source Sans Pro', sans-serif;
    outline: none !important;
}

body {
    background: #fff;
}


.wrapper {
    padding-top: 24px;
}




#article-view-content-wrap {
    height: 100%
}

.overflow-auto {
    overflow: auto;
}

#articleRightSideMenu {

    &.sticky {
        position: fixed;
        top: 20px;
        // width: 100%;
        z-index: 999;
    }
}


.search-result-item-wrap {
    display: flex;
    padding-bottom: 10px;

    .search {
        &--file-icon {
            width: 16px;
            height: 16px;
            margin-right: 20px;
            align-self: center;
        }

        &--article-title {
            color: #308cea;
            font-weight: 600;
            margin: 0;

        }

        &--article-body {
            color: #585858;
            font-size: 18px;
            line-height: 27px;
            padding-bottom: 10px;
        }
    }
}

.body-min {
    position: relative;
}



.main-content {

    display: flex;
    flex-direction: column;
    background: #fff;
    overflow: auto;
    padding: 20px;

    @include md {
        padding: 48px 48px 0;
        overflow-y: visible;

        height: calc(100vh);
    }
}

h2 {
    &.main-title {
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 42px;
        color: $primary;
        margin: 0;
    }
}

h3 {
    &.subtitle-text {
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-top: 40px;
        max-width: 880px;
        color: $secondary;
    }
}

.home-view {
    margin-top: 47px;
    display: flex;
    justify-content: center;
}


#home-header-container {
    display: flex;
    justify-content: center;

    .row {
        max-width: 1260px;
        width: 100%;
        justify-content: flex-end;
    }
}

#divAllTopics {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: stretch;
    gap: 32px;
    justify-content: flex-start;
    // align-content: flex-start;
    // justify-content: center;
    max-width: 1260px;
    width: 100%;

    $colors: (
        1: #5393f4,
        2: #75a139,
        3: #e06cb0,
        4: #e7c970,
        5: #b1345f,
        6: #8953f4,
        7: #FFFA73,
        8: #D4FF73,
        9: #AAFF73,
        10: #73FF73,
        11: #73FFAA,
        12: #73FFD0
    );

@for $i from 1 through length($colors) {
    .topic-items[data-topickeyword="#{$i}"] {
        background-color: map-get($colors, $i);
    }

    .topic-item[data-topickeyword="#{$i}"] .svg-wrap {
        border: 1px solid map-get($colors, $i);

        .icon svg {
            fill: map-get($colors, $i);
        }
    }
}

.topic-item {
    border-radius: 6px;
    border: 1px solid $border;
    background: #FFF;
    padding: 16px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    flex-shrink: 0;
    cursor: pointer;
    width: 100%;

    @include sm {
        width: auto
    }

    .svg-wrap {
        width: 48px;
        height: 48px;
        border-radius: 6px;
        background: #FFF;

        svg {
            margin: 8px;
        }
    }

    .svg-wrap {
        width: 52px;
        height: 52px;
        border-radius: 6px;
        border-width: 1px;

        svg {
            margin: 8px;
        }
    }

    .topic-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        flex: 1 0 0;
        width: 295px;

        a {
            color: $secondary;
            font-size: 16px;
            letter-spacing: 0.25px;
            font-style: normal;
            font-weight: 600;
            line-height: 100%;
            text-transform: uppercase;
            margin-bottom: 2px;

            &:hover {
                text-decoration: none;
            }
        }

        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;

            li {
                padding-left: 24px;
                background: url('../images/subtopic-chevron-right.svg') 0 50% no-repeat;
                line-height: 16px;

                &:not(:last-child) {
                    margin-bottom: 8px;
                }

                a {
                    color: $primary;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 16px;
                    text-transform: none;
                    display: inline-block;

                    &:hover {
                        color: $phBlue;
                    }
                }
            }
        }
    }
}
}

ul {
    &#topic-page-nav {
        list-style-type: none;
        display: flex;
        padding: 12px 0;
        margin-top: 6px;
        align-items: flex-start;
        gap: 16px;
        flex-shrink: 0;
        margin-bottom: 0;
        flex-direction: row;
        border-bottom: solid 1px $border;

        @include md {
            flex-direction: row;
            gap: 28px;
            border-bottom: none;
        }

        li {
            line-height: 24px;
            white-space: nowrap;

            &:nth-child(n+3) {
                display: none;
            }

            &:not(:first-child) {
                overflow: hidden;
                text-overflow: ellipsis;
            }

            @include md {
                &:nth-child(n+3) {
                    display: block;
                }
            }

            a {
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                color: $secondary;

                &:hover,
                &.active {
                    text-decoration: none;
                    color: $phBlue
                }
            }
        }
    }

}

#topic-page-wrap {
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-start;

    @include md {
        flex-direction: row;
    }

    #filters {
        gap: 12px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        width: 100%;
        margin-top: 20px;
        border-top: solid 1px $border;
        padding-top: 20px;

        @include md {
            width: 208px;
            padding: 24px 0px;
            flex-shrink: 0;
            flex-grow: 0;
        }

        h3 {
            color: $secondary;
            text-overflow: ellipsis;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%;
            text-transform: uppercase;
            margin: 0;
        }

        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 12px;

            li {
                line-height: 16px;

                a {
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 16px;
                    color: $primary;

                    &:hover,
                    &.active {
                        text-decoration: none;
                        color: $phBlue;
                    }
                }
            }

            &#subtopics {
                padding-bottom: 20px;
                border-bottom: solid 1px $border;
                width: 100%;
                margin-bottom: 0;

                @include md {
                    padding-bottom: 28px;
                    margin-bottom: 16px;
                    border-bottom: solid 1px $border;
                }

                li {
                    background: url('../images/subtopic-chevron-right.svg') 0 50% no-repeat;
                    padding-left: 24px;

                    a {
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 16px;
                        color: $primary;

                        &:hover,
                        &.active {
                            text-decoration: none;
                            color: $phBlue;
                        }
                    }
                }
            }

            &#featured-articles {
                display: none;

                @include md {
                    display: flex;
                }

                li {
                    a {
                        padding: 4px 8px;
                        border-radius: 4px;
                        display: inline-block;
                        background: $container;

                        //rgba(33, 150, 243, 0.10)
                        &:hover {
                            background: rgba(33, 150, 243, 0.10);
                            color: $phBlue;
                        }
                    }
                }
            }
        }

        #featured-articles-heading {
            display: none;

            @include md {
                display: block;
            }
        }

    }
}

.search-result-body {
    word-break: break-word;
}

#articles-list-wrap {
    min-height: calc(100vh);
    word-wrap: break-word;

    @include md {
        border-left: solid 1px $border;
        padding: 8px 40px 8px 40px;
        flex-grow: 1;
    }

    #breadcrumb {
        padding: 20px 0px;

        @include md {
            padding: 20px 24px;
        }


        ul {
            display: flex;
            list-style-type: none;
            padding: 0;
            margin: 0 0 26px;

            li {
                line-height: 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &:nth-child(n+3) {
                    display: none;
                }

                @include md {
                    &:nth-child(n+3) {
                        display: block;
                    }
                }

                a {
                    color: $primary;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 100%;

                    &:hover {
                        text-decoration: none;
                        color: $phBlue;
                    }

                    &.current {
                        cursor: text;
                        color: $disabled;
                    }
                }

                &:not(:first-child) {
                    &:before {
                        content: '/';
                        padding: 0 12px;
                    }

                }
            }
        }
    }

    #article-list {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .search-result-card {
            @include md {
                padding: 24px;
            }

            cursor: pointer;

            &:hover {
                box-shadow: 0px 8px 13px -1px rgba(0, 0, 0, 0.08);
                border-radius: 6px;
                background: $container;

                .search-result-title {
                    color: $phBlue;
                }
            }
        }

        .search-result-title {
            color: $primary;
            text-align: justify;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
            margin-bottom: 4px;

            &:hover {
                text-decoration: none;
            }
        }

        .article-metadata {
            display: flex;
            gap: 24px;
            flex-direction: row;
            justify-content: flex-start;
            line-height: 20px;

            color: $secondary;
            text-align: justify;
            font-size: 11px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
            text-transform: uppercase;

            .article-author {
                padding: 0 0 0 16px;

                background: url('../images/person.svg') 0 50% no-repeat;

            }

            .article-date {
                padding: 0 0 0 16px;

                background: url('../images/calendar.svg') 0 50% no-repeat;

            }
        }

        .article-body-preview {
            padding: 16px 0;
            color: $primary;

            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
        }

        .article-keywords {
            display: flex;
            line-height: 16px;
            gap: 3px;
            flex-wrap: wrap;

            span.article-tags {
                display: inline-block;
                background: $border;
                color: $secondary;
                text-transform: capitalize;
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                line-height: 16px;
                padding: 1px 6px;
                border-radius: 4px;

                &:hover {
                    color: $phBlue;
                    background: rgba(33, 150, 243, 0.10);

                }
            }
        }
    }
}

#right-menu {
    margin-left: 20px;
    width: 208px;
    flex-shrink: 0;

    #print-article {

        display: flex;
        
        margin: 20px 0;
        justify-content: left;
        border-bottom: solid 1px $border;

        @include md {
            margin: 0px auto 20px;
            border-top: solid 1px $border;
            padding-top: 20px;
            padding-bottom: 20px;
            
        }

        @include lg {
            justify-content: center;
            border-top: none;

            border-bottom: none;
            margin:  40px 0 0 20px;
        }
        @include xl {
            
        }
    }

   

    h3 {
        color: $secondary;
        text-overflow: ellipsis;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%;
        /* 21px */
        text-transform: uppercase;
        margin: 0;
    }

    #btnPrintArticle {
        display: inline-flex;
        padding: 4px 8px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        color: $secondary;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        margin-bottom: 20px;

        &:hover {
            color: $phBlue;
            background: none;
        }

        @include lg {
            justify-content: left;
            margin-bottom: 0;
        }
    }

    #table-of-contents,
    #related-searches {
        display: flex;
        padding: 24px 0 0 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        border-top: solid 1px $border;

        ul {
            margin: 0;
            padding: 12px 0 24px;
            list-style-type: none;
            display: flex;
            flex-direction: column;
            gap: 12px;
            line-height: 16px;
            border-bottom: solid 1px $border;
        }

        a {
            color: $primary;
            font-family: Source Sans Pro;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;


            &:hover,
            &.active {
                color: $phBlue;
                text-decoration: none;
            }
        }
    }

    .related-key-searches {
        border-bottom: none !important;
    }

    #helpful-section {
        display: flex;
        width: 208px;
        padding: 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        @include md {
            padding: 0
        }

        @include lg {
            padding: 24px 0px 24px 20px;
        }

        #feedbackContainer {
            display: flex;
            gap: 12px;
        }

        .feedback-btn {
            color: $secondary;
            font-family: Source Sans Pro;
            font-size: 11px;
            font-style: normal;
            font-weight: 600;
            line-height: 16px;

            &:hover {
                color: $phBlue;
                text-decoration: none;
            }
        }

        #btnHelpfulYes,
        #btnHelpfulNo {
            display: flex;
            height: 24px;
            padding: 1px 6px;
            align-items: center;
            gap: 2px;
            border-radius: 4px;
            border: 1px solid $secondary;
            line-height: 16px;
            box-shadow: none;

            &:hover,
            &.helpful-yes,
            &.helpful-no {
                border: solid 1px $phBlue;
                color: $blue-font;
            }
        }

        #commentContainer {
            color: $primary;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
            display: flex;
            gap: 12px;
            flex-direction: column;
        }

        #txtFeedbackComments {
            height: 64px !important;
            padding: 4px 8px 0px 8px;
            border-radius: 4px;
            border: 1px solid $border;
            background: #fff;
            resize: none;
        }

        #btnSubmitFeedbackComment {
            padding: 4px 20px;
            border-radius: 4px;
            border: 1px solid $phBlue;
            background: $phBlue;
            /* btn/shadow/2 */
            box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.08), 0px 2px 5px 0px rgba(60, 66, 87, 0.12), 0px -1px 1px 0px rgba(0, 0, 0, 0.12) inset;
            color: #fff;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
            text-transform: uppercase;
        }

        #commentSubmitted {
            font-size: 11px;
            font-style: normal;
            line-height: 16px;
            color: $primary;
        }
    }

    
}

#article-body {
    padding: 0 32px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
    color: $primary;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;

  hr {
    width: 100%;
  }

    h1 {
        padding-left: 0 !important;
    }

    h1,
    h2,
    h3 {
        margin: 32px 0 24px 0 !important;
    }

    p {
        margin: 0;
    }

    a {
        text-decoration: underline;
        color: $phBlue;
    }

    strong {
        font-weight: 700;
    }

    .btn-primary {
        color: #fff;
        text-decoration: none;
    }

    ul,
    ol {
        margin: 10px 0 0 20px;
        padding: 0;

        li {
            list-style-position: inside;
            padding: 0 0 20px;

            &:last-child {
                padding: 0
            }

            p {
                display: inline;
            }
        }
    }

    .accordions {
        width: 100%;

        .accordion-header {
            padding: 0;

            a {
                display: inline-block;
                width: 100%;
                height: auto;
                background: url(./../images/chevron-down-sm.svg) no-repeat 98% center;
                background-size: 18px;

                text-decoration: none;
                padding: 12px 20px;

                &[aria-expanded="true"] {
                    background: url(./../images/chevron-up-sm.svg) no-repeat 98% center;
                    background-size: 18px;
                }

            }
        }

    }
}

h1 {
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 42px;
    margin: 0 0 40px;

    @include md {
        padding: 0 0 0 24px;
    }

}

.sticky {
    position: sticky;
    top: 0px;
    align-self: flex-start;
}

.article-content {
    .sticky {
        position: relative;
        top: auto;

        @include lg {
            position: sticky;
            top: 0px;
            align-self: flex-start;
        }
    }

    #topic-page-wrap {
        flex-direction: column;

        @include lg {
            flex-direction: row !important;
        }

        #filters {
            order: 2;
            margin-top: 20px;
            border-top: solid 1px $border;
            padding-top: 20px;
            width: 100%;

            @include lg {
                order: 1;
                width: 208px;
                border-top: none;
                margin-top: 0;
                padding: 24px 20px 24px 0px;
            }
        }

        #articles-list-wrap {
            order: 1;
            border-left: none;
            padding: 20px 0 0;

            @include lg {
                order: 2;
                border-left: solid 1px $border;
                padding: 8px 40px 8px 40px;
            }

            h1 {
                padding: 0;

                @include lg {
                    padding: 0 0 0 24px;
                }
            }

            #breadcrumb {
                padding: 0;

                @include lg {
                    padding: 20px 24px;
                }

            }

            #article-body {
                padding: 0;

                @include lg {
                    padding: 0 32px 20px;
                }
            }
        }

        #right-menu {
            order: 3;
            width: 100%;

            margin: 0;

            @include lg {
                order: 3;
                width: 208px;
                border-top: none;
                margin-left: 20px;
            }

            #table-of-contents {
                padding-left: 0;
                display: none;

                @include lg {
                    padding-left: 20px;
                    display: block;
                    border-top: none !important
                }
            }

            #helpful-section {
                padding-left: 0;

                @include lg {
                    padding-left: 20px;
                }
            }

            #related-searches {
                padding-left: 0 !important;
                display: none;
                margin-left: 20px;

                @include lg {
                    display: block;
                    padding-left: 20px;
                }
            }
        }
    }


}


#articles-list-wrap .contact-support {
    display: none;
    margin: 0  32px 20px;
    @include lg {
        display: block;
    }
}

#right-menu .contact-support {
    display: block;
    font-size: 12px;
    line-height: 16px;
    @include lg {
        display: none;
    }
}


.contact-support {
    padding: 20px 0 0;
    margin: 20px 0 0;
    font-family: Source Sans Pro;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    border-top: solid 1px $border;
    color: $primary;

    @include md {
        padding: 20px 0 0;
        margin: 20px 0 20px;
    }

    @include lg {
        padding: 24px 0 0;
        margin: 0 0 0 20px;
    }

    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;

        li {
            display: flex;
            gap: 6px;
            align-items: center;

            a {
                color: $phBlue
            }
        }
    }
}

.accordion-search {
    position: relative;
    width: 100%;
    .fa-search{
        position: absolute;
        top: 50%;
        left: 0;
        margin: -8px 0 0 7px
    }

    input{
        padding-left: 24px;
    }

     > div{
        margin-bottom: 10px;
    }
}


.uploaded-media{
    position: relative;
    background: rgb(106, 115, 131, 0.4) ;
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto;
    span {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1 !important;
    }

    img {
        position: relative;
        height: auto !important;
        opacity: 0;
        max-width: 100%;
        transition: opacity 1s linear;
        z-index: 2;
        &.img-fluid{
            opacity: 1;
        }
    }
}


.accordions .card-body {
    a {
      color: #007bff !important;
    }
    a:visited {
      color: #007bff !important;
    }
    a:hover {
      color: #007bff !important;
      text-decoration: underline;
    }
  }

  .accordion-header a{
    display: inline-block;
    width: 100%;
    height: 100%;
    background: url(./../images/chevron-down-sm.svg) no-repeat right center;
    background-size: 18px;
    background-position: right center;
&[aria-expanded="true"]{
    background: url(./../images/chevron-up-sm.svg) no-repeat right center;
    background-size: 18px;
}
}

.accordion-item, .alert {
    width: 100%;
}
