
$(document).ready(async function () {
	await initTranslator();
	//clear permalink
	$('#frm_kb_permalink').val('');
	//removes default tokenized '/'
	$("#frm_kb_keywords").tokenfield('destroy');
	//clear text '/'
	$("#frm_kb_keywords").val('');

	//generate random permalink integer
		$('#generate_permalink').click();
	
	$('#navSettingsDashboard').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings';
	});
	$('#navSettingsArticles').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/articles';
	});
	$('#navSettingsConfiguration').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/configuration';
	});

});