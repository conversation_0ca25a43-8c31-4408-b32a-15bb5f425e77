
// This should be a global function because it's called directly from the kb.hbs file...
function trackViewedArticle(article) {
    console.log('trackViewedArticle', article)
    const MAX_ARTICLES = 10;
    const viewedArticles = JSON.parse(localStorage.getItem('viewedArticles') || '[]');
    
    // Check if the article is already in the list
    const existingIndex = viewedArticles.findIndex(a => a.url === article.url);

    if (existingIndex !== -1) {
        viewedArticles.splice(existingIndex, 1);
    }

    // Add the new article to the start of the list
    viewedArticles.unshift(article);

    // Keep only the last MAX_ARTICLES viewed
    localStorage.setItem('viewedArticles', JSON.stringify(viewedArticles.slice(0, MAX_ARTICLES)));
}

// ----------------------------------------

$(document).ready(async function(){
    await initTranslator();
    const algoliaConfig = {
        index: $('#algolia-index').val(),
        appId: $('#algolia-appId').val(),
        apiKey: $('#algolia-apiKey').val(),
    }

    let isSearchWidgetOpen = false;
    const searchClient = algoliasearch(algoliaConfig.appId, algoliaConfig.apiKey);

    document.addEventListener('keydown', function(event) {
        // Check if the key pressed is '/'
        if (event.keyCode === 191) { // 191 is the keyCode for '/'
            
            // Check if currently focused element is an input or a textarea
            let activeElement = document.activeElement.tagName.toLowerCase();
            
            if (activeElement !== 'input' && activeElement !== 'textarea') {
                toggleAlgoliaSearch();
                event.preventDefault(); // Prevent the default '/' behavior
            }
        }
    });

    window.addEventListener('message', function(event) {
        // Check for the expected message
        if (event.data === 'triggerSearch') {
            toggleAlgoliaSearch();
        }
    });
    let latestSearchResults = [];

    aa('init', { appId: algoliaConfig.appId, apiKey: algoliaConfig.apiKey, useCookie: true });

    
    const queryVars = getUrlVars();
    
    // const autocompleteSearch = autocomplete({
    //     container: '#autocomplete',
    //     placeholder: t('Search Wiki'),
    //     detachedMediaQuery: '',
    //     openOnFocus: true,
    //     enterKeyHint: 'enter',
    //     //defaultActiveItemId: 0,
    //     translations: {
    //         clearButtonTitle: t('Clear'),
    //         detachedCancelButtonText: t('Cancel'),
    //         submitButtonTitle: t('Search Wiki'),
    //     },
    //     getSources(query) {
    //         return debounced([

    //             // RECENT SEARCHES
    //             {
    //                 getItems({ query }) {
    //                     if (!query.trim()) {
    //                         // Fetch the recently viewed articles and slice the array to get the first 4 items
    //                         return getRecentlyViewedArticles()
    //                     }
    //                     return [];
    //                 },
    //                 templates: {
    //                     header({ html }) {
    //                         return html`<span class="aa-SourceHeaderTitle">${t('Recently Viewed')}</span>
    //                             <div class="aa-SourceHeaderLine" />`;
    //                     },
    //                     item({ item, components, html }) {
    //                         // Convert camelCase string to space-separated words
    //                         const formatTopicAndSubtopicString = (str) => {
    //                             // Convert camelCase to space-separated words
    //                             let spacedStr = str.replace(/([a-z])([A-Z])/g, '$1 $2');
                                
    //                             // Replace &amp; with &
    //                             return spacedStr.replace(/&amp;/g, '&');
    //                         };
    //                         // Build breadcrumb
    //                         const breadcrumbs = ['Home'];
    //                         if(item.topic) breadcrumbs.push(formatTopicAndSubtopicString(item.topic));
    //                         if(item.subsopic) breadcrumbs.push(formatTopicAndSubtopicString(item.subsopic));

    //                         const breadcrumbStr = breadcrumbs.join(' > ');

    //                         return html`<a class="aa-ItemLink" href="${item.url}">
    //                             <div class="aa-ItemContent">
    //                               <div class="aa-ItemIcon">
    //                                   <svg class="" aria-hidden="true"  viewBox="0 0 16 16"  xmlns="http://www.w3.org/2000/svg"><path d="M7 0v5a2 2 0 0 0 2 2h5v7a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2zm2 0 5 5H9z"></path></svg>
    //                               </div>
    //                               <div class="aa-ItemContentBody">
    //                                 <div class="aa-ItemContentTitle">
    //                                   ${decodeHtmlEntities(item.label)}
    //                                 </div>
    //                                 <div class="aa-ItemContentBreadcrumb" style="font-size: 0.8em; color: #888;">
    //                                   ${breadcrumbStr}
    //                                 </div>
    //                               </div>
    //                             </div>
    //                           </a>`;
    //                     },

    //                 },
    //             },
    //             // TOPICS AND CATEGORIES?
    //             {
    //                 sourceId: 'topics',
    //                 getItems({ query }) {

    //                     // Return an empty array if the query is empty or just whitespace
    //                     // This way when you open the search - you don't see all the topics at the top of the list until you start typing...
    //                     if (!query.trim()) {
    //                         return Promise.resolve([]); 
    //                     }

    //                     return fetch('/openkb/api/topics')
    //                         .then(response => response.json())
    //                         .then(({ data }) => {
    //                             let results = [];

    //                             data.forEach(topic => {
    //                                 // For some reason the algolia search bugs out when trying to render a SVG inline - so we need to display the SVG as a base64 image...
    //                                 let base64Icon = btoa(unescape(encodeURIComponent(topic.svg)));
    //                                 topic.subtopics.forEach(subtopic => {
    //                                     results.push({
    //                                         topic: topic.topic,
    //                                         subtopic: subtopic.subtopic,
    //                                         icon: `data:image/svg+xml;base64,${base64Icon}`,
    //                                         label: `${topic.topic} - ${subtopic.subtopic}`,
    //                                         url: `/openkb/topic/${topic._id}/articles?subtopic=${subtopic.id}`
    //                                     });
    //                                 });
    //                             });

    //                             return results.filter(({ label }) =>
    //                                 label.toLowerCase().includes(query.toLowerCase())
    //                             );
    //                         });
    //                 },
    //                 getItemUrl({ item }) {
    //                   return item.url;
    //                 },
    //                 templates: {
    //                     header({ html }) {
    //                         return html`<span class="aa-SourceHeaderTitle">${t('Topics & Categories')}</span>
    //                             <div class="aa-SourceHeaderLine" />`;
    //                     },
    //                     item({ item, components, html }) {
                            
    //                         return html`<a class="aa-ItemLink" href="${item.url}">
    //                             <div class="aa-ItemContent">
    //                               <div class="aa-ItemIcon"><img src="${item.icon}" alt="icon" /></div>
    //                               <div class="aa-ItemContentBody">
    //                                 <div class="aa-ItemContentTitle">
    //                                   ${item.subtopic}
    //                                 </div>
    //                                 <div class="aa-ItemContentDescription" style="font-size: 0.8em; color: #888;">
    //                                   ${item.topic}
    //                                 </div>
    //                               </div>
    //                             </div>
    //                           </a>`;
    //                     },
    //                 },
    //             },

    //             // SEARCH RESULTS FROM ALGOLIA WIKI INDEX
    //             {
    //                 sourceId: 'hits',
    //                 getItems: function({ query }) {
    //                     const recentlyViewedArticles = getRecentlyViewedArticles();

    //                     // If the query is empty or just whitespace, retrieve the top 3 wiki articles
    //                     if (!query.trim() && recentlyViewedArticles.length) {

    //                         const numToRequest = Math.max(0, 6 - recentlyViewedArticles?.length);
    //                         let filters = `NOT objectID:${recentlyViewedArticles.map(article => article.id).join(' AND NOT objectID:')}`

    //                         return getAlgoliaResults({
    //                             searchClient,
    //                             queries: [{
    //                                 indexName: algoliaConfig.index,
    //                                 params: {
    //                                     hitsPerPage: numToRequest,
    //                                 },
    //                                 // Add filters to exclude specific ID's that are already showing in the "recently viewed" results...
    //                                 filters: filters
    //                             }],
    //                         });
       
    //                     // Otherwise if it's a genuine search - return up to 6 results.
    //                     } else {
    //                         const club = $('#club').val();
                        
    //                         const algoliaArgs = {
    //                             highlightPreTag: "__aa-highlight__",
    //                             highlightPostTag: "__/aa-highlight__",
    //                             hitsPerPage: 6,
    //                             attributesToHighlight: ['kb_title'],
    //                             attributesToSnippet: ['kb_body'],
    //                             attributesToRetrieve: ['_id', 'kb_regional_visibility', 'kb_title', 'kb_viewcount', 'kb_seo_description', 'kb_body']
    //                         }

    //                         const data = {
    //                             searchTerms: query,
    //                             algoliaArgs,
    //                         }

    //                         if( club ) {
    //                             data.club = club;
    //                         }
                        
    //                         return fetch('/api/algolia/search-wiki/', {
    //                             method: 'POST',
    //                             headers: {
    //                                 'Accept': 'application/json',
    //                                 'Content-Type': 'application/json'
    //                               },
    //                             body: JSON.stringify(data)
    //                         })
    //                         .then(response => response.json())
    //                         .then(( result) => {
    //                             return result.suggestedArticles
    //                         });
    //                     }

    //                 },
    //                 // This second getItemURl is required for keyboard nav.
    //                 getItemUrl({ item }) {
    //                     return '/openkb/kb/' + item.kb_permalink ?? item._id;
    //                 },
                  
    //                 templates: {
    //                     header({ html, state }) {
    //                         const title = (!state.query.trim()) ? t('Top Articles') : t('Wiki Articles');
    //                         return html `<span class="aa-SourceHeaderTitle">${title}</span>
    //                             <div class="aa-SourceHeaderLine" />`;
    //                     },
    //                     item({ item, components, html }) {
    //                         return html`<a class="aa-ItemLink" href="/openkb/kb/${item._id}">
    //                           <div class="aa-ItemContent">
    //                             <div class="aa-ItemIcon">
    //                                 <svg class="" aria-hidden="true"  viewBox="0 0 16 16"  xmlns="http://www.w3.org/2000/svg"><path d="M7 0v5a2 2 0 0 0 2 2h5v7a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2zm2 0 5 5H9z"></path></svg>
    //                             </div>
    //                             <div class="aa-ItemContentBody">
    //                               <div class="aa-ItemContentTitle">
    //                                 ${components.Highlight({ hit: item, attribute: 'kb_title' })}
    //                               </div>
    //                               <div class="aa-ItemContentDescription">
    //                                 ${components.Snippet({ hit: item, attribute: 'kb_body'})}
    //                               </div>
    //                             </div>
    //                           </div>
    //                         </a>`;
    //                     },
    //                 },
    //             },
    //         ]);
    //     },
    //     renderNoResults({ render, html, state }, root) {
    //         render(
    //             html `
    //               <div class="aa-PanelLayout aa-Panel--scrollable aa-no-results">
    //                 ${t('There were no results found for the search term:')} "${state.query}".
    //               </div>
    //             `,
    //             root
    //         )
    //     },
    //     onStateChange({ state }) {

    //         // Basic debounce logic so we don't fire the same state over and over
    //         if (isSearchWidgetOpen != state.isOpen) {
    //             isSearchWidgetOpen = state.isOpen

    //             if (state.isOpen === true) {
    //                 window.top.postMessage("modalShow", "*");
    //             } else {
    //                 window.top.postMessage("modalHide", "*");
    //             }

    //             if (!isSearchWidgetOpen) {
    //                 // Clear the current search context when the window closes (this way when you open the search widget a second time the search is empty)
    //                 autocompleteSearch.setQuery('');
    //             }

    //         }

    //     },
    // });

    function removeQuotes(str) {
        return str.replace(/^"|"$/g, '');
    }

    function getRecentlyViewedArticles() {
        return JSON.parse(localStorage.getItem('viewedArticles') || '[]').slice(0, 4);
    }

    function decodeHtmlEntities(str) {
        const entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&#x27;': "'",
            '&nbsp;': ' ',
            '&cent;': '¢',
            '&pound;': '£',
            '&yen;': '¥',
            '&euro;': '€',
            '&copy;': '©',
            '&reg;': '®',
            // ... you can add more as needed
        };

        for (let entity in entities) {
            str = str.split(entity).join(entities[entity]);
        }

        return str;
    }


    function toggleAlgoliaSearch() {
      // If the search widget is open, close it. Otherwise, open it.
      if(!isSearchWidgetOpen) {
        autocompleteSearch.setIsOpen(true);
        //const searchInput = document.querySelector('#autocomplete input');
        //if (searchInput) searchInput.focus(); // You can focus the input if needed
      }
    }

    function getUrlVars() {
        var vars = [], hash;
        var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            vars.push(hash[0]);
            vars[hash[0]] = hash[1];
        }
        return vars;
    }

    function searchKeyword (keyword) {
        
        autocompleteSearch.setIsOpen(true);
        autocompleteSearch.setQuery(keyword);
        autocompleteSearch.refresh();
    }

    $('#search-result-wrap').on('click', '.search-results-tags', function (e){
        e.preventDefault();
        searchKeyword($(this).data('item'));
    });

    $('.related-article').on('click', function (e){
        e.preventDefault();
        searchKeyword($(this).data('item'));
    });

    function debouncePromise(fn, time) {
        let timerId = undefined;

        return function debounced(...args) {
            if (timerId) {
                clearTimeout(timerId);
            }

            return new Promise((resolve) => {
                timerId = setTimeout(() => resolve(fn(...args)), time);
            });
        };
    }
      
    const debounced = debouncePromise((items) => Promise.resolve(items), 300);


})