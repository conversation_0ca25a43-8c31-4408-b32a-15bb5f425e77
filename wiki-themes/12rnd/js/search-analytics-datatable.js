const initArticleDt = () => {
	const dtConfig = {
		responsive: true,
		dom: 'Brtip',
		order: [[3, "desc"]],
		fixedHeader: true,
		// Show all results in table...
		iDisplayLength: -1,
		columnDefs: [
			{
				targets: "no-sort",
				orderable: false,
			},
			{
				targets: "searchable",
				searchable: true,
			},
			{ "width": "110px", "targets": 0 },
		],
		paging: false,
		info: false,
		language: {
			infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
			emptyTable: t('No data available in table'),
			zeroRecords: t('No matching records found'),
			infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
			aria: {
				sortAscending: `: ${t('activate to sort column ascending')}`,
				sortDescending: `: ${t('activate to sort column descending')}`
			}
		},
		buttons: [
			{
				extend: 'csv',
				fileName: 'articles',
				extension: '.csv',
				exportOptions: {
					columns: [ 0, 1, 2, 3, 4, 5 ],
					format: {
						body: function ( data, row, column, node ) {	
							return $(node).data('export');
						}
				}
				}
			}
		],
		initComplete: function () {
			$('.buttons-csv').hide()
		}
	}
	return $('#articles').DataTable(dtConfig);
}

const setFiltersBasedOnDtState = () => {
	const articleDt= getArticleDt();
	const state = articleDt.state();
	$('#article_filter').val(state.columns[0].search.search);


	//regions
	const regionDisplay = $('#filter-regions .filter-selected')	
	$('#filter-regions ul').children('li').each(function () {
		if(state.columns[4].search.search === '') {
			return false;
		}

		if($(this).data('filter') == state.columns[4].search.search) {
			$(this).addClass('filter__option--active');
			$(regionDisplay).text($(this).data('filter'));
		} else {
			$(this).removeClass('filter__option--active');
		}
	})

	//status
	const statusDisplay = $('#filter-status .filter-selected')
	$('#filter-status ul').children('li').each(function () {
		if(['', null, undefined].includes(state.columns[5].search.search)) {
			return false;
		}

		if($(this).data('filter').toString() === state.columns[5].search.search) {
		$(this).addClass('filter__option--active');
			$(statusDisplay).text($(this).text());
		} else {
			$(this).removeClass('filter__option--active');
		}
	})
	//status
	const topicDisplay = $('#filter-topics .filter-selected')
	$('#filter-topics ul').children('li').each(function () {
		if(state.columns[1].search.search === '') {
			return false;
		}

		if($(this).data('filter') == state.columns[1].search.search) {
			$(this).addClass('filter__option--active');
			$(topicDisplay).text($(this).data('filter'));
		} else {
			$(this).removeClass('filter__option--active');
		}
	})
}



function searchArticles() {
	const table = getArticleDt();

  const term = $("#article_filter").val();
	const region = $('#filter-regions .filter__option--active').data('filter');
  const topic = $('#filter-topics .filter__option--active').data('filter') ?? 'all'; 
	const status = $('#filter-status .filter__option--active').data('filter');
	
	const topicRegex = '\\b' + topic + '\\b';
	
	if(topic) {
		table.column(1) //topic 
		.search(topicRegex, true, false)
	} else {
		table.column(1) //topic 
		.search('')
	}

	table.column(0) //title
		.search(term ??  '')
		table.column(4)
		.search(region ?? '')
		.column(5)
		.search(status ?? '')
	table.draw()
}

const getArticleDt = () => {
	return $('#articles').DataTable();
}

$(document).ready(function () {
	//initialize datatable
	initArticleDt();

	$("#article_filter").on("keyup", function(){
		searchArticles();
	});

	setFiltersBasedOnDtState();

	//hide all filter options
	// filter options
	$(document).on('click', function (e) {
		const target = $(e.target);
		
		if(target.hasClass('filter-btn')){
			target.parent('.filter').find('.filter-options').addClass('filter__options--active')
			return;
		} 

		if(target.hasClass('filter-option')) {
			const filterValue = target.html() ?? 'All'

			target.parents('.filter').find('.filter-selected').html(filterValue);
			target.addClass('filter__option--active');	
			
			const siblings = target.siblings();

			siblings.each(function () {
				
				if($(this).data('filter') !== target.data('filter')) {
					$(this).removeClass('filter__option--active')
				} 
			})

			if (target.hasClass('region')) {
				//filter topics filter options to only show topics for selected region
				//get target elements data-filter value
				const region = target.data('filter');
				const topicOptions = $('#filter-topics ul.filter-options').children('li');
				//loop through each of the topicOptions
				//split the data visibility and check if region is part of the array
				//if it is, show the option, otherwise hide it
				$('#filter-topics ul.filter-options').children('li').first().trigger('click');
				if (region === '' || region === undefined) {	
					$(topicOptions).each(function () {
						$(this).show();
					})
				} else {
					$(topicOptions).each(function () {
						const visibility = $(this).data('visibility');
						const visibilityArray = visibility ? visibility.split(/(?:,)\s*/i) : [];
						if (visibilityArray.includes(region) || !visibility) {
							$(this).show();
						} else {
							$(this).hide();
						}
					})
				}
			}
			
			
			searchArticles();
			$('.filter-options').removeClass('filter__options--active');
			return;
		}
		
		$('.filter-options').removeClass('filter__options--active');

	})

	//show filter option
	$('.filter-btn').on('click', function() {
		$('.filter-options').removeClass('filter__options--active')
		$(this).parent('.filter').find('.filter-options').addClass('filter__options--active')
	})

	//trigger csv export for datatable
	$("#csv").on("click", function() {
		const articleDt = getArticleDt();

		articleDt.button(0).trigger()
 });

 //waves init
	Waves.init();
	Waves.attach('.waves-effect', 'waves-effect', true);
	


})

