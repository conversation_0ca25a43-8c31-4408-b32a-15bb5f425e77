
$(document).ready(function () {
	$('table tr').on("click", ".article-duplicate", async function (e) {
		e.stopPropagation();

		const articleId = $(this).data("id");
		let isRegionUpdatedManually = false;

		const swalActionButtons = (cb) => {
			const div = document.createElement('div');
			$(div).addClass('d-flex justify-center gap-1');
			$(div).html(`
					<button type="button" class="btn btn-primary waves-effect save-btn">${t('Save')}</button>
					<button type="button" class="btn btn-default waves-effect cancel-btn">${t('Cancel')}</button>
			`);
			$(div).find('.cancel-btn').on('click', () => Swal.close());
			$(div).find('.save-btn').on('click', () => cb());
			return div;
		}
		const swalWithBootstrapButtons = Swal.mixin({
			customClass: {
				confirmButton: 'btn btn-primary waves-effect',
				cancelButton: 'btn btn-default waves-effect'
			},
			buttonsStyling: false,
			animation: false,
		});

		let selectedLanguageCode = null;

		const html = document.createElement('div');
		html.setAttribute('id', 'duplicate-article-modal');

		if (!articleId) {
			return
		}
		const article = await $.ajax({
			method: "GET",
			url: $("#app_context").val() + "/api/articles/" + articleId + "/duplicate",
		});

		let selectedCountries = [];
		const countries = article.countries;
		        let countryCheckBoxesHtml = "";
        let regionalValue = article.kb_regional_visibility ? article.kb_regional_visibility.split(/(?:,)\s*/i).filter(item => {
            return countries.includes(item);
        }) : [];
		const regionals = regionalValue;
		//check if multiple countries

		selectedCountries.push(regionals);

		countries.forEach((country) => {

			let countryId = country.replace(/\s/g, "");
			countryCheckBoxesHtml += `
						<label for="dropCheck-${countryId}" class="countryLabel" id="countryLabel-${countryId}">
								<input type="checkbox" id="dropCheck-${countryId}"
								class="mr-2 countryCheck" data-country="${country}" ${regionals.includes(country) ? 'checked' : ''} />${country}
						</label>
				`;
		})

		const languages = $('#languages-options').val().split(',') ?? [];

		const getLanguageNameFromCode = (code, langOrigin) => {
			const displayNames = new Intl.DisplayNames([langOrigin || 'en'], { type: 'language' });
			return displayNames.of(code || 'en');
		}

		html.innerHTML = `
				<fieldset>
						<legend>Duplicate Article</legend>
						<form method="post" id="duplicate_form" action="/openkb/settings/articles/${articleId}/duplicate" data-toggle="validator">
								<input type="hidden" name="id" value="${articleId}" />
								<div class="mb-2 d-flex flex-column gap-2">
								<div class="form-group form-group-no-margin d-flex flex-column">
										<label class="form-label">Title</label>
										<input class="form-control input-normal" type="text" id="kb_dup_title" name="kb_dup_title" value="${article.kb_title}" required />
									</div>
									<div class="form-group" style="margin-bottom: 50px;">
										<div class="selectBox">
											<label class="form-label">Region</label>
											<input type="hidden" id="kb_dup_region" data-original="${regionalValue.join(',')}" name="kb_dup_region" value="${regionalValue.join(',')}" name="kb_dup_region" />
											<select class="form-control" id="selectCountryDropdown" data-options="${article.countries}">
													<option id="optionSelectCountry" value="">${regionalValue.length > 0 ? regionalValue.join(', ') : "Global"} </option>
											</select>
											<div class="overSelect" id="overSelect"></div>
										</div>	
										<div id="countryCheckboxes">${countryCheckBoxesHtml}</div>
										<small class="font-italic">"Articles without any regions configured will be globally visible."</small>
									</div>
	
									<div class="demo-switch">
										<div class="switch">
											<label style="min-width: max-content;">
													<input data-hj-allow type="checkbox" id="do-translate-switch" name="article_dup_do_translate">
													<span class="lever lever switch-col-blue"></span>
													Translate Article and Title
											</label>
										</div>
									</div>
									
									<div class="form-group form-group-no-margin d-flex flex-column">
										<label class="form-label">Language</label>
										<select class="selectpicker" data-width="100%" name="kb_dup_lang" id="kb_dup_lang" disabled>
											${languages.map((lang) => {
			return `<option value="${lang}" ${article.kb_language === lang.code ? 'selected' : ''}>${getLanguageNameFromCode(lang)}</option>`
		})}
										</select>
									</div>
									
								</div>
						</form>
						<em class="text-danger form-error-msg"></em>
				</fieldset>
		`;

		const languagePicker = $(html).find('select.selectpicker');
		const isTranslaste = $(html).find("#do-translate-switch");
		const regionalLanguage = {
			ja: ['Japan']
		}

		languagePicker.selectpicker();


		$(isTranslaste).on("change", function () {
			if ($(this).is(":checked")) {
				$(languagePicker).prop("disabled", false);
				$(languagePicker).selectpicker("refresh");
			} else {
				$(languagePicker).prop("disabled", true);
				$(languagePicker).selectpicker("refresh");
			}
		});

		const getSerializedObjectFromForm = (form) => {
			return $(form).serializeArray().reduce(function (a, x) {
				if (!a[x.name]) {
					a[x.name] = x.value;
				} else if (typeof a[x.name] === 'string') {
					a[x.name] = [a[x.name], x.value];
				} else {
					a[x.name].push(x.value);
				}
				return a;
			}, {});
		}

		const preConfirm = async () => {
			//$(html).find('form').trigger('submit');
			const formData = getSerializedObjectFromForm($(html).find('form'));

			Swal.fire({
				html: '<div><h5 style="padding: 40px;">' + t('Duplicating and/or Translating Article... <br />This may take a few moments - please be patient') + '...</h5></div>',
				width: 'auto',
				showCloseButton: false,
				showCancelButton: false,
				showConfirmButton: false,
				animation: false,
			})

			const result = await $.ajax({
				method: "POST",
				url: $("#app_context").val() + "/api/articles/" + formData.id + "/duplicate",
				data: formData
			})

			if (result.status !== 'success') {
				//return $(html).find('.form-error-msg').text('Something went wrong. Please try again later.');
				show_notification('Something went wrong. Please try again later.', 'danger')
			}

			Swal.close();

			return result;
		}

		Swal.fire({
			html,
			showCloseButton: true,
			showCancelButton: true,
			confirmButtonText: 'Duplicate',
			customClass: {
				confirmButton: 'btn btn-primary waves-effect',
				cancelButton: 'btn btn-default waves-effect',
				container: 'article-duplicate',
			},
			didOpen: function () {
				window.top.postMessage("modalShow", "*");
			},
			didClose: function () {
				window.top.postMessage("modalHide", "*");
				isRegionUpdatedManually = false;
			},
			preConfirm: preConfirm,
		}).then(async (result) => {
			console.log('result', result);
			if (!result.isDismissed) {
				show_notification('Article duplicated successfully', 'success')

				window.location.href = $("#app_context").val() + "/edit/" + result.value.data.id;
			}
		}).catch((err) => {
			show_notification('Something went wrong. Please try again later.', 'danger')
		});

		const swal = Swal.getContainer();
		let expanded = false;

		$(swal).on('click', function (e) {
			const clickTarget = e.target.id;

			if (clickTarget == "overSelect") {

				const checkboxes = document.getElementById("countryCheckboxes");

				if (!expanded) {
					$(swal).find("#countryCheckboxes").show()
					$(swal).find("#countryCheckboxes").css("width", $(swal).find(".overSelect").width());
					//adjust width a bit

					expanded = true;
				} else {
					$(swal).find("#countryCheckboxes").hide()
					expanded = false;
				}
			} else {
				//if the user clicks the dropdown checkbox - don't hide
				if (clickTarget.includes("countryLabel") || clickTarget.includes("dropCheck")) {
					$(swal).find("#countryCheckboxes").show()
					$(swal).find("#countryCheckboxes").css("width", $(swal).find(".overSelect").width());
					expanded = true;
				} else {
					if (expanded) {
						$(swal).find("#countryCheckboxes").hide()
						expanded = false;
					}
				}
			}
		})

		//event when checked one of country checkboxes
		$(swal).find(".countryLabel").on("click", function () {
			const countryElem = $(this).find(".countryCheck");

			const country = $(countryElem).data("country");
			const checkedCountries = $(swal).find("#kb_dup_region").val().split(/(?:,)\s*/i);

			//remove empty items in checkedCountries
			if (checkedCountries.indexOf('') !== -1) {
				checkedCountries.splice(checkedCountries.indexOf(''), 1);
			}

			if (!$(countryElem).is(":checked")) {
				checkedCountries.push(country);
				$(countryElem).prop('checked', true);
				isRegionUpdatedManually = true;
			} else {

				if (checkedCountries.indexOf(country) !== -1) {
					checkedCountries.splice(checkedCountries.indexOf(country), 1);
				}
				$(countryElem).prop('checked', false);
			}

			$(swal).find("#optionSelectCountry").text(checkedCountries.join(', '));
			$(swal).find("#kb_dup_region").val(checkedCountries.join(','));
		});

		languagePicker.on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
			const affectedRegions = regionalLanguage[$(this).val()] ?? [];
			
			if(affectedRegions.length > 0 && !isRegionUpdatedManually) {	
				$(swal).find("#kb_dup_region").val(affectedRegions.join(','));
				$(swal).find("#optionSelectCountry").text(affectedRegions.join(','));
				$(swal).find('.countryCheck').prop('checked', false);
				affectedRegions.forEach((region) => {
					const countryId = region.replace(/\s/g, "");
					$(swal).find(`#dropCheck-${countryId}`).prop('checked', true);
				});
			} else if (!isRegionUpdatedManually) {
				const originalValue = $(swal).find("#kb_dup_region").data('original');
				$(swal).find('.countryCheck').prop('checked', false);
				$(swal).find("#kb_dup_region").val(originalValue);
				$(swal).find("#optionSelectCountry").text(originalValue.split(/(?:,)\s*/i).join(', '));

				originalValue.split(/(?:,)\s*/i).forEach((region) => {
					const countryId = region.replace(/\s/g, "");
					$(swal).find(`#dropCheck-${countryId}`).prop('checked', true);
				});
			}
				

			
		});
	});
	
});

