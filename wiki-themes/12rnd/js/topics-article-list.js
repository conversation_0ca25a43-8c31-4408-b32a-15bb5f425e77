$(document).ready(async function () {
    //await initTranslator();
    const appContext = $('#app_context').val();
    const algoliaConfig = {
        index: $('#algolia-index').val(),
        appId: $('#algolia-appId').val(),
        apiKey: $('#algolia-apiKey').val(),
    }
    const searchClient = algoliasearch(algoliaConfig.appId, algoliaConfig.apiKey);
    const index = searchClient.initIndex(algoliaConfig.index)
    const docId = $('#doc_id').val() ?? null; 

    const getAndRenderArticles = async (topic, subTopic = '') => {

        $('#featured-articles').html('');
        $('#article-list').html('');

        const result = await $.ajax({
            url: `${appContext}/api/topic/${topic}/articles/${subTopic !== '' ? subTopic : ''}`,
            type: "GET",
        });

        if (result.status == 'success') {
            let featuredArticlesHtml = '';

            //render featured articles

            result.featured_articles.forEach(function (article) {
                featuredArticlesHtml += `<li><a class="${docId == article._id ? 'active' : ''} search-results-tags" href="${appContext}/kb/${article.kb_permalink}">${article.kb_title}</a> <span class="keywordSeparator">&nbsp;</span></li>`
            })

            if (result.featured_articles.length > 0) {
                $('#featured-articles').html(featuredArticlesHtml);
            }

            //render topic articles
            let articlesListHtml = '';
            result.articles.forEach(function (article) {
                articlesListHtml += `<div class="search-result-card" data-article-permalink="${article.kb_permalink}">
                  <div class="search-card-title">
                      <div class="">
                          <a class="search-result-title"
                              href="${appContext}/kb/${article.kb_permalink}">${article.kb_title}</a>
                      </div>
                  </div>
                  <div class="search-card-sub-title">
                      <div class="article-metadata">
                        <div class="article-author">
                            ${article.kb_author}
                        </div>
                        <div class="article-date">
                          
                         ${moment(article.kb_published_date).format('DD/MM/YYYY h:mmA')}
                          </div>
                      </div>
                  </div>
                  <div class="article-body-preview"> 
                    <div class="search-result-body">${article.kb_body.substring(0, 350)} &hellip; </div>
                  </div>

                  ${article.kb_keywords ? `<div class="article-keywords">
                          <i class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                          <g clip-path="url(#clip0_3566_420)">
                          <path d="M4.25 4.75007C4.25 4.07675 3.70904 3.53578 3.03571 3.53578C2.36239 3.53578 1.82143 4.07675 1.82143 4.75007C1.82143 5.42339 2.36239 5.96436 3.03571 5.96436C3.70904 5.96436 4.25 5.42339 4.25 4.75007ZM14.3723 10.2144C14.3723 10.5367 14.2393 10.85 14.0214 11.068L9.36336 15.7357C9.13568 15.9537 8.82239 16.0866 8.5 16.0866C8.17761 16.0866 7.86432 15.9537 7.64636 15.7357L0.863357 8.943C0.379464 8.46882 0 7.54839 0 6.87507V2.92864C0 2.26443 0.550071 1.71436 1.21429 1.71436H5.16071C5.83404 1.71436 6.75446 2.09382 7.23836 2.57771L14.0214 9.351C14.2393 9.57868 14.3723 9.89196 14.3723 10.2144ZM18.0151 10.2144C18.0151 10.5367 17.8822 10.85 17.6642 11.068L13.0062 15.7357C12.7785 15.9537 12.4652 16.0866 12.1429 16.0866C11.6499 16.0866 11.4027 15.859 11.0804 15.5269L15.5392 11.068C15.7572 10.85 15.8901 10.5367 15.8901 10.2144C15.8901 9.89196 15.7572 9.57868 15.5392 9.351L8.75621 2.57771C8.27232 2.09382 7.3525 1.71436 6.67857 1.71436H8.80357C9.47689 1.71436 10.3973 2.09382 10.8812 2.57771L17.6642 9.351C17.8822 9.57868 18.0151 9.89196 18.0151 10.2144Z" fill="#D5DBE1"/>
                          </g>
                          <defs>
                          <clipPath id="clip0_3566_420">
                          <rect width="18.2143" height="17" fill="white" transform="translate(0 0.5)"/>
                          </clipPath>
                          </defs>
                          </svg>
                          </i>
                          ${article.kb_keywords.split(', ').map(function (keyword) {
                    //return `<a class="search-results-tags" href="${appContext}/?q=${keyword}">${keyword}</a> <span class="keywordSeparator">&nbsp;</span>`
                    return `<span class="article-tags" data-item="${keyword}">${keyword}</span>`
                }).join('')}
                      </div>`
                        : ''}
              </div>`

            })

            $('#article-list').html(articlesListHtml);
        }

    }

    const getRecentlyViewedArticles = () => {
        return JSON.parse(localStorage.getItem('viewedArticles') || '[]').slice(0, 4);
    }

    const renderNav = async () => {
        const recentlyViewedArticles = getRecentlyViewedArticles();
        const navItem = $('#topic-page-nav')


        if(recentlyViewedArticles.length > 0) {
            recentlyViewedArticles.forEach(article => {
                $(navItem).append(`<li><a class="${docId == article.id ? 'active' : ''}" title="${article.label}" href="${article.url}">${article.label}</li>`)
            })
        } else {
            const algoliaArgs = {
                hitsPerPage: 3,
                attributesToRetrieve: ['_id', 'kb_title', 'kb_permalink']
            }

            const data = {
                searchTerms: '',
                club: $('#club').val() ?? '',
                algoliaArgs,
            }

            const club = $('#club').val();

            if( club ) {
                data.club = club;
            }

        
            return fetch('/api/algolia/search-wiki/', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                  },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(( result) => {
                result.suggestedArticles.forEach(article => {
                    $(navItem).append(`<li><a class="${docId == article._id ? 'active' : ''}" title="${article.kb_title}" href="${appContext}/kb/${article.kb_permalink ?? article._id}">${article.kb_title}</li>`)
                })
            });
        }
    }

    const topic = $('#topic').val();
    const subTopic = $('#subTopicsList').val();

    //on load
    renderNav();
    await getAndRenderArticles(topic, subTopic);


    //subtopiclist on change
    $('#subTopicsList').on('change', async function () {
        const subTopic = $(this).val();
        await getAndRenderArticles(topic, subTopic);
    })

    $('.subtopic-filter').on('click', async function (e) {
        if(!docId) {
            e.preventDefault();
            $('.subtopic-filter').removeClass('active');
            $(this).addClass('active')
            const subTopic = $(this).data('subtopic');
            await getAndRenderArticles(topic, subTopic);
        }
    })

    $('#article-list').on('click', '.search-result-card', function () {
        const permalink = $(this).data('article-permalink');

        window.location.href = `${appContext}/kb/${permalink}`;
    })

   
    if($('#filters').height() < $('.main-content').height()) {
        $('#filters').addClass('sticky');
    }
    if($('#right-menu').height() < $('.main-content').height()) {
        $('#right-menu').addClass('sticky');
    }
})