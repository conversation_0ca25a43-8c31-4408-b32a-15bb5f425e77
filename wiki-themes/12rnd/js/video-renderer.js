/**
 * Streaming Video Rendering Module
 * Handles JSON metadata and constructs proper streaming URLs for HLS/DASH playback
 */

// Global video tracking - shared across all contexts
window.processedVideos = window.processedVideos || new Set();
window.processingVideos = window.processingVideos || new Set();

// Enhanced debug logging utility
const VideoDebugLogger = {
    enabled: true,
    prefix: '[StreamingVideoRenderer]',

    log: function(level, message, data = null) {
        if (!this.enabled) return;

        const timestamp = new Date().toISOString();
        const logMessage = `${this.prefix} [${timestamp}] ${level}: ${message}`;

        if (data) {
            console.log(logMessage, data);
        } else {
            console.log(logMessage);
        }
    },

    info: function(message, data) { this.log('INFO', message, data); },
    warn: function(message, data) { this.log('WARN', message, data); },
    error: function(message, data) { this.log('ERROR', message, data); },
    debug: function(message, data) { this.log('DEBUG', message, data); },
    network: function(message, data) { this.log('NETWORK', message, data); },
    streaming: function(message, data) { this.log('STREAMING', message, data); },
    playback: function(message, data) { this.log('PLAYBACK', message, data); },
    hls: function(message, data) { this.log('HLS', message, data); },
    dash: function(message, data) { this.log('DASH', message, data); },
    success: function(message, data) { this.log('SUCCESS', message, data); }
};

/**
 * StreamingVideoRenderer class - uses HLS/DASH streaming instead of blob approach
 */
class StreamingVideoRenderer {
    constructor(options = {}) {
        this.processedVideos = window.processedVideos;
        this.processingVideos = window.processingVideos;
        this.debugLogger = VideoDebugLogger;
        
        this.defaultOptions = {
            containerSelectors: ['.video-embed-container'],
            fileProxySelectors: ['.file-to-proxy'],
            targetContainers: ['body'],
            staggerDelay: 100,
            retryDelay: 200,
            maxRetries: 3,
            ...options
        };
        

    }

    /**
     * Main activation function
     */
    activateVideoPlayers(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const containers = this._getTargetContainers(config.targetContainers);
        

        
        if (containers.length === 0) {
            this.debugLogger.warn('No target containers found for video activation');
            return;
        }

        // Process existing video-embed-container elements
        this._processVideoContainers(containers, config);
        
        // Process file-to-proxy elements and convert them
        this._processFileProxyElements(containers, config);
        
        // Process uploaded-media containers with video img tags
        this._processUploadedMediaElements(containers, config);
    }

    /**
     * Enhanced URL processing with metadata handling
     */
    _processVideoURL(url) {
        let processedUrl = url;

        // Check if it's a secure upload URL and remove /private prefix
        if (url.includes('secure-upload') && url.includes('/private')) {
            processedUrl = url.replace('/private', '');
        }
        
        return {
            processedUrl,
            analysis: { requiresMetadata: url.includes('secure-upload') }
        };
    }

    /**
     * Enhanced video loading with streaming support
     */
    async _loadVideo(container, url) {
        const currentPlaceholder = container.find('.video-loading-placeholder');
        if (currentPlaceholder.length === 0) {
            this.debugLogger.warn('No placeholder found for video loading', { url });
            return;
        }
        
        // Process URL for security and debugging
        const { processedUrl, analysis } = this._processVideoURL(url);
        
        const loadingMsg = $('<div>', {
            html: 'Loading video...',
            'data-video-loading': 'true',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
        currentPlaceholder.replaceWith(loadingMsg);
        
        try {
            if (analysis.requiresMetadata) {
                // Handle secure uploads with metadata
                await this._loadSecureVideo(container, loadingMsg, url, processedUrl);
            } else {
                // Handle direct video URLs
                await this._loadDirectVideo(container, loadingMsg, url);
            }
        } catch (error) {
            this.debugLogger.error('Video loading failed', {
                error: error.message,
                url: url,
                processedUrl: processedUrl
            });
            
            this._showError(loadingMsg, error.message);
            
            // Remove from processing on error
            if (url) {
                this.processingVideos.delete(url);
            }
        }
    }

    /**
     * Load secure upload videos by getting metadata first
     */
    async _loadSecureVideo(container, loadingMsg, originalUrl, metadataUrl) {

        
        try {
            // Step 1: Get metadata

            
            const response = await fetch(metadataUrl, { 
                method: 'GET', 
                credentials: 'include'
            });
            

            
            if (!response.ok) {
                const errorText = await response.text();
                this.debugLogger.error('Metadata fetch failed', {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });
                throw new Error(`Metadata fetch failed: HTTP ${response.status} - ${errorText}`);
            }
            
            const metadata = await response.json();

            

            
            // Step 2: Check processing status
            const statusCheck = this._checkProcessingStatus(metadata);
            if (!statusCheck.canPlay) {
                this._showProcessingStatus(loadingMsg, statusCheck);
                
                if (statusCheck.shouldRetry) {
                    // Retry after delay for processing videos
                    setTimeout(() => {

                        this._loadVideo(container, originalUrl);
                    }, 60000); // Retry in 1 minute
                }
                return;
            }
            
            // Step 3: Construct streaming URL from metadata
            const urlData = this._constructStreamingURL(metadata, originalUrl);

            
            // Step 4: Create streaming player with fallback support
            await this._createStreamingPlayerWithFallback(container, loadingMsg, originalUrl, urlData, metadata);
            
        } catch (error) {
            throw new Error(`Secure video loading failed: ${error.message}`);
        }
    }

    /**
     * Load direct video URLs (non-secure uploads)
     */
    async _loadDirectVideo(container, loadingMsg, url) {

        
        // For direct URLs, create simple URL data structure
        const urlData = {
            primaryUrl: url,
            fallbackUrls: []
        };
        
        await this._createStreamingPlayerWithFallback(container, loadingMsg, url, urlData, null);
    }

    /**
     * Check processing status from metadata
     */
    _checkProcessingStatus(metadata) {
        const status = metadata.status;
        const fileType = metadata.fileType;
        

        
        if (status === 'error') {
            return {
                canPlay: false,
                shouldRetry: false,
                message: metadata.message || 'Video processing failed',
                isError: true
            };
        }
        
        if (['to process', 'processing'].includes(status)) {
            return {
                canPlay: false,
                shouldRetry: true,
                message: 'Processing video...',
                progress: metadata.progress || 0
            };
        }
        
        if (status === 'uploaded' && fileType === 'file') {
            // Direct file upload, ready to play
            return { canPlay: true };
        }
        
        if (status === 'ready' || status === 'complete') {
            // Processed video, ready to play
            return { canPlay: true };
        }
        
        // Unknown status, assume ready
        this.debugLogger.warn('Unknown processing status, assuming ready', { status, fileType });
        return { canPlay: true };
    }

    /**
     * Determine file type from filename extension (matches backend logic)
     */
    _getFileType(filename) {
        const extension = filename.split('.').pop().toLowerCase();

        // Match backend extension arrays exactly
        const videoExtensions = ['avi', 'mkv', 'mp4', 'webm', 'mov', 'mpeg'];
        const audioExtensions = ['mp3', 'wav', 'aac', 'ogg', 'm4a'];
        const imageExtensions = ['bmp', 'gif', 'jpg', 'jpeg', 'png', 'webp', 'svg'];

        if (videoExtensions.includes(extension)) {
            return 'video';
        } else if (audioExtensions.includes(extension)) {
            return 'audio';
        } else if (imageExtensions.includes(extension)) {
            return 'image';
        } else {
            // Default to file for unknown types
            return 'file';
        }
    }

    /**
     * Construct streaming URL from metadata with fallback logic
     */
    _constructStreamingURL(metadata, originalUrl) {
        const { bucket, s3Key, region } = metadata;
        
        if (!bucket || !s3Key || !region) {
            throw new Error('Missing required metadata fields for streaming URL construction');
        }
        
        // Extract organisationId from original URL if present
        let organisationId = null;
        if (originalUrl && originalUrl.includes('organisationId=')) {
            const match = originalUrl.match(/organisationId=([^&]+)/);
            if (match) {
                organisationId = match[1];
            }
        }

        // Fallback: try to get organisationId from metadata if URL doesn't have it
        if (!organisationId && metadata && metadata.organisationId) {
            organisationId = metadata.organisationId;

        }

        // Fallback: try to get from global USER_SESSION if available
        if (!organisationId && typeof USER_SESSION !== 'undefined' && USER_SESSION.organisationId) {
            organisationId = USER_SESSION.organisationId;

        }

        // Build query string with organisationId if available
        const queryString = organisationId ? `?organisationId=${organisationId}` : '';


        
        // Handle both correct type prefix and incorrect "file/" prefix
        // Extract the filename and detect proper type
        const [currentType, filename] = s3Key.includes('/') ? s3Key.split('/', 2) : ['file', s3Key];
        const detectedType = this._getFileType(filename);
        const correctS3Key = `${detectedType}/${filename}`;
        

        
        // Try different URL patterns in order of preference
        // Based on coworker's guidance: /admin/secure-upload/{public/private}/{type}/{key}
        const possibleUrls = [
            // Correct pattern with proper type prefix
            `/admin/secure-upload/public/${correctS3Key}${queryString}`,
            `/admin/secure-upload/private/${correctS3Key}${queryString}`,
            // Original s3Key pattern (in case upload stored it correctly)
            `/admin/secure-upload/public/${s3Key}${queryString}`,
            `/admin/secure-upload/private/${s3Key}${queryString}`,
            // API variants
            `/api/admin/secure-upload/public/${correctS3Key}${queryString}`,
            `/api/admin/secure-upload/private/${correctS3Key}${queryString}`,
            `/api/admin/secure-upload/public/${s3Key}${queryString}`,
            `/api/admin/secure-upload/private/${s3Key}${queryString}`,
            // Without organisationId (fallback)
            `/admin/secure-upload/public/${correctS3Key}`,
            `/admin/secure-upload/private/${correctS3Key}`,
            `/admin/secure-upload/public/${s3Key}`,
            `/admin/secure-upload/private/${s3Key}`,
            // Direct S3 URLs (fallback)
            `https://${bucket}.s3.${region}.amazonaws.com/${s3Key}`,
            `https://s3.${region}.amazonaws.com/${bucket}/${s3Key}`,
            // Original URL (last resort)
            originalUrl
        ];
        

        
        return {
            primaryUrl: possibleUrls[0],
            fallbackUrls: possibleUrls.slice(1)
        };
    }

    /**
     * Create streaming player with URL fallback support
     */
    async _createStreamingPlayerWithFallback(container, loadingMsg, originalUrl, urlData, metadata) {

        
        const urlsToTry = [urlData.primaryUrl, ...urlData.fallbackUrls];
        
        for (let i = 0; i < urlsToTry.length; i++) {
            const streamingUrl = urlsToTry[i];
            
            try {
                // Test URL accessibility
                const canAccess = await this._testUrlAccess(streamingUrl);
                
                if (canAccess) {

                    await this._createStreamingPlayer(container, loadingMsg, originalUrl, streamingUrl, metadata);
                    return;
                } else {

                }
            } catch (error) {

            }
        }
        
        // All URLs failed
        throw new Error('All streaming URL candidates failed - check S3 permissions or proxy configuration');
    }
    
    /**
     * Test URL accessibility and content type
     */
    async _testUrlAccess(url) {
        try {


            const response = await fetch(url, {
                method: 'HEAD',
                credentials: 'include'
            });

            const contentType = response.headers.get('content-type');
            const contentLength = response.headers.get('content-length');

            // Enhanced content type checking for streaming endpoints
            const isVideoContent = contentType && (
                contentType.startsWith('video/') ||
                contentType.startsWith('audio/') ||
                contentType.startsWith('application/dash+xml') ||
                contentType.startsWith('application/vnd.apple.mpegurl') ||
                contentType.startsWith('application/octet-stream')
            );

            // Accept more status codes for streaming
            const accessible = (response.ok || response.status === 206 || response.status === 416) && isVideoContent;



            // Special handling for 404s on streaming endpoints - log for debugging
            if (response.status === 404 && url.includes('/api/admin/secure-upload/')) {
                this.debugLogger.error('Streaming endpoint returned 404 - check backend route configuration', {
                    url: url,
                    expectedBackendRoute: 'Should match /api/admin/secure-upload/{public|private}/:type/:key',
                    troubleshooting: 'Verify portal-core backend is running and routes are registered'
                });
            }

            return accessible;
        } catch (error) {
            this.debugLogger.streaming('URL access test failed', {
                url: url,
                error: error.message,
                isNetworkError: error.name === 'TypeError' && error.message.includes('fetch')
            });
            return false;
        }
    }
    
    /**
     * Create streaming player using HLS/DASH
     */
    async _createStreamingPlayer(container, loadingMsg, originalUrl, streamingUrl, metadata) {
        this.debugLogger.streaming('Creating streaming player', { streamingUrl });

        try {
            // Determine if it's audio or video
            const isAudio = streamingUrl.includes('/audio/') ||
                           (metadata && metadata.fileType === 'audio') ||
                           streamingUrl.match(/\.(mp3|wav|ogg|aac)(\?|$)/i);

            // Create video/audio element
            const mediaElement = this._createMediaElement(originalUrl, streamingUrl, isAudio, metadata);

            // Replace loading message with media element
            loadingMsg.replaceWith(mediaElement);

            // Initialize streaming
            this._initializeStreaming(mediaElement, streamingUrl);

            // Mark as processed
            this.processedVideos.add(originalUrl);
            this.processingVideos.delete(originalUrl);

            this.debugLogger.streaming('Streaming player created', { streamingUrl });

        } catch (error) {
            this.debugLogger.error('❌ Failed to create streaming player', {
                originalUrl,
                streamingUrl,
                error: error.message,
                stack: error.stack
            });

            // Clean up processing state
            this.processingVideos.delete(originalUrl);
            throw error;
        }
    }

    /**
     * Create media element (video or audio)
     */
    _createMediaElement(originalUrl, streamingUrl, isAudio, metadata) {
        const elementType = isAudio ? 'audio' : 'video';
        
        const mediaElement = $(`<${elementType}>`, {
            controls: true,
            crossorigin: 'anonymous',
            playsinline: true,
            preload: 'metadata',
            'data-video-url': originalUrl,
            'data-streaming-url': streamingUrl,
            'data-streaming-type': 'hls-dash',
            css: {
                width: '100%',
                maxWidth: isAudio ? '400px' : '800px',
                margin: '1rem 0',
                display: 'block'
            }
        });
        
        // Add poster for video if available
        if (!isAudio && metadata && streamingUrl.includes('/video/')) {
            const posterUrl = streamingUrl.replace('/video/', '/poster/').split('.')[0];
            mediaElement.attr('data-poster', posterUrl);
        }
        
        // Essential playback events only
        mediaElement.on('loadedmetadata', () => {
            this.debugLogger.streaming('Metadata loaded');
        });

        mediaElement.on('canplay', () => {
            this.debugLogger.streaming('Can play');
        });

        // Remove timeupdate spam - only log first successful play

        // Remove progress spam - only log errors
        mediaElement.on('error', () => {
            const mediaEl = mediaElement[0];
            const error = mediaEl.error;
            
            // Detailed error mapping
            const errorTypes = {
                1: 'MEDIA_ERR_ABORTED',
                2: 'MEDIA_ERR_NETWORK',
                3: 'MEDIA_ERR_DECODE',
                4: 'MEDIA_ERR_SRC_NOT_SUPPORTED'
            };
            
            this.debugLogger.error('Media playback error', {
                url: originalUrl,
                streamingUrl: streamingUrl,
                errorCode: error ? error.code : 'unknown',
                errorType: error ? errorTypes[error.code] || 'unknown' : 'unknown',
                errorMessage: error ? error.message : 'unknown',
                networkState: mediaEl.networkState,
                readyState: mediaEl.readyState,
                currentSrc: mediaEl.currentSrc
            });
        });



        return mediaElement;
    }

    /**
     * Initialize streaming with HLS/DASH
     */
    _initializeStreaming(mediaElement, streamingUrl) {
        const mediaEl = mediaElement[0];
        
        this.debugLogger.streaming('Initializing streaming', { streamingUrl });
        
        // For .mov files and internal streaming URLs, use native HTML5 playback with direct source
        if (streamingUrl.includes('/api/admin/stream/') || streamingUrl.endsWith('.mov')) {
    
            mediaEl.src = streamingUrl;
            
            // Initialize Plyr for better UI if available
            if (typeof window.Plyr === 'function') {
                try {
                    const plyrOptions = this._getPlyrOptions();
                    const player = new window.Plyr(mediaEl, plyrOptions);
                    mediaElement.data('plyr', player);
                    this.debugLogger.streaming('Plyr player initialized for native playback', { streamingUrl });
                } catch (error) {
                    this.debugLogger.warn('Plyr initialization failed, using basic controls', { error: error.message });
                }
            }
            return;
        }
        
        // Detect content type from URL to choose correct player
        if (streamingUrl.includes('.m3u8') || streamingUrl.includes('mpegurl')) {
            // HLS content detected
            this.debugLogger.streaming('HLS content detected');
            if (this._isAppleDevice()) {
                mediaEl.src = streamingUrl;
            } else if (window.Hls && window.Hls.isSupported()) {
                this._initializeHLSPlayer(mediaElement, streamingUrl);
            } else {
                mediaEl.src = streamingUrl;
            }
        } else if (streamingUrl.includes('.mpd') || streamingUrl.includes('dash')) {
            // DASH content detected
            this.debugLogger.streaming('DASH content detected');
            if (window.dashjs && typeof window.dashjs.MediaPlayer === 'function') {
                this._initializeDASHPlayer(mediaElement, streamingUrl);
            } else {
                mediaEl.src = streamingUrl;
            }
        } else {
            // Unknown content type - try to detect from response
            this._detectAndInitializeStreaming(mediaElement, streamingUrl);
        }
    }

    /**
     * Detect streaming type from response and initialize appropriate player
     */
    async _detectAndInitializeStreaming(mediaElement, streamingUrl) {
        try {
            const response = await fetch(streamingUrl, {
                method: 'HEAD',
                credentials: 'include'
            });

            const contentType = response.headers.get('content-type');
            this.debugLogger.streaming('Content type detected', { contentType });

            if (contentType && contentType.includes('mpegurl')) {
                // HLS content
                this.debugLogger.streaming('🎵 HLS detected from content-type');
                if (this._isAppleDevice()) {
                    mediaElement[0].src = streamingUrl;
                } else if (window.Hls && window.Hls.isSupported()) {
                    this._initializeHLSPlayer(mediaElement, streamingUrl);
                } else {
                    mediaElement[0].src = streamingUrl;
                }
            } else if (contentType && contentType.includes('dash')) {
                // DASH content
                this.debugLogger.streaming('⚡ DASH detected from content-type');
                if (window.dashjs) {
                    this._initializeDASHPlayer(mediaElement, streamingUrl);
                } else {
                    mediaElement[0].src = streamingUrl;
                }
            } else {
                // Fallback to direct source
                this.debugLogger.streaming('📺 Using direct source as fallback');
                mediaElement[0].src = streamingUrl;
            }
        } catch (error) {
            this.debugLogger.error('❌ Failed to detect content type', { error: error.message });
            // Fallback to direct source
            mediaElement[0].src = streamingUrl;
        }
    }

    /**
     * Initialize HLS player for Apple devices
     */
    _initializeHLSPlayer(mediaElement, streamingUrl) {

        
        const mediaEl = mediaElement[0];
        const hls = new window.Hls();
        
        // Use Plyr if available
        if (typeof window.Plyr === 'function') {
            const plyrOptions = this._getPlyrOptions();
            
            plyrOptions.quality.onChange = function (quality) {
                hls.levels.forEach((level, levelIndex) => {
                    if (level.height !== quality) return;
                    hls.currentLevel = levelIndex;
                });
            };
            
            const player = new window.Plyr(mediaEl, plyrOptions);
            
            player.on('play', () => {
                if (!hls.media) {
                    hls.loadSource(streamingUrl);
                    hls.attachMedia(mediaEl);
                }
            });
            
            // Store references for cleanup
            mediaElement.data('hls', hls);
            mediaElement.data('plyr', player);
        } else {
            // Direct HLS without Plyr
            hls.loadSource(streamingUrl);
            hls.attachMedia(mediaEl);
        }
    }

    /**
     * Initialize DASH player for other browsers
     */
    _initializeDASHPlayer(mediaElement, streamingUrl) {

        
        const mediaEl = mediaElement[0];
        const dash = window.dashjs.MediaPlayer().create();
        
        // Use Plyr if available
        if (typeof window.Plyr === 'function') {
            const plyrOptions = this._getPlyrOptions();
            
            plyrOptions.quality.onChange = function (quality) {
                const qualityIndex = plyrOptions.quality.options.findIndex((q) => q === quality);
                dash.setQualityFor("video", qualityIndex);
            };
            
            const player = new window.Plyr(mediaEl, plyrOptions);
            
            player.on('play', () => {
                if (!dash.isReady()) {
                    dash.initialize(mediaEl, streamingUrl, false);
                }
            });
            
            // Store references for cleanup
            mediaElement.data('dash', dash);
            mediaElement.data('plyr', player);
        } else {
            // Direct DASH without Plyr
            dash.initialize(mediaEl, streamingUrl, false);
        }
    }

    /**
     * Get Plyr options (use existing defaultPlyrOptions if available)
     */
    _getPlyrOptions() {
        if (window.defaultPlyrOptions) {
            return { ...window.defaultPlyrOptions };
        }
        
        return {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            quality: {
                default: 720,
                options: [1080, 720, 480, 360],
                forced: true,
                onChange: null
            }
        };
    }

    /**
     * Check if device is Apple
     */
    _isAppleDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }

    /**
     * Get human-readable ready state text for debugging
     */
    _getReadyStateText(readyState) {
        const states = {
            0: 'HAVE_NOTHING',
            1: 'HAVE_METADATA',
            2: 'HAVE_CURRENT_DATA',
            3: 'HAVE_FUTURE_DATA',
            4: 'HAVE_ENOUGH_DATA'
        };
        return states[readyState] || `UNKNOWN(${readyState})`;
    }

    /**
     * Get human-readable network state text for debugging
     */
    _getNetworkStateText(networkState) {
        const states = {
            0: 'NETWORK_EMPTY',
            1: 'NETWORK_IDLE',
            2: 'NETWORK_LOADING',
            3: 'NETWORK_NO_SOURCE'
        };
        return states[networkState] || `UNKNOWN(${networkState})`;
    }

    /**
     * Show processing status for videos still being processed
     * Uses consistent blue UI theme with CSS classes from openkb.scss
     */
    _showProcessingStatus(loadingMsg, statusInfo) {
        let statusHtml;

        if (statusInfo.isError) {
            // Error state - red theme
            statusHtml = `
                <div class="ph-alert ph-alert_danger">
                    <div class="ph-alert__title">${statusInfo.message}</div>
                    <div class="ph-alert__description">Video processing failed. Please try uploading again.</div>
                </div>
            `;
        } else {
            /* 
                // const progress = statusInfo.progress || 0;
                // <div class="video-processing-header">
                //     <div class="video-processing-spinner"></div>
                //     <span class="video-processing-title">${statusInfo.message}</span>
                //     ${progress > 0 ? `<span class="video-processing-percentage">${progress}%</span>` : ''}
                // </div>
                // ${progress > 0 ? `
                //     <div class="video-processing-progress">
                //         <div class="video-processing-progress-bar" style="width: ${progress}%;"></div>
                //     </div>
                // ` : ''}
                // <small class="video-processing-message"></small>
            */
            // Processing state - consistent blue theme
            statusHtml = `
                <div class="video-processing-status ph-alert ph-alert_info">
                    <div class="ph-alert__title"><div class="ph-spinner"></div>${statusInfo.message}</div> 
                    <div class="ph-alert__description">
                        <div>You can save this file and close this tab. Feel free to return later to view the video once processing is complete.<div>
                    </div> 
                </div>
            `;
        }

        loadingMsg.html(statusHtml);

    }

    /**
     * Show error message
     */
    _showError(loadingMsg, errorMessage) {
        const errorHtml = `
            <div class="video-error-status" style="padding: 1rem; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
                <strong>Error:</strong> ${errorMessage}<br>
                <small>Check browser console for detailed logs</small>
            </div>
        `;
        
        loadingMsg.html(errorHtml);
        loadingMsg.removeClass('video-loading-placeholder').addClass('video-error');
    }

    // Include all the standard methods from the original implementation
    _getTargetContainers(selectors) {
        if (typeof $ === 'undefined') {
            this.debugLogger.error('jQuery not available, cannot process videos');
            return { length: 0, find: () => ({ length: 0, each: () => {} }) };
        }
        
        const containers = [];
        selectors.forEach(selector => {
            const elements = $(selector);
            if (elements.length > 0) {
                containers.push(...elements.toArray());
                this.debugLogger.debug('Found containers', { selector, count: elements.length });
            }
        });
        

        
        return $(containers);
    }

    _processVideoContainers(containers, config) {
        const videoContainers = containers.find(config.containerSelectors.join(', '));
        

        
        videoContainers.each((index, element) => {
            this._processVideoContainer($(element), index * config.staggerDelay);
        });
    }

    _processVideoContainer(container, delay = 0) {
        const videoUrl = container.attr('data-video-url');
        

        
        if (videoUrl && this.processingVideos.has(videoUrl)) {
            this.debugLogger.warn('Video already being processed, skipping', { videoUrl });
            return;
        }
        
        if (videoUrl && this.processedVideos.has(videoUrl)) {
            const existingMedia = container.find('video, audio');
            if (existingMedia.length > 0 && !existingMedia[0].error) {
                this.debugLogger.debug('Video already processed and working, skipping', { videoUrl });
                return;
            }
            this.debugLogger.info('Re-processing previously processed video', { videoUrl });
            this.processedVideos.delete(videoUrl);
        }
        
        // Clear existing content
        container.find('video, audio, .video-loading-placeholder, .video-error-status, .video-processing-status').remove();
        container.append(this._createPlaceholder());
        
        if (videoUrl) {
            this.processingVideos.add(videoUrl);
            setTimeout(() => {
                this._loadVideo(container, videoUrl);
            }, delay);
        }
    }

    _processFileProxyElements(containers, config) {
        const proxyElements = containers.find(config.fileProxySelectors.join(', '));
        

        
        proxyElements.each((index, element) => {
            this._convertFileProxyToVideoContainer($(element), config.retryDelay + (index * config.staggerDelay));
        });
    }

    _convertFileProxyToVideoContainer(element, delay = 0) {
        const url = element.attr('data-url');
        
        if (!url || this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'streaming',
            'data-converted-from': 'file-to-proxy',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _processUploadedMediaElements(containers, config) {
        const uploadedMediaElements = containers.find('.uploaded-media');
        
        uploadedMediaElements.each((index, element) => {
            const $element = $(element);
            const $img = $element.find('img');
            
            if ($img.length > 0) {
                const src = $img.attr('src');
                
                if (src && this._isVideoFile(src)) {
                    this._convertUploadedMediaToVideoContainer($element, src, config.retryDelay + (index * config.staggerDelay));
                }
            }
        });
    }

    _isVideoFile(url) {
        const videoExtensions = ['mp4', 'mkv', 'webm', 'avi', 'mpeg', 'mov', 'mp3'];
        const urlWithoutParams = url.split('?')[0];
        const extension = urlWithoutParams.split('.').pop().toLowerCase();
        return videoExtensions.includes(extension);
    }

    _convertUploadedMediaToVideoContainer(element, url, delay = 0) {
        if (this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'streaming',
            'data-converted-from': 'uploaded-media',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _createPlaceholder() {
        return $('<div>', {
            class: 'video-loading-placeholder',
            html: 'Loading video...',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
    }

    clearVideoTracking(urlsToRemove = []) {
        this.debugLogger.info('Clearing video tracking', {
            urlsToRemove: urlsToRemove,
            currentProcessed: this.processedVideos.size,
            currentProcessing: this.processingVideos.size
        });
        
        if (urlsToRemove.length === 0) {
            const mediasWithErrors = $('video[data-streaming-url], audio[data-streaming-url]');
            mediasWithErrors.each((index, media) => {
                if (media.error || media.networkState === 3) {
                    const videoUrl = $(media).attr('data-video-url');
                    if (videoUrl) {
                        this.processedVideos.delete(videoUrl);
                        this.processingVideos.delete(videoUrl);
                        
                        // Cleanup streaming instances
                        const $media = $(media);
                        const hls = $media.data('hls');
                        const dash = $media.data('dash');
                        const plyr = $media.data('plyr');
                        
                        if (hls && hls.destroy) hls.destroy();
                        if (dash && dash.destroy) dash.destroy();
                        if (plyr && plyr.destroy) plyr.destroy();
                    }
                }
            });
        } else {
            urlsToRemove.forEach(url => {
                this.processedVideos.delete(url);
                this.processingVideos.delete(url);
            });
        }
        
        this.debugLogger.info('Video tracking cleared', {
            processedCount: this.processedVideos.size,
            processingCount: this.processingVideos.size
        });
    }
}

// Initialize streaming video renderer
window.VideoRenderer = StreamingVideoRenderer;
window.videoRenderer = new StreamingVideoRenderer();
window.VideoDebugLogger = VideoDebugLogger;

// Expose debugging controls
window.enableVideoDebug = function() {
    VideoDebugLogger.enabled = true;
    console.log('🎥 Streaming video debugging enabled');
};

window.disableVideoDebug = () => {
    VideoDebugLogger.enabled = false;
    console.log('Streaming video debugging disabled');
};

// Focused playback debugging
window.debugVideoPlayback = function() {
    const videos = $('video[data-streaming-url]');
    console.log(`🎬 Found ${videos.length} streaming videos:`);
    videos.each((index, video) => {
        const canPlay = video.readyState >= 2;
        const hasError = !!video.error;
        const status = hasError ? '❌ ERROR' : canPlay ? '✅ READY' : '⏳ LOADING';

        console.log(`${status} Video ${index + 1}:`, {
            src: video.currentSrc || video.src,
            readyState: `${video.readyState} (${window.videoRenderer._getReadyStateText(video.readyState)})`,
            networkState: `${video.networkState} (${window.videoRenderer._getNetworkStateText(video.networkState)})`,
            paused: video.paused,
            duration: video.duration,
            error: video.error ? `${video.error.code}: ${video.error.message}` : null
        });
    });
};

// Expose enhanced functions globally
window.activateAllVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers(options);
};

window.activateArticleVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        ...options
    });
};

