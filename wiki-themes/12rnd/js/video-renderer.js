/**
 * Streaming Video Rendering Module
 * Handles JSON metadata and constructs proper streaming URLs for HLS/DASH playback
 */

// Global video tracking - shared across all contexts
window.processedVideos = window.processedVideos || new Set();
window.processingVideos = window.processingVideos || new Set();

// Enhanced debug logging utility
const VideoDebugLogger = {
    enabled: true,
    prefix: '[StreamingVideoRenderer]',
    
    log: function(level, message, data = null) {
        if (!this.enabled) return;
        
        const timestamp = new Date().toISOString();
        const logMessage = `${this.prefix} [${timestamp}] ${level}: ${message}`;
        
        if (data) {
            console.log(logMessage, data);
        } else {
            console.log(logMessage);
        }
    },
    
    info: function(message, data) { this.log('INFO', message, data); },
    warn: function(message, data) { this.log('WARN', message, data); },
    error: function(message, data) { this.log('ERROR', message, data); },
    debug: function(message, data) { this.log('DEBUG', message, data); },
    network: function(message, data) { this.log('NETWORK', message, data); },
    streaming: function(message, data) { this.log('STREAMING', message, data); }
};

/**
 * StreamingVideoRenderer class - uses HLS/DASH streaming instead of blob approach
 */
class StreamingVideoRenderer {
    constructor(options = {}) {
        this.processedVideos = window.processedVideos;
        this.processingVideos = window.processingVideos;
        this.debugLogger = VideoDebugLogger;
        
        this.defaultOptions = {
            containerSelectors: ['.video-embed-container'],
            fileProxySelectors: ['.file-to-proxy'],
            targetContainers: ['body'],
            staggerDelay: 100,
            retryDelay: 200,
            maxRetries: 3,
            ...options
        };
        
        this.debugLogger.info('StreamingVideoRenderer initialized', {
            options: this.defaultOptions,
            processedVideos: this.processedVideos.size,
            processingVideos: this.processingVideos.size
        });
    }

    /**
     * Main activation function
     */
    activateVideoPlayers(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const containers = this._getTargetContainers(config.targetContainers);
        
        this.debugLogger.info('Activating streaming video players', {
            config: config,
            containerCount: containers.length,
            targetContainers: config.targetContainers
        });
        
        if (containers.length === 0) {
            this.debugLogger.warn('No target containers found for video activation');
            return;
        }

        // Process existing video-embed-container elements
        this._processVideoContainers(containers, config);
        
        // Process file-to-proxy elements and convert them
        this._processFileProxyElements(containers, config);
        
        // Process uploaded-media containers with video img tags
        this._processUploadedMediaElements(containers, config);
    }

    /**
     * Enhanced URL processing with metadata handling
     */
    _processVideoURL(url) {
        this.debugLogger.network('Processing video URL', { originalUrl: url });
        
        let processedUrl = url;
        
        // Check if it's a secure upload URL
        if (url.includes('secure-upload')) {
            this.debugLogger.network('Detected secure upload URL');
            
            // Apply URL transformation like existing implementation
            if (url.includes('/private')) {
                processedUrl = url.replace('/private', '');
                this.debugLogger.network('Removed /private from URL', {
                    originalUrl: url,
                    processedUrl: processedUrl
                });
            }
        }
        
        // Log URL pattern analysis
        const urlAnalysis = {
            isSecureUpload: url.includes('secure-upload'),
            hasPrivatePrefix: url.includes('/private'),
            hasSignedParams: url.includes('?') && (url.includes('Signature') || url.includes('signature')),
            hasExpiration: url.includes('Expires') || url.includes('expires'),
            protocol: url.substring(0, url.indexOf(':')),
            domain: url.includes('://') ? url.split('://')[1].split('/')[0] : 'unknown',
            path: url.includes('://') ? '/' + url.split('://')[1].split('/').slice(1).join('/') : url,
            requiresMetadata: url.includes('secure-upload')
        };
        
        this.debugLogger.network('URL Analysis', urlAnalysis);
        
        return { processedUrl, analysis: urlAnalysis };
    }

    /**
     * Enhanced video loading with streaming support
     */
    async _loadVideo(container, url) {
        const currentPlaceholder = container.find('.video-loading-placeholder');
        if (currentPlaceholder.length === 0) {
            this.debugLogger.warn('No placeholder found for video loading', { url });
            return;
        }
        
        // Process URL for security and debugging
        const { processedUrl, analysis } = this._processVideoURL(url);
        
        const loadingMsg = $('<div>', {
            html: 'Loading video...',
            'data-video-loading': 'true',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
        currentPlaceholder.replaceWith(loadingMsg);
        
        try {
            if (analysis.requiresMetadata) {
                // Handle secure uploads with metadata
                await this._loadSecureVideo(container, loadingMsg, url, processedUrl);
            } else {
                // Handle direct video URLs
                await this._loadDirectVideo(container, loadingMsg, url);
            }
        } catch (error) {
            this.debugLogger.error('Video loading failed', {
                error: error.message,
                url: url,
                processedUrl: processedUrl
            });
            
            this._showError(loadingMsg, error.message);
            
            // Remove from processing on error
            if (url) {
                this.processingVideos.delete(url);
            }
        }
    }

    /**
     * Load secure upload videos by getting metadata first
     */
    async _loadSecureVideo(container, loadingMsg, originalUrl, metadataUrl) {
        this.debugLogger.streaming('Loading secure video with metadata', {
            originalUrl: originalUrl,
            metadataUrl: metadataUrl
        });
        
        try {
            // Step 1: Get metadata
            this.debugLogger.streaming('Attempting to fetch metadata', { metadataUrl });
            
            const response = await fetch(metadataUrl, { 
                method: 'GET', 
                credentials: 'include'
            });
            
            this.debugLogger.streaming('Metadata fetch response received', {
                status: response.status,
                statusText: response.statusText,
                ok: response.ok,
                url: response.url,
                headers: {
                    'content-type': response.headers.get('content-type'),
                    'content-length': response.headers.get('content-length')
                }
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                this.debugLogger.error('Metadata fetch failed', {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });
                throw new Error(`Metadata fetch failed: HTTP ${response.status} - ${errorText}`);
            }
            
            const metadata = await response.json();
            this.debugLogger.streaming('Metadata received', metadata);
            
            // Log S3 bucket information and objectUrl if available
            if (metadata.bucket && metadata.s3Key) {
                const directS3Urls = [
                    `https://${metadata.bucket}.s3.${metadata.region || 'us-east-1'}.amazonaws.com/${metadata.s3Key}`,
                    `https://s3.${metadata.region || 'us-east-1'}.amazonaws.com/${metadata.bucket}/${metadata.s3Key}`
                ];
                this.debugLogger.streaming('Direct S3 URLs constructed', {
                    bucket: metadata.bucket,
                    s3Key: metadata.s3Key,
                    region: metadata.region,
                    directS3Urls: directS3Urls,
                    objectUrl: metadata.objectUrl || 'Not provided in metadata'
                });
            }
            
            // Step 2: Check processing status
            const statusCheck = this._checkProcessingStatus(metadata);
            if (!statusCheck.canPlay) {
                this._showProcessingStatus(loadingMsg, statusCheck);
                
                if (statusCheck.shouldRetry) {
                    // Retry after delay for processing videos
                    setTimeout(() => {
                        this.debugLogger.streaming('Retrying video after processing delay', { originalUrl });
                        this._loadVideo(container, originalUrl);
                    }, 60000); // Retry in 1 minute
                }
                return;
            }
            
            // Step 3: Construct streaming URL from metadata
            const urlData = this._constructStreamingURL(metadata, originalUrl);
            this.debugLogger.streaming('Constructed streaming URLs', {
                metadata: metadata,
                originalUrl: originalUrl,
                primaryUrl: urlData.primaryUrl,
                fallbackCount: urlData.fallbackUrls.length
            });
            
            // Step 4: Create streaming player with fallback support
            await this._createStreamingPlayerWithFallback(container, loadingMsg, originalUrl, urlData, metadata);
            
        } catch (error) {
            throw new Error(`Secure video loading failed: ${error.message}`);
        }
    }

    /**
     * Load direct video URLs (non-secure uploads)
     */
    async _loadDirectVideo(container, loadingMsg, url) {
        this.debugLogger.streaming('Loading direct video URL', { url });
        
        // For direct URLs, create simple URL data structure
        const urlData = {
            primaryUrl: url,
            fallbackUrls: []
        };
        
        await this._createStreamingPlayerWithFallback(container, loadingMsg, url, urlData, null);
    }

    /**
     * Check processing status from metadata
     */
    _checkProcessingStatus(metadata) {
        const status = metadata.status;
        const fileType = metadata.fileType;
        
        this.debugLogger.streaming('Checking processing status', {
            status: status,
            fileType: fileType,
            message: metadata.message,
            progress: metadata.progress
        });
        
        if (status === 'error') {
            return {
                canPlay: false,
                shouldRetry: false,
                message: metadata.message || 'Video processing failed',
                isError: true
            };
        }
        
        if (['to process', 'processing'].includes(status)) {
            return {
                canPlay: false,
                shouldRetry: true,
                message: 'Processing video...',
                progress: metadata.progress || 0
            };
        }
        
        if (status === 'uploaded' && fileType === 'file') {
            // Direct file upload, ready to play
            return { canPlay: true };
        }
        
        if (status === 'ready' || status === 'complete') {
            // Processed video, ready to play
            return { canPlay: true };
        }
        
        // Unknown status, assume ready
        this.debugLogger.warn('Unknown processing status, assuming ready', { status, fileType });
        return { canPlay: true };
    }

    /**
     * Determine file type from filename extension
     */
    _getFileType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        
        const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'mpeg', 'wmv', 'flv'];
        const audioExtensions = ['mp3', 'wav', 'aac', 'ogg', 'm4a', 'wma'];
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        
        if (videoExtensions.includes(extension)) {
            return 'video';
        } else if (audioExtensions.includes(extension)) {
            return 'audio';
        } else if (imageExtensions.includes(extension)) {
            return 'image';
        } else {
            // Default to file for unknown types
            return 'file';
        }
    }

    /**
     * Construct streaming URL from metadata with fallback logic
     */
    _constructStreamingURL(metadata, originalUrl) {
        const { bucket, s3Key, region } = metadata;
        
        if (!bucket || !s3Key || !region) {
            throw new Error('Missing required metadata fields for streaming URL construction');
        }
        
        // Extract organisationId from original URL if present
        let organisationId = null;
        if (originalUrl && originalUrl.includes('organisationId=')) {
            const match = originalUrl.match(/organisationId=([^&]+)/);
            if (match) {
                organisationId = match[1];
            }
        }
        
        // Build query string with organisationId if available
        const queryString = organisationId ? `?organisationId=${organisationId}` : '';
        
        // Handle both correct type prefix and incorrect "file/" prefix
        // Extract the filename and detect proper type
        const [currentType, filename] = s3Key.includes('/') ? s3Key.split('/', 2) : ['file', s3Key];
        const detectedType = this._getFileType(filename);
        const correctS3Key = `${detectedType}/${filename}`;
        
        this.debugLogger.streaming('S3 Key analysis', {
            originalS3Key: s3Key,
            currentType: currentType,
            filename: filename,
            detectedType: detectedType,
            correctS3Key: correctS3Key
        });
        
        // Try different URL patterns in order of preference
        // Based on coworker's guidance: /admin/secure-upload/{public/private}/{type}/{key}
        const possibleUrls = [
            // Correct pattern with proper type prefix
            `/admin/secure-upload/public/${correctS3Key}${queryString}`,
            `/admin/secure-upload/private/${correctS3Key}${queryString}`,
            // Original s3Key pattern (in case upload stored it correctly)
            `/admin/secure-upload/public/${s3Key}${queryString}`,
            `/admin/secure-upload/private/${s3Key}${queryString}`,
            // API variants
            `/api/admin/secure-upload/public/${correctS3Key}${queryString}`,
            `/api/admin/secure-upload/private/${correctS3Key}${queryString}`,
            `/api/admin/secure-upload/public/${s3Key}${queryString}`,
            `/api/admin/secure-upload/private/${s3Key}${queryString}`,
            // Without organisationId (fallback)
            `/admin/secure-upload/public/${correctS3Key}`,
            `/admin/secure-upload/private/${correctS3Key}`,
            `/admin/secure-upload/public/${s3Key}`,
            `/admin/secure-upload/private/${s3Key}`,
            // Direct S3 URLs (fallback)
            `https://${bucket}.s3.${region}.amazonaws.com/${s3Key}`,
            `https://s3.${region}.amazonaws.com/${bucket}/${s3Key}`,
            // Original URL (last resort)
            originalUrl
        ];
        
        this.debugLogger.streaming('Constructed streaming URL candidates', { 
            bucket, s3Key, region, 
            possibleUrls,
            preferredUrl: possibleUrls[0]
        });
        
        return {
            primaryUrl: possibleUrls[0],
            fallbackUrls: possibleUrls.slice(1)
        };
    }

    /**
     * Create streaming player with URL fallback support
     */
    async _createStreamingPlayerWithFallback(container, loadingMsg, originalUrl, urlData, metadata) {
        this.debugLogger.streaming('Creating streaming player with fallback support', {
            originalUrl: originalUrl,
            primaryUrl: urlData.primaryUrl,
            fallbackCount: urlData.fallbackUrls.length
        });
        
        const urlsToTry = [urlData.primaryUrl, ...urlData.fallbackUrls];
        
        for (let i = 0; i < urlsToTry.length; i++) {
            const streamingUrl = urlsToTry[i];
            
            try {
                // Test URL accessibility
                const canAccess = await this._testUrlAccess(streamingUrl);
                
                if (canAccess) {
                    this.debugLogger.streaming(`URL ${i + 1}/${urlsToTry.length} accessible, using for streaming`, {
                        streamingUrl: streamingUrl,
                        urlIndex: i
                    });
                    
                    await this._createStreamingPlayer(container, loadingMsg, originalUrl, streamingUrl, metadata);
                    return;
                } else {
                    this.debugLogger.streaming(`URL ${i + 1}/${urlsToTry.length} not accessible, trying next`, {
                        failedUrl: streamingUrl,
                        remainingUrls: urlsToTry.length - i - 1
                    });
                }
            } catch (error) {
                this.debugLogger.streaming(`URL ${i + 1}/${urlsToTry.length} failed with error`, {
                    failedUrl: streamingUrl,
                    error: error.message,
                    remainingUrls: urlsToTry.length - i - 1
                });
            }
        }
        
        // All URLs failed
        throw new Error('All streaming URL candidates failed - check S3 permissions or proxy configuration');
    }
    
    /**
     * Test URL accessibility and content type
     */
    async _testUrlAccess(url) {
        try {
            this.debugLogger.streaming('Testing URL access', { url });
            
            const response = await fetch(url, { 
                method: 'HEAD',
                credentials: 'include'
            });
            
            const contentType = response.headers.get('content-type');
            const contentLength = response.headers.get('content-length');
            
            // Check if it's actually video content
            const isVideoContent = contentType && (
                contentType.startsWith('video/') || 
                contentType.startsWith('application/octet-stream')
            );
            
            const accessible = (response.ok || response.status === 206) && isVideoContent;
            
            this.debugLogger.streaming('URL access test result', {
                url: url,
                status: response.status,
                statusText: response.statusText,
                contentType: contentType,
                contentLength: contentLength,
                isVideoContent: isVideoContent,
                accessible: accessible
            });
            
            return accessible;
        } catch (error) {
            this.debugLogger.streaming('URL access test failed', {
                url: url,
                error: error.message
            });
            return false;
        }
    }
    
    /**
     * Create streaming player using HLS/DASH
     */
    async _createStreamingPlayer(container, loadingMsg, originalUrl, streamingUrl, metadata) {
        this.debugLogger.streaming('Creating streaming player', {
            originalUrl: originalUrl,
            streamingUrl: streamingUrl
        });
        
        // Determine if it's audio or video
        const isAudio = streamingUrl.includes('/audio/') || 
                       (metadata && metadata.fileType === 'audio') ||
                       streamingUrl.match(/\.(mp3|wav|ogg|aac)(\?|$)/i);
        
        // Create video/audio element
        const mediaElement = this._createMediaElement(originalUrl, streamingUrl, isAudio, metadata);
        
        // Replace loading message with media element
        loadingMsg.replaceWith(mediaElement);
        
        // Initialize streaming
        this._initializeStreaming(mediaElement, streamingUrl);
        
        // Mark as processed
        this.processedVideos.add(originalUrl);
        this.processingVideos.delete(originalUrl);
        
        this.debugLogger.streaming('Streaming player created successfully', {
            originalUrl: originalUrl,
            streamingUrl: streamingUrl,
            isAudio: isAudio
        });
    }

    /**
     * Create media element (video or audio)
     */
    _createMediaElement(originalUrl, streamingUrl, isAudio, metadata) {
        const elementType = isAudio ? 'audio' : 'video';
        
        const mediaElement = $(`<${elementType}>`, {
            controls: true,
            crossorigin: 'anonymous',
            playsinline: true,
            preload: 'metadata',
            'data-video-url': originalUrl,
            'data-streaming-url': streamingUrl,
            'data-streaming-type': 'hls-dash',
            css: {
                width: '100%',
                maxWidth: isAudio ? '400px' : '800px',
                margin: '1rem 0',
                display: 'block'
            }
        });
        
        // Add poster for video if available
        if (!isAudio && metadata && streamingUrl.includes('/video/')) {
            const posterUrl = streamingUrl.replace('/video/', '/poster/').split('.')[0];
            mediaElement.attr('data-poster', posterUrl);
        }
        
        // Enhanced event handlers with detailed logging
        mediaElement.on('loadstart', () => {
            this.debugLogger.streaming('Media loadstart event', { originalUrl, streamingUrl });
        });
        
        mediaElement.on('loadedmetadata', () => {
            const mediaEl = mediaElement[0];
            this.debugLogger.streaming('Media metadata loaded', {
                url: originalUrl,
                duration: mediaEl.duration,
                readyState: mediaEl.readyState,
                networkState: mediaEl.networkState,
                currentSrc: mediaEl.currentSrc
            });
        });
        
        mediaElement.on('canplay', () => {
            this.debugLogger.streaming('Media can play', { originalUrl });
        });
        
        mediaElement.on('canplaythrough', () => {
            this.debugLogger.streaming('Media can play through', { originalUrl });
        });
        
        mediaElement.on('stalled', () => {
            this.debugLogger.warn('Media stalled', { originalUrl, streamingUrl });
        });
        
        mediaElement.on('waiting', () => {
            this.debugLogger.streaming('Media waiting for more data', { originalUrl });
        });
        
        mediaElement.on('play', () => {
            this.debugLogger.streaming('Media play started', { originalUrl });
        });
        
        mediaElement.on('pause', () => {
            this.debugLogger.streaming('Media paused', { originalUrl });
        });
        
        mediaElement.on('error', (e) => {
            const mediaEl = mediaElement[0];
            const error = mediaEl.error;
            
            // Detailed error mapping
            const errorTypes = {
                1: 'MEDIA_ERR_ABORTED',
                2: 'MEDIA_ERR_NETWORK',
                3: 'MEDIA_ERR_DECODE',
                4: 'MEDIA_ERR_SRC_NOT_SUPPORTED'
            };
            
            this.debugLogger.error('Media playback error', {
                url: originalUrl,
                streamingUrl: streamingUrl,
                errorCode: error ? error.code : 'unknown',
                errorType: error ? errorTypes[error.code] || 'unknown' : 'unknown',
                errorMessage: error ? error.message : 'unknown',
                networkState: mediaEl.networkState,
                readyState: mediaEl.readyState,
                currentSrc: mediaEl.currentSrc
            });
        });
        
        return mediaElement;
    }

    /**
     * Initialize streaming with HLS/DASH
     */
    _initializeStreaming(mediaElement, streamingUrl) {
        const mediaEl = mediaElement[0];
        
        this.debugLogger.streaming('Initializing streaming', {
            streamingUrl: streamingUrl,
            hlsSupported: window.Hls && window.Hls.isSupported(),
            dashSupported: window.dashjs && typeof window.dashjs.MediaPlayer === 'function',
            plyrAvailable: typeof window.Plyr === 'function'
        });
        
        // For .mov files and internal streaming URLs, use native HTML5 playback with direct source
        if (streamingUrl.includes('/api/admin/stream/') || streamingUrl.endsWith('.mov')) {
            this.debugLogger.streaming('Using native HTML5 playback for internal stream', { streamingUrl });
            mediaEl.src = streamingUrl;
            
            // Initialize Plyr for better UI if available
            if (typeof window.Plyr === 'function') {
                try {
                    const plyrOptions = this._getPlyrOptions();
                    const player = new window.Plyr(mediaEl, plyrOptions);
                    mediaElement.data('plyr', player);
                    this.debugLogger.streaming('Plyr player initialized for native playback', { streamingUrl });
                } catch (error) {
                    this.debugLogger.warn('Plyr initialization failed, using basic controls', { error: error.message });
                }
            }
            return;
        }
        
        // Check if we have the streaming libraries available for HLS/DASH
        if (this._isAppleDevice() && window.Hls && window.Hls.isSupported()) {
            this._initializeHLSPlayer(mediaElement, streamingUrl);
        } else if (window.dashjs && typeof window.dashjs.MediaPlayer === 'function') {
            this._initializeDASHPlayer(mediaElement, streamingUrl);
        } else {
            // Fallback to native HTML5 playback
            this.debugLogger.warn('No streaming libraries available, using native playback', { streamingUrl });
            mediaEl.src = streamingUrl;
        }
    }

    /**
     * Initialize HLS player for Apple devices
     */
    _initializeHLSPlayer(mediaElement, streamingUrl) {
        this.debugLogger.streaming('Initializing HLS player', { streamingUrl });
        
        const mediaEl = mediaElement[0];
        const hls = new window.Hls();
        
        // Use Plyr if available
        if (typeof window.Plyr === 'function') {
            const plyrOptions = this._getPlyrOptions();
            
            plyrOptions.quality.onChange = function (quality) {
                hls.levels.forEach((level, levelIndex) => {
                    if (level.height !== quality) return;
                    hls.currentLevel = levelIndex;
                });
            };
            
            const player = new window.Plyr(mediaEl, plyrOptions);
            
            player.on('play', () => {
                if (!hls.media) {
                    hls.loadSource(streamingUrl);
                    hls.attachMedia(mediaEl);
                }
            });
            
            // Store references for cleanup
            mediaElement.data('hls', hls);
            mediaElement.data('plyr', player);
        } else {
            // Direct HLS without Plyr
            hls.loadSource(streamingUrl);
            hls.attachMedia(mediaEl);
        }
    }

    /**
     * Initialize DASH player for other browsers
     */
    _initializeDASHPlayer(mediaElement, streamingUrl) {
        this.debugLogger.streaming('Initializing DASH player', { streamingUrl });
        
        const mediaEl = mediaElement[0];
        const dash = window.dashjs.MediaPlayer().create();
        
        // Use Plyr if available
        if (typeof window.Plyr === 'function') {
            const plyrOptions = this._getPlyrOptions();
            
            plyrOptions.quality.onChange = function (quality) {
                const qualityIndex = plyrOptions.quality.options.findIndex((q) => q === quality);
                dash.setQualityFor("video", qualityIndex);
            };
            
            const player = new window.Plyr(mediaEl, plyrOptions);
            
            player.on('play', () => {
                if (!dash.isReady()) {
                    dash.initialize(mediaEl, streamingUrl, false);
                }
            });
            
            // Store references for cleanup
            mediaElement.data('dash', dash);
            mediaElement.data('plyr', player);
        } else {
            // Direct DASH without Plyr
            dash.initialize(mediaEl, streamingUrl, false);
        }
    }

    /**
     * Get Plyr options (use existing defaultPlyrOptions if available)
     */
    _getPlyrOptions() {
        if (window.defaultPlyrOptions) {
            return { ...window.defaultPlyrOptions };
        }
        
        return {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            quality: {
                default: 720,
                options: [1080, 720, 480, 360],
                forced: true,
                onChange: null
            }
        };
    }

    /**
     * Check if device is Apple
     */
    _isAppleDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }

    /**
     * Show processing status for videos still being processed
     */
    _showProcessingStatus(loadingMsg, statusInfo) {
        let statusHtml;
        
        if (statusInfo.isError) {
            statusHtml = `
                <div class="video-error-status" style="padding: 1rem; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
                    <strong>Error:</strong> ${statusInfo.message}<br>
                    <small>Video processing failed. Please try uploading again.</small>
                </div>
            `;
        } else {
            const progress = statusInfo.progress || 0;
            statusHtml = `
                <div class="video-processing-status" style="padding: 1rem; color: #004085; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px;">
                    <p><strong>${statusInfo.message}</strong> ${progress > 0 ? `${progress}%` : ''}</p>
                    ${progress > 0 ? `
                        <div class="progress-bar" style="width: 100%; background: #e9ecef; border-radius: 4px; overflow: hidden; height: 8px;">
                            <div style="width: ${progress}%; height: 100%; background: #007bff; transition: width 0.3s;"></div>
                        </div>
                    ` : ''}
                    <small>You can save this file and close this tab. Feel free to return later to view the video once processing is complete.</small>
                </div>
            `;
        }
        
        loadingMsg.html(statusHtml);
    }

    /**
     * Show error message
     */
    _showError(loadingMsg, errorMessage) {
        const errorHtml = `
            <div class="video-error-status" style="padding: 1rem; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
                <strong>Error:</strong> ${errorMessage}<br>
                <small>Check browser console for detailed logs</small>
            </div>
        `;
        
        loadingMsg.html(errorHtml);
        loadingMsg.removeClass('video-loading-placeholder').addClass('video-error');
    }

    // Include all the standard methods from the original implementation
    _getTargetContainers(selectors) {
        if (typeof $ === 'undefined') {
            this.debugLogger.error('jQuery not available, cannot process videos');
            return { length: 0, find: () => ({ length: 0, each: () => {} }) };
        }
        
        const containers = [];
        selectors.forEach(selector => {
            const elements = $(selector);
            if (elements.length > 0) {
                containers.push(...elements.toArray());
                this.debugLogger.debug('Found containers', { selector, count: elements.length });
            }
        });
        
        this.debugLogger.info('Target containers collected', {
            selectors: selectors,
            totalContainers: containers.length
        });
        
        return $(containers);
    }

    _processVideoContainers(containers, config) {
        const videoContainers = containers.find(config.containerSelectors.join(', '));
        
        this.debugLogger.info('Processing video containers', {
            containerCount: videoContainers.length,
            selectors: config.containerSelectors
        });
        
        videoContainers.each((index, element) => {
            this._processVideoContainer($(element), index * config.staggerDelay);
        });
    }

    _processVideoContainer(container, delay = 0) {
        const videoUrl = container.attr('data-video-url');
        
        this.debugLogger.debug('Processing individual video container', {
            videoUrl: videoUrl,
            delay: delay,
            isProcessing: this.processingVideos.has(videoUrl),
            isProcessed: this.processedVideos.has(videoUrl)
        });
        
        if (videoUrl && this.processingVideos.has(videoUrl)) {
            this.debugLogger.warn('Video already being processed, skipping', { videoUrl });
            return;
        }
        
        if (videoUrl && this.processedVideos.has(videoUrl)) {
            const existingMedia = container.find('video, audio');
            if (existingMedia.length > 0 && !existingMedia[0].error) {
                this.debugLogger.debug('Video already processed and working, skipping', { videoUrl });
                return;
            }
            this.debugLogger.info('Re-processing previously processed video', { videoUrl });
            this.processedVideos.delete(videoUrl);
        }
        
        // Clear existing content
        container.find('video, audio, .video-loading-placeholder, .video-error-status, .video-processing-status').remove();
        container.append(this._createPlaceholder());
        
        if (videoUrl) {
            this.processingVideos.add(videoUrl);
            setTimeout(() => {
                this._loadVideo(container, videoUrl);
            }, delay);
        }
    }

    _processFileProxyElements(containers, config) {
        const proxyElements = containers.find(config.fileProxySelectors.join(', '));
        
        this.debugLogger.info('Processing file-to-proxy elements', {
            elementCount: proxyElements.length,
            selectors: config.fileProxySelectors
        });
        
        proxyElements.each((index, element) => {
            this._convertFileProxyToVideoContainer($(element), config.retryDelay + (index * config.staggerDelay));
        });
    }

    _convertFileProxyToVideoContainer(element, delay = 0) {
        const url = element.attr('data-url');
        
        if (!url || this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'streaming',
            'data-converted-from': 'file-to-proxy',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _processUploadedMediaElements(containers, config) {
        const uploadedMediaElements = containers.find('.uploaded-media');
        
        uploadedMediaElements.each((index, element) => {
            const $element = $(element);
            const $img = $element.find('img');
            
            if ($img.length > 0) {
                const src = $img.attr('src');
                
                if (src && this._isVideoFile(src)) {
                    this._convertUploadedMediaToVideoContainer($element, src, config.retryDelay + (index * config.staggerDelay));
                }
            }
        });
    }

    _isVideoFile(url) {
        const videoExtensions = ['mp4', 'mkv', 'webm', 'avi', 'mpeg', 'mov', 'mp3'];
        const urlWithoutParams = url.split('?')[0];
        const extension = urlWithoutParams.split('.').pop().toLowerCase();
        return videoExtensions.includes(extension);
    }

    _convertUploadedMediaToVideoContainer(element, url, delay = 0) {
        if (this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'streaming',
            'data-converted-from': 'uploaded-media',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _createPlaceholder() {
        return $('<div>', {
            class: 'video-loading-placeholder',
            html: 'Loading video...',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
    }

    clearVideoTracking(urlsToRemove = []) {
        this.debugLogger.info('Clearing video tracking', {
            urlsToRemove: urlsToRemove,
            currentProcessed: this.processedVideos.size,
            currentProcessing: this.processingVideos.size
        });
        
        if (urlsToRemove.length === 0) {
            const mediasWithErrors = $('video[data-streaming-url], audio[data-streaming-url]');
            mediasWithErrors.each((index, media) => {
                if (media.error || media.networkState === 3) {
                    const videoUrl = $(media).attr('data-video-url');
                    if (videoUrl) {
                        this.processedVideos.delete(videoUrl);
                        this.processingVideos.delete(videoUrl);
                        
                        // Cleanup streaming instances
                        const $media = $(media);
                        const hls = $media.data('hls');
                        const dash = $media.data('dash');
                        const plyr = $media.data('plyr');
                        
                        if (hls && hls.destroy) hls.destroy();
                        if (dash && dash.destroy) dash.destroy();
                        if (plyr && plyr.destroy) plyr.destroy();
                    }
                }
            });
        } else {
            urlsToRemove.forEach(url => {
                this.processedVideos.delete(url);
                this.processingVideos.delete(url);
            });
        }
        
        this.debugLogger.info('Video tracking cleared', {
            processedCount: this.processedVideos.size,
            processingCount: this.processingVideos.size
        });
    }
}

// Initialize streaming video renderer
window.VideoRenderer = StreamingVideoRenderer;
window.videoRenderer = new StreamingVideoRenderer();
window.VideoDebugLogger = VideoDebugLogger;

// Expose debugging controls
window.enableVideoDebug = () => {
    VideoDebugLogger.enabled = true;
    // console.clear(); // Clear console before starting video debug logs
    console.log('🎥 Streaming video debugging enabled - console cleared for clean logs');
};

window.disableVideoDebug = () => {
    VideoDebugLogger.enabled = false;
    console.log('Streaming video debugging disabled');
};

// Expose enhanced functions globally
window.activateAllVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers(options);
};

window.activateArticleVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        ...options
    });
};

// Debug function to test video playback
window.testVideoPlayback = function() {
    const videos = $('video[data-streaming-url]');
    console.log(`Found ${videos.length} streaming videos`);
    
    videos.each((index, video) => {
        const $video = $(video);
        const streamingUrl = $video.attr('data-streaming-url');
        const originalUrl = $video.attr('data-video-url');
        
        console.log(`Video ${index + 1}:`);
        console.log('  Original URL:', originalUrl);
        console.log('  Streaming URL:', streamingUrl);
        console.log('  Current src:', video.currentSrc);
        console.log('  Ready state:', video.readyState);
        console.log('  Network state:', video.networkState);
        console.log('  Error:', video.error);
        console.log('  Can play:', video.readyState >= 3);
        
        // Test manual playback
        if (video.readyState >= 3) {
            console.log(`  Attempting to play video ${index + 1}...`);
            video.play().then(() => {
                console.log(`  Video ${index + 1} started playing successfully`);
                video.pause(); // Pause after successful test
            }).catch(error => {
                console.error(`  Video ${index + 1} play failed:`, error);
            });
        }
    });
};