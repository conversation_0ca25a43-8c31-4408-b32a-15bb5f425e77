let allTopics = [];
let allSubtopics = [];
let allArticles = [];
let uniqueSearches = [];
let uniqueSearchesBackup = [];
let allArticlesAnalytics = [];
let allPageviewsAnalytics = [];
let allArticleComments = [];
let allFeedbacks = [];

let appContext = "";
// let dateFrom = '';
// let dateTo = '';
var topicsSubtopicsChart = null;
var pageViewsChart = null;

let flagsAvailable = [
  { country: 'Australia', filename: 'AU.svg'},
  { country: 'New Zealand', filename: 'NZ.svg'},
  { country: 'Philippines', filename: 'PH.svg'},
  { country: 'Singapore', filename: 'SG.svg'},
  { country: 'United Kingdom', filename: 'UK.svg'},
  { country: 'United States', filename: 'US.svg'},
	{ country: 'Japan', filename: 'JP.svg'},
];


$(document).ready(async function () {
	await initTranslator();

	appContext = $("#app_context").val();

	var start = moment().subtract(30, 'day');
	let today = new moment();

	//configure datetimepicker component
    function cb(start, end) {
        $('#dateRangeAnalytics span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
    }

	//init daterangepicker
	const ranges = {}
	ranges[t('Today')] = [moment(), moment()];
	// ranges[t('Yesterday')] = [moment().subtract(1, 'days'), moment().subtract(1, 'days')];
	// ranges[t('Last 7 Days')] = [moment().subtract(6, 'days'), today];
	// ranges[t('Last 30 Days')] = [moment().subtract(29, 'days'), today];
	// ranges[t('This Month')] = [moment().startOf('month'), moment().endOf('month')];
	// ranges[t('Last Month')] = [moment().subtract(1, 'month'), today];
	ranges[t('Last 3 Months')] = [moment().subtract(3, 'month'), today];
	ranges[t('Last 6 Months')] = [moment().subtract(6, 'month'), today];
	ranges[t('This Year')] = [moment().startOf('year'), today];
	ranges[t('Last Year')] = [moment().startOf('year'), today];
	ranges[t('Last 2 Years')] = [moment().subtract(2, 'year').startOf('year'), today];


	$('#dateRangeAnalytics').daterangepicker({
		maxDate: new Date(),
    startDate: start,
		endDate: today,
		alwaysShowCalendars: true,
		opens: 'left',
    ranges: ranges,
		locale: {
			format: 'MMMM DD, YYYY',
			applyLabel: t('Apply'),
			cancelLabel: t('Cancel'),
			customRangeLabel: t('Custom Range'),
			monthNames: [
					t('Jan'),
					t('Feb'),
					t('Mar'),
					t('Apr'),
					t('May'),
					t('Jun'),
					t('Jul'),
					t('Aug'),
					t('Sep'),
					t('Oct'),
					t('Nov'),
					t('Dec'),
			],
			daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
	},
	}, function (sta, end, label) {
			$('#dateRangeAnalytics span').html(sta.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
			//console.log("A new date selection was made: " + sta + ' to ' + end);
			// dateFrom = start.format('YYYY-MM-DD');
			// dateTo = end.format('YYYY-MM-DD');

		getAllSearches(sta, end);
		getData(sta, end);

	});

    cb(start, today);
		await getAllTopicsData();

	//independent table
	getAllSearches(start, today);
	getData(start, today);


	//manually triggering tabs because it's a bug in Mac
    let contentDashboard = $('#navContentDashboard');
	let contentArticles = $('#navContentArticles');
	let contentConfiguration = $('#navContentConfiguration');
	
	$('#navSettingsDashboard').on('click', function () {
		contentDashboard.show();
		contentArticles.hide();
		contentConfiguration.hide();
		if ($(this).hasClass('active')) {
		} else {
			$(this).addClass('active');
			$('#navSettingsArticles').removeClass('active');
			$('#navSettingsConfiguration').removeClass('active');
		}
	});

	$('#navSettingsArticles').on('click', function () {
		contentDashboard.hide();
		contentArticles.show();
		contentConfiguration.hide();
		if ($(this).hasClass('active')) {
		} else {
			$(this).addClass('active');
			$('#navSettingsDashboard').removeClass('active');
			$('#navSettingsConfiguration').removeClass('active');
		}
	});

	$('#navSettingsConfiguration').on('click', function () {
		contentDashboard.hide();
		contentArticles.hide();
		contentConfiguration.show();
		if ($(this).hasClass('active')) {
		} else {
			$(this).addClass('active');
			$('#navSettingsArticles').removeClass('active');
			$('#navSettingsDashboard').removeClass('active');
		}
	});



	// EVENTS ---------------------------------------------------------------------------------------------------
	//user presses Enter
	$("#article_filter").on("keyup", function (e) {
		var keycode = e.keyCode ? e.keyCode : e.which;
		if (keycode == "13") {
		$("#btn_articles_filter").click();
		}
	});
	//user selects topic
	$("#articleTopicOptions").on("change", function () {
		setSubtopicSelect($(this).val());
	});

	$("#btn_articles_reset").click(function () {
		$("#article_filter").val("");
		$("#articleTopicOptions").val("");
		$("#articleSubTopicOptions").val("");

		displaySearchResults(allArticles);
	});

	//disable Subtopic options on initial load
	$("#articleSubTopicOptions").prop("disabled", true);

	//when user clicks the sortByTerm button
	$('#btnSortTerm').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'search';

		if ($(`#btnSortTerm > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`.tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortTerm > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');
			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByString(uniqueSearches, sortByWhat, direction, 'spinnerSearches');
		displaySearchesTable(newArray);
	});
	//when user clicks the sortBy number of searches button
	$('#btnSortSearches').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'count';

		if ($(`#btnSortSearches > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`#tblSearches .tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortSearches > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');

			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByNumber(uniqueSearches, sortByWhat, direction, 'spinnerSearches');
		displaySearchesTable(newArray);
	});
	//when user clicks the sortBy number of searches button
	$('#btnSortMatches').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'nbHits';

		if ($(`#btnSortMatches > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`#tblSearches .tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortMatches > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');

			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByNumber(uniqueSearches, sortByWhat, direction, 'spinnerSearches');
		displaySearchesTable(newArray);
	});

	//when user clicks the sortBy number of comments button
	$('#btnSortComments').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'comments';

		if ($(`#btnSortComments > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`#tblPageViews .tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortComments > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');

			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByNumber(allPageviewsAnalytics, sortByWhat, direction, 'spinnerPageViews');
		displayPageviewsTable(newArray);
	});

	//when user clicks the sortBy number of likes button
	$('#btnSortLikes').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'likes';

		if ($(`#btnSortLikes > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`#tblPageViews .tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortLikes > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');

			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByNumber(allPageviewsAnalytics, sortByWhat, direction, 'spinnerPageViews');
		displayPageviewsTable(newArray);
	});

	//when user clicks the sortBy number of likes button
	$('#btnSortDislikes').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'dislikes';

		if ($(`#btnSortDislikes > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`#tblPageViews .tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortDislikes > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');

			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByNumber(allPageviewsAnalytics, sortByWhat, direction, 'spinnerPageViews');
		displayPageviewsTable(newArray);
	});

	//when user clicks the sortBy number of likes button
	$('#btnSortViews').on('click', function () {
				
		let direction = 'ascending';
		let sortByWhat = 'views';

		if ($(`#btnSortViews > i`).hasClass('fa fa-arrow-down')) {
			direction = 'descending';
		}
		$(`#tblPageViews .tbl-sort-icon`).removeClass('active-sort-btn');
		$(`#btnSortViews > i`).toggleClass('fa-arrow-up fa-arrow-down');
		$(this).addClass('active-sort-btn');

			
		//console.log(`sortby: ${sortByWhat} direction: ${direction} `);

		let newArray = sortArrayByNumber(allPageviewsAnalytics, sortByWhat, direction, 'spinnerPageViews');
		displayPageviewsTable(newArray);
	});

	//show popular searches with 0 match
	$('#btnShowSearchesLessMatch').on('click', function () {
		
		try {
			$(`#spinnerSearches`).show(100);
			$(`#btnResetSearches`).removeClass('d-none');

			uniqueSearches = uniqueSearches.filter(a => a.nbHits == 0);
			
			uniqueSearches = uniqueSearches.sort((a, b) => {
				let bb = b.count ? b.count : 0;
				let aa = a.count ? a.count : 0;

				return parseInt(bb) - parseInt(aa);
			});

			displaySearchesTable(uniqueSearches);

			//also reset sort buttons
			$(`#tblSearches .tbl-sort-icon`).removeClass('active-sort-btn');
			$(`#btnSortMatches > i`).attr('class', 'fa fa-arrow-up');
			$(`#btnSortSearches > i`).attr('class', 'fa fa-arrow-up');
			$(`#btnSortTerm > i`).attr('class', 'fa fa-arrow-up');

			$(`spinnerSearches`).hide(100);

		} catch (err) {
			$(`spinnerSearches`).hide(100);
			
			console.log('Error on sorting array, returning orig array');
			return uniqueSearches;
		}
	});

	$('#btnResetSearches').on('click', function () {
		
		try {
			$(`#spinnerSearches`).show(100);
			//restore from backup
			uniqueSearches = uniqueSearchesBackup;
			
			// uniqueSearches = uniqueSearches.sort((a, b) => {
			// 	let bb = b.no_of_results ? b.no_of_results : 0;
			// 	let aa = a.no_of_results ? a.no_of_results : 0;

			// 	return parseInt(bb) - parseInt(aa);
			// });

			// //sort by count descending
			uniqueSearches.sort((a, b) => {
				let bb = b.count;
				let aa = a.count;
				return bb - aa;
			});
				
			displaySearchesTable(uniqueSearches);

			$(`#btnResetSearches`).addClass('d-none');

			//also reset sort buttons
			$(`#tblSearches .tbl-sort-icon`).removeClass('active-sort-btn');
			$(`#btnSortMatches > i`).attr('class', 'fa fa-arrow-up');
			$(`#btnSortSearches > i`).attr('class', 'fa fa-arrow-up');
			$(`#btnSortTerm > i`).attr('class', 'fa fa-arrow-up');

			$(`spinnerSearches`).hide(100);

		} catch (err) {
			$(`spinnerSearches`).hide(100);
			
			console.log('Error on sorting array, returning orig array');
		}
	});
	
	//hide spinners
	$('#spinnerSearches').hide(100);
	$('#spinnerPageViews').hide(100);

	//topics/subtopics taggings in Articles tab
	//await getAllTopicsData();

	$('#articles').on('click', 'tbody tr', function() {
		window.location.href = appContext + '/edit/' + $(this).data('id');
	})
});	

//functions ---------------------------------------------------------------------
async function getData(start, today) {

	//pageviews then feedbacks then comments
	// getAllArticles().then(function () {
	// 	getAllPageviews(start, today).then(function () {
	// 		getAllFeedbacks(start, today).then(function () {
	// 			getAllComments(start, today);
	// 		});
	// 	});
	// });
	//same result as this
	await getAllArticles();
	await getAllPageviews(start, today);
	await getAllFeedbacks(start, today);
	await getAllComments(start, today);

	//console.log('Data fetch done.');
}


//returns color based on value/amount
//i think we base it on percentage
function searchColorSupplier(itemCount = 10, number = 10) {
	let percentage = (number / itemCount) * 100;

	/* shades of greens
		1 #acf540
		2 #9ceb34
		3 #71eb34
		4 #49eb34
		5 #34eb46
		6 #34eb71
		7 #34eb93
		8 #34eba8
		9 #34ebc0
		10 #34ebd9
	
	shades of gray to bluish
		1 #83868a 181, 183, 186
		2 #838991 183, 188, 196
		3 #838f9e 181, 189, 201
		4 #7d91ab 182, 193, 209
		5 #7792b5 176, 191, 214
		6 #7295c2 177, 196, 224
		7 #5687c7 174, 198, 235
		8 #4885d4 168, 196, 240
		9 #3a83e0 167, 200, 252
		10 #2780f2 153, 193, 255

	from Palette Generator: https://www.learnui.design/tools/data-color-picker.html
		1 #9fa6ab 159, 166, 171
		2 #86afb9 134, 175, 185
		3 #66b8b8 102, 184, 184
		4 #56bfa3 86, 191, 163
		5 #6bc37f 107, 195, 127
		6 #94c152 148, 193, 82
		7 #c7b921 199, 185, 33
		8 #ffa600 255, 166, 0
		9 #ff8400 255, 132, 0

	*/


	if (percentage >= 1 && percentage < 10) {
		return 'rgb(159, 166, 171, 0.7)';
	} else if (percentage >= 10 && percentage < 20) {
		return 'rgb(159, 166, 171, 0.7)';
	} else if (percentage >= 20 && percentage < 30) {
		return 'rgb(134, 175, 185, 0.7)';
	} else if (percentage >= 30 && percentage < 40) {
		return 'rgb(102, 184, 184, 0.7)';
	} else if (percentage >= 40 && percentage < 50) {
		return 'rgb(86, 191, 163, 0.7)';
	} else if (percentage >= 50 && percentage < 60) {
		return 'rgb(107, 195, 127, 0.7)';
	} else if (percentage >= 60 && percentage < 70) {
		return 'rgb(148, 193, 82, 0.7)';
	} else if (percentage >= 70 && percentage < 80) {
		return 'rgb(199, 185, 33, 0.7)';
	} else if (percentage >= 80 && percentage < 90) {
		return 'rgb(255, 166, 0, 0.7)';
	} else if (percentage >= 90) {
		return 'rgb(255, 132, 0, 0.7)';
	}

}

//will sort a given array based on number property
function sortArrayByNumber(theArray = [], sortByWhat = '', direction = 'ascending', spinner = '') {
	$(`#${spinner}`).show(100);
	try {
		let newArray = [];
		if (direction == 'ascending') {
			newArray = theArray.sort((a, b) => {
				let bb = b[sortByWhat] ? b[sortByWhat] : 0;
				let aa = a[sortByWhat] ? a[sortByWhat] : 0;
				return parseInt(aa) - parseInt(bb);
			});
			//console.log('asc...');
			return newArray;

		} else if (direction == 'descending') {
			newArray = theArray.sort((a, b) => {
				let bb = b[sortByWhat] ? b[sortByWhat] : 0;
				let aa = a[sortByWhat] ? a[sortByWhat] : 0;
				return parseInt(bb) - parseInt(aa);

			});
			//console.log('des...');
			return newArray;
		}
		$(`#${spinner}`).hide(100);
		// return newArray;
	} catch (err) {
		$(`#${spinner}`).hide(100);
		
		console.log('Error on sorting array, returning orig array');
		return theArray;
	}
}

//will sort a given array based on string property
function sortArrayByString(theArray = [], sortByWhat = '', direction = 'ascending', spinner = '') {
	$(`#${spinner}`).show(100);
	try {
		let newArray = [];
		if (direction == 'ascending') {
			newArray = theArray.sort((a, b) => {
				if (a[sortByWhat] < b[sortByWhat])
					return -1;
				if (a[sortByWhat] > b[sortByWhat])
					return 1;
				return 0;
			})
			//console.log('asc...', newArray);
			return newArray;

		} else if (direction == 'descending') {
			newArray = theArray.sort((a, b) => {
				if (a[sortByWhat] < b[sortByWhat])
					return 1;
				if (a[sortByWhat] > b[sortByWhat])
					return -1;
				return 0;
			})
			//console.log('des...', newArray);
			return newArray;
		}
		$(`#${spinner}`).hide(100);
		// return newArray;
	} catch (err) {
		$(`#${spinner}`).hide(100);
		
		console.log('Error on sorting array, returning orig array', theArray);
		return theArray;
	}
}

//show toast
const Toast = Swal.mixin({
	toast: true,
	position: "top-end",
	showConfirmButton: false,
	timer: 2000,
	timerProgressBar: false,
	didOpen: (toast) => {
			toast.addEventListener("mouseenter", Swal.stopTimer);
			toast.addEventListener("mouseleave", Swal.resumeTimer);
	},
	showClass: {
			backdrop: "swal2-noanimation", // disable backdrop animation
			popup: "", // disable popup animation
	},
});

//modify date with months +-
function getDateMonths(date, months) {
  date.setMonth(date.getMonth() + months);
  return date;
}

function displayTopicsChart() {
	//console.log('Display topics chart...')
	let topicsViews = [];
	let subtopicsViews = [];
	let labels = [];

	// console.log('all topics', allTopics);
	// console.log('all subtopics', allSubtopics);
	// console.log('all allPageviewsAnalytics', allPageviewsAnalytics.length);

	// //get all unique topics
	// let uniqueTopics = [...new Map(allPageviewsAnalytics.map(item =>
	// 	[item['kb_pinned_topic'], item])).values()];
	// //get all unique subtopics
	// let uniqueSubtopics = [...new Map(allPageviewsAnalytics.map(item =>
	// 	[item['kb_pinned_subtopic'], item])).values()];
	
	// //sort all by topic
	// uniqueTopics = uniqueTopics.sort((a, b) => {
	// 	if (a.kb_pinned_topic < b.kb_pinned_topic)
	// 		return -1;
	// 	if (a.kb_pinned_topic > b.kb_pinned_topic)
	// 		return 1;
	// 	return 0;
	// });
	// uniqueSubtopics = uniqueSubtopics.sort((a, b) => {
	// 	if (a.kb_pinned_topic < b.kb_pinned_topic)
	// 		return -1;
	// 	if (a.kb_pinned_topic > b.kb_pinned_topic)
	// 		return 1;
	// 	return 0;
	// });


	//store topics
	// uniqueTopics.forEach(ut => {
	// 	if (ut.kb_pinned_topic && ut.kb_pinned_topic !== 'undefined' && ut.kb_pinned_topic !== '') {
	// 		topicsViews.push({topic: ut.kb_pinned_topic, views: 0 });
	// 	}
	// 	// else {
	// 	// 	//insert untagged topic for articles not tagged yet
	// 	// 	topicsViews.push({topic: "Untagged", views: 0 });
	// 	// }
	// });


	allTopics.forEach(topics => {
		topicsViews.push({ id: topics._id, topicTranslations: topics.topicTranslations, topicDescrip: topics.topic, topic: topics._id, views: 0 });
		
	});
	console.log('allPageviewsAnalytics', allPageviewsAnalytics)

	// allSubtopics.forEach(us => {
		//find all subtopics
	for (let us of allSubtopics) {
		//console.log(`subtopic: ${us.subtopic}`);
		//remove spaces
		const subt = us.id;
		
		let subtopicArticles = allPageviewsAnalytics.filter(aaa => aaa.kb_pinned_subtopic == subt);
		if (subtopicArticles) {
		
			//get the index of the topic of this subtopic
			let topicIndex = topicsViews.findIndex(tv => tv.id == us.topic);

			let newSubtopicData = { id: subt, topic: us.topic, subtopic: us.subtopic, subtopicDesc: us.subtopic, views: 0 };

			//subtopicArticles.forEach(sta => {
			for (let sta of subtopicArticles) {
				//console.log(`   -> article: ${sta.kb_pinned_subtopic} ${sta.views}`);
				
				//add up views in topics
				topicsViews[topicIndex].views += parseInt(sta.views);

				//add up views in subtopics
				newSubtopicData.views += parseInt(sta.views);
			}
			//});

			//if (newSubtopicData.views > 0) {
			subtopicsViews.push(newSubtopicData);
			//}
		}
	}
		
	// });

	

	// console.log('topics', uniqueTopics);
	// console.log('subtopics', uniqueSubtopics);

	//get sum of pageviews per subtopic
	// uniqueSubtopics.forEach(us => {
	// 	//there are articles that does not have subtopic
	// 	if (us.kb_pinned_subtopic && us.kb_pinned_topic) {
	// 		//find all subtopics
	// 		let subtopicArticles = allPageviewsAnalytics.filter(aaa => aaa.kb_pinned_subtopic == us.kb_pinned_subtopic);
	// 		if (subtopicArticles) {
	// 			//so this subtopic belongs to a topic
	// 			//get the index of the topic of this subtopic
	// 			let topicIndex = topicsViews.findIndex(x => x.topic == us.kb_pinned_topic);

	// 			let newSubtopicData = { topic: us.kb_pinned_topic, subtopic: us.kb_pinned_subtopic, views: 0 };

	// 			subtopicArticles.forEach(sta => {
					
	// 				//add up views in topics
	// 				topicsViews[topicIndex].views += parseInt(sta.views);

	// 				//add up views in subtopics
	// 				newSubtopicData.views += parseInt(sta.views);
	// 			});
	// 			if (newSubtopicData.views > 0) {
	// 				subtopicsViews.push(newSubtopicData);
	// 			}
	// 		}
	// 	}
	// 	//else {
	// 		//not including untagged articles for now
	// 		// console.log('Lost soul', us);
	// 		// let newSubtopicData = { topic: "No Topic", subtopic: "Untagged", views: 0 };

	// 		// //maybe there's a taggedTopic
	// 		// topicsViews.push({topic: ut.kb_pinned_topic, views: 0 });
	// 		// subtopicsViews.push(newSubtopicData);
	// 	//}
	// });

	//ANOTHER APPROACH ------------------------- per article
	//store subtopics
	// uniqueSubtopics.forEach(ust => {
	// 	if (ust.kb_pinned_subtopic && ust.kb_pinned_subtopic !== 'undefined' && ust.kb_pinned_subtopic !== '') {
	// 		subtopicsViews.push({topic: ust.kb_pinned_topic, subtopic: ust.kb_pinned_subtopic, views: 0 });
	// 	}
	// 	// else {
	// 	// 	//insert untagged topic for articles not tagged yet
	// 	// 	subtopicsViews.push({topic: "Untagged", subtopic: "Untagged", views: 0 });
	// 	// }
	// });

	// allArticlesAnalytics.forEach(article => {
	// 	let viewcount = parseInt(article.kb_viewcount);
	// 	let topicIndex = 0;
	// 	let subtopicIndex = 0;
	// 	//add topics total
	// 	if (article.kb_pinned_topic && article.kb_pinned_topic != '') {
	// 		topicIndex = topicsViews.findIndex(x => x.topic == article.kb_pinned_topic);
	// 	}
	// 	// else {
	// 	// 	//add to untagged topic
	// 	// 	topicIndex = topicsViews.findIndex(x => x.topic == "Untagged");
	// 	// }

	// 	if (article.kb_pinned_subtopic && article.kb_pinned_subtopic != '') {
	// 		subtopicIndex = subtopicsViews.findIndex(x => x.subtopic == article.kb_pinned_subtopic);
	// 	}
	// 	// else {
	// 	// 	//add to untagged subtopic
	// 	// 	subtopicIndex = subtopicsViews.findIndex(x => x.subtopic == "Untagged");
	// 	// }

	// 	topicsViews[topicIndex].views += viewcount;
	// 	subtopicsViews[subtopicIndex].views += viewcount;

	// });

	let topicData = [];
	let subtopicData = [];
	let topicLabels = [];
	let subtopicLabels = [];

	//construct data and labels
	subtopicsViews.forEach(stv => {
		subtopicData.push(stv.views);
		labels.push(stv.subtopic);
		subtopicLabels.push(stv.subtopicDesc);
	});

	topicsViews.forEach(tv => {
		topicData.push(tv.views);
		labels.push(tv.topic);
		topicLabels.push(tv.topicDescrip);
	});

	// console.log('topicsViews', topicsViews);
	// console.log('subtopicsViews', subtopicsViews);
	// console.log('Labels', labels);
	
	//empty first to prevent old data bug
	if (topicsSubtopicsChart) topicsSubtopicsChart.destroy();

	var ctx = document.getElementById("topicsSubtopicsChart").getContext('2d');

	topicsSubtopicsChart = new Chart(ctx, {
		type: 'pie',
		data: {
			labels: labels,
			datasets: [{
				//label: 'Subtopics',
				labels: subtopicLabels,
				data: subtopicData,
				//hoverBackgroundColor: 'darkblue', 
				hoverBorderWidth: 3,
				hoverBorderColor: 'darkgrey',
				backgroundColor: [],
			},{
				//label: 'Topics',
				labels: topicLabels,
				data: topicData,
				//hoverBackgroundColor: 'darkgreen', 
				hoverBorderWidth: 3,
				hoverBorderColor: 'darkgrey',
				backgroundColor: [],
			}]
		},
		options: {
			plugins: {
				legend: {
						display: false,
				},
				title: {
					display: true,
					text: t('Topics and Subtopics Popularity Trend')
				},
				tooltip: {
					callbacks: {
						label: function (context) {
							console.log(context)
							
							return context.label + ': ' + context.formattedValue;
						}
					},
					titleFontSize: 20,
						bodyFontSize: 14,
						title: ''
				},
			},
			
      responsive: true,
			
			legend: {
				display: false
			},
			maintainAspectRatio: false,
			
		}
	});

	function colorTopics() {
		//budget 10 colors from color picker
		//don't add space betweeen commas please
		return [
			'rgb(255,115,0)',
			'rgb(255,196,0)',
			'rgb(212,255,0)',
			'rgb(34,255,0)',
			'rgb(0,255,187)',
			'rgb(0,149,255)',
			'rgb(0,149,255)',
			'rgb(183,0,255)',
			'rgb(255,0,179)',
			'rgb(255,0,85)',
			'rgb(255,115,0)',
		]
	}

	//this returns a color that is a bit lighter than what is given (+-20)
	function colorVarietyGenerator(rgbValue, multiplier) {
		//just trying to edit the green and blue
		
		let subtractor = 20 * (multiplier+1);
		let adder = 20 * (multiplier+1);
		//for example, rgb(255, 115, 0)
		// 115 - subtractor
		// 0 + adder
		// = rgb(255, 105, 10)
		let cleaned = rgbValue.replace('rgb', '').replace(/[{()}]/g, '');
		//console.log('cleaned', cleaned);
		let splitted = cleaned.split(',');

		let newRGB = `rgb(${splitted[0]},${parseInt(splitted[1])-subtractor},${parseInt(splitted[2])+adder}, 0.5)`;
		
		//console.log('output', newRGB);
		
		return newRGB;
	}
	//this returns a color that has lesser opacity
	function colorLessOpacGenerator(rgbValue, opacity) {
		let cleaned = rgbValue.replace('rgb', '').replace(/[{()}]/g, '');
		let splitted = cleaned.split(',');
		let newRGB = `rgb(${splitted[0]},${splitted[1]},${splitted[2]},${opacity})`;
		return newRGB;
	}

	//backgrounds for topics
	let topicsDT = topicsSubtopicsChart.data.datasets[1];
	
	topicsDT.backgroundColor = colorTopics();

	let subtopicsDT = topicsSubtopicsChart.data.datasets[0];

	//will not be using color variety anymore
	// for (let ii = 0; ii < uniqueTopics.length; ii++) {
	// 	let topicColors = colorTopics();
	// 	let subtopics = subtopicsViews.filter(stv => stv.topic == uniqueTopics[ii].kb_pinned_topic);
	// 	if (subtopics) {
	// 		for (var i = 0; i < subtopics.length; i++) {
	// 			// subtopicsDT.backgroundColor.push(colorVarietyGenerator(topicColors[ii], i));
	// 			subtopicsDT.backgroundColor.push(colorVarietyGenerator(topicColors[ii], i));
	// 		}
	// 	}
	// }
	
	//console.log('Subtopic Background Colors')
	//will be using the same color as the topic but less opacity
	console.log('lenght',allTopics.length)
	
	for (let ii = 0; ii < allTopics.length; ii++) {
		let topicColors = colorTopics();
		
		let subtopics = subtopicsViews.filter(stv => stv.topic == allTopics[ii]._id);
		if (subtopics) {
			for (var i = 0; i < subtopics.length; i++) {
				subtopicsDT.backgroundColor.push(colorLessOpacGenerator(topicColors[ii], 0.7));
			}
		}	
	}
	
	topicsSubtopicsChart.update();
	
}

function displayPageViewsChart() {
	//console.log('Display Pageviews chart');

	//get top 20 articles
	let arts = [];
	let views = [];
	if (allPageviewsAnalytics.length >= 20) {
		allPageviewsAnalytics.slice([0], [19]).map((item, i) => {
			arts.push(item.title);
			views.push(item.views);
		});
	} else {
		allPageviewsAnalytics.forEach(item => {
			arts.push(item.title);
			views.push(item.views);
		});
	}

	//bar chart -------------------------------------------------------------
	
	renderChart(views, arts);

	function renderChart(data, labels) {
		//empty first to prevent old data bug
		if (pageViewsChart) pageViewsChart.destroy();
		
		var ctx = document.getElementById("pageViewsChart").getContext('2d');
		let yAxesticks = [];

		pageViewsChart = new Chart(ctx, {
			type: 'bar',
			data: {
				labels: labels,
				datasets: [{
					label: t('Top 20 Popular Articles'),
					data: data,
					hoverBackgroundColor: 'lightgreen', 
					backgroundColor: [],
				}]
			},
			options: {
				maintainAspectRatio: false,
				scales: {
					x: {
						ticks: {
							callback: function (value, index, ticks) {
								return ''
						},
						}
					},
					y : {
						ticks : {
							beginAtZero : true,
							callback : function(value,index,ticks){
								yAxesticks.push(value);
								return value;
							}
						}
					}
				}
			}
		});

		let highestYvalue = data[0];

		var dataset = pageViewsChart.data.datasets[0];
		for (var i = 0; i < dataset.data.length; i++) {
			dataset.backgroundColor.push(searchColorSupplier(highestYvalue,dataset.data[i]));
		}
		console.log('datasets', dataset)
		pageViewsChart.update();
	}
}

function StringSpaceSplitter(w) {
  return w.match(/[\-&A-Z]+(?![a-z])|[A-Z]?[a-z]+|\d+/g).join(" ");
}

function displaySearchesTable(searches = []) {
	//console.log('Displaying searches table.');
	
	$("#tblSearches > tbody").empty();
	if (searches.length > 0) {
		searches.forEach((s) => {
			// let testEllipses = a.kb_title.substr(0, 50) + '&hellip;';
			$("#tblSearches > tbody").append(`<tr>
				<td>${s.search}</td>
				<td>${s.count}</td>
				<td class="text-center">${s.nbHits}</td>
			</tr>`);
		});	
	} else {
		$("#tblSearches > tbody").append(`<tr>
			<td>${t('No Data to show')}</td>
			<td></td>
			<td></td>
			
		</tr>`);
	}

	$('#spinnerSearches').hide(100);
}

function displayPageviewsTable(articles = []) {
	//console.log('Displaying Pageviews table');
	
	$("#tblPageViews > tbody").empty();
	if (articles.length > 0) {
		articles.forEach((a) => {
			// let testEllipses = a.kb_title.substr(0, 50) + '&hellip;';
			$("#tblPageViews > tbody").append(`<tr>
			<td class="rowid" data-articleid="${a.articleId}">${a.title}</td>
			<td>
				${a.comments && a.comments > 0
				? `<a href='#' class='linkCommentCount' title=${t('Click to view comments')}>${a.comments.toLocaleString('en')}</a>`
				: '0'}
				
			</td>
			<td>${a.likes ? a.likes.toLocaleString('en') : '0'}</td>
			<td>${a.dislikes ? a.dislikes.toLocaleString('en') : '0'}</td>
			<td>${a.views ? a.views.toLocaleString('en') : '0'}</td>
			</tr>`);
		});

		$('#modalViewComments').on('hidden.bs.modal', function () {
			window.top.postMessage("modalHide", "*");
		});

		//create the click event here ,not in document ready
		$('.linkCommentCount').on('click', function () {
			
			//$('#modalViewComments').modal('show');
			//try to disable blur backdrop
			// $('#modalViewComments').removeClass("modal-backdrop");
			// $('#modalViewComments').css('background', 'none');
			
			window.top.postMessage("modalShow", "*");

			//get id
			let articleid = $(this).closest('tr').find('.rowid').data('articleid');
			let articleTitle = $(this).closest('tr').find('.rowid').text();

			// $('#modalViewComments .modal-body').empty();

			//load details
			let articleComments = allArticleComments.filter(a => a.articleId == articleid);
			let commentDiv = `
				<div style="max-height: calc(100vh - 400px);overflow-y: auto;overflow-x: hidden;">
				<h5>${t('Title')}: ${articleTitle}</h5>
				
			`;

			if (articleComments) {
				articleComments.forEach(c => {
					//find from feedbacks also to show like and dislike
					let foundFeedback = allFeedbacks.find(f => f.articleId == articleid && f.user == c.user)

					let feedbackIcon = ``;
					if (foundFeedback) {
						if (JSON.parse(foundFeedback.helpful)) {
							feedbackIcon = `<span style="color: green; font-size: 20px;"><i class="fa fa-thumbs-up"></i></span>`;
						} else {
							feedbackIcon = `<span style="color: red; font-size: 20px;"><i class="fa fa-thumbs-down"></i></span>`;
						}
					}
					commentDiv += `
						<div class="mb-4 ml-2 mr-2 text-left" style="font-size: 14px;">
							${feedbackIcon} From <span class="font-weight-bold mb-1">${c.user}</span><small> on ${c.updatedAtFormatted}</small>
							<br>
							<div class="ml-4" style="border-left: 2px solid lightgray ; padding-left: 10px; padding-right: 40px">
								<span class="font-italic comment-left-border">${c.comment}</span <br>
							</div>
						</div>
					`;
				});

				//$('#modalViewComments .modal-body').append(commentDiv);
				commentDiv += `</div>`;
			}

			Swal.fire({
				//title: "Markdown Guide",
				html: commentDiv,
				showCloseButton: true,
				width: 900,
				customClass: {
					popup: "swal2-border-radius",
				},
				icon: "",
					showConfirmButton: false,
				// confirmButtonText: "Close",
				// confirmButtonColor: '#3085d6',
				didOpen: function () {
					window.top.postMessage("modalShow", "*");
				},
				didClose: function () {
					window.top.postMessage("modalHide", "*");
				},
				backdrop: `rgba(0,0,0,0.5)`,
				showClass: {
					backdrop: "swal2-noanimation", // disable backdrop animation
					popup: "", // disable popup animation
					icon: "", // disable icon animation
				},
				hideClass: {
					popup: "", // disable popup fade-out animation
				},
    		});


		});



		$('#spinnerPageViews').hide(100);
	} else {
		$("#tblPageViews > tbody").append(`<tr>
			<td>${t('No Data to show')}</td>
			<td></td>
			<td></td>
			<td></td>
		</tr>`);
		$('#spinnerPageViews').hide(100);
	}
}

function displaySearchResults(articles = []) {
	//console.log('Displaying article search results..');
	$("#articleSearchResults").empty();

	$("#articleSearchResults").append(`
		<li class="list-group-item">
		<strong>${t('Articles')} (Count : <span class="text-primary">${articles.length}</span>)</strong>
		</li>`);

	articles.forEach((a) => {
		// console.log(a);
		let regionalFlag = '';

		let status = a.kb_published == 'true' ? 
		'<span class="badge badge-green">'+t('Published')+'</span>' : 
		'<span class="badge badge-orange">'+t('Draft')+'</span>';
		// let regional = a.kb_regional_visibility && a.kb_regional_visibility.length > 0 ?
		//   `<span class="badge badge-outline-dark align-middle">${a.kb_regional_visibility}</span>` : '';

		
		let permission = Number(a.kb_security_level) > 100 ? 
		`<span class="badge badge-outline-dark align-middle">
			${t('Security')}: ${a.kb_security_level}
		</span>` : '';

		let subTopicChild = a.kb_pinned_subtopic && a.kb_pinned_subtopic.length > 0 ?
			`<ul>
				<li><p>${StringSpaceSplitter(a.kb_pinned_subtopic)}</p></li>
			</ul>`
			: '';

		let unOrderedTopic = a.kb_pinned_topic && a.kb_pinned_topic.length > 0 ? 
			`<span class="badge badge-outline-none align-middle topic-tree">
			${StringSpaceSplitter(a.kb_pinned_topic)} ${subTopicChild}
			</span>`
		: '';
		
		        if (a.kb_regional_visibility && a.kb_regional_visibility.length > 1) {
        //check if there are more than one country
        let allCountries = [];
        let flagsDirectory = `${appContext}/flags/`;

        if (a.kb_regional_visibility && a.kb_regional_visibility.includes(',')) {
            allCountries = a.kb_regional_visibility.split(',');
			allCountries.forEach(c => {
			let flagName = c.trim();
			//find from flagsAvail
			let foundFlag = flagsAvailable.find(f => f.country == flagName);
			//console.log(`flag for: ${a.kb_regional_visibility} is ${foundFlag}`);
			if (foundFlag) {
				//set flag
				regionalFlag += `<img class="country-flag-icon ml-1" alt="${a.kb_regional_visibility}" src="${flagsDirectory}/${foundFlag.filename}">`;
			} 
			// else {
			// 	//default to AU
			// 	regionalFlag += `<img class="country-flag-icon ml-1" alt="${a.kb_regional_visibility}" src="${flagsDirectory}/AU.svg">`;
			// }
			});
		        } else {
            //just one country flag
            let foundFlag = a.kb_regional_visibility ? flagsAvailable.find(f => f.country == a.kb_regional_visibility.trim()) : null;
			if (foundFlag) {            
			regionalFlag += `<img class="country-flag-icon ml-1" alt="${a.kb_regional_visibility}" src="${flagsDirectory}/${foundFlag.filename}">`;
			} 
			// else {            
			// regionalFlag += `<img class="country-flag-icon ml-1" alt="${a.kb_regional_visibility}" src="${flagsDirectory}/AU.svg">`;
			// }
		}
		}
		
		// let status = a.kb_published == 'true' ? 
		// 	`<i class="fa fa-check align-middle badge-status-green" title="Published"></i>` : 
		// 	'<i class="fa fa-pencil align-middle badge-status-orange" title="Draft"></i>' ;

		// let regional = a.kb_regional_visibility && a.kb_regional_visibility.length > 0 ?
		//  	`<span class="badge badge-secondary align-middle">${a.kb_regional_visibility}</span>` : '';
		// let permission = Number(a.kb_security_level) > 100 ? 
		// 	`<span class="badge badge-secondary align-middle">
		// 		${a.kb_security_level}
		// 	</span>` : '';
		const userLanguage = $('#user_lang').val();
		
		$("#articleSearchResults").append(
			`<li class="list-group-item">
			<div class="row">
			<div class="col">
				<a href="${appContext}/edit/${a._id}" class="align-middle">${a.kb_title}</a>
				${a.kb_translation_title  !== undefined ? `<br /><small class="font-italic">Translation: ${a.kb_translation_title[userLanguage]}</small>` : ''}
				<span class="pull-right">
				${regionalFlag}
				${permission}
				</span>
			</div>
			<div class="col-2" style="margin-left: -10px;padding-left: 0px;">
				<span>
				${unOrderedTopic}
				</span>
			</div>
			<div class="col-1 pull-right">
				<span>
				${status}
				</span>
			</div>
			<div class="col-1 pull-right">
		
				<span role="button" title="Duplicate" data-id="${a._id}" class="article-duplicate">
				<i class="fa fa-copy"></i>
				</span>
			
			</div>
			</div>
		</li>`
		);
	});
}

async function getAllSearches() {
	uniqueSearches = [];
	$('#spinnerSearches').show(100);
	const index = $('#algolia-index').val();
	const applicationId = $('#algolia-appId').val();
	const applicationKey = $('#algolia-apiKey').val();

	await $.ajax({
		method: "GET",
		url: `https://analytics.us.algolia.com/2/searches?index=${index}&clickAnalytics=true&limit=1000`,
		headers: {
			'X-Algolia-Application-Id': applicationId,
			'X-Algolia-API-Key': applicationKey
		}
	})
	.done(function (res) {
		console.log('res',res)
		$("#tblSearches > tbody").empty();
		uniqueSearches = res.searches || [];
		displaySearchesTable(res.searches || []);
	})
	.fail((err) => {
		$('#spinnerSearches').hide(100);
		console.error(err);
    });
}

async function getAllFeedbacks(from, to) {
	//this calls the pageviews bar chart and topics pie chart
	//console.log('Getting all feedbacks...');
	allFeedbacks = [];
	$.ajax({
		method: "GET",
		url: `${appContext}/feedbacks/all/${from}to${to}`,
	})
	.done(function (res) {
		//console.log('feedbacks: ', res)	
		if (res && res.message == "success") {
			if (res.data && res.data.length > 0) {
				allFeedbacks = res.data;
				// res.data.forEach((a) => {
				// 	allFeedbacks.push(a);
				// });

				//insert likes and dislikes (MANUALLY)
				//allPageviewsAnalytics.forEach(a => {
				//for (let i = 0; i < allPageviewsAnalytics.length; i++) {
				for (let a of allPageviewsAnalytics) {
					// let a = allPageviewsAnalytics[i];
					//1 find all feedbacks for a._id and loop thru each
					let articleFeedbacksFound = allFeedbacks.filter(af => af.articleId == a.articleId);
					if (articleFeedbacksFound && articleFeedbacksFound.length > 0) {
						articleFeedbacksFound.forEach(fb => {
							//console.log('evaluating -- ', JSON.parse(fb.helpful));
							if (JSON.parse(fb.helpful)) {
								//was helpful
								a.likes++;
							} else if (!JSON.parse(fb.helpful)) {
								a.dislikes++;
							}
						});
					}
				}
				//});
			} 
			// console.log('Done Getting all feedbacks');
		} else {
			$('#spinnerPageViews').hide(100);	
			console.log("Something is wrong with the feedbacks query...");
		}

	})
	.fail(function () {
		$('#spinnerPageViews').hide(100);	
		console.log("fail feedbacks query ---------------- ");
	});
}

async function getAllComments(from, to) {
	//this calls the pageviews bar chart and topics pie chart
	//console.log('Getting all comments...');
	allArticleComments = [];
	$.ajax({
		method: "GET",
		url: `${appContext}/comments/all/${from}to${to}`,
	})
	.done(function (res) {
		//console.log('comments: ', res)	
		if (res && res.message == "success") {
			if (res.data && res.data.length > 0) {
				allArticleComments = res.data;
				
				//insert likes and dislikes (MANUALLY)
				//allPageviewsAnalytics.forEach(a => {
				for (let a of allPageviewsAnalytics) {
				//for (let i = 0; i < allPageviewsAnalytics.length; i++) {
					//let a = allPageviewsAnalytics[i];
					
					//1 find all feedbacks for a._id and loop thru each
					let articleCommentsFound = allArticleComments.filter(af => af.articleId == a.articleId);
					if (articleCommentsFound && articleCommentsFound.length > 0) {
						articleCommentsFound.forEach(fb => {
							a.comments += 1;
						});								
					}
				}
				//});

				//console.log('Done Getting all comments');

				//then display
				displayPageviewsTable(allPageviewsAnalytics);			
			
			} else {
				displayPageviewsTable(allPageviewsAnalytics);
			}
			
		} else {
			$('#spinnerPageViews').hide(100);	
			console.log("Something is wrong with the feedbacks query...");
		}
	})
	.fail(function () {
		$('#spinnerPageViews').hide(100);	
		console.log("fail query ---------------- ");
	});
}

async function getAllPageviews(from, to) {
	//console.log('Getting all pageviews...');
	$('#spinnerPageViews').show(100);

	allPageviewsAnalytics = [];

	await $.ajax({
		method: "GET",
		url: `${appContext}/pageviews/all/${from.format('x')}/${to.format('x')}`,
	})
	.done(function (res) {
		if (res && res.message == "success") {
			if (res.data && res.data.length > 0) {
				let allPageviews = res.data;

				//because pageviews only stores articleId, we need to get the title of the article
				//use allArticlesAnalytics to map
				
				//goal - find the fastest way to update title of all items

				//1 get all unique articleIds
				let uniqueArticleIds = [...new Map(res.data.map(item =>
					[item['articleId'], item])).values()];
				
				//2 get title of that articleId
				//uniqueArticleIds.forEach(ua => {
				for(let ua of uniqueArticleIds) {
					let foundArticle = allArticlesAnalytics.find(a => a._id == ua.articleId);
					if (foundArticle) {
						//3 update all items with that articleId
						let filteredPageviews = allPageviews.filter(ap => ap.articleId == ua.articleId);
						if (filteredPageviews) {
							let totalPageViews = 0;
							filteredPageviews.forEach((fp, index) => {
								// fp.title = foundArticle.kb_title;

								//4 total all pageviews
								totalPageViews += parseInt(fp.views);
							});

							let newPageView = {
								articleId: ua.articleId,
								title: foundArticle.kb_title,
								kb_pinned_topic: foundArticle.kb_pinned_topic,
								kb_pinned_subtopic: foundArticle.kb_pinned_subtopic,
								views: totalPageViews,
								likes: 0,
								dislikes: 0,
								comments: 0
							}

							//manually reducing the array of objects

							//push once
							allPageviewsAnalytics.push(newPageView);
						}

					} else {
						console.log(`Article not found: ${ua.articleId}`);
					}
				}
				//});

				//show highest first
				allPageviewsAnalytics = allPageviewsAnalytics.sort((a, b) => {
					let bb = b.views;
					let aa = a.views;
					return bb - aa;
				});


			} 

				
			//bar chart for page views
			displayPageViewsChart();
			
			//topics chart
			displayTopicsChart();	
		} else {			
			console.log("Something is wrong with the pageviews query...");
		}
		//console.log('Done Getting all pageviews');

	})
	.fail(function () {		
      	console.log("fail query ---------------- ");
    });
}

async function getAllArticles() {
  	//get all articles at once
	allArticles = [];
	allArticlesAnalytics = [];

	//console.log('Getting all Articles...');

	let finalRoute = `${appContext}/articles/minimal`;

	await $.ajax({
			method: "GET",
			url: finalRoute,
		})
		.done(function (res) {
      	if (res && res.message == "success") {
			if (res.data && res.data.length > 0) {
				res.data.forEach((a) => {
					//this will be used in title search
					a.loweredTitle = a.kb_title.toLowerCase();
					allArticles.push(a);

					if (a.kb_published == 'true') {
						allArticlesAnalytics.push(a);
					}

				});
				//articles page - 2nd tab	
				//displaySearchResults(res.data);				

			} 
		} else {			
			console.log("Something is wrong with the search query...");
		}
	})
	.fail(function () {		
      	console.log("fail query ---------------- ");
    });
}
async function searchArticles() {
  //use ajax instead - so no page refresh
  //new style
  let appContext = $("#app_context").val();
  let term = $("#article_filter").val();
  let topic = $("#articleTopicOptions").val();
  let subtopic = $("#articleSubTopicOptions").val();

  let finalRoute = `${appContext}/articles/search/${term}`;

  if (topic && topic.length > 0) {
    finalRoute = `${appContext}/articles/search/${term}/${topic}`;
  }
  if (subtopic && subtopic.length > 0) {
    finalRoute = `${appContext}/articles/search/${term}/${topic}/${subtopic}`;
  }

  //window.location.href = finalRoute;

  await $.ajax({
    method: "GET",
    url: finalRoute,
  })
    .done(function (res) {
      $("#articleSearchResults").empty();
      if (res && res.message == "success") {
        if (res.data && res.data.length > 0) {
          $("#articleSearchResults").append(`
			<li class="list-group-item">
			<strong>Articles (${t('Count')} : <span class="text-primary">${res.data.length}</span>)</strong>
			  </li>`);
          res.data.forEach((a) => {
            $("#articleSearchResults").append(
              `<li class="list-group-item">
					  <div class="row">
						  <div class="col-lg-8"><a href="${appContext}/edit/${a._id}">${a.kb_title}</a></div>						
					  </div>
				  </li>`
            );
          });
        }
      } else {
        console.log("Something is wrong with the search query...");
      }
    })
    .fail(function () {
      console.log("fail query ---------------- ");
    });
}
async function getAllTopicsData() {
	
	await $.ajax({
		method: "GET",
		url: $("#app_context").val() + `/topics_list`,
	})
		.done(function (res) {
			if (res && res.data) {
				//allTopics = res.data;
				$("#selectTopics option").remove();
				const userLang = $('#user_lang').val();

				res.data.map((topic) => {
					allTopics.push(topic);
					
					$("#articleTopicOptions").append(`
						<option value="${topic._id}">${t(topic.topic)} ${topic.topicTranslations && topic.topic !== topic.topicTranslations[userLang] ? '('+topic.topicTranslations[userLang]+')' : ''}</option>
					`);

					if (topic.subtopics) {
						topic.subtopics.map((s, i) => {
							allSubtopics.push({
								id: s.id,
								topic: topic._id,
								subtopic: s.subtopic,
							});
						});
					}
				});

				// console.log('alltopics', allTopics)
				// console.log('allsubtopics', allSubtopics)

				//now set values based on data attribute from dropdown
				let selectTopics = $("#selectTopics");
				selectTopics.val(selectTopics.data("topic"));

				//set subtopics
				setSubtopicSelect(selectTopics.data("topic"));
			}
		})
		.fail(function () {
			console.log("Failed getting topics and subtopics");
		});
}

function setSubtopicSelect(topic = "") {
	const articleSubTopic =  $('#kb_pinned_subtopic').val();
  if (topic.length > 0) {
    //get values from allSubtopics based on respective topic
    $("#articleSubTopicOptions option").remove();

    $("#articleSubTopicOptions").append('<option value="">All</option>');

    let subtopicSelect = $("#articleSubTopicOptions");

    allSubtopics.map((s) => {
      if (s.topic == topic) {
        let nospace = s.subtopic.replace(/\s/g, "");

        subtopicSelect.append(`
                    <option value="${nospace}" ${articleSubTopic === nospace ? 'selected': ''} >${s.subtopic}</option>
                `);
      }
    });
  } else {
    $("#articleSubTopicOptions option").remove();
    $("#articleSubTopicOptions").append('<option value="">All</option>');
  }
}


