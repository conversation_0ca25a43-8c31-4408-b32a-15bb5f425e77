$(document).ready(async function () {
    await initTranslator();

    const userLang = $('#user_lang').val() ?? 'en';
    const is_pseudoadmin = $('#is_pseudoadmin').val() ?? 'false';
    const countryDropdownData = $('#selectCountryDropdown').data('options');
    const pseudoadminCountries = countryDropdownData ? countryDropdownData.split(',').join(', ') : '';
    const articleId = $('#article_id').val();
    
    
    const selectPlaceholder = is_pseudoadmin === 'true' && articleId == '' ? pseudoadminCountries : t('Global');

    // Store original article data for change tracking
    let originalArticleData = null;
    
    // Capture original data on page load for existing articles
    // Delay capture to ensure dropdowns are populated
    if (articleId && articleId !== '') {
        setTimeout(() => {
            originalArticleData = captureCurrentArticleData();
            console.log('Original article data captured:', originalArticleData);
        }, 1000); // Wait 1 second for dropdowns to be populated
    }
    

    // Send 'hide modal' on page load/reload...
    // Since our app runs in an iframe, send bootstrap model 'hide|show' events
    // to the parent iframe to also apply a model overlay background...
    window.top.postMessage("modalHide", "*");



    let settingsDiv = $("#settingsModal");
    let historyDiv = $("#articleHistory");


    //hide the settings first
    settingsDiv.hide();
    historyDiv.hide();
    console.log('edit article ready')

    $("#btnSettings").on("click", function () {
        if (settingsDiv.is(":hidden")) {
            $("#settingsModal").show(50);
            $("#articleHistory").hide();
        } else {
            $("#settingsModal").hide();
        }
    });
    console.log('btnHistory', $("#btnHistory"))

    $("#btnHistory").on("click", function () {
        console.log('article history')
        if (historyDiv.is(":hidden")) {
            $("#articleHistory").show(50);
            $("#settingsModal").hide();
        } else {
            $("#articleHistory").hide();
        }
	});
	
	$('#navSettingsDashboard').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings';
	});
	$('#navSettingsArticles').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/articles';
	});
	$('#navSettingsConfiguration').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/configuration';
	});

    $("#btnCloseModal").on("click", function () {
        $("#settingsModal").hide();
    });

    $("#btnCloseHistory").on("click", function () {
        $("#articleHistory").hide();
    });

    //get all topics to be used in Topic and Subtopic dropdown
    getAllTopicsData().then(() => {
        // Capture original data after topics are loaded
        if (articleId && articleId !== '' && !originalArticleData) {
            setTimeout(() => {
                originalArticleData = captureCurrentArticleData();
                console.log('Original article data captured after topics loaded:', originalArticleData);
            }, 500);
        }
    }).catch((error) => {
        console.warn('Failed to load topics, but will still capture original data:', error);
        // Still capture original data even if topics failed to load
        if (articleId && articleId !== '' && !originalArticleData) {
            setTimeout(() => {
                originalArticleData = captureCurrentArticleData();
                console.log('Original article data captured (topics failed):', originalArticleData);
            }, 1000);
        }
    });

    // Country selector click handlers for the new card-based design ---------------------------------------------------------------
    
    // Direct click handler for overSelect element
    $(document).on("click", "#overSelect", function (e) {
        console.log("overSelect clicked!"); // Debug log
        e.preventDefault();
        e.stopPropagation();
        
        // Check if the select dropdown is disabled
        if($('#selectCountryDropdown').is(':disabled') || $('#overSelect').hasClass('disabled')) {
            return false;
        }
        
        var checkboxes = document.getElementById("countryCheckboxes");
        if (!expanded) {
            checkboxes.style.display = "block";
            //adjust width to match the select dropdown
            $("#countryCheckboxes").css("width", $("#selectCountryDropdown").outerWidth());
            expanded = true;
        } else {
            checkboxes.style.display = "none";
            expanded = false;
        }
    });

    // Handle clicks on country checkboxes - don't hide dropdown
    $(document).on("click", ".countryLabel, .countryCheck", function (e) {
        e.stopPropagation();
        $("#countryCheckboxes").css("width", $("#selectCountryDropdown").outerWidth());
        expanded = true;
    });

    // Close dropdown when clicking outside
    $(document).on("click", function (e) {
        if (expanded && !$(e.target).closest("#countryCheckboxes, #overSelect, #selectCountryDropdown").length) {
            $("#countryCheckboxes").css("display", "none");
            expanded = false;
        }
    });

    //check if Article is Pinned - updated for new card design
    const topicCheckbox = document.getElementById('kb_pinned');
    const topicSelectionGroup = document.querySelector('.ph-topic-selection-group');

    const toggleTopicSelectionGroupVisibility = () => {
        if (!topicCheckbox || !topicSelectionGroup) return; // Early exit if elements not found
        topicSelectionGroup.style.display = topicCheckbox.checked ? 'block' : 'none';
        if (!topicCheckbox.checked) {
            // Optional: Clear selections when hiding
            const topicSelect = document.getElementById('selectTopics');
            const subtopicSelect = document.getElementById('selectSubtopics');
            if (topicSelect) topicSelect.value = '';
            if (subtopicSelect) subtopicSelect.value = '';
            selectTopicsChange(); // Call to clear subtopics and reset country visibility
        }
    };

    // Set initial state
    toggleTopicSelectionGroupVisibility();

    // Add event listener for changes
    if (topicCheckbox) {
        topicCheckbox.addEventListener('change', toggleTopicSelectionGroupVisibility);
    }
    // -------------------------------------------------------------------------------------------

    if ($("#frm_kb_title").val().length > 5) {
        $("#generate_permalink_from_title").removeClass("disabled");
    } else {
        $("#generate_permalink_from_title").addClass("disabled");
    }

    setAvailableCountries();

    //event when checked one of country checkboxes
    
    $("#countryCheckboxes").on("change", '.countryCheck', function () {
        const selectedCountries = $("#frm_kb_regional_visibility").val() !== '' ? $("#frm_kb_regional_visibility").val().split(",") : [];

        let country = $(this).data("country");

        if ($(this).is(":checked")) {
            selectedCountries.push(country);
        } else {
            if(is_pseudoadmin !== 'true') {
                selectedCountries.splice(selectedCountries.indexOf(country), 1);
            } else {
                console.log('selectedCountries', selectedCountries)
                if(selectedCountries.length <= 1) {
                    $(this).prop('checked', true);
                    Swal.fire("Error", t("Pseudoadmin cannot set article to global visibility"), "error");
                }    
                else {
                    selectedCountries.splice(selectedCountries.indexOf(country), 1);
                }
            }
        }

        selectedCountries.filter((s) => s !== "");
        console.log('selectedCountries', selectedCountries)

        if (selectedCountries.length > 0) {
            $("#optionSelectCountry").text(selectedCountries.join(", "));
            $("#frm_kb_regional_visibility").val(selectedCountries.join(","));
        } else {
            $("#optionSelectCountry").text(selectPlaceholder);
            $("#frm_kb_regional_visibility").val("");
        }
    });

    $("#selectTopics").on("change", selectTopicsChange);

    async function selectTopicsChange() {
        const subtopicSelect = $("#selectSubtopics");
        $(subtopicSelect).children('option').remove();
        subtopicSelect.append(`
            <option value="">Select Subtopic</option>
        `);

        if ($("#selectTopics").val() !== '') {
            const data = await $.ajax({
                method: "GET",
                url: $("#app_context").val() + '/topic/' + $(this).val() + '/details',
            })

            const topicDetails = data.data;
            //subtopics
            
            
            topicDetails.subtopics.forEach((s) => {
                subtopicSelect.append(`
                    <option value="${s.id}">${s.subtopic} ${s.subtopicTranslations && s.subtopic !== s.subtopicTranslations[userLang] ? '('+s.subtopicTranslations[userLang]+')' : ''}</option>
                `);
            });

            const countriesData = $("#selectCountryDropdown").data("possibleOptions")
            const countryOptions = (topicDetails.topic_regional_visibility === '' || topicDetails.topic_regional_visibility === undefined) ? countriesData : topicDetails.topic_regional_visibility;
            console.log('countryOptions',countryOptions)
            $("#selectCountryDropdown").data("options", countryOptions);
            //$("#frm_kb_regional_visibility").val('');

            let selectedCountries = $("#frm_kb_regional_visibility").val().split(/(?:,)\s*/i);
            
            selectedCountries = selectedCountries.filter((s) => {
                console.log('is included', countryOptions.split(/(?:,)\s*/i).includes(s))
                return countryOptions.split(/(?:,)\s*/i).includes(s);
            });
            
            console.log('selectedCountries', selectedCountries)

            $("#frm_kb_regional_visibility").val(selectedCountries.join(','));

            $("#countryCheckboxes").html('');
            $("#optionSelectCountry").text(selectedCountries.length > 0 ? selectedCountries.join(', ') : selectPlaceholder);
            setAvailableCountries()
        } else {
            $("#countryCheckboxes").html('');
            $("#frm_kb_regional_visibility").val($('#original-visibility').val())
            $("#optionSelectCountry").text($('#original-visibility').val().split(',').join(', '));
            $("#selectCountryDropdown").data("options", $("#selectCountryDropdown").data('possible-options'));
            setAvailableCountries()
        }
    }

    $("#btnCopyArticleLink").on("click", function () {
        let permaLink = $("#frm_kb_permalink").val();
        let routeName = $(this).data("routename");
        let articleId = $(this).data("articleid");
        let pageUrl = `${routeName}`;

        //permalink must prevail
        if (permaLink.length > 0) {
            pageUrl += `/${permaLink}`;
        } else {
            pageUrl += `/${articleId}`;
        }

        navigator.clipboard.writeText(pageUrl);

        //show toast
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: false,
            didOpen: (toast) => {
                toast.addEventListener("mouseenter", Swal.stopTimer);
                toast.addEventListener("mouseleave", Swal.resumeTimer);
            },
            showClass: {
                backdrop: "swal2-noanimation", // disable backdrop animation
                popup: "", // disable popup animation
            },
        });

        Toast.fire({
            icon: "success",
            title: t("Article URL copied successfully"),
        });
    });

    //to adjust height
    let tokenf = $(".tokenfield");
	tokenf.removeClass("form-control");



    function setAvailableCountries() {
        // about countries multi-select ------------------------------------------------------------------------------------
       //setup countries dropdown
       const selectedCountries = [];
       const regionalValue = $("#frm_kb_regional_visibility").val();
       const countriesData = $("#selectCountryDropdown").data("options");
       
       let countries = countriesData.split(",");
    
       for (let i = 0; i < countries.length; i++) {
           let countryId = countries[i].replace(/\s/g, "");
           $("#countryCheckboxes").append(`
               <label for="dropCheck-${countryId}" class="countryLabel" id="countryLabel-${countryId}">
                   <input type="checkbox" id="dropCheck-${countryId}"
                   class="mr-2 countryCheck" data-country="${countries[i]}" ${is_pseudoadmin === 'true' && articleId == '' ? 'checked="checked"': ''}  />${countries[i]}
               </label>
           `);
       }
   

       if (regionalValue.length > 0) {
           $("#optionSelectCountry").text(regionalValue.split(/(?:,)\s*/i).join(', '));
   
           //check if multiple countries
           if (regionalValue.includes(",")) {
               let regionals = regionalValue.split(",");
               regionals.forEach((r) => {
                   let countryId = r.replace(/\s/g, "").trim();
   
                   //checks the checkbox
                   $("input[id='dropCheck-" + countryId + "']").prop("checked", true);
                   selectedCountries.push(r.trim());
               });
           } else {
               let countryId = regionalValue.replace(/\s/g, "");
   
               $(`#dropCheck-${countryId}`).prop("checked", true);
               selectedCountries.push(regionalValue);
           }
       } else {
           $("#optionSelectCountry").text(selectPlaceholder);
       }
   }

    $(document).on('click', '#btnDeleteArticle', function (e) {
        if ($(this).is('[disabled]')) return;

        e.preventDefault();

        const deleteUrl = $(this).attr('href');

        Swal.fire({
            title: t('Are you sure?'),
            text: t('This action cannot be undone and will permanently delete the article.'),
            icon: 'warning',
            showCancelButton: true,
            customClass: {
                confirmButton: 'btn btn-danger waves-effect ph-btn',
                cancelButton: 'btn btn-primary waves-effect ph-btn',
            },
            buttonsStyling: false,
            focusConfirm: false,
            confirmButtonText: t('Yes, delete it!'),
            cancelButtonText: t('Cancel'),
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                // Optional: show a loading indicator before redirecting
                Swal.fire({
                    title: t('Deleting...'),
                    allowOutsideClick: false,
                    didOpen: () => Swal.showLoading(),
                });
                window.location.href = deleteUrl;
            }
        });
    });

    // ─── Preview Toggle Functionality ────────────────────────────────────────────
    
    const $markdownToggle = $('#editor-switch');
    const $btnTogglePreview = $('#btnTogglePreview');
    const $previewArea = $('#preview-wrap');
    const $previewButtonText = $btnTogglePreview.find('.button-text');
    const $previewButtonIcon = $btnTogglePreview.find('i.material-icons');

    // Function to update the button text and icon
    const updateToggleButtonUI = (isPreviewVisible) => {
        if (isPreviewVisible) {
            $previewButtonText.text(t('Hide Preview'));
            $previewButtonIcon.text('chevron_right');
        } else {
            $previewButtonText.text(t('Show Preview'));
            $previewButtonIcon.text('chevron_left');
        }
    };

    // Function to sync preview visibility with button state
    const syncPreviewVisibility = (showPreview) => {
        const isMarkdownEnabled = $markdownToggle.is(':checked');
        console.log('should show preview area')

        if (isMarkdownEnabled && showPreview) {
            console.log('should show preview area')
            $previewArea.removeClass('hidden');
            updateToggleButtonUI(true);
        } else {
            console.log('should hide preview area')
            $previewArea.addClass('hidden');
            updateToggleButtonUI(false);
        }
    };

    // Function to update preview toggle button visibility
    const updatePreviewToggleButtonVisibility = (isMarkdownEnabled) => {
        if (isMarkdownEnabled) {
            $btnTogglePreview.show();
        } else {
            $btnTogglePreview.hide();
        }
    };

    // Function to update the editor label text based on markdown toggle state
    const updateEditorLabel = () => {
        const isMarkdownEnabled = $markdownToggle.is(':checked');
        const $editorLabel = $('.ph-editor-label label[for="editor"]');
        if ($editorLabel.length) {
            if (isMarkdownEnabled) {
                $editorLabel.html(`${t('Article body (Markdown)')}`);
            } else {
                $editorLabel.html(`${t('Article body (WYSIWYG)')}`);
            }
        }
    };

    // Expose functions globally so openKB.js can access them
    window.updatePreviewToggleButtonVisibility = updatePreviewToggleButtonVisibility;
    window.syncPreviewVisibility = syncPreviewVisibility;
    window.updateEditorLabel = updateEditorLabel; // Expose the new function globally

    // Initialize preview toggle functionality
    const initializePreviewFunctionality = () => {
        // Restore preview state from localStorage (default to true if not set)
        const savedPreviewState = localStorage.getItem('ph-editor-show-preview');
        const shouldShowPreview = savedPreviewState !== null ? savedPreviewState === 'true' : true;

        // Immediately hide the preview area to prevent FOUC/race conditions
        syncPreviewVisibility(false);
        
        // Capture initial markdown enabled state on page load
        const initialMarkdownEnabled = $markdownToggle.is(':checked');

        // Set initial visibility of the button. This happens immediately.
        updatePreviewToggleButtonVisibility(initialMarkdownEnabled);
        
        // Set initial state of the editor label
        updateEditorLabel();
        
        // Set initial visibility of the preview area after a delay
        // This counters potential race conditions where other scripts (e.g., markdown editor)
        // might be overriding display properties on editor initialization.
        setTimeout(() => {
            // Only sync preview visibility if markdown editor was initially enabled on page load
            if (initialMarkdownEnabled) {
                if (shouldShowPreview) {
                    syncPreviewVisibility(true);
                } else {
                    // Explicitly hide again if markdown was enabled but preview should be off
                    syncPreviewVisibility(false);
                }
            } else {
                // Ensure it's hidden if markdown was not initially enabled
                syncPreviewVisibility(false);
            }
        }, 1000); // Increased delay to 1000ms for more robustness
    };

    // Preview toggle button click handler
    $btnTogglePreview.on('click', function() {
        const currentlyVisible = !$previewArea.hasClass('hidden');
        const newState = !currentlyVisible;
        
        localStorage.setItem('ph-editor-show-preview', newState.toString());
        
        syncPreviewVisibility(newState);
    });

    if ($markdownToggle.length && $btnTogglePreview.length) {
        initializePreviewFunctionality();
    }
    
    // Add debug button for development (can be removed in production)
    const $editorControls = $('.ph-editor-controls');
    if ($editorControls.length) {
        const $debugDataButton = $('<button id="btnDebugData" class="btn btn-sm btn-secondary ml-2" style="margin-left: 8px;" type="button">Debug Data</button>');
        $editorControls.append($debugDataButton);
        
        // Only keep the debug data button handler
        $debugDataButton.on('click', function(e) {
            e.preventDefault();
            
            // Test with mock consolidated changes
            const mockChanges = [
                {
                    field: 'keywords',
                    fieldName: 'Keywords',
                    oldText: 'Keywords: ["test", "test1"]',
                    newText: 'Keywords: ["test1", "test3", "lorem ipsum", "hello world"]'
                },
                {
                    field: 'isMarkdown',
                    fieldName: 'Editor Type',
                    oldText: 'Editor Type: WYSIWYG mode',
                    newText: 'Editor Type: Markdown mode'
                },
                {
                    field: 'published',
                    fieldName: 'Publication Status',
                    oldText: 'Publication Status: Draft',
                    newText: 'Publication Status: Published'
                }
            ];

            // Show loading state
            $(this).prop('disabled', true).text('Testing...');
            
            $.ajax({
                url: $("#app_context").val() + '/api/ai/summarize-changelog',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ changes: mockChanges }),
                success: function(response) {
                    console.log('✅ AI API Test Successful!');
                    console.log('AI response:', response.data);
                    
                    Swal.fire({
                        title: 'AI API Test Successful',
                        html: `
                            <div style="text-align: left; margin: 20px 0;">
                                <h4>🤖 Consolidated AI Summary:</h4>
                                <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; text-align: left;">${JSON.stringify(response.data, null, 2)}</pre>
                                <p><strong>Changes processed:</strong> ${response.changeCount || mockChanges.length}</p>
                            </div>
                        `,
                        icon: 'success',
                        confirmButtonText: 'Cool!',
                        confirmButtonColor: '#52ad39'
                    });
                    
                    // Reset button
                    $('#btnTestAI').prop('disabled', false).text('Test AI API');
                },
                error: function(error) {
                    console.error('❌ AI API Test Failed:', error);
                    
                    // Show error notification
                    Swal.fire({
                        title: 'AI API Test Failed',
                        html: `
                            <div style="text-align: left; margin: 20px 0;">
                                <p>Error: ${error.responseJSON?.error || error.statusText || 'Unknown error'}</p>
                                <p>Status: ${error.status}</p>
                            </div>
                        `,
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc3545'
                    });
                    
                    // Reset button
                    $('#btnTestAI').prop('disabled', false).text('Test AI API');
                }
            });
        });

        $testChangesButton.on('click', function(e) {
            e.preventDefault();
            
            if (!originalArticleData) {
                Swal.fire({
                    title: 'No Original Data',
                    text: 'Original article data not captured. This feature only works when editing existing articles.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return;
            }

            const currentData = captureCurrentArticleData();
            const changes = detectArticleChanges(originalArticleData, currentData);
            
            console.log('Original data:', originalArticleData);
            console.log('Current data:', currentData);
            console.log('Detected changes:', changes);
            
            if (changes.length === 0) {
                Swal.fire({
                    title: 'No Changes Detected',
                    text: 'No differences found between original and current article data.',
                    icon: 'info',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Show detected changes
            let changesHtml = changes.map(change => {
                // Display normalized values for better readability
                let displayOldValue = change.oldValue;
                let displayNewValue = change.newValue;
                
                // Special formatting for certain fields
                if (change.field === 'regionalVisibility') {
                    displayOldValue = displayOldValue || 'Global';
                    displayNewValue = displayNewValue || 'Global';
                } else if (change.field === 'password') {
                    // Mask password values for security
                    displayOldValue = displayOldValue ? '••••••••' : 'No password';
                    displayNewValue = displayNewValue ? '••••••••' : 'No password';
                } else if (change.field === 'isMarkdown') {
                    displayOldValue = displayOldValue ? 'Markdown mode' : 'WYSIWYG mode';
                    displayNewValue = displayNewValue ? 'Markdown mode' : 'WYSIWYG mode';
                } else if (change.field === 'published') {
                    displayOldValue = displayOldValue === 'true' ? 'Published' : 'Draft';
                    displayNewValue = displayNewValue === 'true' ? 'Published' : 'Draft';
                } else if (change.field === 'featured') {
                    displayOldValue = displayOldValue ? 'Featured' : 'Not featured';
                    displayNewValue = displayNewValue ? 'Featured' : 'Not featured';
                } else if (change.field === 'pinned') {
                    displayOldValue = displayOldValue ? 'Associated with topic' : 'Not associated';
                    displayNewValue = displayNewValue ? 'Associated with topic' : 'Not associated';
                } else if (change.field === 'adminRestricted') {
                    displayOldValue = displayOldValue ? 'Admin only editing' : 'Open editing';
                    displayNewValue = displayNewValue ? 'Admin only editing' : 'Open editing';
                } else if (change.field === 'visibleState') {
                    displayOldValue = displayOldValue === 'private' ? 'Private' : 'Public';
                    displayNewValue = displayNewValue === 'private' ? 'Private' : 'Public';
                } else if (change.field === 'securityLevel') {
                    const levels = {
                        '100': 'Standard User',
                        '200': 'Club Manager', 
                        '300': 'Club Owner',
                        '400': 'Admin Only'
                    };
                    displayOldValue = levels[displayOldValue] || displayOldValue;
                    displayNewValue = levels[displayNewValue] || displayNewValue;
                }
                
                // Format content for better display
                let oldDisplay, newDisplay;
                
                if (change.field === 'body') {
                    // For body content, show more text and handle HTML/Markdown
                    const oldText = String(displayOldValue).replace(/<[^>]*>/g, '').trim(); // Strip HTML
                    const newText = String(displayNewValue).replace(/<[^>]*>/g, '').trim(); // Strip HTML
                    oldDisplay = oldText.length > 200 ? oldText.substring(0, 200) + '...' : oldText || '[Empty]';
                    newDisplay = newText.length > 200 ? newText.substring(0, 200) + '...' : newText || '[Empty]';
                } else {
                    // For other fields, use shorter display
                    oldDisplay = String(displayOldValue).length > 100 ? String(displayOldValue).substring(0, 100) + '...' : String(displayOldValue) || '[Empty]';
                    newDisplay = String(displayNewValue).length > 100 ? String(displayNewValue).substring(0, 100) + '...' : String(displayNewValue) || '[Empty]';
                }
                
                return `
                    <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #e0e0e0; border-radius: 4px;">
                        <strong>${change.fieldName}:</strong><br>
                        <div style="margin: 8px 0;">
                            <small style="color: #666; font-weight: bold;">Old:</small><br>
                            <div style="background: #f9f9f9; padding: 8px; border-radius: 4px; margin: 4px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap;">${oldDisplay}</div>
                        </div>
                        <div style="margin: 8px 0;">
                            <small style="color: #666; font-weight: bold;">New:</small><br>
                            <div style="background: #f0f8ff; padding: 8px; border-radius: 4px; margin: 4px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap;">${newDisplay}</div>
                        </div>
                    </div>
                `;
            }).join('');

            Swal.fire({
                title: `${changes.length} Change${changes.length !== 1 ? 's' : ''} Detected`,
                html: `
                    <div style="text-align: left; max-height: 300px; overflow-y: auto;">
                        ${changesHtml}
                    </div>
                `,
                icon: 'success',
                confirmButtonText: 'Cool!',
                confirmButtonColor: '#52ad39',
                width: '600px'
            });
        });

        $debugDataButton.on('click', function(e) {
            e.preventDefault();
            
            const currentData = captureCurrentArticleData();
            
            // Create masked versions for display (security)
            const maskedOriginalData = { ...originalArticleData };
            const maskedCurrentData = { ...currentData };
            
            if (maskedOriginalData && maskedOriginalData.password) {
                maskedOriginalData.password = '••••••••';
            }
            if (maskedCurrentData.password) {
                maskedCurrentData.password = '••••••••';
            }
            
            // Get dropdown debug info
            const topicId = $('#selectTopics').val();
            const topicText = $('#selectTopics option:selected').text();
            const subtopicId = $('#selectSubtopics').val();
            const subtopicText = $('#selectSubtopics option:selected').text();
            
            const debugInfo = {
                originalData: maskedOriginalData,
                currentData: maskedCurrentData,
                dropdownDebug: {
                    topicId: topicId,
                    topicText: topicText,
                    subtopicId: subtopicId,
                    subtopicText: subtopicText,
                    topicOptions: $('#selectTopics option').length,
                    subtopicOptions: $('#selectSubtopics option').length
                },
                checkboxDebug: {
                    featured_byId: $('#frm_kb_featured').length,
                    featured_byName: $('input[name="frm_kb_featured"]').length,
                    featured_checked: $('input[name="frm_kb_featured"]').is(':checked'),
                    pinned_checked: $('#kb_pinned').is(':checked'),
                    adminRestricted_checked: $('#frm_kb_admin_restricted').is(':checked')
                },
                fieldDebug: {
                    language_field_exists: $('#frm_kb_language').length,
                    language_value: $('#frm_kb_language').val(),
                    user_lang_exists: $('#user_lang').length,
                    user_lang_value: $('#user_lang').val()
                },
                editorDebug: {
                    is_markdown: $('#is_markdown').val(),
                    editor_element_exists: $('#editor').length,
                    editor_value_length: $('#editor').val() ? $('#editor').val().length : 0,
                    simplemde_exists: (() => { try { return typeof simplemde !== 'undefined'; } catch(e) { return false; } })(),
                    window_simplemde_exists: typeof window.simplemde !== 'undefined',
                    simplemde_value_length: (() => { 
                        try { 
                            return (typeof simplemde !== 'undefined' && simplemde && simplemde.value) ? simplemde.value().length : 0; 
                        } catch(e) { 
                            return 0; 
                        } 
                    })(),
                    window_simplemde_value_length: (typeof window.simplemde !== 'undefined' && window.simplemde && window.simplemde.value) ? window.simplemde.value().length : 0,
                    trumbowyg_exists: typeof $('#editor').trumbowyg === 'function',
                    current_body_length: currentData.body ? currentData.body.length : 0,
                    original_body_length: originalArticleData && originalArticleData.body ? originalArticleData.body.length : 0
                }
            };
            
            console.log('Debug Info:', debugInfo);
            
            Swal.fire({
                title: 'Debug Data',
                html: `
                    <div style="text-align: left; max-height: 400px; overflow-y: auto;">
                        <h5>Current Data:</h5>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${JSON.stringify(currentData, null, 2)}</pre>
                        
                        <h5>Original Data:</h5>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${JSON.stringify(originalArticleData, null, 2)}</pre>
                        
                        <h5>Dropdown Debug:</h5>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${JSON.stringify(debugInfo.dropdownDebug, null, 2)}</pre>
                        
                        <h5>Checkbox Debug:</h5>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${JSON.stringify(debugInfo.checkboxDebug, null, 2)}</pre>
                        
                        <h5>Field Debug:</h5>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${JSON.stringify(debugInfo.fieldDebug, null, 2)}</pre>
                        
                        <h5>Editor Debug:</h5>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">${JSON.stringify(debugInfo.editorDebug, null, 2)}</pre>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'OK',
                width: '800px'
            });
        });
    }

    // Expose functions globally for access from openKB.js
    window.captureCurrentArticleData = captureCurrentArticleData;
    window.detectArticleChanges = detectArticleChanges;
    window.generateChangelogSummaries = generateChangelogSummaries;
    window.originalArticleData = originalArticleData;

}); //end of document.ready

// FUNCTIONS ---------------------------------------------------------------------------------------------------------
var expanded = false;
var allSubtopics = [];

//called only once during page load
async function getAllTopicsData() {
    const articleTopic = $('#selectTopics').data('topic');
    const userLang = $('#user_lang').val();
    
    return new Promise((resolve, reject) => {
        $.ajax({
            method: "GET",
            url: $("#app_context").val() + `/api/topics`,
        })
        .done(function (res) {
            if (res && res.data) {
                //$("#selectTopics option").remove();

                res.data.map((t) => {
                    $("#selectTopics").append(`
                    <option value="${t._id}" ${articleTopic === t._id ? 'selected': ''} >${t.topic} ${t.topicTranslations && t.topic !== t.topicTranslations[userLang] ? '('+t.topicTranslations[userLang]+')' : ''}</option>
                `);

                    if (t.subtopics) {
                        t.subtopics.map((s, i) => {
                            allSubtopics.push({
                                id: s.id,
                                topic: t._id,
                                subtopic: s.subtopic,
                                subtopicTranslations: s.subtopicTranslations
                            });
                        });
                    }
                });

                //now set values based on data attribute from dropdown
                const selectTopics = $("#selectTopics");
                const selectSubTopics = $("#selectSubtopics");
                selectTopics.val(selectTopics.data("topic"));

                //set subtopics
                setSubtopicSelect(selectTopics.data("topic"));
                
                resolve(res);
            } else {
                resolve(null);
            }
        })
        .fail(function (error) {
            console.log("fail query ---------------- ", error);
            reject(error);
        });
    });
}

function setSubtopicSelect(topic = null) {
    const userLang = $('#user_lang').val() ?? 'en';

    if (topic) {
        //get values from allSubtopics based on respective topic
        $("#selectSubtopics option").remove();
        let subtopicSelect = $("#selectSubtopics");
        
        allSubtopics.map((s) => {
            if (s.topic == topic) {
                subtopicSelect.append(`
                    <option value="${s.id}">${s.subtopic} ${s.subtopicTranslations && s.subtopic !== s.subtopicTranslations[userLang] ? '('+s.subtopicTranslations[userLang]+')' : ''}</option>
                `);
            }
        });
       
        //then set selected based on data attribute
        subtopicSelect.val(subtopicSelect.data("subtopic"));
    }
}

/**
 * Capture current article data from form fields
 * @returns {Object} Current article data
 */
function captureCurrentArticleData() {
    // Get body content based on editor type
    let bodyContent = '';
    const isMarkdown = $('#is_markdown').val() === 'true';
    
    console.log('Capturing body content - isMarkdown:', isMarkdown);
    
    if (isMarkdown) {
        // For markdown editor, try to get content from simplemde or fallback to textarea
        console.log('Markdown mode - checking simplemde...');
        
        // Try multiple ways to access simplemde safely
        let simplemdeInstance = null;
        try {
            if (typeof simplemde !== 'undefined' && simplemde) {
                simplemdeInstance = simplemde;
                console.log('Found simplemde in local scope');
            } else if (typeof window.simplemde !== 'undefined' && window.simplemde) {
                simplemdeInstance = window.simplemde;
                console.log('Found simplemde in window scope');
            }
        } catch (e) {
            console.log('Error accessing simplemde:', e);
        }
        
        if (simplemdeInstance && typeof simplemdeInstance.value === 'function') {
            try {
                bodyContent = simplemdeInstance.value();
                console.log('Got content from simplemde:', bodyContent.substring(0, 100) + '...');
            } catch (e) {
                console.log('Error getting simplemde value:', e);
                bodyContent = $('#editor').val() || '';
                console.log('Got content from textarea fallback:', bodyContent.substring(0, 100) + '...');
            }
        } else {
            bodyContent = $('#editor').val() || '';
            console.log('Got content from textarea fallback:', bodyContent.substring(0, 100) + '...');
        }
    } else {
        // For WYSIWYG editor, get HTML content
        console.log('WYSIWYG mode - checking trumbowyg...');
        console.log('Editor element exists:', $('#editor').length);
        console.log('Trumbowyg function exists:', typeof $('#editor').trumbowyg === 'function');
        
        if ($('#editor').length && typeof $('#editor').trumbowyg === 'function') {
            try {
                bodyContent = $('#editor').trumbowyg('html');
                console.log('Got content from trumbowyg:', bodyContent.substring(0, 100) + '...');
            } catch (e) {
                console.log('Trumbowyg error:', e);
                bodyContent = $('#editor').val() || '';
                console.log('Got content from textarea fallback:', bodyContent.substring(0, 100) + '...');
            }
        } else {
            bodyContent = $('#editor').val() || '';
            console.log('Got content from textarea fallback:', bodyContent.substring(0, 100) + '...');
        }
    }
    
    console.log('Final body content length:', bodyContent.length);

    // Get topic and subtopic text values instead of IDs
    const topicId = $('#selectTopics').val() || '';
    const subtopicId = $('#selectSubtopics').val() || '';
    const topicText = topicId ? $('#selectTopics option:selected').text() || '' : '';
    const subtopicText = subtopicId ? $('#selectSubtopics option:selected').text() || '' : '';

    return {
        title: $('#frm_kb_title').val() || '',
        body: bodyContent,
        keywords: $('#frm_kb_keywords').val() || '',
        regionalVisibility: $('#frm_kb_regional_visibility').val() || 'Global',
        published: $('#frm_kb_published').val() || 'false',
        pinnedTopic: topicText,
        pinnedTopicId: topicId, // Keep ID for comparison if needed
        pinnedSubtopic: subtopicText,
        pinnedSubtopicId: subtopicId, // Keep ID for comparison if needed
        securityLevel: $('#frm_kb_security_level').val() || '100',
        visibleState: $('#frm_kb_visible_state').val() || 'public',
        password: $('#frm_kb_password').val() || '',
        featured: $('input[name="frm_kb_featured"]').is(':checked'),
        pinned: $('#kb_pinned').is(':checked'),
        adminRestricted: $('#frm_kb_admin_restricted').is(':checked'),
        isMarkdown: $('#editor-switch').is(':checked'),
        language: $('#frm_kb_language').val() || $('#user_lang').val() || 'en'
    };
}

/**
 * Detect changes between original and current article data
 * @param {Object} originalData - Original article data
 * @param {Object} currentData - Current article data
 * @returns {Array} Array of change objects
 */
function detectArticleChanges(originalData, currentData) {
    const changes = [];
    
    // Define field mappings with human-readable names
    const fieldMappings = {
        title: 'Article Title',
        body: 'Article Content',
        keywords: 'Keywords',
        regionalVisibility: 'Regional Visibility',
        published: 'Publication Status',
        pinnedTopic: 'Topic Assignment',
        pinnedSubtopic: 'Subtopic Assignment',
        securityLevel: 'Security Level',
        visibleState: 'Visibility State',
        password: 'Password Protection',
        featured: 'Featured Status',
        pinned: 'Topic Association',
        adminRestricted: 'Admin Restriction',
        isMarkdown: 'Editor Type',
        language: 'Language'
        // Note: pinnedTopicId and pinnedSubtopicId are excluded from change detection
        // as they are just IDs - we use the text versions instead
    };

    // Check each field for changes
    Object.keys(fieldMappings).forEach(field => {
        let oldValue = originalData[field];
        let newValue = currentData[field];
        
        // Special handling for regional visibility - normalize empty values to "Global"
        if (field === 'regionalVisibility') {
            oldValue = oldValue || 'Global';
            newValue = newValue || 'Global';
        }
        
        // Handle different data types appropriately
        let hasChanged = false;
        
        if (typeof oldValue === 'boolean' || typeof newValue === 'boolean') {
            hasChanged = Boolean(oldValue) !== Boolean(newValue);
        } else {
            // Convert to strings for comparison and trim whitespace
            const oldStr = String(oldValue || '').trim();
            const newStr = String(newValue || '').trim();
            hasChanged = oldStr !== newStr;
        }
        
        if (hasChanged) {
            let oldText, newText;
            
            // Special handling for password field - don't send actual passwords to AI
            if (field === 'password') {
                const hadPassword = oldValue && oldValue.trim() !== '';
                const hasPassword = newValue && newValue.trim() !== '';
                
                if (!hadPassword && hasPassword) {
                    oldText = 'Password Protection: No password';
                    newText = 'Password Protection: Password has been set';
                } else if (hadPassword && !hasPassword) {
                    oldText = 'Password Protection: Password protected';
                    newText = 'Password Protection: Password has been removed';
                } else if (hadPassword && hasPassword) {
                    oldText = 'Password Protection: Password protected';
                    newText = 'Password Protection: Password has been updated';
                } else {
                    // This shouldn't happen if hasChanged is true, but just in case
                    oldText = formatValueForAI(field, oldValue);
                    newText = formatValueForAI(field, newValue);
                }
            } else {
                oldText = formatValueForAI(field, oldValue);
                newText = formatValueForAI(field, newValue);
            }
            
            changes.push({
                field: field,
                fieldName: fieldMappings[field],
                oldValue: oldValue,
                newValue: newValue,
                oldText: oldText,
                newText: newText
            });
        }
    });
    
    return changes;
}

/**
 * Format field values for AI processing
 * @param {string} field - Field name
 * @param {*} value - Field value
 * @returns {string} Formatted text for AI
 */
function formatValueForAI(field, value) {
    switch (field) {
        case 'title':
            return `Title: "${value || 'No title'}"`;
        case 'body':
            return `Content: ${value || 'No content'}`;
        case 'keywords':
            return `Keywords: [${value || 'No keywords'}]`;
        case 'regionalVisibility':
            return `Regional Visibility: ${value || 'Global'}`;
        case 'published':
            return `Publication Status: ${value === 'true' ? 'Published' : 'Draft'}`;
        case 'pinnedTopic':
            return `Topic: ${value || 'No topic assigned'}`;
        case 'pinnedSubtopic':
            return `Subtopic: ${value || 'No subtopic assigned'}`;
        case 'securityLevel':
            const levels = {
                '100': 'Standard User',
                '200': 'Club Manager', 
                '300': 'Club Owner',
                '400': 'Admin Only'
            };
            return `Security Level: ${levels[value] || value}`;
        case 'visibleState':
            return `Visibility: ${value === 'private' ? 'Private' : 'Public'}`;
        case 'password':
            return `Password Protection: ${value ? 'Password protected' : 'No password'}`;
        case 'featured':
            return `Featured Status: ${value ? 'Featured' : 'Not featured'}`;
        case 'pinned':
            return `Topic Association: ${value ? 'Associated with topic' : 'Not associated'}`;
        case 'adminRestricted':
            return `Admin Restriction: ${value ? 'Admin only editing' : 'Open editing'}`;
        case 'isMarkdown':
            return `Editor Type: ${value ? 'Markdown mode' : 'WYSIWYG mode'}`;
        case 'language':
            return `Language: ${value || 'English'}`;
        default:
            return `${field}: ${value || 'Not set'}`;
    }
}

/**
 * Generate AI-powered changelog summary for all changes (consolidated approach)
 * @param {Array} changes - Array of change objects
 * @returns {Promise<Object>} Consolidated AI summary object
 */
async function generateChangelogSummaries(changes) {
    if (!changes || changes.length === 0) {
        return {
            success: false,
            error: 'No changes to summarize'
        };
    }

    console.log(`Generating consolidated AI summary for ${changes.length} changes...`);
    
    try {
        const response = await axios.post($("#app_context").val() + '/api/ai/summarize-changelog', {
            changes: changes
        });
        
        console.log('Consolidated AI summary generation completed:', response.data);
        
        return {
            success: true,
            summary: response.data.data,
            changeCount: response.data.changeCount || changes.length
        };
        
    } catch (error) {
        console.error('Failed to generate consolidated summary:', error);
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Unknown error',
            changeCount: changes.length
        };
    }
}


