// Global variables
let allSubtopics = [];

// Video processing is now handled by the shared VideoRenderer module
// Maintain backward compatibility by aliasing the global processedVideos



$(document).ready(async function () {
    await initTranslator();
    
    function activateAllVideoPlayers() {
        const isWysiwygMode = !$('#is_markdown').val() || $('#is_markdown').val() === 'false';
        
        if (isWysiwygMode) {
            const trumbowygEditor = $('.trumbowyg-editor');
            if (trumbowygEditor.length > 0) {
                const videoContainers = trumbowygEditor.find('.video-embed-container');
                
                // Clean up conflicting embedded scripts
                trumbowygEditor.find('.video-embed-container script').remove();
                
                if (videoContainers.length > 0) {
                    videoContainers.each(function(index) {
                        const container = $(this);
                        const placeholder = container.find('.video-loading-placeholder');
                        const existingVideo = container.find('video');
                        const videoUrl = container.attr('data-video-url');
                        
                        // Skip if already processed in this session AND video is working
                        if (videoUrl && processedVideos.has(videoUrl) && placeholder.length === 0 && existingVideo.length > 0 && !existingVideo[0].error) {
                            return;
                        }
                        
                        // Remove broken videos from old script system (but not videos with blob URLs from our system)
                        if (existingVideo.length > 0 && !existingVideo.attr('data-blob-url') && videoUrl && !processedVideos.has(videoUrl)) {
                            existingVideo.remove();
                        }
                        
                        // If no placeholder exists, create one (this handles page reload scenario)
                        if (placeholder.length === 0) {
                            // If there's an existing video with invalid blob URL, remove it first
                            if (existingVideo.length > 0 && existingVideo.attr('data-blob-url')) {
                                existingVideo.remove();
                            }
                            
                            const newPlaceholder = $('<div>', {
                                class: 'video-loading-placeholder',
                                html: 'Loading video...',
                                css: {
                                    padding: '1rem',
                                    color: '#666',
                                    textAlign: 'center'
                                }
                            });
                            container.append(newPlaceholder);
                        }
                        
                        const url = container.attr('data-video-url');
                        const type = container.attr('data-video-type') || 'mp4';
                        
                        if (url) {
                            setTimeout(() => {
                                const currentPlaceholder = container.find('.video-loading-placeholder');
                                if (currentPlaceholder.length === 0) {
                                    return;
                                }
                                
                                const loadingMsg = $('<div>', {
                                    html: 'Loading video...',
                                    'data-video-loading': 'true',
                                    css: {
                                        padding: '1rem',
                                        color: '#666',
                                        textAlign: 'center'
                                    }
                                });
                                currentPlaceholder.replaceWith(loadingMsg);
                                fetch(url, { 
                                    method: 'GET', 
                                    credentials: 'include',
                                    headers: {
                                        'Accept': 'video/*'
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                    }
                                    return response.blob();
                                })
                                .then(blob => {
                                    if (loadingMsg.parent().length === 0) {
                                        URL.revokeObjectURL(URL.createObjectURL(blob));
                                        return;
                                    }
                                    
                                    const blobUrl = URL.createObjectURL(blob);
                                    
                                    const video = $('<video>', {
                                        controls: true,
                                        preload: 'metadata',
                                        'data-video-url': url,
                                        'data-blob-url': blobUrl,
                                        css: {
                                            width: '100%',
                                            maxWidth: '800px',
                                            margin: '1rem 0',
                                            display: 'block'
                                        }
                                    });
                                    
                                    video.attr('src', blobUrl);
                                    
                                    video.on('remove', function() {
                                        URL.revokeObjectURL(blobUrl);
                                    });
                                    
                                    $(window).on('beforeunload', function() {
                                        URL.revokeObjectURL(blobUrl);
                                    });
                                    
                                    loadingMsg.replaceWith(video);
                                    
                                    // Mark this video as processed in the session
                                    if (url) {
                                        processedVideos.add(url);
                                    }
                                })
                                .catch(error => {
                                    if (loadingMsg.parent().length === 0) {
                                        return;
                                    }
                                    
                                    const errorMsg = $('<div>', {
                                        html: 'Failed to load video',
                                        css: {
                                            padding: '1rem',
                                            color: '#666',
                                            textAlign: 'center',
                                            fontStyle: 'italic'
                                        }
                                    });
                                    loadingMsg.replaceWith(errorMsg);
                                });
                            }, index * 200);
                        }
                    });
                }
                
                // Legacy file-to-proxy elements
                const proxyElements = trumbowygEditor.find('.file-to-proxy');
                
                if (proxyElements.length > 0) {
                    proxyElements.each(function(index) {
                        const element = $(this);
                        const url = element.attr('data-url');
                        if (url) {
                            setTimeout(() => {
                                if (element.parent().length === 0) {
                                    return;
                                }
                                
                                const urlWithoutQuery = url.split('?')[0];
                                const fileExt = urlWithoutQuery.split('.').pop().toLowerCase();
                                const isVideo = ['mp4', 'mov', 'webm', 'ogg', 'avi', 'mkv', 'm4v'].includes(fileExt);
                                
                                if (isVideo) {
                                    const video = $('<video>', {
                                        controls: true,
                                        preload: 'metadata',
                                        'data-video-url': url,
                                        'data-legacy-video': 'true',
                                        css: {
                                            width: '100%',
                                            maxWidth: '800px',
                                            margin: '1rem 0',
                                            display: 'block'
                                        }
                                    });
                                    
                                    fetch(url, { 
                                        method: 'GET', 
                                        credentials: 'include',
                                        headers: {
                                            'Accept': 'video/*'
                                        }
                                    })
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                        }
                                        return response.blob();
                                    })
                                    .then(blob => {
                                        if (element.parent().length === 0) {
                                            URL.revokeObjectURL(URL.createObjectURL(blob));
                                            return;
                                        }
                                        
                                        const blobUrl = URL.createObjectURL(blob);
                                        video.attr('src', blobUrl);
                                        video.attr('data-blob-url', blobUrl);
                                        
                                        video.on('remove', function() {
                                            URL.revokeObjectURL(blobUrl);
                                        });
                                        
                                        $(window).on('beforeunload', function() {
                                            URL.revokeObjectURL(blobUrl);
                                        });
                                        
                                        element.replaceWith(video);
                                    })
                                    .catch(error => {
                                        if (element.parent().length === 0) {
                                            return;
                                        }
                                        
                                        element.replaceWith($('<div>', {
                                            html: 'Failed to load video',
                                            css: {
                                                padding: '1rem',
                                                color: '#666',
                                                textAlign: 'center',
                                                fontStyle: 'italic'
                                            }
                                        }));
                                    });
                                }
                            }, (videoContainers.length + index) * 200);
                        }
                    });
                }
                
                if (videoContainers.length === 0 && proxyElements.length === 0) {
                    window.videoActivationAttempts = (window.videoActivationAttempts || 0) + 1;
                    
                    if (window.videoActivationAttempts < 5) {
                        setTimeout(activateAllVideoPlayers, 1000);
                    } else {
                        window.videoActivationAttempts = 0;
                    }
                } else {
                    window.videoActivationAttempts = 0;
                }
            } else {
                setTimeout(activateAllVideoPlayers, 1000);
            }
        } else {
            // Markdown mode - process videos in the preview container
            const previewContainer = $('#preview');
            if (previewContainer.length > 0) {
                // Process both .video-embed-container (from WYSIWYG) and .file-to-proxy elements
                const videoContainers = previewContainer.find('.video-embed-container');
                const proxyElements = previewContainer.find('.file-to-proxy');
                
                // Handle .video-embed-container elements (videos from WYSIWYG mode)
                if (videoContainers.length > 0) {
                    videoContainers.each(function(index) {
                        const container = $(this);
                        const existingVideo = container.find('video');
                        const videoUrl = container.attr('data-video-url');
                        
                        // Check if this video needs processing using global session tracking
                        if (videoUrl && existingVideo.length > 0) {
                            // Check if video is already properly processed in this session
                            const isAlreadyProcessed = processedVideos.has(videoUrl);
                            const hasValidBlobUrl = existingVideo.attr('data-blob-url') && existingVideo.attr('src');
                            
                            // Only process if not already done in this session OR if blob URL seems invalid
                            if (!isAlreadyProcessed || !hasValidBlobUrl || existingVideo[0].error) {
                            setTimeout(() => {
                                fetch(videoUrl, { 
                                    method: 'GET', 
                                    credentials: 'include',
                                    headers: {
                                        'Accept': 'video/*'
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                    }
                                    return response.blob();
                                })
                                .then(blob => {
                                    if (existingVideo.parent().length === 0) {
                                        URL.revokeObjectURL(URL.createObjectURL(blob));
                                        return;
                                    }
                                    
                                    // Revoke old blob URL
                                    const oldBlobUrl = existingVideo.attr('data-blob-url');
                                    if (oldBlobUrl) {
                                        URL.revokeObjectURL(oldBlobUrl);
                                    }
                                    
                                    const blobUrl = URL.createObjectURL(blob);
                                    existingVideo.attr('src', blobUrl);
                                    existingVideo.attr('data-blob-url', blobUrl);
                                    existingVideo.attr('data-markdown-video', 'true');
                                    
                                    existingVideo.on('remove', function() {
                                        URL.revokeObjectURL(blobUrl);
                                    });
                                    
                                    $(window).on('beforeunload', function() {
                                        URL.revokeObjectURL(blobUrl);
                                    });
                                    
                                    // Update session tracking
                                    processedVideos.add(videoUrl);
                                })
                                .catch(error => {
                                    console.error('Failed to refresh video in markdown preview:', error);
                                    existingVideo.replaceWith($('<div>', {
                                        html: 'Failed to load video',
                                        css: {
                                            padding: '1rem',
                                            color: '#666',
                                            textAlign: 'center',
                                            fontStyle: 'italic'
                                        }
                                    }));
                                });
                            }, index * 200);
                            }
                        }
                    });
                }
                
                // Process .file-to-proxy elements (legacy or new markdown videos)
                if (proxyElements.length > 0) {
                    proxyElements.each(function(index) {
                        const element = $(this);
                        const url = element.attr('data-url');
                        
                        // Skip if already processed in this session
                        if (url && processedVideos.has(url) && !element.text().includes('Loading media...')) {
                            return;
                        }
                        
                        if (url) {
                            setTimeout(() => {
                                if (element.parent().length === 0 || !element.text().includes('Loading media...')) {
                                    return;
                                }
                                
                                const urlWithoutQuery = url.split('?')[0];
                                const fileExt = urlWithoutQuery.split('.').pop().toLowerCase();
                                const isVideo = ['mp4', 'mov', 'webm', 'ogg', 'avi', 'mkv', 'm4v'].includes(fileExt);
                                
                                if (isVideo) {
                                    const video = $('<video>', {
                                        controls: true,
                                        preload: 'metadata',
                                        'data-video-url': url,
                                        'data-markdown-video': 'true',
                                        css: {
                                            width: '100%',
                                            maxWidth: '800px',
                                            margin: '1rem 0',
                                            display: 'block'
                                        }
                                    });
                                    
                                    fetch(url, { 
                                        method: 'GET', 
                                        credentials: 'include',
                                        headers: {
                                            'Accept': 'video/*'
                                        }
                                    })
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                        }
                                        return response.blob();
                                    })
                                    .then(blob => {
                                        if (element.parent().length === 0) {
                                            URL.revokeObjectURL(URL.createObjectURL(blob));
                                            return;
                                        }
                                        
                                        const blobUrl = URL.createObjectURL(blob);
                                        video.attr('src', blobUrl);
                                        video.attr('data-blob-url', blobUrl);
                                        
                                        video.on('remove', function() {
                                            URL.revokeObjectURL(blobUrl);
                                        });
                                        
                                        $(window).on('beforeunload', function() {
                                            URL.revokeObjectURL(blobUrl);
                                        });
                                        
                                        element.replaceWith(video);
                                        
                                        // Mark this video as processed in the session
                                        if (url) {
                                            processedVideos.add(url);
                                        }
                                    })
                                    .catch(error => {
                                        if (element.parent().length === 0) {
                                            return;
                                        }
                                        
                                        element.replaceWith($('<div>', {
                                            html: 'Failed to load video',
                                            css: {
                                                padding: '1rem',
                                                color: '#666',
                                                textAlign: 'center',
                                                fontStyle: 'italic'
                                            }
                                        }));
                                    });
                                }
                            }, index * 200);
                        }
                    });
                }
            }
        }
    }
    
    // Run video activation after a delay to ensure editor is fully loaded
    setTimeout(activateAllVideoPlayers, 500);
    
    // Also run video activation when switching between markdown and WYSIWYG modes
    $(document).on('change', '#editor-switch, .ph-switch-input', function() {
        // When switching modes, videos may need blob URL refresh due to context change
        // Clear session tracking for videos that might have invalid blob URLs
        const currentVideos = $('video[data-blob-url]');
        currentVideos.each(function() {
            const video = this;
            const videoUrl = $(video).attr('data-video-url');
            
            // If video has error or blob URL issues, remove from processed tracking
            if (video.error || video.networkState === 3) {
                if (videoUrl && processedVideos.has(videoUrl)) {
                    processedVideos.delete(videoUrl);
                }
            }
        });
        
        setTimeout(activateAllVideoPlayers, 1000);
    });
    
    
    // Make the activateAllVideoPlayers function available globally
    window.activateAllVideoPlayers = activateAllVideoPlayers;
    
    // Make the processedVideos Set available globally for upload plugin
    window.processedVideos = processedVideos;
    
    // Utility function to clear video session tracking if needed
    window.clearVideoSessionTracking = function() {
        processedVideos.clear();
    };
    

    const userLang = $('#user_lang').val() ?? 'en';
    const is_pseudoadmin = $('#is_pseudoadmin').val() ?? 'false';
    const countryDropdownData = $('#selectCountryDropdown').data('options');
    const pseudoadminCountries = countryDropdownData ? countryDropdownData.split(',').join(', ') : '';
    const articleId = $('#article_id').val();
    
    
    const selectPlaceholder = is_pseudoadmin === 'true' && articleId == '' ? pseudoadminCountries : t('Global');

    // Store original article data for change tracking
    let originalArticleData = null;
    
    // Capture original data on page load for existing articles
    // Delay capture to ensure dropdowns are populated
    if (articleId && articleId !== '') {
        setTimeout(() => {
            originalArticleData = captureCurrentArticleData();
            window.originalArticleData = originalArticleData; // Make sure it's available globally
        }, 1000); // Wait 1 second for dropdowns to be populated
    }
    

    // Send 'hide modal' on page load/reload...
    // Since our app runs in an iframe, send bootstrap model 'hide|show' events
    // to the parent iframe to also apply a model overlay background...
    window.top.postMessage("modalHide", "*");



    let settingsDiv = $("#settingsModal");
    let historyDiv = $("#articleHistory");


    //hide the settings first
    settingsDiv.hide();
    historyDiv.hide();

    $("#btnSettings").on("click", function () {
        if (settingsDiv.is(":hidden")) {
            $("#settingsModal").show(50);
            $("#articleHistory").hide();
        } else {
            $("#settingsModal").hide();
        }
    });
    $("#btnHistory").on("click", function () {
        if (historyDiv.is(":hidden")) {
            $("#articleHistory").show(50);
            $("#settingsModal").hide();
        } else {
            $("#articleHistory").hide();
        }
	});
	
	$('#navSettingsDashboard').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings';
	});
	$('#navSettingsArticles').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/articles';
	});
	$('#navSettingsConfiguration').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/configuration';
	});

    $("#btnCloseModal").on("click", function () {
        $("#settingsModal").hide();
    });

    $("#btnCloseHistory").on("click", function () {
        $("#articleHistory").hide();
    });

    //get all topics to be used in Topic and Subtopic dropdown
    getAllTopicsData().then(() => {
        // Capture original data after topics are loaded
        if (articleId && articleId !== '' && !originalArticleData) {
            setTimeout(() => {
                originalArticleData = captureCurrentArticleData();
                window.originalArticleData = originalArticleData; // Make sure it's available globally
            }, 500);
        }
    }).catch((error) => {
        // Still capture original data even if topics failed to load
        if (articleId && articleId !== '' && !originalArticleData) {
            setTimeout(() => {
                originalArticleData = captureCurrentArticleData();
                window.originalArticleData = originalArticleData; // Make sure it's available globally
            }, 1000);
        }
    });

    // Country selector click handlers for the new card-based design ---------------------------------------------------------------
    
    // Direct click handler for overSelect element
    $(document).on("click", "#overSelect", function (e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Check if the select dropdown is disabled
        if($('#selectCountryDropdown').is(':disabled') || $('#overSelect').hasClass('disabled')) {
            return false;
        }
        
        var checkboxes = document.getElementById("countryCheckboxes");
        if (!expanded) {
            checkboxes.style.display = "block";
            //adjust width to match the select dropdown
            $("#countryCheckboxes").css("width", $("#selectCountryDropdown").outerWidth());
            expanded = true;
        } else {
            checkboxes.style.display = "none";
            expanded = false;
        }
    });

    // Handle clicks on country checkboxes - don't hide dropdown
    $(document).on("click", ".countryLabel, .countryCheck", function (e) {
        e.stopPropagation();
        $("#countryCheckboxes").css("width", $("#selectCountryDropdown").outerWidth());
        expanded = true;
    });

    // Close dropdown when clicking outside
    $(document).on("click", function (e) {
        if (expanded && !$(e.target).closest("#countryCheckboxes, #overSelect, #selectCountryDropdown").length) {
            $("#countryCheckboxes").css("display", "none");
            expanded = false;
        }
    });

    //check if Article is Pinned
    const topicCheckbox = document.getElementById('kb_pinned');
    const topicSelectionGroup = document.querySelector('.ph-topic-selection-group');

    const toggleTopicSelectionGroupVisibility = () => {
        if (!topicCheckbox || !topicSelectionGroup) return; // Early exit if elements not found
        topicSelectionGroup.style.display = topicCheckbox.checked ? 'block' : 'none';
        if (!topicCheckbox.checked) {
            // Clear selections when hiding
            const topicSelect = document.getElementById('selectTopics');
            const subtopicSelect = document.getElementById('selectSubtopics');
            if (topicSelect) topicSelect.value = '';
            if (subtopicSelect) subtopicSelect.value = '';
            selectTopicsChange(); // Call to clear subtopics and reset country visibility
        }
    };

    // Set initial state
    toggleTopicSelectionGroupVisibility();

    // Add event listener for changes
    if (topicCheckbox) {
        topicCheckbox.addEventListener('change', toggleTopicSelectionGroupVisibility);
    }
    // -------------------------------------------------------------------------------------------

    if ($("#frm_kb_title").val().length > 5) {
        $("#generate_permalink_from_title").removeClass("disabled");
    } else {
        $("#generate_permalink_from_title").addClass("disabled");
    }

    initializePermalinkControls();

    setAvailableCountries();

    //event when checked one of country checkboxes
    
    $("#countryCheckboxes").on("change", '.countryCheck', function () {
        const selectedCountries = $("#frm_kb_regional_visibility").val() !== '' ? $("#frm_kb_regional_visibility").val().split(",") : [];

        let country = $(this).data("country");

        if ($(this).is(":checked")) {
                            selectedCountries.push(country);
            } else {
                if(is_pseudoadmin !== 'true') {
                    selectedCountries.splice(selectedCountries.indexOf(country), 1);
                } else {
                    if(selectedCountries.length <= 1) {
                        $(this).prop('checked', true);
                        Swal.fire("Error", t("Pseudoadmin cannot set article to global visibility"), "error");
                    }    
                    else {
                        selectedCountries.splice(selectedCountries.indexOf(country), 1);
                    }
                }
            }

            selectedCountries.filter((s) => s !== "");

        if (selectedCountries.length > 0) {
            $("#optionSelectCountry").text(selectedCountries.join(", "));
            $("#frm_kb_regional_visibility").val(selectedCountries.join(","));
        } else {
            $("#optionSelectCountry").text(selectPlaceholder);
            $("#frm_kb_regional_visibility").val("");
        }
    });

    $("#selectTopics").on("change", selectTopicsChange);

    async function selectTopicsChange() {
        const subtopicSelect = $("#selectSubtopics");
        $(subtopicSelect).children('option').remove();
        subtopicSelect.append(`
            <option value="">Select Subtopic</option>
        `);

        if ($("#selectTopics").val() !== '') {
            const data = await $.ajax({
                method: "GET",
                url: $("#app_context").val() + '/topic/' + $("#selectTopics").val() + '/details',
            })

            const topicDetails = data.data;
            //subtopics
            
            
            topicDetails.subtopics.forEach((s) => {
                subtopicSelect.append(`
                    <option value="${s.id}">${s.subtopic} ${s.subtopicTranslations && s.subtopic !== s.subtopicTranslations[userLang] ? '('+s.subtopicTranslations[userLang]+')' : ''}</option>
                `);
            });

            const countriesData = $("#selectCountryDropdown").data("possibleOptions")
            const countryOptions = (topicDetails.topic_regional_visibility === '' || topicDetails.topic_regional_visibility === undefined) ? countriesData : topicDetails.topic_regional_visibility;
            $("#selectCountryDropdown").data("options", countryOptions);
            //$("#frm_kb_regional_visibility").val('');

            let selectedCountries = $("#frm_kb_regional_visibility").val().split(/(?:,)\s*/i);
            
            selectedCountries = selectedCountries.filter((s) => {
                return countryOptions.split(/(?:,)\s*/i).includes(s);
            });

            $("#frm_kb_regional_visibility").val(selectedCountries.join(','));

            $("#optionSelectCountry").text(selectedCountries.length > 0 ? selectedCountries.join(', ') : selectPlaceholder);
            setAvailableCountries()
        } else {
            const originalVisibility = $('#original-visibility').val() || '';
            $("#frm_kb_regional_visibility").val(originalVisibility);
            $("#optionSelectCountry").text(originalVisibility ? originalVisibility.split(',').join(', ') : selectPlaceholder);
            $("#selectCountryDropdown").data("options", $("#selectCountryDropdown").data('possible-options'));
            setAvailableCountries()
        }
    }

    $("#btnCopyArticleLink").on("click", function () {
        let permaLink = $("#frm_kb_permalink").val();
        let routeName = $(this).data("routename");
        let articleId = $(this).data("articleid");
        let pageUrl = `${routeName}`;

        //permalink must prevail
        if (permaLink.length > 0) {
            pageUrl += `/${permaLink}`;
        } else {
            pageUrl += `/${articleId}`;
        }

        navigator.clipboard.writeText(pageUrl);

        //show toast
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: false,
            didOpen: (toast) => {
                toast.addEventListener("mouseenter", Swal.stopTimer);
                toast.addEventListener("mouseleave", Swal.resumeTimer);
            },
            showClass: {
                backdrop: "swal2-noanimation", // disable backdrop animation
                popup: "", // disable popup animation
            },
        });

        Toast.fire({
            icon: "success",
            title: t("Article URL copied successfully"),
        });
    });

    //to adjust height
    let tokenf = $(".tokenfield");
	tokenf.removeClass("form-control");



    function setAvailableCountries() {
        // about countries multi-select ------------------------------------------------------------------------------------
       //setup countries dropdown
       const selectedCountries = [];
       const regionalValue = $("#frm_kb_regional_visibility").val();
       const countriesData = $("#selectCountryDropdown").data("options");
       
       // Clear the container first to prevent duplication
       $("#countryCheckboxes").html('');
       
       let countries = countriesData.split(",");
    
       for (let i = 0; i < countries.length; i++) {
           let countryId = countries[i].replace(/\s/g, "");
           $("#countryCheckboxes").append(`
               <label for="dropCheck-${countryId}" class="countryLabel" id="countryLabel-${countryId}">
                   <input type="checkbox" id="dropCheck-${countryId}"
                   class="mr-2 countryCheck" data-country="${countries[i]}" ${is_pseudoadmin === 'true' && articleId == '' ? 'checked="checked"': ''}  />${countries[i]}
               </label>
           `);
       }
   

       if (regionalValue.length > 0) {
           $("#optionSelectCountry").text(regionalValue.split(/(?:,)\s*/i).join(', '));
   
           //check if multiple countries
           if (regionalValue.includes(",")) {
               let regionals = regionalValue.split(",");
               regionals.forEach((r) => {
                   let countryId = r.replace(/\s/g, "").trim();
   
                   //checks the checkbox
                   $("input[id='dropCheck-" + countryId + "']").prop("checked", true);
                   selectedCountries.push(r.trim());
               });
           } else {
               let countryId = regionalValue.replace(/\s/g, "");
   
               $(`#dropCheck-${countryId}`).prop("checked", true);
               selectedCountries.push(regionalValue);
           }
       } else {
           $("#optionSelectCountry").text(selectPlaceholder);
       }
   }

    $(document).on('click', '#btnDeleteArticle', function (e) {
        if ($(this).is('[disabled]')) return;

        e.preventDefault();

        const deleteUrl = $(this).attr('href');

        Swal.fire({
            title: t('Are you sure?'),
            text: t('This action cannot be undone and will permanently delete the article.'),
            icon: 'warning',
            showCancelButton: true,
            customClass: {
                confirmButton: 'btn btn-danger waves-effect ph-btn',
                cancelButton: 'btn btn-primary waves-effect ph-btn',
            },
            buttonsStyling: false,
            focusConfirm: false,
            confirmButtonText: t('Yes, delete it!'),
            cancelButtonText: t('Cancel'),
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                // Optional: show a loading indicator before redirecting
                Swal.fire({
                    title: t('Deleting...'),
                    allowOutsideClick: false,
                    didOpen: () => Swal.showLoading(),
                });
                window.location.href = deleteUrl;
            }
        });
    });

    // ─── Preview Toggle Functionality ────────────────────────────────────────────
    
    const $markdownToggle = $('#editor-switch');
    const $btnTogglePreview = $('#btnTogglePreview');
    const $previewArea = $('#preview-wrap');
    const $previewButtonText = $btnTogglePreview.find('.button-text');
    const $previewButtonIcon = $btnTogglePreview.find('i.material-icons');

    // Function to update the button text and icon
    const updateToggleButtonUI = (isPreviewVisible) => {
        if (isPreviewVisible) {
            $previewButtonText.text(t('Hide Preview'));
            $previewButtonIcon.text('chevron_right');
        } else {
            $previewButtonText.text(t('Show Preview'));
            $previewButtonIcon.text('chevron_left');
        }
    };

    // Function to sync preview visibility with button state
    const syncPreviewVisibility = (showPreview) => {
        const isMarkdownEnabled = $markdownToggle.is(':checked');

        if (isMarkdownEnabled && showPreview) {
            $previewArea.removeClass('hidden');
            updateToggleButtonUI(true);
        } else {
            $previewArea.addClass('hidden');
            updateToggleButtonUI(false);
        }
    };

    // Function to update preview toggle button visibility
    const updatePreviewToggleButtonVisibility = (isMarkdownEnabled) => {
        if (isMarkdownEnabled) {
            $btnTogglePreview.show();
        } else {
            $btnTogglePreview.hide();
        }
    };

    // Function to update the editor label text based on markdown toggle state
    const updateEditorLabel = () => {
        const isMarkdownEnabled = $markdownToggle.is(':checked');
        const $editorLabel = $('.ph-editor-label label[for="editor"]');
        if ($editorLabel.length) {
            if (isMarkdownEnabled) {
                $editorLabel.html(`${t('Article body (Markdown)')}`);
            } else {
                $editorLabel.html(`${t('Article body (WYSIWYG)')}`);
            }
        }
    };

    // Expose functions globally so openKB.js can access them
    window.updatePreviewToggleButtonVisibility = updatePreviewToggleButtonVisibility;
    window.syncPreviewVisibility = syncPreviewVisibility;
    window.updateEditorLabel = updateEditorLabel; // Expose the new function globally

    // Initialize preview toggle functionality
    const initializePreviewFunctionality = () => {
        // Restore preview state from localStorage (default to true if not set)
        const savedPreviewState = localStorage.getItem('ph-editor-show-preview');
        const shouldShowPreview = savedPreviewState !== null ? savedPreviewState === 'true' : true;

        // Immediately hide the preview area to prevent FOUC/race conditions
        syncPreviewVisibility(false);
        
        // Capture initial markdown enabled state on page load
        const initialMarkdownEnabled = $markdownToggle.is(':checked');

        // Set initial visibility of the button. This happens immediately.
        updatePreviewToggleButtonVisibility(initialMarkdownEnabled);
        
        // Set initial state of the editor label
        updateEditorLabel();
        
        // Set initial visibility of the preview area after a delay
        // This counters potential race conditions where other scripts (e.g., markdown editor)
        // might be overriding display properties on editor initialization.
        setTimeout(() => {
            // Only sync preview visibility if markdown editor was initially enabled on page load
            if (initialMarkdownEnabled) {
                if (shouldShowPreview) {
                    syncPreviewVisibility(true);
                } else {
                    // Explicitly hide again if markdown was enabled but preview should be off
                    syncPreviewVisibility(false);
                }
            } else {
                // Ensure it's hidden if markdown was not initially enabled
                syncPreviewVisibility(false);
            }
        }, 1000); // Increased delay to 1000ms for more robustness
    };

    // Preview toggle button click handler
    $btnTogglePreview.on('click', function() {
        const currentlyVisible = !$previewArea.hasClass('hidden');
        const newState = !currentlyVisible;
        
        localStorage.setItem('ph-editor-show-preview', newState.toString());
        
        syncPreviewVisibility(newState);
    });

    if ($markdownToggle.length && $btnTogglePreview.length) {
        initializePreviewFunctionality();
    }
    




    
    // Expose functions globally for access from openKB.js
    // ================================
    // AI-Powered Article Change Detection Functions
    // ================================

    /**
     * Capture current state of all article form fields for change detection
     * 
     * This function creates a snapshot of the current article data that can be
     * compared later to detect what fields have been modified by the user.
     * 
     * @returns {Object} Current article data object with all form field values
     */
    function captureCurrentArticleData() {
        const data = {
            title: $('#frm_kb_title').val() || '',
            body: $('#is_markdown').val() === 'true' ? 
                (window.getSimplemdeValue ? window.getSimplemdeValue() : $('#editor').val()) : 
                ($('#editor').data('trumbowyg') ? $('#editor').trumbowyg('html') : $('#editor').val()),
            permalink: $('#frm_kb_permalink').val() || '',
            keywords: $('#frm_kb_keywords').val() || '',
            topic: $('#selectTopics option:selected').text() || '',
            subtopic: $('#selectSubtopics option:selected').text() || '',
            status: $('#frm_kb_published').val() || '',
            featured: $('#frm_kb_featured').prop('checked') || false,
            pinned: $('#kb_pinned').prop('checked') || false,
            password_protected: $('#frm_kb_password').val() !== '',
            password: $('#frm_kb_password').val() || '',
            seo_title: $('#frm_kb_seo_title').val() || '',
            seo_description: $('#frm_kb_seo_description').val() || '',
            visible_state: $('#frm_kb_visible_state').val() || '',
            security_level: $('#frm_kb_security_level').val() || '',
            language: $('#frm_kb_language').val() || '',
            regional_visibility: $('#frm_kb_regional_visibility').val() || '',
            admin_restricted: $('#frm_kb_admin_restricted').prop('checked') || false,
            is_markdown: $('#is_markdown').val() === 'true'
        };
        
        return data;
    }

    /**
     * Detect changes between original and current article data
     * 
     * Compares two article data objects and identifies which fields have been
     * modified, providing detailed change information for AI analysis.
     * 
     * @param {Object} original - Original article data snapshot
     * @param {Object} current - Current article data snapshot
     * @returns {Array} Array of change objects with field names and change details
     */
    function detectArticleChanges(original, current) {
        const changes = [];
        
        // Define field mappings with user-friendly names
        const fieldMappings = {
            title: 'Article Title',
            body: 'Article Content',
            permalink: 'Permalink',
            keywords: 'Keywords',
            topic: 'Topic Assignment',
            subtopic: 'Subtopic Assignment',
            status: 'Publication Status',
            featured: 'Featured Status',
            pinned: 'Topic Association',
            password_protected: 'Password Protection',
            password: 'Password',
            seo_title: 'SEO Title',
            seo_description: 'SEO Description',
            visible_state: 'Visibility State',
            security_level: 'Security Level',
            language: 'Language',
            regional_visibility: 'Regional Visibility',
            admin_restricted: 'Admin Restriction',
            is_markdown: 'Editor Type'
        };

        // Check each field for changes
        for (const [field, friendlyName] of Object.entries(fieldMappings)) {
            let originalValue = original[field];
            let currentValue = current[field];
            
            // Special handling for regional visibility - normalize empty values to "Global"
            if (field === 'regional_visibility') {
                originalValue = originalValue || 'Global';
                currentValue = currentValue || 'Global';
            }
            
            // Handle different types of comparisons
            let hasChanged = false;
            
            if (typeof originalValue === 'boolean' || typeof currentValue === 'boolean') {
                hasChanged = Boolean(originalValue) !== Boolean(currentValue);
            } else {
                // String comparison - convert to strings and trim whitespace
                const origStr = String(originalValue || '').trim();
                const currStr = String(currentValue || '').trim();
                hasChanged = origStr !== currStr;
            }
            
            if (hasChanged) {
                changes.push({
                    field: field,
                    fieldName: friendlyName,
                    oldValue: originalValue,
                    newValue: currentValue,
                    oldText: formatValueForAI(field, originalValue),
                    newText: formatValueForAI(field, currentValue)
                });
            }
        }
        
        return changes;
    }

    /**
     * Format field values for AI processing with descriptive text
     * @param {string} field - Field name
     * @param {*} value - Field value
     * @returns {string} Formatted text for AI
     */
    function formatValueForAI(field, value) {
        switch (field) {
            case 'title':
                return `Title: "${value || 'No title'}"`;
            case 'body':
                return `Content: ${value || 'No content'}`;
            case 'keywords':
                return `Keywords: [${value || 'No keywords'}]`;
            case 'regional_visibility':
                return `Regional Visibility: ${value || 'Global'}`;
            case 'status':
                return `Publication Status: ${value === 'true' ? 'Published' : 'Draft'}`;
            case 'topic':
                const topicValue = (value && value !== 'Select Topic') ? value : 'No topic assigned';
                return `Topic: ${topicValue}`;
            case 'subtopic':
                const subtopicValue = (value && value !== 'Select Subtopic') ? value : 'No subtopic assigned';
                return `Subtopic: ${subtopicValue}`;
            case 'security_level':
                const levels = {
                    '100': 'Standard User',
                    '200': 'Club Manager', 
                    '300': 'Club Owner',
                    '400': 'Admin Only'
                };
                return `Security Level: ${levels[value] || value}`;
            case 'visible_state':
                return `Visibility: ${value === 'private' ? 'Private' : 'Public'}`;
            case 'password':
                return `Password Protection: ${value ? 'Added password' : 'Removed password'}`;
            case 'featured':
                return `Featured Status: ${value ? 'Featured' : 'Not featured'}`;
            case 'pinned':
                return `Topic Association: ${value ? 'Associated with topic' : 'Not associated'}`;
            case 'admin_restricted':
                return `Admin Restriction: ${value ? 'Admin only editing' : 'Anyone can edit'}`;
            case 'is_markdown':
                return `Editor Type: ${value ? 'Markdown' : 'WYSIWYG'}`;
            case 'language':
                return `Language: ${value || 'English'}`;
            case 'permalink':
                return `Permalink: ${value || 'No permalink set'}`;
            case 'seo_title':
                return `SEO Title: ${value || 'No SEO title'}`;
            case 'seo_description':
                return `SEO Description: ${value || 'No SEO description'}`;
            case 'password_protected':
                return `Password Protection: ${value ? 'Protected' : 'Not protected'}`;
            default:
                return `${field}: ${value || 'Not set'}`;
        }
    }

    // Expose functions to global scope for use by openKB.js
    window.captureCurrentArticleData = captureCurrentArticleData;
    window.detectArticleChanges = detectArticleChanges;
    window.generateChangelogSummaries = generateChangelogSummaries;
    window.originalArticleData = originalArticleData;
    window.selectTopicsChange = selectTopicsChange;
    window.setAvailableCountries = setAvailableCountries;

    // Handle broken images and failed media in WYSIWYG editor
    const handleBrokenImages = () => {
        // Handle existing images that fail to load (only for images without error handlers already)
        $('.uploaded-media img').not('[data-error-handled]').on('error', function() {
            const $img = $(this);
            // Find the outermost uploaded-media container
            const $containers = $img.parents('.uploaded-media');
            const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
            
            // Store the original image URL in data attribute before modifying
            const originalUrl = $img.attr('src');
            if (originalUrl) {
                $container.attr('data-failed-media-url', originalUrl);
            }
            
            // Add error classes
            $img.addClass('img-error');
            $container.addClass('media-error');
            
            // Hide the broken image but preserve it in DOM
            $img.hide();
            
            // Hide loading text when image fails
            const $span = $container.find('span').first();
            if ($span.text().includes('Loading')) {
                $span.hide();
            }
            
            // Remove any direct text nodes containing "Loading"
            $container.contents().filter(function() {
                return this.nodeType === 3 && this.textContent.includes('Loading');
            }).remove();
            
            // Add error overlay without destroying original content
            if (!$container.find('.media-error-overlay').length) {
                $container.append('<div class="media-error-overlay" style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Image not uploaded</div>');
            }
        }).attr('data-error-handled', 'true');
        
        // Handle images that successfully load
        $('.uploaded-media img').on('load', function() {
            const $img = $(this);
            // Find the outermost uploaded-media container
            const $containers = $img.parents('.uploaded-media');
            const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
            
            // Remove error classes if image loads successfully
            $img.removeClass('img-error').addClass('img-fluid').show();
            $container.removeClass('media-error');
            
            // Remove error overlay if image loads successfully
            $container.find('.media-error-overlay').remove();
            
            // Remove failed URL data attribute since image loaded successfully
            $container.removeAttr('data-failed-media-url');
            
            // Hide the loading text when image loads
            const $span = $container.find('span').first();
            $span.fadeOut(300);
        });
        
        // Check for images that are already broken (cached failures) or already loaded
        $('.uploaded-media img').each(function() {
            const img = this;
            const $img = $(img);
            const $containers = $img.parents('.uploaded-media');
            const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
            
            if (img.complete && img.naturalWidth === 0) {
                // Image failed to load
                $(img).trigger('error');
            } else if (img.complete && img.naturalWidth > 0) {
                // Image loaded successfully - hide loading text
                const $span = $container.find('span').first();
                $span.hide();
            }
        });
        
        // Handle file-to-proxy elements that are stuck showing "Loading media..."
        // These should be converted to video players but if that fails, we should hide the loading text
        $('.file-to-proxy').each(function() {
            const $element = $(this);
            const url = $element.data('url');
            
            // If element still contains loading text and hasn't been processed, store URL and show error
            if ($element.text().includes('Loading media...') && url) {
                // Store the original media URL in data attribute
                $element.attr('data-failed-media-url', url);
                
                // Replace loading text with error message
                $element.html('<div style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Media not loaded</div>');
                $element.addClass('media-error');
            }
        });
        
        // Handle orphaned loading spans only in containers with broken images
        $('.uploaded-media.media-error span').each(function() {
            const $span = $(this);
            const text = $span.text();
            
            // Check for various loading text patterns
            if (text.includes('Loading Media...') || text.includes('Loading media...') || text.includes('Loading')) {
                $span.hide();
            }
        });
        
        // Remove any direct text nodes containing "Loading" in error containers
        $('.uploaded-media.media-error, .file-to-proxy').each(function() {
            $(this).contents().filter(function() {
                return this.nodeType === 3 && this.textContent.includes('Loading');
            }).remove();
        });
    };
    
    // Initialize broken image handling
    handleBrokenImages();
    
    // Also check for unprocessed file-to-proxy elements after a delay
    // This catches cases where video processing fails or is delayed
    setTimeout(() => {
        $('.file-to-proxy').each(function() {
            const $element = $(this);
            const url = $element.data('url');
            
            // If element still contains loading text after delay, treat as failed
            if ($element.text().includes('Loading media...') && url) {
                // Store the original media URL in data attribute
                $element.attr('data-failed-media-url', url);
                
                // Replace loading text with error message
                $element.html('<div style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Media not loaded</div>');
                $element.addClass('media-error');
            }
        });
        
        // Also check for any remaining loading spans after delay, but only in error containers
        $('.uploaded-media.media-error span, .file-to-proxy span').each(function() {
            const $span = $(this);
            const text = $span.text();
            
            // Check for various loading text patterns
            if (text.includes('Loading Media...') || text.includes('Loading media...') || text.includes('Loading')) {
                $span.hide();
            }
        });
        
        // Remove any direct text nodes containing "Loading" in error containers
        $('.uploaded-media.media-error, .file-to-proxy').each(function() {
            $(this).contents().filter(function() {
                return this.nodeType === 3 && this.textContent.includes('Loading');
            }).remove();
        });
    }, 2000); // Wait 2 seconds for video processing to complete
    
    // Re-apply broken image handling when editor content changes
    // This handles images added dynamically via the editor
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                // Check if any uploaded-media elements were added
                $(mutation.addedNodes).find('.uploaded-media img').each(function() {
                    const $img = $(this);
                    
                    // Add error handler for new images
                    $img.on('error', function() {
                        // Find the outermost uploaded-media container
                        const $containers = $img.parents('.uploaded-media');
                        const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
                        
                        // Store the original image URL in data attribute before modifying
                        const originalUrl = $img.attr('src');
                        if (originalUrl) {
                            $container.attr('data-failed-media-url', originalUrl);
                        }
                        
                        $img.addClass('img-error');
                        $container.addClass('media-error');
                        
                        // Hide the broken image but preserve it in DOM
                        $img.hide();
                        
                        // Hide loading text when image fails
                        const $span = $container.find('span').first();
                        if ($span.text().includes('Loading')) {
                            $span.hide();
                        }
                        
                        // Remove any direct text nodes containing "Loading"
                        $container.contents().filter(function() {
                            return this.nodeType === 3 && this.textContent.includes('Loading');
                        }).remove();
                        
                        // Add error overlay without destroying original content
                        if (!$container.find('.media-error-overlay').length) {
                            $container.append('<div class="media-error-overlay" style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Image not uploaded</div>');
                        }
                    });
                    
                    // Add load handler for new images  
                    $img.on('load', function() {
                        // Find the outermost uploaded-media container
                        const $containers = $img.parents('.uploaded-media');
                        const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
                        $img.removeClass('img-error').addClass('img-fluid').show();
                        $container.removeClass('media-error');
                        
                        // Remove error overlay if image loads successfully
                        $container.find('.media-error-overlay').remove();
                        
                        // Remove failed URL data attribute since image loaded successfully
                        $container.removeAttr('data-failed-media-url');
                        
                        const $span = $container.find('span').first();
                        $span.fadeOut(300);
                    });
                    
                    // Check if already broken
                    if (this.complete && this.naturalWidth === 0) {
                        $img.trigger('error');
                    } else if (this.complete && this.naturalWidth > 0) {
                        // Image already loaded - hide loading text
                        const $containers = $img.parents('.uploaded-media');
                        const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
                        const $span = $container.find('span').first();
                        $span.hide();
                    }
                });
                
                // Check for file-to-proxy elements in newly added content
                $(mutation.addedNodes).find('.file-to-proxy').each(function() {
                    const $element = $(this);
                    const url = $element.data('url');
                    
                    // If element contains loading text and hasn't been processed, store URL and show error
                    if ($element.text().includes('Loading media...') && url) {
                        // Store the original media URL in data attribute
                        $element.attr('data-failed-media-url', url);
                        
                        // Replace loading text with error message
                        $element.html('<div style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Media not loaded</div>');
                        $element.addClass('media-error');
                    }
                });
                
                // Check for orphaned loading spans in newly added content with errors
                $(mutation.addedNodes).find('.uploaded-media.media-error span').each(function() {
                    const $span = $(this);
                    const text = $span.text();
                    
                    // Check for various loading text patterns
                    if (text.includes('Loading Media...') || text.includes('Loading media...') || text.includes('Loading')) {
                        $span.hide();
                    }
                });
            }
        });
    });
    
    // Start observing the editor for changes
    const editorElement = document.getElementById('editor');
    if (editorElement) {
        observer.observe(editorElement, {
            childList: true,
            subtree: true
        });
    }

    // Also observe the trumbowyg editor content area when it's initialized
    $(document).on('tbwinit', function() {
        const trumbowygEditor = $('.trumbowyg-editor')[0];
        if (trumbowygEditor) {
            observer.observe(trumbowygEditor, {
                childList: true,
                subtree: true
            });
            
            // Apply to existing images in the editor
            setTimeout(() => {
                $(trumbowygEditor).find('.uploaded-media img').each(function() {
                    const $img = $(this);
                    
                    $img.on('error', function() {
                        // Find the outermost uploaded-media container
                        const $containers = $img.parents('.uploaded-media');
                        const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
                        
                        // Store the original image URL in data attribute before modifying
                        const originalUrl = $img.attr('src');
                        if (originalUrl) {
                            $container.attr('data-failed-media-url', originalUrl);
                        }
                        
                        $img.addClass('img-error');
                        $container.addClass('media-error');
                        
                        // Hide the broken image but preserve it in DOM
                        $img.hide();
                        
                        // Hide loading text when image fails
                        const $span = $container.find('span').first();
                        if ($span.text().includes('Loading')) {
                            $span.hide();
                        }
                        
                        // Remove any direct text nodes containing "Loading"
                        $container.contents().filter(function() {
                            return this.nodeType === 3 && this.textContent.includes('Loading');
                        }).remove();
                        
                        // Add error overlay without destroying original content
                        if (!$container.find('.media-error-overlay').length) {
                            $container.append('<div class="media-error-overlay" style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Image not uploaded</div>');
                        }
                    });
                    
                    $img.on('load', function() {
                        // Find the outermost uploaded-media container
                        const $containers = $img.parents('.uploaded-media');
                        const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
                        $img.removeClass('img-error').addClass('img-fluid').show();
                        $container.removeClass('media-error');
                        
                        // Remove error overlay if image loads successfully
                        $container.find('.media-error-overlay').remove();
                        
                        // Remove failed URL data attribute since image loaded successfully
                        $container.removeAttr('data-failed-media-url');
                        
                        const $span = $container.find('span').first();
                        $span.fadeOut(300);
                    });
                    
                    if (this.complete && this.naturalWidth === 0) {
                        $img.trigger('error');
                    } else if (this.complete && this.naturalWidth > 0) {
                        // Image already loaded - hide loading text
                        const $containers = $img.parents('.uploaded-media');
                        const $container = $containers.length > 0 ? $containers.last() : $img.closest('.uploaded-media');
                        const $span = $container.find('span').first();
                        $span.hide();
                    }
                });
            }, 500);
        }
    });



}); //end of document.ready

// FUNCTIONS ---------------------------------------------------------------------------------------------------------
var expanded = false;

//called only once during page load
async function getAllTopicsData() {
    const articleTopic = $('#selectTopics').data('topic');
    const userLang = $('#user_lang').val();
    
    return new Promise((resolve, reject) => {
        $.ajax({
            method: "GET",
            url: $("#app_context").val() + `/api/topics`,
        })
        .done(function (res) {
            if (res && res.data) {
                //$("#selectTopics option").remove();

                res.data.map((t) => {
                    $("#selectTopics").append(`
                    <option value="${t._id}" ${articleTopic === t._id ? 'selected': ''} >${t.topic} ${t.topicTranslations && t.topic !== t.topicTranslations[userLang] ? '('+t.topicTranslations[userLang]+')' : ''}</option>
                `);

                    if (t.subtopics) {
                        t.subtopics.map((s, i) => {
                            allSubtopics.push({
                                id: s.id,
                                topic: t._id,
                                subtopic: s.subtopic,
                                subtopicTranslations: s.subtopicTranslations
                            });
                        });
                    }
                });

                //now set values based on data attribute from dropdown
                const selectTopics = $("#selectTopics");
                const selectSubTopics = $("#selectSubtopics");
                selectTopics.val(selectTopics.data("topic"));

                //set subtopics
                setSubtopicSelect(selectTopics.data("topic"));
                
                resolve(res);
            } else {
                resolve(null);
            }
        })
        .fail(function (error) {
            reject(error);
        });
    });
}

function setSubtopicSelect(topic = null) {
    const userLang = $('#user_lang').val() ?? 'en';

    if (topic) {
        //get values from allSubtopics based on respective topic
        $("#selectSubtopics option").remove();
        let subtopicSelect = $("#selectSubtopics");
        
        allSubtopics.map((s) => {
            if (s.topic == topic) {
                subtopicSelect.append(`
                    <option value="${s.id}">${s.subtopic} ${s.subtopicTranslations && s.subtopic !== s.subtopicTranslations[userLang] ? '('+s.subtopicTranslations[userLang]+')' : ''}</option>
                `);
            }
        });
       
        //then set selected based on data attribute
        subtopicSelect.val(subtopicSelect.data("subtopic"));
    }
}

/**
 * Generate AI-powered changelog summaries for all changes
 * @param {Array} changes - Array of change objects
 * @returns {Promise<Object>} Consolidated AI summary or individual summaries
 */
async function generateChangelogSummaries(changes) {
    if (!changes || changes.length === 0) {
        return [];
    }
    
    try {
        const apiUrl = $("#app_context").val() + '/api/ai/summarize-changelog';
        
        const response = await axios.post(apiUrl, {
            changes: changes
        });
        
        const result = {
            type: 'consolidated',
            summary: response.data.data,
            changeCount: response.data.changeCount,
            success: true
        };
        
        return result;
    } catch (error) {
        const errorResult = {
            type: 'error',
            error: error.response?.data?.error || error.message || 'Unknown error',
            success: false
        };
        
        return errorResult;
    }
}

function initializePermalinkControls() {
    // Update permalink display when permalink input changes
    const updatePermalinkDisplay = () => {
        const permalinkValue = $('#frm_kb_permalink').val();
        const appContext = $('#app_context').val() || window.location.origin;
        $('#permalink-display').text(`${appContext}/${permalinkValue}`);
    };

    updatePermalinkDisplay();

    // Edit permalink button click
    $('#btnEditPermalink').on('click', function(e) {
        e.preventDefault();
        $('#permalink-edit').show();
        // Hide the entire permalink display container
        $(this).closest('.d-flex.flex-gap-2.align-center').hide();
        $('#frm_kb_permalink').focus().select();
    });

    // Cancel edit permalink button click
    $('#btnCancelEditPermalink').on('click', function(e) {
        e.preventDefault();
        $('#permalink-edit').hide();
        // Show the entire permalink display container again
        $('.d-flex.flex-gap-2.align-center').show();
        // Restore original value if needed
        updatePermalinkDisplay();
    });

    // Update display when permalink input changes
    $('#frm_kb_permalink').on('input', function() {
        updatePermalinkDisplay();
    });

    // Title change handler to enable/disable generate from title button
    $('#frm_kb_title').on('input change', function() {
        const title = $(this).val();
        if (title && title.length > 5) {
            $('#generate_permalink_from_title').removeClass('disabled').prop('disabled', false);
            
            // Auto-generate permalink if current one is empty
            if ($('#frm_kb_permalink').val() === '') {
                $('#generate_permalink_from_title').click();
            }
        } else {
            $('#generate_permalink_from_title').addClass('disabled').prop('disabled', true);
        }
    });

    // Generate from title click handler - use existing global function
    $('#generate_permalink_from_title').on('click', function(e) {
        e.preventDefault();
        if (!$(this).hasClass('disabled') && !$(this).prop('disabled')) {
            // The global openKB.js already has this functionality
            // We just need to update our display after generation
            setTimeout(() => {
                updatePermalinkDisplay();
                // Hide edit mode after successful generation
                $('#permalink-edit').hide();
                // Show the entire permalink display container again
                $('.d-flex.flex-gap-2.align-center').show();
            }, 100);
        }
    });

    // Validate permalink click handler - use existing global function
    $('#validate_permalink').on('click', function(e) {
        e.preventDefault();
        setTimeout(() => {
            updatePermalinkDisplay();
        }, 100);
    });

    // Generate random permalink click handler - use existing global function
    $('#generate_permalink').on('click', function(e) {
        e.preventDefault();
        setTimeout(() => {
            updatePermalinkDisplay();
        }, 100);
    });

    // Enter key handler for permalink input
    $('#frm_kb_permalink').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            $('#btnCancelEditPermalink').click();
        }
    });

    // Escape key handler for permalink input
    $('#frm_kb_permalink').on('keydown', function(e) {
        if (e.which === 27) { // Escape key
            e.preventDefault();
            $('#btnCancelEditPermalink').click();
        }
    });
}



// ─── Feedback Stats Functionality ───────────────────────────────────────────

function initializeFeedbackStats() {
    const articleId = $('#article_id').val();
    if (!articleId) return;

    loadFeedbackStats(articleId);
}

function loadFeedbackStats(articleId) {
    const container = $('#feedbackStatsContainer');
    
    $.ajax({
        url: $('#app_context').val() + '/api/article/' + articleId + '/feedback-stats',
        method: 'GET',
        success: function(response) {
            if (response && response.success) {
                renderFeedbackStats(response.data);
            } else {
                showFeedbackError();
            }
        },
        error: function() {
            showFeedbackError();
        }
    });
}

function renderFeedbackStats(stats) {
    const container = $('#feedbackStatsContainer');
    const totalVotes = (stats.upvotes || 0) + (stats.downvotes || 0);
    
    // If no votes, show a simple message
    if (totalVotes === 0) {
        container.html(`
            <div class="fb-no-data">
                <p>${t('No feedback recorded yet')}</p>
            </div>
        `);
        return;
    }
    
    const upPercentage = totalVotes > 0 ? Math.round((stats.upvotes / totalVotes) * 100) : 0;
    const downPercentage = totalVotes > 0 ? Math.round((stats.downvotes / totalVotes) * 100) : 0;
    
    container.html(`
        <div class="fb-stats-summary">
            <div class="fb-stats-labels">
                <div class="fb-stats-label fb-stats-label--positive">
                    <i class="fa fa-thumbs-up"></i>
                    <span>${t('Helpful')}</span>
                </div>
                <div class="fb-stats-label fb-stats-label--negative">
                    <span>${t('Not Helpful')}</span>
                    <i class="fa fa-thumbs-down"></i>
                </div>
            </div>
            <div class="fb-stats-progress-unified">
                <div class="fb-stats-bar-helpful" style="width: ${upPercentage}%;"></div>
                <div class="fb-stats-bar-not-helpful" style="width: ${downPercentage}%;"></div>
            </div>
            <div class="d-flex justify-between align-center">
                <div class="fb-stats-count-container">
                    <span class="fb-stats-count">${stats.upvotes || 0}</span>
                    <span class="fb-stats-label">${upPercentage}%</span>
                </div>
                <span class="fb-stats-count-container">
                    <div class="fb-stats-count fb-stats-count--total text-center">${totalVotes}</div>
                    <div class="fb-stats-label">${t('total')}</div>
                </span>
                <div class="fb-stats-count-container">
                    <span class="fb-stats-count">${stats.downvotes || 0}</span>
                    <span class="fb-stats-label">${downPercentage}%</span>
                </div>
            </div>
        </div>
    `);
}

function showFeedbackError() {
    const container = $('#feedbackStatsContainer');
    container.html(`
        <div class="ph-stats-error">
            <i class="fa fa-exclamation-triangle"></i>
            <p class="text-muted">${t('Unable to load feedback statistics')}</p>
        </div>
    `);
}

// Initialize all new UI components
$(document).ready(function() {
    // Wait for the main document ready to complete first
    setTimeout(() => {
        initializeFeedbackStats();
    }, 500);
});


