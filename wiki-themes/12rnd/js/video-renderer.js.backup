/**
 * Shared Video Rendering Module
 * Provides reusable video processing functionality for both edit and article viewing modes
 */

// Global video tracking - shared across all contexts
window.processedVideos = window.processedVideos || new Set();
window.processingVideos = window.processingVideos || new Set();

/**
 * Video<PERSON>enderer class - handles video processing with configurable contexts
 */
class VideoRenderer {
    constructor(options = {}) {
        this.processedVideos = window.processedVideos;
        this.processingVideos = window.processingVideos;
        this.defaultOptions = {
            containerSelectors: ['.video-embed-container'],
            fileProxySelectors: ['.file-to-proxy'],
            targetContainers: ['body'],
            staggerDelay: 100,
            retryDelay: 200,
            ...options
        };
    }

    /**
     * Main activation function - processes videos in specified containers
     */
    activateVideoPlayers(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const containers = this._getTargetContainers(config.targetContainers);
        
        if (containers.length === 0) {
            return;
        }

        // Process existing video-embed-container elements
        this._processVideoContainers(containers, config);
        
        // Process file-to-proxy elements and convert them
        this._processFileProxyElements(containers, config);
        
        // Process uploaded-media containers with video img tags
        this._processUploadedMediaElements(containers, config);
    }

    /**
     * Get target containers based on selectors
     */
    _getTargetContainers(selectors) {
        // Check if jQuery is available when actually using it
        if (typeof $ === 'undefined') {
            console.warn('VideoRenderer: jQuery not available, cannot process videos');
            return { length: 0, find: () => ({ length: 0, each: () => {} }) };
        }
        
        const containers = [];
        selectors.forEach(selector => {
            const elements = $(selector);
            if (elements.length > 0) {
                containers.push(...elements.toArray());
            }
        });
        return $(containers);
    }

    /**
     * Process video-embed-container elements
     */
    _processVideoContainers(containers, config) {
        const videoContainers = containers.find(config.containerSelectors.join(', '));
        
        videoContainers.each((index, element) => {
            this._processVideoContainer($(element), index * config.staggerDelay);
        });
    }

    /**
     * Process individual video container
     */
    _processVideoContainer(container, delay = 0) {
        const placeholder = container.find('.video-loading-placeholder');
        const existingVideo = container.find('video');
        const videoUrl = container.attr('data-video-url');
        
        // Skip if URL is already being processed (prevents duplicate processing during concurrent calls)
        if (videoUrl && this.processingVideos.has(videoUrl)) {
            return;
        }
        
        // If we have a URL and it's already processed in this session
        if (videoUrl && this.processedVideos.has(videoUrl)) {
            // Check if we have a working video element
            if (existingVideo.length > 0 && 
                existingVideo.attr('data-blob-url') && 
                !existingVideo[0].error &&
                existingVideo[0].readyState !== 0) { // readyState 0 means no data
                return;
            }
            this.processedVideos.delete(videoUrl);
        }
        
        if (existingVideo.length > 0) {
            existingVideo.remove();
        }
        
        if (placeholder.length > 0) {
            placeholder.remove();
        }
        
        container.append(this._createPlaceholder());
        
        const url = container.attr('data-video-url');
        const type = container.attr('data-video-type') || 'mp4';
        
        if (url) {
            this.processingVideos.add(url);
            
            setTimeout(() => {
                this._loadVideo(container, url);
            }, delay);
        }
    }

    /**
     * Process file-to-proxy elements and convert them to video containers
     */
    _processFileProxyElements(containers, config) {
        const proxyElements = containers.find(config.fileProxySelectors.join(', '));
        
        proxyElements.each((index, element) => {
            this._convertFileProxyToVideoContainer($(element), config.retryDelay + (index * config.staggerDelay));
        });
    }

    /**
     * Convert file-to-proxy element to video-embed-container
     */
    _convertFileProxyToVideoContainer(element, delay = 0) {
        const url = element.attr('data-url');
        
        if (!url) return;
        
        // Skip if already processed in this session
        if (this.processedVideos.has(url)) {
            return;
        }
        
        // Mark as processed
        this.processedVideos.add(url);
        
        // Convert to video-embed-container for consistent handling
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'mp4',
            css: {
                maxWidth: '100%',
                margin: '1rem 0'
            }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        // Process the newly created container
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    /**
     * Process uploaded-media elements that contain video img tags
     */
    _processUploadedMediaElements(containers, config) {
        // Find uploaded-media divs that contain img tags with video extensions
        const uploadedMediaElements = containers.find('.uploaded-media');
        
        uploadedMediaElements.each((index, element) => {
            const $element = $(element);
            const $img = $element.find('img');
            
            if ($img.length > 0) {
                const src = $img.attr('src');
                
                // Check if the image src has a video extension
                if (src && this._isVideoFile(src)) {
                    this._convertUploadedMediaToVideoContainer($element, src, config.retryDelay + (index * config.staggerDelay));
                }
            }
        });
    }

    /**
     * Check if a URL points to a video file
     */
    _isVideoFile(url) {
        const videoExtensions = ['mp4', 'mkv', 'webm', 'avi', 'mpeg', 'mov', 'mp3'];
        const urlWithoutParams = url.split('?')[0]; // Remove query parameters
        const extension = urlWithoutParams.split('.').pop().toLowerCase();
        return videoExtensions.includes(extension);
    }

    /**
     * Convert uploaded-media element with video img to video container
     */
    _convertUploadedMediaToVideoContainer(element, url, delay = 0) {
        // Skip if already processed in this session
        if (this.processedVideos.has(url)) {
            return;
        }
        
        // Mark as processed
        this.processedVideos.add(url);
        
        // Convert to video-embed-container for consistent handling
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'mp4',
            css: {
                maxWidth: '100%',
                margin: '1rem 0'
            }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        // Process the newly created container
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    /**
     * Create loading placeholder element
     */
    _createPlaceholder() {
        return $('<div>', {
            class: 'video-loading-placeholder',
            html: 'Loading video...',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
    }

    /**
     * Load video using fetch with credentials and blob URL
     */
    _loadVideo(container, url) {
        const currentPlaceholder = container.find('.video-loading-placeholder');
        if (currentPlaceholder.length === 0) {
            return;
        }
        
        const loadingMsg = $('<div>', {
            html: 'Loading video...',
            'data-video-loading': 'true',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
        currentPlaceholder.replaceWith(loadingMsg);
        
        fetch(url, { 
            method: 'GET', 
            credentials: 'include',
            headers: {
                'Accept': 'video/*'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.blob();
        })
        .then(blob => {
            if (loadingMsg.parent().length === 0) {
                URL.revokeObjectURL(URL.createObjectURL(blob));
                return;
            }
            
            const blobUrl = URL.createObjectURL(blob);
            const video = this._createVideoElement(url, blobUrl);
            
            loadingMsg.replaceWith(video);
            
            // Mark this video as processed and remove from processing
            if (url) {
                this.processedVideos.add(url);
                this.processingVideos.delete(url);
            }
        })
        .catch(error => {
            console.error('Error loading video:', error);
            if (loadingMsg.parent().length === 0) {
                return;
            }
            
            const errorMsg = this._createErrorElement();
            loadingMsg.replaceWith(errorMsg);
            
            // Remove from processing on error
            if (url) {
                this.processingVideos.delete(url);
            }
        });
    }

    /**
     * Create video element with blob URL
     */
    _createVideoElement(url, blobUrl) {
        const video = $('<video>', {
            controls: true,
            preload: 'metadata',
            'data-video-url': url,
            'data-blob-url': blobUrl,
            css: {
                width: '100%',
                maxWidth: '800px',
                margin: '1rem 0',
                display: 'block'
            }
        });
        
        video.attr('src', blobUrl);
        
        // Cleanup blob URL when video is removed
        video.on('remove', function() {
            URL.revokeObjectURL(blobUrl);
        });
        
        // Cleanup blob URL on page unload
        $(window).on('beforeunload', function() {
            URL.revokeObjectURL(blobUrl);
        });
        
        return video;
    }

    /**
     * Create error message element
     */
    _createErrorElement() {
        return $('<div>', {
            html: 'Failed to load video',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center',
                fontStyle: 'italic'
            }
        });
    }

    /**
     * Add retry mechanism for failed videos
     */
    addRetryMechanism(targetContainers, retryDelay = 3000) {
        setTimeout(() => {
            const selectors = targetContainers.map(selector => 
                `${selector} .video-loading-placeholder`
            ).join(', ');
            
            const failedVideos = $(selectors);
            if (failedVideos.length > 0) {
                this.activateVideoPlayers({ targetContainers });
            }
        }, retryDelay);
    }

    /**
     * Clear video session tracking (useful for mode switches)
     */
    clearVideoTracking(urlsToRemove = []) {
        if (urlsToRemove.length === 0) {
            // Clear videos with errors
            const videosWithErrors = $('video[data-blob-url]');
            videosWithErrors.each((index, video) => {
                if (video.error || video.networkState === 3) {
                    const videoUrl = $(video).attr('data-video-url');
                    if (videoUrl && this.processedVideos.has(videoUrl)) {
                        this.processedVideos.delete(videoUrl);
                    }
                    if (videoUrl && this.processingVideos.has(videoUrl)) {
                        this.processingVideos.delete(videoUrl);
                    }
                }
            });
        } else {
            urlsToRemove.forEach(url => {
                this.processedVideos.delete(url);
                this.processingVideos.delete(url);
            });
        }
    }
}

// Simple, immediate initialization
window.VideoRenderer = VideoRenderer;
window.videoRenderer = new VideoRenderer();

// Expose key functions globally for backward compatibility
window.activateAllVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers(options);
};

window.activateArticleVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        ...options
    });
};