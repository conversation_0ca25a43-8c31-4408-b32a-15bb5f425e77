$(document).ready(function () {
    initTopSearchesDt();
    initTopResultsDt();
    initTopNoResultsDt();

    $("#top-searches-csv").on("click", function (e) {
        e.preventDefault();
        if($('#analytics-type').val() === 'top-searches') {
            const topSearchesDt = getTopSearchesDt();
            topSearchesDt.button(0).trigger()
        }
        else if($('#analytics-type').val() === 'top-results') {
            const topResultsDt = getTopResultsDt();
      
            topResultsDt.button(0).trigger()
        }
        else if($('#analytics-type').val() === 'top-no-results') {
            
            const topNoResultsDt = getTopNoResultsDt();
            console.log(topNoResultsDt)
            topNoResultsDt.button(0).trigger()
        }
        
    });

    $("#result_filter").on("keyup", function () {
        if($('#analytics-type').val() === 'top-searches') {
            searchTopSearches();
        }
        else if($('#analytics-type').val() === 'top-results') {
            searchTopResults();
        }
        else if($('#analytics-type').val() === 'top-no-results') {
            searchTopNoResults();
        }
    });

    //waves init
    Waves.init();
    Waves.attach('.waves-effect', 'waves-effect', true);

    $('#navSettingsDashboard').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings';
	});
	$('#navSettingsArticles').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/articles';
	});
	$('#navSettingsConfiguration').on('click', function () {
		window.location.href = $('#app_context').val() + '/settings/configuration';
	});

});


function searchTopSearches() {
    const table = getTopSearchesDt();

    const filter = $("#result_filter").val();

    table.column(1) //title
        .search(filter ?? '')
    table.draw()
}

function searchTopResults() {
    const table = getTopResultsDt();

    const filter = $("#result_filter").val();

    table.column(1) //title
        .search(filter ?? '')
    table.draw()
}

function searchTopNoResults() {
    const table = getTopNoResultsDt();

    const filter = $("#result_filter").val();

    table.column(1) //title
        .search(filter ?? '')
    table.draw()
}

const getTopSearchesDt = () => {
    return $('#top-searches-table').DataTable();
}

const getTopResultsDt = () => {
    return $('#top-results-table').DataTable();
}

const getTopNoResultsDt = () => {
    return $('#top-no-results-table').DataTable();
}

const initTopSearchesDt = () => {
    const dtConfig = {
        responsive: true,
        dom: 'Brtip',
        order: [[2, "desc"]],
        fixedHeader: true,
        // Show all results in table...
        iDisplayLength: -1,
        columnDefs: [
            {
                targets: "no-sort",
                orderable: false,
            },
            {
                targets: "searchable",
                searchable: true,
            },
            { "width": "110px", "targets": 0 },
        ],
        paging: false,
        info: false,
        language: {
            infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
            emptyTable: t('No data available in table'),
            zeroRecords: t('No matching records found'),
            infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
            aria: {
                sortAscending: `: ${t('activate to sort column ascending')}`,
                sortDescending: `: ${t('activate to sort column descending')}`
            },
            searchPlaceholder: "Filter results"
        },
        buttons: [
            {
                extend: 'csv',
                fileName: 'top-searches',
                extension: '.csv',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5],
                    format: {
                        body: function (data, row, column, node) {
                            return $(node).data('export');
                        }
                    }
                }
            }
        ],
        initComplete: function () {
            $('.buttons-csv').hide()
        }
    }
    return $('#top-searches-table').DataTable(dtConfig);
}

const initTopResultsDt = () => {
    const dtConfig = {
        responsive: true,
        dom: 'Brtip',
        order: [[2, "desc"]],
        fixedHeader: true,
        // Show all results in table...
        iDisplayLength: -1,
        columnDefs: [
            {
                targets: "no-sort",
                orderable: false,
            },
            {
                targets: "searchable",
                searchable: true,
            },
            { "width": "110px", "targets": 0 },
        ],
        paging: false,
        info: false,
        language: {
            infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
            emptyTable: t('No data available in table'),
            zeroRecords: t('No matching records found'),
            infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
            aria: {
                sortAscending: `: ${t('activate to sort column ascending')}`,
                sortDescending: `: ${t('activate to sort column descending')}`
            },
            searchPlaceholder: "Filter results"
        },
        buttons: [
            {
                extend: 'csv',
                fileName: 'top-results',
                extension: '.csv',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4],
                    format: {
                        body: function (data, row, column, node) {
                            return $(node).data('export');
                        }
                    }
                }
            }
        ],
        initComplete: function () {
            $('.buttons-csv').hide()
        }
    }
    return $('#top-results-table').DataTable(dtConfig);
}

const initTopNoResultsDt = () => {
    const dtConfig = {
        responsive: true,
        dom: 'Brtip',
        order: [[2, "desc"]],
        fixedHeader: true,
        // Show all results in table...
        iDisplayLength: -1,
        columnDefs: [
            {
                targets: "no-sort",
                orderable: false,
            },
            {
                targets: "searchable",
                searchable: true,
            },
            { "width": "110px", "targets": 0 },
        ],
        paging: false,
        info: false,
        language: {
            infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
            emptyTable: t('No data available in table'),
            zeroRecords: t('No matching records found'),
            infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
            aria: {
                sortAscending: `: ${t('activate to sort column ascending')}`,
                sortDescending: `: ${t('activate to sort column descending')}`
            },
            searchPlaceholder: "Filter results"
        },
        buttons: [
            {
                extend: 'csv',
                fileName: 'top-no-results',
                extension: '.csv',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4],
                    format: {
                        body: function (data, row, column, node) {
                            return $(node).data('export');
                        }
                    }
                }
            }
        ],
        initComplete: function () {
            $('.buttons-csv').hide()
        }
    }
    return $('#top-no-results-table').DataTable(dtConfig);
}