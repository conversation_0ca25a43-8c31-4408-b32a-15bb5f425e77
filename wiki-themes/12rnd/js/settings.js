$(document).ready(async function () {
    await initTranslator();
    $( document ).tooltip();
    

    let analyticsOverview = null;
    let analyticsTotalSearches = null;
    let analyticsNoResultsRate = null;

    const baseAlgoliaAPI = 'https://analytics.us.algolia.com/2';
    const index = $('#algolia-index').val();
    const applicationId = $('#algolia-appId').val();
    const applicationKey = $('#algolia-apiKey').val();

    let contentWebsite = $('#tabContentWebsite');
    let contentArticle = $('#tabContentArticle');
    let contentDisplay = $('#tabContentDisplay');
    let contentStyle = $('#tabContentStyle');
    let contentTopicSettings = $('#tabContentTopicSettings');
    let contentFiles = $('#tabContentFiles');
    let contentImport = $('#tabContentImport');

    //check if navContainer has data
    let defaultTab = $('#navContainer').data('tabtoopen');
    let defaultNav = $('#wikiAdminNav').data('navtoopen');


    //hide other contents on first load
    //contentWebsite.hide();
    contentArticle.hide();
    contentDisplay.hide();
    contentStyle.hide();
    contentTopicSettings.hide();
    contentFiles.hide();
    contentImport.hide();

    //
    if (defaultNav === 'articles') {
        $('#navContentDashboard').hide()
        $('#navContentConfiguration').hide()
        $('#navContentArticles').show()
    } else if (defaultNav === 'configuration') {
        $('#navContentDashboard').hide()
        $('#navContentConfiguration').show()
        $('#navContentArticles').hide()
    } else {
        $('#navContentDashboard').show()
        $('#navContentConfiguration').hide()
        $('#navContentArticles').hide()
    }

    $('#tabWebsite').on('click', function () {
        contentWebsite.show();
        contentArticle.hide();
        contentDisplay.hide();
        contentStyle.hide();
        contentTopicSettings.hide();
        contentFiles.hide();
        contentImport.hide();

    })

    $('#tabArticle').on('click', function () {

        contentArticle.show();
        contentWebsite.hide();
        contentDisplay.hide();
        contentStyle.hide();
        contentTopicSettings.hide();
        contentFiles.hide();
        contentImport.hide();

    })

    $('#tabDisplay').on('click', function () {

        contentDisplay.show();
        contentWebsite.hide();
        contentArticle.hide();
        contentStyle.hide();
        contentTopicSettings.hide();
        contentFiles.hide();
        contentImport.hide();

    })

    $('#tabStyle').on('click', function () {

        contentStyle.show();
        contentWebsite.hide();
        contentArticle.hide();
        contentDisplay.hide();
        contentTopicSettings.hide();
        contentFiles.hide();
        contentImport.hide();

    })

    $('#tabTopicSettings').on('click', function () {

        contentTopicSettings.show();
        contentWebsite.hide();
        contentArticle.hide();
        contentDisplay.hide();
        contentStyle.hide();
        contentFiles.hide();
        contentImport.hide();

    })

    $('#tabFiles').on('click', function () {

        contentFiles.show();
        contentWebsite.hide();
        contentArticle.hide();
        contentDisplay.hide();
        contentStyle.hide();
        contentTopicSettings.hide();
        contentImport.hide();

    })

    $('#tabImport').on('click', function () {

        contentImport.show();
        contentFiles.hide();
        contentWebsite.hide();
        contentArticle.hide();
        contentDisplay.hide();
        contentStyle.hide();
        contentTopicSettings.hide();

    })


    if (defaultTab == 'topicsettings') {
        //open the tab
        $('#tabTopicSettings').click();
    }


    //if there is a specified nav to open
    if (defaultNav == 'articles') {
        $('#navSettingsArticles').click();
    } else if (defaultNav == 'configuration') {
        $('#navSettingsConfiguration').click();
    }

		
    $('#dateRangeAnalytics').on('apply.daterangepicker', function(ev, picker) {
        //do something, like clearing an input
        drawOverviewChart();
        drawTotalSearchChart();
        drawNoResultsRateChart();
        drawTopSearchesTable();
        drawTopResultsTable();
        drawNoResultsTable();

        //setViewAllTopResultsUrl();

        $('.reporting-period').html(picker.endDate.diff(picker.startDate, 'days') + t('days'))
      });

    function getOverViewDateRange() {
    
        const startDate = $('#dateRangeAnalytics').data('daterangepicker') ? $('#dateRangeAnalytics').data('daterangepicker').startDate.format('YYYY-MM-DD') : moment().subtract(6, 'month').format('YYYY-MM-DD');
        
        
        const endDate =  $('#dateRangeAnalytics').data('daterangepicker')  ? $('#dateRangeAnalytics').data('daterangepicker').endDate.format('YYYY-MM-DD'): new moment().format('YYYY-MM-DD');
        
        $('#startDate').val(startDate);
        $('#endDate').val(endDate);

        
        return { endDate: endDate, startDate: startDate };

        
    
    }

    function getTotalUsers() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/users/count?index=${index}&clickAnalytics=true&limit=1000&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },

        })
    }

    function getSearchCount() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/searches/count?index=${index}&clickAnalytics=true&limit=1000&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },

        })
    }

    function getClickThrough() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/clicks/clickThroughRate?index=${index}&clickAnalytics=true&limit=1000&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },
            data: JSON.stringify(getOverViewDateRange())
        })
    }

    function getConversionRate() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/conversions/conversionRate?index=${index}&clickAnalytics=true&limit=1000&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },

        })
    }

    function getNoResultsRate() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/searches/noResultRate?index=${index}&clickAnalytics=true&limit=1000&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },
        })
    }

    function getNoClickRate() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/searches/noClickRate?index=${index}&limit=1000&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },

        })
    }

    function getTopSearches(isShowClickAnalytics = false, ) {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/searches?index=${index}&clickAnalytics=${isShowClickAnalytics}&limit=5&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },

        })
    }

    function getTopResults(isShowClickAnalytics = false) {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/hits?index=${index}&clickAnalytics=${isShowClickAnalytics}&limit=5&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },
            data: JSON.stringify(getOverViewDateRange())
        })
    }

    function getNoResults() {
        const dateRange = getOverViewDateRange();
        return axios.get(`${baseAlgoliaAPI}/searches/noResults?index=${index}&clickAnalytics=true&limit=5&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
            headers: {
                'X-Algolia-Application-Id': applicationId,
                'X-Algolia-API-Key': applicationKey
            },

        })
    }

    

    async function drawOverviewChart() {
        const overViewData = await Promise.all([getTotalUsers(), getSearchCount(), getClickThrough(), getConversionRate(), getNoResultsRate(), getNoClickRate()])
        $('#totalUsers').html(overViewData[0].data.count)
        $('#totalNoClicks').html(overViewData[5].data.count)
        $('#totalNoResults').html(overViewData[4].data.count)
        $('#totalClicks').html(overViewData[2].data.clickCount)
        $('#totalTrackedSearches').html(overViewData[2].data.trackedSearchCount)
        $('#totalConversions').html(overViewData[3].data.conversionCount)
        //overview chart
        var ctx = document.getElementById("analytics-overview");

        const yLine = {
            id: 'yLine',
            afterDraw: chart => {
                const {
                    tooltip
                } = chart;
                
                if (tooltip._active && tooltip._active.length > 0) {
                    const {
                        ctx,
                        chartArea: { left, right, top, bottom },
                        scales: { y },
                    } = chart;
                    ctx.save();
                    const activePoint = tooltip._active[0];
                    // draw the vertical line chartjs
                    ctx.beginPath();
                    ctx.setLineDash([5, 5]);
                    ctx.moveTo(activePoint.element.x, top);
                    ctx.lineTo(activePoint.element.x, bottom);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = 'rgba(179,9,60,.7)';
                    ctx.stroke();
                    ctx.restore();
                }
            }
        }

        const data = {
            labels: overViewData[0].data.dates.map(date => {
                const formattedDate = moment(date.date).format('MMM D');
                return formattedDate;
            }),
            datasets: [{
                label: 'Total Users',
                data: overViewData[0].data.dates.map(date => date.count),
                fill: false,
                borderColor: 'rgb(44, 200, 247)',
                backgroundColor: '#2cc8f7',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2,

            },
            // {
            //     label: 'Total Searches',
            //     data: overViewData[1].data.dates.map(date => date.count),
            //     fill: false,
            //     borderColor: 'rgb(30, 89, 255)',
            //     backgroundColor: 'rgb(30, 89, 255, 0.2)',
            //     tension: 0,
            //     borderWidth: 1,
            //     pointRadius: 2
            // },
            {
                label: 'Click-through Rate',
                data: overViewData[2].data.dates.map(date => date.rate),
                fill: false,
                borderColor: 'rgb(248, 44, 170)',
                backgroundColor: 'rgb(248, 44, 170)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            {
                label: 'Conversion Rate',
                data: overViewData[3].data.dates.map(date => date.rate),
                fill: false,
                borderColor: 'rgb(0, 194, 65)',
                backgroundColor: 'rgb(0, 194, 65)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            // {
            //     label: 'No Results Rate',
            //     data: overViewData[4].data.dates.map(date => date.rate),
            //     fill: false,
            //     borderColor: 'rgb(119, 122, 175)',
            //     backgroundColor: 'rgb(119, 122, 175)',
            //     tension: 0,
            //     borderWidth: 2,
            //     pointRadius: 2
            // },
            {
                label: 'No Click Rate',
                data: overViewData[5].data.dates.map(date => date.rate),
                fill: false,
                borderColor: 'rgb(250, 160, 75)',
                backgroundColor: 'rgb(250, 160, 75)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            {
                label: 'Total No Clicks',
                data: overViewData[5].data.dates.map(date => date.count),
                fill: false,
                borderColor: 'rgb(232, 96, 10)',
                backgroundColor: 'rgb(232, 96, 10)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            {
                label: 'Total No Results',
                data: overViewData[4].data.dates.map(date => date.count),
                fill: false,
                borderColor: 'rgb(72, 76, 122)',
                backgroundColor: 'rgb(72, 76, 122)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            {
                label: 'Total Clicks',
                data: overViewData[2].data.dates.map(date => date.clickCount),
                fill: false,
                borderColor: 'rgb(184, 9, 121)',
                backgroundColor: 'rgb(184, 9, 121)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            {
                label: 'Total Conversions',
                data: overViewData[3].data.dates.map(date => date.conversionCount),
                fill: false,
                borderColor: 'rgb(184, 9, 121)',
                backgroundColor: 'rgb(184, 9, 121)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            },
            {
                label: 'Tracked Searches',
                data: overViewData[2].data.dates.map(date => date.trackedSearchCount),
                fill: false,
                borderColor: 'rgb(0, 139, 74)',
                backgroundColor: 'rgb(0, 139, 74)',
                tension: 0,
                borderWidth: 2,
                pointRadius: 2
            }
            ]
        }
        
        
        if (analyticsOverview) analyticsOverview.destroy();

         analyticsOverview = new Chart(ctx, {
            type: 'line',
            data: data,
            plugins: [yLine],
            options: {
                layout: {
                    padding: {
                        top: 10,
                       
                    }
                },
        
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    axis: 'x',
                    mode: 'index',
                },
                // interaction: {
                //     intersect: false,
                //     mode: 'index',
                // },
                plugins: {
                    legend: {
                        display: false,

                    }
                },
                scales: {
                    y: {
                       
                        ticks: {
                            callback: function (value, index, ticks) {
                                return index % 2
                                    ? ''
                                    : value
                            },
                            font: {
                                size: 10,
                            },
                            color: '#6A7383',
                            display: window.innerWidth < 600 ? false : true,
                        },
                      
                        grid: {
                            color: '#EBEEF1',
                            borderColor: 'rgba(180,191,203,0.3)',
                            drawBorder: window.innerWidth < 600 ? false : true,
                        }
                    },
                    x: {
                        ticks: {
                            callback: function (value, index, ticks) {
                                const labelValue = this.getLabelForValue(value);
                                const result = index % 2 ? labelValue : '';
                                return result;
                            },
                            font: {
                                size: 10,
                            },
                            maxRotation: 0,
                            color: '#6A7383'
                        },
                        grid: {
                            color: '#EBEEF1',
                            borderColor: 'rgba(180,191,203,0.3)'
                        }
                    },
                },
            }
        });
    }


    async function drawTotalSearchChart() {
        const totalSearchData = await getSearchCount();
        console.log(totalSearchData)
        //total search chart
        var ctx = document.getElementById("analytics-total-searches").getContext('2d');

        if(analyticsTotalSearches) analyticsTotalSearches.destroy();

        analyticsTotalSearches = new Chart(ctx, {
            type: 'line',
            data: {
                labels: totalSearchData.data.dates.map(date => {
                    const formattedDate = moment(date.date).format('MMM D');
                    return formattedDate;
                }),
                datasets: [{
                    label: 'Total Searches',
                    data: totalSearchData.data.dates.map(date => date.count),
                    fill: false,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: .6,
                    pointRadius: 0
                }]
            },
            options: {
               
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,

                    }
                },
                interaction: {
                    intersect: false,
                    axis: 'x',
                    mode: 'index',
                },
                scales: {
                    y: {
                       
                        ticks: {
                            callback: function (value, index, ticks) {
                                return index % 2
                                    ? ''
                                    : value
                            },
                            font: {
                                size: 10,
                            },
                            color: '#6A7383',
                            display: true,
                        },
                      
                        grid: {
                            color: '#EBEEF1',
                            borderColor: 'rgba(180,191,203,0.3)',
                            drawBorder: true,
                        }
                    },
                    x: {
                        ticks: {
                            callback: function (value, index, ticks) {
                                const labelValue = this.getLabelForValue(value);
                                const result = index % 2 !== 0 ? labelValue : '';
                                return result;
                            },
                            font: {
                                size: 10,
                            },
                            maxRotation: 0,
                            color: '#6A7383'
                        },
                        grid: {
                            color: '#EBEEF1',
                            borderColor: 'rgba(180,191,203,0.3)'
                        }
                    },
                },
            }
        });
    }

    //create a function to display a chart of algolia no results rate
    async function drawNoResultsRateChart() {
        const noResultsRateData = await getNoResultsRate();
  
        //no results rate chart
        var ctx = document.getElementById("analytics-no-results").getContext('2d');

        if(analyticsNoResultsRate) analyticsNoResultsRate.destroy();

        analyticsNoResultsRate = new Chart(ctx, {
            type: 'line',
            data: {
                labels: noResultsRateData.data.dates.map(date => {
                    const formattedDate = moment(date.date).format('MMM D');
                    return formattedDate;
                }),
                datasets: [{
                    label: 'No Results Rate',
                    data: noResultsRateData.data.dates.map(date => date.rate),
                    fill: false,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.6,
                    pointRadius: 0
                }]
            },
            options: {
               
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false,

                    }
                },
                scales: {
                    y: {
                       
                        ticks: {
                            callback: function (value, index, ticks) {
                                return index % 2
                                    ? ''
                                    : value
                            },
                            font: {
                                size: 10,
                            },
                            color: '#6A7383',
                            display: true,
                        },
                      
                        grid: {
                            color: '#EBEEF1',
                            borderColor: 'rgba(180,191,203,0.3)',
                            drawBorder: true,
                        }
                    },
                    x: {
                        ticks: {
                            callback: function (value, index, ticks) {
                                const labelValue = this.getLabelForValue(value);
                                const result = index % 2 !== 0 ? labelValue : '';
                                return result;
                            },
                            font: {
                                size: 10,
                            },
                            maxRotation: 0,
                            color: '#6A7383'
                        },
                        grid: {
                            color: '#EBEEF1',
                            borderColor: 'rgba(180,191,203,0.3)'
                        }
                    },
                },
            }
        });
    }


    async function drawTopSearchesTable() {
        const topSearches = await getTopSearches(false, 5);
        let tableHtml = "";

        topSearches.data.searches.forEach((search, index) => {
            tableHtml += `<tr>
                <td>${index + 1}</td>
                <td>${search.search == '' ? t('(blank / no search terms provided)') : search.search}</td>
                <td>${search.count}</td>
            </tr>`
        })
        $('#top-searches table tbody').empty();
        $('#top-searches table tbody').html(tableHtml);
    }

    async function drawTopResultsTable() {
        const topResults = await getTopResults(true, 5);
        let tableHtml = "";
        const ids = [];

        topResults.data.hits?.forEach((search) => {
            ids.push(search.hit)
        })

        const articles = await getArticlesMini(ids);
        
        articles.data.articles.forEach((article, index) => {
            const searchData = topResults.data.hits?.find(hit => hit.hit === article._id);

            tableHtml += `<tr>
                <td>${index + 1}</td>
                <td><a href=${$('#app_context').val()}/kb/${article.kb_permalink ?? article._id} target="_parent">${article.kb_title}</td>
                <td>${searchData.count}</td>
            </tr>`
        })
        $('#top-results table tbody').empty();
        $('#top-results table tbody').html(tableHtml);
    }

    async function getArticlesMini(ids = []) {
        return axios.post(`${$('#app_context').val()}/api/articles/details/mini`, {ids})
    }

    async function drawNoResultsTable() {
        const noResults = await getNoResults(5);
        let tableHtml = "";

        if(noResults.data.searches){
            noResults.data.searches.forEach((search, index) => {
                tableHtml += `<tr>
                    <td>${index + 1}</td>
                    <td>${search.search == '' ? t('(blank / no search terms provided)') : search.search}</td>
                    <td>${search.count}</td>
                </tr>`
            })
        }
        
        $('#no-results table tbody').empty();
        $('#no-results table tbody').html(tableHtml);
    }


    async function drawFullTopSearchesTable() {
        const topSearches = await getFullTopSearches(false);
        let tableHtml = "";
        console.log(topSearches)

        topSearches.data.searches.forEach((search, index) => {
            tableHtml += `<tr>
                <td>${index + 1}</td>
                <td>${search.search == '' ? t('(blank / no search terms provided)') : search.search}</td>
                <td>${search.count}</td>
            </tr>`
        })
        $('#full-top-searches table tbody').empty();
        $('#full-top-searches table tbody').html(tableHtml);
    }

    drawOverviewChart();
    drawTotalSearchChart();
    drawNoResultsRateChart();
    drawTopSearchesTable();
    drawTopResultsTable();
    drawNoResultsTable();

    const sta = moment().subtract(6, 'month');
	const end = new moment();
    $('.reporting-period').html(end.diff(sta, 'days') +" "+ t('days'))
    

    // function setViewAllTopSearchesUrl() {
    //     $('#top-searches-all').attr('href', `/wiki-admin/#${$('#app_context').val()}/search-analytics/top-searches/${$('#startDate').val()}/${$('#endDate').val()}`)
    // };
    // setViewAllTopSearchesUrl();
   
    // function setViewAllTopResultsUrl() {
    //     $('#top-results-all').attr('href', `/wiki-admin/#${$('#app_context').val()}/search-analytics/top-results/${$('#startDate').val()}/${$('#endDate').val()}`)
    // };
    // setViewAllTopResultsUrl();

    // function setViewAllTopNoResultsUrl() {
    //     $('#top-no-results-all').attr('href', `/wiki-admin/#${$('#app_context').val()}/search-analytics/top-no-results/${$('#startDate').val()}/${$('#endDate').val()}`)
    // };
    // setViewAllTopNoResultsUrl();

    $('#top-searches-all').on('click', function(e) {
        e.preventDefault();
        window.location.href = `${$('#app_context').val()}/search-analytics/top-searches/${$('#startDate').val()}/${$('#endDate').val()}`
    })

    $('#top-results-all').on('click', function(e) {
        e.preventDefault();
        window.location.href = `${$('#app_context').val()}/search-analytics/top-results/${$('#startDate').val()}/${$('#endDate').val()}`
    })

    $('#top-no-results-all').on('click', function(e) {
        e.preventDefault();
        window.location.href = `${$('#app_context').val()}/search-analytics/top-no-results/${$('#startDate').val()}/${$('#endDate').val()}`
    })

});
