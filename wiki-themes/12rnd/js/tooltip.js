/**
 * Custom Tooltip Implementation for Wiki
 * Inspired by portal-core tooltip with technology adaptations for jQuery + Bootstrap
 */

(function($) {
    'use strict';

    // Tooltip configuration
    const TOOLTIP_CONFIG = {
        arrowSize: 6,
        edgeOffset: 6,
        defaultPosition: 'top',
        fadeAnimation: true,
        showDelay: 300,
        hideDelay: 0,
        container: 'body'
    };

    // Store active tooltips for cleanup
    let activeTooltips = new Map();
    let tooltipIdCounter = 0;

    /**
     * Calculate arrow offset for precise positioning
     */
    function calculateArrowOffset(triggerPos, triggerSize, tooltipPos, tooltipSize, arrowSize, edgeOffset) {
        if (!triggerPos || !tooltipPos || !triggerSize || !tooltipSize) {
            return `calc(50% - ${arrowSize}px)`;
        }

        // Calculate the center of the trigger relative to the tooltip
        const triggerCenter = triggerPos + (triggerSize / 2);
        const tooltipStart = tooltipPos;
        const relativeCenter = triggerCenter - tooltipStart;

        // Ensure the arrow stays within tooltip bounds with an edge offset
        const minOffset = edgeOffset + arrowSize;
        const maxOffset = tooltipSize - edgeOffset - arrowSize;
        
        const clampedOffset = Math.max(minOffset, Math.min(maxOffset, relativeCenter));
        
        return `${clampedOffset}px`;
    }

    /**
     * Get the optimal tooltip position based on viewport constraints
     */
    function getOptimalPosition(trigger, tooltipElement, preferredPosition) {
        const $trigger = $(trigger);
        const $tooltip = $(tooltipElement);
        const $window = $(window);
        
        const triggerRect = trigger.getBoundingClientRect();
        const tooltipRect = tooltipElement.getBoundingClientRect();
        const viewportWidth = $window.width();
        const viewportHeight = $window.height();
        const scrollTop = $window.scrollTop();
        const scrollLeft = $window.scrollLeft();
        
        const positions = {
            top: {
                fits: triggerRect.top - tooltipRect.height - TOOLTIP_CONFIG.arrowSize > 0,
                priority: preferredPosition === 'top' ? 1 : 3
            },
            bottom: {
                fits: triggerRect.bottom + tooltipRect.height + TOOLTIP_CONFIG.arrowSize < viewportHeight,
                priority: preferredPosition === 'bottom' ? 1 : 3
            },
            left: {
                fits: triggerRect.left - tooltipRect.width - TOOLTIP_CONFIG.arrowSize > 0,
                priority: preferredPosition === 'left' ? 1 : 4
            },
            right: {
                fits: triggerRect.right + tooltipRect.width + TOOLTIP_CONFIG.arrowSize < viewportWidth,
                priority: preferredPosition === 'right' ? 1 : 4
            }
        };

        // Find the best position (preferred if it fits, otherwise best fitting alternative)
        let bestPosition = preferredPosition;
        let bestPriority = positions[preferredPosition].fits ? 1 : 999;

        for (const [pos, config] of Object.entries(positions)) {
            if (config.fits && config.priority < bestPriority) {
                bestPosition = pos;
                bestPriority = config.priority;
            }
        }

        // If no position fits perfectly, use preferred or fallback to top
        if (bestPriority === 999) {
            bestPosition = preferredPosition;
        }

        return bestPosition;
    }

    /**
     * Position tooltip relative to trigger element
     */
    function positionTooltip(trigger, tooltip, position) {
        const $trigger = $(trigger);
        const $tooltip = $(tooltip);
        const $window = $(window);
        
        // Use getBoundingClientRect for fixed positioning relative to viewport
        const triggerRect = trigger.getBoundingClientRect();
        const tooltipWidth = $tooltip.outerWidth();
        const tooltipHeight = $tooltip.outerHeight();
        const viewportWidth = $window.width();
        
        let left, top;
        
        switch (position) {
            case 'top':
                left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                top = triggerRect.top - tooltipHeight - TOOLTIP_CONFIG.arrowSize;
                break;
            case 'bottom':
                left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                top = triggerRect.bottom + TOOLTIP_CONFIG.arrowSize;
                break;
            case 'left':
                left = triggerRect.left - tooltipWidth - TOOLTIP_CONFIG.arrowSize;
                top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
                break;
            case 'right':
                left = triggerRect.right + TOOLTIP_CONFIG.arrowSize;
                top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
                break;
            default:
                // Fallback to 'top'
                left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                top = triggerRect.top - tooltipHeight - TOOLTIP_CONFIG.arrowSize;
        }

        // --- START: VIEWPORT BOUNDARY FIX ---
        // Adjust horizontal position to stay within viewport
        if (left < TOOLTIP_CONFIG.edgeOffset) {
            left = TOOLTIP_CONFIG.edgeOffset;
        } else if (left + tooltipWidth > viewportWidth - TOOLTIP_CONFIG.edgeOffset) {
            left = viewportWidth - tooltipWidth - TOOLTIP_CONFIG.edgeOffset;
        }

        // Adjust the vertical position to stay within the viewport
        if (top < TOOLTIP_CONFIG.edgeOffset) {
            top = TOOLTIP_CONFIG.edgeOffset;
        } else if (top + tooltipHeight > $window.height() - TOOLTIP_CONFIG.edgeOffset) {
            top = $window.height() - tooltipHeight - TOOLTIP_CONFIG.edgeOffset;
        }
        // --- END: VIEWPORT BOUNDARY FIX ---

        $tooltip.css({
            left: left + 'px',
            top: top + 'px'
        });

        // Calculate arrow offset after final positioning
        requestAnimationFrame(() => {
            try {
                // For arrow calculation, we need the final rendered position, so we use getBoundingClientRect again.
                const finalTooltipRect = tooltip[0].getBoundingClientRect();
                let arrowOffset;

                if (position === 'top' || position === 'bottom') {
                    arrowOffset = calculateArrowOffset(
                        triggerRect.left,
                        triggerRect.width,
                        finalTooltipRect.left,
                        finalTooltipRect.width,
                        TOOLTIP_CONFIG.arrowSize,
                        TOOLTIP_CONFIG.edgeOffset
                    );
                } else {
                    arrowOffset = calculateArrowOffset(
                        triggerRect.top,
                        triggerRect.height,
                        finalTooltipRect.top,
                        finalTooltipRect.height,
                        TOOLTIP_CONFIG.arrowSize,
                        TOOLTIP_CONFIG.edgeOffset
                    );
                }

                $tooltip.css('--arrow-offset', arrowOffset);

            } catch (error) {
                $tooltip.css('--arrow-offset', `calc(50% - ${TOOLTIP_CONFIG.arrowSize}px)`);
            }
        });
    }

    /**
     * Create tooltip element
     */
    function createTooltip(content, id) {
        const tooltip = $(`
            <div class="custom-tooltip${TOOLTIP_CONFIG.fadeAnimation ? ' tooltip-fade' : ''}" 
                 id="tooltip-${id}" 
                 role="tooltip">
                ${content}
            </div>
        `);
        
        return tooltip;
    }

    /**
     * Show tooltip
     */
    function showTooltip(trigger) {
        // --- START: RACE CONDITION FIX ---
        const existingTooltipData = activeTooltips.get(trigger);
        if (existingTooltipData) {
            // If a hide process is scheduled, cancel it and re-show the tooltip.
            if (existingTooltipData.hideTimeout) {
                clearTimeout(existingTooltipData.hideTimeout);
                existingTooltipData.hideTimeout = null;
                existingTooltipData.tooltip.addClass('show');
            }
            return; // Exit, as the tooltip is either visible or its removal has been cancelled.
        }
        // --- END: RACE CONDITION FIX ---
        
        const $trigger = $(trigger);
        const content = $trigger.attr('data-tooltip') || $trigger.attr('title');
        const position = $trigger.attr('data-position') || TOOLTIP_CONFIG.defaultPosition;
        const size = $trigger.attr('data-tooltip-size') || '';
        
        if (!content) return;

        // Remove title to prevent browser tooltip
        if ($trigger.attr('title')) {
            $trigger.attr('data-original-title', $trigger.attr('title'));
            $trigger.removeAttr('title');
        }

        const tooltipId = ++tooltipIdCounter;
        const tooltip = createTooltip(content, tooltipId);
        
        // Add size modifier if specified
        if (size && (size === 'small' || size === 'large')) {
            tooltip.addClass(`tooltip-${size}`);
        }

        // Append to container
        $(TOOLTIP_CONFIG.container).append(tooltip);
        
        // Get optimal position
        const optimalPosition = getOptimalPosition(trigger, tooltip[0], position);
        
        // Add position class
        tooltip.addClass(`tooltip-${optimalPosition}`);
        
        // Position tooltip
        positionTooltip(trigger, tooltip, optimalPosition);
        
        // Store reference
        activeTooltips.set(trigger, {
            tooltip: tooltip,
            id: tooltipId,
            hideTimeout: null // Initialize hide timeout
        });

        // Show tooltip
        setTimeout(() => {
            tooltip.addClass('show');
        }, TOOLTIP_CONFIG.showDelay);
    }

    /**
     * Hide tooltip
     */
    function hideTooltip(trigger) {
        const tooltipData = activeTooltips.get(trigger);

        if (!tooltipData) {
            return;
        }

        const { tooltip } = tooltipData;
        const $trigger = $(trigger);

        // Restore original title if it existed
        if ($trigger.attr('data-original-title')) {
            $trigger.attr('title', $trigger.attr('data-original-title'));
            $trigger.removeAttr('data-original-title');
        }

        // Hide tooltip visually
        tooltip.removeClass('show');
        
        // Remove from DOM after animation
        tooltipData.hideTimeout = setTimeout(() => {
            tooltip.remove();
            activeTooltips.delete(trigger);
        }, TOOLTIP_CONFIG.fadeAnimation ? 300 : 0);
    }

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        // Clean up existing tooltips
        activeTooltips.forEach((data, trigger) => {
            data.tooltip.remove();
        });
        activeTooltips.clear();

        // Find elements with tooltip data OR title attributes
        const tooltipElements = $('[data-tooltip], [title]');

        // Initialize new tooltips
        tooltipElements.each(function() {
            const $this = $(this);
            
            // Convert title attributes to data-tooltip
            if ($this.attr('title') && !$this.attr('data-tooltip')) {
                $this.attr('data-tooltip', $this.attr('title'));
                $this.removeAttr('title'); // Remove title to prevent browser tooltip
            }
            
            // Remove any existing event listeners to prevent duplicates
            $this.off('mouseenter.customTooltip mouseleave.customTooltip focus.customTooltip blur.customTooltip');
            
            // Add event listeners
            $this.on('mouseenter.customTooltip focus.customTooltip', function(e) {
                e.preventDefault();
                showTooltip(this);
            });
            
            $this.on('mouseleave.customTooltip blur.customTooltip', function(e) {
                e.preventDefault();
                hideTooltip(this);
            });
        });
    }

    /**
     * Destroy all tooltips
     */
    function destroyTooltips() {
        $('[data-tooltip]').off('.customTooltip');
        activeTooltips.forEach((data) => {
            data.tooltip.remove();
        });
        activeTooltips.clear();
    }

    /**
     * Update tooltip content
     */
    function updateTooltip(trigger, newContent) {
        const tooltipData = activeTooltips.get(trigger);
        if (tooltipData) {
            tooltipData.tooltip.html(newContent);
        }
        $(trigger).attr('data-tooltip', newContent);
    }

    // Global API
    window.CustomTooltip = {
        init: initTooltips,
        destroy: destroyTooltips,
        show: showTooltip,
        hide: hideTooltip,
        update: updateTooltip,
        config: TOOLTIP_CONFIG,
        // Debug helpers
        debug: {
            findElements: function() {
                const elements = $('[data-tooltip]');
                console.log('CustomTooltip Debug: Found', elements.length, 'tooltip elements:');
                elements.each(function(i, el) {
                    console.log(`Element ${i+1}:`, el, 'Content:', $(el).attr('data-tooltip'));
                });
                return elements;
            },
            testTooltip: function() {
                const elements = $('[data-tooltip]');
                if (elements.length > 0) {
                    console.log('Testing first tooltip element...');
                    showTooltip(elements[0]);
                } else {
                    console.log('No tooltip elements found to test');
                }
            },
            manualInit: function() {
                console.log('Manual initialization called...');
                initTooltips();
            }
        }
    };

    // Auto-initialize on DOM ready with delay to ensure all content is loaded
    $(document).ready(function() {
        initTooltips();
        
        // Also try again after a short delay in case content loads dynamically
        setTimeout(initTooltips, 500);
        
        // And one more time after page is fully loaded
        setTimeout(initTooltips, 2000);
    });

    // Re-initialize on dynamic content changes using modern MutationObserver
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            let shouldReinit = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if ($(node).find('[data-tooltip]').length || $(node).is('[data-tooltip]')) {
                                shouldReinit = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldReinit) {
                setTimeout(initTooltips, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    } else {
        // Fallback for older browsers
        setInterval(function() {
            const currentCount = $('[data-tooltip]').length;
            if (currentCount !== window.lastTooltipCount) {
                window.lastTooltipCount = currentCount;
                initTooltips();
            }
        }, 1000);
    }

    // Handle window resize
    $(window).on('resize', function() {
        // Hide all active tooltips on resize
        activeTooltips.forEach((data, trigger) => {
            hideTooltip(trigger);
        });
    });

})(jQuery);