// Enhanced video activation function for article viewing pages using shared VideoRenderer module
function activateArticleVideoPlayers() {
    // Simple check - if VideoRender<PERSON> isn't available, skip
    if (!window.videoRenderer || !window.videoRenderer.activateVideoPlayers) {
        console.warn('VideoRenderer not available, skipping video activation');
        return;
    }
    
    // Use shared video renderer for consistent behavior
    window.videoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        staggerDelay: 100,
        retryDelay: 200
    });
}

$(document).ready(function () {
	adjustSidebar();

	// adjust stickeness of siderbar
	$(window).on("resize", function () {
		adjustSidebar();
	});

	function adjustSidebar() {
		if ($(document).width() > 767) {

			const sidebarHeight = $('#articleRightSideMenu').height();
			const articleHeight = $(document).height();

			if (sidebarHeight >= articleHeight) {
				$('#articleRightSideMenu').removeClass('sticky');
			} else {
				$('#articleRightSideMenu').addClass('sticky');
			}
		} else {
			// Never stick articles on mobile devices...
			$('#articleRightSideMenu').removeClass('sticky');
		}
	}


	//about Yes/No Feedbacks --------------------------------------------------
	let helpful = $('#feedbackContainer').data('feedbackdata');
	let helpfulUpdated = $('#feedbackContainer').data('feedbackon');
	let comment = $('#commentSubmitted').data('commentdata');
	let commentUpdated = $('#commentSubmitted').data('commentedon');
	// Simple delayed activation - no complex waiting logic
	setTimeout(() => {
		activateArticleVideoPlayers();
		
		// Add retry mechanism for failed videos using the shared video renderer
		if (window.videoRenderer && window.videoRenderer.addRetryMechanism) {
			window.videoRenderer.addRetryMechanism(['#article-body', '.body_text'], 3000);
		}
	}, 1000);
	
	// Expose function globally for use by other scripts
	window.activateArticleVideoPlayers = activateArticleVideoPlayers;


	$("#btnHelpfulYes").on('click', function () {
		if ($(this).hasClass('helpful-yes')) {
			//console.log('Cannot yes again..');
		} else {
			//console.log('Yes, recording feedback...');
			setHelpfulOrNot(true);
			//set btn variant
			$(this).toggleClass('helpful-yes');
			//set btnNo back to secondary
			if ($('#btnHelpfulNo').hasClass('helpful-no')) {
				$('#btnHelpfulNo').toggleClass('helpful-no');
			}
			//allow user to input feedback
			$('#commentContainer').show(100);
		}
	});

	$("#btnHelpfulNo").on('click', function () {
		if ($(this).hasClass('helpful-no')) {
			//console.log('Cannot no again..');
		} else {
			//console.log('No, recording feedback...');
			setHelpfulOrNot(false);
			//set btn variant
			$(this).toggleClass('helpful-no');
			//set btnYes back to secondary
			if ($('#btnHelpfulYes').hasClass('helpful-yes')) {
				$('#btnHelpfulYes').toggleClass('helpful-yes');
			}
			//allow user to input feedback
			$('#commentContainer').show(100);
		}
	});


	$("#btnPrintArticle").on("click", function () {
		console.log("Printing...");

		let pageContents = [],
			css = [];
		let articleContents = $("#articles-list-wrap");

		let article = articleContents.html();

		pageContents.push(article);

		var additionalCss = `

				@import url("https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css");
				@import url("https://fonts.googleapis.com/css2?family=Khula:wght@400;600&display=swap");

				body {
					padding: 20px;
					font-family: 'Khula', sans-serif;
				}
				h1,
				h2,
				h3,
				h4,
				h5,
				h6,
				.h1,
				.h2,
				.h3,
				.h4,
				.h5,
				.h6 {
				font-family: inherit;
				font-weight: 300;
				line-height: 1.65;
				color: #292f33;
				}

				h2,
				.h2 {
				font-size: 38px;
				font-weight: 700;
				}
				#breadcrumb {display: none}
				.accordions .collapse {
					display: block;
				}
			`;
		console.log('pageContents', pageContents.length)
		if (pageContents && pageContents.length) {
			let w = window.open("", "width=400, height=400");

			let title = $("#articleContents h1#title").text();
			w.document.head.innerHTML = `
					<style>${css.join("\n") + additionalCss}</style>
					<title>${title}</title>
				`;
			w.document.body.innerHTML = pageContents.join("");

			w.print();
			w.close();
		}
	});


	//user types in feedback comment ---------------------------
	$('#txtFeedbackComments').on('keyup', function () {
		let txt = $(this).val();
		let ctr = $('#feedbackCounter');
		let limit = 1000;

		if (txt.length > 0 && txt.length <= limit) {
			//update counter
			ctr.text(limit - txt.length);
		} else if (txt.length > limit) {
			//cutoff text based on limit
			$(this).val(txt.substring(0, limit));
		} else {
			ctr.text(`${limit}`);
		}

		//auto height add
		$(this).height("auto").height($(this)[0].scrollHeight);

	});

	$('#txtFeedbackComments').on('click', function () {
		$('#divFeedbackControls').show();
		if ($('#txtFeedbackComments').val().length > 1) {
			//when there is comment
			$(this).height("auto").height($(this)[0].scrollHeight);
		} else {
			//set height to 150px
			$(this).height("auto").height(150);
		}
	});

	$('#txtFeedbackComments').on('blur', function () {
		if ($('#txtFeedbackComments').val().length > 0) {
			$('#divFeedbackControls').show();
		} else {
			$('#feedbackCounter').text("1000");
			$('#divFeedbackControls').hide(100);
		}
	});

	$('#btnSubmitFeedbackComment').on('click', function () {
		let comment = $('#txtFeedbackComments').val();
		if (comment.length > 10) {
			setComment(comment);
		} else {
			$('#feedbackErrorMessage').show();
		}
	});


	$('#linkEditComment').on('click', function () {
		//console.log('Editing comment');
		$('#commentContainer').show(100);
		$('#commentSubmitted').hide(100);
	});



	//hide feedback controls on first load
	$('#divFeedbackControls').hide(100);
	$('#feedbackErrorMessage').hide(100);


	//check first the feedback date, not the helpful because it will default to No
	if (helpfulUpdated && helpfulUpdated.length > 0) {
		//let wasHelpful = JSON.parse(helpful.toLowerCase());
		//let convertedDate = JSON.parse(helpfulUpdated);
		//then remove the dashes (-) in the helpfulUpdated, nice trick, lol
		// let hoverText = `You rated this article on ${convertedDate.replace(/-/g, " ")}`;
		let hoverText = `You rated this article on ${helpfulUpdated}`;

		if (helpful) {
			$('#btnHelpfulYes').toggleClass('helpful-yes');
			$("#btnHelpfulYes").attr('title', hoverText);
			$("#btnHelpfulNo").removeAttr('title');
		} else {
			$('#btnHelpfulNo').toggleClass('helpful-no');
			$("#btnHelpfulNo").attr('title', hoverText);
			$("#btnHelpfulYes").removeAttr('title');
		}

		//will only evaluate comments if Yes/No is answered
		if (commentUpdated && commentUpdated.length > 0) {
			//hide textarea and show quoted comment
			$('#commentContainer').hide(100);
			$('#commentSubmitted').show(100);

			//set values
			$('#txtFeedbackComments').text(comment);
			//limit chars to 100 and add ...
			$('#commentSubmitted .comment-submitted').text(`" ${comment.substring(0, 100) + '...'} "`);
			$('#commentSubmitted .comment-submitted-on').text(`- Your comment on ${commentUpdated}`);
		} else {
			$('#commentContainer').show(100);
			$('#commentSubmitted').hide(100);
		}
	} else {
		//dont show feedback box
		$('#commentContainer').hide(100);
		$('#commentSubmitted').hide(100);
	}

	$('#table-of-contents li a').on('click', function (e) {
		e.preventDefault();
		$('#table-of-contents li a').removeClass('active');
		$(this).addClass('active');
		var target = $(this.getAttribute('href'));


		$('.main-content').animate({
			scrollTop: target.position().top
		}, 500);
	});


	$('.main-content').on('scroll', function () {
	
		$($('#table-of-contents li').get().reverse()).each(function (index) {
			const target = $(this).find('a').attr('href');

			if (target) {
				const targetTop = $(target).position().top;
				const scrollTop =  $('.main-content').scrollTop()
			
				if (targetTop - scrollTop  < 50 ) {
			
					$('#table-of-contents li a').removeClass('active');
					$(this).find('a').addClass('active');
					return false;
				}
			}

		})
	})
});


// -- FUNCTIONS ---------------------------------------------------------------------------------
//show toast
const Toast = Swal.mixin({
	toast: true,
	position: "top-end",
	showConfirmButton: false,
	timer: 2000,
	timerProgressBar: false,
	didOpen: (toast) => {
		toast.addEventListener("mouseenter", Swal.stopTimer);
		toast.addEventListener("mouseleave", Swal.resumeTimer);
	},
	showClass: {
		backdrop: "swal2-noanimation", // disable backdrop animation
		popup: "", // disable popup animation
	},
});


async function setComment(comment) {
	let articleId = $('#doc_id').val();

	await $.ajax({
		method: 'POST',
		url: $('#app_context').val() + '/comments',
		data: { comment: comment, articleId }
	})
		.done(function (res) {
			Toast.fire({
				icon: "success",
				title: "Thanks for your comment",
			});

			//hide textarea and show quoted comment
			$('#commentContainer').hide(100);
			$('#commentSubmitted').show(100);

			//set values
			$('#commentSubmitted .comment-submitted').text(comment);
			$('#commentSubmitted .comment-submitted-on').text('- ' + t('Your comment just now'));

		})
		.fail(function () {
			console.log('Error while doing save.');
		});
}

async function setHelpfulOrNot(helpful) {
	let articleId = $('#doc_id').val();

	await $.ajax({
		method: 'POST',
		url: $('#app_context').val() + '/helpfulornot',
		data: {
			helpful: helpful,
			articleId: articleId
		}
	})
		.done(function (res) {
			Toast.fire({
				icon: "success",
				title: t("Thanks for your feedback"),
			});

		})
		.fail(function () {
			console.log('Error while doing save.');
		});
}