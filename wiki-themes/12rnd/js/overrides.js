jQuery(document).ready(function($){
    window.dispatchEvent(new Event('resize'));
    
    if (typeof CustomTooltip !== 'undefined') {
        CustomTooltip.init();
    }
});


$(window).on('resize', function(){
    var win = $(this);

    if (win.height() <= 770) {
        $(".searchBar").addClass("searchbar-sm");
        $(".home-view").addClass("home-view-sm");
    } else {
        $(".searchBar").removeClass("searchbar-sm");
        $(".home-view").removeClass("home-view-sm");
    }

    if (win.height() <= 700) {
        $(".article-details").hide();
    } else {
        $(".article-details").show();
    }

    if (win.height() <= 570) {
        //$(".home-view").css({"overflow": "scroll"});
        $(".subheading").hide();
    } else {
        //$(".home-view").css({"overflow": "hidden"});
        $(".subheading").show();
    }
});
