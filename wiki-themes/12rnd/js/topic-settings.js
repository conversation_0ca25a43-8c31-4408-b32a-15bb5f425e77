var currentTopicId = null;
var creatingTopic = false;

let expanded = false;
let selectedCountries = [];

// events --------------------------------------------------------------
$(document).ready(async function () {
    await initTranslator();
    clearDetails();
    getTopicsList();

    //hide topic details first
    $('#topics-card').hide();

    $('#btnNewSubtopic').on('click', function() {

        let newInput = $(`
            <input type="text" value="" name="subtopic-" class="form-control form-control-sm mb-1"
            style="background: #ecf6ff;">
        `).hide();
        $(`#cardSubtopics`).append(newInput);
        newInput.show(100);

        //focus
        $(`#cardSubtopics input`).last().focus();

    });

    $('#btnSaveTopicChanges').on('click', function(e) {
        e.preventDefault();

        //small validation
        if($('#topic').val() == '') {
            alert('Please specify Topic.');
            $('#topic').focus();
        } else if($('#displayOrder').val() == '') {
            alert('Please specify Order.');
            $('#displayOrder').focus();
        } else {

            let form = $(`#topicDetailsForm`).serializeArray();

            saveTopic(form);
        }

    })//end of savetopicchanges

    $('#enableSwitch').on('change', function() {
        if($(this).is(':checked')) {
            $('#enableSwitchLabel').text(t('Enabled'))
        } else {
            $('#enableSwitchLabel').text(t('Disabled'))
        }
    })

    $('#btnNewTopic').on('click', function(e) {
        e.preventDefault();
        creatingTopic = true;

        $('#topics-card').show();

        clearDetails();

        $('#btnSaveTopicChanges').prop('disabled', false);
        $('#btnNewSubtopic').prop('disabled', false);

        $('#displayOrder').focus();

        //enabled by default
        $('#enableSwitch').prop('checked', true);
        $('#enableSwitchLabel').text(t('Enabled'));
        $('#topic_regional_visibility').val('');
        $('#optionSelectTopicCountry').text('Select Country');
    })

    $('#btnDeleteTopic').on('click', function() {
        if (confirm('Delete this topic and sub-topics?')) {
            deleteTopic(currentTopicId);
            alert('Topic deleted permanently.');
        }
    })



        //all about countries dropdown
        let selectTopicCountry = $('#selectTopicCountry');
        let countriesData = selectTopicCountry.data('options');

        let countries = countriesData.split(',');
        
        for(let i=0; i< countries.length;i++){
            let countryId = countries[i].replace(/\s/g, '');
            $('#countryCheckboxes').append(`
                <label for="dropCheck-${countryId}" class="countryLabel" id="countryLabel-${countryId}">
                    <input type="checkbox" id="dropCheck-${countryId}"
                    class="mr-2 countryCheck" data-country="${countries[i]}" />${countries[i]}
                </label>
            `)
        }

        $("#countryCheckboxes").css('display', 'none');





        $('body').on('click', function (e) {
            let clickTarget = e.target.id;

            if(clickTarget == 'overSelect'){
                var checkboxes = document.getElementById("countryCheckboxes");
                if (!expanded) {
                    checkboxes.style.display = "block";
                    //adjust width a bit
                    $('#countryCheckboxes').css('width', $('.overSelect').width());
                    expanded = true;
                } else {
                    checkboxes.style.display = "none";
                    expanded = false;
                }
            }

            else {
                //if the user clicks the dropdown checkbox - don't hide
                if(clickTarget.includes('countryLabel') || clickTarget.includes('dropCheck')) {
                    $('#countryCheckboxes').css('width', $('.overSelect').width());
                    expanded = true;
                } else {

                    if (expanded){
                        $("#countryCheckboxes").css('display', 'none');
                        expanded = false;
                    }
                }
            }

        });

        //event when checked one of country checkboxes
        $('.countryCheck').on('change', function () {
            let country = $(this).data('country');

            if ($(this).is(":checked"))
            {
                selectedCountries.push(country);
            } else {
                selectedCountries = selectedCountries.filter(function(elem){
                    return elem != country;
                });

            }


            //set country dropdown text to selectedCountries
            let countriesString = '';
            if(selectedCountries.length > 0) {
                selectedCountries.forEach(c => {
                    countriesString.length > 0 ? countriesString += `, ${c}` : countriesString += c;
                })

                regionalValue = countriesString;
                $('#optionSelectTopicCountry').text(countriesString);
                $('#topic_regional_visibility').val(countriesString);
            } else {
                $('#optionSelectTopicCountry').text('Select Country');
                $('#topic_regional_visibility').val('');
            }
        })
}); //end of document.ready()


async function getTopicDetails(topicId) {
    creatingTopic = false;
    if(topicId) {
        await $.ajax({
            method: 'GET',
            url: $('#app_context').val() + `/topic_details/${topicId}`,
        })
        .done(function (res) {
            if(res)
            displayDetails(res.data);
        })
        .fail(function () {
            console.log('Error occured while on getting topic details')
        });
    }
}

async function getTopicsList() {
        clearDetails();
        const userLang = $('#user_lang').val() ?? 'en';

        await $.ajax({
            method: 'GET',
            url: $('#app_context').val() + `/topics_list/all`,
        })
        .done(function (res) {
            if(res && res.data){
                $('#topicsListGroup button').remove();

                res.data.map((topic) => {
                    
                    $('#topicsListGroup').append(`
                        <button type="button" class="list-group-item list-group-item-action btn-sm topic-text"
                            data-topicid="${topic._id}">${topic.display_order} ${topic.topic}  ${topic.topicTranslations && topic.topic !== topic.topicTranslations[userLang] ? '('+topic.topicTranslations[userLang]+')' : ''}</button>
                    `);

                })

                //clicks one list item button
                $('.list-group-item-action').on('click', function() {

                    //remove all actives to others
                    $('.topic-list .list-group-item').removeClass("active");
                    //set current active item
                    $(this).addClass('active');

                    currentTopicId = $(this).data('topicid');

                    clearDetails();
                    getTopicDetails(currentTopicId);
                })
            }
        })
        .fail(function () {
            console.log('Error occured while getting topics')
        });
}

function displayDetails(topic) {
    console.log('topic', topic)
    selectedCountries = [];
    $('#topics-card').show();
    const userLang = $('#user_lang').val() ?? 'en';
    
    $('#hiddenTopicId').val(currentTopicId);
    $('#displayOrder').val(topic.display_order);
    $('#topic').val(topic.topic);
    $('#svg').val(topic.svg);
    $('#keyword').val(topic.keyword);

    if(topic.topic_regional_visibility) {

        $('#topic_regional_visibility').val(topic.topic_regional_visibility);
        $('#optionSelectTopicCountry').text(topic.topic_regional_visibility);

        //check if multiple countries
        if(topic.topic_regional_visibility.includes(',')){
            let regionals = topic.topic_regional_visibility.split(',');
            regionals.forEach(r => {
                let countryId = r.replace(/\s/g, '').trim();

                //checks the checkbox
                $("input[id='dropCheck-"+ countryId +"']").prop('checked', true);
                selectedCountries.push(r.trim());
            });
        } else {
            let countryId = topic.topic_regional_visibility.replace(/\s/g, '');

            $(`#dropCheck-${countryId}`).prop('checked', true);
            selectedCountries.push(topic.topic_regional_visibility);

        }

    } else {
        $('#optionSelectTopicCountry').text('Select Country');
        $('#topic_regional_visibility').val('');
    }

    if(topic.enabled == 'true') {
        $('#enableSwitch').prop('checked', true);
        $('#enableSwitchLabel').text(t('Enabled'))
    }

    if(topic.subtopics) {
        topic.subtopics.map(s => {
            let newInput = $(`
            <div class="input-group">
            <input title="${s.subtopic} ${s.subtopicTranslations && s.subtopic !== s.subtopicTranslations[userLang] ? '('+s.subtopicTranslations[userLang]+')' : ''}" type="text" value="${s.subtopic}" name="subtopic-" class="form-control form-control-sm mb-1">
                <div class="input-group-append">
                    <button type="button" class="btn btn-default btn-sm btnDeleteSubtopic" data-subtopic="subtopic-${s.subtopic}">
                    &nbsp;<i class="fa fa-close"></i>&nbsp;</button>
                </div>
            </div>

            `).hide();
            $(`#cardSubtopics`).append(newInput);
            newInput.show(100);
        })

        $('.btnDeleteSubtopic').on('click', function() {
            let subtopic = $(this).data('subtopic');

            $(this).parent().prev('input').remove();
            (this).remove();
        });
    }


    $('#btnNewSubtopic').prop('disabled', false);
    $('#btnSaveTopicChanges').prop('disabled', false);
    $('#btnDeleteTopic').css('visibility', 'visible');
}

function clearDetails() {

    $('#displayOrder').val('');
    $('#topic').val('');
    $('#svg').val('');
    $('#keyword').val('');
    $('#enableSwitchLabel').text(t('Disabled'))
    $('#enableSwitch').prop('checked', false);

    $('#topic_regional_visibility').val('');
    $('#countryCheckboxes .countryCheck').prop('checked', false);

    $('#optionSelectTopicCountry').val('');
    $(`#cardSubtopics input`).remove();
    $(`#cardSubtopics button`).remove();

    $('#btnNewSubtopic').prop('disabled', true);
    $('#btnSaveTopicChanges').prop('disabled', true);
    $('#btnDeleteTopic').css('visibility', 'hidden');
}

async function saveTopic(form) {
    var saveData = {};
    let subtopics = [];
    let subtopicIndex = 1;
    let enableSwitch = false;

    //for enabled switch
    if($(`#enableSwitch`).is(':checked')) {
        enableSwitch = "true";
    }
    $.map(form, function(n, i){
        //subtopics stored in 1 array
        if(n['name'].includes('subtopic-')) {
            subtopics.push({
                id: subtopicIndex,
                subtopic: n['value']
            });
            subtopicIndex++;
        } else {
            saveData[""+ n['name']] = n['value'];
        }

    });

    //other fields
    saveData.enabled = enableSwitch;
    saveData.subtopics = JSON.stringify(subtopics);

    let saveUrl = $('#app_context').val() + '/update_topic';
    if(creatingTopic) {
        saveUrl = $('#app_context').val() + '/create_topic';
    }

    await $.ajax({
        method: 'POST',
        url: saveUrl,
        data: saveData
    })
    .done(function (res) {
        if(res.message == 'success'){
            if(creatingTopic) {
                //reload list
                getTopicsList();
                currentTopicId = null;
                creatingTopic = false;

            } else {
                alert('Changes Saved!')

            }
            $('#topics-card').hide();

        } else {
            alert('Error')
        }
    })
    .fail(function () {
        console.log('Error while doing save.')
    });
}

async function deleteTopic(topicId) {
    creatingTopic = false;

    await $.ajax({
        method: 'POST',
        url: $('#app_context').val() + `/delete_topic/${topicId}`,
    })
    .done(function (res) {
        if(res.message == 'success'){
            //reload list
            getTopicsList();
            currentTopicId = null;

        } else {
            alert('Error')
        }
    })
    .fail(function () {
        console.log('Error while deleting.')
    });

}
