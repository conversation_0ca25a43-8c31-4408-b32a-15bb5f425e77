version: '3.4'
services:

  openkb-db:
    image: mongo:4.4
    container_name: openkb-db
    privileged: true
    # restart: always
    command: mongod --port 27019 --logpath /dev/null --oplogSize 128 --quiet
    networks:
      - portal-net
    ports:
      - 27019:27019
    volumes:
      - ./openkb-data:/data/db
      - ./openkb-data-dump:/dump

    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo openkb-db:27019/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s

  openkb:
    #image: docker-registry.aws.gymsystems.co/backoffice/wiki:latest
    build: .
    container_name: openkb

    privileged: true

    # Now exposed via SSO service...
    ports:
      - "8080:4444"
      # - "7000:7000"
      - "7001:7001"

    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - SHOW_CONFIG=true

    command: bash start-dev.sh
    stdin_open: true
    tty: true

    volumes:
      # Map in source files locally (for developing)
      - ./src:/var/openKB

      # Map in DB & Uploads Directory (so they are not lost)
      - ./data:/var/openKB/data
      - ./uploads:/var/openKB/public/uploads

      # Map in configuration & themes...
      - ./wiki-config:/var/openKB/config
      - ./wiki-themes:/var/openKB/public/themes

    networks:
      - portal-net

    healthcheck:
      test: curl -sS http://127.0.0.1:4444/healthcheck || exit 1
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  portal-net:
    external: true
