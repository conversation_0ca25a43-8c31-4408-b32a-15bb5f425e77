# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a 12RND Blog/Wiki application based on openKB - a Node.js knowledge base/FAQ/Wiki platform built with Express.js, MongoDB, and Handlebars templating. The application supports multi-tenancy and includes features like article management, search (via Algolia), user authentication, and theme customization.

## Key Architecture Components

### Multi-tenant Architecture
- Uses MongoDB with tenant isolation via database separation
- `globalStore` (lib/globalStore.js) manages tenant context using async local storage
- Tenant IDs are prefixed with `TENANT_ID_PREFIX` environment variable
- Database connections are cached and managed per tenant in `lib/db.js`

### Core Application Structure
- **Entry Point**: `src/app.js` - Express application setup with tenant initialization
- **Routes**: Located in `src/routes/` - main routing logic (index.js, api.js, mods.js)
- **Database Layer**: `src/lib/db.js` - MongoDB connection management with tenant support
- **Common Utilities**: `src/routes/common.js` - shared functions, config reading, translations
- **Views**: `src/views/` - Handlebars templates with extensive helper functions
- **Static Assets**: `src/public/` and themed assets in `wiki-themes/12rnd/`

### Translation System
- Uses `application-translator` package for internationalization
- Language files in `src/locales/` and CSV files in `src/csv/`
- Translation API integration via `TRANSLATOR_API` environment variable
- `initTranslations()` function in common.js handles request-level translation setup

### Search Integration
- Algolia search integration in `src/lib/algolia.js`
- Article synchronization and index management
- Search results handled in frontend JavaScript

### Theme System
- Custom theme support with 12rnd theme in `wiki-themes/12rnd/`
- SCSS compilation via Gulp
- Theme-specific views, CSS, and JavaScript
- Configuration-based theme switching

## Development Commands

### Core Commands
```bash
# Start the application (from root directory)
npm start  # Uses docker-compose-dev.yml

# Start the application (from src directory)
cd src && npm start  # Runs node app.js directly

# Build assets and translations
cd src && npm run build  # Runs gulp build task

# Watch for changes during development
cd src && npm run watch  # Runs gulp watch with browsersync on port 7000
```

### Docker Development
```bash
# Start with Docker (development)
npm start  # Equivalent to: docker-compose -f docker-compose-dev.yml up

# Stop Docker containers
npm run down  # Equivalent to: docker-compose -f docker-compose-dev.yml down
```

### Asset Building (Gulp)
```bash
cd src
gulp          # Build CSS and translations
gulp watch    # Watch mode with browsersync (proxy localhost:4444 on port 7000)
```

## Configuration

### Environment Variables
- `MONGO_CONN` - MongoDB connection string
- `MONGO_DBNAME` - Default database name
- `MONGO_OPTIONS` - MongoDB connection options
- `TENANT_ID_PREFIX` - Prefix for tenant database names (default: 'wiki-dev-')
- `TRANSLATOR_API` - Translation service API endpoint

### Config Files
- `src/config/config.json` - Main application configuration
- `wiki-config/config.json` - Alternative config location
- Environment-specific configs in `config/environment/` and `wiki-config/environment/`

## Database

### MongoDB Collections
- Multi-tenant setup with separate databases per tenant
- Article storage with markdown content
- User management and authentication
- Topic/category organization
- File uploads and media management

### Migrations
- Migration system in `src/lib/migrations.js`
- Migration files in `src/migrate/` directory
- Automatic execution during tenant database initialization

## Key Files for Development

- `src/app.js` - Main application entry point and Express setup
- `src/routes/common.js` - Shared utilities and configuration management
- `src/lib/db.js` - Database connection and tenant management
- `src/lib/globalStore.js` - Tenant context management
- `wiki-themes/12rnd/` - Custom theme assets and templates
- `src/gulpfile.js` - Asset build pipeline and development workflow

## Application Context

The application supports running under a URL prefix via the `app_context` setting in config.json. All routes and static assets respect this context path.

## Testing and Quality

The application includes basic error handling and logging but does not appear to have a formal test suite. Manual testing can be done via:
- Main application: http://localhost:4444/
- With app context: http://localhost:4444/{app_context}/
- Development with browsersync: http://localhost:7000/ (when using gulp watch)