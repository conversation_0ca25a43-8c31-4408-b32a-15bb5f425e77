websockets.js:9 WS Connect: nV2hvUeXzVJz5JA1AAAL
js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:1478   GET https://maps.googleapis.com/maps/api/mapsjs/gen_204?csp_test=true net::ERR_BLOCKED_BY_CLIENT
_.B.send @ js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:1478
Sca @ js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:1460
Uca @ js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:1452
google.maps.Load @ js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:14
(anonymous) @ js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:1689
(anonymous) @ js?key=AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys&libraries=places&_=1751262791844:1689
libraries.js:30 [Mon Jun 30 2025 13:53:14 GMT+0800 (Philippine Standard Time)] main-container runRoute get /wiki-admin/#%2Fopenkb%2Fedit%2F686221c483e47c17f4945980%3Fclub%3DDemoclub
router.js:550 Checking firstLoad inside setActivePath: /wiki-admin true
router.js:553 First load condition met. Expanding menu for: b.fn.init {0: a.menu-toggle.waves-effect.waves-block.waves-slate-dark.toggled, length: 1, prevObject: b.fn.init}
router.js:563 firstLoad flag set to false inside setActivePath after expansion
router.js:570 expandActiveMenuOnFirstLoad false
router.js:283 wiki postload logic run /openkb/edit/686221c483e47c17f4945980?club=Democlub
router.js:291 change wiki url: /openkb/edit/686221c483e47c17f4945980?club=Democlub
router.js:550 Checking firstLoad inside setActivePath: /wiki-admin false
router.js:550 Checking firstLoad inside setActivePath: /wiki-admin false
router.js:550 Checking firstLoad inside setActivePath: /wiki-admin false
router.js:550 Checking firstLoad inside setActivePath: /wiki-admin false
router.js:550 Checking firstLoad inside setActivePath: /wiki-admin false
router.js:624 Welcome wizard currently disabled, and needs to be re-factored based on the users permissions.
tooltip.js:428  Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at tooltip.js:428:18
    at tooltip.js:451:3
(anonymous) @ tooltip.js:428
(anonymous) @ tooltip.js:451
686221c483e47c17f4945980:316   GET http://localhost:3000/openkb/images/help-icon.svg 404 (Not Found)
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:27.412Z] INFO: StreamingVideoRenderer initialized {options: {…}, processedVideos: 0, processingVideos: 0}
video-renderer.js:1289 🎥 Streaming video debugging enabled - console cleared for clean logs
edit-article.js:15 🎥 Video debugging enabled - monitoring all video transmission
wiki.js:61 sWikiURI b.Event {originalEvent: Event, type: 'load', target: iframe#wikiContainer, currentTarget: iframe#wikiContainer, isDefaultPrevented: ƒ, …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.136Z] DEBUG: Found containers {selector: '#preview', count: 1}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.136Z] INFO: Target containers collected {selectors: Array(1), totalContainers: 1}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.136Z] INFO: Activating streaming video players {config: {…}, containerCount: 1, targetContainers: Array(1)}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.136Z] INFO: Processing video containers {containerCount: 0, selectors: Array(1)}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.136Z] INFO: Processing file-to-proxy elements {elementCount: 1, selectors: Array(1)}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.238Z] NETWORK: Processing video URL {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:28.238Z] NETWORK: Detected secure upload URL
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.238Z] NETWORK: Removed /private from URL {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', processedUrl: '/api/admin/secure-upload/video/70ef945e-0575-4218-…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.238Z] NETWORK: URL Analysis {isSecureUpload: true, hasPrivatePrefix: true, hasSignedParams: false, hasExpiration: false, protocol: '', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.238Z] STREAMING: Loading secure video with metadata {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', metadataUrl: '/api/admin/secure-upload/video/70ef945e-0575-4218-…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.238Z] STREAMING: Attempting to fetch metadata {metadataUrl: '/api/admin/secure-upload/video/70ef945e-0575-4218-…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.890Z] STREAMING: Metadata fetch response received {status: 200, statusText: 'OK', ok: true, url: 'http://localhost:3000/api/admin/secure-upload/vide…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', headers: {…}}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.891Z] STREAMING: Metadata received {size: '61338', organisationId: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 'processed', created: 1751261678.945, message: '', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.891Z] STREAMING: Direct S3 URLs constructed {bucket: 'ph-uploads-stg', s3Key: 'video/70ef945e-0575-4218-b301-7c54f16d758b.mov', region: 'ap-southeast-2', directS3Urls: Array(2), objectUrl: 'Not provided in metadata'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.891Z] STREAMING: Checking processing status {status: 'processed', fileType: 'video', message: '', progress: 100}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.891Z] WARN: Unknown processing status, assuming ready {status: 'processed', fileType: 'video'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.891Z] STREAMING: Organization ID resolution {fromUrl: true, fromMetadata: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', fromUserSession: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', finalOrganisationId: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', queryString: '?organisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.892Z] STREAMING: S3 Key analysis {originalS3Key: 'video/70ef945e-0575-4218-b301-7c54f16d758b.mov', currentType: 'video', filename: '70ef945e-0575-4218-b301-7c54f16d758b.mov', detectedType: 'video', correctS3Key: 'video/70ef945e-0575-4218-b301-7c54f16d758b.mov'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.892Z] STREAMING: 🔗 Primary URL {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.892Z] STREAMING: Constructed streaming URLs {metadata: {…}, originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', primaryUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', fallbackCount: 14}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.892Z] STREAMING: Creating streaming player with fallback support {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', primaryUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', fallbackCount: 14}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.892Z] STREAMING: Testing URL access {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.903Z] STREAMING: URL access test result {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.903Z] STREAMING: ❌ URL 1/15 not accessible, trying next {failedUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 14, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.903Z] STREAMING: Testing URL access {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.912Z] STREAMING: URL access test result {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.912Z] STREAMING: ❌ URL 2/15 not accessible, trying next {failedUrl: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 13, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.912Z] STREAMING: Testing URL access {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.919Z] STREAMING: URL access test result {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.919Z] STREAMING: ❌ URL 3/15 not accessible, trying next {failedUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 12, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.919Z] STREAMING: Testing URL access {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.925Z] STREAMING: URL access test result {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.925Z] STREAMING: ❌ URL 4/15 not accessible, trying next {failedUrl: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 11, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:28.925Z] STREAMING: Testing URL access {url: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
edit-article.js:78 📝 Editor detected, activating video players
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:28.993Z] INFO: Editor ready, starting video activation
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.495Z] DEBUG: Found containers {selector: '#preview', count: 1}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.496Z] INFO: Target containers collected {selectors: Array(1), totalContainers: 1}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.496Z] INFO: Activating streaming video players {config: {…}, containerCount: 1, targetContainers: Array(1)}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.496Z] INFO: Processing video containers {containerCount: 1, selectors: Array(1)}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.496Z] DEBUG: Processing individual video container {videoUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', delay: 0, isProcessing: false, isProcessed: true}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.496Z] INFO: Re-processing previously processed video {videoUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.497Z] INFO: Processing file-to-proxy elements {elementCount: 0, selectors: Array(1)}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.498Z] NETWORK: Processing video URL {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:29.498Z] NETWORK: Detected secure upload URL
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.498Z] NETWORK: Removed /private from URL {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', processedUrl: '/api/admin/secure-upload/video/70ef945e-0575-4218-…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.498Z] NETWORK: URL Analysis {isSecureUpload: true, hasPrivatePrefix: true, hasSignedParams: false, hasExpiration: false, protocol: '', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.498Z] STREAMING: Loading secure video with metadata {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', metadataUrl: '/api/admin/secure-upload/video/70ef945e-0575-4218-…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.498Z] STREAMING: Attempting to fetch metadata {metadataUrl: '/api/admin/secure-upload/video/70ef945e-0575-4218-…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.961Z] STREAMING: Metadata fetch response received {status: 200, statusText: 'OK', ok: true, url: 'http://localhost:3000/api/admin/secure-upload/vide…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', headers: {…}}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.963Z] STREAMING: Metadata received {size: '61338', organisationId: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 'processed', created: 1751261678.945, message: '', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.963Z] STREAMING: Direct S3 URLs constructed {bucket: 'ph-uploads-stg', s3Key: 'video/70ef945e-0575-4218-b301-7c54f16d758b.mov', region: 'ap-southeast-2', directS3Urls: Array(2), objectUrl: 'Not provided in metadata'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: Checking processing status {status: 'processed', fileType: 'video', message: '', progress: 100}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] WARN: Unknown processing status, assuming ready {status: 'processed', fileType: 'video'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: Organization ID resolution {fromUrl: true, fromMetadata: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', fromUserSession: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', finalOrganisationId: 'c7d7e6cc-99e5-4939-9956-bb5e32a42418', queryString: '?organisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: S3 Key analysis {originalS3Key: 'video/70ef945e-0575-4218-b301-7c54f16d758b.mov', currentType: 'video', filename: '70ef945e-0575-4218-b301-7c54f16d758b.mov', detectedType: 'video', correctS3Key: 'video/70ef945e-0575-4218-b301-7c54f16d758b.mov'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: 🔗 Primary URL {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: Constructed streaming URLs {metadata: {…}, originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', primaryUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', fallbackCount: 14}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: Creating streaming player with fallback support {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', primaryUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', fallbackCount: 14}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.964Z] STREAMING: Testing URL access {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.980Z] STREAMING: URL access test result {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.980Z] STREAMING: ❌ URL 1/15 not accessible, trying next {failedUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 14, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.980Z] STREAMING: Testing URL access {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.988Z] STREAMING: URL access test result {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.988Z] STREAMING: ❌ URL 2/15 not accessible, trying next {failedUrl: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 13, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.988Z] STREAMING: Testing URL access {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.999Z] STREAMING: URL access test result {url: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:29.999Z] STREAMING: ❌ URL 3/15 not accessible, trying next {failedUrl: '/admin/secure-upload/public/video/70ef945e-0575-42…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 12, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.000Z] STREAMING: Testing URL access {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.008Z] STREAMING: URL access test result {url: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'text/html; charset=UTF-8', contentLength: '26401', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.008Z] STREAMING: ❌ URL 4/15 not accessible, trying next {failedUrl: '/admin/secure-upload/private/video/70ef945e-0575-4…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 11, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.008Z] STREAMING: Testing URL access {url: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:501   HEAD http://localhost:3000/api/admin/secure-upload/public/video/70ef945e-0575-4218-b301-7c54f16d758b.mov?organisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418 404 (Not Found)
_testUrlAccess @ video-renderer.js:501
_createStreamingPlayerWithFallback @ video-renderer.js:462
await in _createStreamingPlayerWithFallback
_loadSecureVideo @ video-renderer.js:263
await in _loadSecureVideo
_loadVideo @ video-renderer.js:160
(anonymous) @ video-renderer.js:1177
setTimeout
_convertFileProxyToVideoContainer @ video-renderer.js:1176
(anonymous) @ video-renderer.js:1154
each @ jquery.min.js:2
each @ jquery.min.js:2
_processFileProxyElements @ video-renderer.js:1153
activateVideoPlayers @ video-renderer.js:88
activateAllVideoPlayers @ edit-article.js:43
(anonymous) @ openKB.js:478
setTimeout
initMarkdownEditor @ openKB.js:477
(anonymous) @ openKB.js:566
await in (anonymous)
e @ jquery.min.js:2
t @ jquery.min.js:2
setTimeout
(anonymous) @ jquery.min.js:2
c @ jquery.min.js:2
fireWith @ jquery.min.js:2
fire @ jquery.min.js:2
c @ jquery.min.js:2
fireWith @ jquery.min.js:2
ready @ jquery.min.js:2
B @ jquery.min.js:2
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.256Z] STREAMING: URL access test result {url: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 404, statusText: 'Not Found', contentType: 'application/vnd.apple.mpegurl; charset=utf-8', contentLength: '28', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.257Z] ERROR: Streaming endpoint returned 404 - check backend route configuration {url: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', expectedBackendRoute: 'Should match /api/admin/secure-upload/{public|private}/:type/:key', troubleshooting: 'Verify portal-core backend is running and routes are registered'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.257Z] STREAMING: ❌ URL 5/15 not accessible, trying next {failedUrl: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 10, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:30.257Z] STREAMING: Testing URL access {url: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:501   HEAD http://localhost:3000/api/admin/secure-upload/public/video/70ef945e-0575-4218-b301-7c54f16d758b.mov?organisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418 404 (Not Found)
_testUrlAccess @ video-renderer.js:501
_createStreamingPlayerWithFallback @ video-renderer.js:462
await in _createStreamingPlayerWithFallback
_loadSecureVideo @ video-renderer.js:263
await in _loadSecureVideo
_loadVideo @ video-renderer.js:160
(anonymous) @ video-renderer.js:1140
setTimeout
_processVideoContainer @ video-renderer.js:1139
(anonymous) @ video-renderer.js:1104
each @ jquery.min.js:2
each @ jquery.min.js:2
_processVideoContainers @ video-renderer.js:1103
activateVideoPlayers @ video-renderer.js:85
activateAllVideoPlayers @ edit-article.js:43
setTimeout
waitForEditor @ edit-article.js:82
setTimeout
(anonymous) @ edit-article.js:90
await in (anonymous)
e @ jquery.min.js:2
t @ jquery.min.js:2
setTimeout
(anonymous) @ jquery.min.js:2
c @ jquery.min.js:2
fireWith @ jquery.min.js:2
fire @ jquery.min.js:2
c @ jquery.min.js:2
fireWith @ jquery.min.js:2
ready @ jquery.min.js:2
B @ jquery.min.js:2
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.324Z] STREAMING: URL access test result {url: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 404, statusText: 'Not Found', contentType: 'application/vnd.apple.mpegurl; charset=utf-8', contentLength: '28', …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.325Z] ERROR: Streaming endpoint returned 404 - check backend route configuration {url: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', expectedBackendRoute: 'Should match /api/admin/secure-upload/{public|private}/:type/:key', troubleshooting: 'Verify portal-core backend is running and routes are registered'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.325Z] STREAMING: ❌ URL 5/15 not accessible, trying next {failedUrl: '/api/admin/secure-upload/public/video/70ef945e-057…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', remainingUrls: 10, isLastUrl: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.325Z] STREAMING: Testing URL access {url: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.677Z] STREAMING: URL access test result {url: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'application/vnd.apple.mpegurl', contentLength: null, …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.678Z] STREAMING: ✅ URL 6/15 accessible, creating streaming player {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', urlIndex: 5, isSecureUpload: true, hasOrganisationId: true}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.678Z] STREAMING: 🎬 Creating streaming player {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', hasMetadata: true, containerType: 'DIV'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.679Z] STREAMING: 📊 Media type detection {isAudio: null, streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', metadataFileType: 'video'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:31.679Z] STREAMING: 📺 Creating media element
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:31.680Z] STREAMING: 🔄 Replacing loading message with media player
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.680Z] STREAMING: 🔧 Initializing streaming {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.680Z] STREAMING: Initializing streaming {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', hlsSupported: true, dashSupported: true, plyrAvailable: true}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:31.681Z] STREAMING: 🔍 Unknown content type, detecting from response
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.681Z] STREAMING: ✅ Streaming player created successfully {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', isAudio: null}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:31.883Z] STREAMING: 📊 Post-initialization status {readyState: 0, readyStateText: 'HAVE_NOTHING', networkState: 0, networkStateText: 'NETWORK_EMPTY', src: '', …}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:31.883Z] STREAMING: 🔄 Video not loading automatically, triggering load()
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.742Z] STREAMING: URL access test result {url: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', status: 200, statusText: 'OK', contentType: 'application/vnd.apple.mpegurl', contentLength: null, …}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.743Z] STREAMING: ✅ URL 6/15 accessible, creating streaming player {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', urlIndex: 5, isSecureUpload: true, hasOrganisationId: true}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.743Z] STREAMING: 🎬 Creating streaming player {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', hasMetadata: true, containerType: 'DIV'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.743Z] STREAMING: 📊 Media type detection {isAudio: null, streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', metadataFileType: 'video'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:32.743Z] STREAMING: 📺 Creating media element
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:32.743Z] STREAMING: 🔄 Replacing loading message with media player
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.744Z] STREAMING: 🔧 Initializing streaming {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.744Z] STREAMING: Initializing streaming {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', hlsSupported: true, dashSupported: true, plyrAvailable: true}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:32.744Z] STREAMING: 🔍 Unknown content type, detecting from response
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.744Z] STREAMING: ✅ Streaming player created successfully {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', isAudio: null}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:32.945Z] STREAMING: 📊 Post-initialization status {readyState: 0, readyStateText: 'HAVE_NOTHING', networkState: 0, networkStateText: 'NETWORK_EMPTY', src: '', …}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:32.945Z] STREAMING: 🔄 Video not loading automatically, triggering load()
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:33.063Z] STREAMING: 🔍 Content type detected {contentType: 'application/vnd.apple.mpegurl', streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:33.063Z] STREAMING: 🎵 HLS detected from content-type
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:33.063Z] STREAMING: Initializing HLS player {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:34.094Z] STREAMING: 🔍 Content type detected {contentType: 'application/vnd.apple.mpegurl', streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:34.094Z] STREAMING: 🎵 HLS detected from content-type
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:34.094Z] STREAMING: Initializing HLS player {streamingUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:35.302Z] STREAMING: ▶️ Media play started {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0, paused: false, readyState: 0}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:38.904Z] STREAMING: 📊 Metadata loaded {duration: 3, readyState: 'HAVE_METADATA'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:38.926Z] STREAMING: ✅ Can play
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:38.926Z] STREAMING: 🎵 Media actually playing {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:38.935Z] STREAMING: 🔍 Media seeking {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:38.936Z] STREAMING: ⏳ Media waiting for more data {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0.1, buffered: '0.033333-2.133333'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:38.945Z] STREAMING: 🎯 Media seeked {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:38.945Z] STREAMING: ✅ Can play
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:38.945Z] STREAMING: 🎵 Media actually playing {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:40.835Z] STREAMING: ⏸️ Media paused {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 1.917334, ended: false}
window.enableVideoDebug()
VM1620:1 Uncaught TypeError: window.enableVideoDebug is not a function
    at <anonymous>:1:8
(anonymous) @ VM1620:1
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:48.585Z] STREAMING: ▶️ Media play started {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 1.977333, paused: false, readyState: 4}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:48.588Z] STREAMING: 🎵 Media actually playing {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:48.813Z] STREAMING: ⏸️ Media paused {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 2.133333, ended: true}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:48.813Z] STREAMING: 🏁 Media ended {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:49.861Z] STREAMING: 🔍 Media seeking {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:49.864Z] STREAMING: ▶️ Media play started {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0, paused: false, readyState: 1}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:49.864Z] STREAMING: ⏳ Media waiting for more data {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0, buffered: '0.033333-2.133333'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:49.890Z] STREAMING: 🎯 Media seeked {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:49.897Z] STREAMING: ✅ Can play
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:49.897Z] STREAMING: 🎵 Media actually playing {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:50.961Z] STREAMING: ⏸️ Media paused {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0.994332, ended: false}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:51.740Z] STREAMING: ▶️ Media play started {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0, paused: false, readyState: 0}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:53.826Z] STREAMING: 📊 Metadata loaded {duration: 3, readyState: 'HAVE_METADATA'}
video-renderer.js:24 [StreamingVideoRenderer] [2025-06-30T05:53:53.889Z] STREAMING: ✅ Can play
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:53.889Z] STREAMING: 🎵 Media actually playing {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418'}
video-renderer.js:22 [StreamingVideoRenderer] [2025-06-30T05:53:54.930Z] STREAMING: ⏸️ Media paused {originalUrl: '/api/admin/secure-upload/private/video/70ef945e-05…ganisationId=c7d7e6cc-99e5-4939-9956-bb5e32a42418', currentTime: 0.979898, ended: false}
