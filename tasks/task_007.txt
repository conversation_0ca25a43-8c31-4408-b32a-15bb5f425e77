# Task ID: 7
# Title: Enhance Permalink Display
# Status: pending
# Dependencies: 2, 5
# Priority: medium
# Description: Implement a new permalink display below the article title and hide existing permalink controls.
# Details:
1. Modify the title section in src/views/form.hbs to include permalink display
2. Style the permalink display to be visually distinct but not intrusive
3. Hide the existing permalink input field and related buttons
4. Preserve all permalink-related JavaScript functions for future use
5. Implement a copy-to-clipboard functionality for the permalink
6. Add a tooltip to explain the permalink's purpose
7. Ensure the permalink updates dynamically if the title changes

# Test Strategy:
1. Verify permalink displays correctly below the title
2. Test that hidden permalink controls don't affect functionality
3. Ensure copy-to-clipboard feature works across browsers
4. Check that tooltip appears and is accessible
5. Validate that permalink updates correctly with title changes
6. Test permalink display on various screen sizes
