# Task ID: 6
# Title: Implement Conditional Preview Panel
# Status: pending
# Dependencies: 2, 5
# Priority: medium
# Description: Modify the preview panel to be visible only when the markdown editor mode is enabled.
# Details:
1. Update #preview-wrap visibility logic in src/views/form.hbs
2. Modify changeToMarkdown() and changeToWysiwyg() functions in src/public/javascripts/openKB.js
3. Implement a CSS class for hiding/showing the preview panel
4. Ensure smooth transitions when toggling preview visibility
5. Maintain existing preview synchronization functionality
6. Update any event listeners related to preview panel
7. Implement responsive behavior for preview panel on smaller screens

# Test Strategy:
1. Test switching between Markdown and WYSIWYG modes
2. Verify preview panel appears only in Markdown mode
3. Check that preview synchronization still works correctly
4. Test preview panel behavior on different screen sizes
5. Ensure no console errors occur during editor mode switches
6. Validate that hiding preview doesn't affect other layout elements
