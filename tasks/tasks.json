{"tasks": [{"id": 2, "title": "Implement New Layout Structure", "description": "Create the new layout structure with a main editor area and right column for form elements, utilizing the existing design system in theme-ph.scss.", "status": "pending", "dependencies": [], "priority": "high", "details": "1. Modify src/views/form.hbs to create the new two-column layout\n2. Use CSS Grid or Flexbox for the main layout structure\n3. Implement responsive breakpoints using Bootstrap 5 grid system\n4. Create placeholder containers for the editor area and right column\n5. Style the layout using the existing design system in wiki-themes/12rnd/scss/theme-ph.scss\n6. Leverage the slate color variables ($slate-25 through $slate-800) for text and background colors\n7. Apply appropriate shadow variables ($natural-shadow-*) for depth and elevation\n8. Use the predefined border radius variables ($border_radius-*) for consistent component styling\n9. Implement card styling using $base-card-padding, $card-padding, and $card-border-radius\n10. Apply slate colors ($slate-25, $slate-50, $slate-75) for different background levels\n11. Use accent colors ($accent-blue-*) for interactive elements and highlights\n12. Style form elements using the input variables ($input-border-color, $border-radius-input, $input-shadow)\n13. Ensure the layout is responsive and works on all target screen sizes\n14. Use surface colors only for borders, specifically:\n    - $surface-100 for main border color ($border-color)\n    - $surface-75 for tab borders ($tab-border-color)\n    - Do not use surface colors for backgrounds", "testStrategy": "1. Verify layout on desktop, tablet, and mobile viewports\n2. Test responsiveness using Chrome DevTools device emulation\n3. Ensure no horizontal scrollbars appear on any screen size\n4. Validate HTML structure using W3C Validator\n5. Test layout with different content lengths\n6. Verify that all design tokens from theme-ph.scss are applied correctly\n7. Check that the styling is consistent with the existing design system\n8. Test color contrast for accessibility compliance\n9. Verify that shadows and border radii are applied consistently\n10. Confirm surface colors are only used for borders and not backgrounds\n11. Validate that slate colors are properly used for background styling\n12. Check that $surface-100 is used for main borders and $surface-75 for tab borders", "subtasks": []}, {"id": 3, "title": "Remove Existing Side Panel", "description": "Remove the collapsible right-side panel and associated functionality.", "details": "1. Remove .settings-wrap HTML structure from src/views/form.hbs\n2. Delete related SCSS from wiki-themes/12rnd/scss/newstyles.scss\n3. Remove JavaScript for panel toggling in wiki-themes/12rnd/js/edit-article.js\n4. Remove #toggleNavSmallBtn and its event listeners\n5. Clean up any references to .settings-wrap, .settings-toggle, and .expanded classes\n6. Remove localStorage persistence for panel state\n7. Update any dependencies in other JavaScript files that might reference the removed elements", "testStrategy": "1. Verify that the side panel no longer appears in the UI\n2. Check console for any JavaScript errors related to removed elements\n3. Test that removing the panel doesn't break any existing functionality\n4. Ensure localStorage no longer contains panel state information\n5. Verify that the layout adjusts correctly without the panel", "priority": "medium", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 4, "title": "Implement Card-based Right Column", "description": "Create three vertically stacked cards in the right column for Publish Settings, Security Settings, and Topic Management using the established design system.", "status": "pending", "dependencies": [2, 3], "priority": "high", "details": "1. Create a new partial hbs file for the right column cards\n2. Implement card structure using the existing .ph-card component classes (.ph-card, .ph-card_header, .ph-card_body)\n3. Reference the theme-ph.scss file for styling consistency\n4. Apply slate color variables ($slate-*) for text and borders instead of generic gray colors\n5. Use predefined shadow variables ($natural-shadow-xs, $natural-shadow-sm) for card elevation\n6. Apply border radius from $card-border-radius and other $border_radius-* variables\n7. Use $base-card-padding and $card-padding variables for consistent spacing\n8. Use $surface-100 ($border-color) for card borders\n9. Use $surface-75 for any tab-related borders if applicable\n10. For card backgrounds, use slate colors ($slate-25, $slate-50, $slate-75) or white\n11. Apply input styling variables for form elements within cards\n12. Use $accent-blue-* colors for buttons and interactive elements\n13. Ensure proper spacing between cards and internal elements\n14. Implement responsive behavior for cards on smaller screens", "testStrategy": "1. Verify cards render correctly on all target screen sizes\n2. Test card layout with varying content lengths\n3. Ensure card styling is consistent across browsers\n4. Validate accessibility of card structure using aXe or WAVE\n5. Test keyboard navigation between cards and their elements\n6. Confirm styling matches the established design system\n7. Verify correct implementation of theme variables, especially surface colors for borders and slate colors for backgrounds\n8. Check that cards follow the existing .ph-card pattern", "subtasks": []}, {"id": 5, "title": "Migrate Form Elements to Cards", "description": "Move existing form elements from their current locations to the appropriate cards in the right column.", "details": "1. Carefully move each form element to its designated card:\n   - Publish Card: frm_kb_published, frm_kb_visible_state, frm_kb_regional_visibility, frm_kb_language, frm_kb_featured\n   - Security Card: frm_kb_security_level, frm_kb_password, frm_kb_admin_restricted\n   - Topics Card: selectTopics, selectSubtopics\n2. Preserve all input names, IDs, and data attributes\n3. Maintain all validation attributes and aria labels\n4. Update any JavaScript selectors that reference moved elements\n5. Ensure Bootstrap form styles are applied correctly in the new location\n6. Implement custom styling for multi-select and complex inputs\n7. Use CSS Grid or Flexbox for form layout within cards", "testStrategy": "1. Verify all form elements are present in their new locations\n2. Test form submission to ensure all data is correctly sent\n3. Validate that all JavaScript event handlers still work\n4. Check that validation attributes are functioning\n5. Test accessibility of form elements using screen readers\n6. Ensure form elements are styled consistently within cards", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Implement Conditional Preview Panel", "description": "Modify the preview panel to be visible only when the markdown editor mode is enabled, using the theme-ph.scss design system.", "status": "pending", "dependencies": [2, 5], "priority": "medium", "details": "1. Update #preview-wrap visibility logic in src/views/form.hbs\n2. Modify changeToMarkdown() and changeToWysiwyg() functions in src/public/javascripts/openKB.js\n3. Implement a CSS class for hiding/showing the preview panel\n4. Apply theme-ph.scss design system variables:\n   - Use $slate-* variables for text and panel backgrounds ($slate-25, $slate-50, $slate-75 or white)\n   - Use $surface-100 ($border-color) for panel borders\n   - Apply $border_radius-* for consistent corner styling\n   - Implement $natural-shadow-* for panel elevation if needed\n   - Use form styling variables for any controls within the panel\n   - Apply $accent-blue-* for interactive elements and highlights\n5. Ensure smooth transitions when toggling preview visibility\n6. Maintain existing preview synchronization functionality\n7. Update any event listeners related to preview panel\n8. Implement responsive behavior for preview panel on smaller screens", "testStrategy": "1. Test switching between Markdown and WYSIWYG modes\n2. Verify preview panel appears only in Markdown mode\n3. Check that preview synchronization still works correctly\n4. Validate design system implementation:\n   - Confirm correct color variables are applied (slate colors for backgrounds, surface-100 for borders)\n   - Verify border radius consistency with other UI elements\n   - Check shadow implementation if used\n   - Test that accent colors are properly applied to interactive elements\n5. Test preview panel behavior on different screen sizes\n6. Ensure no console errors occur during editor mode switches\n7. Validate that hiding preview doesn't affect other layout elements\n8. Verify the styling is consistent with other components using the theme-ph.scss system", "subtasks": []}, {"id": 7, "title": "<PERSON><PERSON><PERSON> Display", "description": "Implement a new permalink display below the article title and hide existing permalink controls, using the theme-ph.scss design system.", "status": "pending", "dependencies": [2, 5], "priority": "medium", "details": "1. Modify the title section in src/views/form.hbs to include permalink display\n2. Style the permalink display using the theme-ph.scss design system:\n   - Use $secondary-text-color and $slate-400 for permalink text\n   - Apply .ph-secondary-text-color class for consistent styling\n   - Use $surface-100 ($border-color) for any permalink container borders if needed\n   - For any background elements, use slate colors ($slate-25, $slate-50, $slate-75) or white\n   - Follow existing typography styles from the design system\n   - Apply $border_radius-* variables for any containers\n   - Use $accent-blue-* colors for interactive elements (e.g., copy button)\n3. Hide the existing permalink input field and related buttons\n4. Preserve all permalink-related JavaScript functions for future use\n5. Implement a copy-to-clipboard functionality for the permalink\n6. Add a tooltip to explain the permalink's purpose\n7. Ensure the permalink updates dynamically if the title changes", "testStrategy": "1. Verify permalink displays correctly below the title\n2. Confirm the styling matches the theme-ph.scss design system\n3. Verify the permalink text uses the correct slate color variables\n4. Test that hidden permalink controls don't affect functionality\n5. Ensure copy-to-clipboard feature works across browsers\n6. Check that tooltip appears and is accessible\n7. Validate that permalink updates correctly with title changes\n8. Test permalink display on various screen sizes\n9. Verify the permalink styling is consistent with other secondary text elements\n10. Confirm that any background elements use appropriate slate colors or white, not surface colors", "subtasks": []}, {"id": 8, "title": "Implement Responsive Behavior", "description": "Ensure the new layout works across all device sizes using the theme-ph.scss design system.", "status": "pending", "dependencies": [2, 4, 5, 6, 7], "priority": "high", "details": "1. Implement responsive behavior using the theme-ph.scss design system\n2. Utilize existing responsive mixins and breakpoints defined in the theme\n3. Use mobile-first approach for CSS styling\n4. Implement the following responsive behavior:\n   - Desktop (>992px): Side-by-side layout\n   - Tablet (768px-992px): Right column moves below editor\n   - Mobile (<768px): Single column, cards stack vertically\n5. Apply consistent spacing and sizing variables from the design system\n6. Use Slate color variables ($slate-*) for responsive text and UI elements\n7. Implement card and layout variables that scale appropriately across devices\n8. Use surface colors correctly:\n   - $surface-100 ($border-color) and $surface-75 ($tab-border-color) for borders only\n   - For backgrounds that scale across devices, use slate colors ($slate-25, $slate-50, $slate-75) or white\n9. Adjust shadow variables ($natural-shadow-*) as needed for mobile displays\n10. Maintain consistent border radius variables ($border_radius-*) across breakpoints\n11. Use CSS Flexbox for flexible component layouts\n12. Implement responsive typography using rem units and design system variables\n13. Optimize images and media for different screen sizes\n14. Ensure touch targets are appropriately sized on mobile", "testStrategy": "1. Test layout on various devices and orientations\n2. Use Chrome DevTools device emulation for thorough testing\n3. Verify that no horizontal scrollbars appear on any screen size\n4. Check that all interactive elements are easily tappable on mobile\n5. Test performance on low-end mobile devices\n6. Validate that content is readable and not truncated on small screens\n7. Verify that design system variables are correctly applied across breakpoints\n8. Ensure color contrast meets accessibility standards on all device sizes\n9. Confirm that shadow effects render appropriately on different pixel densities\n10. Test that border radius consistency is maintained across all components and screen sizes\n11. Verify surface colors are only used for borders and slate colors or white are used for backgrounds", "subtasks": []}, {"id": 9, "title": "Preserve and Update JavaScript Functionality", "description": "Maintain all existing JavaScript functionality and update event bindings for the new layout.", "details": "1. Review and update all JavaScript files, focusing on:\n   - src/public/javascripts/openKB.js\n   - wiki-themes/12rnd/js/edit-article.js\n2. Update selectors to match new HTML structure\n3. Maintain form submission logic (submitEditArticleForm, submitNewArticleForm)\n4. Preserve editor switching between Markdown and WYSIWYG\n5. Update preview synchronization and scrolling logic\n6. Maintain topic/subtopic cascading dropdowns functionality\n7. Preserve regional visibility multi-select functionality\n8. Update validation and error handling for new layout\n9. Maintain auto-save functionality if present\n10. Implement error boundaries and better error logging", "testStrategy": "1. Comprehensive testing of all JavaScript functionality\n2. Verify form submissions work correctly\n3. Test editor mode switching and preview synchronization\n4. Ensure all dropdowns and multi-selects function properly\n5. Validate that error handling works in the new layout\n6. Test auto-save functionality if applicable\n7. Use Jest for unit testing critical JavaScript functions", "priority": "high", "dependencies": [5, 6, 7, 8], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement New Styling and Design System", "description": "Apply the theme-ph.scss design system to the modernized editor interface for consistent styling across all components.", "status": "pending", "dependencies": [2, 4, 5, 7, 8], "priority": "medium", "details": "1. Update wiki-themes/12rnd/scss/newstyles.scss to implement theme-ph.scss design system\n2. Apply slate color variables ($slate-25 through $slate-800) to replace any gray/dark/light colors\n3. Implement shadow variables ($natural-shadow-lg, $natural-shadow-md, $natural-shadow-sm, $natural-shadow-xs) for depth\n4. Apply border radius variables ($border_radius-xs through $border_radius-xl) consistently\n5. Use surface colors ($surface-100, $surface-75) only for borders as defined in the theme\n6. Use slate colors ($slate-25, $slate-50, $slate-75) or white for layered backgrounds\n7. Implement accent colors ($accent-blue-*) for interactive elements and states\n8. Apply input styling variables ($input-border-color, $border-radius-input, $input-shadow)\n9. Utilize existing component classes (.ph-card, .ph-btn, .ph-form, .ph-alert, etc.)\n10. Ensure consistent typography using $primary-text-color and $secondary-text-color\n11. Implement smooth transitions for interactive elements", "testStrategy": "1. Visual inspection across different browsers\n2. Verify consistent styling across all components\n3. Test hover and focus states for all interactive elements\n4. Ensure color contrast meets WCAG 2.1 AA standards\n5. Validate that all slate colors are properly applied with no legacy gray/dark/light colors\n6. Confirm all shadow variables are correctly implemented\n7. Verify border radius consistency across all UI elements\n8. Validate surface colors are only used for borders, not backgrounds\n9. Confirm slate colors or white are used for layered backgrounds\n10. Validate accent colors on all interactive elements\n11. Ensure typography follows the design system specifications", "subtasks": []}, {"id": 13, "title": "Implement Accessibility Enhancements", "description": "Ensure the modernized editor meets or exceeds existing accessibility standards.", "details": "1. Implement proper ARIA labels and roles\n2. Ensure keyboard navigation works for all interactive elements\n3. Implement focus management for modals and dynamic content\n4. Ensure color contrast meets WCAG 2.1 AA standards\n5. Implement skip links for keyboard users\n6. Ensure form inputs have associated labels\n7. Implement error messaging for screen readers\n8. Ensure all images have alt text\n9. Implement aria-live regions for dynamic content updates\n10. Test and adjust heading hierarchy for logical structure", "testStrategy": "1. Use aXe or WAVE for automated accessibility testing\n2. Manually test with screen readers (NVDA, VoiceOver)\n3. Perform keyboard-only navigation testing\n4. Validate color contrast using WebAIM Color Contrast Checker\n5. Conduct usability testing with users who have disabilities\n6. Verify that all form errors are announced by screen readers", "priority": "high", "dependencies": [5, 7, 10], "status": "pending", "subtasks": []}, {"id": 15, "title": "Documentation and Handover", "description": "Create comprehensive documentation for the modernized editor and prepare for project handover.", "status": "pending", "dependencies": [2, 3, 4, 5, 6, 7, 8, 9, 10, 13], "priority": "medium", "details": "1. Update README.md with new project setup instructions\n2. Create a CHANGELOG.md to document all changes\n3. Update or create API documentation for any modified endpoints\n4. Document the new layout structure and component hierarchy\n5. Create a style guide documenting the design system\n6. Document all new JavaScript functions and their purposes\n7. Create user documentation for any new features or changes in UX\n8. Document known issues and future considerations\n9. Create a migration guide for any breaking changes\n10. Prepare a presentation for the development team on the modernization efforts", "testStrategy": "1. <PERSON><PERSON> review all documentation for accuracy and completeness\n2. Verify that setup instructions work on a clean environment\n3. Ensure all new features and changes are properly documented\n4. Validate that API documentation is up-to-date and accurate\n5. Confirm that the style guide reflects the actual implementation\n6. Test the migration guide on a sample legacy project", "subtasks": []}]}