en,ja
Live View
Backup Media
View Support Guides for In Club Screens in the Wiki
Workout/Program Preview
Load Selected Program
Display/Device Control <i>(All Displays)
Displays Off
Displays On
Workout Timer
Round Remaining
There are no screens setup yet for this location
Add your first screen!
"If this is your first time configuring, don't forget to read the <a href=""/wiki/#%2Fopenkb%2Fkb%2FhUkCBRc1YayENccs"">Installation Guide</a> available in the Performance Hub's How To Resources before starting."
Program Preview
Filter Coaching Screens
Add New Screen
Hide devices offline for more than 1 month
Use Backup Media if your network is offline or you are experiencing issues with one or more devices. Backup Media will provide a minimal summary of the current workouts without video content.
"Before resorting to Backup Media, be sure to complete the <a href=""/wiki/#%2Fopenkb%2Fkb%2Ftroubleshooting-in-club-screens"">troubleshooting steps</a> outlined in the Wiki."
Steps to use Backup Media
"Provide one or more USB Flash Drive(s) for each screen that requires Backup Media. We suggest purchasing the <a href=""https://www.amazon.com.au/SanDisk-Cruzer-Blade-Drive-SDCZ50-016G-B35/dp/B002U1ZBG0/"" target=""_blank"">16GB SanDisk Cruzer Blade</a> drives."
"Insert the USB Flash Drive in your computer, and format with a FAT32 file-system using the <a target=""_blank"" href=""https://www.sdcard.org/downloads/formatter/"">SD Format Tool</a>. This tool is available for free download for both Windows and Mac Operating Systems."
"Download Backup Media from the Performance Hub, Unzip the downloaded file, and copy ONE FILE (for the offline device) to the root folder of the USB drive."
"Plug in the USB Flash Drive directly into the spare USB port on your Compute Stick or NUC (Computer connected to the TV). If the computer is offline, you may also be able to plug the USB into the TV directly if the TV has a USB port."
Download Backup Media
Boxing Timer: Start requested
Boxing Timer: Stop on all devices requested
Timer Audio Un-Muted
Timer Audio Muted
Today's Program
Not Today's Program