en,ja
Devices
Global Configuration
View Support Guides for Technology in Knowledge Base
Filter Devices
Add New Device
Never
Hide devices offline for more than 1 month
There are no devices setup yet for this location
Device Name
Device UUID,"デバイス識別子"
Metadata
Current Internet Speed (Updated
Download
Upload
Latency
Updated
This speed test was initiated from the device
Connected Display
Serial Number
Device Serial Number
Display Serial Number
Display Audio Support
to the server
Local IP,"ローカルアイピー"
Wifi Name,"ワイファイ名"
Wifi Experience,"ワイファイエクスペリエンス"
"Activity Down/Up"
Uptime
Not Found
Unknown
Build
Released
Supervisor
OS
Channels
Serial Number
Updates Available
Equipment: #1 - Boxing 1
Equipment: #2 - Boxing 2
Equipment: #3 - Floor to ceiling
Equipment: #4 - Strength 1
Equipment: #5 - Strength 2
Equipment: #6 - Speedball
Equipment: #7 - Sled
Equipment: #8 - Boxing 4
Equipment: #9 - Boxing 5
Equipment: #10 - Cell
Equipment: #11 - Strength 3
Equipment: #12 - Strength 4
Ethernet Connected
Software Information
"#1 - Boxing 1"
#2 - Boxing 2
#3 - Floor to ceiling
#4 - Strength 1
#5 - Strength 2
#6 - Speedball
#7 - Sled
#8 - Boxing 4
#9 - Boxing 5
#10 - Cell
#11 - Strength 3
#12 - Strength 4
Round Timer
Error sending command to one or more devices
Configuration Saved - The device may automatically restart for your changes to take effect.
Error sending command to one or more devices
FAILED TO SAVE DEVICE CONFIGURATION!
An error occurred while trying to save the device configuration changes.<br>Please contact HQ for further details.
Device is currently Offline
Data displayed is the last know data recorded from the device.<br> Updates will be automatically applied when device reconnects next.
Last known details
Wifi Signal,"ワイファイ信号"
Wifi Connection,"ワイファイ信号接続"
Connection
Connect
Device Details
Pairing ID
Networks with the highest value will be connected first. Networks the same priority value will be connected in the order of the last connection time.
SSID
Looking to add Sonos?
"Sonos devices are automatically detected by your Coaching Screens and do not need to be manually added into the Performance Hub."
Last seen
Increasing the Underscan will move your content to the center of the display - this is useful if part of the image or text is cut off on your display.
Underscan
Wifi Configuration stored on Device
Failed to change Wifi Network - Please check the Password (PSK) is correct and the Wifi Network is still reachable. Network settings have been reverted to your previous configuration.
Network configuration successfully changed to new network:
Equipment Associated
Display Orientation
Rotate 90° Left
Rotate 90° Right
Inverted (Upside Down)
Turn Display ON
Turn Display OFF
Restart Chromium
"If you have slow internet, or your cameras are struggling to upload video and view the live stream simultaneously, try setting a lower resolution."
Highest Quality (Larger files)
Lowest Quality (Use with slow internet)
Motion Triggered
Adjust Image Settings
Remotely Reboot Device
Reboot Devices
Connect to New Wifi Network
Change Wifi Networks,"ワイファイネットワークを変更する"
Manage Wifi Networks
Changing Wifi Networks
Setup & Debugging
Type
Security Mode
Password
Mode
IPv4
IPv6
Connection Priority
Debug On
Debug Off
Capture Screenshot
Load Remote URL
Motion Threshold
"The Motion Threshold is the number of pixels changed (between video frames) to 'trigger' a recording. A lower value will trigger recordings with less movement in front of the camera, whereas higher values will result in more motion required to trigger a recording."
Select or Enter New Location
Main Room
Bathroom
Please allow 1-2 minutes for the device to safely shut down all services and restart.
Enter the full URL you would like to load - include the http://
https://ubxtraining.com/what
Image Settings
Getting settings from camera
Connecting to Camera
Device may restart
Device will restart
Configuration Mask Saved - The device will automatically restart for your changes to take effect
Loading Default Camera Mask
Fetching Data from Camera
Load/Reset Defaults
Camera Lens Type
Millimeter
Configuration Saved - The device may automatically restart for your changes to take effect.
Select a network to connect to
Network Setup
"Note your device will connect to the new network. If your network details are incorrect or the new network does not have Internet access, the device will automatically re-connect to your original network after a few minutes."
Signal Strength
Change wifi network request sent. Please allow 2-3 minutes for changes to take effect and be reflected in UI.,"ワイファイネットワークの変更要求が送信されました。変更が有効になり、UI に反映されるまで 2 ～ 3 分かかります。"
FAILED TO MODIFY DEVICE!
"An error occurred while trying change Wifi Configuration... Please try again later","ワイファイ構成の変更中にエラーが発生しました...後でもう一度お試しください"
Command Sent
Changing Settings
Coaching Screens
Auto power on/off displays based on Club Hours
"Displays will power on 15 minutes before your club opens, and 15 minutes after it closes"
Restart Chromium (User Interface) on all Devices
Your devices are configured to automatically install updates at 2:00 AM based on the timezone of your club.
"Unlocking updates will allow any pending software updates to your devices to install right away without waiting for 2:00 AM scheduled update time. Note that unlocking updates may take up to 15 minutes before they are automatically installed."
Unlock Updates on all Devices
Device updates have been allowed on all your online devices. Any available updates will be installed shortly
Error when communicating with API. Contact HQ for support.
Add CCTV Camera
Scan QR Code With Camera
Camera Name
Front Door
Alarm Triggered
Sonos & Master Clock
Prevent from becoming Master Clock
Prevent from becoming Sonos Controller
This device has been automatically selected to control your Sonos devices
Screen Resolution
Lowest, use on slow devices
Disable audio output on this device
"Please refer to the Installation & Configuration Guide for device setup instructions if you have not yet powered on and connected your device to your network."
View Installation Guide for EA Devices in Knowledge Base
What type of device would you like to associate with your club?
Coaching Screen
CCTV Camera
Room:
Bluetooth Mesh,ブルートゥース
"The estimated lifespan of the internal SD card (writes remaining) is a measure of the estimated remaining life of the SD card, calculated based on the total data that has been written to it so far and the typical lifespan of an SD card, with the assumption that the SD card has a total write capacity of ~64-128TB (depending on the size of your SD)"
Battery Level
Duress Button
Add Duress Button
Select Device Type
Add Coaching Screen Hardware
Equipment Associated
Display Name
Temperature
Round #1
View Installation Guide in Knowledge Base
FAILED TO ADD DEVICE!
"An error occurred while trying to associate the device with your club...<br>Please check the device has not already been added and/or contact HQ for further details."
Requesting screenshot from device...
Used
SD Storage Utilization
SD Storage Type
SD Filesystem State
SD Writes Remaining
written to-date
Manufacturer
Added/associated device with club
Connection Strength
Local/LAN IP,ローカル/LAN IP
Peripheral Services
Device Type,デバイスタイプ
This Week,今週
Only Online,オンラインのみ
Last 30 days,過去 30 日間
Network Source,ネットワークソース
Unlock &amp; Install Updates
Device,デバイス
Last Updated,最終更新日
Screen,画面
Camera,カメラ
"Use this configuration to enable/disable this device from either controlling your Sonos Devices or Becoming the Master Clock (the device which synchronizes the timer to the rest of your displays). We recommend disabling Master Clock and Sonos Controller on devices with bad Wifi Signal or that often freeze up. Note you require a minimum of 4 Devices or your timer/Sonos will totally stop."
These options can be used to override the automatic settings/configuration of your boxing timer in the event of network/performance troubleshooting and issues
This device has been automatically selected to be the Master Clock - The Master Clock triggers the bell timer on all your devices in unison
Identify Display
Base Display Brightness
"This configuration sets the baseline display brightness setting. You can adjust your displays to automatically dim during evening periods,and then brighten during the day in the Coaching Screen settings"
"This configuration sets the baseline display brightness setting. You can adjust your displays to automatically dim during evening periods, and then brighten during the day in the Coaching Screen settings"
"Rebooting all devices will mean that perphial devices such as Sonos, Duress Buttons, etc will be offline while your devices reboot."
"This action should only be performed while your club is staffed should any troubleshooting steps be required post rebooting your devices."
Proceed?
identify devices,デバイスを識別する
reboot all devices,すべてのデバイスを再起動する
Clean
Dirty
Errors
Orphaned Inodes
Non-contiguous
Recovering
Needs Recovery
Read-Only
"The file system is in good condition with no detected errors."
"The file system has been mounted and there are changes that have not yet been written to disk. It is possible that a crash could result in loss of data."
"There are errors in the file system that require attention."
"There are inodes that were open when the system crashed, and are no longer linked to any directory."
"The file system has non-contiguous blocks, which could impact performance."
"The file system is currently in the process of recovering from a crash."
"The file system has experienced a crash and requires recovery, but this process has not yet been initiated."
"The file system has errors that have caused it to be mounted in a read-only state to prevent further damage."
"State unknown or not provided."
Motion Zones
"Connect CCTV Device to Network First!"
"Please ensure your CCTV Device is powered on and connected to your network before adding into the Performance Hub."
"The device you are trying to add is not currently online. Please connect the device to your network, then retry adding the device."
Device Overheating
Adjust the mask/points on the snapshot from your camera that you would like to exclude motion detection from.
If required you may add additional points to your exclusion mask by clicking the small circle between the larger white circles on the mask.
Double click on a point to remove it from the mask.
Any motion that occurs within the solid purple area will trigger a recording.
Adjust Motion Zone
Duress Button Next Steps
Wait Time
Device Reboot Required
"Please allow up to 15 minutes for your new Duress Button to sync with the Performance Hub. This will enable status reporting, including battery usage."
Remember to reboot your Coaching Screens when convenient for the configuration to update automatically.
"Performance will be limited, this device is Thermal Throttled. Devices exceeding 95°C will shut down"
Subtle
Default
Energetic
Intense
"This setting is ideal for detecting very light and subtle movements. Suitable for monitoring spaces where minimal motion occurs, such as adjusting objects or small shifts in position."
"A well-balanced setting designed to capture regular movement patterns. Effective for monitoring areas with steady motion, such as people walking between rooms or performing routine activities."
"Optimized for environments with more frequent movement. This threshold detects faster and more frequent activities, such as people actively moving or objects shifting regularly."
"Best suited for environments with continuous, intense movement, such as high-traffic areas or roads. This level focuses on capturing only significant movements to avoid false positives from constant motion."
Motion Threshold
Camera Make
Low Disk Space - Recording May Fail
Purge Internal Storage
Purge Internal Storage?
This action is irreversible!
"This action will permanently delete all recordings from your camera's internal storage"
"If you proceed with purging the internal storage, please be aware that any recordings stored on the camera will be irretrievably wiped. This includes recordings that have not yet been uploaded to the cloud or any other storage solution. Once deleted, these recordings cannot be recovered."
"Please ensure that all important recordings have been safely uploaded or backed up before proceeding with this action."
Scheduled Reboot
"Use Scheduled Reboots to automatically restart the device at 2 AM (local time) to enhance performance, clears memory leaks, and facilitate updates. This also helps provide smoother operation on older devices."
2AM
Sunday - 2AM
"First Sunday of the month - 2AM"
Request to Remove Device from Account
Compliance Details
Compliance
"Duress Button B1"
AI Camera with
Qualcomm Atheros QCA9377 Regulatory
5GHz product for indoor use only
2.4 GHz ISM spectrum band product
"This device should be installed with a minimum separation of 20 cm from the antenna to any person is required to comply with RF exposure limits."
"This device is coated for electrical insulation, excluding the I/O port areas, to ensure electrical safety. It complies with Part 15 of the FCC Rules, meaning it should not cause harmful interference and must tolerate any interference received. Although this equipment is tested against Class B limits to be safely used in home environments, there's no absolute assurance against interference. If interference to radio or TV reception occurs, it is recommended to reorient the antenna, increase separation from the receiver, or consult with a technician."
"This device should not be placed near or operate with other antennas or transmitters. In Canada, adherence to ISED radiation exposure limits is necessary. The device cannot cause interference and must accept any interference per the regulatory conditions. Lastly, the device conforms to HDMI Licensing Administrator, Inc. standards, as indicated by its compliance statement. Any unauthorized changes could void the authority to operate this equipment."
"This device is IP65 rated. Do not place any hot items on or near the device as the device contains a lithium battery inside."
"Note: The Duress Button B1 has been tested and complies with the limits for a Class A digital device, pursuant to FCC Rules. It features a rechargeable 240mAh lithium battery, with a Type-C charge port and offers a prolonged operational period, which varies depending on usage conditions (e.g., up to 9 months at 0dBm Tx Power, 1000ms advertising interval). When operated in commercial environments, it adheres to standards set for minimizing harmful interference with other devices. Improper installation or use could lead to radio communication interference. If this equipment is used in a residential area, it may cause interference, necessitating the user to implement appropriate measures to correct the interference at their own expense."
This product contains a SBC designed and engineered by ASUSTek Computer Inc
"The device conforms to the above certifications, and should be installed and operated with minimum distance 20cm between the device and the general public."
"Note: This equipment has been tested and found to comply with the limits for a Class A digital device. pursuant to part of the FCC Rules. These limits are designed to provide reasonable protection against harmful interference when the equipment is operated in a commercial environment. This equipment generates, uses and can radiate radio frequency energy and, if not installed and used in accordance with the instruction manual, may cause harmful interference to radio communications. Operation of this equipment in a  residential area may cause harmful interference in which case the user will be required to correct the interference at his own expense."
Uploads Queued
Advanced
Configuration
Details
Reset Filters
Networking
Storage
Outputting at
Online
Updates
Unlock Updates on Device
Warm-up
Equipment: Warm-up
Relay Controller - This device can turn on & off an electrical circuit/relay based on the configured variables.
Relay Configuration
Adapter Serial Number
Connected Relay Adapter
UID
Relay Event Triggers
"When a specific event happens from your smart facility connected technology such as a duress button alarm is triggered, or your screens/technology turns on, the relay can be configured to be toggled on|off"
Unconfigured
Duress Buttons
Duress Alarm Activated | Deactivated
Duress Low Battery
General State of Club
Mirror Coaching Screens Power State
Relay #
Action
Current State
Relay Controller
Equipment: Relay Controller
AI Accelerator Chip
Driver
Framework
Consumed
Camera Chip
Speed
Class
"AI Accelerator Chip Not Found - AI enabled features will not function on this device, including facial recognition."
"Bad SD Card Detected - This device contains an SD card that may experience performance issues over time. For optimal performance and reliability, please contact support to discuss replacement options."
"Possible Fan Fault Detected - The device may be overheating due to a fan failure. This will reduce its performance and can cause overheating. Please contact support to organize a replacement."
Open Router Webpage
"Yes, Reboot"
Connecting to router...
We could not establish a tunnel to the remote router at this time.
This device has reached end-of-life and has been sunset
"Configuration options are no longer available as support for this device ended on <strong>Monday, 14 April 2025</strong>."