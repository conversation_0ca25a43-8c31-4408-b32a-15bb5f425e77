en,ja
CONFIGURE YOUR INTEGRATIONS TO THIRD PARTY SYSTEMS
GymMaster
"We require your GymMaster API, to integrate with our website, and other internal systems for automation."
"The GymMaster API is usually automatically configured by your Account Manager, however you may manually change the API key if requested by GymMaster support."
GymMaster Primary Domain
GymMaster API Key
Lead Forwarding
Lead Forwarding <i>(forwards on 'free trial' & facility page leads)
Myzone API
Myzone API Key
Stripe Account ID
"If you or your suppliers require integrating to/from the various UBX API's, Please first discuss this with your Account Manager and refer them to our <a href=""http://developers.gymsystems.co/"" target=""_blank"">Developer Network</a> documentation."
Integrations for the Facility:
successfully saved.
An error occurred while trying to save Integration Changes for the Facility:
Please contact HQ to further investigate this issue.
These variables are pre-defined and cannot be changed.
Review Management Only
Not Set
Use a comma
to separate emails and/or add the email into the list.
Extended Access
Please note that the Back2Base Alarm ID needs to be provided by Back2Base once your account has been provisioned by Back2Base.
To initiate a new request with Back2Base please complete the
To initiate a new request with W4G please complete the
EA Onboarding Form
Back2Base Alarm ID
Alarm Integration Type
TCP/IP CID Call
Hard Wired Relay
"Note, EA integration configurations are required to be set to enable 'Extended Access Hours' on your facilities web page. Please refer to the support documentation on how to configure this."
EA Alarms to external monitoring system will not be enabled at this time
Device Type
To purchase a device that can integrate with the EA Duress Buttons please refer to the Performance Hub Store. For documentation around configuring hard wired relay devices please refer to the Knowledge Base.
Ubiquiti Unifi Network Configuration
"If your network is equipped with a Unifi Express or similar Ubiquiti Unifi Network controller, please input the details to allow integration to your Smart Technology", "ネットワークがUnifi Expressまたは同様のUbiquiti Unifiネットワークコントローラーを備えている場合、スマートクラブテクノロジーへの統合を可能にするために詳細を入力してください。"
Unifi Controller Host IP
Username
Enter Username
Enter Password
Leave blank to auto detect
"To ensure seamless integration of your facilities operations with the Performance Hub, it's important to connect your Member Management System (MMS) via its API. This integration enables synchronization of membership data, automates billing, and streamlines various administrative tasks within our platform. Your API key is typically provided during setup. If you require assistance with manual configuration, your Account Manager or your MMS provider's support team can help guide you through the process."
MMS API Endpoint
MMS Provider
MMS API Key
Membership Mapping
"This allows you to assign specific membership types to predefined categories, enabling tailored access to different areas of the facility or by defining whether members can use facilities during staffed or unstaffed hours, etc."
Member Management System
"Lead Forwarding automatically forwards leads, such as contact submissions and free trial bookings, to specified email addresses. These emails are then parsed by your CRM software, which maps the relevant fields into your CRM system for seamless tracking and follow-up."
"You can also create a Zapier account and set up an Email Parser to connect your free trial bookings and leads from your facility to a wide range of third-party systems. This allows you to automate workflows and streamline data handling across different platforms outside of the Performance Hub. For step-by-step instructions, please refer to the following guide"
Email Parsing in Zapier
Example Free Trial Booking Email
"This is an example of a Free Trial Booking email that is sent via plain text to your configured Lead Forwarding addresses."
"PLEASE NOTE: This email is sent in plain text format."
View Sample Email
Lead Forwarding (CRM)
"Our system integrates with Myzone to sync Myzone Effort Points (MEP) data for your members directly into our Training Camp and Train on Demand apps. This integration allows members' workout efforts to be seamlessly tracked and reflected within your facilities digital ecosystem."
"To enable this integration between Myzone and the Performance Hub, your facility must be configured with a "Data level 4" access. This ensures that the necessary permissions are in place for the secure transfer and synchronization of member data. For more details on data levels and configuration, please refer to"
"Myzone's GDPR Agreement and Member Data Guide."
"If your club has not yet been provided with a Stripe account ID, you'll need to complete the onboarding process to activate payment processing. To begin, please fill out the Stripe Onboarding form. Once your account is set up, you can integrate it with the Performance Hub to start processing member payments without delay."
"The Performance Hub integrates seamlessly with Stripe to process and track all payments made by members to your facility. This integration ensures that all transactions are securely handled and efficiently managed within the Performance Hub, providing real-time visibility into your facilities financial activity. By leveraging Stripe's robust payment infrastructure, you can easily manage subscriptions, one-time payments, and other financial interactions directly through the platform."
"Stripe enforces strict Know Your Customer (KYC) requirements to comply with financial regulations and ensure secure transactions. Within the Performance Hub, these requirements can be managed through the 'Business Profile (KYC)' section. This section allows you to easily submit and update the necessary business and personal information to meet Stripe's compliance standards, ensuring uninterrupted payment processing and financial management for your facility."
Manage KYC
Begin Onboarding
Stripe Account
Myzone API
With Extended Access
Without Extended Access
Access
Membership
Paying
Sell Online
Staffed
Off-Peak
Archived
Fortnightly By Billing
Monthly By Billing
Full Cost
You have unsaved changes. Do you want to save them before switching tabs?
Discard Changes
Your API Key will look like: b9a66500-9bab-49f2-82fd-850eafdbff24
Your Account will look like: acct_XXXXXXXXXXXXXXXX
Provider
W4G Site ID
"Please note that the Site ID will be provided by W4G once your account has been provisioned by W4G."
This test will check connectivity between the Performance Hub and your Unifi Network Controller. No settings will be changed on your device.
Ensure your credentials are correct before testing.
Test Unifi Configuration
"You have unsaved changes. If you made any updates to the Unifi Controller, please save them before testing. Would you like to save your changes now?"
We were not able to log in to the Unifi Controller.
Please check the
"username, password, and IP address"
", then try again."
Successfully connected to the Unifi Controller!
Controller Name
Device Type
Version
Update Available
Uptime
The Performance Hub will automatically health-check smart facility devices connected to the Unifi Controller.
All requests to communicate with the Unifi Controller
timed out
This could mean that the
IP address is incorrect
or that the Unifi Controller is not on the same network as your Smart Facility devices.
There must be at least
ethernet-connected Smart Facility device (screen or camera) online and running for this test to work.