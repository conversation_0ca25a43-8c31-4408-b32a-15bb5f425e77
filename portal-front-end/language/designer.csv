en,ja
Files
Exports
Brand Guidelines
EDITOR
ASSET MANAGER
View Support Guides Marketing and Print in Knowledge Base
Filter Files
New Design
Admin View
"New Design / File Upload"
"All Designs / Admin View"
Close design
Save Changes
"Document / Print Setup"
Which type of design would you like to create?
Dynamic Design
"A dynamic design may have user/dynamically generated variables (such as text inputs, pictures, logos, etc) generated based on either the ID of a club, or the Brand of the club."
"Dynamically generated variables are rendered in real-time by our server before being downloaded in the desired format. Formats include JPG, TIFF, PNG, PDF, and PSD, and colour profiles for professional printing such as CMYK:FOGRA39 may also be selected/embedded in the exported image to ensure maximum print accuracy and colour reproduction."
Static File Upload
"Static designs or plain 'File Uploads' are may be uploaded to the Performance Hub Cloud as a single file/attachment."
"Static designs may be ""filtered"" per club/brand, however no dynamic generation/modification of design files are performed. Uploaded files are directly downloaded to the end user's computer."
You must select a design type to continue.
Most Recent Export
Oldest Exports First
Group by Design
There are no exported designs for your club:
"After you have 'Exported' a design, previous historic exports will appear in a gallery here."
Browse designs to begin
Download File
Re-Render Design
Design Details
Exported
Color Mode
Export ID
"Could not find source design."
"The design you are trying to open may have been removed or no longer published."
"Please contact support if you require additional assistance."
There are no available Files or Designs for your club
Multi-brand
Brand
Device Scaling
Static Design
Not Yet Published
Not Approved
Preview Design
Open File
Designs without any Folders
Designs Available
All Designs
Viewing Folder
No results found for the term
"Edit Metadata / Design"
"Status / Publishing"
"Usage & Guidelines"
Design Scaling
Design Dimensions
DPI
Preferred Format
File Format
File Pages
Input Variables
Current Status
Unapproved designs will not be shown to individual facilities
Set publish date (restrict access until this date)
Note timezone for publishing automation is based on UTC +0 Timezone
Publish Now
"Set expiration date (restrict access after this date)"
Changing this status will be logged using
"Please note, only Performance Hub admins can view and comment on designs, assets & files"
Name of file
"To create a new folder, simply type in a new name and it will be created & assigned to this file"
Restrict to Club
Restrict to Region
Globally Accessible
Brand Restrictions
"All - (Design works with any Brand)"
Author Dynamic Design in Editor
Delete Design?
Are you sure you wish to delete the file
"Please carefully check the file name before proceeding .... this cannot be undone, there is no 'recycling bin' and will require manual restoration should you delete by accident."
Replace existing design
"A design is currently being edited, and it's most recent changes have not yet been saved. Loading a new design will overwrite your work, and any changes that have not been saved will be lost. Continue?"
"Yes, load new design"
No preview available
Edit / Export
Download File Directly
The design you are currently editing has unsaved changes. Save changes before closing?
"Please refer to the 'Help' section in the Editor toolbar for information around adding Dynamic Elements to this document."
Document Setup
"Please note that 'Metadata' such as publishing status, guidelines, brand/visibility etc may be set AFTER the initial design has been created/saved. Initial designs will be created in a ""Unapproved"" state, and not visible to Clubs."
Input Settings
Default Format
"Use ""Design Upscaling"" for high resolution exports"
"This will upscale your exports by using pixel doubling (based on the range selected). For example, a 100x100px document with a scale of ""4"" would be 'pixel doubled' to 400x400px, however when editing, the base document will still be 100x100px."
"Renders may take from 30 seconds to several minutes to export. Remember you can always come back & download your exported design in the "Exports" tab in Marketing & Print after your exporting is complete."
Design Tagline
The following taglines can be selected
Text Inputs & Variables
The following variables will be automatically set
Design will be exported with the following
This is a large design
Render Design for Export
Toggle Design Elements
Export Format
Colour Mode
Colour Profile
"If you are unsure what Colour profile to use, please speak with your local print supplier BEFORE exporting, this will ensure that your print supplier will have colours according to our brand guidelines."
Export
Hide Advanced
Show Advanced
Rendering Design
"You may continue doing other tasks."
"If you close this window the design will be available for download when completed in the <b>""Renders/Exports""</b> tab."
Design Render Failed
Render Preview
Using Club
"Failed to export design. Please contact HQ with the design you are trying to export for details."
Downloading design file
Please replace this span with your custom code
Open custom code
Club Map
Are you sure you want to clear the design?
Clear Design
Toggle Rulers
Toggle Crop Marks
"Absolute Mode (Allows Drag & Drop of Elements)"
Preview Render Document
Templating Guide
Show Layer/Object Borders
View Canvas Code
Add Variable
Crop Marks
Add or Select one or more Folders
"<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to save your asset changes...<br>Please contact HQ for further details."
Delete Asset?
Are you sure you wish to delete the asset
THIS CAN NOT BE UNDONE!
"Delete asset successfully!!"
Edit Asset
Enter asset name
"Update asset successfully!!"
Upload New Asset
Upload File Here
You can only upload one file.
Drop file or click here to upload
"Uploading file - please wait (saving will be available after upload complete)"
"You can only upload one file, the first one will get uploaded."
Upload Asset
Please upload one file.
Upload asset successfully!!
"<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to save your design changes...<br>Please contact HQ for further details."
Design marked as deleted.
SUBSTITUTED VARIABLES
DYNAMIC ELEMENTS
"Variables are automatically substituted with the relevant details of the selected Club/Venue making your designs 'Dynamic'"
How to add
"You may add variables directly into your HTML code or CSS (as per above example) Variables that are not automatically substituted will appear as a ""text input"" for users when going to render/download the design. A full list of supported variables that are dynamically generated include"
Expected Value
based on
"Dynamic Elements are elements in your design that can be interacted with by the end user before rendering/downloading a design. <br>Note should you have suggestions for additional Dynamic Elements which are required in your Design, please lodge a Feature Request with HQ."
Single Line User Inputs
"User input allows for 'free text' that users may input when rendering a design"
"Each input field represents a single 'input' in the design"
"Inputs will be substituted anywhere in the design - including HTML elements, image URL's etc"
"Inputs can be configured to be 'Required' in the design settings dialog, forcing the user to complete all inputs before rendering a design"
"A character count limit may also be set (by default it's unset) in the design settings dialog if required"
Example code for adding User Inputs to your design HTML
Result when Rendering Design
Multi-Line User Inputs (Textarea)
Textarea inputs allow for multi-line free text input by users in a basic WYSIWYG (What you see is what you get) editor when rendering a design.
"Only basic formatting (Bold, Italic, Underline, Bullet Points/Lists, and Font Size) are supported - All other HTML including images will be stripped from the render."
"Example code for adding one or more Textarea's to your design HTML"
Result in Render Design UI
Toggles allow for content to be toggled on/off - aka Hidden/Shown
"When a user renders a design, they will be presented with multiple 'toggle' options that bay be selected before downloading the design."
"Content that is wrapped inside of the <code>{{#t=Name}}{{#/}}</code> will be what's toggled on/off in your design."
Example code for adding Toggles to your design HTML
Selectable Pictures (Asset Library)
You may add user-selectable pictures/references in your dynamic designs by uploading images in the ASSET MANAGER
Assets can be coded/configured like
"ASSET_NAME: = Name of the you'd like to filter by (from Asset Manager)"
"TAGS: = Refers to the which 'grouping tags' you would like the selectable pictures to be restricted by"
"UUID: = Should be specified as the 'Default Asset' if a selection is not made by the user (ie when automatically rendering design thumbnails etc)"
There are no limitations to the number of selectable images or times an asset may be referenced in your code.
QR Codes
QR Codes may be dynamically generated which will redirect to any custom URL provided
"The URL variable <b>must be encoded</b> using the 'urlencode()' method, which can be performed using an online URL Encoder such as <a href=""https://www.urlencoder.org/"" target=""_blank"">https://www.urlencoder.org/</a>"
"Substituted Variables may be also referenced INSIDE of the URL if required, to generate unique URL's per club."
Example code for adding QR Codes to your design HTML
"An example 'Dynamically' Generated QR which links off to a club page using our URL shortening service."
Taglines/Options
"You may use Taglines/Options to show user 'selectable' content without allowing the user to edit/the raw content."
"This is useful for A|B testing content, or providing a different variety of text options to select from when rendering."
NOTE: Currently only one set of Taglines/Options are supported in a single design.
Example code for adding Taglines/Options to your design HTML
"Please note that 'Metadata' such as publishing status, guidelines, brand/visibility etc may be set AFTER the initial design has been uploaded and saved. Initial designs will be uploaded in a &quot;Unapproved&quot; state, and not visible to Clubs."
Enter static File name
Static design saved successfully!
Waiting for new design metadata
Filter Designs
Name
Date Modified
One moment
Comments
Post a comment about this file
Cancel
File Type
Search Designs
Pinned
Date Updated
File/Folder Name
Search Design...
Save
All Designs (Admin)
Published Designs Only
Loading Design
Show
more Marketing & Print results for
Search in Marketing & Print
Sort By
Filters
Folder/Filename
Date/Time
Design Count
Static File
View All Designs
Folder
Designs
Design Available
Static
Dynamic
View
Edit Page
Add Page
URL Slug
Types
Iframe
WYSIWYG
All Facilities
Please enter a valid URL to preview the content.