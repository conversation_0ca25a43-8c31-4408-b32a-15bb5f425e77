{"tasks": [{"id": 1, "title": "Project Setup with FSD Architecture", "description": "Initialize the project with required dependencies and set up the Feature-Sliced Design architecture structure", "status": "done", "dependencies": [], "priority": "high", "details": "Create a new Vite project with React and TypeScript. Install dependencies: TailwindCSS, SWR, Valtio, TanStack Table, clsx/tailwind-merge, Vitest/React Testing Library. Set up the FSD architecture with folders for features, entities, shared, etc. Configure TailwindCSS and create the cn utility function. Set up the basic iframe entry point that will be embedded in the legacy application.", "testStrategy": "Verify project builds successfully. Create basic smoke tests to ensure the application renders without errors. Test the cn utility function."}, {"id": 2, "title": "Implement Translation System", "description": "Modify the existing shared translation system to read language preference from cookies with fallback to 'en'", "status": "done", "dependencies": [1], "priority": "high", "details": "Update the existing shared translation implementation in `src/shared/il8n`. Modify or create a hook in `src/shared/il8n/hooks.ts` that attempts to read language preference from the 'UserLang' cookie. Implement mandatory fallback to 'en' if cookie is inaccessible. Update the existing mechanism to fetch translation JSON files from /assets/translations to use this language code. Ensure types for translation keys are properly maintained and that variable interpolation in translation strings continues to work.", "testStrategy": "Test the updated translation hook with various scenarios: 'UserLang' cookie present, cookie missing, invalid language code. Verify 'en' fallback works correctly. Mock fetch requests for translation files. Ensure existing translation functionality remains intact.", "subtasks": [{"id": 2.1, "title": "Analyze existing translation implementation", "description": "Review the current implementation in src/shared/il8n to understand how it works and identify points for modification", "status": "done"}, {"id": 2.2, "title": "Implement cookie-based language detection", "description": "Add functionality to read 'UserLang' cookie with fallback to 'en'", "status": "done"}, {"id": 2.3, "title": "Update translation file fetching", "description": "Modify the existing fetch logic to use the language code from cookie", "status": "done"}, {"id": 2.4, "title": "Write tests for updated functionality", "description": "Create tests for cookie reading, fallback behavior, and translation file fetching", "status": "done"}]}, {"id": 3, "title": "Create DynamicSearch Feature Slice Structure", "description": "Set up the DynamicSearch feature slice with necessary folders and files", "status": "done", "dependencies": [1], "priority": "high", "details": "Following FSD principles, create the features/DynamicSearch slice with subfolders: api (for API endpoints), model (for state management), ui (for components), lib (for utilities), and types. Define shared interfaces for search results from different sources. Create skeleton components for ResultList and other UI elements. Implement basic state management with Valtio where needed beyond SWR.", "testStrategy": "Verify folder structure adheres to FSD guidelines. Test basic component rendering. Validate type definitions for search results.", "subtasks": [{"id": 1, "title": "Create DynamicSearch folder structure and type definitions", "description": "Set up the initial folder structure for the DynamicSearch feature slice and define shared interfaces for search results", "dependencies": [], "details": "1. Create the main features/DynamicSearch directory\n2. Create subfolders: api, model, ui, lib, and types\n3. In the types folder, create index.ts file\n4. Define base interfaces for search results in types/index.ts:\n   - IBaseSearchResult interface with common properties (id, title, description, etc.)\n   - ISearchSource interface to define search source metadata\n   - ISearchResults interface to aggregate results from multiple sources\n   - ISearchParams interface for search parameters\n5. Export all interfaces from the types folder\n6. Test by importing the types in a test file to verify there are no TypeScript errors", "status": "done", "parentTaskId": 3}, {"id": 2, "title": "Implement API layer for search functionality", "description": "Create API functions to handle search requests to different sources", "dependencies": [1], "details": "1. Create api/index.ts file\n2. Implement fetchSearchResults function that accepts ISearchParams\n3. Create separate functions for each search source (e.g., fetchProductResults, fetchArticleResults)\n4. Implement proper error handling for API requests\n5. Create mock data for development and testing\n6. Export all API functions from the api folder\n7. Test the API functions with mock data to ensure they return data in the expected format defined by the interfaces", "status": "done", "parentTaskId": 3}, {"id": 3, "title": "Set up state management with <PERSON><PERSON><PERSON>", "description": "Implement state management for the search feature using Valtio", "dependencies": [1, 2], "details": "1. Create model/store.ts file\n2. Define searchState proxy object with <PERSON><PERSON><PERSON> containing:\n   - query: string\n   - results: ISearchResults\n   - isLoading: boolean\n   - error: Error | null\n   - selectedFilters: object\n3. Create model/actions.ts file\n4. Implement actions for state updates:\n   - setQuery function\n   - setResults function\n   - setLoading function\n   - setError function\n   - performSearch function that uses the API layer\n5. Create model/index.ts to export all state management\n6. Test state management by creating a simple test that updates state and verifies changes", "status": "done", "parentTaskId": 3}, {"id": 4, "title": "Create UI components for search results", "description": "Implement skeleton UI components for displaying search results", "dependencies": [1, 3], "details": "1. Create ui/ResultList/index.tsx component\n2. Create ui/ResultItem/index.tsx component\n3. Create ui/SearchInput/index.tsx component\n4. Create ui/SearchFilters/index.tsx component\n5. Implement basic styling for components\n6. Connect components to state using Valtio's useSnapshot hook\n7. Create ui/index.ts to export all UI components\n8. Test components by rendering them with mock data and verifying they display correctly", "status": "done", "parentTaskId": 3}, {"id": 5, "title": "Implement utility functions and public API", "description": "Create utility functions and expose the public API for the DynamicSearch feature", "dependencies": [1, 2, 3, 4], "details": "1. Create lib/helpers.ts for utility functions:\n   - searchResultsFormatter function\n   - filterResults function\n   - sortResults function\n2. Create lib/index.ts to export all utility functions\n3. Create index.ts in the DynamicSearch root to export the public API:\n   - Export UI components from ui/index.ts\n   - Export state management from model/index.ts\n   - Export types from types/index.ts\n   - Export utility functions from lib/index.ts\n4. Create a simple example/demo component that uses the DynamicSearch feature\n5. Test the entire feature by importing and using it in the demo component\n6. Verify that all parts work together correctly", "status": "done", "parentTaskId": 3}]}, {"id": 4, "title": "Implement Core SWR Hooks for Initial Data Sources", "description": "Create SWR hooks for fetching data from the first set of sources", "status": "done", "dependencies": [3], "priority": "high", "details": "Implement SWR hooks for the first 3 data sources: Wiki Article Search Results (hits), Wiki Topics & Categories (topics), and Internal Page Routes (functions). Create API endpoint configurations based on react_search_migration_plan.md. Implement data transformation functions to normalize the response data into a consistent format for the UI. Handle error states and implement retry logic.", "testStrategy": "Write unit tests for each SWR hook, mocking API responses. Test data transformation functions with sample data from each source. Verify error handling works correctly.", "subtasks": [{"id": 1, "title": "Create API endpoint configuration for Wiki Article Search", "description": "Define the API endpoint configuration for the Wiki Article Search (hits) data source based on the migration plan document.", "dependencies": [], "details": "1. Review react_search_migration_plan.md to understand the Wiki Article Search API requirements.\n2. Create a new file in the API configuration directory (e.g., src/api/endpoints/wikiSearch.ts).\n3. Define the endpoint URL, HTTP method, and required parameters.\n4. Create TypeScript interfaces for request parameters (query, pagination, filters).\n5. Define response type interfaces that match the expected API response structure.\n6. Export the configuration for use in the SWR hook.\n7. Test the configuration by making a sample API call using fetch or axios to verify endpoint details.", "status": "done", "parentTaskId": 4}, {"id": 2, "title": "Implement data transformation for Wiki Search results", "description": "Create utility functions to transform and normalize Wiki Search API responses into a consistent format for the UI.", "dependencies": [1], "details": "1. Create a new file for transformation utilities (e.g., src/utils/transformers/wikiSearchTransformer.ts).\n2. Analyze the raw API response structure from the Wiki Search endpoint.\n3. Define the normalized output interface that will be consistent across all search sources.\n4. Implement a main transformation function that converts raw API data to the normalized format.\n5. Add helper functions for specific transformations (e.g., extracting titles, formatting descriptions).\n6. Handle edge cases like missing data or unexpected formats.\n7. Add unit tests for the transformation functions with various input scenarios.\n8. Document the transformation logic with JSDoc comments.", "status": "done", "parentTaskId": 4}, {"id": 3, "title": "Create base SWR hook for Wiki Article Search", "description": "Implement the core SWR hook that fetches data from the Wiki Article Search API and applies the transformation.", "dependencies": [1, 2], "details": "1. Create a new file for the hook (e.g., src/hooks/useWikiSearch.ts).\n2. Import SWR library and the API configuration from subtask 1.\n3. Define the hook parameters interface (search query, pagination, filters).\n4. Implement the hook function that uses SWR's useSWR to fetch data.\n5. Integrate the data transformation function from subtask 2.\n6. Return the normalized data along with loading and error states.\n7. Implement basic caching configuration.\n8. Test the hook in isolation with mock API responses.", "status": "done", "parentTaskId": 4}, {"id": 4, "title": "Add error handling and retry logic to Wiki Search hook", "description": "Enhance the Wiki Search SWR hook with robust error handling and retry capabilities.", "dependencies": [3], "details": "1. Update the useWikiSearch hook to include comprehensive error handling.\n2. Implement error classification (network errors, API errors, validation errors).\n3. Add retry logic using SWR's built-in retry capabilities.\n4. Configure exponential backoff for retries.\n5. Add error logging functionality.\n6. Create custom error messages for different error scenarios.\n7. Update the hook's return type to include detailed error information.\n8. Test error scenarios by mocking failed API responses and network issues.", "status": "done", "parentTaskId": 4}, {"id": 5, "title": "Implement pagination and filtering support in Wiki Search hook", "description": "Extend the Wiki Search hook to support pagination and filtering capabilities.", "dependencies": [3], "details": "1. Update the hook parameters to accept pagination options (page number, page size).\n2. Add support for filtering parameters based on the Wiki Search API capabilities.\n3. Implement logic to construct the correct query parameters for pagination and filtering.\n4. Update the cache key generation to include pagination and filter parameters.\n5. Add utility functions to help with pagination calculations (total pages, next/prev page).\n6. Ensure the transformed data includes pagination metadata.\n7. Test pagination functionality with different page sizes and filter combinations.\n8. Document the pagination and filtering API in the hook's JSDoc.", "status": "done", "parentTaskId": 4}, {"id": 6, "title": "Create a comprehensive test suite for Wiki Search hook", "description": "Develop a complete test suite for the Wiki Search hook to ensure reliability and correct behavior.", "dependencies": [3, 4, 5], "details": "1. Set up a test file for the Wiki Search hook (e.g., src/hooks/useWikiSearch.test.ts).\n2. Create mock data for API responses in different scenarios.\n3. Implement tests for successful data fetching and transformation.\n4. Add tests for error handling and retry logic.\n5. Test pagination functionality with different parameters.\n6. Test filtering capabilities with various filter combinations.\n7. Implement edge case tests (empty results, malformed responses).\n8. Add integration tests that verify the hook works correctly with the actual components that will use it.\n9. Document test coverage and any known limitations.", "status": "done", "parentTaskId": 4}]}, {"id": 5, "title": "Integrate TanStack Table with Basic Result Display", "description": "Implement the ResultList component with TanStack Table for displaying search results", "status": "done", "dependencies": [4], "priority": "high", "details": "Create the ResultList component using TanStack Table. Implement column definitions for the initial data sources. Create custom cell renderers for different data types. Group results by source with appropriate headers. Implement skeleton UI components for loading states. Create empty state displays for when no results are found.", "testStrategy": "Test ResultList rendering with mock data. Verify skeleton loaders appear during loading. Test empty state displays. Check that results are properly grouped by source.", "subtasks": [{"id": 1, "title": "Set up basic ResultList component with TanStack Table", "description": "Create the foundational ResultList component structure with TanStack Table integration", "dependencies": [], "details": "1. Install TanStack Table dependencies if not already installed\n2. Create a new ResultList.tsx component file\n3. Import necessary TanStack Table hooks (useTable, useRowSelect, etc.)\n4. Define the basic component structure with proper typing for search results\n5. Implement a simple table instance using useTable hook with minimal configuration\n6. Create a basic table rendering structure (thead, tbody, tr, td)\n7. Test the component with mock data to ensure the table renders correctly\n8. Export the component for use in the search results page\n<info added on 2025-05-06T04:23:20.703Z>\n1. Install TanStack Table dependencies if not already installed\n2. Create a new ResultList.tsx component file\n3. Import necessary TanStack Table hooks (useTable, useRowSelect, etc.)\n4. Define the basic component structure with proper typing for search results\n5. Implement a simple table instance using useTable hook with minimal configuration\n6. Create a basic table rendering structure (thead, tbody, tr, td)\n7. Test the component with mock data to ensure the table renders correctly\n8. Export the component for use in the search results page\n\nImplementation completed:\n- Installed @tanstack/react-table package\n- Created ResultList.tsx with TanStack Table integration using dynamic column generation\n- Implemented TableInstance component for rendering tables based on content data\n- Added loading states with skeleton UI via a SkeletonRow component\n- Implemented empty state handling with a custom EmptyState component\n- Created comprehensive tests for all states (loading, empty, and populated)\n- Maintained backward compatibility with existing component API\n- Utilized TypeScript generics for improved type safety\n\nThe component now supports:\n- Dynamic column generation based on data structure\n- Customizable cell rendering for different data types\n- Loading state visualization with skeleton UI\n- Empty state messaging for better UX\n- All existing functionality from previous implementation\n\nAll tests are passing, and the component is ready for the next phase of development, which involves defining column configurations for different data sources.\n</info added on 2025-05-06T04:23:20.703Z>", "status": "done", "parentTaskId": 5}, {"id": 2, "title": "Define column configurations for different data sources", "description": "Create column definitions for various data sources that will be displayed in the results table", "dependencies": [1], "details": "1. Identify all data sources that need to be displayed (e.g., documents, images, videos)\n2. Create a columns.ts file to define column configurations\n3. Define base column properties (header, accessor, Cell)\n4. Implement specific column definitions for each data source type\n5. Add sorting configurations where applicable\n6. Create utility functions for formatting data in columns\n7. Test column definitions with sample data from each source\n8. Integrate column definitions into the ResultList component from subtask 1\n<info added on 2025-05-06T05:41:19.233Z>\n1. Identify all data sources that need to be displayed (e.g., documents, images, videos)\n2. Create a columns.ts file to define column configurations\n3. Define base column properties (header, accessor, Cell)\n4. Implement specific column definitions for each data source type\n5. Add sorting configurations where applicable\n6. Create utility functions for formatting data in columns\n7. Test column definitions with sample data from each source\n8. Integrate column definitions into the ResultList component from subtask 1\n\nAfter examining the codebase, the following implementation plan has been established:\n\n1. Create a dedicated columns directory within src/features/FederatedSearch/ to organize all column configurations\n2. Define a base TypeScript interface for column configurations to ensure consistency and type safety across all data sources\n3. Implement separate column configuration files for each identified data source:\n   - Wiki data source\n   - Store data source\n   - Marketing data source\n4. Each data source configuration will include:\n   - Type-safe accessor definitions\n   - Appropriate header labels\n   - Default sorting behavior\n   - Placeholder for custom cell renderers (to be implemented in subtask 5.3)\n5. Create a central index.ts file to export all column configurations for easy importing\n6. Update the ResultList component to dynamically select the appropriate column configuration based on the active data source\n7. Ensure all column definitions maintain type safety with the data structures from each source\n</info added on 2025-05-06T05:41:19.233Z>\n<info added on 2025-05-06T05:44:58.280Z>\n1. Identify all data sources that need to be displayed (e.g., documents, images, videos)\n2. Create a columns.ts file to define column configurations\n3. Define base column properties (header, accessor, Cell)\n4. Implement specific column definitions for each data source type\n5. Add sorting configurations where applicable\n6. Create utility functions for formatting data in columns\n7. Test column definitions with sample data from each source\n8. Integrate column definitions into the ResultList component from subtask 1\n<info added on 2025-05-06T05:41:19.233Z>\n1. Identify all data sources that need to be displayed (e.g., documents, images, videos)\n2. Create a columns.ts file to define column configurations\n3. Define base column properties (header, accessor, Cell)\n4. Implement specific column definitions for each data source type\n5. Add sorting configurations where applicable\n6. Create utility functions for formatting data in columns\n7. Test column definitions with sample data from each source\n8. Integrate column definitions into the ResultList component from subtask 1\n\nAfter examining the codebase, the following implementation plan has been established:\n\n1. Create a dedicated columns directory within src/features/FederatedSearch/ to organize all column configurations\n2. Define a base TypeScript interface for column configurations to ensure consistency and type safety across all data sources\n3. Implement separate column configuration files for each identified data source:\n   - Wiki data source\n   - Store data source\n   - Marketing data source\n4. Each data source configuration will include:\n   - Type-safe accessor definitions\n   - Appropriate header labels\n   - Default sorting behavior\n   - Placeholder for custom cell renderers (to be implemented in subtask 5.3)\n5. Create a central index.ts file to export all column configurations for easy importing\n6. Update the ResultList component to dynamically select the appropriate column configuration based on the active data source\n7. Ensure all column definitions maintain type safety with the data structures from each source\n</info added on 2025-05-06T05:41:19.233Z>\n\nImplementation of column configurations has been completed successfully with the following structure and features:\n\n1. Created a comprehensive column configuration system in `src/features/FederatedSearch/columns/index.ts` that:\n   - Implements typed column helpers for each content type (Wiki, Store, Marketing)\n   - Defines column definitions with appropriate headers and accessor functions\n   - Sets up placeholder cell renderers that will be enhanced in the next subtask\n   - Provides a utility function `getColumnsByCategory` that returns the correct column set based on category name\n\n2. The ResultList component has been updated to:\n   - Integrate with the new column configuration system\n   - Implement fallback logic to dynamically generate columns if no predefined columns exist for a category\n   - Pass the category name to the TableInstance component for proper column selection\n\n3. Created test coverage to verify:\n   - Column configurations are correctly generated for each data source\n   - The getColumnsByCategory function returns the expected column set\n   - Type safety is maintained throughout the column definition system\n\n4. Resolved TypeScript typing challenges by:\n   - Creating proper interfaces for each data source structure\n   - Ensuring type safety between column accessors and data structures\n   - Using generics to maintain type consistency across the table implementation\n\nThe implementation provides a flexible foundation that will seamlessly connect with the custom cell renderers to be developed in subtask 5.3. The column configuration system is extensible, allowing for easy addition of new data sources in the future.\n</info added on 2025-05-06T05:44:58.280Z>", "status": "done", "parentTaskId": 5}, {"id": 3, "title": "Implement custom cell renderers for different data types", "description": "Create specialized cell renderers to properly display different types of data in the results table", "dependencies": [1, 2], "details": "1. Create a cells directory to organize custom cell renderers\n2. Implement TextCell for standard text content\n3. Create ImageCell for thumbnail previews\n4. Develop LinkCell for clickable URLs\n5. Build DateCell for formatted date/time display\n6. Implement StatusCell for displaying status indicators\n7. Create any other specialized cells needed for specific data types\n8. Add appropriate styling to each cell type\n9. Test each cell renderer with various input data\n10. Update column definitions to use the custom cell renderers\n<info added on 2025-05-06T05:57:58.644Z>\n1. Create a cells directory to organize custom cell renderers\\n2. Implement TextCell for standard text content\\n3. Create ImageCell for thumbnail previews\\n4. Develop LinkCell for clickable URLs\\n5. Build DateCell for formatted date/time display\\n6. Implement StatusCell for displaying status indicators\\n7. Create any other specialized cells needed for specific data types\\n8. Add appropriate styling to each cell type\\n9. Test each cell renderer with various input data\\n10. Update column definitions to use the custom cell renderers\n\nImplementation details:\n- Created specialized cell renderer components in `src/features/FederatedSearch/columns/cells/`\n- Implemented six cell types:\n  * TextCell: Renders text with HTML highlighting and truncation capabilities\n  * LinkCell: Renders clickable links with customizable text and styling\n  * DateCell: Formats dates in various styles (relative, short, medium, long)\n  * NumericCell: Formats numbers as plain numbers, currency, percentages, or compact format\n  * StatusCell: Renders status indicators with appropriate color coding\n  * ImageCell: Displays image thumbnails with fallback handling\n- Developed cell factory functions using TypeScript generics for type safety\n- Created helper functions like `createTextCell`, `createLinkCell`, etc. for easy configuration\n- Added customization options for each cell type's appearance and behavior\n- Updated column definitions to use appropriate renderers based on data type\n- Applied proper sizing and formatting based on content\n- Created comprehensive tests covering basic rendering, empty values, formatting options, and event handling\n\nThe implementation provides a flexible and maintainable way to display different data types in the DataTable component, with proper formatting and styling for each content type.\n</info added on 2025-05-06T05:57:58.644Z>", "status": "done", "parentTaskId": 5}, {"id": 5, "title": "Implement loading and empty states", "description": "Create skeleton UI for loading states and empty state displays when no results are found", "dependencies": [1, 2, 3], "details": "1. Create a SkeletonRow component to represent loading state for table rows\n2. Implement a loading state that displays multiple SkeletonRows\n3. Create an EmptyState component with appropriate messaging\n4. Design and implement visuals for the empty state (illustrations, icons)\n5. Add conditional rendering in ResultList to show loading state when isLoading is true\n6. Add conditional rendering to show empty state when results array is empty\n7. Ensure smooth transitions between loading, empty, and populated states\n8. Test all states with various data scenarios\n9. Add appropriate aria attributes for accessibility during loading and empty states\n<info added on 2025-05-07T06:40:57.225Z>\n1. Create a SkeletonRow component to represent loading state for table rows\n2. Implement a loading state that displays multiple SkeletonRows\n3. Create an EmptyState component with appropriate messaging\n4. Design and implement visuals for the empty state (illustrations, icons)\n5. Add conditional rendering in ResultList to show loading state when isLoading is true\n6. Add conditional rendering to show empty state when results array is empty\n7. Ensure smooth transitions between loading, empty, and populated states\n8. Test all states with various data scenarios\n9. Add appropriate aria attributes for accessibility during loading and empty states\n\nImplementation details:\n\nEmptyState Component:\n- Successfully implemented in src/components/DataTable/EmptyState.tsx\n- Features customizable message and icon with SearchX as the default icon\n- Properly integrated in both TableCore and ResultList components\n- Handles different empty state scenarios with contextual messages based on the search context\n\nLoading State:\n- SkeletonRow component implemented in src/components/DataTable/SkeletonRow.tsx\n- Loading state implemented in TableCore.tsx using animated skeleton rows for visual feedback\n- ResultList.tsx shows animated placeholder UI when isLoading=true\n- Animation leverages Tailwind's animate-pulse for skeleton loading effects\n\nBoth implementations include comprehensive tests in src/components/ResultList/__tests__/ResultList.test.tsx that verify their functionality across different states. The implementation follows best practices with conditional rendering based on data/loading state and provides a smooth user experience with appropriate visual feedback during state transitions.\n</info added on 2025-05-07T06:40:57.225Z>", "status": "done", "parentTaskId": 5}]}, {"id": 6, "title": "Implement Remaining SWR Hooks for All Data Sources", "description": "Create SWR hooks for the remaining data sources and ensure proper data transformation for display", "status": "done", "dependencies": [4], "priority": "medium", "details": "Implement SWR hooks for the remaining data sources: Recently Viewed Wiki Articles (recent wiki searches), Store Products (store-products), Marketing & Print Designs (marketingprint), and CCTV People (cctv-people). Follow the patterns established in the initial hooks. Ensure consistent error handling and data transformation across all sources. Pay special attention to how each content type's data should be transformed for display in search results.", "testStrategy": "Write unit tests for each new SWR hook. Test data transformation functions with sample data. Verify consistent behavior across all data sources.", "subtasks": [{"id": 2, "title": "Implement SWR hook for Store Products", "description": "Create a custom SWR hook to fetch and manage Store Products data, replacing the existing Algolia implementation.", "dependencies": [], "details": "1. Create `useStoreProducts.js` hook\n2. Configure API endpoint URL and parameters for store products\n3. Implement data transformation to convert API response to the format expected by the UI\n4. Handle special cases for product categories and filtering\n5. Implement debouncing and pagination if required\n6. Set up proper caching strategy with SWR\n7. Add comprehensive error handling\n8. Document the hook's usage and parameters", "status": "done"}, {"id": 3, "title": "Implement SWR hook for Marketing & Print Designs", "description": "Create a custom SWR hook to fetch and manage Marketing & Print Designs data, replacing the existing Algolia implementation.", "dependencies": [], "details": "1. Create `useMarketingPrintDesigns.js` hook\n2. Configure API endpoint URL and parameters for marketing and print designs\n3. Implement data transformation to handle design metadata and thumbnails\n4. Handle filtering by design type and categories\n5. Implement debouncing for search queries\n6. Set up proper caching strategy with SWR\n7. Add comprehensive error handling\n8. Document the hook's usage and parameters\n<info added on 2025-05-08T05:19:08.231Z>\n1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook\n2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present\n3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`\n4. Leverage the existing shared `useApi` hook which already handles SWR caching\n5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails\n6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)\n7. Implement conditional fetching based on search terms (only fetch when search term is valid)\n8. Create formatting utility for consistent data display in results\n9. Utilize the error handling already provided by the useApi hook\n10. Handle filtering by design type and categories\n11. Implement debouncing for search queries\n12. Write tests similar to existing WikiSearch and StoreProducts hook tests\n13. Document the hook's usage and parameters\n</info added on 2025-05-08T05:19:08.231Z>\n<info added on 2025-05-08T06:11:43.417Z>\n1. Create `useMarketingPrintDesigns.js` hook\n2. Configure API endpoint URL and parameters for marketing and print designs\n3. Implement data transformation to handle design metadata and thumbnails\n4. Handle filtering by design type and categories\n5. Implement debouncing for search queries\n6. Set up proper caching strategy with SWR\n7. Add comprehensive error handling\n8. Document the hook's usage and parameters\n<info added on 2025-05-08T05:19:08.231Z>\n1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook\n2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present\n3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`\n4. Leverage the existing shared `useApi` hook which already handles SWR caching\n5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails\n6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)\n7. Implement conditional fetching based on search terms (only fetch when search term is valid)\n8. Create formatting utility for consistent data display in results\n9. Utilize the error handling already provided by the useApi hook\n10. Handle filtering by design type and categories\n11. Implement debouncing for search queries\n12. Write tests similar to existing WikiSearch and StoreProducts hook tests\n13. Document the hook's usage and parameters\n</info added on 2025-05-08T05:19:08.231Z>\n\nThe MarketingPrint provider types have been updated based on the actual API schema from the JSON schema documentation. The changes include:\n\n1. Created simplified `MarketingPrintHit` interface that focuses on the key fields needed:\n   - Required fields: designType, isPublished, name, thumbnail, tags, updated, url, objectID, and id \n   - Additional relevant fields: brand, club, designVisibility, _highlightResult, thumbnails\n\n2. Enhanced `MarketingContent` to include the additional fields through an `EnhancedMarketingContent` interface\n\n3. Updated the transformers to properly map from the API response to our enhanced content type\n\n4. Updated the test component to display all the relevant fields from the API response\n\n5. Fixed the tests to match the new data structure\n\nThese changes provide a more accurate representation of the actual API data while still maintaining compatibility with the existing `MarketingContent` interface structure.\n</info added on 2025-05-08T06:11:43.417Z>\n<info added on 2025-05-08T06:47:38.929Z>\n1. Create `useMarketingPrintDesigns.js` hook\n2. Configure API endpoint URL and parameters for marketing and print designs\n3. Implement data transformation to handle design metadata and thumbnails\n4. Handle filtering by design type and categories\n5. Implement debouncing for search queries\n6. Set up proper caching strategy with SWR\n7. Add comprehensive error handling\n8. Document the hook's usage and parameters\n<info added on 2025-05-08T05:19:08.231Z>\n1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook\n2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present\n3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`\n4. Leverage the existing shared `useApi` hook which already handles SWR caching\n5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails\n6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)\n7. Implement conditional fetching based on search terms (only fetch when search term is valid)\n8. Create formatting utility for consistent data display in results\n9. Utilize the error handling already provided by the useApi hook\n10. Handle filtering by design type and categories\n11. Implement debouncing for search queries\n12. Write tests similar to existing WikiSearch and StoreProducts hook tests\n13. Document the hook's usage and parameters\n</info added on 2025-05-08T05:19:08.231Z>\n<info added on 2025-05-08T06:11:43.417Z>\n1. Create `useMarketingPrintDesigns.js` hook\n2. Configure API endpoint URL and parameters for marketing and print designs\n3. Implement data transformation to handle design metadata and thumbnails\n4. Handle filtering by design type and categories\n5. Implement debouncing for search queries\n6. Set up proper caching strategy with SWR\n7. Add comprehensive error handling\n8. Document the hook's usage and parameters\n<info added on 2025-05-08T05:19:08.231Z>\n1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook\n2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present\n3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`\n4. Leverage the existing shared `useApi` hook which already handles SWR caching\n5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails\n6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)\n7. Implement conditional fetching based on search terms (only fetch when search term is valid)\n8. Create formatting utility for consistent data display in results\n9. Utilize the error handling already provided by the useApi hook\n10. Handle filtering by design type and categories\n11. Implement debouncing for search queries\n12. Write tests similar to existing WikiSearch and StoreProducts hook tests\n13. Document the hook's usage and parameters\n</info added on 2025-05-08T05:19:08.231Z>\n\nThe MarketingPrint provider types have been updated based on the actual API schema from the JSON schema documentation. The changes include:\n\n1. Created simplified `MarketingPrintHit` interface that focuses on the key fields needed:\n   - Required fields: designType, isPublished, name, thumbnail, tags, updated, url, objectID, and id \n   - Additional relevant fields: brand, club, designVisibility, _highlightResult, thumbnails\n\n2. Enhanced `MarketingContent` to include the additional fields through an `EnhancedMarketingContent` interface\n\n3. Updated the transformers to properly map from the API response to our enhanced content type\n\n4. Updated the test component to display all the relevant fields from the API response\n\n5. Fixed the tests to match the new data structure\n\nThese changes provide a more accurate representation of the actual API data while still maintaining compatibility with the existing `MarketingContent` interface structure.\n</info added on 2025-05-08T06:11:43.417Z>\n\nEnhanced the MarketingPrint provider with additional field mappings to improve the data representation:\n\n1. Implemented dimensions field by combining width and height properties:\n   - When both width and height are present, formats them as \"widthxheight\" (e.g., \"1920×1080\")\n   - Returns an empty string when either property is missing\n   - Added appropriate type definitions for these fields\n\n2. Added support for output_type field by mapping it directly from the format property in the API response\n\n3. Repurposed the design_count field to display resolution (DPI) information:\n   - Extracts DPI value from the API response\n   - Falls back to zero when the property doesn't exist\n\n4. Created comprehensive test suite to verify these new mappings:\n   - Tests for successful dimension formatting with valid width/height\n   - Tests for empty dimension string when width or height is missing\n   - Tests for output_type mapping from format property\n   - Tests for DPI resolution mapping to design_count\n   - Tests for proper fallback behavior with missing optional fields\n\n5. Updated the transformer functions to handle these new mappings while maintaining backward compatibility\n\nThese enhancements provide more detailed and useful information about marketing and print designs without breaking existing functionality.\n</info added on 2025-05-08T06:47:38.929Z>", "status": "done"}, {"id": 4, "title": "Implement SWR hook for CCTV People", "description": "Create a custom SWR hook to fetch and manage CCTV People data, replacing the existing Algolia implementation.", "dependencies": [], "details": "1. Create `useCctvPeople.js` hook\n2. Configure API endpoint URL and parameters for CCTV people search\n3. Implement data transformation to handle person records and images\n4. Implement special handling for facial recognition parameters if applicable\n5. Set up proper debouncing and caching strategy\n6. Add comprehensive error handling for failed image loads\n7. Implement integration with the parent component\n8. Create unit tests for the hook's functionality\n<info added on 2025-05-08T12:51:23.791Z>\n1. Create `useCctvPeople.js` hook\n2. Configure API endpoint URL and parameters for CCTV people search\n3. Implement data transformation to handle person records and images\n4. Implement special handling for facial recognition parameters if applicable\n5. Set up proper debouncing and caching strategy\n6. Add comprehensive error handling for failed image loads\n7. Implement integration with the parent component\n8. Create unit tests for the hook's functionality\n\nImplementation details:\n- Created a proper directory structure following established patterns:\n  - Created `src/features/FederatedSearch/providers/CctvPeople/` with hooks, types, and transformers directories\n- Implemented TypeScript interfaces for CCTV People data:\n  - Created `CctvPersonHit` to represent raw API data with properties like person<PERSON>, firstName, lastName, clubID, membershipStatus, avatar, shadowPhoto, facial recognition info\n  - Created `CctvPeopleContent` for transformed data to be consumed by UI components\n  - Added standard response interfaces for raw and normalized data\n- Added CCTV_TYPE and CCTV_LABEL constants to the shared constants file\n- Implemented data transformation functions:\n  - Created transformers to convert API data into UI-friendly format\n  - Added proper handling for avatar/shadow photo fallbacks\n  - Extracted facial recognition data\n  - Handled empty/missing fields with sensible defaults\n- Created the useCctvPeople hook:\n  - Implemented conditional fetching based on search term presence\n  - Used existing API_ENDPOINTS.cctvPeople configuration\n  - Added SWR configuration consistent with other hooks\n  - Applied proper data transformation for component consumption\n- Added comprehensive tests:\n  - Tests for successful data transformation\n  - Tests for conditional fetching behavior\n  - Tests for pagination parameter handling\n  - Error handling tests\n- The implementation follows the same patterns established by other provider hooks, maintaining consistency throughout the codebase\n</info added on 2025-05-08T12:51:23.791Z>\n<info added on 2025-05-08T12:54:24.075Z>\nAfter double-checking the implementation against the original requirements in react_search_migration_plan.md and algoliaSearch.js, I made the following adjustments:\n\n1. Updated CCTV_PEOPLE_SEARCH_ENDPOINT to match the original implementation:\n   - Changed the path from '/api/search/cctv-people' to '/api/cctv/people/:clubId'\n   - Changed the method from POST to GET\n   - Removed the headers configuration\n\n2. Updated CategoryTypes.ts to include CCTV_TYPE in the CategoryType union type:\n   - Added CCTV_TYPE to the import list\n   - Added typeof CCTV_TYPE to the CategoryType union\n\n3. Enhanced useCctvPeople hook to match the original implementation:\n   - Added clubId parameter to match the selectedID in the original code\n   - Added proper path parameter replacement for :clubId\n   - Changed data handling to use query parameters in the URL instead of request body\n   - Made fetching conditional on having both a non-empty search term and clubId\n\n4. Updated CctvPeopleOptions interface to include clubId parameter\n\n5. Updated tests to include clubId in all test cases\n\nThese changes ensure our implementation aligns with the behavior of the original algoliaSearch.js code while maintaining the modern React SWR hook architecture. The hook now properly replicates the API calls, parameter handling, and data transformation of the original implementation.\n</info added on 2025-05-08T12:54:24.075Z>", "status": "done"}, {"id": 5, "title": "Implement local storage utility for Recently Viewed Wiki Articles", "description": "Create a utility function to manage recently viewed wiki articles in localStorage", "details": "1. Create a utility function similar to the original `trackViewedArticle` in algoliaSearch.js\\n2. Implement `getRecentlyViewedArticles()` function to retrieve stored articles\\n3. Ensure proper JSON parsing and error handling for localStorage access\\n4. Keep track of the maximum number of articles to store (10)\\n5. Handle the case when localStorage is not available\\n6. Create unit tests for localStorage interaction\\n7. Document the utility functions and their parameters", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 6, "title": "Create data transformation utilities for different content types", "description": "Implement utility functions for transforming data from each API source into the format required by UI components", "details": "1. Analyze the original transformation logic in algoliaSearch.js for each content type\\n2. Create separate utility functions for each data type:\\n   - Wiki article formatting with HTML entity decoding and breadcrumb building\\n   - Store product description truncation and HTML stripping\\n   - Marketing & Print design tag highlighting based on search terms\\n   - CCTV people status formatting and avatar handling\\n3. Ensure highlighting functions properly handle matching terms in search results\\n4. Implement helpers for building URLs for each content type\\n5. Handle edge cases like missing images or incomplete data\\n6. Write comprehensive unit tests for each transformation function\\n7. Create documentation for each utility function", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 7, "title": "Implement URL navigation utility", "description": "Create a utility to handle URL building and navigation for different content types", "details": "1. Analyze the existing URL handling in algoliaSearch.js, particularly in `onStateChange`\\n2. Create a unified utility function to generate URLs for each content type:\\n   - Wiki articles (`/wiki/#${encodedPath}`)\\n   - Store products (`/store-ui/#%2Fstore%2F%3FsearchTerm%3D${encodedId}`)\\n   - Marketing & Print designs (`/designer/designs?tag=${encodedTag}&designId=${encodedId}`)\\n   - CCTV people (`/cctv-ui/#${encodedPath}`)\\n3. Implement special handling for CCTV iframe communication\\n4. Create a navigation function that handles URL opening based on content type\\n5. Support both internal navigation and opening in new tabs\\n6. Add error handling for malformed URLs\\n7. Create unit tests for URL generation and navigation functionality", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 8, "title": "Create shared hooks for data fetching patterns", "description": "Implement common hooks and utilities for shared data fetching patterns used across all search sources", "details": "1. Analyze common patterns in the data fetching logic from algoliaSearch.js\\n2. Create a `useDebounce` hook for search query inputs\\n3. Implement a `useFetch` utility that wraps SWR with consistent error handling\\n4. Create a shared configuration for API endpoints and parameters\\n5. Implement utilities for conditional fetching based on query presence\\n6. Create helpers for managing loading states and transitions\\n7. Add functionality for request cancellation if needed\\n8. Document how these shared hooks should be used in the individual data source hooks\\n9. Write unit tests for the shared hooks and utilities", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 9, "title": "Implement image lazy loading utility", "description": "Create a utility for lazy loading images in search results", "details": "1. Analyze the existing MutationObserver-based lazy loading in algoliaSearch.js\\n2. Create a modern React-based lazy loading solution for images in search results\\n3. Implement a custom hook that manages image loading state\\n4. Add fallback handling for image load failures\\n5. Implement progressive loading with placeholder/skeleton states\\n6. Ensure compatibility with the original implementation's features\\n7. Support different image types from various content sources (avatars, thumbnails, etc.)\\n8. Create unit tests for the lazy loading functionality\\n9. Document the hook's usage with different image sources", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 10, "title": "Create conditional rendering utility for search results", "description": "Implement utility functions to handle conditional rendering of search results based on query state", "details": "1. Analyze the existing conditional display logic in algoliaSearch.js\\n2. Create utility functions that determine when each source should be displayed:\\n   - Functions (shown only with non-empty query)\\n   - Recently Viewed Wiki Articles (shown only with empty query)\\n   - Topics (shown only with non-empty query)\\n   - Wiki Articles (different behavior for empty vs. non-empty query)\\n   - Store Products (shown only with non-empty query)\\n   - Marketing & Print (shown only with non-empty query)\\n   - CCTV People (shown only with non-empty query)\\n3. Implement a unified display management system for all sources\\n4. Create helpers for controlling the order of displayed sections\\n5. Add functionality for \\\"Show more results\\\" links based on total hits\\n6. Implement empty state handling (no results, loading states)\\n7. Write unit tests for the conditional rendering logic\\n8. Document the utility's usage patterns\n<info added on 2025-05-08T05:16:23.990Z>\n1. Analyze the existing conditional display logic in algoliaSearch.js\n2. Create utility functions that determine when each source should be displayed:\n   - Functions (shown only with non-empty query)\n   - Recently Viewed Wiki Articles (shown only with empty query)\n   - Topics (shown only with non-empty query)\n   - Wiki Articles (different behavior for empty vs. non-empty query)\n   - Store Products (shown only with non-empty query)\n   - Marketing & Print (shown only with non-empty query)\n   - CCTV People (shown only with non-empty query)\n3. Create helpers for controlling the order of displayed sections\n4. Add functionality for \"Show more results\" links based on total hits\n5. Implement empty state handling (no results, loading states)\n6. Write unit tests for the conditional rendering logic\n7. Document the utility's usage patterns\n</info added on 2025-05-08T05:16:23.990Z>", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 11, "title": "Implement integration tests for all data sources", "description": "Create comprehensive integration tests for all data source hooks and their interactions", "details": "1. Set up testing environment with mock API responses for all sources\\n2. Create fixtures that simulate the response data from each API endpoint\\n3. Implement integration tests for each data source hook:\\n   - Test data fetching with empty and non-empty queries\\n   - Verify proper data transformation\\n   - Test conditional fetching behavior\\n   - Verify error handling\\n4. Test integration between hooks and parent components\\n5. Test handling of loading states and transitions\\n6. Verify URL generation and navigation behavior\\n7. Test image loading and fallback behavior\\n8. Create documentation for the test suite\\n9. Set up CI integration for running tests", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 12, "title": "Implement Performance Hub Features (functions) search utility", "description": "Create a utility function to search Performance Hub features/routes based on user input", "details": "1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n<info added on 2025-05-14T11:54:22.966Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. <PERSON>reate helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n\nBased on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:\n\n1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts\n2. Define TypeScript interfaces for:\n   - PageRoute (representing items in availablePageRoutes)\n   - SearchResult (representing the formatted search results)\n3. Implement the core search function that:\n   - Takes a query string and the availablePageRoutes array as parameters\n   - Normalizes text by replacing '&' with 'and' in both query and titles\n   - Filters routes where the normalized title includes the normalized query\n   - Calculates relevance score based on the position of match in the title\n   - Sorts results by relevance score (lower index = higher relevance)\n   - Limits to top 3 most relevant matches\n   - Returns empty array for empty/undefined queries\n4. Ensure the implementation has no dependencies on jQuery or lodash\n5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)\n6. Write unit tests covering all edge cases and functionality\n7. Document the API with JSDoc comments for better developer experience\n</info added on 2025-05-14T11:54:22.966Z>\n<info added on 2025-05-14T11:59:00.939Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n<info added on 2025-05-14T11:54:22.966Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n\nBased on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:\n\n1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts\n2. Define TypeScript interfaces for:\n   - PageRoute (representing items in availablePageRoutes)\n   - SearchResult (representing the formatted search results)\n3. Implement the core search function that:\n   - Takes a query string and the availablePageRoutes array as parameters\n   - Normalizes text by replacing '&' with 'and' in both query and titles\n   - Filters routes where the normalized title includes the normalized query\n   - Calculates relevance score based on the position of match in the title\n   - Sorts results by relevance score (lower index = higher relevance)\n   - Limits to top 3 most relevant matches\n   - Returns empty array for empty/undefined queries\n4. Ensure the implementation has no dependencies on jQuery or lodash\n5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)\n6. Write unit tests covering all edge cases and functionality\n7. Document the API with JSDoc comments for better developer experience\n</info added on 2025-05-14T11:54:22.966Z>\n\n<info added on 2025-05-15T09:32:45.123Z>\nImplementation completed successfully with the following structure and details:\n\n1. Directory Structure:\n   - Created `src/features/FederatedSearch/types/HubFeatureTypes.ts` for type definitions\n   - Implemented main utility in `src/features/FederatedSearch/lib/hubFeaturesSearch.ts`\n   - Added comprehensive test suite in `src/features/FederatedSearch/lib/__tests__/hubFeaturesSearch.test.ts`\n   - Created mock data in `src/features/FederatedSearch/mockData/hubFeatures.ts`\n   - Updated relevant index files to export the new functionality\n\n2. Implementation Details:\n   - Developed `searchHubFeatures` utility function that accepts a query string and routes array\n   - Implemented text normalization by replacing '&' with 'and' in both titles and query strings\n   - Created relevance-based sorting algorithm that prioritizes matches appearing earlier in titles\n   - Added configurable limit parameter (defaulting to 3 results)\n   - Ensured proper handling of empty/null/undefined queries (returns empty array)\n   - Implemented term-by-term matching approach for better handling of partial matches in multi-word queries\n   - Eliminated dependencies on jQuery and lodash while maintaining equivalent functionality\n\n3. Testing:\n   - Wrote 10 comprehensive test cases covering all requirements\n   - Tested text normalization functionality\n   - Verified correct sorting by relevance\n   - Confirmed proper limiting of results\n   - Validated handling of edge cases\n   - Ensured empty queries return empty results\n   - All tests are now passing\n\nThe implementation successfully replaces the Algolia-based search for Performance Hub features while maintaining the same functionality and eliminating external dependencies.\n</info added on 2025-05-15T09:32:45.123Z>\n</info added on 2025-05-14T11:59:00.939Z>\n<info added on 2025-05-14T12:35:44.109Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n<info added on 2025-05-14T11:54:22.966Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n\nBased on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:\n\n1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts\n2. Define TypeScript interfaces for:\n   - PageRoute (representing items in availablePageRoutes)\n   - SearchResult (representing the formatted search results)\n3. Implement the core search function that:\n   - Takes a query string and the availablePageRoutes array as parameters\n   - Normalizes text by replacing '&' with 'and' in both query and titles\n   - Filters routes where the normalized title includes the normalized query\n   - Calculates relevance score based on the position of match in the title\n   - Sorts results by relevance score (lower index = higher relevance)\n   - Limits to top 3 most relevant matches\n   - Returns empty array for empty/undefined queries\n4. Ensure the implementation has no dependencies on jQuery or lodash\n5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)\n6. Write unit tests covering all edge cases and functionality\n7. Document the API with JSDoc comments for better developer experience\n</info added on 2025-05-14T11:54:22.966Z>\n<info added on 2025-05-14T11:59:00.939Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n<info added on 2025-05-14T11:54:22.966Z>\n1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)\n2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:\n   - Filter routes based on title matches with the query\n   - Replace '&' with 'and' in both title and query for better matching\n   - Sort results by relevance (how early the match appears in the title)\n   - Limit results to the top 3 most relevant matches\n3. Create helper functions to format the search results for display\n4. Implement URL building for navigation to the matched features\n5. Ensure results are only returned for non-empty queries\n6. Write comprehensive unit tests for:\n   - Correct filtering and matching behavior\n   - Proper sorting by relevance\n   - Correct limiting to top 3 results\n   - Empty query handling (should return empty array)\n7. Create types for Performance Hub features data structure\n8. Document the utility function's usage and parameters\n\nBased on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:\n\n1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts\n2. Define TypeScript interfaces for:\n   - PageRoute (representing items in availablePageRoutes)\n   - SearchResult (representing the formatted search results)\n3. Implement the core search function that:\n   - Takes a query string and the availablePageRoutes array as parameters\n   - Normalizes text by replacing '&' with 'and' in both query and titles\n   - Filters routes where the normalized title includes the normalized query\n   - Calculates relevance score based on the position of match in the title\n   - Sorts results by relevance score (lower index = higher relevance)\n   - Limits to top 3 most relevant matches\n   - Returns empty array for empty/undefined queries\n4. Ensure the implementation has no dependencies on jQuery or lodash\n5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)\n6. Write unit tests covering all edge cases and functionality\n7. Document the API with JSDoc comments for better developer experience\n</info added on 2025-05-14T11:54:22.966Z>\n\n<info added on 2025-05-15T09:32:45.123Z>\nImplementation completed successfully with the following structure and details:\n\n1. Directory Structure:\n   - Created `src/features/FederatedSearch/types/HubFeatureTypes.ts` for type definitions\n   - Implemented main utility in `src/features/FederatedSearch/lib/hubFeaturesSearch.ts`\n   - Added comprehensive test suite in `src/features/FederatedSearch/lib/__tests__/hubFeaturesSearch.test.ts`\n   - Created mock data in `src/features/FederatedSearch/mockData/hubFeatures.ts`\n   - Updated relevant index files to export the new functionality\n\n2. Implementation Details:\n   - Developed `searchHubFeatures` utility function that accepts a query string and routes array\n   - Implemented text normalization by replacing '&' with 'and' in both titles and query strings\n   - Created relevance-based sorting algorithm that prioritizes matches appearing earlier in titles\n   - Added configurable limit parameter (defaulting to 3 results)\n   - Ensured proper handling of empty/null/undefined queries (returns empty array)\n   - Implemented term-by-term matching approach for better handling of partial matches in multi-word queries\n   - Eliminated dependencies on jQuery and lodash while maintaining equivalent functionality\n\n3. Testing:\n   - Wrote 10 comprehensive test cases covering all requirements\n   - Tested text normalization functionality\n   - Verified correct sorting by relevance\n   - Confirmed proper limiting of results\n   - Validated handling of edge cases\n   - Ensured empty queries return empty results\n   - All tests are now passing\n\nThe implementation successfully replaces the Algolia-based search for Performance Hub features while maintaining the same functionality and eliminating external dependencies.\n</info added on 2025-05-15T09:32:45.123Z>\n</info added on 2025-05-14T11:59:00.939Z>\n\n<info added on 2025-05-16T14:22:18.000Z>\nSuccessfully completed the implementation and integration of Hub Features search functionality with the following structure and improvements:\n\n1. Created a new provider structure in the FederatedSearch feature:\n   - Created types in `/providers/HubFeatures/types/HubFeatureTypes.ts` with proper interfaces for PageRoute and SearchResult\n   - Implemented search utility in `/providers/HubFeatures/lib/hubFeaturesSearch.ts` with the core search algorithm\n   - Created custom hook in `/providers/HubFeatures/hooks/useHubFeaturesSearch.ts` to handle data fetching and state management\n   - Added formatting function in `/providers/HubFeatures/format/index.ts` to transform search results into the required display format\n   - Added mock data for testing in `/providers/HubFeatures/mockData/hubFeatures.ts`\n\n2. Added new constants to support Hub features:\n   - Added `HUB_TYPE` and `HUB_LABEL` to shared constants for consistent type identification\n   - Updated column configurations to support the new type in the results display\n\n3. Integrated the search into ResultList.tsx:\n   - Added the hook to fetch Hub Features data alongside other data sources\n   - Applied the formatter to prepare data for display in the unified results list\n   - Added results to the dataSources array with proper typing\n   - Ensured proper error handling and type safety throughout the integration\n\n4. Fixed all TypeScript and linting errors to ensure code quality and maintainability.\n\nThe implementation successfully passed building and is now fully integrated into the search results page, following the existing architectural patterns. This completes the transition from Algolia-based search to our custom implementation for Performance Hub features.\n</info added on 2025-05-16T14:22:18.000Z>\n</info added on 2025-05-14T12:35:44.109Z>", "status": "done", "dependencies": [], "parentTaskId": 6}, {"id": 13, "title": "Implement Wiki Topics & Categories Search Integration", "description": "Add a new search source for Wiki Topics & Categories: fetch and transform topic/subtopic data from `/openkb/api/topics` and integrate it into the GlobalSearchController as per `react_search_migration_plan.md`, following patterns in existing providers.", "details": "Before implementation, confirm the API response shape for `/openkb/api/topics` (including `topic.svg` and `subtopics` array). Then create a custom hook (`useTopicsSearch`) in `src/features/FederatedSearch/providers/TopicsSearch` that:\n- Fetches data and handles loading/error states\n- Transforms each topic into `{ topic, subtopic, icon (base64 SVG), label, url }`\n- Filters and matches based on the search query\n\nIntegrate this hook into the GlobalSearchController under the `topics` source. Follow file structure, naming conventions, and error/loading patterns used by other providers (e.g., StoreProducts, CctvPeople).", "status": "done", "dependencies": [], "parentTaskId": 6}]}, {"id": 7, "title": "<PERSON><PERSON><PERSON> Table for All Data Sources", "description": "Extend the ResultList component to handle all data sources with appropriate rendering", "status": "done", "dependencies": [5, 6], "priority": "medium", "details": "Update the ResultList component to handle all data sources. Create additional column definitions and cell renderers for the new data types. Implement source-specific icons and formatting. Add 'Show More' footer logic based on total results vs. displayed. Ensure consistent skeleton loading for all sources.", "testStrategy": "Test ResultList with data from all sources. Verify correct rendering of each source type. Test 'Show More' functionality. Ensure skeleton loaders work consistently across all sources."}, {"id": 8, "title": "Implement Result Navigation with postMessage", "description": "Create the mechanism for communicating with the parent frame when results are clicked", "status": "deferred", "dependencies": [7], "priority": "medium", "details": "Implement click handlers for search results that extract the target URL. Create a utility function to send navigation requests to the parent frame using window.postMessage. Add origin validation for security. Implement a mechanism to receive configuration from the parent frame if needed. Add visual feedback for clicked results.", "testStrategy": "Mock window.postMessage and verify correct messages are sent when results are clicked. Test origin validation. Verify visual feedback works correctly."}, {"id": 9, "title": "Implement UI Refinements and Keyboard Navigation", "description": "Add term highlighting in results and implement keyboard navigation", "status": "deferred", "dependencies": [7, 8], "priority": "low", "details": "Create a utility to highlight search terms within result text. Implement keyboard navigation within the results list (arrow keys, Enter for selection). Add focus indicators for accessibility. Optimize rendering performance for large result sets. Enhance error states with retry options.", "testStrategy": "Test term highlighting with various search terms. Verify keyboard navigation works correctly. Test accessibility with screen readers. Measure and verify performance with large datasets."}, {"id": 10, "title": "Final Integration and Testing", "description": "Integrate all components, perform comprehensive testing, and prepare for deployment", "status": "deferred", "dependencies": [9], "priority": "medium", "details": "Connect all components into the final application. Implement comprehensive error boundaries. Add logging for critical operations. Optimize bundle size. Create production build configuration. Test the application in an iframe context similar to the production environment. Document any known issues or limitations.", "testStrategy": "Perform end-to-end testing of the complete application flow. Test in various browsers. Verify iframe integration works correctly. Test with real API endpoints if possible. Validate bundle size and performance metrics."}, {"id": 11, "title": "Fix Failing Tests Due to Updated Component Logic and Testing Configuration", "description": "Update test suites to align with new component behaviors and fix Jest-DOM configuration issues causing test failures across ResultList, UI Cell Renderers, Column Configuration, and Wiki Search Format tests.", "details": "This task involves addressing several test failures resulting from updated component logic:\n\n1. ResultList Component Tests:\n   - Update tests to expect the new search information message when no search term is provided\n   - Modify assertions that expect 'No search results found' to match the new behavior\n   - Ensure all test cases cover both scenarios: with and without search terms\n\n2. UI Cell Renderers Tests:\n   - Fix the 'Invalid Chai property' errors by properly configuring Jest-DOM\n   - Ensure the testing setup properly imports and extends Jest with the required DOM assertions\n   - Check for potential version conflicts between testing libraries\n   - Update package.json and test setup files to correctly configure Jest-DOM\n\n3. Column Configuration Tests:\n   - Update test expectations for Wiki, Store, and Marketing column counts\n   - Modify assertions to match the new column configuration structure\n   - Consider parameterizing tests to make them more resilient to future column count changes\n\n4. Wiki Search Format Tests:\n   - Update test expectations to match the new formatter return structure\n   - Refactor tests to be more flexible with the formatter output format\n   - Add tests that specifically validate the new structure properties\n\nDo not modify the component logic to match outdated tests. Instead, ensure all tests are updated to reflect the current implementation. Document any significant changes in test approach for future reference.", "testStrategy": "To verify this task has been completed successfully:\n\n1. Run the full test suite and confirm all previously failing tests now pass\n2. For ResultList tests:\n   - Verify tests correctly expect the search information message when no search term is provided\n   - Confirm tests still validate correct behavior when search terms are provided\n\n3. For UI Cell Renderer tests:\n   - Confirm Jest-DOM assertions like 'toBeInTheDocument', 'toHaveClass', and 'toHaveAttribute' work properly\n   - Run a sample test that uses each of these assertions to verify configuration is correct\n\n4. For Column Configuration tests:\n   - Verify tests correctly expect the new column counts for Wiki, Store, and Marketing\n   - Ensure tests are not hardcoding column counts where possible to prevent future failures\n\n5. For Wiki Search Format tests:\n   - Confirm tests correctly validate the new formatter structure\n   - Verify edge cases are still covered with the updated test assertions\n\n6. Code Review:\n   - Have another developer review the test changes to ensure they maintain proper test coverage\n   - Verify no test logic was modified to artificially pass tests without actually validating functionality\n\n7. Documentation:\n   - Document any significant changes to testing approach in relevant documentation\n   - Update test documentation if the expected component behavior has changed substantially", "status": "done", "dependencies": [5], "priority": "high", "subtasks": []}, {"id": 12, "title": "Implement Card View for Specific Search Categories", "description": "Develop a new card-based UI for displaying search results from categories like Wiki Topics and Performance Hub. These results will be shown as horizontally stacked cards, suitable for categories with limited data, instead of the standard tabular view. The implementation should be modular to allow easy extension to other categories.", "status": "done", "dependencies": [], "priority": "medium", "details": "1.  **Identify Target Categories:**\n    *   Confirm 'Wiki Topics' and 'Performance Hub' as initial targets.\n    *   Design a mechanism (e.g., configuration object, category metadata) to flag categories for card view.\n2.  **Design Reusable Card Component:**\n    *   Create a `SearchResultCard` component.\n    *   Props: title, snippet, source, link, icon (if applicable), etc.\n    *   Style: Visually distinct, clear, and concise.\n3.  **Develop Horizontal Card Layout Container:**\n    *   Create a `CardViewContainer` component.\n    *   Accepts a list of search results.\n    *   Renders `SearchResultCard` components in a horizontal, scrollable (if necessary) layout.\n    *   Ensure responsiveness: cards should stack or adjust appropriately on smaller screens.\n4.  **Implement Conditional Rendering Logic:**\n    *   In the main search results display area, check the category of the current results.\n    *   If the category is flagged for card view, render `CardViewContainer`.\n    *   Otherwise, render the existing tabular view.\n5.  **Modularity and Configuration:**\n    *   Ensure the system allows new categories to be easily configured to use the card view without extensive code changes.\n    *   This could involve updating a central configuration file or adding a property to the category definition objects.\n6.  **Styling and Theming:**\n    *   Ensure card view aligns with the overall application's design language.\n    *   Use existing styling conventions (e.g., TailwindCSS if used in the project).\n7.  **Accessibility:**\n    *   Ensure cards and the card view container are keyboard navigable.\n    *   Provide appropriate ARIA attributes.\n", "testStrategy": "1.  **Unit Tests:**\n    *   `SearchResultCard` component: Test rendering with various props, interactions (if any).\n    *   `CardViewContainer` component: Test rendering of multiple cards, layout behavior.\n    *   Logic for selecting view type (card vs. table) based on category.\n2.  **Manual Testing:**\n    *   Verify responsiveness of the card view on different screen sizes and devices.\n    *   Test with varying numbers of results in card view categories.\n    *   Check accessibility (keyboard navigation, screen reader compatibility).\n", "subtasks": [{"id": 1, "title": "Create Category Configuration System", "description": "Implement a configuration system to identify which search categories should use the card view instead of the tabular view.", "dependencies": [], "details": "Create a configuration object or metadata system that flags categories for card view. Start with 'Wiki Topics' and 'Performance Hub' as initial targets. The configuration should include a boolean flag 'useCardView' and potentially other card-specific display options. Implement this as a central configuration file that can be easily updated when adding new categories.", "status": "done", "testStrategy": "Write unit tests to verify that the configuration correctly identifies card view categories. Test edge cases like missing configuration entries."}, {"id": 2, "title": "Develop Reusable SearchResultCard Component", "description": "Create a reusable card component that displays individual search results in a visually appealing card format.", "dependencies": [], "details": "Build a SearchResultCard component that accepts props including title, snippet, source, link, and icon. Style the component to be visually distinct while maintaining the application's design language. Ensure the card has appropriate hover states, focus indicators, and clickable areas. Use existing styling conventions (e.g., TailwindCSS) for consistency.", "status": "done", "testStrategy": "Create component tests to verify the card renders correctly with different prop combinations. Test accessibility features like keyboard focus and screen reader compatibility."}, {"id": 3, "title": "Implement CardViewContainer Component", "description": "Create a container component that arranges multiple SearchResultCard components in a horizontal, scrollable layout.", "dependencies": [2], "details": "Develop a CardViewContainer component that accepts an array of search results and renders them as SearchResultCard components. Implement horizontal scrolling for overflow content. Ensure the container is responsive, with cards stacking or adjusting appropriately on smaller screens. Add navigation controls if needed for better user experience.", "status": "done", "testStrategy": "Test the container with various numbers of cards to ensure proper layout and scrolling behavior. Verify responsive design across different screen sizes."}, {"id": 4, "title": "Implement Conditional Rendering Logic", "description": "Add logic to the main search results component to conditionally render either the card view or tabular view based on the category configuration.", "dependencies": [1, 3], "details": "Modify the main search results display component to check if the current category is flagged for card view in the configuration. If it is, render the CardViewContainer with the search results; otherwise, render the existing tabular view. Ensure smooth transitions between views when switching categories.", "status": "done", "testStrategy": "Write integration tests that verify the correct view is displayed for different categories. Test the transition between views when changing search categories."}, {"id": 5, "title": "Enhance Accessibility and Finalize Styling", "description": "Ensure the card view implementation is fully accessible and visually consistent with the application's design language.", "dependencies": [2, 3, 4], "details": "Add appropriate ARIA attributes to both the SearchResultCard and CardViewContainer components. Implement keyboard navigation for the horizontal card layout, allowing users to tab through cards and use arrow keys for scrolling. Finalize styling to ensure visual consistency with the rest of the application. Add any missing hover states, focus indicators, or animations to enhance the user experience.", "status": "done", "testStrategy": "Conduct accessibility testing using screen readers and keyboard-only navigation. Perform visual regression testing to ensure the card view maintains consistency with the application's design."}]}, {"id": 13, "title": "Add Clear Button to Filter Select Component", "description": "Implement a clear button (X icon) within the filter select component that appears when a selection is active and allows users to reset their selection with a single click.", "details": "Implementation details:\n\n1. UI Implementation:\n   - Add an X icon button positioned on the right side of the filter text input\n   - The button should only render when there is an active selection in the component\n   - Ensure the button is properly aligned and sized according to the design system\n   - Implement proper spacing to avoid overlapping with other UI elements\n\n2. Functionality:\n   - When clicked, the button should clear the current selection and reset the component state\n   - The component should fire appropriate change events when the selection is cleared\n   - Ensure the filter's onChange handlers are properly triggered\n\n3. Styling:\n   - Style the clear button to match the existing design system\n   - Implement hover state with appropriate color/opacity changes\n   - Implement focus state with visible focus ring for accessibility\n   - Ensure the button has sufficient contrast against its background\n\n4. Accessibility:\n   - Add aria-label=\"Clear selection\" to the button\n   - Ensure the button is keyboard focusable (tabindex=\"0\")\n   - Add keyboard support to trigger the clear action when Enter or Space is pressed\n   - Update any relevant ARIA attributes on the parent component when selection is cleared\n   - Verify screen reader compatibility\n\n5. Integration:\n   - Ensure the clear button works with all existing filter select component variations\n   - Verify that clearing the selection properly updates any dependent components or state\n\n6. Code Organization:\n   - Consider creating a separate sub-component for the clear button if appropriate\n   - Ensure proper prop passing and state management\n   - Document the new functionality in component documentation", "testStrategy": "Testing approach:\n\n1. Unit Tests:\n   - Test that the clear button is not rendered when no selection is active\n   - Test that the clear button appears when a selection is made\n   - Test that clicking the clear button resets the selection\n   - Test that appropriate change events are fired when selection is cleared\n   - Test keyboard interaction (Enter and Space keys should trigger clearing)\n   - Test that focus is properly managed after clearing\n\n2. Accessibility Testing:\n   - Verify that the button has the correct ARIA attributes\n   - Test keyboard navigation to and from the clear button\n   - Verify screen reader announces the button correctly\n   - Test color contrast ratios meet WCAG AA standards\n\n3. Integration Testing:\n   - Test the clear button functionality within forms that use the filter select\n   - Verify that clearing a selection properly updates any dependent components\n   - Test in different viewport sizes to ensure responsive behavior\n\n4. Visual Regression Testing:\n   - Capture screenshots before and after implementation to verify visual consistency\n   - Verify hover and focus states match design specifications\n\n5. Browser Compatibility:\n   - Test across supported browsers (Chrome, Firefox, Safari, Edge)\n   - Verify mobile browser compatibility\n\n6. Manual Testing Checklist:\n   - Verify the clear button is properly positioned in all component states\n   - Check that hover and focus states work as expected\n   - Test with keyboard-only navigation\n   - Test with screen readers (NVDA, VoiceOver)\n   - Verify the component works correctly after multiple selection/clearing cycles", "status": "done", "dependencies": [], "priority": "medium", "subtasks": []}, {"id": 14, "title": "Task #14: Implement URL Parameter for Category Filters with Basic Pagination", "description": "Add functionality to update the URL with the selected category filter parameter and implement a simple pagination system for filtered results.", "status": "done", "dependencies": [], "priority": "medium", "details": "This task involves enhancing the existing filter functionality to reflect the selected category in the URL parameters, making filtered states shareable and bookmarkable. The implementation should:\n\n1. Modify the category filter component to update the URL when a category is selected (e.g., ?category=performance-hub)\n2. Ensure the application reads the URL parameters on load to set the initial filter state\n3. Implement URL parameter handling using the router's query parameter capabilities\n4. Add state persistence so that navigating back/forward in browser history maintains the correct filter state\n5. Implement a basic pagination component with:\n   - Previous/Next buttons\n   - Current page indicator (e.g., \"Page 2 of 5\")\n   - Simple page number display\n   - Appropriate disabled states for pagination controls when at first/last page\n6. Connect the pagination component to the filtered results data\n7. Ensure the pagination state is also reflected in URL parameters (e.g., ?category=performance-hub&page=2)\n8. Make the pagination component visually simple as it will be styled manually later\n9. Handle edge cases such as:\n   - Invalid category parameters in URL\n   - Out-of-range page numbers\n   - Empty result sets\n\nThis implementation should work alongside the existing filter components and integrate with the current search/filter architecture.", "testStrategy": "Testing should verify both the URL parameter functionality and the pagination implementation:\n\n1. **Unit Tests:**\n   - Test URL parameter setting when a category is selected\n   - Test URL parameter reading on component mount\n   - Test pagination component rendering in various states (first page, middle page, last page)\n   - Test pagination navigation logic\n   - Test edge cases (invalid parameters, empty results)\n\n2. **Integration Tests:**\n   - Verify that selecting a category updates both the UI and the URL\n   - Verify that loading a page with URL parameters correctly sets the filter state\n   - Test that pagination controls update the result set correctly\n   - Verify browser back/forward navigation works with the filter and pagination state\n\n3. **End-to-End Tests:**\n   - Complete user flow test: select category, paginate through results, use browser navigation, and verify state consistency\n   - Test URL sharing functionality by simulating loading the application with various URL parameters\n\n4. **Manual Testing:**\n   - Verify that direct URL entry with parameters works as expected\n   - Check that all pagination controls function correctly\n   - Confirm that the URL updates correctly when navigating through pages\n   - Verify that the filter selection is maintained when using pagination\n   - Test across different browsers to ensure consistent behavior", "subtasks": [{"id": "14.1", "title": "Create useUrlParams custom hook", "description": "Developed a custom hook to handle URL parameter state for both category filters and pagination, enabling consistent URL parameter management across components.", "status": "completed"}, {"id": "14.2", "title": "Implement URL parameter synchronization", "description": "Added functionality to synchronize URL parameters with the application state for both category selection and pagination, ensuring shareable and bookmarkable URLs.", "status": "completed"}, {"id": "14.3", "title": "Create Pagination component", "description": "Built a simple Pagination component with Previous/Next buttons and current page indicator that only displays when multiple pages are available.", "status": "completed"}, {"id": "14.4", "title": "Update SearchFilterBar component", "description": "Modified the SearchFilterBar component to accept and use the selected category from URL parameters, ensuring consistency between URL state and UI.", "status": "completed"}, {"id": "14.5", "title": "Update ResultList for pagination", "description": "Enhanced the ResultList component to support pagination with currentPage and onPageChange props, connecting the UI to the pagination state.", "status": "completed"}, {"id": "14.6", "title": "Integrate URL parameters in Home component", "description": "Updated the Home component to manage URL parameters and pass them correctly to child components, creating a central management point for URL state.", "status": "completed"}, {"id": "14.7", "title": "Update API calls with pagination parameters", "description": "Modified API calls to include the current page from URL parameters, ensuring data fetching is synchronized with the pagination state.", "status": "completed"}, {"id": "14.8", "title": "Handle edge cases", "description": "Implemented handling for edge cases including invalid categories, out-of-range page numbers, and empty result sets to ensure a robust user experience.", "status": "completed"}]}], "metadata": {"projectName": "Search Results Applet Implementation", "totalTasks": 10, "sourceFile": "/Volumes/Keen/Desk/Career/Valhalla/UBX/portal-core/portal-front-end/applets/search-results-page/scripts/search_applet_prd.txt", "generatedAt": "2023-11-15"}}