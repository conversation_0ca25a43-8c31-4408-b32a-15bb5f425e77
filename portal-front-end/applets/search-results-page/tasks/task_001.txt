# Task ID: 1
# Title: Project Setup with FSD Architecture
# Status: done
# Dependencies: None
# Priority: high
# Description: Initialize the project with required dependencies and set up the Feature-Sliced Design architecture structure
# Details:
Create a new Vite project with React and TypeScript. Install dependencies: TailwindCSS, SWR, Valtio, TanStack Table, clsx/tailwind-merge, Vitest/React Testing Library. Set up the FSD architecture with folders for features, entities, shared, etc. Configure TailwindCSS and create the cn utility function. Set up the basic iframe entry point that will be embedded in the legacy application.

# Test Strategy:
Verify project builds successfully. Create basic smoke tests to ensure the application renders without errors. Test the cn utility function.
