# Task ID: 6
# Title: Implement Remaining SWR Hooks for All Data Sources
# Status: done
# Dependencies: 4
# Priority: medium
# Description: Create SWR hooks for the remaining data sources and ensure proper data transformation for display
# Details:
Implement SWR hooks for the remaining data sources: Recently Viewed Wiki Articles (recent wiki searches), Store Products (store-products), Marketing & Print Designs (marketingprint), and CCTV People (cctv-people). Follow the patterns established in the initial hooks. Ensure consistent error handling and data transformation across all sources. Pay special attention to how each content type's data should be transformed for display in search results.

# Test Strategy:
Write unit tests for each new SWR hook. Test data transformation functions with sample data. Verify consistent behavior across all data sources.

# Subtasks:
## 2. Implement SWR hook for Store Products [done]
### Dependencies: None
### Description: Create a custom SWR hook to fetch and manage Store Products data, replacing the existing Algolia implementation.
### Details:
1. Create `useStoreProducts.js` hook
2. Configure API endpoint URL and parameters for store products
3. Implement data transformation to convert API response to the format expected by the UI
4. Handle special cases for product categories and filtering
5. Implement debouncing and pagination if required
6. Set up proper caching strategy with SWR
7. Add comprehensive error handling
8. Document the hook's usage and parameters

## 3. Implement SWR hook for Marketing & Print Designs [done]
### Dependencies: None
### Description: Create a custom SWR hook to fetch and manage Marketing & Print Designs data, replacing the existing Algolia implementation.
### Details:
1. Create `useMarketingPrintDesigns.js` hook
2. Configure API endpoint URL and parameters for marketing and print designs
3. Implement data transformation to handle design metadata and thumbnails
4. Handle filtering by design type and categories
5. Implement debouncing for search queries
6. Set up proper caching strategy with SWR
7. Add comprehensive error handling
8. Document the hook's usage and parameters
<info added on 2025-05-08T05:19:08.231Z>
1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook
2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present
3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`
4. Leverage the existing shared `useApi` hook which already handles SWR caching
5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails
6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)
7. Implement conditional fetching based on search terms (only fetch when search term is valid)
8. Create formatting utility for consistent data display in results
9. Utilize the error handling already provided by the useApi hook
10. Handle filtering by design type and categories
11. Implement debouncing for search queries
12. Write tests similar to existing WikiSearch and StoreProducts hook tests
13. Document the hook's usage and parameters
</info added on 2025-05-08T05:19:08.231Z>
<info added on 2025-05-08T06:11:43.417Z>
1. Create `useMarketingPrintDesigns.js` hook
2. Configure API endpoint URL and parameters for marketing and print designs
3. Implement data transformation to handle design metadata and thumbnails
4. Handle filtering by design type and categories
5. Implement debouncing for search queries
6. Set up proper caching strategy with SWR
7. Add comprehensive error handling
8. Document the hook's usage and parameters
<info added on 2025-05-08T05:19:08.231Z>
1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook
2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present
3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`
4. Leverage the existing shared `useApi` hook which already handles SWR caching
5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails
6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)
7. Implement conditional fetching based on search terms (only fetch when search term is valid)
8. Create formatting utility for consistent data display in results
9. Utilize the error handling already provided by the useApi hook
10. Handle filtering by design type and categories
11. Implement debouncing for search queries
12. Write tests similar to existing WikiSearch and StoreProducts hook tests
13. Document the hook's usage and parameters
</info added on 2025-05-08T05:19:08.231Z>

The MarketingPrint provider types have been updated based on the actual API schema from the JSON schema documentation. The changes include:

1. Created simplified `MarketingPrintHit` interface that focuses on the key fields needed:
   - Required fields: designType, isPublished, name, thumbnail, tags, updated, url, objectID, and id 
   - Additional relevant fields: brand, club, designVisibility, _highlightResult, thumbnails

2. Enhanced `MarketingContent` to include the additional fields through an `EnhancedMarketingContent` interface

3. Updated the transformers to properly map from the API response to our enhanced content type

4. Updated the test component to display all the relevant fields from the API response

5. Fixed the tests to match the new data structure

These changes provide a more accurate representation of the actual API data while still maintaining compatibility with the existing `MarketingContent` interface structure.
</info added on 2025-05-08T06:11:43.417Z>
<info added on 2025-05-08T06:47:38.929Z>
1. Create `useMarketingPrintDesigns.js` hook
2. Configure API endpoint URL and parameters for marketing and print designs
3. Implement data transformation to handle design metadata and thumbnails
4. Handle filtering by design type and categories
5. Implement debouncing for search queries
6. Set up proper caching strategy with SWR
7. Add comprehensive error handling
8. Document the hook's usage and parameters
<info added on 2025-05-08T05:19:08.231Z>
1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook
2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present
3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`
4. Leverage the existing shared `useApi` hook which already handles SWR caching
5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails
6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)
7. Implement conditional fetching based on search terms (only fetch when search term is valid)
8. Create formatting utility for consistent data display in results
9. Utilize the error handling already provided by the useApi hook
10. Handle filtering by design type and categories
11. Implement debouncing for search queries
12. Write tests similar to existing WikiSearch and StoreProducts hook tests
13. Document the hook's usage and parameters
</info added on 2025-05-08T05:19:08.231Z>
<info added on 2025-05-08T06:11:43.417Z>
1. Create `useMarketingPrintDesigns.js` hook
2. Configure API endpoint URL and parameters for marketing and print designs
3. Implement data transformation to handle design metadata and thumbnails
4. Handle filtering by design type and categories
5. Implement debouncing for search queries
6. Set up proper caching strategy with SWR
7. Add comprehensive error handling
8. Document the hook's usage and parameters
<info added on 2025-05-08T05:19:08.231Z>
1. Create `src/features/FederatedSearch/providers/MarketingPrint/hooks/useMarketingPrintDesigns.ts` hook
2. Define the Marketing & Print Designs endpoint in the API_ENDPOINTS object if not already present
3. Create type definitions in `src/features/FederatedSearch/providers/MarketingPrint/types/MarketingPrintTypes.ts`
4. Leverage the existing shared `useApi` hook which already handles SWR caching
5. Implement data transformation in a separate transformer file to handle design metadata and thumbnails
6. Configure SWR with settings consistent with other hooks (revalidateOnFocus: false, revalidateIfStale: true)
7. Implement conditional fetching based on search terms (only fetch when search term is valid)
8. Create formatting utility for consistent data display in results
9. Utilize the error handling already provided by the useApi hook
10. Handle filtering by design type and categories
11. Implement debouncing for search queries
12. Write tests similar to existing WikiSearch and StoreProducts hook tests
13. Document the hook's usage and parameters
</info added on 2025-05-08T05:19:08.231Z>

The MarketingPrint provider types have been updated based on the actual API schema from the JSON schema documentation. The changes include:

1. Created simplified `MarketingPrintHit` interface that focuses on the key fields needed:
   - Required fields: designType, isPublished, name, thumbnail, tags, updated, url, objectID, and id 
   - Additional relevant fields: brand, club, designVisibility, _highlightResult, thumbnails

2. Enhanced `MarketingContent` to include the additional fields through an `EnhancedMarketingContent` interface

3. Updated the transformers to properly map from the API response to our enhanced content type

4. Updated the test component to display all the relevant fields from the API response

5. Fixed the tests to match the new data structure

These changes provide a more accurate representation of the actual API data while still maintaining compatibility with the existing `MarketingContent` interface structure.
</info added on 2025-05-08T06:11:43.417Z>

Enhanced the MarketingPrint provider with additional field mappings to improve the data representation:

1. Implemented dimensions field by combining width and height properties:
   - When both width and height are present, formats them as "widthxheight" (e.g., "1920×1080")
   - Returns an empty string when either property is missing
   - Added appropriate type definitions for these fields

2. Added support for output_type field by mapping it directly from the format property in the API response

3. Repurposed the design_count field to display resolution (DPI) information:
   - Extracts DPI value from the API response
   - Falls back to zero when the property doesn't exist

4. Created comprehensive test suite to verify these new mappings:
   - Tests for successful dimension formatting with valid width/height
   - Tests for empty dimension string when width or height is missing
   - Tests for output_type mapping from format property
   - Tests for DPI resolution mapping to design_count
   - Tests for proper fallback behavior with missing optional fields

5. Updated the transformer functions to handle these new mappings while maintaining backward compatibility

These enhancements provide more detailed and useful information about marketing and print designs without breaking existing functionality.
</info added on 2025-05-08T06:47:38.929Z>

## 4. Implement SWR hook for CCTV People [done]
### Dependencies: None
### Description: Create a custom SWR hook to fetch and manage CCTV People data, replacing the existing Algolia implementation.
### Details:
1. Create `useCctvPeople.js` hook
2. Configure API endpoint URL and parameters for CCTV people search
3. Implement data transformation to handle person records and images
4. Implement special handling for facial recognition parameters if applicable
5. Set up proper debouncing and caching strategy
6. Add comprehensive error handling for failed image loads
7. Implement integration with the parent component
8. Create unit tests for the hook's functionality
<info added on 2025-05-08T12:51:23.791Z>
1. Create `useCctvPeople.js` hook
2. Configure API endpoint URL and parameters for CCTV people search
3. Implement data transformation to handle person records and images
4. Implement special handling for facial recognition parameters if applicable
5. Set up proper debouncing and caching strategy
6. Add comprehensive error handling for failed image loads
7. Implement integration with the parent component
8. Create unit tests for the hook's functionality

Implementation details:
- Created a proper directory structure following established patterns:
  - Created `src/features/FederatedSearch/providers/CctvPeople/` with hooks, types, and transformers directories
- Implemented TypeScript interfaces for CCTV People data:
  - Created `CctvPersonHit` to represent raw API data with properties like personID, firstName, lastName, clubID, membershipStatus, avatar, shadowPhoto, facial recognition info
  - Created `CctvPeopleContent` for transformed data to be consumed by UI components
  - Added standard response interfaces for raw and normalized data
- Added CCTV_TYPE and CCTV_LABEL constants to the shared constants file
- Implemented data transformation functions:
  - Created transformers to convert API data into UI-friendly format
  - Added proper handling for avatar/shadow photo fallbacks
  - Extracted facial recognition data
  - Handled empty/missing fields with sensible defaults
- Created the useCctvPeople hook:
  - Implemented conditional fetching based on search term presence
  - Used existing API_ENDPOINTS.cctvPeople configuration
  - Added SWR configuration consistent with other hooks
  - Applied proper data transformation for component consumption
- Added comprehensive tests:
  - Tests for successful data transformation
  - Tests for conditional fetching behavior
  - Tests for pagination parameter handling
  - Error handling tests
- The implementation follows the same patterns established by other provider hooks, maintaining consistency throughout the codebase
</info added on 2025-05-08T12:51:23.791Z>
<info added on 2025-05-08T12:54:24.075Z>
After double-checking the implementation against the original requirements in react_search_migration_plan.md and algoliaSearch.js, I made the following adjustments:

1. Updated CCTV_PEOPLE_SEARCH_ENDPOINT to match the original implementation:
   - Changed the path from '/api/search/cctv-people' to '/api/cctv/people/:clubId'
   - Changed the method from POST to GET
   - Removed the headers configuration

2. Updated CategoryTypes.ts to include CCTV_TYPE in the CategoryType union type:
   - Added CCTV_TYPE to the import list
   - Added typeof CCTV_TYPE to the CategoryType union

3. Enhanced useCctvPeople hook to match the original implementation:
   - Added clubId parameter to match the selectedID in the original code
   - Added proper path parameter replacement for :clubId
   - Changed data handling to use query parameters in the URL instead of request body
   - Made fetching conditional on having both a non-empty search term and clubId

4. Updated CctvPeopleOptions interface to include clubId parameter

5. Updated tests to include clubId in all test cases

These changes ensure our implementation aligns with the behavior of the original algoliaSearch.js code while maintaining the modern React SWR hook architecture. The hook now properly replicates the API calls, parameter handling, and data transformation of the original implementation.
</info added on 2025-05-08T12:54:24.075Z>

## 5. Implement local storage utility for Recently Viewed Wiki Articles [done]
### Dependencies: None
### Description: Create a utility function to manage recently viewed wiki articles in localStorage
### Details:
1. Create a utility function similar to the original `trackViewedArticle` in algoliaSearch.js\n2. Implement `getRecentlyViewedArticles()` function to retrieve stored articles\n3. Ensure proper JSON parsing and error handling for localStorage access\n4. Keep track of the maximum number of articles to store (10)\n5. Handle the case when localStorage is not available\n6. Create unit tests for localStorage interaction\n7. Document the utility functions and their parameters

## 6. Create data transformation utilities for different content types [done]
### Dependencies: None
### Description: Implement utility functions for transforming data from each API source into the format required by UI components
### Details:
1. Analyze the original transformation logic in algoliaSearch.js for each content type\n2. Create separate utility functions for each data type:\n   - Wiki article formatting with HTML entity decoding and breadcrumb building\n   - Store product description truncation and HTML stripping\n   - Marketing & Print design tag highlighting based on search terms\n   - CCTV people status formatting and avatar handling\n3. Ensure highlighting functions properly handle matching terms in search results\n4. Implement helpers for building URLs for each content type\n5. Handle edge cases like missing images or incomplete data\n6. Write comprehensive unit tests for each transformation function\n7. Create documentation for each utility function

## 7. Implement URL navigation utility [done]
### Dependencies: None
### Description: Create a utility to handle URL building and navigation for different content types
### Details:
1. Analyze the existing URL handling in algoliaSearch.js, particularly in `onStateChange`\n2. Create a unified utility function to generate URLs for each content type:\n   - Wiki articles (`/wiki/#${encodedPath}`)\n   - Store products (`/store-ui/#%2Fstore%2F%3FsearchTerm%3D${encodedId}`)\n   - Marketing & Print designs (`/designer/designs?tag=${encodedTag}&designId=${encodedId}`)\n   - CCTV people (`/cctv-ui/#${encodedPath}`)\n3. Implement special handling for CCTV iframe communication\n4. Create a navigation function that handles URL opening based on content type\n5. Support both internal navigation and opening in new tabs\n6. Add error handling for malformed URLs\n7. Create unit tests for URL generation and navigation functionality

## 8. Create shared hooks for data fetching patterns [done]
### Dependencies: None
### Description: Implement common hooks and utilities for shared data fetching patterns used across all search sources
### Details:
1. Analyze common patterns in the data fetching logic from algoliaSearch.js\n2. Create a `useDebounce` hook for search query inputs\n3. Implement a `useFetch` utility that wraps SWR with consistent error handling\n4. Create a shared configuration for API endpoints and parameters\n5. Implement utilities for conditional fetching based on query presence\n6. Create helpers for managing loading states and transitions\n7. Add functionality for request cancellation if needed\n8. Document how these shared hooks should be used in the individual data source hooks\n9. Write unit tests for the shared hooks and utilities

## 9. Implement image lazy loading utility [done]
### Dependencies: None
### Description: Create a utility for lazy loading images in search results
### Details:
1. Analyze the existing MutationObserver-based lazy loading in algoliaSearch.js\n2. Create a modern React-based lazy loading solution for images in search results\n3. Implement a custom hook that manages image loading state\n4. Add fallback handling for image load failures\n5. Implement progressive loading with placeholder/skeleton states\n6. Ensure compatibility with the original implementation's features\n7. Support different image types from various content sources (avatars, thumbnails, etc.)\n8. Create unit tests for the lazy loading functionality\n9. Document the hook's usage with different image sources

## 10. Create conditional rendering utility for search results [done]
### Dependencies: None
### Description: Implement utility functions to handle conditional rendering of search results based on query state
### Details:
1. Analyze the existing conditional display logic in algoliaSearch.js\n2. Create utility functions that determine when each source should be displayed:\n   - Functions (shown only with non-empty query)\n   - Recently Viewed Wiki Articles (shown only with empty query)\n   - Topics (shown only with non-empty query)\n   - Wiki Articles (different behavior for empty vs. non-empty query)\n   - Store Products (shown only with non-empty query)\n   - Marketing & Print (shown only with non-empty query)\n   - CCTV People (shown only with non-empty query)\n3. Implement a unified display management system for all sources\n4. Create helpers for controlling the order of displayed sections\n5. Add functionality for \"Show more results\" links based on total hits\n6. Implement empty state handling (no results, loading states)\n7. Write unit tests for the conditional rendering logic\n8. Document the utility's usage patterns
<info added on 2025-05-08T05:16:23.990Z>
1. Analyze the existing conditional display logic in algoliaSearch.js
2. Create utility functions that determine when each source should be displayed:
   - Functions (shown only with non-empty query)
   - Recently Viewed Wiki Articles (shown only with empty query)
   - Topics (shown only with non-empty query)
   - Wiki Articles (different behavior for empty vs. non-empty query)
   - Store Products (shown only with non-empty query)
   - Marketing & Print (shown only with non-empty query)
   - CCTV People (shown only with non-empty query)
3. Create helpers for controlling the order of displayed sections
4. Add functionality for "Show more results" links based on total hits
5. Implement empty state handling (no results, loading states)
6. Write unit tests for the conditional rendering logic
7. Document the utility's usage patterns
</info added on 2025-05-08T05:16:23.990Z>

## 11. Implement integration tests for all data sources [done]
### Dependencies: None
### Description: Create comprehensive integration tests for all data source hooks and their interactions
### Details:
1. Set up testing environment with mock API responses for all sources\n2. Create fixtures that simulate the response data from each API endpoint\n3. Implement integration tests for each data source hook:\n   - Test data fetching with empty and non-empty queries\n   - Verify proper data transformation\n   - Test conditional fetching behavior\n   - Verify error handling\n4. Test integration between hooks and parent components\n5. Test handling of loading states and transitions\n6. Verify URL generation and navigation behavior\n7. Test image loading and fallback behavior\n8. Create documentation for the test suite\n9. Set up CI integration for running tests

## 12. Implement Performance Hub Features (functions) search utility [done]
### Dependencies: None
### Description: Create a utility function to search Performance Hub features/routes based on user input
### Details:
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters
<info added on 2025-05-14T11:54:22.966Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters

Based on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:

1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts
2. Define TypeScript interfaces for:
   - PageRoute (representing items in availablePageRoutes)
   - SearchResult (representing the formatted search results)
3. Implement the core search function that:
   - Takes a query string and the availablePageRoutes array as parameters
   - Normalizes text by replacing '&' with 'and' in both query and titles
   - Filters routes where the normalized title includes the normalized query
   - Calculates relevance score based on the position of match in the title
   - Sorts results by relevance score (lower index = higher relevance)
   - Limits to top 3 most relevant matches
   - Returns empty array for empty/undefined queries
4. Ensure the implementation has no dependencies on jQuery or lodash
5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)
6. Write unit tests covering all edge cases and functionality
7. Document the API with JSDoc comments for better developer experience
</info added on 2025-05-14T11:54:22.966Z>
<info added on 2025-05-14T11:59:00.939Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters
<info added on 2025-05-14T11:54:22.966Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters

Based on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:

1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts
2. Define TypeScript interfaces for:
   - PageRoute (representing items in availablePageRoutes)
   - SearchResult (representing the formatted search results)
3. Implement the core search function that:
   - Takes a query string and the availablePageRoutes array as parameters
   - Normalizes text by replacing '&' with 'and' in both query and titles
   - Filters routes where the normalized title includes the normalized query
   - Calculates relevance score based on the position of match in the title
   - Sorts results by relevance score (lower index = higher relevance)
   - Limits to top 3 most relevant matches
   - Returns empty array for empty/undefined queries
4. Ensure the implementation has no dependencies on jQuery or lodash
5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)
6. Write unit tests covering all edge cases and functionality
7. Document the API with JSDoc comments for better developer experience
</info added on 2025-05-14T11:54:22.966Z>

<info added on 2025-05-15T09:32:45.123Z>
Implementation completed successfully with the following structure and details:

1. Directory Structure:
   - Created `src/features/FederatedSearch/types/HubFeatureTypes.ts` for type definitions
   - Implemented main utility in `src/features/FederatedSearch/lib/hubFeaturesSearch.ts`
   - Added comprehensive test suite in `src/features/FederatedSearch/lib/__tests__/hubFeaturesSearch.test.ts`
   - Created mock data in `src/features/FederatedSearch/mockData/hubFeatures.ts`
   - Updated relevant index files to export the new functionality

2. Implementation Details:
   - Developed `searchHubFeatures` utility function that accepts a query string and routes array
   - Implemented text normalization by replacing '&' with 'and' in both titles and query strings
   - Created relevance-based sorting algorithm that prioritizes matches appearing earlier in titles
   - Added configurable limit parameter (defaulting to 3 results)
   - Ensured proper handling of empty/null/undefined queries (returns empty array)
   - Implemented term-by-term matching approach for better handling of partial matches in multi-word queries
   - Eliminated dependencies on jQuery and lodash while maintaining equivalent functionality

3. Testing:
   - Wrote 10 comprehensive test cases covering all requirements
   - Tested text normalization functionality
   - Verified correct sorting by relevance
   - Confirmed proper limiting of results
   - Validated handling of edge cases
   - Ensured empty queries return empty results
   - All tests are now passing

The implementation successfully replaces the Algolia-based search for Performance Hub features while maintaining the same functionality and eliminating external dependencies.
</info added on 2025-05-15T09:32:45.123Z>
</info added on 2025-05-14T11:59:00.939Z>
<info added on 2025-05-14T12:35:44.109Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters
<info added on 2025-05-14T11:54:22.966Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters

Based on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:

1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts
2. Define TypeScript interfaces for:
   - PageRoute (representing items in availablePageRoutes)
   - SearchResult (representing the formatted search results)
3. Implement the core search function that:
   - Takes a query string and the availablePageRoutes array as parameters
   - Normalizes text by replacing '&' with 'and' in both query and titles
   - Filters routes where the normalized title includes the normalized query
   - Calculates relevance score based on the position of match in the title
   - Sorts results by relevance score (lower index = higher relevance)
   - Limits to top 3 most relevant matches
   - Returns empty array for empty/undefined queries
4. Ensure the implementation has no dependencies on jQuery or lodash
5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)
6. Write unit tests covering all edge cases and functionality
7. Document the API with JSDoc comments for better developer experience
</info added on 2025-05-14T11:54:22.966Z>
<info added on 2025-05-14T11:59:00.939Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters
<info added on 2025-05-14T11:54:22.966Z>
1. Create a utility function that searches through the Performance Hub features (availablePageRoutes array)
2. Implement the filtering and sorting logic from algoliaSearch.js's 'functions' source:
   - Filter routes based on title matches with the query
   - Replace '&' with 'and' in both title and query for better matching
   - Sort results by relevance (how early the match appears in the title)
   - Limit results to the top 3 most relevant matches
3. Create helper functions to format the search results for display
4. Implement URL building for navigation to the matched features
5. Ensure results are only returned for non-empty queries
6. Write comprehensive unit tests for:
   - Correct filtering and matching behavior
   - Proper sorting by relevance
   - Correct limiting to top 3 results
   - Empty query handling (should return empty array)
7. Create types for Performance Hub features data structure
8. Document the utility function's usage and parameters

Based on codebase analysis, we need to create a standalone utility to replace the existing Algolia implementation for Performance Hub features search. The implementation should:

1. Create a new file at src/features/FederatedSearch/lib/hubFeaturesSearch.ts
2. Define TypeScript interfaces for:
   - PageRoute (representing items in availablePageRoutes)
   - SearchResult (representing the formatted search results)
3. Implement the core search function that:
   - Takes a query string and the availablePageRoutes array as parameters
   - Normalizes text by replacing '&' with 'and' in both query and titles
   - Filters routes where the normalized title includes the normalized query
   - Calculates relevance score based on the position of match in the title
   - Sorts results by relevance score (lower index = higher relevance)
   - Limits to top 3 most relevant matches
   - Returns empty array for empty/undefined queries
4. Ensure the implementation has no dependencies on jQuery or lodash
5. Follow the FederatedSearch feature organization pattern (ui/, transformers/, types/, providers/, lib/)
6. Write unit tests covering all edge cases and functionality
7. Document the API with JSDoc comments for better developer experience
</info added on 2025-05-14T11:54:22.966Z>

<info added on 2025-05-15T09:32:45.123Z>
Implementation completed successfully with the following structure and details:

1. Directory Structure:
   - Created `src/features/FederatedSearch/types/HubFeatureTypes.ts` for type definitions
   - Implemented main utility in `src/features/FederatedSearch/lib/hubFeaturesSearch.ts`
   - Added comprehensive test suite in `src/features/FederatedSearch/lib/__tests__/hubFeaturesSearch.test.ts`
   - Created mock data in `src/features/FederatedSearch/mockData/hubFeatures.ts`
   - Updated relevant index files to export the new functionality

2. Implementation Details:
   - Developed `searchHubFeatures` utility function that accepts a query string and routes array
   - Implemented text normalization by replacing '&' with 'and' in both titles and query strings
   - Created relevance-based sorting algorithm that prioritizes matches appearing earlier in titles
   - Added configurable limit parameter (defaulting to 3 results)
   - Ensured proper handling of empty/null/undefined queries (returns empty array)
   - Implemented term-by-term matching approach for better handling of partial matches in multi-word queries
   - Eliminated dependencies on jQuery and lodash while maintaining equivalent functionality

3. Testing:
   - Wrote 10 comprehensive test cases covering all requirements
   - Tested text normalization functionality
   - Verified correct sorting by relevance
   - Confirmed proper limiting of results
   - Validated handling of edge cases
   - Ensured empty queries return empty results
   - All tests are now passing

The implementation successfully replaces the Algolia-based search for Performance Hub features while maintaining the same functionality and eliminating external dependencies.
</info added on 2025-05-15T09:32:45.123Z>
</info added on 2025-05-14T11:59:00.939Z>

<info added on 2025-05-16T14:22:18.000Z>
Successfully completed the implementation and integration of Hub Features search functionality with the following structure and improvements:

1. Created a new provider structure in the FederatedSearch feature:
   - Created types in `/providers/HubFeatures/types/HubFeatureTypes.ts` with proper interfaces for PageRoute and SearchResult
   - Implemented search utility in `/providers/HubFeatures/lib/hubFeaturesSearch.ts` with the core search algorithm
   - Created custom hook in `/providers/HubFeatures/hooks/useHubFeaturesSearch.ts` to handle data fetching and state management
   - Added formatting function in `/providers/HubFeatures/format/index.ts` to transform search results into the required display format
   - Added mock data for testing in `/providers/HubFeatures/mockData/hubFeatures.ts`

2. Added new constants to support Hub features:
   - Added `HUB_TYPE` and `HUB_LABEL` to shared constants for consistent type identification
   - Updated column configurations to support the new type in the results display

3. Integrated the search into ResultList.tsx:
   - Added the hook to fetch Hub Features data alongside other data sources
   - Applied the formatter to prepare data for display in the unified results list
   - Added results to the dataSources array with proper typing
   - Ensured proper error handling and type safety throughout the integration

4. Fixed all TypeScript and linting errors to ensure code quality and maintainability.

The implementation successfully passed building and is now fully integrated into the search results page, following the existing architectural patterns. This completes the transition from Algolia-based search to our custom implementation for Performance Hub features.
</info added on 2025-05-16T14:22:18.000Z>
</info added on 2025-05-14T12:35:44.109Z>

## 13. Implement Wiki Topics & Categories Search Integration [done]
### Dependencies: None
### Description: Add a new search source for Wiki Topics & Categories: fetch and transform topic/subtopic data from `/openkb/api/topics` and integrate it into the GlobalSearchController as per `react_search_migration_plan.md`, following patterns in existing providers.
### Details:
Before implementation, confirm the API response shape for `/openkb/api/topics` (including `topic.svg` and `subtopics` array). Then create a custom hook (`useTopicsSearch`) in `src/features/FederatedSearch/providers/TopicsSearch` that:
- Fetches data and handles loading/error states
- Transforms each topic into `{ topic, subtopic, icon (base64 SVG), label, url }`
- Filters and matches based on the search query

Integrate this hook into the GlobalSearchController under the `topics` source. Follow file structure, naming conventions, and error/loading patterns used by other providers (e.g., StoreProducts, CctvPeople).

