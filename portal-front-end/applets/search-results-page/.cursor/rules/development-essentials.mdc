---
description: 
globs: 
alwaysApply: false
---
# Development Essentials

## **Core Principles**
- **Investigate before acting**: Use tools to read files and understand codebase structure - never guess
- **Plan then implement**: Think step-by-step, develop detailed plans, make incremental changes
- **Complete solutions**: No TODOs, placeholders, or missing pieces - deliver fully functional code
- **Persist until resolved**: Continue until the user's query is completely solved

## **Code Quality Standards**
- Use early returns for readability
- Descriptive names: `handleClick`, `handleKeyDown` for event handlers
- Prefer `const` over `function`: `const toggle = () => {}`
- Include all necessary imports and proper TypeScript types
- Follow DRY principles and best practices

## **React/TypeScript Specifics**
- **Styling**: Always use Tailwind classes, avoid inline CSS
- **Accessibility**: Include `tabindex`, `aria-label`, keyboard handlers
- **Testing**: Co-locate tests with components (`Component.test.tsx`)
- **Data Fetching**: Use SWR patterns with custom hooks

## **Feature-Sliced Design Architecture**

### **Directory Structure**
```
/src
  /pages          # Route components
  /features       # Business logic (auth, search, etc.)
    /[feature]
      /ui         # Components
      /model      # State management  
      /api        # Data fetching
      /hooks      # Feature hooks
  /entities       # Domain models, types
  /shared         # Cross-feature utilities
    /ui           # Reusable components
    /api          # API utilities
    /hooks        # Common hooks
```

### **Import Rules**
- **Strict layer hierarchy**: Only import from same or lower layers
- **No cross-feature imports**: Features cannot import from other features
- **Use path aliases**: `@/features/auth`, `@/shared/ui`

### **File Organization**
- Feature-specific code stays within feature boundaries
- Tests co-located with implementation
- Use barrel files (`index.ts`) for clean public APIs
- Avoid circular dependencies in barrel exports

## **Project Context**
- React + Vite + TypeScript + Tailwind CSS
- Vitest + React Testing Library (happy-dom environment)
- SWR for data fetching
- Translation via `useTranslation` hook
- Canvas mocking available in test setup
