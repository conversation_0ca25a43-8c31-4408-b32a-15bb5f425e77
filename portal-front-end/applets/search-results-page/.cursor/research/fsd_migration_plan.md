# Feature-Sliced Design Migration Plan

This document tracks our progress converting the React project to follow the Feature-Sliced Design (FSD) methodology, adapted for React conventions.

## 1. Audit Current Structure

- Scan `/src` and list existing top‑level folders and files.
- Identify legacy `widgets/`, utilities, feature folders, and any unorganized code.

## 2. Define FSD Layers (React Mapping)

| Layer      | Path                  | Description                                             |
| ---------- | --------------------- | ------------------------------------------------------- |
| App        | `/src/App.tsx`        | Application entrypoint, providers, layout wrappers      |
| Pages      | `/src/pages/...`      | Route-based page components                             |
| Components | `/src/components/...` | Reusable UI components (formerly `widgets/`)            |
| Features   | `/src/features/...`   | Business features with domains and segments             |
| Entities   | `/src/entities/...`   | Core domain models, types, and interfaces               |
| Shared     | `/src/shared/...`     | Utilities, hooks, constants, API clients, design tokens |

## 3. Rename / Move "widgets" → `components`

- For each folder under `src/widgets/*`:
  1. Move to `src/components/<Name>`
  2. Update imports: `@/widgets/X` → `@/components/X`

## 4. Consolidate "Pages"

- Ensure all route components live under `src/pages/`.
- Move any orphaned pages into their own subfolders: `src/pages/<route>/index.tsx`.

## 5. Establish "Entities"

- Create `src/entities/` for domain models, TS types, interfaces.
- Migrate files like `User.ts`, DTOs here and update imports to `@/entities/...`.

## 6. Carve Out "Shared"

- Create `src/shared/` and subfolders:
  - `api/` for HTTP clients and adapters
  - `hooks/` for shared custom hooks
  - `utils/` for general utilities
  - `styles/` or `tokens/` for design constants
- Move relevant code and fix imports to `@/shared/...`.

## 7. Restructure "Features"

For each `src/features/<feature>/`:

1. Create segments:
   - `ui/` (presentational components)
   - `model/` (state management, slices)
   - `api/` (data-fetching layers)
   - `hooks/` (feature-specific hooks)
   - `types/` (feature TS types)
2. Relocate code into appropriate segments.
3. Add `index.ts` barrel exporting:
   ```ts
   export * from './ui';
   export * from './model';
   export * from './api';
   export * from './hooks';
   export * from './types';
   ```

## 8. Update Path Aliases & `tsconfig.json`

```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/features/*": ["src/features/*"],
      "@/entities/*": ["src/entities/*"],
      "@/shared/*": ["src/shared/*"],
      "@/pages/*": ["src/pages/*"]
    }
  }
}
```

## 9. Enforce Import Boundaries

- Install/configure `eslint-plugin-boundaries`.
- Define layer rules so imports only go from upper to same or lower layers.

## 10. Clean Up & Validate

- Remove deprecated folders (`widgets/`, old utils).
- Run `npm run lint && npm run build && npm run test`.
- Fix broken imports, adjust barrels, correct alias paths.

## 11. Document & Train

- Update README with new structure diagram.
- Share this FSD plan for team alignment.

---

**🚧 In Progress**
