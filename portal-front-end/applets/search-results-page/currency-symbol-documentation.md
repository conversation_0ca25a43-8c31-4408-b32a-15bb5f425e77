# Currency Symbol Handling in ExpressCart

## Overview

The ExpressCart application uses multiple methods to handle currency symbols across the codebase. This documentation explains how the application determines what symbol to use for a given currency code, where it gets these symbols from, and how to implement similar functionality in your React application.

## Methods for Determining Currency Symbols

The application uses two primary approaches to determine currency symbols:

### 1. Using the `currency-symbol-map` NPM Package

The primary method used in the backend is the `currency-symbol-map` npm package, which provides a simple function to look up currency symbols for given currency codes.

**Implementation:**

```javascript
// Imported in src/lib/common.js
const symbolMap = require('currency-symbol-map');

const getSymbolFromCurrency = (currency = 'aud') => {
    let symbol = '$';  // Default symbol
    try {
        symbol = symbolMap(currency.toUpperCase());
    } catch (e) {}
    return symbol;
}

// Additional validation function
function isValidCurrencyCode(code) {
    try {
        const hasSymbol = symbolMap(code.toUpperCase());
        return !!hasSymbol;
    } catch (e) {
        return false;
    }
}
```

This function is exported and used throughout the application to convert currency codes to their respective symbols.

### 2. Using JavaScript's Native `toLocaleString` Method

In the frontend JavaScript code, the application uses the browser's built-in `toLocaleString` method with formatting options to get currency symbols:

```javascript
// Located in src/frontend/js/common/common.js
function getCurrencySymbol(currency, locale = 'en-US') {
    try {
        return (0)
            .toLocaleString(locale, {
                style: 'currency',
                currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
            })
            .replace(/\d/g, '')
            .replace(/^.+(.)$/, '$1')
            .trim();
    } catch (error) {
        console.error(error);
        return '$';
    }
}
```

This function uses JavaScript's native internationalization API to format a number (0) as a currency, then extracts just the currency symbol part by removing any digits and trimming the result.

## Number Formatting with Currency Symbols

The application extends the `Number` prototype to add a `formatCurrency` method for formatting numbers with currency symbols:

```javascript
// Located in src/frontend/js/common/common.js
Number.prototype.formatCurrency = function (currency, n) {
    const zeroDecimalCurrencies = [
        'BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA',
        'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF',
    ];

    const symbol = !currency ? '' : getCurrencySymbol(currency);
    const decimals =
        n ?? (zeroDecimalCurrencies.includes(currency?.toUpperCase()) ? 0 : 2);

    return (
        symbol +
        numeral(this).format(
            '0,0' + (decimals > 0 ? '.' : '') + '0'.repeat(decimals)
        )
    );
};
```

This method handles:
1. Using the appropriate currency symbol for the given currency code
2. Determining the correct number of decimal places (some currencies don't use decimals)
3. Formatting the number with the correct thousands separators and decimal places

## Special Cases Handling

### Handling Special Currency Positioning

In some cases, currency symbols need to be positioned differently. The application has special logic for this in certain places:

```javascript
// Example from src/lib/createInvoice.js
function formatCurrency(cents, symbol = '$') {
    const decimals = symbol === '¥' ? 0 : 2;
    const amount = numeral(cents / 100).format(`0,0.${'0'.repeat(decimals)}`);

    // AED needs its symbol after the amount, the reverse causes formatting issues
    if (symbol === 'د.إ') {
        return `${amount} ${symbol}`;
    }

    return `${symbol}${amount}`;
}
```

### Zero-Decimal Currencies

The application maintains a list of zero-decimal currencies (currencies that don't use cents/decimal places):

```javascript
const zeroDecimalCurrencies = [
    'BIF', // Burundian Franc
    'CLP', // Chilean Peso
    'DJF', // Djiboutian Franc
    'GNF', // Guinean Franc
    'JPY', // Japanese Yen
    'KMF', // Comorian Franc
    'KRW', // South Korean Won
    'MGA', // Malagasy Ariary
    'PYG', // Paraguayan Guarani
    'RWF', // Rwandan Franc
    'UGX', // Ugandan Shilling
    'VND', // Vietnamese Dong
    'VUV', // Vanuatu Vatu
    'XAF', // Central African CFA Franc
    'XOF', // West African CFA Franc
    'XPF', // CFP Franc
];
```

## Usage in Templates

The currency symbol is used in the application's templates (using Handlebars) through helper functions:

```handlebars
<!-- Example from src/views/partials/cartSummary.hbs -->
<span>
    {{currencySymbol this.currency}}{{formatAmount this.value}}
</span>
```

The `currencySymbol` helper function is registered in the application setup:

```javascript
// In src/app.js
// ... 
currencySymbol: getSymbolFromCurrency,
// ...
```

## Implementation in React

To implement similar functionality in a React application, you have two main options:

### Option 1: Using currency-symbol-map Package

```bash
npm install currency-symbol-map
```

```javascript
// currencyUtils.js
import getSymbolFromCurrency from 'currency-symbol-map';

export const getCurrencySymbol = (currencyCode = 'USD') => {
  try {
    const symbol = getSymbolFromCurrency(currencyCode.toUpperCase());
    return symbol || '$'; // Fallback to $ if no symbol found
  } catch (e) {
    console.error('Error getting currency symbol:', e);
    return '$';
  }
};

export const formatCurrency = (amount, currencyCode = 'USD', decimals) => {
  const zeroDecimalCurrencies = [
    'BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA',
    'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF',
  ];
  
  const symbol = getCurrencySymbol(currencyCode);
  const useDecimals = decimals !== undefined ? 
    decimals : 
    (zeroDecimalCurrencies.includes(currencyCode.toUpperCase()) ? 0 : 2);
  
  // Use a formatting library like Numeral.js or Intl API
  const formattedNumber = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: useDecimals,
    maximumFractionDigits: useDecimals,
  }).format(amount);
  
  // Handle special cases like AED
  if (symbol === 'د.إ') {
    return `${formattedNumber} ${symbol}`;
  }
  
  return `${symbol}${formattedNumber}`;
};
```

### Option 2: Using JavaScript's Native Intl API

```javascript
// currencyUtils.js
export const getCurrencySymbol = (currencyCode = 'USD', locale = 'en-US') => {
  try {
    return (0)
      .toLocaleString(locale, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      })
      .replace(/\d/g, '')
      .replace(/^.+(.)$/, '$1')
      .trim();
  } catch (error) {
    console.error('Error getting currency symbol:', error);
    return '$';
  }
};

export const formatCurrency = (amount, currencyCode = 'USD', options = {}) => {
  const zeroDecimalCurrencies = [
    'BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA',
    'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF',
  ];
  
  const decimals = options.decimals !== undefined ? 
    options.decimals : 
    (zeroDecimalCurrencies.includes(currencyCode.toUpperCase()) ? 0 : 2);
  
  return amount.toLocaleString(options.locale || 'en-US', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};
```

### React Component Example

```jsx
// CurrencyDisplay.jsx
import React from 'react';
import { formatCurrency } from './currencyUtils';

const CurrencyDisplay = ({ amount, currency = 'USD', decimals }) => {
  const formattedAmount = formatCurrency(amount, currency, { decimals });
  
  return <span className="currency-amount">{formattedAmount}</span>;
};

export default CurrencyDisplay;

// Usage
// <CurrencyDisplay amount={1234.56} currency="EUR" />
// Outputs: €1,234.56
```

## Considerations for React Implementation

1. **Performance**: If your application needs to format many currency values, consider using a memoized version of the format function or a context provider for currency settings.

2. **Internationalization**: If your application needs to support multiple locales, pass the locale to the formatting function.

3. **Edge Cases**: 
   - Some currencies may need special positioning of the symbol
   - Ensure proper handling of zero-decimal currencies
   - Add proper error handling for invalid currency codes

4. **Accessibility**: When displaying currency values, ensure proper semantics are used for screen readers and other assistive technologies.

## Conclusion

The ExpressCart codebase uses a combination of the `currency-symbol-map` package and JavaScript's native `toLocaleString` method to handle currency symbols. This dual approach provides flexibility and fallback options for currency formatting throughout the application.

For implementing similar functionality in React, you can choose either approach based on your needs - using the native Intl API is generally simpler and doesn't require external dependencies, while the currency-symbol-map package might provide more consistent results across different environments. 