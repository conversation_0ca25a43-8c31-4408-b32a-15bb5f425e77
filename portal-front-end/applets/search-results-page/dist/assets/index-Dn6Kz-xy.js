function ab(t,a){for(var i=0;i<a.length;i++){const o=a[i];if(typeof o!="string"&&!Array.isArray(o)){for(const u in o)if(u!=="default"&&!(u in t)){const c=Object.getOwnPropertyDescriptor(o,u);c&&Object.defineProperty(t,u,c.get?c:{enumerable:!0,get:()=>o[u]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))o(u);new MutationObserver(u=>{for(const c of u)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function i(u){const c={};return u.integrity&&(c.integrity=u.integrity),u.referrerPolicy&&(c.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?c.credentials="include":u.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function o(u){if(u.ep)return;u.ep=!0;const c=i(u);fetch(u.href,c)}})();function Zf(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Uc={exports:{}},ti={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sp;function rb(){if(Sp)return ti;Sp=1;var t=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function i(o,u,c){var d=null;if(c!==void 0&&(d=""+c),u.key!==void 0&&(d=""+u.key),"key"in u){c={};for(var g in u)g!=="key"&&(c[g]=u[g])}else c=u;return u=c.ref,{$$typeof:t,type:o,key:d,ref:u!==void 0?u:null,props:c}}return ti.Fragment=a,ti.jsx=i,ti.jsxs=i,ti}var bp;function ib(){return bp||(bp=1,Uc.exports=rb()),Uc.exports}var E=ib(),jc={exports:{}},Ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xp;function ob(){if(xp)return Ce;xp=1;var t=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),b=Symbol.iterator;function S(M){return M===null||typeof M!="object"?null:(M=b&&M[b]||M["@@iterator"],typeof M=="function"?M:null)}var x={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,w={};function A(M,$,Q){this.props=M,this.context=$,this.refs=w,this.updater=Q||x}A.prototype.isReactComponent={},A.prototype.setState=function(M,$){if(typeof M!="object"&&typeof M!="function"&&M!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,M,$,"setState")},A.prototype.forceUpdate=function(M){this.updater.enqueueForceUpdate(this,M,"forceUpdate")};function _(){}_.prototype=A.prototype;function D(M,$,Q){this.props=M,this.context=$,this.refs=w,this.updater=Q||x}var z=D.prototype=new _;z.constructor=D,R(z,A.prototype),z.isPureReactComponent=!0;var k=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},Z=Object.prototype.hasOwnProperty;function I(M,$,Q,K,J,pe){return Q=pe.ref,{$$typeof:t,type:M,key:$,ref:Q!==void 0?Q:null,props:pe}}function q(M,$){return I(M.type,$,void 0,void 0,void 0,M.props)}function ae(M){return typeof M=="object"&&M!==null&&M.$$typeof===t}function ce(M){var $={"=":"=0",":":"=2"};return"$"+M.replace(/[=:]/g,function(Q){return $[Q]})}var me=/\/+/g;function oe(M,$){return typeof M=="object"&&M!==null&&M.key!=null?ce(""+M.key):$.toString(36)}function se(){}function fe(M){switch(M.status){case"fulfilled":return M.value;case"rejected":throw M.reason;default:switch(typeof M.status=="string"?M.then(se,se):(M.status="pending",M.then(function($){M.status==="pending"&&(M.status="fulfilled",M.value=$)},function($){M.status==="pending"&&(M.status="rejected",M.reason=$)})),M.status){case"fulfilled":return M.value;case"rejected":throw M.reason}}throw M}function de(M,$,Q,K,J){var pe=typeof M;(pe==="undefined"||pe==="boolean")&&(M=null);var re=!1;if(M===null)re=!0;else switch(pe){case"bigint":case"string":case"number":re=!0;break;case"object":switch(M.$$typeof){case t:case a:re=!0;break;case v:return re=M._init,de(re(M._payload),$,Q,K,J)}}if(re)return J=J(M),re=K===""?"."+oe(M,0):K,k(J)?(Q="",re!=null&&(Q=re.replace(me,"$&/")+"/"),de(J,$,Q,"",function(ze){return ze})):J!=null&&(ae(J)&&(J=q(J,Q+(J.key==null||M&&M.key===J.key?"":(""+J.key).replace(me,"$&/")+"/")+re)),$.push(J)),1;re=0;var W=K===""?".":K+":";if(k(M))for(var ue=0;ue<M.length;ue++)K=M[ue],pe=W+oe(K,ue),re+=de(K,$,Q,pe,J);else if(ue=S(M),typeof ue=="function")for(M=ue.call(M),ue=0;!(K=M.next()).done;)K=K.value,pe=W+oe(K,ue++),re+=de(K,$,Q,pe,J);else if(pe==="object"){if(typeof M.then=="function")return de(fe(M),$,Q,K,J);throw $=String(M),Error("Objects are not valid as a React child (found: "+($==="[object Object]"?"object with keys {"+Object.keys(M).join(", ")+"}":$)+"). If you meant to render a collection of children, use an array instead.")}return re}function H(M,$,Q){if(M==null)return M;var K=[],J=0;return de(M,K,"","",function(pe){return $.call(Q,pe,J++)}),K}function B(M){if(M._status===-1){var $=M._result;$=$(),$.then(function(Q){(M._status===0||M._status===-1)&&(M._status=1,M._result=Q)},function(Q){(M._status===0||M._status===-1)&&(M._status=2,M._result=Q)}),M._status===-1&&(M._status=0,M._result=$)}if(M._status===1)return M._result.default;throw M._result}var j=typeof reportError=="function"?reportError:function(M){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var $=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof M=="object"&&M!==null&&typeof M.message=="string"?String(M.message):String(M),error:M});if(!window.dispatchEvent($))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",M);return}console.error(M)};function ee(){}return Ce.Children={map:H,forEach:function(M,$,Q){H(M,function(){$.apply(this,arguments)},Q)},count:function(M){var $=0;return H(M,function(){$++}),$},toArray:function(M){return H(M,function($){return $})||[]},only:function(M){if(!ae(M))throw Error("React.Children.only expected to receive a single React element child.");return M}},Ce.Component=A,Ce.Fragment=i,Ce.Profiler=u,Ce.PureComponent=D,Ce.StrictMode=o,Ce.Suspense=m,Ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,Ce.__COMPILER_RUNTIME={__proto__:null,c:function(M){return P.H.useMemoCache(M)}},Ce.cache=function(M){return function(){return M.apply(null,arguments)}},Ce.cloneElement=function(M,$,Q){if(M==null)throw Error("The argument must be a React element, but you passed "+M+".");var K=R({},M.props),J=M.key,pe=void 0;if($!=null)for(re in $.ref!==void 0&&(pe=void 0),$.key!==void 0&&(J=""+$.key),$)!Z.call($,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&$.ref===void 0||(K[re]=$[re]);var re=arguments.length-2;if(re===1)K.children=Q;else if(1<re){for(var W=Array(re),ue=0;ue<re;ue++)W[ue]=arguments[ue+2];K.children=W}return I(M.type,J,void 0,void 0,pe,K)},Ce.createContext=function(M){return M={$$typeof:d,_currentValue:M,_currentValue2:M,_threadCount:0,Provider:null,Consumer:null},M.Provider=M,M.Consumer={$$typeof:c,_context:M},M},Ce.createElement=function(M,$,Q){var K,J={},pe=null;if($!=null)for(K in $.key!==void 0&&(pe=""+$.key),$)Z.call($,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(J[K]=$[K]);var re=arguments.length-2;if(re===1)J.children=Q;else if(1<re){for(var W=Array(re),ue=0;ue<re;ue++)W[ue]=arguments[ue+2];J.children=W}if(M&&M.defaultProps)for(K in re=M.defaultProps,re)J[K]===void 0&&(J[K]=re[K]);return I(M,pe,void 0,void 0,null,J)},Ce.createRef=function(){return{current:null}},Ce.forwardRef=function(M){return{$$typeof:g,render:M}},Ce.isValidElement=ae,Ce.lazy=function(M){return{$$typeof:v,_payload:{_status:-1,_result:M},_init:B}},Ce.memo=function(M,$){return{$$typeof:h,type:M,compare:$===void 0?null:$}},Ce.startTransition=function(M){var $=P.T,Q={};P.T=Q;try{var K=M(),J=P.S;J!==null&&J(Q,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(ee,j)}catch(pe){j(pe)}finally{P.T=$}},Ce.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},Ce.use=function(M){return P.H.use(M)},Ce.useActionState=function(M,$,Q){return P.H.useActionState(M,$,Q)},Ce.useCallback=function(M,$){return P.H.useCallback(M,$)},Ce.useContext=function(M){return P.H.useContext(M)},Ce.useDebugValue=function(){},Ce.useDeferredValue=function(M,$){return P.H.useDeferredValue(M,$)},Ce.useEffect=function(M,$,Q){var K=P.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return K.useEffect(M,$)},Ce.useId=function(){return P.H.useId()},Ce.useImperativeHandle=function(M,$,Q){return P.H.useImperativeHandle(M,$,Q)},Ce.useInsertionEffect=function(M,$){return P.H.useInsertionEffect(M,$)},Ce.useLayoutEffect=function(M,$){return P.H.useLayoutEffect(M,$)},Ce.useMemo=function(M,$){return P.H.useMemo(M,$)},Ce.useOptimistic=function(M,$){return P.H.useOptimistic(M,$)},Ce.useReducer=function(M,$,Q){return P.H.useReducer(M,$,Q)},Ce.useRef=function(M){return P.H.useRef(M)},Ce.useState=function(M){return P.H.useState(M)},Ce.useSyncExternalStore=function(M,$,Q){return P.H.useSyncExternalStore(M,$,Q)},Ce.useTransition=function(){return P.H.useTransition()},Ce.version="19.1.0",Ce}var wp;function cs(){return wp||(wp=1,jc.exports=ob()),jc.exports}var C=cs();const We=Zf(C),Hv=ab({__proto__:null,default:We},[C]);var Vc={exports:{}},ni={},Bc={exports:{}},Gc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cp;function sb(){return Cp||(Cp=1,function(t){function a(H,B){var j=H.length;H.push(B);e:for(;0<j;){var ee=j-1>>>1,M=H[ee];if(0<u(M,B))H[ee]=B,H[j]=M,j=ee;else break e}}function i(H){return H.length===0?null:H[0]}function o(H){if(H.length===0)return null;var B=H[0],j=H.pop();if(j!==B){H[0]=j;e:for(var ee=0,M=H.length,$=M>>>1;ee<$;){var Q=2*(ee+1)-1,K=H[Q],J=Q+1,pe=H[J];if(0>u(K,j))J<M&&0>u(pe,K)?(H[ee]=pe,H[J]=j,ee=J):(H[ee]=K,H[Q]=j,ee=Q);else if(J<M&&0>u(pe,j))H[ee]=pe,H[J]=j,ee=J;else break e}}return B}function u(H,B){var j=H.sortIndex-B.sortIndex;return j!==0?j:H.id-B.id}if(t.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;t.unstable_now=function(){return c.now()}}else{var d=Date,g=d.now();t.unstable_now=function(){return d.now()-g}}var m=[],h=[],v=1,b=null,S=3,x=!1,R=!1,w=!1,A=!1,_=typeof setTimeout=="function"?setTimeout:null,D=typeof clearTimeout=="function"?clearTimeout:null,z=typeof setImmediate<"u"?setImmediate:null;function k(H){for(var B=i(h);B!==null;){if(B.callback===null)o(h);else if(B.startTime<=H)o(h),B.sortIndex=B.expirationTime,a(m,B);else break;B=i(h)}}function P(H){if(w=!1,k(H),!R)if(i(m)!==null)R=!0,Z||(Z=!0,oe());else{var B=i(h);B!==null&&de(P,B.startTime-H)}}var Z=!1,I=-1,q=5,ae=-1;function ce(){return A?!0:!(t.unstable_now()-ae<q)}function me(){if(A=!1,Z){var H=t.unstable_now();ae=H;var B=!0;try{e:{R=!1,w&&(w=!1,D(I),I=-1),x=!0;var j=S;try{t:{for(k(H),b=i(m);b!==null&&!(b.expirationTime>H&&ce());){var ee=b.callback;if(typeof ee=="function"){b.callback=null,S=b.priorityLevel;var M=ee(b.expirationTime<=H);if(H=t.unstable_now(),typeof M=="function"){b.callback=M,k(H),B=!0;break t}b===i(m)&&o(m),k(H)}else o(m);b=i(m)}if(b!==null)B=!0;else{var $=i(h);$!==null&&de(P,$.startTime-H),B=!1}}break e}finally{b=null,S=j,x=!1}B=void 0}}finally{B?oe():Z=!1}}}var oe;if(typeof z=="function")oe=function(){z(me)};else if(typeof MessageChannel<"u"){var se=new MessageChannel,fe=se.port2;se.port1.onmessage=me,oe=function(){fe.postMessage(null)}}else oe=function(){_(me,0)};function de(H,B){I=_(function(){H(t.unstable_now())},B)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(H){H.callback=null},t.unstable_forceFrameRate=function(H){0>H||125<H?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<H?Math.floor(1e3/H):5},t.unstable_getCurrentPriorityLevel=function(){return S},t.unstable_next=function(H){switch(S){case 1:case 2:case 3:var B=3;break;default:B=S}var j=S;S=B;try{return H()}finally{S=j}},t.unstable_requestPaint=function(){A=!0},t.unstable_runWithPriority=function(H,B){switch(H){case 1:case 2:case 3:case 4:case 5:break;default:H=3}var j=S;S=H;try{return B()}finally{S=j}},t.unstable_scheduleCallback=function(H,B,j){var ee=t.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?ee+j:ee):j=ee,H){case 1:var M=-1;break;case 2:M=250;break;case 5:M=1073741823;break;case 4:M=1e4;break;default:M=5e3}return M=j+M,H={id:v++,callback:B,priorityLevel:H,startTime:j,expirationTime:M,sortIndex:-1},j>ee?(H.sortIndex=j,a(h,H),i(m)===null&&H===i(h)&&(w?(D(I),I=-1):w=!0,de(P,j-ee))):(H.sortIndex=M,a(m,H),R||x||(R=!0,Z||(Z=!0,oe()))),H},t.unstable_shouldYield=ce,t.unstable_wrapCallback=function(H){var B=S;return function(){var j=S;S=B;try{return H.apply(this,arguments)}finally{S=j}}}}(Gc)),Gc}var Rp;function ub(){return Rp||(Rp=1,Bc.exports=sb()),Bc.exports}var Pc={exports:{}},xt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ep;function cb(){if(Ep)return xt;Ep=1;var t=cs();function a(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)h+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var o={d:{f:i,r:function(){throw Error(a(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},u=Symbol.for("react.portal");function c(m,h,v){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:b==null?null:""+b,children:m,containerInfo:h,implementation:v}}var d=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return xt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,xt.createPortal=function(m,h){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(a(299));return c(m,h,null,v)},xt.flushSync=function(m){var h=d.T,v=o.p;try{if(d.T=null,o.p=2,m)return m()}finally{d.T=h,o.p=v,o.d.f()}},xt.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,o.d.C(m,h))},xt.prefetchDNS=function(m){typeof m=="string"&&o.d.D(m)},xt.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var v=h.as,b=g(v,h.crossOrigin),S=typeof h.integrity=="string"?h.integrity:void 0,x=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;v==="style"?o.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:b,integrity:S,fetchPriority:x}):v==="script"&&o.d.X(m,{crossOrigin:b,integrity:S,fetchPriority:x,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},xt.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var v=g(h.as,h.crossOrigin);o.d.M(m,{crossOrigin:v,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&o.d.M(m)},xt.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var v=h.as,b=g(v,h.crossOrigin);o.d.L(m,v,{crossOrigin:b,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},xt.preloadModule=function(m,h){if(typeof m=="string")if(h){var v=g(h.as,h.crossOrigin);o.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:v,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else o.d.m(m)},xt.requestFormReset=function(m){o.d.r(m)},xt.unstable_batchedUpdates=function(m,h){return m(h)},xt.useFormState=function(m,h,v){return d.H.useFormState(m,h,v)},xt.useFormStatus=function(){return d.H.useHostTransitionStatus()},xt.version="19.1.0",xt}var _p;function Lv(){if(_p)return Pc.exports;_p=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(a){console.error(a)}}return t(),Pc.exports=cb(),Pc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tp;function fb(){if(Tp)return ni;Tp=1;var t=ub(),a=cs(),i=Lv();function o(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)n+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function c(e){var n=e,l=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,(n.flags&4098)!==0&&(l=n.return),e=n.return;while(e)}return n.tag===3?l:null}function d(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function g(e){if(c(e)!==e)throw Error(o(188))}function m(e){var n=e.alternate;if(!n){if(n=c(e),n===null)throw Error(o(188));return n!==e?null:e}for(var l=e,r=n;;){var s=l.return;if(s===null)break;var f=s.alternate;if(f===null){if(r=s.return,r!==null){l=r;continue}break}if(s.child===f.child){for(f=s.child;f;){if(f===l)return g(s),e;if(f===r)return g(s),n;f=f.sibling}throw Error(o(188))}if(l.return!==r.return)l=s,r=f;else{for(var p=!1,y=s.child;y;){if(y===l){p=!0,l=s,r=f;break}if(y===r){p=!0,r=s,l=f;break}y=y.sibling}if(!p){for(y=f.child;y;){if(y===l){p=!0,l=f,r=s;break}if(y===r){p=!0,r=f,l=s;break}y=y.sibling}if(!p)throw Error(o(189))}}if(l.alternate!==r)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?e:n}function h(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e;for(e=e.child;e!==null;){if(n=h(e),n!==null)return n;e=e.sibling}return null}var v=Object.assign,b=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),x=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),w=Symbol.for("react.strict_mode"),A=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),D=Symbol.for("react.consumer"),z=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),Z=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),ae=Symbol.for("react.activity"),ce=Symbol.for("react.memo_cache_sentinel"),me=Symbol.iterator;function oe(e){return e===null||typeof e!="object"?null:(e=me&&e[me]||e["@@iterator"],typeof e=="function"?e:null)}var se=Symbol.for("react.client.reference");function fe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===se?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case A:return"Profiler";case w:return"StrictMode";case P:return"Suspense";case Z:return"SuspenseList";case ae:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case x:return"Portal";case z:return(e.displayName||"Context")+".Provider";case D:return(e._context.displayName||"Context")+".Consumer";case k:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case I:return n=e.displayName||null,n!==null?n:fe(e.type)||"Memo";case q:n=e._payload,e=e._init;try{return fe(e(n))}catch{}}return null}var de=Array.isArray,H=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j={pending:!1,data:null,method:null,action:null},ee=[],M=-1;function $(e){return{current:e}}function Q(e){0>M||(e.current=ee[M],ee[M]=null,M--)}function K(e,n){M++,ee[M]=e.current,e.current=n}var J=$(null),pe=$(null),re=$(null),W=$(null);function ue(e,n){switch(K(re,n),K(pe,e),K(J,null),n.nodeType){case 9:case 11:e=(e=n.documentElement)&&(e=e.namespaceURI)?Xh(e):0;break;default:if(e=n.tagName,n=n.namespaceURI)n=Xh(n),e=Ih(n,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Q(J),K(J,e)}function ze(){Q(J),Q(pe),Q(re)}function xe(e){e.memoizedState!==null&&K(W,e);var n=J.current,l=Ih(n,e.type);n!==l&&(K(pe,e),K(J,l))}function Re(e){pe.current===e&&(Q(J),Q(pe)),W.current===e&&(Q(W),Zr._currentValue=j)}var Te=Object.prototype.hasOwnProperty,be=t.unstable_scheduleCallback,we=t.unstable_cancelCallback,Xe=t.unstable_shouldYield,Ue=t.unstable_requestPaint,Ge=t.unstable_now,cn=t.unstable_getCurrentPriorityLevel,Fe=t.unstable_ImmediatePriority,st=t.unstable_UserBlockingPriority,_t=t.unstable_NormalPriority,Si=t.unstable_LowPriority,ar=t.unstable_IdlePriority,bi=t.log,Tt=t.unstable_setDisableYieldValue,Rt=null,gt=null;function fn(e){if(typeof bi=="function"&&Tt(e),gt&&typeof gt.setStrictMode=="function")try{gt.setStrictMode(Rt,e)}catch{}}var mt=Math.clz32?Math.clz32:qy,ky=Math.log,$y=Math.LN2;function qy(e){return e>>>=0,e===0?32:31-(ky(e)/$y|0)|0}var xi=256,wi=4194304;function Rl(e){var n=e&42;if(n!==0)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ci(e,n,l){var r=e.pendingLanes;if(r===0)return 0;var s=0,f=e.suspendedLanes,p=e.pingedLanes;e=e.warmLanes;var y=r&134217727;return y!==0?(r=y&~f,r!==0?s=Rl(r):(p&=y,p!==0?s=Rl(p):l||(l=y&~e,l!==0&&(s=Rl(l))))):(y=r&~f,y!==0?s=Rl(y):p!==0?s=Rl(p):l||(l=r&~e,l!==0&&(s=Rl(l)))),s===0?0:n!==0&&n!==s&&(n&f)===0&&(f=s&-s,l=n&-n,f>=l||f===32&&(l&4194048)!==0)?n:s}function rr(e,n){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)===0}function Yy(e,n){switch(e){case 1:case 2:case 4:case 8:case 64:return n+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Od(){var e=xi;return xi<<=1,(xi&4194048)===0&&(xi=256),e}function Dd(){var e=wi;return wi<<=1,(wi&62914560)===0&&(wi=4194304),e}function Es(e){for(var n=[],l=0;31>l;l++)n.push(e);return n}function ir(e,n){e.pendingLanes|=n,n!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Fy(e,n,l,r,s,f){var p=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var y=e.entanglements,T=e.expirationTimes,U=e.hiddenUpdates;for(l=p&~l;0<l;){var Y=31-mt(l),X=1<<Y;y[Y]=0,T[Y]=-1;var V=U[Y];if(V!==null)for(U[Y]=null,Y=0;Y<V.length;Y++){var G=V[Y];G!==null&&(G.lane&=-536870913)}l&=~X}r!==0&&Nd(e,r,0),f!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=f&~(p&~n))}function Nd(e,n,l){e.pendingLanes|=n,e.suspendedLanes&=~n;var r=31-mt(n);e.entangledLanes|=n,e.entanglements[r]=e.entanglements[r]|1073741824|l&4194090}function zd(e,n){var l=e.entangledLanes|=n;for(e=e.entanglements;l;){var r=31-mt(l),s=1<<r;s&n|e[r]&n&&(e[r]|=n),l&=~s}}function _s(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ts(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Hd(){var e=B.p;return e!==0?e:(e=window.event,e===void 0?32:gp(e.type))}function Xy(e,n){var l=B.p;try{return B.p=e,n()}finally{B.p=l}}var kn=Math.random().toString(36).slice(2),St="__reactFiber$"+kn,At="__reactProps$"+kn,Wl="__reactContainer$"+kn,As="__reactEvents$"+kn,Iy="__reactListeners$"+kn,Ky="__reactHandles$"+kn,Ld="__reactResources$"+kn,or="__reactMarker$"+kn;function Ms(e){delete e[St],delete e[At],delete e[As],delete e[Iy],delete e[Ky]}function Jl(e){var n=e[St];if(n)return n;for(var l=e.parentNode;l;){if(n=l[Wl]||l[St]){if(l=n.alternate,n.child!==null||l!==null&&l.child!==null)for(e=Wh(e);e!==null;){if(l=e[St])return l;e=Wh(e)}return n}e=l,l=e.parentNode}return null}function ea(e){if(e=e[St]||e[Wl]){var n=e.tag;if(n===5||n===6||n===13||n===26||n===27||n===3)return e}return null}function sr(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e.stateNode;throw Error(o(33))}function ta(e){var n=e[Ld];return n||(n=e[Ld]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function ut(e){e[or]=!0}var Ud=new Set,jd={};function El(e,n){na(e,n),na(e+"Capture",n)}function na(e,n){for(jd[e]=n,e=0;e<n.length;e++)Ud.add(n[e])}var Zy=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Vd={},Bd={};function Qy(e){return Te.call(Bd,e)?!0:Te.call(Vd,e)?!1:Zy.test(e)?Bd[e]=!0:(Vd[e]=!0,!1)}function Ri(e,n,l){if(Qy(n))if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(n);return;case"boolean":var r=n.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){e.removeAttribute(n);return}}e.setAttribute(n,""+l)}}function Ei(e,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttribute(n,""+l)}}function xn(e,n,l,r){if(r===null)e.removeAttribute(l);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(n,l,""+r)}}var Os,Gd;function la(e){if(Os===void 0)try{throw Error()}catch(l){var n=l.stack.trim().match(/\n( *(at )?)/);Os=n&&n[1]||"",Gd=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Os+e+Gd}var Ds=!1;function Ns(e,n){if(!e||Ds)return"";Ds=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(n){var X=function(){throw Error()};if(Object.defineProperty(X.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(X,[])}catch(G){var V=G}Reflect.construct(e,[],X)}else{try{X.call()}catch(G){V=G}e.call(X.prototype)}}else{try{throw Error()}catch(G){V=G}(X=e())&&typeof X.catch=="function"&&X.catch(function(){})}}catch(G){if(G&&V&&typeof G.stack=="string")return[G.stack,V.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=r.DetermineComponentFrameRoot(),p=f[0],y=f[1];if(p&&y){var T=p.split(`
`),U=y.split(`
`);for(s=r=0;r<T.length&&!T[r].includes("DetermineComponentFrameRoot");)r++;for(;s<U.length&&!U[s].includes("DetermineComponentFrameRoot");)s++;if(r===T.length||s===U.length)for(r=T.length-1,s=U.length-1;1<=r&&0<=s&&T[r]!==U[s];)s--;for(;1<=r&&0<=s;r--,s--)if(T[r]!==U[s]){if(r!==1||s!==1)do if(r--,s--,0>s||T[r]!==U[s]){var Y=`
`+T[r].replace(" at new "," at ");return e.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",e.displayName)),Y}while(1<=r&&0<=s);break}}}finally{Ds=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?la(l):""}function Wy(e){switch(e.tag){case 26:case 27:case 5:return la(e.type);case 16:return la("Lazy");case 13:return la("Suspense");case 19:return la("SuspenseList");case 0:case 15:return Ns(e.type,!1);case 11:return Ns(e.type.render,!1);case 1:return Ns(e.type,!0);case 31:return la("Activity");default:return""}}function Pd(e){try{var n="";do n+=Wy(e),e=e.return;while(e);return n}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function qt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function kd(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Jy(e){var n=kd(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),r=""+e[n];if(!e.hasOwnProperty(n)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var s=l.get,f=l.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return s.call(this)},set:function(p){r=""+p,f.call(this,p)}}),Object.defineProperty(e,n,{enumerable:l.enumerable}),{getValue:function(){return r},setValue:function(p){r=""+p},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function _i(e){e._valueTracker||(e._valueTracker=Jy(e))}function $d(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var l=n.getValue(),r="";return e&&(r=kd(e)?e.checked?"true":"false":e.value),e=r,e!==l?(n.setValue(e),!0):!1}function Ti(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var eS=/[\n"\\]/g;function Yt(e){return e.replace(eS,function(n){return"\\"+n.charCodeAt(0).toString(16)+" "})}function zs(e,n,l,r,s,f,p,y){e.name="",p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.type=p:e.removeAttribute("type"),n!=null?p==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+qt(n)):e.value!==""+qt(n)&&(e.value=""+qt(n)):p!=="submit"&&p!=="reset"||e.removeAttribute("value"),n!=null?Hs(e,p,qt(n)):l!=null?Hs(e,p,qt(l)):r!=null&&e.removeAttribute("value"),s==null&&f!=null&&(e.defaultChecked=!!f),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+qt(y):e.removeAttribute("name")}function qd(e,n,l,r,s,f,p,y){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.type=f),n!=null||l!=null){if(!(f!=="submit"&&f!=="reset"||n!=null))return;l=l!=null?""+qt(l):"",n=n!=null?""+qt(n):l,y||n===e.value||(e.value=n),e.defaultValue=n}r=r??s,r=typeof r!="function"&&typeof r!="symbol"&&!!r,e.checked=y?e.checked:!!r,e.defaultChecked=!!r,p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"&&(e.name=p)}function Hs(e,n,l){n==="number"&&Ti(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function aa(e,n,l,r){if(e=e.options,n){n={};for(var s=0;s<l.length;s++)n["$"+l[s]]=!0;for(l=0;l<e.length;l++)s=n.hasOwnProperty("$"+e[l].value),e[l].selected!==s&&(e[l].selected=s),s&&r&&(e[l].defaultSelected=!0)}else{for(l=""+qt(l),n=null,s=0;s<e.length;s++){if(e[s].value===l){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}n!==null||e[s].disabled||(n=e[s])}n!==null&&(n.selected=!0)}}function Yd(e,n,l){if(n!=null&&(n=""+qt(n),n!==e.value&&(e.value=n),l==null)){e.defaultValue!==n&&(e.defaultValue=n);return}e.defaultValue=l!=null?""+qt(l):""}function Fd(e,n,l,r){if(n==null){if(r!=null){if(l!=null)throw Error(o(92));if(de(r)){if(1<r.length)throw Error(o(93));r=r[0]}l=r}l==null&&(l=""),n=l}l=qt(n),e.defaultValue=l,r=e.textContent,r===l&&r!==""&&r!==null&&(e.value=r)}function ra(e,n){if(n){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=n;return}}e.textContent=n}var tS=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Xd(e,n,l){var r=n.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?r?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="":r?e.setProperty(n,l):typeof l!="number"||l===0||tS.has(n)?n==="float"?e.cssFloat=l:e[n]=(""+l).trim():e[n]=l+"px"}function Id(e,n,l){if(n!=null&&typeof n!="object")throw Error(o(62));if(e=e.style,l!=null){for(var r in l)!l.hasOwnProperty(r)||n!=null&&n.hasOwnProperty(r)||(r.indexOf("--")===0?e.setProperty(r,""):r==="float"?e.cssFloat="":e[r]="");for(var s in n)r=n[s],n.hasOwnProperty(s)&&l[s]!==r&&Xd(e,s,r)}else for(var f in n)n.hasOwnProperty(f)&&Xd(e,f,n[f])}function Ls(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var nS=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),lS=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ai(e){return lS.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Us=null;function js(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ia=null,oa=null;function Kd(e){var n=ea(e);if(n&&(e=n.stateNode)){var l=e[At]||null;e:switch(e=n.stateNode,n.type){case"input":if(zs(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),n=l.name,l.type==="radio"&&n!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Yt(""+n)+'"][type="radio"]'),n=0;n<l.length;n++){var r=l[n];if(r!==e&&r.form===e.form){var s=r[At]||null;if(!s)throw Error(o(90));zs(r,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(n=0;n<l.length;n++)r=l[n],r.form===e.form&&$d(r)}break e;case"textarea":Yd(e,l.value,l.defaultValue);break e;case"select":n=l.value,n!=null&&aa(e,!!l.multiple,n,!1)}}}var Vs=!1;function Zd(e,n,l){if(Vs)return e(n,l);Vs=!0;try{var r=e(n);return r}finally{if(Vs=!1,(ia!==null||oa!==null)&&(mo(),ia&&(n=ia,e=oa,oa=ia=null,Kd(n),e)))for(n=0;n<e.length;n++)Kd(e[n])}}function ur(e,n){var l=e.stateNode;if(l===null)return null;var r=l[At]||null;if(r===null)return null;l=r[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(o(231,n,typeof l));return l}var wn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Bs=!1;if(wn)try{var cr={};Object.defineProperty(cr,"passive",{get:function(){Bs=!0}}),window.addEventListener("test",cr,cr),window.removeEventListener("test",cr,cr)}catch{Bs=!1}var $n=null,Gs=null,Mi=null;function Qd(){if(Mi)return Mi;var e,n=Gs,l=n.length,r,s="value"in $n?$n.value:$n.textContent,f=s.length;for(e=0;e<l&&n[e]===s[e];e++);var p=l-e;for(r=1;r<=p&&n[l-r]===s[f-r];r++);return Mi=s.slice(e,1<r?1-r:void 0)}function Oi(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function Di(){return!0}function Wd(){return!1}function Mt(e){function n(l,r,s,f,p){this._reactName=l,this._targetInst=s,this.type=r,this.nativeEvent=f,this.target=p,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(l=e[y],this[y]=l?l(f):f[y]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Di:Wd,this.isPropagationStopped=Wd,this}return v(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Di)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Di)},persist:function(){},isPersistent:Di}),n}var _l={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ni=Mt(_l),fr=v({},_l,{view:0,detail:0}),aS=Mt(fr),Ps,ks,dr,zi=v({},fr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:qs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==dr&&(dr&&e.type==="mousemove"?(Ps=e.screenX-dr.screenX,ks=e.screenY-dr.screenY):ks=Ps=0,dr=e),Ps)},movementY:function(e){return"movementY"in e?e.movementY:ks}}),Jd=Mt(zi),rS=v({},zi,{dataTransfer:0}),iS=Mt(rS),oS=v({},fr,{relatedTarget:0}),$s=Mt(oS),sS=v({},_l,{animationName:0,elapsedTime:0,pseudoElement:0}),uS=Mt(sS),cS=v({},_l,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),fS=Mt(cS),dS=v({},_l,{data:0}),eg=Mt(dS),gS={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mS={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hS={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pS(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=hS[e])?!!n[e]:!1}function qs(){return pS}var vS=v({},fr,{key:function(e){if(e.key){var n=gS[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=Oi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?mS[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:qs,charCode:function(e){return e.type==="keypress"?Oi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Oi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),yS=Mt(vS),SS=v({},zi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),tg=Mt(SS),bS=v({},fr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:qs}),xS=Mt(bS),wS=v({},_l,{propertyName:0,elapsedTime:0,pseudoElement:0}),CS=Mt(wS),RS=v({},zi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ES=Mt(RS),_S=v({},_l,{newState:0,oldState:0}),TS=Mt(_S),AS=[9,13,27,32],Ys=wn&&"CompositionEvent"in window,gr=null;wn&&"documentMode"in document&&(gr=document.documentMode);var MS=wn&&"TextEvent"in window&&!gr,ng=wn&&(!Ys||gr&&8<gr&&11>=gr),lg=" ",ag=!1;function rg(e,n){switch(e){case"keyup":return AS.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ig(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var sa=!1;function OS(e,n){switch(e){case"compositionend":return ig(n);case"keypress":return n.which!==32?null:(ag=!0,lg);case"textInput":return e=n.data,e===lg&&ag?null:e;default:return null}}function DS(e,n){if(sa)return e==="compositionend"||!Ys&&rg(e,n)?(e=Qd(),Mi=Gs=$n=null,sa=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return ng&&n.locale!=="ko"?null:n.data;default:return null}}var NS={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function og(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!NS[e.type]:n==="textarea"}function sg(e,n,l,r){ia?oa?oa.push(r):oa=[r]:ia=r,n=bo(n,"onChange"),0<n.length&&(l=new Ni("onChange","change",null,l,r),e.push({event:l,listeners:n}))}var mr=null,hr=null;function zS(e){kh(e,0)}function Hi(e){var n=sr(e);if($d(n))return e}function ug(e,n){if(e==="change")return n}var cg=!1;if(wn){var Fs;if(wn){var Xs="oninput"in document;if(!Xs){var fg=document.createElement("div");fg.setAttribute("oninput","return;"),Xs=typeof fg.oninput=="function"}Fs=Xs}else Fs=!1;cg=Fs&&(!document.documentMode||9<document.documentMode)}function dg(){mr&&(mr.detachEvent("onpropertychange",gg),hr=mr=null)}function gg(e){if(e.propertyName==="value"&&Hi(hr)){var n=[];sg(n,hr,e,js(e)),Zd(zS,n)}}function HS(e,n,l){e==="focusin"?(dg(),mr=n,hr=l,mr.attachEvent("onpropertychange",gg)):e==="focusout"&&dg()}function LS(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hi(hr)}function US(e,n){if(e==="click")return Hi(n)}function jS(e,n){if(e==="input"||e==="change")return Hi(n)}function VS(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var Ht=typeof Object.is=="function"?Object.is:VS;function pr(e,n){if(Ht(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var l=Object.keys(e),r=Object.keys(n);if(l.length!==r.length)return!1;for(r=0;r<l.length;r++){var s=l[r];if(!Te.call(n,s)||!Ht(e[s],n[s]))return!1}return!0}function mg(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function hg(e,n){var l=mg(e);e=0;for(var r;l;){if(l.nodeType===3){if(r=e+l.textContent.length,e<=n&&r>=n)return{node:l,offset:n-e};e=r}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=mg(l)}}function pg(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?pg(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function vg(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var n=Ti(e.document);n instanceof e.HTMLIFrameElement;){try{var l=typeof n.contentWindow.location.href=="string"}catch{l=!1}if(l)e=n.contentWindow;else break;n=Ti(e.document)}return n}function Is(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}var BS=wn&&"documentMode"in document&&11>=document.documentMode,ua=null,Ks=null,vr=null,Zs=!1;function yg(e,n,l){var r=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Zs||ua==null||ua!==Ti(r)||(r=ua,"selectionStart"in r&&Is(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vr&&pr(vr,r)||(vr=r,r=bo(Ks,"onSelect"),0<r.length&&(n=new Ni("onSelect","select",null,n,l),e.push({event:n,listeners:r}),n.target=ua)))}function Tl(e,n){var l={};return l[e.toLowerCase()]=n.toLowerCase(),l["Webkit"+e]="webkit"+n,l["Moz"+e]="moz"+n,l}var ca={animationend:Tl("Animation","AnimationEnd"),animationiteration:Tl("Animation","AnimationIteration"),animationstart:Tl("Animation","AnimationStart"),transitionrun:Tl("Transition","TransitionRun"),transitionstart:Tl("Transition","TransitionStart"),transitioncancel:Tl("Transition","TransitionCancel"),transitionend:Tl("Transition","TransitionEnd")},Qs={},Sg={};wn&&(Sg=document.createElement("div").style,"AnimationEvent"in window||(delete ca.animationend.animation,delete ca.animationiteration.animation,delete ca.animationstart.animation),"TransitionEvent"in window||delete ca.transitionend.transition);function Al(e){if(Qs[e])return Qs[e];if(!ca[e])return e;var n=ca[e],l;for(l in n)if(n.hasOwnProperty(l)&&l in Sg)return Qs[e]=n[l];return e}var bg=Al("animationend"),xg=Al("animationiteration"),wg=Al("animationstart"),GS=Al("transitionrun"),PS=Al("transitionstart"),kS=Al("transitioncancel"),Cg=Al("transitionend"),Rg=new Map,Ws="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ws.push("scrollEnd");function en(e,n){Rg.set(e,n),El(n,[e])}var Eg=new WeakMap;function Ft(e,n){if(typeof e=="object"&&e!==null){var l=Eg.get(e);return l!==void 0?l:(n={value:e,source:n,stack:Pd(n)},Eg.set(e,n),n)}return{value:e,source:n,stack:Pd(n)}}var Xt=[],fa=0,Js=0;function Li(){for(var e=fa,n=Js=fa=0;n<e;){var l=Xt[n];Xt[n++]=null;var r=Xt[n];Xt[n++]=null;var s=Xt[n];Xt[n++]=null;var f=Xt[n];if(Xt[n++]=null,r!==null&&s!==null){var p=r.pending;p===null?s.next=s:(s.next=p.next,p.next=s),r.pending=s}f!==0&&_g(l,s,f)}}function Ui(e,n,l,r){Xt[fa++]=e,Xt[fa++]=n,Xt[fa++]=l,Xt[fa++]=r,Js|=r,e.lanes|=r,e=e.alternate,e!==null&&(e.lanes|=r)}function eu(e,n,l,r){return Ui(e,n,l,r),ji(e)}function da(e,n){return Ui(e,null,null,n),ji(e)}function _g(e,n,l){e.lanes|=l;var r=e.alternate;r!==null&&(r.lanes|=l);for(var s=!1,f=e.return;f!==null;)f.childLanes|=l,r=f.alternate,r!==null&&(r.childLanes|=l),f.tag===22&&(e=f.stateNode,e===null||e._visibility&1||(s=!0)),e=f,f=f.return;return e.tag===3?(f=e.stateNode,s&&n!==null&&(s=31-mt(l),e=f.hiddenUpdates,r=e[s],r===null?e[s]=[n]:r.push(n),n.lane=l|536870912),f):null}function ji(e){if(50<kr)throw kr=0,ic=null,Error(o(185));for(var n=e.return;n!==null;)e=n,n=e.return;return e.tag===3?e.stateNode:null}var ga={};function $S(e,n,l,r){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lt(e,n,l,r){return new $S(e,n,l,r)}function tu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Cn(e,n){var l=e.alternate;return l===null?(l=Lt(e.tag,n,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=n,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,n=e.dependencies,l.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Tg(e,n){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,n=l.dependencies,e.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Vi(e,n,l,r,s,f){var p=0;if(r=e,typeof e=="function")tu(e)&&(p=1);else if(typeof e=="string")p=Y1(e,l,J.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ae:return e=Lt(31,l,n,s),e.elementType=ae,e.lanes=f,e;case R:return Ml(l.children,s,f,n);case w:p=8,s|=24;break;case A:return e=Lt(12,l,n,s|2),e.elementType=A,e.lanes=f,e;case P:return e=Lt(13,l,n,s),e.elementType=P,e.lanes=f,e;case Z:return e=Lt(19,l,n,s),e.elementType=Z,e.lanes=f,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case _:case z:p=10;break e;case D:p=9;break e;case k:p=11;break e;case I:p=14;break e;case q:p=16,r=null;break e}p=29,l=Error(o(130,e===null?"null":typeof e,"")),r=null}return n=Lt(p,l,n,s),n.elementType=e,n.type=r,n.lanes=f,n}function Ml(e,n,l,r){return e=Lt(7,e,r,n),e.lanes=l,e}function nu(e,n,l){return e=Lt(6,e,null,n),e.lanes=l,e}function lu(e,n,l){return n=Lt(4,e.children!==null?e.children:[],e.key,n),n.lanes=l,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}var ma=[],ha=0,Bi=null,Gi=0,It=[],Kt=0,Ol=null,Rn=1,En="";function Dl(e,n){ma[ha++]=Gi,ma[ha++]=Bi,Bi=e,Gi=n}function Ag(e,n,l){It[Kt++]=Rn,It[Kt++]=En,It[Kt++]=Ol,Ol=e;var r=Rn;e=En;var s=32-mt(r)-1;r&=~(1<<s),l+=1;var f=32-mt(n)+s;if(30<f){var p=s-s%5;f=(r&(1<<p)-1).toString(32),r>>=p,s-=p,Rn=1<<32-mt(n)+s|l<<s|r,En=f+e}else Rn=1<<f|l<<s|r,En=e}function au(e){e.return!==null&&(Dl(e,1),Ag(e,1,0))}function ru(e){for(;e===Bi;)Bi=ma[--ha],ma[ha]=null,Gi=ma[--ha],ma[ha]=null;for(;e===Ol;)Ol=It[--Kt],It[Kt]=null,En=It[--Kt],It[Kt]=null,Rn=It[--Kt],It[Kt]=null}var Et=null,Ze=null,Le=!1,Nl=null,dn=!1,iu=Error(o(519));function zl(e){var n=Error(o(418,""));throw br(Ft(n,e)),iu}function Mg(e){var n=e.stateNode,l=e.type,r=e.memoizedProps;switch(n[St]=e,n[At]=r,l){case"dialog":Oe("cancel",n),Oe("close",n);break;case"iframe":case"object":case"embed":Oe("load",n);break;case"video":case"audio":for(l=0;l<qr.length;l++)Oe(qr[l],n);break;case"source":Oe("error",n);break;case"img":case"image":case"link":Oe("error",n),Oe("load",n);break;case"details":Oe("toggle",n);break;case"input":Oe("invalid",n),qd(n,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),_i(n);break;case"select":Oe("invalid",n);break;case"textarea":Oe("invalid",n),Fd(n,r.value,r.defaultValue,r.children),_i(n)}l=r.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||n.textContent===""+l||r.suppressHydrationWarning===!0||Fh(n.textContent,l)?(r.popover!=null&&(Oe("beforetoggle",n),Oe("toggle",n)),r.onScroll!=null&&Oe("scroll",n),r.onScrollEnd!=null&&Oe("scrollend",n),r.onClick!=null&&(n.onclick=xo),n=!0):n=!1,n||zl(e)}function Og(e){for(Et=e.return;Et;)switch(Et.tag){case 5:case 13:dn=!1;return;case 27:case 3:dn=!0;return;default:Et=Et.return}}function yr(e){if(e!==Et)return!1;if(!Le)return Og(e),Le=!0,!1;var n=e.tag,l;if((l=n!==3&&n!==27)&&((l=n===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||wc(e.type,e.memoizedProps)),l=!l),l&&Ze&&zl(e),Og(e),n===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(n===0){Ze=nn(e.nextSibling);break e}n--}else l!=="$"&&l!=="$!"&&l!=="$?"||n++;e=e.nextSibling}Ze=null}}else n===27?(n=Ze,rl(e.type)?(e=_c,_c=null,Ze=e):Ze=n):Ze=Et?nn(e.stateNode.nextSibling):null;return!0}function Sr(){Ze=Et=null,Le=!1}function Dg(){var e=Nl;return e!==null&&(Nt===null?Nt=e:Nt.push.apply(Nt,e),Nl=null),e}function br(e){Nl===null?Nl=[e]:Nl.push(e)}var ou=$(null),Hl=null,_n=null;function qn(e,n,l){K(ou,n._currentValue),n._currentValue=l}function Tn(e){e._currentValue=ou.current,Q(ou)}function su(e,n,l){for(;e!==null;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,r!==null&&(r.childLanes|=n)):r!==null&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===l)break;e=e.return}}function uu(e,n,l,r){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var f=s.dependencies;if(f!==null){var p=s.child;f=f.firstContext;e:for(;f!==null;){var y=f;f=s;for(var T=0;T<n.length;T++)if(y.context===n[T]){f.lanes|=l,y=f.alternate,y!==null&&(y.lanes|=l),su(f.return,l,e),r||(p=null);break e}f=y.next}}else if(s.tag===18){if(p=s.return,p===null)throw Error(o(341));p.lanes|=l,f=p.alternate,f!==null&&(f.lanes|=l),su(p,l,e),p=null}else p=s.child;if(p!==null)p.return=s;else for(p=s;p!==null;){if(p===e){p=null;break}if(s=p.sibling,s!==null){s.return=p.return,p=s;break}p=p.return}s=p}}function xr(e,n,l,r){e=null;for(var s=n,f=!1;s!==null;){if(!f){if((s.flags&524288)!==0)f=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var p=s.alternate;if(p===null)throw Error(o(387));if(p=p.memoizedProps,p!==null){var y=s.type;Ht(s.pendingProps.value,p.value)||(e!==null?e.push(y):e=[y])}}else if(s===W.current){if(p=s.alternate,p===null)throw Error(o(387));p.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(Zr):e=[Zr])}s=s.return}e!==null&&uu(n,e,l,r),n.flags|=262144}function Pi(e){for(e=e.firstContext;e!==null;){if(!Ht(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ll(e){Hl=e,_n=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function bt(e){return Ng(Hl,e)}function ki(e,n){return Hl===null&&Ll(e),Ng(e,n)}function Ng(e,n){var l=n._currentValue;if(n={context:n,memoizedValue:l,next:null},_n===null){if(e===null)throw Error(o(308));_n=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else _n=_n.next=n;return l}var qS=typeof AbortController<"u"?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(l,r){e.push(r)}};this.abort=function(){n.aborted=!0,e.forEach(function(l){return l()})}},YS=t.unstable_scheduleCallback,FS=t.unstable_NormalPriority,rt={$$typeof:z,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function cu(){return{controller:new qS,data:new Map,refCount:0}}function wr(e){e.refCount--,e.refCount===0&&YS(FS,function(){e.controller.abort()})}var Cr=null,fu=0,pa=0,va=null;function XS(e,n){if(Cr===null){var l=Cr=[];fu=0,pa=gc(),va={status:"pending",value:void 0,then:function(r){l.push(r)}}}return fu++,n.then(zg,zg),n}function zg(){if(--fu===0&&Cr!==null){va!==null&&(va.status="fulfilled");var e=Cr;Cr=null,pa=0,va=null;for(var n=0;n<e.length;n++)(0,e[n])()}}function IS(e,n){var l=[],r={status:"pending",value:null,reason:null,then:function(s){l.push(s)}};return e.then(function(){r.status="fulfilled",r.value=n;for(var s=0;s<l.length;s++)(0,l[s])(n)},function(s){for(r.status="rejected",r.reason=s,s=0;s<l.length;s++)(0,l[s])(void 0)}),r}var Hg=H.S;H.S=function(e,n){typeof n=="object"&&n!==null&&typeof n.then=="function"&&XS(e,n),Hg!==null&&Hg(e,n)};var Ul=$(null);function du(){var e=Ul.current;return e!==null?e:Ye.pooledCache}function $i(e,n){n===null?K(Ul,Ul.current):K(Ul,n.pool)}function Lg(){var e=du();return e===null?null:{parent:rt._currentValue,pool:e}}var Rr=Error(o(460)),Ug=Error(o(474)),qi=Error(o(542)),gu={then:function(){}};function jg(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Yi(){}function Vg(e,n,l){switch(l=e[l],l===void 0?e.push(n):l!==n&&(n.then(Yi,Yi),n=l),n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,Gg(e),e;default:if(typeof n.status=="string")n.then(Yi,Yi);else{if(e=Ye,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=n,e.status="pending",e.then(function(r){if(n.status==="pending"){var s=n;s.status="fulfilled",s.value=r}},function(r){if(n.status==="pending"){var s=n;s.status="rejected",s.reason=r}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,Gg(e),e}throw Er=n,Rr}}var Er=null;function Bg(){if(Er===null)throw Error(o(459));var e=Er;return Er=null,e}function Gg(e){if(e===Rr||e===qi)throw Error(o(483))}var Yn=!1;function mu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function hu(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Fn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Xn(e,n,l){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(je&2)!==0){var s=r.pending;return s===null?n.next=n:(n.next=s.next,s.next=n),r.pending=n,n=ji(e),_g(e,null,l),n}return Ui(e,r,n,l),ji(e)}function _r(e,n,l){if(n=n.updateQueue,n!==null&&(n=n.shared,(l&4194048)!==0)){var r=n.lanes;r&=e.pendingLanes,l|=r,n.lanes=l,zd(e,l)}}function pu(e,n){var l=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,l===r)){var s=null,f=null;if(l=l.firstBaseUpdate,l!==null){do{var p={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};f===null?s=f=p:f=f.next=p,l=l.next}while(l!==null);f===null?s=f=n:f=f.next=n}else s=f=n;l={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:f,shared:r.shared,callbacks:r.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=n:e.next=n,l.lastBaseUpdate=n}var vu=!1;function Tr(){if(vu){var e=va;if(e!==null)throw e}}function Ar(e,n,l,r){vu=!1;var s=e.updateQueue;Yn=!1;var f=s.firstBaseUpdate,p=s.lastBaseUpdate,y=s.shared.pending;if(y!==null){s.shared.pending=null;var T=y,U=T.next;T.next=null,p===null?f=U:p.next=U,p=T;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,y=Y.lastBaseUpdate,y!==p&&(y===null?Y.firstBaseUpdate=U:y.next=U,Y.lastBaseUpdate=T))}if(f!==null){var X=s.baseState;p=0,Y=U=T=null,y=f;do{var V=y.lane&-536870913,G=V!==y.lane;if(G?(De&V)===V:(r&V)===V){V!==0&&V===pa&&(vu=!0),Y!==null&&(Y=Y.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var ve=e,ge=y;V=n;var $e=l;switch(ge.tag){case 1:if(ve=ge.payload,typeof ve=="function"){X=ve.call($e,X,V);break e}X=ve;break e;case 3:ve.flags=ve.flags&-65537|128;case 0:if(ve=ge.payload,V=typeof ve=="function"?ve.call($e,X,V):ve,V==null)break e;X=v({},X,V);break e;case 2:Yn=!0}}V=y.callback,V!==null&&(e.flags|=64,G&&(e.flags|=8192),G=s.callbacks,G===null?s.callbacks=[V]:G.push(V))}else G={lane:V,tag:y.tag,payload:y.payload,callback:y.callback,next:null},Y===null?(U=Y=G,T=X):Y=Y.next=G,p|=V;if(y=y.next,y===null){if(y=s.shared.pending,y===null)break;G=y,y=G.next,G.next=null,s.lastBaseUpdate=G,s.shared.pending=null}}while(!0);Y===null&&(T=X),s.baseState=T,s.firstBaseUpdate=U,s.lastBaseUpdate=Y,f===null&&(s.shared.lanes=0),tl|=p,e.lanes=p,e.memoizedState=X}}function Pg(e,n){if(typeof e!="function")throw Error(o(191,e));e.call(n)}function kg(e,n){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)Pg(l[e],n)}var ya=$(null),Fi=$(0);function $g(e,n){e=Hn,K(Fi,e),K(ya,n),Hn=e|n.baseLanes}function yu(){K(Fi,Hn),K(ya,ya.current)}function Su(){Hn=Fi.current,Q(ya),Q(Fi)}var In=0,Ee=null,Pe=null,nt=null,Xi=!1,Sa=!1,jl=!1,Ii=0,Mr=0,ba=null,KS=0;function Je(){throw Error(o(321))}function bu(e,n){if(n===null)return!1;for(var l=0;l<n.length&&l<e.length;l++)if(!Ht(e[l],n[l]))return!1;return!0}function xu(e,n,l,r,s,f){return In=f,Ee=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,H.H=e===null||e.memoizedState===null?_m:Tm,jl=!1,f=l(r,s),jl=!1,Sa&&(f=Yg(n,l,r,s)),qg(e),f}function qg(e){H.H=eo;var n=Pe!==null&&Pe.next!==null;if(In=0,nt=Pe=Ee=null,Xi=!1,Mr=0,ba=null,n)throw Error(o(300));e===null||ct||(e=e.dependencies,e!==null&&Pi(e)&&(ct=!0))}function Yg(e,n,l,r){Ee=e;var s=0;do{if(Sa&&(ba=null),Mr=0,Sa=!1,25<=s)throw Error(o(301));if(s+=1,nt=Pe=null,e.updateQueue!=null){var f=e.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}H.H=n1,f=n(l,r)}while(Sa);return f}function ZS(){var e=H.H,n=e.useState()[0];return n=typeof n.then=="function"?Or(n):n,e=e.useState()[0],(Pe!==null?Pe.memoizedState:null)!==e&&(Ee.flags|=1024),n}function wu(){var e=Ii!==0;return Ii=0,e}function Cu(e,n,l){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~l}function Ru(e){if(Xi){for(e=e.memoizedState;e!==null;){var n=e.queue;n!==null&&(n.pending=null),e=e.next}Xi=!1}In=0,nt=Pe=Ee=null,Sa=!1,Mr=Ii=0,ba=null}function Ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return nt===null?Ee.memoizedState=nt=e:nt=nt.next=e,nt}function lt(){if(Pe===null){var e=Ee.alternate;e=e!==null?e.memoizedState:null}else e=Pe.next;var n=nt===null?Ee.memoizedState:nt.next;if(n!==null)nt=n,Pe=e;else{if(e===null)throw Ee.alternate===null?Error(o(467)):Error(o(310));Pe=e,e={memoizedState:Pe.memoizedState,baseState:Pe.baseState,baseQueue:Pe.baseQueue,queue:Pe.queue,next:null},nt===null?Ee.memoizedState=nt=e:nt=nt.next=e}return nt}function Eu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Or(e){var n=Mr;return Mr+=1,ba===null&&(ba=[]),e=Vg(ba,e,n),n=Ee,(nt===null?n.memoizedState:nt.next)===null&&(n=n.alternate,H.H=n===null||n.memoizedState===null?_m:Tm),e}function Ki(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Or(e);if(e.$$typeof===z)return bt(e)}throw Error(o(438,String(e)))}function _u(e){var n=null,l=Ee.updateQueue;if(l!==null&&(n=l.memoCache),n==null){var r=Ee.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(n={data:r.data.map(function(s){return s.slice()}),index:0})))}if(n==null&&(n={data:[],index:0}),l===null&&(l=Eu(),Ee.updateQueue=l),l.memoCache=n,l=n.data[n.index],l===void 0)for(l=n.data[n.index]=Array(e),r=0;r<e;r++)l[r]=ce;return n.index++,l}function An(e,n){return typeof n=="function"?n(e):n}function Zi(e){var n=lt();return Tu(n,Pe,e)}function Tu(e,n,l){var r=e.queue;if(r===null)throw Error(o(311));r.lastRenderedReducer=l;var s=e.baseQueue,f=r.pending;if(f!==null){if(s!==null){var p=s.next;s.next=f.next,f.next=p}n.baseQueue=s=f,r.pending=null}if(f=e.baseState,s===null)e.memoizedState=f;else{n=s.next;var y=p=null,T=null,U=n,Y=!1;do{var X=U.lane&-536870913;if(X!==U.lane?(De&X)===X:(In&X)===X){var V=U.revertLane;if(V===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null}),X===pa&&(Y=!0);else if((In&V)===V){U=U.next,V===pa&&(Y=!0);continue}else X={lane:0,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},T===null?(y=T=X,p=f):T=T.next=X,Ee.lanes|=V,tl|=V;X=U.action,jl&&l(f,X),f=U.hasEagerState?U.eagerState:l(f,X)}else V={lane:X,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},T===null?(y=T=V,p=f):T=T.next=V,Ee.lanes|=X,tl|=X;U=U.next}while(U!==null&&U!==n);if(T===null?p=f:T.next=y,!Ht(f,e.memoizedState)&&(ct=!0,Y&&(l=va,l!==null)))throw l;e.memoizedState=f,e.baseState=p,e.baseQueue=T,r.lastRenderedState=f}return s===null&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Au(e){var n=lt(),l=n.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=e;var r=l.dispatch,s=l.pending,f=n.memoizedState;if(s!==null){l.pending=null;var p=s=s.next;do f=e(f,p.action),p=p.next;while(p!==s);Ht(f,n.memoizedState)||(ct=!0),n.memoizedState=f,n.baseQueue===null&&(n.baseState=f),l.lastRenderedState=f}return[f,r]}function Fg(e,n,l){var r=Ee,s=lt(),f=Le;if(f){if(l===void 0)throw Error(o(407));l=l()}else l=n();var p=!Ht((Pe||s).memoizedState,l);p&&(s.memoizedState=l,ct=!0),s=s.queue;var y=Kg.bind(null,r,s,e);if(Dr(2048,8,y,[e]),s.getSnapshot!==n||p||nt!==null&&nt.memoizedState.tag&1){if(r.flags|=2048,xa(9,Qi(),Ig.bind(null,r,s,l,n),null),Ye===null)throw Error(o(349));f||(In&124)!==0||Xg(r,n,l)}return l}function Xg(e,n,l){e.flags|=16384,e={getSnapshot:n,value:l},n=Ee.updateQueue,n===null?(n=Eu(),Ee.updateQueue=n,n.stores=[e]):(l=n.stores,l===null?n.stores=[e]:l.push(e))}function Ig(e,n,l,r){n.value=l,n.getSnapshot=r,Zg(n)&&Qg(e)}function Kg(e,n,l){return l(function(){Zg(n)&&Qg(e)})}function Zg(e){var n=e.getSnapshot;e=e.value;try{var l=n();return!Ht(e,l)}catch{return!0}}function Qg(e){var n=da(e,2);n!==null&&Gt(n,e,2)}function Mu(e){var n=Ot();if(typeof e=="function"){var l=e;if(e=l(),jl){fn(!0);try{l()}finally{fn(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:An,lastRenderedState:e},n}function Wg(e,n,l,r){return e.baseState=l,Tu(e,Pe,typeof r=="function"?r:An)}function QS(e,n,l,r,s){if(Ji(e))throw Error(o(485));if(e=n.action,e!==null){var f={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(p){f.listeners.push(p)}};H.T!==null?l(!0):f.isTransition=!1,r(f),l=n.pending,l===null?(f.next=n.pending=f,Jg(n,f)):(f.next=l.next,n.pending=l.next=f)}}function Jg(e,n){var l=n.action,r=n.payload,s=e.state;if(n.isTransition){var f=H.T,p={};H.T=p;try{var y=l(s,r),T=H.S;T!==null&&T(p,y),em(e,n,y)}catch(U){Ou(e,n,U)}finally{H.T=f}}else try{f=l(s,r),em(e,n,f)}catch(U){Ou(e,n,U)}}function em(e,n,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(r){tm(e,n,r)},function(r){return Ou(e,n,r)}):tm(e,n,l)}function tm(e,n,l){n.status="fulfilled",n.value=l,nm(n),e.state=l,n=e.pending,n!==null&&(l=n.next,l===n?e.pending=null:(l=l.next,n.next=l,Jg(e,l)))}function Ou(e,n,l){var r=e.pending;if(e.pending=null,r!==null){r=r.next;do n.status="rejected",n.reason=l,nm(n),n=n.next;while(n!==r)}e.action=null}function nm(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function lm(e,n){return n}function am(e,n){if(Le){var l=Ye.formState;if(l!==null){e:{var r=Ee;if(Le){if(Ze){t:{for(var s=Ze,f=dn;s.nodeType!==8;){if(!f){s=null;break t}if(s=nn(s.nextSibling),s===null){s=null;break t}}f=s.data,s=f==="F!"||f==="F"?s:null}if(s){Ze=nn(s.nextSibling),r=s.data==="F!";break e}}zl(r)}r=!1}r&&(n=l[0])}}return l=Ot(),l.memoizedState=l.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:lm,lastRenderedState:n},l.queue=r,l=Cm.bind(null,Ee,r),r.dispatch=l,r=Mu(!1),f=Lu.bind(null,Ee,!1,r.queue),r=Ot(),s={state:n,dispatch:null,action:e,pending:null},r.queue=s,l=QS.bind(null,Ee,s,f,l),s.dispatch=l,r.memoizedState=e,[n,l,!1]}function rm(e){var n=lt();return im(n,Pe,e)}function im(e,n,l){if(n=Tu(e,n,lm)[0],e=Zi(An)[0],typeof n=="object"&&n!==null&&typeof n.then=="function")try{var r=Or(n)}catch(p){throw p===Rr?qi:p}else r=n;n=lt();var s=n.queue,f=s.dispatch;return l!==n.memoizedState&&(Ee.flags|=2048,xa(9,Qi(),WS.bind(null,s,l),null)),[r,f,e]}function WS(e,n){e.action=n}function om(e){var n=lt(),l=Pe;if(l!==null)return im(n,l,e);lt(),n=n.memoizedState,l=lt();var r=l.queue.dispatch;return l.memoizedState=e,[n,r,!1]}function xa(e,n,l,r){return e={tag:e,create:l,deps:r,inst:n,next:null},n=Ee.updateQueue,n===null&&(n=Eu(),Ee.updateQueue=n),l=n.lastEffect,l===null?n.lastEffect=e.next=e:(r=l.next,l.next=e,e.next=r,n.lastEffect=e),e}function Qi(){return{destroy:void 0,resource:void 0}}function sm(){return lt().memoizedState}function Wi(e,n,l,r){var s=Ot();r=r===void 0?null:r,Ee.flags|=e,s.memoizedState=xa(1|n,Qi(),l,r)}function Dr(e,n,l,r){var s=lt();r=r===void 0?null:r;var f=s.memoizedState.inst;Pe!==null&&r!==null&&bu(r,Pe.memoizedState.deps)?s.memoizedState=xa(n,f,l,r):(Ee.flags|=e,s.memoizedState=xa(1|n,f,l,r))}function um(e,n){Wi(8390656,8,e,n)}function cm(e,n){Dr(2048,8,e,n)}function fm(e,n){return Dr(4,2,e,n)}function dm(e,n){return Dr(4,4,e,n)}function gm(e,n){if(typeof n=="function"){e=e();var l=n(e);return function(){typeof l=="function"?l():n(null)}}if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function mm(e,n,l){l=l!=null?l.concat([e]):null,Dr(4,4,gm.bind(null,n,e),l)}function Du(){}function hm(e,n){var l=lt();n=n===void 0?null:n;var r=l.memoizedState;return n!==null&&bu(n,r[1])?r[0]:(l.memoizedState=[e,n],e)}function pm(e,n){var l=lt();n=n===void 0?null:n;var r=l.memoizedState;if(n!==null&&bu(n,r[1]))return r[0];if(r=e(),jl){fn(!0);try{e()}finally{fn(!1)}}return l.memoizedState=[r,n],r}function Nu(e,n,l){return l===void 0||(In&1073741824)!==0?e.memoizedState=n:(e.memoizedState=l,e=Sh(),Ee.lanes|=e,tl|=e,l)}function vm(e,n,l,r){return Ht(l,n)?l:ya.current!==null?(e=Nu(e,l,r),Ht(e,n)||(ct=!0),e):(In&42)===0?(ct=!0,e.memoizedState=l):(e=Sh(),Ee.lanes|=e,tl|=e,n)}function ym(e,n,l,r,s){var f=B.p;B.p=f!==0&&8>f?f:8;var p=H.T,y={};H.T=y,Lu(e,!1,n,l);try{var T=s(),U=H.S;if(U!==null&&U(y,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var Y=IS(T,r);Nr(e,n,Y,Bt(e))}else Nr(e,n,r,Bt(e))}catch(X){Nr(e,n,{then:function(){},status:"rejected",reason:X},Bt())}finally{B.p=f,H.T=p}}function JS(){}function zu(e,n,l,r){if(e.tag!==5)throw Error(o(476));var s=Sm(e).queue;ym(e,s,n,j,l===null?JS:function(){return bm(e),l(r)})}function Sm(e){var n=e.memoizedState;if(n!==null)return n;n={memoizedState:j,baseState:j,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:An,lastRenderedState:j},next:null};var l={};return n.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:An,lastRenderedState:l},next:null},e.memoizedState=n,e=e.alternate,e!==null&&(e.memoizedState=n),n}function bm(e){var n=Sm(e).next.queue;Nr(e,n,{},Bt())}function Hu(){return bt(Zr)}function xm(){return lt().memoizedState}function wm(){return lt().memoizedState}function e1(e){for(var n=e.return;n!==null;){switch(n.tag){case 24:case 3:var l=Bt();e=Fn(l);var r=Xn(n,e,l);r!==null&&(Gt(r,n,l),_r(r,n,l)),n={cache:cu()},e.payload=n;return}n=n.return}}function t1(e,n,l){var r=Bt();l={lane:r,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Ji(e)?Rm(n,l):(l=eu(e,n,l,r),l!==null&&(Gt(l,e,r),Em(l,n,r)))}function Cm(e,n,l){var r=Bt();Nr(e,n,l,r)}function Nr(e,n,l,r){var s={lane:r,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Ji(e))Rm(n,s);else{var f=e.alternate;if(e.lanes===0&&(f===null||f.lanes===0)&&(f=n.lastRenderedReducer,f!==null))try{var p=n.lastRenderedState,y=f(p,l);if(s.hasEagerState=!0,s.eagerState=y,Ht(y,p))return Ui(e,n,s,0),Ye===null&&Li(),!1}catch{}finally{}if(l=eu(e,n,s,r),l!==null)return Gt(l,e,r),Em(l,n,r),!0}return!1}function Lu(e,n,l,r){if(r={lane:2,revertLane:gc(),action:r,hasEagerState:!1,eagerState:null,next:null},Ji(e)){if(n)throw Error(o(479))}else n=eu(e,l,r,2),n!==null&&Gt(n,e,2)}function Ji(e){var n=e.alternate;return e===Ee||n!==null&&n===Ee}function Rm(e,n){Sa=Xi=!0;var l=e.pending;l===null?n.next=n:(n.next=l.next,l.next=n),e.pending=n}function Em(e,n,l){if((l&4194048)!==0){var r=n.lanes;r&=e.pendingLanes,l|=r,n.lanes=l,zd(e,l)}}var eo={readContext:bt,use:Ki,useCallback:Je,useContext:Je,useEffect:Je,useImperativeHandle:Je,useLayoutEffect:Je,useInsertionEffect:Je,useMemo:Je,useReducer:Je,useRef:Je,useState:Je,useDebugValue:Je,useDeferredValue:Je,useTransition:Je,useSyncExternalStore:Je,useId:Je,useHostTransitionStatus:Je,useFormState:Je,useActionState:Je,useOptimistic:Je,useMemoCache:Je,useCacheRefresh:Je},_m={readContext:bt,use:Ki,useCallback:function(e,n){return Ot().memoizedState=[e,n===void 0?null:n],e},useContext:bt,useEffect:um,useImperativeHandle:function(e,n,l){l=l!=null?l.concat([e]):null,Wi(4194308,4,gm.bind(null,n,e),l)},useLayoutEffect:function(e,n){return Wi(4194308,4,e,n)},useInsertionEffect:function(e,n){Wi(4,2,e,n)},useMemo:function(e,n){var l=Ot();n=n===void 0?null:n;var r=e();if(jl){fn(!0);try{e()}finally{fn(!1)}}return l.memoizedState=[r,n],r},useReducer:function(e,n,l){var r=Ot();if(l!==void 0){var s=l(n);if(jl){fn(!0);try{l(n)}finally{fn(!1)}}}else s=n;return r.memoizedState=r.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},r.queue=e,e=e.dispatch=t1.bind(null,Ee,e),[r.memoizedState,e]},useRef:function(e){var n=Ot();return e={current:e},n.memoizedState=e},useState:function(e){e=Mu(e);var n=e.queue,l=Cm.bind(null,Ee,n);return n.dispatch=l,[e.memoizedState,l]},useDebugValue:Du,useDeferredValue:function(e,n){var l=Ot();return Nu(l,e,n)},useTransition:function(){var e=Mu(!1);return e=ym.bind(null,Ee,e.queue,!0,!1),Ot().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,l){var r=Ee,s=Ot();if(Le){if(l===void 0)throw Error(o(407));l=l()}else{if(l=n(),Ye===null)throw Error(o(349));(De&124)!==0||Xg(r,n,l)}s.memoizedState=l;var f={value:l,getSnapshot:n};return s.queue=f,um(Kg.bind(null,r,f,e),[e]),r.flags|=2048,xa(9,Qi(),Ig.bind(null,r,f,l,n),null),l},useId:function(){var e=Ot(),n=Ye.identifierPrefix;if(Le){var l=En,r=Rn;l=(r&~(1<<32-mt(r)-1)).toString(32)+l,n="«"+n+"R"+l,l=Ii++,0<l&&(n+="H"+l.toString(32)),n+="»"}else l=KS++,n="«"+n+"r"+l.toString(32)+"»";return e.memoizedState=n},useHostTransitionStatus:Hu,useFormState:am,useActionState:am,useOptimistic:function(e){var n=Ot();n.memoizedState=n.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=l,n=Lu.bind(null,Ee,!0,l),l.dispatch=n,[e,n]},useMemoCache:_u,useCacheRefresh:function(){return Ot().memoizedState=e1.bind(null,Ee)}},Tm={readContext:bt,use:Ki,useCallback:hm,useContext:bt,useEffect:cm,useImperativeHandle:mm,useInsertionEffect:fm,useLayoutEffect:dm,useMemo:pm,useReducer:Zi,useRef:sm,useState:function(){return Zi(An)},useDebugValue:Du,useDeferredValue:function(e,n){var l=lt();return vm(l,Pe.memoizedState,e,n)},useTransition:function(){var e=Zi(An)[0],n=lt().memoizedState;return[typeof e=="boolean"?e:Or(e),n]},useSyncExternalStore:Fg,useId:xm,useHostTransitionStatus:Hu,useFormState:rm,useActionState:rm,useOptimistic:function(e,n){var l=lt();return Wg(l,Pe,e,n)},useMemoCache:_u,useCacheRefresh:wm},n1={readContext:bt,use:Ki,useCallback:hm,useContext:bt,useEffect:cm,useImperativeHandle:mm,useInsertionEffect:fm,useLayoutEffect:dm,useMemo:pm,useReducer:Au,useRef:sm,useState:function(){return Au(An)},useDebugValue:Du,useDeferredValue:function(e,n){var l=lt();return Pe===null?Nu(l,e,n):vm(l,Pe.memoizedState,e,n)},useTransition:function(){var e=Au(An)[0],n=lt().memoizedState;return[typeof e=="boolean"?e:Or(e),n]},useSyncExternalStore:Fg,useId:xm,useHostTransitionStatus:Hu,useFormState:om,useActionState:om,useOptimistic:function(e,n){var l=lt();return Pe!==null?Wg(l,Pe,e,n):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:_u,useCacheRefresh:wm},wa=null,zr=0;function to(e){var n=zr;return zr+=1,wa===null&&(wa=[]),Vg(wa,e,n)}function Hr(e,n){n=n.props.ref,e.ref=n!==void 0?n:null}function no(e,n){throw n.$$typeof===b?Error(o(525)):(e=Object.prototype.toString.call(n),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e)))}function Am(e){var n=e._init;return n(e._payload)}function Mm(e){function n(N,O){if(e){var L=N.deletions;L===null?(N.deletions=[O],N.flags|=16):L.push(O)}}function l(N,O){if(!e)return null;for(;O!==null;)n(N,O),O=O.sibling;return null}function r(N){for(var O=new Map;N!==null;)N.key!==null?O.set(N.key,N):O.set(N.index,N),N=N.sibling;return O}function s(N,O){return N=Cn(N,O),N.index=0,N.sibling=null,N}function f(N,O,L){return N.index=L,e?(L=N.alternate,L!==null?(L=L.index,L<O?(N.flags|=67108866,O):L):(N.flags|=67108866,O)):(N.flags|=1048576,O)}function p(N){return e&&N.alternate===null&&(N.flags|=67108866),N}function y(N,O,L,F){return O===null||O.tag!==6?(O=nu(L,N.mode,F),O.return=N,O):(O=s(O,L),O.return=N,O)}function T(N,O,L,F){var te=L.type;return te===R?Y(N,O,L.props.children,F,L.key):O!==null&&(O.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===q&&Am(te)===O.type)?(O=s(O,L.props),Hr(O,L),O.return=N,O):(O=Vi(L.type,L.key,L.props,null,N.mode,F),Hr(O,L),O.return=N,O)}function U(N,O,L,F){return O===null||O.tag!==4||O.stateNode.containerInfo!==L.containerInfo||O.stateNode.implementation!==L.implementation?(O=lu(L,N.mode,F),O.return=N,O):(O=s(O,L.children||[]),O.return=N,O)}function Y(N,O,L,F,te){return O===null||O.tag!==7?(O=Ml(L,N.mode,F,te),O.return=N,O):(O=s(O,L),O.return=N,O)}function X(N,O,L){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return O=nu(""+O,N.mode,L),O.return=N,O;if(typeof O=="object"&&O!==null){switch(O.$$typeof){case S:return L=Vi(O.type,O.key,O.props,null,N.mode,L),Hr(L,O),L.return=N,L;case x:return O=lu(O,N.mode,L),O.return=N,O;case q:var F=O._init;return O=F(O._payload),X(N,O,L)}if(de(O)||oe(O))return O=Ml(O,N.mode,L,null),O.return=N,O;if(typeof O.then=="function")return X(N,to(O),L);if(O.$$typeof===z)return X(N,ki(N,O),L);no(N,O)}return null}function V(N,O,L,F){var te=O!==null?O.key:null;if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return te!==null?null:y(N,O,""+L,F);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case S:return L.key===te?T(N,O,L,F):null;case x:return L.key===te?U(N,O,L,F):null;case q:return te=L._init,L=te(L._payload),V(N,O,L,F)}if(de(L)||oe(L))return te!==null?null:Y(N,O,L,F,null);if(typeof L.then=="function")return V(N,O,to(L),F);if(L.$$typeof===z)return V(N,O,ki(N,L),F);no(N,L)}return null}function G(N,O,L,F,te){if(typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint")return N=N.get(L)||null,y(O,N,""+F,te);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case S:return N=N.get(F.key===null?L:F.key)||null,T(O,N,F,te);case x:return N=N.get(F.key===null?L:F.key)||null,U(O,N,F,te);case q:var Ae=F._init;return F=Ae(F._payload),G(N,O,L,F,te)}if(de(F)||oe(F))return N=N.get(L)||null,Y(O,N,F,te,null);if(typeof F.then=="function")return G(N,O,L,to(F),te);if(F.$$typeof===z)return G(N,O,L,ki(O,F),te);no(O,F)}return null}function ve(N,O,L,F){for(var te=null,Ae=null,ie=O,he=O=0,dt=null;ie!==null&&he<L.length;he++){ie.index>he?(dt=ie,ie=null):dt=ie.sibling;var He=V(N,ie,L[he],F);if(He===null){ie===null&&(ie=dt);break}e&&ie&&He.alternate===null&&n(N,ie),O=f(He,O,he),Ae===null?te=He:Ae.sibling=He,Ae=He,ie=dt}if(he===L.length)return l(N,ie),Le&&Dl(N,he),te;if(ie===null){for(;he<L.length;he++)ie=X(N,L[he],F),ie!==null&&(O=f(ie,O,he),Ae===null?te=ie:Ae.sibling=ie,Ae=ie);return Le&&Dl(N,he),te}for(ie=r(ie);he<L.length;he++)dt=G(ie,N,he,L[he],F),dt!==null&&(e&&dt.alternate!==null&&ie.delete(dt.key===null?he:dt.key),O=f(dt,O,he),Ae===null?te=dt:Ae.sibling=dt,Ae=dt);return e&&ie.forEach(function(cl){return n(N,cl)}),Le&&Dl(N,he),te}function ge(N,O,L,F){if(L==null)throw Error(o(151));for(var te=null,Ae=null,ie=O,he=O=0,dt=null,He=L.next();ie!==null&&!He.done;he++,He=L.next()){ie.index>he?(dt=ie,ie=null):dt=ie.sibling;var cl=V(N,ie,He.value,F);if(cl===null){ie===null&&(ie=dt);break}e&&ie&&cl.alternate===null&&n(N,ie),O=f(cl,O,he),Ae===null?te=cl:Ae.sibling=cl,Ae=cl,ie=dt}if(He.done)return l(N,ie),Le&&Dl(N,he),te;if(ie===null){for(;!He.done;he++,He=L.next())He=X(N,He.value,F),He!==null&&(O=f(He,O,he),Ae===null?te=He:Ae.sibling=He,Ae=He);return Le&&Dl(N,he),te}for(ie=r(ie);!He.done;he++,He=L.next())He=G(ie,N,he,He.value,F),He!==null&&(e&&He.alternate!==null&&ie.delete(He.key===null?he:He.key),O=f(He,O,he),Ae===null?te=He:Ae.sibling=He,Ae=He);return e&&ie.forEach(function(lb){return n(N,lb)}),Le&&Dl(N,he),te}function $e(N,O,L,F){if(typeof L=="object"&&L!==null&&L.type===R&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case S:e:{for(var te=L.key;O!==null;){if(O.key===te){if(te=L.type,te===R){if(O.tag===7){l(N,O.sibling),F=s(O,L.props.children),F.return=N,N=F;break e}}else if(O.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===q&&Am(te)===O.type){l(N,O.sibling),F=s(O,L.props),Hr(F,L),F.return=N,N=F;break e}l(N,O);break}else n(N,O);O=O.sibling}L.type===R?(F=Ml(L.props.children,N.mode,F,L.key),F.return=N,N=F):(F=Vi(L.type,L.key,L.props,null,N.mode,F),Hr(F,L),F.return=N,N=F)}return p(N);case x:e:{for(te=L.key;O!==null;){if(O.key===te)if(O.tag===4&&O.stateNode.containerInfo===L.containerInfo&&O.stateNode.implementation===L.implementation){l(N,O.sibling),F=s(O,L.children||[]),F.return=N,N=F;break e}else{l(N,O);break}else n(N,O);O=O.sibling}F=lu(L,N.mode,F),F.return=N,N=F}return p(N);case q:return te=L._init,L=te(L._payload),$e(N,O,L,F)}if(de(L))return ve(N,O,L,F);if(oe(L)){if(te=oe(L),typeof te!="function")throw Error(o(150));return L=te.call(L),ge(N,O,L,F)}if(typeof L.then=="function")return $e(N,O,to(L),F);if(L.$$typeof===z)return $e(N,O,ki(N,L),F);no(N,L)}return typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint"?(L=""+L,O!==null&&O.tag===6?(l(N,O.sibling),F=s(O,L),F.return=N,N=F):(l(N,O),F=nu(L,N.mode,F),F.return=N,N=F),p(N)):l(N,O)}return function(N,O,L,F){try{zr=0;var te=$e(N,O,L,F);return wa=null,te}catch(ie){if(ie===Rr||ie===qi)throw ie;var Ae=Lt(29,ie,null,N.mode);return Ae.lanes=F,Ae.return=N,Ae}finally{}}}var Ca=Mm(!0),Om=Mm(!1),Zt=$(null),gn=null;function Kn(e){var n=e.alternate;K(it,it.current&1),K(Zt,e),gn===null&&(n===null||ya.current!==null||n.memoizedState!==null)&&(gn=e)}function Dm(e){if(e.tag===22){if(K(it,it.current),K(Zt,e),gn===null){var n=e.alternate;n!==null&&n.memoizedState!==null&&(gn=e)}}else Zn()}function Zn(){K(it,it.current),K(Zt,Zt.current)}function Mn(e){Q(Zt),gn===e&&(gn=null),Q(it)}var it=$(0);function lo(e){for(var n=e;n!==null;){if(n.tag===13){var l=n.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Ec(l)))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function Uu(e,n,l,r){n=e.memoizedState,l=l(r,n),l=l==null?n:v({},n,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var ju={enqueueSetState:function(e,n,l){e=e._reactInternals;var r=Bt(),s=Fn(r);s.payload=n,l!=null&&(s.callback=l),n=Xn(e,s,r),n!==null&&(Gt(n,e,r),_r(n,e,r))},enqueueReplaceState:function(e,n,l){e=e._reactInternals;var r=Bt(),s=Fn(r);s.tag=1,s.payload=n,l!=null&&(s.callback=l),n=Xn(e,s,r),n!==null&&(Gt(n,e,r),_r(n,e,r))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var l=Bt(),r=Fn(l);r.tag=2,n!=null&&(r.callback=n),n=Xn(e,r,l),n!==null&&(Gt(n,e,l),_r(n,e,l))}};function Nm(e,n,l,r,s,f,p){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,f,p):n.prototype&&n.prototype.isPureReactComponent?!pr(l,r)||!pr(s,f):!0}function zm(e,n,l,r){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(l,r),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(l,r),n.state!==e&&ju.enqueueReplaceState(n,n.state,null)}function Vl(e,n){var l=n;if("ref"in n){l={};for(var r in n)r!=="ref"&&(l[r]=n[r])}if(e=e.defaultProps){l===n&&(l=v({},l));for(var s in e)l[s]===void 0&&(l[s]=e[s])}return l}var ao=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Hm(e){ao(e)}function Lm(e){console.error(e)}function Um(e){ao(e)}function ro(e,n){try{var l=e.onUncaughtError;l(n.value,{componentStack:n.stack})}catch(r){setTimeout(function(){throw r})}}function jm(e,n,l){try{var r=e.onCaughtError;r(l.value,{componentStack:l.stack,errorBoundary:n.tag===1?n.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Vu(e,n,l){return l=Fn(l),l.tag=3,l.payload={element:null},l.callback=function(){ro(e,n)},l}function Vm(e){return e=Fn(e),e.tag=3,e}function Bm(e,n,l,r){var s=l.type.getDerivedStateFromError;if(typeof s=="function"){var f=r.value;e.payload=function(){return s(f)},e.callback=function(){jm(n,l,r)}}var p=l.stateNode;p!==null&&typeof p.componentDidCatch=="function"&&(e.callback=function(){jm(n,l,r),typeof s!="function"&&(nl===null?nl=new Set([this]):nl.add(this));var y=r.stack;this.componentDidCatch(r.value,{componentStack:y!==null?y:""})})}function l1(e,n,l,r,s){if(l.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(n=l.alternate,n!==null&&xr(n,l,s,!0),l=Zt.current,l!==null){switch(l.tag){case 13:return gn===null?sc():l.alternate===null&&Qe===0&&(Qe=3),l.flags&=-257,l.flags|=65536,l.lanes=s,r===gu?l.flags|=16384:(n=l.updateQueue,n===null?l.updateQueue=new Set([r]):n.add(r),cc(e,r,s)),!1;case 22:return l.flags|=65536,r===gu?l.flags|=16384:(n=l.updateQueue,n===null?(n={transitions:null,markerInstances:null,retryQueue:new Set([r])},l.updateQueue=n):(l=n.retryQueue,l===null?n.retryQueue=new Set([r]):l.add(r)),cc(e,r,s)),!1}throw Error(o(435,l.tag))}return cc(e,r,s),sc(),!1}if(Le)return n=Zt.current,n!==null?((n.flags&65536)===0&&(n.flags|=256),n.flags|=65536,n.lanes=s,r!==iu&&(e=Error(o(422),{cause:r}),br(Ft(e,l)))):(r!==iu&&(n=Error(o(423),{cause:r}),br(Ft(n,l))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,r=Ft(r,l),s=Vu(e.stateNode,r,s),pu(e,s),Qe!==4&&(Qe=2)),!1;var f=Error(o(520),{cause:r});if(f=Ft(f,l),Pr===null?Pr=[f]:Pr.push(f),Qe!==4&&(Qe=2),n===null)return!0;r=Ft(r,l),l=n;do{switch(l.tag){case 3:return l.flags|=65536,e=s&-s,l.lanes|=e,e=Vu(l.stateNode,r,e),pu(l,e),!1;case 1:if(n=l.type,f=l.stateNode,(l.flags&128)===0&&(typeof n.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(nl===null||!nl.has(f))))return l.flags|=65536,s&=-s,l.lanes|=s,s=Vm(s),Bm(s,e,l,r),pu(l,s),!1}l=l.return}while(l!==null);return!1}var Gm=Error(o(461)),ct=!1;function ht(e,n,l,r){n.child=e===null?Om(n,null,l,r):Ca(n,e.child,l,r)}function Pm(e,n,l,r,s){l=l.render;var f=n.ref;if("ref"in r){var p={};for(var y in r)y!=="ref"&&(p[y]=r[y])}else p=r;return Ll(n),r=xu(e,n,l,p,f,s),y=wu(),e!==null&&!ct?(Cu(e,n,s),On(e,n,s)):(Le&&y&&au(n),n.flags|=1,ht(e,n,r,s),n.child)}function km(e,n,l,r,s){if(e===null){var f=l.type;return typeof f=="function"&&!tu(f)&&f.defaultProps===void 0&&l.compare===null?(n.tag=15,n.type=f,$m(e,n,f,r,s)):(e=Vi(l.type,null,r,n,n.mode,s),e.ref=n.ref,e.return=n,n.child=e)}if(f=e.child,!Fu(e,s)){var p=f.memoizedProps;if(l=l.compare,l=l!==null?l:pr,l(p,r)&&e.ref===n.ref)return On(e,n,s)}return n.flags|=1,e=Cn(f,r),e.ref=n.ref,e.return=n,n.child=e}function $m(e,n,l,r,s){if(e!==null){var f=e.memoizedProps;if(pr(f,r)&&e.ref===n.ref)if(ct=!1,n.pendingProps=r=f,Fu(e,s))(e.flags&131072)!==0&&(ct=!0);else return n.lanes=e.lanes,On(e,n,s)}return Bu(e,n,l,r,s)}function qm(e,n,l){var r=n.pendingProps,s=r.children,f=e!==null?e.memoizedState:null;if(r.mode==="hidden"){if((n.flags&128)!==0){if(r=f!==null?f.baseLanes|l:l,e!==null){for(s=n.child=e.child,f=0;s!==null;)f=f|s.lanes|s.childLanes,s=s.sibling;n.childLanes=f&~r}else n.childLanes=0,n.child=null;return Ym(e,n,r,l)}if((l&536870912)!==0)n.memoizedState={baseLanes:0,cachePool:null},e!==null&&$i(n,f!==null?f.cachePool:null),f!==null?$g(n,f):yu(),Dm(n);else return n.lanes=n.childLanes=536870912,Ym(e,n,f!==null?f.baseLanes|l:l,l)}else f!==null?($i(n,f.cachePool),$g(n,f),Zn(),n.memoizedState=null):(e!==null&&$i(n,null),yu(),Zn());return ht(e,n,s,l),n.child}function Ym(e,n,l,r){var s=du();return s=s===null?null:{parent:rt._currentValue,pool:s},n.memoizedState={baseLanes:l,cachePool:s},e!==null&&$i(n,null),yu(),Dm(n),e!==null&&xr(e,n,r,!0),null}function io(e,n){var l=n.ref;if(l===null)e!==null&&e.ref!==null&&(n.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(e===null||e.ref!==l)&&(n.flags|=4194816)}}function Bu(e,n,l,r,s){return Ll(n),l=xu(e,n,l,r,void 0,s),r=wu(),e!==null&&!ct?(Cu(e,n,s),On(e,n,s)):(Le&&r&&au(n),n.flags|=1,ht(e,n,l,s),n.child)}function Fm(e,n,l,r,s,f){return Ll(n),n.updateQueue=null,l=Yg(n,r,l,s),qg(e),r=wu(),e!==null&&!ct?(Cu(e,n,f),On(e,n,f)):(Le&&r&&au(n),n.flags|=1,ht(e,n,l,f),n.child)}function Xm(e,n,l,r,s){if(Ll(n),n.stateNode===null){var f=ga,p=l.contextType;typeof p=="object"&&p!==null&&(f=bt(p)),f=new l(r,f),n.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=ju,n.stateNode=f,f._reactInternals=n,f=n.stateNode,f.props=r,f.state=n.memoizedState,f.refs={},mu(n),p=l.contextType,f.context=typeof p=="object"&&p!==null?bt(p):ga,f.state=n.memoizedState,p=l.getDerivedStateFromProps,typeof p=="function"&&(Uu(n,l,p,r),f.state=n.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(p=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),p!==f.state&&ju.enqueueReplaceState(f,f.state,null),Ar(n,r,f,s),Tr(),f.state=n.memoizedState),typeof f.componentDidMount=="function"&&(n.flags|=4194308),r=!0}else if(e===null){f=n.stateNode;var y=n.memoizedProps,T=Vl(l,y);f.props=T;var U=f.context,Y=l.contextType;p=ga,typeof Y=="object"&&Y!==null&&(p=bt(Y));var X=l.getDerivedStateFromProps;Y=typeof X=="function"||typeof f.getSnapshotBeforeUpdate=="function",y=n.pendingProps!==y,Y||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(y||U!==p)&&zm(n,f,r,p),Yn=!1;var V=n.memoizedState;f.state=V,Ar(n,r,f,s),Tr(),U=n.memoizedState,y||V!==U||Yn?(typeof X=="function"&&(Uu(n,l,X,r),U=n.memoizedState),(T=Yn||Nm(n,l,T,r,V,U,p))?(Y||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(n.flags|=4194308)):(typeof f.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=U),f.props=r,f.state=U,f.context=p,r=T):(typeof f.componentDidMount=="function"&&(n.flags|=4194308),r=!1)}else{f=n.stateNode,hu(e,n),p=n.memoizedProps,Y=Vl(l,p),f.props=Y,X=n.pendingProps,V=f.context,U=l.contextType,T=ga,typeof U=="object"&&U!==null&&(T=bt(U)),y=l.getDerivedStateFromProps,(U=typeof y=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(p!==X||V!==T)&&zm(n,f,r,T),Yn=!1,V=n.memoizedState,f.state=V,Ar(n,r,f,s),Tr();var G=n.memoizedState;p!==X||V!==G||Yn||e!==null&&e.dependencies!==null&&Pi(e.dependencies)?(typeof y=="function"&&(Uu(n,l,y,r),G=n.memoizedState),(Y=Yn||Nm(n,l,Y,r,V,G,T)||e!==null&&e.dependencies!==null&&Pi(e.dependencies))?(U||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(r,G,T),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(r,G,T)),typeof f.componentDidUpdate=="function"&&(n.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof f.componentDidUpdate!="function"||p===e.memoizedProps&&V===e.memoizedState||(n.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||p===e.memoizedProps&&V===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=G),f.props=r,f.state=G,f.context=T,r=Y):(typeof f.componentDidUpdate!="function"||p===e.memoizedProps&&V===e.memoizedState||(n.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||p===e.memoizedProps&&V===e.memoizedState||(n.flags|=1024),r=!1)}return f=r,io(e,n),r=(n.flags&128)!==0,f||r?(f=n.stateNode,l=r&&typeof l.getDerivedStateFromError!="function"?null:f.render(),n.flags|=1,e!==null&&r?(n.child=Ca(n,e.child,null,s),n.child=Ca(n,null,l,s)):ht(e,n,l,s),n.memoizedState=f.state,e=n.child):e=On(e,n,s),e}function Im(e,n,l,r){return Sr(),n.flags|=256,ht(e,n,l,r),n.child}var Gu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Pu(e){return{baseLanes:e,cachePool:Lg()}}function ku(e,n,l){return e=e!==null?e.childLanes&~l:0,n&&(e|=Qt),e}function Km(e,n,l){var r=n.pendingProps,s=!1,f=(n.flags&128)!==0,p;if((p=f)||(p=e!==null&&e.memoizedState===null?!1:(it.current&2)!==0),p&&(s=!0,n.flags&=-129),p=(n.flags&32)!==0,n.flags&=-33,e===null){if(Le){if(s?Kn(n):Zn(),Le){var y=Ze,T;if(T=y){e:{for(T=y,y=dn;T.nodeType!==8;){if(!y){y=null;break e}if(T=nn(T.nextSibling),T===null){y=null;break e}}y=T}y!==null?(n.memoizedState={dehydrated:y,treeContext:Ol!==null?{id:Rn,overflow:En}:null,retryLane:536870912,hydrationErrors:null},T=Lt(18,null,null,0),T.stateNode=y,T.return=n,n.child=T,Et=n,Ze=null,T=!0):T=!1}T||zl(n)}if(y=n.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return Ec(y)?n.lanes=32:n.lanes=536870912,null;Mn(n)}return y=r.children,r=r.fallback,s?(Zn(),s=n.mode,y=oo({mode:"hidden",children:y},s),r=Ml(r,s,l,null),y.return=n,r.return=n,y.sibling=r,n.child=y,s=n.child,s.memoizedState=Pu(l),s.childLanes=ku(e,p,l),n.memoizedState=Gu,r):(Kn(n),$u(n,y))}if(T=e.memoizedState,T!==null&&(y=T.dehydrated,y!==null)){if(f)n.flags&256?(Kn(n),n.flags&=-257,n=qu(e,n,l)):n.memoizedState!==null?(Zn(),n.child=e.child,n.flags|=128,n=null):(Zn(),s=r.fallback,y=n.mode,r=oo({mode:"visible",children:r.children},y),s=Ml(s,y,l,null),s.flags|=2,r.return=n,s.return=n,r.sibling=s,n.child=r,Ca(n,e.child,null,l),r=n.child,r.memoizedState=Pu(l),r.childLanes=ku(e,p,l),n.memoizedState=Gu,n=s);else if(Kn(n),Ec(y)){if(p=y.nextSibling&&y.nextSibling.dataset,p)var U=p.dgst;p=U,r=Error(o(419)),r.stack="",r.digest=p,br({value:r,source:null,stack:null}),n=qu(e,n,l)}else if(ct||xr(e,n,l,!1),p=(l&e.childLanes)!==0,ct||p){if(p=Ye,p!==null&&(r=l&-l,r=(r&42)!==0?1:_s(r),r=(r&(p.suspendedLanes|l))!==0?0:r,r!==0&&r!==T.retryLane))throw T.retryLane=r,da(e,r),Gt(p,e,r),Gm;y.data==="$?"||sc(),n=qu(e,n,l)}else y.data==="$?"?(n.flags|=192,n.child=e.child,n=null):(e=T.treeContext,Ze=nn(y.nextSibling),Et=n,Le=!0,Nl=null,dn=!1,e!==null&&(It[Kt++]=Rn,It[Kt++]=En,It[Kt++]=Ol,Rn=e.id,En=e.overflow,Ol=n),n=$u(n,r.children),n.flags|=4096);return n}return s?(Zn(),s=r.fallback,y=n.mode,T=e.child,U=T.sibling,r=Cn(T,{mode:"hidden",children:r.children}),r.subtreeFlags=T.subtreeFlags&65011712,U!==null?s=Cn(U,s):(s=Ml(s,y,l,null),s.flags|=2),s.return=n,r.return=n,r.sibling=s,n.child=r,r=s,s=n.child,y=e.child.memoizedState,y===null?y=Pu(l):(T=y.cachePool,T!==null?(U=rt._currentValue,T=T.parent!==U?{parent:U,pool:U}:T):T=Lg(),y={baseLanes:y.baseLanes|l,cachePool:T}),s.memoizedState=y,s.childLanes=ku(e,p,l),n.memoizedState=Gu,r):(Kn(n),l=e.child,e=l.sibling,l=Cn(l,{mode:"visible",children:r.children}),l.return=n,l.sibling=null,e!==null&&(p=n.deletions,p===null?(n.deletions=[e],n.flags|=16):p.push(e)),n.child=l,n.memoizedState=null,l)}function $u(e,n){return n=oo({mode:"visible",children:n},e.mode),n.return=e,e.child=n}function oo(e,n){return e=Lt(22,e,null,n),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function qu(e,n,l){return Ca(n,e.child,null,l),e=$u(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function Zm(e,n,l){e.lanes|=n;var r=e.alternate;r!==null&&(r.lanes|=n),su(e.return,n,l)}function Yu(e,n,l,r,s){var f=e.memoizedState;f===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:l,tailMode:s}:(f.isBackwards=n,f.rendering=null,f.renderingStartTime=0,f.last=r,f.tail=l,f.tailMode=s)}function Qm(e,n,l){var r=n.pendingProps,s=r.revealOrder,f=r.tail;if(ht(e,n,r.children,l),r=it.current,(r&2)!==0)r=r&1|2,n.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zm(e,l,n);else if(e.tag===19)Zm(e,l,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(K(it,r),s){case"forwards":for(l=n.child,s=null;l!==null;)e=l.alternate,e!==null&&lo(e)===null&&(s=l),l=l.sibling;l=s,l===null?(s=n.child,n.child=null):(s=l.sibling,l.sibling=null),Yu(n,!1,s,l,f);break;case"backwards":for(l=null,s=n.child,n.child=null;s!==null;){if(e=s.alternate,e!==null&&lo(e)===null){n.child=s;break}e=s.sibling,s.sibling=l,l=s,s=e}Yu(n,!0,l,null,f);break;case"together":Yu(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function On(e,n,l){if(e!==null&&(n.dependencies=e.dependencies),tl|=n.lanes,(l&n.childLanes)===0)if(e!==null){if(xr(e,n,l,!1),(l&n.childLanes)===0)return null}else return null;if(e!==null&&n.child!==e.child)throw Error(o(153));if(n.child!==null){for(e=n.child,l=Cn(e,e.pendingProps),n.child=l,l.return=n;e.sibling!==null;)e=e.sibling,l=l.sibling=Cn(e,e.pendingProps),l.return=n;l.sibling=null}return n.child}function Fu(e,n){return(e.lanes&n)!==0?!0:(e=e.dependencies,!!(e!==null&&Pi(e)))}function a1(e,n,l){switch(n.tag){case 3:ue(n,n.stateNode.containerInfo),qn(n,rt,e.memoizedState.cache),Sr();break;case 27:case 5:xe(n);break;case 4:ue(n,n.stateNode.containerInfo);break;case 10:qn(n,n.type,n.memoizedProps.value);break;case 13:var r=n.memoizedState;if(r!==null)return r.dehydrated!==null?(Kn(n),n.flags|=128,null):(l&n.child.childLanes)!==0?Km(e,n,l):(Kn(n),e=On(e,n,l),e!==null?e.sibling:null);Kn(n);break;case 19:var s=(e.flags&128)!==0;if(r=(l&n.childLanes)!==0,r||(xr(e,n,l,!1),r=(l&n.childLanes)!==0),s){if(r)return Qm(e,n,l);n.flags|=128}if(s=n.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),K(it,it.current),r)break;return null;case 22:case 23:return n.lanes=0,qm(e,n,l);case 24:qn(n,rt,e.memoizedState.cache)}return On(e,n,l)}function Wm(e,n,l){if(e!==null)if(e.memoizedProps!==n.pendingProps)ct=!0;else{if(!Fu(e,l)&&(n.flags&128)===0)return ct=!1,a1(e,n,l);ct=(e.flags&131072)!==0}else ct=!1,Le&&(n.flags&1048576)!==0&&Ag(n,Gi,n.index);switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var r=n.elementType,s=r._init;if(r=s(r._payload),n.type=r,typeof r=="function")tu(r)?(e=Vl(r,e),n.tag=1,n=Xm(null,n,r,e,l)):(n.tag=0,n=Bu(null,n,r,e,l));else{if(r!=null){if(s=r.$$typeof,s===k){n.tag=11,n=Pm(null,n,r,e,l);break e}else if(s===I){n.tag=14,n=km(null,n,r,e,l);break e}}throw n=fe(r)||r,Error(o(306,n,""))}}return n;case 0:return Bu(e,n,n.type,n.pendingProps,l);case 1:return r=n.type,s=Vl(r,n.pendingProps),Xm(e,n,r,s,l);case 3:e:{if(ue(n,n.stateNode.containerInfo),e===null)throw Error(o(387));r=n.pendingProps;var f=n.memoizedState;s=f.element,hu(e,n),Ar(n,r,null,l);var p=n.memoizedState;if(r=p.cache,qn(n,rt,r),r!==f.cache&&uu(n,[rt],l,!0),Tr(),r=p.element,f.isDehydrated)if(f={element:r,isDehydrated:!1,cache:p.cache},n.updateQueue.baseState=f,n.memoizedState=f,n.flags&256){n=Im(e,n,r,l);break e}else if(r!==s){s=Ft(Error(o(424)),n),br(s),n=Im(e,n,r,l);break e}else{switch(e=n.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ze=nn(e.firstChild),Et=n,Le=!0,Nl=null,dn=!0,l=Om(n,null,r,l),n.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Sr(),r===s){n=On(e,n,l);break e}ht(e,n,r,l)}n=n.child}return n;case 26:return io(e,n),e===null?(l=np(n.type,null,n.pendingProps,null))?n.memoizedState=l:Le||(l=n.type,e=n.pendingProps,r=wo(re.current).createElement(l),r[St]=n,r[At]=e,vt(r,l,e),ut(r),n.stateNode=r):n.memoizedState=np(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return xe(n),e===null&&Le&&(r=n.stateNode=Jh(n.type,n.pendingProps,re.current),Et=n,dn=!0,s=Ze,rl(n.type)?(_c=s,Ze=nn(r.firstChild)):Ze=s),ht(e,n,n.pendingProps.children,l),io(e,n),e===null&&(n.flags|=4194304),n.child;case 5:return e===null&&Le&&((s=r=Ze)&&(r=N1(r,n.type,n.pendingProps,dn),r!==null?(n.stateNode=r,Et=n,Ze=nn(r.firstChild),dn=!1,s=!0):s=!1),s||zl(n)),xe(n),s=n.type,f=n.pendingProps,p=e!==null?e.memoizedProps:null,r=f.children,wc(s,f)?r=null:p!==null&&wc(s,p)&&(n.flags|=32),n.memoizedState!==null&&(s=xu(e,n,ZS,null,null,l),Zr._currentValue=s),io(e,n),ht(e,n,r,l),n.child;case 6:return e===null&&Le&&((e=l=Ze)&&(l=z1(l,n.pendingProps,dn),l!==null?(n.stateNode=l,Et=n,Ze=null,e=!0):e=!1),e||zl(n)),null;case 13:return Km(e,n,l);case 4:return ue(n,n.stateNode.containerInfo),r=n.pendingProps,e===null?n.child=Ca(n,null,r,l):ht(e,n,r,l),n.child;case 11:return Pm(e,n,n.type,n.pendingProps,l);case 7:return ht(e,n,n.pendingProps,l),n.child;case 8:return ht(e,n,n.pendingProps.children,l),n.child;case 12:return ht(e,n,n.pendingProps.children,l),n.child;case 10:return r=n.pendingProps,qn(n,n.type,r.value),ht(e,n,r.children,l),n.child;case 9:return s=n.type._context,r=n.pendingProps.children,Ll(n),s=bt(s),r=r(s),n.flags|=1,ht(e,n,r,l),n.child;case 14:return km(e,n,n.type,n.pendingProps,l);case 15:return $m(e,n,n.type,n.pendingProps,l);case 19:return Qm(e,n,l);case 31:return r=n.pendingProps,l=n.mode,r={mode:r.mode,children:r.children},e===null?(l=oo(r,l),l.ref=n.ref,n.child=l,l.return=n,n=l):(l=Cn(e.child,r),l.ref=n.ref,n.child=l,l.return=n,n=l),n;case 22:return qm(e,n,l);case 24:return Ll(n),r=bt(rt),e===null?(s=du(),s===null&&(s=Ye,f=cu(),s.pooledCache=f,f.refCount++,f!==null&&(s.pooledCacheLanes|=l),s=f),n.memoizedState={parent:r,cache:s},mu(n),qn(n,rt,s)):((e.lanes&l)!==0&&(hu(e,n),Ar(n,null,null,l),Tr()),s=e.memoizedState,f=n.memoizedState,s.parent!==r?(s={parent:r,cache:r},n.memoizedState=s,n.lanes===0&&(n.memoizedState=n.updateQueue.baseState=s),qn(n,rt,r)):(r=f.cache,qn(n,rt,r),r!==s.cache&&uu(n,[rt],l,!0))),ht(e,n,n.pendingProps.children,l),n.child;case 29:throw n.pendingProps}throw Error(o(156,n.tag))}function Dn(e){e.flags|=4}function Jm(e,n){if(n.type!=="stylesheet"||(n.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!op(n)){if(n=Zt.current,n!==null&&((De&4194048)===De?gn!==null:(De&62914560)!==De&&(De&536870912)===0||n!==gn))throw Er=gu,Ug;e.flags|=8192}}function so(e,n){n!==null&&(e.flags|=4),e.flags&16384&&(n=e.tag!==22?Dd():536870912,e.lanes|=n,Ta|=n)}function Lr(e,n){if(!Le)switch(e.tailMode){case"hidden":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var r=null;l!==null;)l.alternate!==null&&(r=l),l=l.sibling;r===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ke(e){var n=e.alternate!==null&&e.alternate.child===e.child,l=0,r=0;if(n)for(var s=e.child;s!==null;)l|=s.lanes|s.childLanes,r|=s.subtreeFlags&65011712,r|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)l|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=l,n}function r1(e,n,l){var r=n.pendingProps;switch(ru(n),n.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ke(n),null;case 1:return Ke(n),null;case 3:return l=n.stateNode,r=null,e!==null&&(r=e.memoizedState.cache),n.memoizedState.cache!==r&&(n.flags|=2048),Tn(rt),ze(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(yr(n)?Dn(n):e===null||e.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,Dg())),Ke(n),null;case 26:return l=n.memoizedState,e===null?(Dn(n),l!==null?(Ke(n),Jm(n,l)):(Ke(n),n.flags&=-16777217)):l?l!==e.memoizedState?(Dn(n),Ke(n),Jm(n,l)):(Ke(n),n.flags&=-16777217):(e.memoizedProps!==r&&Dn(n),Ke(n),n.flags&=-16777217),null;case 27:Re(n),l=re.current;var s=n.type;if(e!==null&&n.stateNode!=null)e.memoizedProps!==r&&Dn(n);else{if(!r){if(n.stateNode===null)throw Error(o(166));return Ke(n),null}e=J.current,yr(n)?Mg(n):(e=Jh(s,r,l),n.stateNode=e,Dn(n))}return Ke(n),null;case 5:if(Re(n),l=n.type,e!==null&&n.stateNode!=null)e.memoizedProps!==r&&Dn(n);else{if(!r){if(n.stateNode===null)throw Error(o(166));return Ke(n),null}if(e=J.current,yr(n))Mg(n);else{switch(s=wo(re.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof r.is=="string"?s.createElement("select",{is:r.is}):s.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e=typeof r.is=="string"?s.createElement(l,{is:r.is}):s.createElement(l)}}e[St]=n,e[At]=r;e:for(s=n.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===n)break e;for(;s.sibling===null;){if(s.return===null||s.return===n)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}n.stateNode=e;e:switch(vt(e,l,r),l){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Dn(n)}}return Ke(n),n.flags&=-16777217,null;case 6:if(e&&n.stateNode!=null)e.memoizedProps!==r&&Dn(n);else{if(typeof r!="string"&&n.stateNode===null)throw Error(o(166));if(e=re.current,yr(n)){if(e=n.stateNode,l=n.memoizedProps,r=null,s=Et,s!==null)switch(s.tag){case 27:case 5:r=s.memoizedProps}e[St]=n,e=!!(e.nodeValue===l||r!==null&&r.suppressHydrationWarning===!0||Fh(e.nodeValue,l)),e||zl(n)}else e=wo(e).createTextNode(r),e[St]=n,n.stateNode=e}return Ke(n),null;case 13:if(r=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=yr(n),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(o(318));if(s=n.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[St]=n}else Sr(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;Ke(n),s=!1}else s=Dg(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return n.flags&256?(Mn(n),n):(Mn(n),null)}if(Mn(n),(n.flags&128)!==0)return n.lanes=l,n;if(l=r!==null,e=e!==null&&e.memoizedState!==null,l){r=n.child,s=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(s=r.alternate.memoizedState.cachePool.pool);var f=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(f=r.memoizedState.cachePool.pool),f!==s&&(r.flags|=2048)}return l!==e&&l&&(n.child.flags|=8192),so(n,n.updateQueue),Ke(n),null;case 4:return ze(),e===null&&vc(n.stateNode.containerInfo),Ke(n),null;case 10:return Tn(n.type),Ke(n),null;case 19:if(Q(it),s=n.memoizedState,s===null)return Ke(n),null;if(r=(n.flags&128)!==0,f=s.rendering,f===null)if(r)Lr(s,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=n.child;e!==null;){if(f=lo(e),f!==null){for(n.flags|=128,Lr(s,!1),e=f.updateQueue,n.updateQueue=e,so(n,e),n.subtreeFlags=0,e=l,l=n.child;l!==null;)Tg(l,e),l=l.sibling;return K(it,it.current&1|2),n.child}e=e.sibling}s.tail!==null&&Ge()>fo&&(n.flags|=128,r=!0,Lr(s,!1),n.lanes=4194304)}else{if(!r)if(e=lo(f),e!==null){if(n.flags|=128,r=!0,e=e.updateQueue,n.updateQueue=e,so(n,e),Lr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!f.alternate&&!Le)return Ke(n),null}else 2*Ge()-s.renderingStartTime>fo&&l!==536870912&&(n.flags|=128,r=!0,Lr(s,!1),n.lanes=4194304);s.isBackwards?(f.sibling=n.child,n.child=f):(e=s.last,e!==null?e.sibling=f:n.child=f,s.last=f)}return s.tail!==null?(n=s.tail,s.rendering=n,s.tail=n.sibling,s.renderingStartTime=Ge(),n.sibling=null,e=it.current,K(it,r?e&1|2:e&1),n):(Ke(n),null);case 22:case 23:return Mn(n),Su(),r=n.memoizedState!==null,e!==null?e.memoizedState!==null!==r&&(n.flags|=8192):r&&(n.flags|=8192),r?(l&536870912)!==0&&(n.flags&128)===0&&(Ke(n),n.subtreeFlags&6&&(n.flags|=8192)):Ke(n),l=n.updateQueue,l!==null&&so(n,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),r=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(r=n.memoizedState.cachePool.pool),r!==l&&(n.flags|=2048),e!==null&&Q(Ul),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),n.memoizedState.cache!==l&&(n.flags|=2048),Tn(rt),Ke(n),null;case 25:return null;case 30:return null}throw Error(o(156,n.tag))}function i1(e,n){switch(ru(n),n.tag){case 1:return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return Tn(rt),ze(),e=n.flags,(e&65536)!==0&&(e&128)===0?(n.flags=e&-65537|128,n):null;case 26:case 27:case 5:return Re(n),null;case 13:if(Mn(n),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(o(340));Sr()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return Q(it),null;case 4:return ze(),null;case 10:return Tn(n.type),null;case 22:case 23:return Mn(n),Su(),e!==null&&Q(Ul),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 24:return Tn(rt),null;case 25:return null;default:return null}}function eh(e,n){switch(ru(n),n.tag){case 3:Tn(rt),ze();break;case 26:case 27:case 5:Re(n);break;case 4:ze();break;case 13:Mn(n);break;case 19:Q(it);break;case 10:Tn(n.type);break;case 22:case 23:Mn(n),Su(),e!==null&&Q(Ul);break;case 24:Tn(rt)}}function Ur(e,n){try{var l=n.updateQueue,r=l!==null?l.lastEffect:null;if(r!==null){var s=r.next;l=s;do{if((l.tag&e)===e){r=void 0;var f=l.create,p=l.inst;r=f(),p.destroy=r}l=l.next}while(l!==s)}}catch(y){qe(n,n.return,y)}}function Qn(e,n,l){try{var r=n.updateQueue,s=r!==null?r.lastEffect:null;if(s!==null){var f=s.next;r=f;do{if((r.tag&e)===e){var p=r.inst,y=p.destroy;if(y!==void 0){p.destroy=void 0,s=n;var T=l,U=y;try{U()}catch(Y){qe(s,T,Y)}}}r=r.next}while(r!==f)}}catch(Y){qe(n,n.return,Y)}}function th(e){var n=e.updateQueue;if(n!==null){var l=e.stateNode;try{kg(n,l)}catch(r){qe(e,e.return,r)}}}function nh(e,n,l){l.props=Vl(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(r){qe(e,n,r)}}function jr(e,n){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;case 30:r=e.stateNode;break;default:r=e.stateNode}typeof l=="function"?e.refCleanup=l(r):l.current=r}}catch(s){qe(e,n,s)}}function mn(e,n){var l=e.ref,r=e.refCleanup;if(l!==null)if(typeof r=="function")try{r()}catch(s){qe(e,n,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(s){qe(e,n,s)}else l.current=null}function lh(e){var n=e.type,l=e.memoizedProps,r=e.stateNode;try{e:switch(n){case"button":case"input":case"select":case"textarea":l.autoFocus&&r.focus();break e;case"img":l.src?r.src=l.src:l.srcSet&&(r.srcset=l.srcSet)}}catch(s){qe(e,e.return,s)}}function Xu(e,n,l){try{var r=e.stateNode;T1(r,e.type,l,n),r[At]=n}catch(s){qe(e,e.return,s)}}function ah(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&rl(e.type)||e.tag===4}function Iu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ah(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&rl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ku(e,n,l){var r=e.tag;if(r===5||r===6)e=e.stateNode,n?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,n):(n=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,n.appendChild(e),l=l._reactRootContainer,l!=null||n.onclick!==null||(n.onclick=xo));else if(r!==4&&(r===27&&rl(e.type)&&(l=e.stateNode,n=null),e=e.child,e!==null))for(Ku(e,n,l),e=e.sibling;e!==null;)Ku(e,n,l),e=e.sibling}function uo(e,n,l){var r=e.tag;if(r===5||r===6)e=e.stateNode,n?l.insertBefore(e,n):l.appendChild(e);else if(r!==4&&(r===27&&rl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(uo(e,n,l),e=e.sibling;e!==null;)uo(e,n,l),e=e.sibling}function rh(e){var n=e.stateNode,l=e.memoizedProps;try{for(var r=e.type,s=n.attributes;s.length;)n.removeAttributeNode(s[0]);vt(n,r,l),n[St]=e,n[At]=l}catch(f){qe(e,e.return,f)}}var Nn=!1,et=!1,Zu=!1,ih=typeof WeakSet=="function"?WeakSet:Set,ft=null;function o1(e,n){if(e=e.containerInfo,bc=Ao,e=vg(e),Is(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var r=l.getSelection&&l.getSelection();if(r&&r.rangeCount!==0){l=r.anchorNode;var s=r.anchorOffset,f=r.focusNode;r=r.focusOffset;try{l.nodeType,f.nodeType}catch{l=null;break e}var p=0,y=-1,T=-1,U=0,Y=0,X=e,V=null;t:for(;;){for(var G;X!==l||s!==0&&X.nodeType!==3||(y=p+s),X!==f||r!==0&&X.nodeType!==3||(T=p+r),X.nodeType===3&&(p+=X.nodeValue.length),(G=X.firstChild)!==null;)V=X,X=G;for(;;){if(X===e)break t;if(V===l&&++U===s&&(y=p),V===f&&++Y===r&&(T=p),(G=X.nextSibling)!==null)break;X=V,V=X.parentNode}X=G}l=y===-1||T===-1?null:{start:y,end:T}}else l=null}l=l||{start:0,end:0}}else l=null;for(xc={focusedElem:e,selectionRange:l},Ao=!1,ft=n;ft!==null;)if(n=ft,e=n.child,(n.subtreeFlags&1024)!==0&&e!==null)e.return=n,ft=e;else for(;ft!==null;){switch(n=ft,f=n.alternate,e=n.flags,n.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&f!==null){e=void 0,l=n,s=f.memoizedProps,f=f.memoizedState,r=l.stateNode;try{var ve=Vl(l.type,s,l.elementType===l.type);e=r.getSnapshotBeforeUpdate(ve,f),r.__reactInternalSnapshotBeforeUpdate=e}catch(ge){qe(l,l.return,ge)}}break;case 3:if((e&1024)!==0){if(e=n.stateNode.containerInfo,l=e.nodeType,l===9)Rc(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Rc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=n.sibling,e!==null){e.return=n.return,ft=e;break}ft=n.return}}function oh(e,n,l){var r=l.flags;switch(l.tag){case 0:case 11:case 15:Wn(e,l),r&4&&Ur(5,l);break;case 1:if(Wn(e,l),r&4)if(e=l.stateNode,n===null)try{e.componentDidMount()}catch(p){qe(l,l.return,p)}else{var s=Vl(l.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(s,n,e.__reactInternalSnapshotBeforeUpdate)}catch(p){qe(l,l.return,p)}}r&64&&th(l),r&512&&jr(l,l.return);break;case 3:if(Wn(e,l),r&64&&(e=l.updateQueue,e!==null)){if(n=null,l.child!==null)switch(l.child.tag){case 27:case 5:n=l.child.stateNode;break;case 1:n=l.child.stateNode}try{kg(e,n)}catch(p){qe(l,l.return,p)}}break;case 27:n===null&&r&4&&rh(l);case 26:case 5:Wn(e,l),n===null&&r&4&&lh(l),r&512&&jr(l,l.return);break;case 12:Wn(e,l);break;case 13:Wn(e,l),r&4&&ch(e,l),r&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=p1.bind(null,l),H1(e,l))));break;case 22:if(r=l.memoizedState!==null||Nn,!r){n=n!==null&&n.memoizedState!==null||et,s=Nn;var f=et;Nn=r,(et=n)&&!f?Jn(e,l,(l.subtreeFlags&8772)!==0):Wn(e,l),Nn=s,et=f}break;case 30:break;default:Wn(e,l)}}function sh(e){var n=e.alternate;n!==null&&(e.alternate=null,sh(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&Ms(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ie=null,Dt=!1;function zn(e,n,l){for(l=l.child;l!==null;)uh(e,n,l),l=l.sibling}function uh(e,n,l){if(gt&&typeof gt.onCommitFiberUnmount=="function")try{gt.onCommitFiberUnmount(Rt,l)}catch{}switch(l.tag){case 26:et||mn(l,n),zn(e,n,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:et||mn(l,n);var r=Ie,s=Dt;rl(l.type)&&(Ie=l.stateNode,Dt=!1),zn(e,n,l),Fr(l.stateNode),Ie=r,Dt=s;break;case 5:et||mn(l,n);case 6:if(r=Ie,s=Dt,Ie=null,zn(e,n,l),Ie=r,Dt=s,Ie!==null)if(Dt)try{(Ie.nodeType===9?Ie.body:Ie.nodeName==="HTML"?Ie.ownerDocument.body:Ie).removeChild(l.stateNode)}catch(f){qe(l,n,f)}else try{Ie.removeChild(l.stateNode)}catch(f){qe(l,n,f)}break;case 18:Ie!==null&&(Dt?(e=Ie,Qh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),ei(e)):Qh(Ie,l.stateNode));break;case 4:r=Ie,s=Dt,Ie=l.stateNode.containerInfo,Dt=!0,zn(e,n,l),Ie=r,Dt=s;break;case 0:case 11:case 14:case 15:et||Qn(2,l,n),et||Qn(4,l,n),zn(e,n,l);break;case 1:et||(mn(l,n),r=l.stateNode,typeof r.componentWillUnmount=="function"&&nh(l,n,r)),zn(e,n,l);break;case 21:zn(e,n,l);break;case 22:et=(r=et)||l.memoizedState!==null,zn(e,n,l),et=r;break;default:zn(e,n,l)}}function ch(e,n){if(n.memoizedState===null&&(e=n.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ei(e)}catch(l){qe(n,n.return,l)}}function s1(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return n===null&&(n=e.stateNode=new ih),n;case 22:return e=e.stateNode,n=e._retryCache,n===null&&(n=e._retryCache=new ih),n;default:throw Error(o(435,e.tag))}}function Qu(e,n){var l=s1(e);n.forEach(function(r){var s=v1.bind(null,e,r);l.has(r)||(l.add(r),r.then(s,s))})}function Ut(e,n){var l=n.deletions;if(l!==null)for(var r=0;r<l.length;r++){var s=l[r],f=e,p=n,y=p;e:for(;y!==null;){switch(y.tag){case 27:if(rl(y.type)){Ie=y.stateNode,Dt=!1;break e}break;case 5:Ie=y.stateNode,Dt=!1;break e;case 3:case 4:Ie=y.stateNode.containerInfo,Dt=!0;break e}y=y.return}if(Ie===null)throw Error(o(160));uh(f,p,s),Ie=null,Dt=!1,f=s.alternate,f!==null&&(f.return=null),s.return=null}if(n.subtreeFlags&13878)for(n=n.child;n!==null;)fh(n,e),n=n.sibling}var tn=null;function fh(e,n){var l=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ut(n,e),jt(e),r&4&&(Qn(3,e,e.return),Ur(3,e),Qn(5,e,e.return));break;case 1:Ut(n,e),jt(e),r&512&&(et||l===null||mn(l,l.return)),r&64&&Nn&&(e=e.updateQueue,e!==null&&(r=e.callbacks,r!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?r:l.concat(r))));break;case 26:var s=tn;if(Ut(n,e),jt(e),r&512&&(et||l===null||mn(l,l.return)),r&4){var f=l!==null?l.memoizedState:null;if(r=e.memoizedState,l===null)if(r===null)if(e.stateNode===null){e:{r=e.type,l=e.memoizedProps,s=s.ownerDocument||s;t:switch(r){case"title":f=s.getElementsByTagName("title")[0],(!f||f[or]||f[St]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=s.createElement(r),s.head.insertBefore(f,s.querySelector("head > title"))),vt(f,r,l),f[St]=e,ut(f),r=f;break e;case"link":var p=rp("link","href",s).get(r+(l.href||""));if(p){for(var y=0;y<p.length;y++)if(f=p[y],f.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&f.getAttribute("rel")===(l.rel==null?null:l.rel)&&f.getAttribute("title")===(l.title==null?null:l.title)&&f.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){p.splice(y,1);break t}}f=s.createElement(r),vt(f,r,l),s.head.appendChild(f);break;case"meta":if(p=rp("meta","content",s).get(r+(l.content||""))){for(y=0;y<p.length;y++)if(f=p[y],f.getAttribute("content")===(l.content==null?null:""+l.content)&&f.getAttribute("name")===(l.name==null?null:l.name)&&f.getAttribute("property")===(l.property==null?null:l.property)&&f.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&f.getAttribute("charset")===(l.charSet==null?null:l.charSet)){p.splice(y,1);break t}}f=s.createElement(r),vt(f,r,l),s.head.appendChild(f);break;default:throw Error(o(468,r))}f[St]=e,ut(f),r=f}e.stateNode=r}else ip(s,e.type,e.stateNode);else e.stateNode=ap(s,r,e.memoizedProps);else f!==r?(f===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):f.count--,r===null?ip(s,e.type,e.stateNode):ap(s,r,e.memoizedProps)):r===null&&e.stateNode!==null&&Xu(e,e.memoizedProps,l.memoizedProps)}break;case 27:Ut(n,e),jt(e),r&512&&(et||l===null||mn(l,l.return)),l!==null&&r&4&&Xu(e,e.memoizedProps,l.memoizedProps);break;case 5:if(Ut(n,e),jt(e),r&512&&(et||l===null||mn(l,l.return)),e.flags&32){s=e.stateNode;try{ra(s,"")}catch(G){qe(e,e.return,G)}}r&4&&e.stateNode!=null&&(s=e.memoizedProps,Xu(e,s,l!==null?l.memoizedProps:s)),r&1024&&(Zu=!0);break;case 6:if(Ut(n,e),jt(e),r&4){if(e.stateNode===null)throw Error(o(162));r=e.memoizedProps,l=e.stateNode;try{l.nodeValue=r}catch(G){qe(e,e.return,G)}}break;case 3:if(Eo=null,s=tn,tn=Co(n.containerInfo),Ut(n,e),tn=s,jt(e),r&4&&l!==null&&l.memoizedState.isDehydrated)try{ei(n.containerInfo)}catch(G){qe(e,e.return,G)}Zu&&(Zu=!1,dh(e));break;case 4:r=tn,tn=Co(e.stateNode.containerInfo),Ut(n,e),jt(e),tn=r;break;case 12:Ut(n,e),jt(e);break;case 13:Ut(n,e),jt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(lc=Ge()),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,Qu(e,r)));break;case 22:s=e.memoizedState!==null;var T=l!==null&&l.memoizedState!==null,U=Nn,Y=et;if(Nn=U||s,et=Y||T,Ut(n,e),et=Y,Nn=U,jt(e),r&8192)e:for(n=e.stateNode,n._visibility=s?n._visibility&-2:n._visibility|1,s&&(l===null||T||Nn||et||Bl(e)),l=null,n=e;;){if(n.tag===5||n.tag===26){if(l===null){T=l=n;try{if(f=T.stateNode,s)p=f.style,typeof p.setProperty=="function"?p.setProperty("display","none","important"):p.display="none";else{y=T.stateNode;var X=T.memoizedProps.style,V=X!=null&&X.hasOwnProperty("display")?X.display:null;y.style.display=V==null||typeof V=="boolean"?"":(""+V).trim()}}catch(G){qe(T,T.return,G)}}}else if(n.tag===6){if(l===null){T=n;try{T.stateNode.nodeValue=s?"":T.memoizedProps}catch(G){qe(T,T.return,G)}}}else if((n.tag!==22&&n.tag!==23||n.memoizedState===null||n===e)&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;n.sibling===null;){if(n.return===null||n.return===e)break e;l===n&&(l=null),n=n.return}l===n&&(l=null),n.sibling.return=n.return,n=n.sibling}r&4&&(r=e.updateQueue,r!==null&&(l=r.retryQueue,l!==null&&(r.retryQueue=null,Qu(e,l))));break;case 19:Ut(n,e),jt(e),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,Qu(e,r)));break;case 30:break;case 21:break;default:Ut(n,e),jt(e)}}function jt(e){var n=e.flags;if(n&2){try{for(var l,r=e.return;r!==null;){if(ah(r)){l=r;break}r=r.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var s=l.stateNode,f=Iu(e);uo(e,f,s);break;case 5:var p=l.stateNode;l.flags&32&&(ra(p,""),l.flags&=-33);var y=Iu(e);uo(e,y,p);break;case 3:case 4:var T=l.stateNode.containerInfo,U=Iu(e);Ku(e,U,T);break;default:throw Error(o(161))}}catch(Y){qe(e,e.return,Y)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function dh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var n=e;dh(n),n.tag===5&&n.flags&1024&&n.stateNode.reset(),e=e.sibling}}function Wn(e,n){if(n.subtreeFlags&8772)for(n=n.child;n!==null;)oh(e,n.alternate,n),n=n.sibling}function Bl(e){for(e=e.child;e!==null;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:Qn(4,n,n.return),Bl(n);break;case 1:mn(n,n.return);var l=n.stateNode;typeof l.componentWillUnmount=="function"&&nh(n,n.return,l),Bl(n);break;case 27:Fr(n.stateNode);case 26:case 5:mn(n,n.return),Bl(n);break;case 22:n.memoizedState===null&&Bl(n);break;case 30:Bl(n);break;default:Bl(n)}e=e.sibling}}function Jn(e,n,l){for(l=l&&(n.subtreeFlags&8772)!==0,n=n.child;n!==null;){var r=n.alternate,s=e,f=n,p=f.flags;switch(f.tag){case 0:case 11:case 15:Jn(s,f,l),Ur(4,f);break;case 1:if(Jn(s,f,l),r=f,s=r.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(U){qe(r,r.return,U)}if(r=f,s=r.updateQueue,s!==null){var y=r.stateNode;try{var T=s.shared.hiddenCallbacks;if(T!==null)for(s.shared.hiddenCallbacks=null,s=0;s<T.length;s++)Pg(T[s],y)}catch(U){qe(r,r.return,U)}}l&&p&64&&th(f),jr(f,f.return);break;case 27:rh(f);case 26:case 5:Jn(s,f,l),l&&r===null&&p&4&&lh(f),jr(f,f.return);break;case 12:Jn(s,f,l);break;case 13:Jn(s,f,l),l&&p&4&&ch(s,f);break;case 22:f.memoizedState===null&&Jn(s,f,l),jr(f,f.return);break;case 30:break;default:Jn(s,f,l)}n=n.sibling}}function Wu(e,n){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(e=n.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&wr(l))}function Ju(e,n){e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&wr(e))}function hn(e,n,l,r){if(n.subtreeFlags&10256)for(n=n.child;n!==null;)gh(e,n,l,r),n=n.sibling}function gh(e,n,l,r){var s=n.flags;switch(n.tag){case 0:case 11:case 15:hn(e,n,l,r),s&2048&&Ur(9,n);break;case 1:hn(e,n,l,r);break;case 3:hn(e,n,l,r),s&2048&&(e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&wr(e)));break;case 12:if(s&2048){hn(e,n,l,r),e=n.stateNode;try{var f=n.memoizedProps,p=f.id,y=f.onPostCommit;typeof y=="function"&&y(p,n.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(T){qe(n,n.return,T)}}else hn(e,n,l,r);break;case 13:hn(e,n,l,r);break;case 23:break;case 22:f=n.stateNode,p=n.alternate,n.memoizedState!==null?f._visibility&2?hn(e,n,l,r):Vr(e,n):f._visibility&2?hn(e,n,l,r):(f._visibility|=2,Ra(e,n,l,r,(n.subtreeFlags&10256)!==0)),s&2048&&Wu(p,n);break;case 24:hn(e,n,l,r),s&2048&&Ju(n.alternate,n);break;default:hn(e,n,l,r)}}function Ra(e,n,l,r,s){for(s=s&&(n.subtreeFlags&10256)!==0,n=n.child;n!==null;){var f=e,p=n,y=l,T=r,U=p.flags;switch(p.tag){case 0:case 11:case 15:Ra(f,p,y,T,s),Ur(8,p);break;case 23:break;case 22:var Y=p.stateNode;p.memoizedState!==null?Y._visibility&2?Ra(f,p,y,T,s):Vr(f,p):(Y._visibility|=2,Ra(f,p,y,T,s)),s&&U&2048&&Wu(p.alternate,p);break;case 24:Ra(f,p,y,T,s),s&&U&2048&&Ju(p.alternate,p);break;default:Ra(f,p,y,T,s)}n=n.sibling}}function Vr(e,n){if(n.subtreeFlags&10256)for(n=n.child;n!==null;){var l=e,r=n,s=r.flags;switch(r.tag){case 22:Vr(l,r),s&2048&&Wu(r.alternate,r);break;case 24:Vr(l,r),s&2048&&Ju(r.alternate,r);break;default:Vr(l,r)}n=n.sibling}}var Br=8192;function Ea(e){if(e.subtreeFlags&Br)for(e=e.child;e!==null;)mh(e),e=e.sibling}function mh(e){switch(e.tag){case 26:Ea(e),e.flags&Br&&e.memoizedState!==null&&X1(tn,e.memoizedState,e.memoizedProps);break;case 5:Ea(e);break;case 3:case 4:var n=tn;tn=Co(e.stateNode.containerInfo),Ea(e),tn=n;break;case 22:e.memoizedState===null&&(n=e.alternate,n!==null&&n.memoizedState!==null?(n=Br,Br=16777216,Ea(e),Br=n):Ea(e));break;default:Ea(e)}}function hh(e){var n=e.alternate;if(n!==null&&(e=n.child,e!==null)){n.child=null;do n=e.sibling,e.sibling=null,e=n;while(e!==null)}}function Gr(e){var n=e.deletions;if((e.flags&16)!==0){if(n!==null)for(var l=0;l<n.length;l++){var r=n[l];ft=r,vh(r,e)}hh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ph(e),e=e.sibling}function ph(e){switch(e.tag){case 0:case 11:case 15:Gr(e),e.flags&2048&&Qn(9,e,e.return);break;case 3:Gr(e);break;case 12:Gr(e);break;case 22:var n=e.stateNode;e.memoizedState!==null&&n._visibility&2&&(e.return===null||e.return.tag!==13)?(n._visibility&=-3,co(e)):Gr(e);break;default:Gr(e)}}function co(e){var n=e.deletions;if((e.flags&16)!==0){if(n!==null)for(var l=0;l<n.length;l++){var r=n[l];ft=r,vh(r,e)}hh(e)}for(e=e.child;e!==null;){switch(n=e,n.tag){case 0:case 11:case 15:Qn(8,n,n.return),co(n);break;case 22:l=n.stateNode,l._visibility&2&&(l._visibility&=-3,co(n));break;default:co(n)}e=e.sibling}}function vh(e,n){for(;ft!==null;){var l=ft;switch(l.tag){case 0:case 11:case 15:Qn(8,l,n);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var r=l.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:wr(l.memoizedState.cache)}if(r=l.child,r!==null)r.return=l,ft=r;else e:for(l=e;ft!==null;){r=ft;var s=r.sibling,f=r.return;if(sh(r),r===l){ft=null;break e}if(s!==null){s.return=f,ft=s;break e}ft=f}}}var u1={getCacheForType:function(e){var n=bt(rt),l=n.data.get(e);return l===void 0&&(l=e(),n.data.set(e,l)),l}},c1=typeof WeakMap=="function"?WeakMap:Map,je=0,Ye=null,Me=null,De=0,Ve=0,Vt=null,el=!1,_a=!1,ec=!1,Hn=0,Qe=0,tl=0,Gl=0,tc=0,Qt=0,Ta=0,Pr=null,Nt=null,nc=!1,lc=0,fo=1/0,go=null,nl=null,pt=0,ll=null,Aa=null,Ma=0,ac=0,rc=null,yh=null,kr=0,ic=null;function Bt(){if((je&2)!==0&&De!==0)return De&-De;if(H.T!==null){var e=pa;return e!==0?e:gc()}return Hd()}function Sh(){Qt===0&&(Qt=(De&536870912)===0||Le?Od():536870912);var e=Zt.current;return e!==null&&(e.flags|=32),Qt}function Gt(e,n,l){(e===Ye&&(Ve===2||Ve===9)||e.cancelPendingCommit!==null)&&(Oa(e,0),al(e,De,Qt,!1)),ir(e,l),((je&2)===0||e!==Ye)&&(e===Ye&&((je&2)===0&&(Gl|=l),Qe===4&&al(e,De,Qt,!1)),pn(e))}function bh(e,n,l){if((je&6)!==0)throw Error(o(327));var r=!l&&(n&124)===0&&(n&e.expiredLanes)===0||rr(e,n),s=r?g1(e,n):uc(e,n,!0),f=r;do{if(s===0){_a&&!r&&al(e,n,0,!1);break}else{if(l=e.current.alternate,f&&!f1(l)){s=uc(e,n,!1),f=!1;continue}if(s===2){if(f=n,e.errorRecoveryDisabledLanes&f)var p=0;else p=e.pendingLanes&-536870913,p=p!==0?p:p&536870912?536870912:0;if(p!==0){n=p;e:{var y=e;s=Pr;var T=y.current.memoizedState.isDehydrated;if(T&&(Oa(y,p).flags|=256),p=uc(y,p,!1),p!==2){if(ec&&!T){y.errorRecoveryDisabledLanes|=f,Gl|=f,s=4;break e}f=Nt,Nt=s,f!==null&&(Nt===null?Nt=f:Nt.push.apply(Nt,f))}s=p}if(f=!1,s!==2)continue}}if(s===1){Oa(e,0),al(e,n,0,!0);break}e:{switch(r=e,f=s,f){case 0:case 1:throw Error(o(345));case 4:if((n&4194048)!==n)break;case 6:al(r,n,Qt,!el);break e;case 2:Nt=null;break;case 3:case 5:break;default:throw Error(o(329))}if((n&62914560)===n&&(s=lc+300-Ge(),10<s)){if(al(r,n,Qt,!el),Ci(r,0,!0)!==0)break e;r.timeoutHandle=Kh(xh.bind(null,r,l,Nt,go,nc,n,Qt,Gl,Ta,el,f,2,-0,0),s);break e}xh(r,l,Nt,go,nc,n,Qt,Gl,Ta,el,f,0,-0,0)}}break}while(!0);pn(e)}function xh(e,n,l,r,s,f,p,y,T,U,Y,X,V,G){if(e.timeoutHandle=-1,X=n.subtreeFlags,(X&8192||(X&16785408)===16785408)&&(Kr={stylesheets:null,count:0,unsuspend:F1},mh(n),X=I1(),X!==null)){e.cancelPendingCommit=X(Ah.bind(null,e,n,f,l,r,s,p,y,T,Y,1,V,G)),al(e,f,p,!U);return}Ah(e,n,f,l,r,s,p,y,T)}function f1(e){for(var n=e;;){var l=n.tag;if((l===0||l===11||l===15)&&n.flags&16384&&(l=n.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var r=0;r<l.length;r++){var s=l[r],f=s.getSnapshot;s=s.value;try{if(!Ht(f(),s))return!1}catch{return!1}}if(l=n.child,n.subtreeFlags&16384&&l!==null)l.return=n,n=l;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function al(e,n,l,r){n&=~tc,n&=~Gl,e.suspendedLanes|=n,e.pingedLanes&=~n,r&&(e.warmLanes|=n),r=e.expirationTimes;for(var s=n;0<s;){var f=31-mt(s),p=1<<f;r[f]=-1,s&=~p}l!==0&&Nd(e,l,n)}function mo(){return(je&6)===0?($r(0),!1):!0}function oc(){if(Me!==null){if(Ve===0)var e=Me.return;else e=Me,_n=Hl=null,Ru(e),wa=null,zr=0,e=Me;for(;e!==null;)eh(e.alternate,e),e=e.return;Me=null}}function Oa(e,n){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,M1(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),oc(),Ye=e,Me=l=Cn(e.current,null),De=n,Ve=0,Vt=null,el=!1,_a=rr(e,n),ec=!1,Ta=Qt=tc=Gl=tl=Qe=0,Nt=Pr=null,nc=!1,(n&8)!==0&&(n|=n&32);var r=e.entangledLanes;if(r!==0)for(e=e.entanglements,r&=n;0<r;){var s=31-mt(r),f=1<<s;n|=e[s],r&=~f}return Hn=n,Li(),l}function wh(e,n){Ee=null,H.H=eo,n===Rr||n===qi?(n=Bg(),Ve=3):n===Ug?(n=Bg(),Ve=4):Ve=n===Gm?8:n!==null&&typeof n=="object"&&typeof n.then=="function"?6:1,Vt=n,Me===null&&(Qe=1,ro(e,Ft(n,e.current)))}function Ch(){var e=H.H;return H.H=eo,e===null?eo:e}function Rh(){var e=H.A;return H.A=u1,e}function sc(){Qe=4,el||(De&4194048)!==De&&Zt.current!==null||(_a=!0),(tl&134217727)===0&&(Gl&134217727)===0||Ye===null||al(Ye,De,Qt,!1)}function uc(e,n,l){var r=je;je|=2;var s=Ch(),f=Rh();(Ye!==e||De!==n)&&(go=null,Oa(e,n)),n=!1;var p=Qe;e:do try{if(Ve!==0&&Me!==null){var y=Me,T=Vt;switch(Ve){case 8:oc(),p=6;break e;case 3:case 2:case 9:case 6:Zt.current===null&&(n=!0);var U=Ve;if(Ve=0,Vt=null,Da(e,y,T,U),l&&_a){p=0;break e}break;default:U=Ve,Ve=0,Vt=null,Da(e,y,T,U)}}d1(),p=Qe;break}catch(Y){wh(e,Y)}while(!0);return n&&e.shellSuspendCounter++,_n=Hl=null,je=r,H.H=s,H.A=f,Me===null&&(Ye=null,De=0,Li()),p}function d1(){for(;Me!==null;)Eh(Me)}function g1(e,n){var l=je;je|=2;var r=Ch(),s=Rh();Ye!==e||De!==n?(go=null,fo=Ge()+500,Oa(e,n)):_a=rr(e,n);e:do try{if(Ve!==0&&Me!==null){n=Me;var f=Vt;t:switch(Ve){case 1:Ve=0,Vt=null,Da(e,n,f,1);break;case 2:case 9:if(jg(f)){Ve=0,Vt=null,_h(n);break}n=function(){Ve!==2&&Ve!==9||Ye!==e||(Ve=7),pn(e)},f.then(n,n);break e;case 3:Ve=7;break e;case 4:Ve=5;break e;case 7:jg(f)?(Ve=0,Vt=null,_h(n)):(Ve=0,Vt=null,Da(e,n,f,7));break;case 5:var p=null;switch(Me.tag){case 26:p=Me.memoizedState;case 5:case 27:var y=Me;if(!p||op(p)){Ve=0,Vt=null;var T=y.sibling;if(T!==null)Me=T;else{var U=y.return;U!==null?(Me=U,ho(U)):Me=null}break t}}Ve=0,Vt=null,Da(e,n,f,5);break;case 6:Ve=0,Vt=null,Da(e,n,f,6);break;case 8:oc(),Qe=6;break e;default:throw Error(o(462))}}m1();break}catch(Y){wh(e,Y)}while(!0);return _n=Hl=null,H.H=r,H.A=s,je=l,Me!==null?0:(Ye=null,De=0,Li(),Qe)}function m1(){for(;Me!==null&&!Xe();)Eh(Me)}function Eh(e){var n=Wm(e.alternate,e,Hn);e.memoizedProps=e.pendingProps,n===null?ho(e):Me=n}function _h(e){var n=e,l=n.alternate;switch(n.tag){case 15:case 0:n=Fm(l,n,n.pendingProps,n.type,void 0,De);break;case 11:n=Fm(l,n,n.pendingProps,n.type.render,n.ref,De);break;case 5:Ru(n);default:eh(l,n),n=Me=Tg(n,Hn),n=Wm(l,n,Hn)}e.memoizedProps=e.pendingProps,n===null?ho(e):Me=n}function Da(e,n,l,r){_n=Hl=null,Ru(n),wa=null,zr=0;var s=n.return;try{if(l1(e,s,n,l,De)){Qe=1,ro(e,Ft(l,e.current)),Me=null;return}}catch(f){if(s!==null)throw Me=s,f;Qe=1,ro(e,Ft(l,e.current)),Me=null;return}n.flags&32768?(Le||r===1?e=!0:_a||(De&536870912)!==0?e=!1:(el=e=!0,(r===2||r===9||r===3||r===6)&&(r=Zt.current,r!==null&&r.tag===13&&(r.flags|=16384))),Th(n,e)):ho(n)}function ho(e){var n=e;do{if((n.flags&32768)!==0){Th(n,el);return}e=n.return;var l=r1(n.alternate,n,Hn);if(l!==null){Me=l;return}if(n=n.sibling,n!==null){Me=n;return}Me=n=e}while(n!==null);Qe===0&&(Qe=5)}function Th(e,n){do{var l=i1(e.alternate,e);if(l!==null){l.flags&=32767,Me=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!n&&(e=e.sibling,e!==null)){Me=e;return}Me=e=l}while(e!==null);Qe=6,Me=null}function Ah(e,n,l,r,s,f,p,y,T){e.cancelPendingCommit=null;do po();while(pt!==0);if((je&6)!==0)throw Error(o(327));if(n!==null){if(n===e.current)throw Error(o(177));if(f=n.lanes|n.childLanes,f|=Js,Fy(e,l,f,p,y,T),e===Ye&&(Me=Ye=null,De=0),Aa=n,ll=e,Ma=l,ac=f,rc=s,yh=r,(n.subtreeFlags&10256)!==0||(n.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,y1(_t,function(){return zh(),null})):(e.callbackNode=null,e.callbackPriority=0),r=(n.flags&13878)!==0,(n.subtreeFlags&13878)!==0||r){r=H.T,H.T=null,s=B.p,B.p=2,p=je,je|=4;try{o1(e,n,l)}finally{je=p,B.p=s,H.T=r}}pt=1,Mh(),Oh(),Dh()}}function Mh(){if(pt===1){pt=0;var e=ll,n=Aa,l=(n.flags&13878)!==0;if((n.subtreeFlags&13878)!==0||l){l=H.T,H.T=null;var r=B.p;B.p=2;var s=je;je|=4;try{fh(n,e);var f=xc,p=vg(e.containerInfo),y=f.focusedElem,T=f.selectionRange;if(p!==y&&y&&y.ownerDocument&&pg(y.ownerDocument.documentElement,y)){if(T!==null&&Is(y)){var U=T.start,Y=T.end;if(Y===void 0&&(Y=U),"selectionStart"in y)y.selectionStart=U,y.selectionEnd=Math.min(Y,y.value.length);else{var X=y.ownerDocument||document,V=X&&X.defaultView||window;if(V.getSelection){var G=V.getSelection(),ve=y.textContent.length,ge=Math.min(T.start,ve),$e=T.end===void 0?ge:Math.min(T.end,ve);!G.extend&&ge>$e&&(p=$e,$e=ge,ge=p);var N=hg(y,ge),O=hg(y,$e);if(N&&O&&(G.rangeCount!==1||G.anchorNode!==N.node||G.anchorOffset!==N.offset||G.focusNode!==O.node||G.focusOffset!==O.offset)){var L=X.createRange();L.setStart(N.node,N.offset),G.removeAllRanges(),ge>$e?(G.addRange(L),G.extend(O.node,O.offset)):(L.setEnd(O.node,O.offset),G.addRange(L))}}}}for(X=[],G=y;G=G.parentNode;)G.nodeType===1&&X.push({element:G,left:G.scrollLeft,top:G.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<X.length;y++){var F=X[y];F.element.scrollLeft=F.left,F.element.scrollTop=F.top}}Ao=!!bc,xc=bc=null}finally{je=s,B.p=r,H.T=l}}e.current=n,pt=2}}function Oh(){if(pt===2){pt=0;var e=ll,n=Aa,l=(n.flags&8772)!==0;if((n.subtreeFlags&8772)!==0||l){l=H.T,H.T=null;var r=B.p;B.p=2;var s=je;je|=4;try{oh(e,n.alternate,n)}finally{je=s,B.p=r,H.T=l}}pt=3}}function Dh(){if(pt===4||pt===3){pt=0,Ue();var e=ll,n=Aa,l=Ma,r=yh;(n.subtreeFlags&10256)!==0||(n.flags&10256)!==0?pt=5:(pt=0,Aa=ll=null,Nh(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(nl=null),Ts(l),n=n.stateNode,gt&&typeof gt.onCommitFiberRoot=="function")try{gt.onCommitFiberRoot(Rt,n,void 0,(n.current.flags&128)===128)}catch{}if(r!==null){n=H.T,s=B.p,B.p=2,H.T=null;try{for(var f=e.onRecoverableError,p=0;p<r.length;p++){var y=r[p];f(y.value,{componentStack:y.stack})}}finally{H.T=n,B.p=s}}(Ma&3)!==0&&po(),pn(e),s=e.pendingLanes,(l&4194090)!==0&&(s&42)!==0?e===ic?kr++:(kr=0,ic=e):kr=0,$r(0)}}function Nh(e,n){(e.pooledCacheLanes&=n)===0&&(n=e.pooledCache,n!=null&&(e.pooledCache=null,wr(n)))}function po(e){return Mh(),Oh(),Dh(),zh()}function zh(){if(pt!==5)return!1;var e=ll,n=ac;ac=0;var l=Ts(Ma),r=H.T,s=B.p;try{B.p=32>l?32:l,H.T=null,l=rc,rc=null;var f=ll,p=Ma;if(pt=0,Aa=ll=null,Ma=0,(je&6)!==0)throw Error(o(331));var y=je;if(je|=4,ph(f.current),gh(f,f.current,p,l),je=y,$r(0,!1),gt&&typeof gt.onPostCommitFiberRoot=="function")try{gt.onPostCommitFiberRoot(Rt,f)}catch{}return!0}finally{B.p=s,H.T=r,Nh(e,n)}}function Hh(e,n,l){n=Ft(l,n),n=Vu(e.stateNode,n,2),e=Xn(e,n,2),e!==null&&(ir(e,2),pn(e))}function qe(e,n,l){if(e.tag===3)Hh(e,e,l);else for(;n!==null;){if(n.tag===3){Hh(n,e,l);break}else if(n.tag===1){var r=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(nl===null||!nl.has(r))){e=Ft(l,e),l=Vm(2),r=Xn(n,l,2),r!==null&&(Bm(l,r,n,e),ir(r,2),pn(r));break}}n=n.return}}function cc(e,n,l){var r=e.pingCache;if(r===null){r=e.pingCache=new c1;var s=new Set;r.set(n,s)}else s=r.get(n),s===void 0&&(s=new Set,r.set(n,s));s.has(l)||(ec=!0,s.add(l),e=h1.bind(null,e,n,l),n.then(e,e))}function h1(e,n,l){var r=e.pingCache;r!==null&&r.delete(n),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Ye===e&&(De&l)===l&&(Qe===4||Qe===3&&(De&62914560)===De&&300>Ge()-lc?(je&2)===0&&Oa(e,0):tc|=l,Ta===De&&(Ta=0)),pn(e)}function Lh(e,n){n===0&&(n=Dd()),e=da(e,n),e!==null&&(ir(e,n),pn(e))}function p1(e){var n=e.memoizedState,l=0;n!==null&&(l=n.retryLane),Lh(e,l)}function v1(e,n){var l=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(l=s.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}r!==null&&r.delete(n),Lh(e,l)}function y1(e,n){return be(e,n)}var vo=null,Na=null,fc=!1,yo=!1,dc=!1,Pl=0;function pn(e){e!==Na&&e.next===null&&(Na===null?vo=Na=e:Na=Na.next=e),yo=!0,fc||(fc=!0,b1())}function $r(e,n){if(!dc&&yo){dc=!0;do for(var l=!1,r=vo;r!==null;){if(e!==0){var s=r.pendingLanes;if(s===0)var f=0;else{var p=r.suspendedLanes,y=r.pingedLanes;f=(1<<31-mt(42|e)+1)-1,f&=s&~(p&~y),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(l=!0,Bh(r,f))}else f=De,f=Ci(r,r===Ye?f:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(f&3)===0||rr(r,f)||(l=!0,Bh(r,f));r=r.next}while(l);dc=!1}}function S1(){Uh()}function Uh(){yo=fc=!1;var e=0;Pl!==0&&(A1()&&(e=Pl),Pl=0);for(var n=Ge(),l=null,r=vo;r!==null;){var s=r.next,f=jh(r,n);f===0?(r.next=null,l===null?vo=s:l.next=s,s===null&&(Na=l)):(l=r,(e!==0||(f&3)!==0)&&(yo=!0)),r=s}$r(e)}function jh(e,n){for(var l=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,f=e.pendingLanes&-62914561;0<f;){var p=31-mt(f),y=1<<p,T=s[p];T===-1?((y&l)===0||(y&r)!==0)&&(s[p]=Yy(y,n)):T<=n&&(e.expiredLanes|=y),f&=~y}if(n=Ye,l=De,l=Ci(e,e===n?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r=e.callbackNode,l===0||e===n&&(Ve===2||Ve===9)||e.cancelPendingCommit!==null)return r!==null&&r!==null&&we(r),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||rr(e,l)){if(n=l&-l,n===e.callbackPriority)return n;switch(r!==null&&we(r),Ts(l)){case 2:case 8:l=st;break;case 32:l=_t;break;case 268435456:l=ar;break;default:l=_t}return r=Vh.bind(null,e),l=be(l,r),e.callbackPriority=n,e.callbackNode=l,n}return r!==null&&r!==null&&we(r),e.callbackPriority=2,e.callbackNode=null,2}function Vh(e,n){if(pt!==0&&pt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(po()&&e.callbackNode!==l)return null;var r=De;return r=Ci(e,e===Ye?r:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r===0?null:(bh(e,r,n),jh(e,Ge()),e.callbackNode!=null&&e.callbackNode===l?Vh.bind(null,e):null)}function Bh(e,n){if(po())return null;bh(e,n,!0)}function b1(){O1(function(){(je&6)!==0?be(Fe,S1):Uh()})}function gc(){return Pl===0&&(Pl=Od()),Pl}function Gh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ai(""+e)}function Ph(e,n){var l=n.ownerDocument.createElement("input");return l.name=n.name,l.value=n.value,e.id&&l.setAttribute("form",e.id),n.parentNode.insertBefore(l,n),e=new FormData(e),l.parentNode.removeChild(l),e}function x1(e,n,l,r,s){if(n==="submit"&&l&&l.stateNode===s){var f=Gh((s[At]||null).action),p=r.submitter;p&&(n=(n=p[At]||null)?Gh(n.formAction):p.getAttribute("formAction"),n!==null&&(f=n,p=null));var y=new Ni("action","action",null,r,s);e.push({event:y,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(Pl!==0){var T=p?Ph(s,p):new FormData(s);zu(l,{pending:!0,data:T,method:s.method,action:f},null,T)}}else typeof f=="function"&&(y.preventDefault(),T=p?Ph(s,p):new FormData(s),zu(l,{pending:!0,data:T,method:s.method,action:f},f,T))},currentTarget:s}]})}}for(var mc=0;mc<Ws.length;mc++){var hc=Ws[mc],w1=hc.toLowerCase(),C1=hc[0].toUpperCase()+hc.slice(1);en(w1,"on"+C1)}en(bg,"onAnimationEnd"),en(xg,"onAnimationIteration"),en(wg,"onAnimationStart"),en("dblclick","onDoubleClick"),en("focusin","onFocus"),en("focusout","onBlur"),en(GS,"onTransitionRun"),en(PS,"onTransitionStart"),en(kS,"onTransitionCancel"),en(Cg,"onTransitionEnd"),na("onMouseEnter",["mouseout","mouseover"]),na("onMouseLeave",["mouseout","mouseover"]),na("onPointerEnter",["pointerout","pointerover"]),na("onPointerLeave",["pointerout","pointerover"]),El("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),El("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),El("onBeforeInput",["compositionend","keypress","textInput","paste"]),El("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),El("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),El("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var qr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),R1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(qr));function kh(e,n){n=(n&4)!==0;for(var l=0;l<e.length;l++){var r=e[l],s=r.event;r=r.listeners;e:{var f=void 0;if(n)for(var p=r.length-1;0<=p;p--){var y=r[p],T=y.instance,U=y.currentTarget;if(y=y.listener,T!==f&&s.isPropagationStopped())break e;f=y,s.currentTarget=U;try{f(s)}catch(Y){ao(Y)}s.currentTarget=null,f=T}else for(p=0;p<r.length;p++){if(y=r[p],T=y.instance,U=y.currentTarget,y=y.listener,T!==f&&s.isPropagationStopped())break e;f=y,s.currentTarget=U;try{f(s)}catch(Y){ao(Y)}s.currentTarget=null,f=T}}}}function Oe(e,n){var l=n[As];l===void 0&&(l=n[As]=new Set);var r=e+"__bubble";l.has(r)||($h(n,e,2,!1),l.add(r))}function pc(e,n,l){var r=0;n&&(r|=4),$h(l,e,r,n)}var So="_reactListening"+Math.random().toString(36).slice(2);function vc(e){if(!e[So]){e[So]=!0,Ud.forEach(function(l){l!=="selectionchange"&&(R1.has(l)||pc(l,!1,e),pc(l,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[So]||(n[So]=!0,pc("selectionchange",!1,n))}}function $h(e,n,l,r){switch(gp(n)){case 2:var s=Q1;break;case 8:s=W1;break;default:s=Dc}l=s.bind(null,n,l,e),s=void 0,!Bs||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(n,l,{capture:!0,passive:s}):e.addEventListener(n,l,!0):s!==void 0?e.addEventListener(n,l,{passive:s}):e.addEventListener(n,l,!1)}function yc(e,n,l,r,s){var f=r;if((n&1)===0&&(n&2)===0&&r!==null)e:for(;;){if(r===null)return;var p=r.tag;if(p===3||p===4){var y=r.stateNode.containerInfo;if(y===s)break;if(p===4)for(p=r.return;p!==null;){var T=p.tag;if((T===3||T===4)&&p.stateNode.containerInfo===s)return;p=p.return}for(;y!==null;){if(p=Jl(y),p===null)return;if(T=p.tag,T===5||T===6||T===26||T===27){r=f=p;continue e}y=y.parentNode}}r=r.return}Zd(function(){var U=f,Y=js(l),X=[];e:{var V=Rg.get(e);if(V!==void 0){var G=Ni,ve=e;switch(e){case"keypress":if(Oi(l)===0)break e;case"keydown":case"keyup":G=yS;break;case"focusin":ve="focus",G=$s;break;case"focusout":ve="blur",G=$s;break;case"beforeblur":case"afterblur":G=$s;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=Jd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=iS;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=xS;break;case bg:case xg:case wg:G=uS;break;case Cg:G=CS;break;case"scroll":case"scrollend":G=aS;break;case"wheel":G=ES;break;case"copy":case"cut":case"paste":G=fS;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=tg;break;case"toggle":case"beforetoggle":G=TS}var ge=(n&4)!==0,$e=!ge&&(e==="scroll"||e==="scrollend"),N=ge?V!==null?V+"Capture":null:V;ge=[];for(var O=U,L;O!==null;){var F=O;if(L=F.stateNode,F=F.tag,F!==5&&F!==26&&F!==27||L===null||N===null||(F=ur(O,N),F!=null&&ge.push(Yr(O,F,L))),$e)break;O=O.return}0<ge.length&&(V=new G(V,ve,null,l,Y),X.push({event:V,listeners:ge}))}}if((n&7)===0){e:{if(V=e==="mouseover"||e==="pointerover",G=e==="mouseout"||e==="pointerout",V&&l!==Us&&(ve=l.relatedTarget||l.fromElement)&&(Jl(ve)||ve[Wl]))break e;if((G||V)&&(V=Y.window===Y?Y:(V=Y.ownerDocument)?V.defaultView||V.parentWindow:window,G?(ve=l.relatedTarget||l.toElement,G=U,ve=ve?Jl(ve):null,ve!==null&&($e=c(ve),ge=ve.tag,ve!==$e||ge!==5&&ge!==27&&ge!==6)&&(ve=null)):(G=null,ve=U),G!==ve)){if(ge=Jd,F="onMouseLeave",N="onMouseEnter",O="mouse",(e==="pointerout"||e==="pointerover")&&(ge=tg,F="onPointerLeave",N="onPointerEnter",O="pointer"),$e=G==null?V:sr(G),L=ve==null?V:sr(ve),V=new ge(F,O+"leave",G,l,Y),V.target=$e,V.relatedTarget=L,F=null,Jl(Y)===U&&(ge=new ge(N,O+"enter",ve,l,Y),ge.target=L,ge.relatedTarget=$e,F=ge),$e=F,G&&ve)t:{for(ge=G,N=ve,O=0,L=ge;L;L=za(L))O++;for(L=0,F=N;F;F=za(F))L++;for(;0<O-L;)ge=za(ge),O--;for(;0<L-O;)N=za(N),L--;for(;O--;){if(ge===N||N!==null&&ge===N.alternate)break t;ge=za(ge),N=za(N)}ge=null}else ge=null;G!==null&&qh(X,V,G,ge,!1),ve!==null&&$e!==null&&qh(X,$e,ve,ge,!0)}}e:{if(V=U?sr(U):window,G=V.nodeName&&V.nodeName.toLowerCase(),G==="select"||G==="input"&&V.type==="file")var te=ug;else if(og(V))if(cg)te=jS;else{te=LS;var Ae=HS}else G=V.nodeName,!G||G.toLowerCase()!=="input"||V.type!=="checkbox"&&V.type!=="radio"?U&&Ls(U.elementType)&&(te=ug):te=US;if(te&&(te=te(e,U))){sg(X,te,l,Y);break e}Ae&&Ae(e,V,U),e==="focusout"&&U&&V.type==="number"&&U.memoizedProps.value!=null&&Hs(V,"number",V.value)}switch(Ae=U?sr(U):window,e){case"focusin":(og(Ae)||Ae.contentEditable==="true")&&(ua=Ae,Ks=U,vr=null);break;case"focusout":vr=Ks=ua=null;break;case"mousedown":Zs=!0;break;case"contextmenu":case"mouseup":case"dragend":Zs=!1,yg(X,l,Y);break;case"selectionchange":if(BS)break;case"keydown":case"keyup":yg(X,l,Y)}var ie;if(Ys)e:{switch(e){case"compositionstart":var he="onCompositionStart";break e;case"compositionend":he="onCompositionEnd";break e;case"compositionupdate":he="onCompositionUpdate";break e}he=void 0}else sa?rg(e,l)&&(he="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(he="onCompositionStart");he&&(ng&&l.locale!=="ko"&&(sa||he!=="onCompositionStart"?he==="onCompositionEnd"&&sa&&(ie=Qd()):($n=Y,Gs="value"in $n?$n.value:$n.textContent,sa=!0)),Ae=bo(U,he),0<Ae.length&&(he=new eg(he,e,null,l,Y),X.push({event:he,listeners:Ae}),ie?he.data=ie:(ie=ig(l),ie!==null&&(he.data=ie)))),(ie=MS?OS(e,l):DS(e,l))&&(he=bo(U,"onBeforeInput"),0<he.length&&(Ae=new eg("onBeforeInput","beforeinput",null,l,Y),X.push({event:Ae,listeners:he}),Ae.data=ie)),x1(X,e,U,l,Y)}kh(X,n)})}function Yr(e,n,l){return{instance:e,listener:n,currentTarget:l}}function bo(e,n){for(var l=n+"Capture",r=[];e!==null;){var s=e,f=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||f===null||(s=ur(e,l),s!=null&&r.unshift(Yr(e,s,f)),s=ur(e,n),s!=null&&r.push(Yr(e,s,f))),e.tag===3)return r;e=e.return}return[]}function za(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function qh(e,n,l,r,s){for(var f=n._reactName,p=[];l!==null&&l!==r;){var y=l,T=y.alternate,U=y.stateNode;if(y=y.tag,T!==null&&T===r)break;y!==5&&y!==26&&y!==27||U===null||(T=U,s?(U=ur(l,f),U!=null&&p.unshift(Yr(l,U,T))):s||(U=ur(l,f),U!=null&&p.push(Yr(l,U,T)))),l=l.return}p.length!==0&&e.push({event:n,listeners:p})}var E1=/\r\n?/g,_1=/\u0000|\uFFFD/g;function Yh(e){return(typeof e=="string"?e:""+e).replace(E1,`
`).replace(_1,"")}function Fh(e,n){return n=Yh(n),Yh(e)===n}function xo(){}function ke(e,n,l,r,s,f){switch(l){case"children":typeof r=="string"?n==="body"||n==="textarea"&&r===""||ra(e,r):(typeof r=="number"||typeof r=="bigint")&&n!=="body"&&ra(e,""+r);break;case"className":Ei(e,"class",r);break;case"tabIndex":Ei(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":Ei(e,l,r);break;case"style":Id(e,r,f);break;case"data":if(n!=="object"){Ei(e,"data",r);break}case"src":case"href":if(r===""&&(n!=="a"||l!=="href")){e.removeAttribute(l);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(l);break}r=Ai(""+r),e.setAttribute(l,r);break;case"action":case"formAction":if(typeof r=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(l==="formAction"?(n!=="input"&&ke(e,n,"name",s.name,s,null),ke(e,n,"formEncType",s.formEncType,s,null),ke(e,n,"formMethod",s.formMethod,s,null),ke(e,n,"formTarget",s.formTarget,s,null)):(ke(e,n,"encType",s.encType,s,null),ke(e,n,"method",s.method,s,null),ke(e,n,"target",s.target,s,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(l);break}r=Ai(""+r),e.setAttribute(l,r);break;case"onClick":r!=null&&(e.onclick=xo);break;case"onScroll":r!=null&&Oe("scroll",e);break;case"onScrollEnd":r!=null&&Oe("scrollend",e);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(o(61));if(l=r.__html,l!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=l}}break;case"multiple":e.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":e.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){e.removeAttribute("xlink:href");break}l=Ai(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(l,""+r):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":r===!0?e.setAttribute(l,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(l,r):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?e.setAttribute(l,r):e.removeAttribute(l);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?e.removeAttribute(l):e.setAttribute(l,r);break;case"popover":Oe("beforetoggle",e),Oe("toggle",e),Ri(e,"popover",r);break;case"xlinkActuate":xn(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":xn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":xn(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":xn(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":xn(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":xn(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":xn(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":xn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":xn(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":Ri(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=nS.get(l)||l,Ri(e,l,r))}}function Sc(e,n,l,r,s,f){switch(l){case"style":Id(e,r,f);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(o(61));if(l=r.__html,l!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=l}}break;case"children":typeof r=="string"?ra(e,r):(typeof r=="number"||typeof r=="bigint")&&ra(e,""+r);break;case"onScroll":r!=null&&Oe("scroll",e);break;case"onScrollEnd":r!=null&&Oe("scrollend",e);break;case"onClick":r!=null&&(e.onclick=xo);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!jd.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(s=l.endsWith("Capture"),n=l.slice(2,s?l.length-7:void 0),f=e[At]||null,f=f!=null?f[l]:null,typeof f=="function"&&e.removeEventListener(n,f,s),typeof r=="function")){typeof f!="function"&&f!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(n,r,s);break e}l in e?e[l]=r:r===!0?e.setAttribute(l,""):Ri(e,l,r)}}}function vt(e,n,l){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Oe("error",e),Oe("load",e);var r=!1,s=!1,f;for(f in l)if(l.hasOwnProperty(f)){var p=l[f];if(p!=null)switch(f){case"src":r=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:ke(e,n,f,p,l,null)}}s&&ke(e,n,"srcSet",l.srcSet,l,null),r&&ke(e,n,"src",l.src,l,null);return;case"input":Oe("invalid",e);var y=f=p=s=null,T=null,U=null;for(r in l)if(l.hasOwnProperty(r)){var Y=l[r];if(Y!=null)switch(r){case"name":s=Y;break;case"type":p=Y;break;case"checked":T=Y;break;case"defaultChecked":U=Y;break;case"value":f=Y;break;case"defaultValue":y=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(o(137,n));break;default:ke(e,n,r,Y,l,null)}}qd(e,f,y,T,U,p,s,!1),_i(e);return;case"select":Oe("invalid",e),r=p=f=null;for(s in l)if(l.hasOwnProperty(s)&&(y=l[s],y!=null))switch(s){case"value":f=y;break;case"defaultValue":p=y;break;case"multiple":r=y;default:ke(e,n,s,y,l,null)}n=f,l=p,e.multiple=!!r,n!=null?aa(e,!!r,n,!1):l!=null&&aa(e,!!r,l,!0);return;case"textarea":Oe("invalid",e),f=s=r=null;for(p in l)if(l.hasOwnProperty(p)&&(y=l[p],y!=null))switch(p){case"value":r=y;break;case"defaultValue":s=y;break;case"children":f=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(o(91));break;default:ke(e,n,p,y,l,null)}Fd(e,r,s,f),_i(e);return;case"option":for(T in l)if(l.hasOwnProperty(T)&&(r=l[T],r!=null))switch(T){case"selected":e.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:ke(e,n,T,r,l,null)}return;case"dialog":Oe("beforetoggle",e),Oe("toggle",e),Oe("cancel",e),Oe("close",e);break;case"iframe":case"object":Oe("load",e);break;case"video":case"audio":for(r=0;r<qr.length;r++)Oe(qr[r],e);break;case"image":Oe("error",e),Oe("load",e);break;case"details":Oe("toggle",e);break;case"embed":case"source":case"link":Oe("error",e),Oe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(U in l)if(l.hasOwnProperty(U)&&(r=l[U],r!=null))switch(U){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:ke(e,n,U,r,l,null)}return;default:if(Ls(n)){for(Y in l)l.hasOwnProperty(Y)&&(r=l[Y],r!==void 0&&Sc(e,n,Y,r,l,void 0));return}}for(y in l)l.hasOwnProperty(y)&&(r=l[y],r!=null&&ke(e,n,y,r,l,null))}function T1(e,n,l,r){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,f=null,p=null,y=null,T=null,U=null,Y=null;for(G in l){var X=l[G];if(l.hasOwnProperty(G)&&X!=null)switch(G){case"checked":break;case"value":break;case"defaultValue":T=X;default:r.hasOwnProperty(G)||ke(e,n,G,null,r,X)}}for(var V in r){var G=r[V];if(X=l[V],r.hasOwnProperty(V)&&(G!=null||X!=null))switch(V){case"type":f=G;break;case"name":s=G;break;case"checked":U=G;break;case"defaultChecked":Y=G;break;case"value":p=G;break;case"defaultValue":y=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(o(137,n));break;default:G!==X&&ke(e,n,V,G,r,X)}}zs(e,p,y,T,U,Y,f,s);return;case"select":G=p=y=V=null;for(f in l)if(T=l[f],l.hasOwnProperty(f)&&T!=null)switch(f){case"value":break;case"multiple":G=T;default:r.hasOwnProperty(f)||ke(e,n,f,null,r,T)}for(s in r)if(f=r[s],T=l[s],r.hasOwnProperty(s)&&(f!=null||T!=null))switch(s){case"value":V=f;break;case"defaultValue":y=f;break;case"multiple":p=f;default:f!==T&&ke(e,n,s,f,r,T)}n=y,l=p,r=G,V!=null?aa(e,!!l,V,!1):!!r!=!!l&&(n!=null?aa(e,!!l,n,!0):aa(e,!!l,l?[]:"",!1));return;case"textarea":G=V=null;for(y in l)if(s=l[y],l.hasOwnProperty(y)&&s!=null&&!r.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:ke(e,n,y,null,r,s)}for(p in r)if(s=r[p],f=l[p],r.hasOwnProperty(p)&&(s!=null||f!=null))switch(p){case"value":V=s;break;case"defaultValue":G=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==f&&ke(e,n,p,s,r,f)}Yd(e,V,G);return;case"option":for(var ve in l)if(V=l[ve],l.hasOwnProperty(ve)&&V!=null&&!r.hasOwnProperty(ve))switch(ve){case"selected":e.selected=!1;break;default:ke(e,n,ve,null,r,V)}for(T in r)if(V=r[T],G=l[T],r.hasOwnProperty(T)&&V!==G&&(V!=null||G!=null))switch(T){case"selected":e.selected=V&&typeof V!="function"&&typeof V!="symbol";break;default:ke(e,n,T,V,r,G)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ge in l)V=l[ge],l.hasOwnProperty(ge)&&V!=null&&!r.hasOwnProperty(ge)&&ke(e,n,ge,null,r,V);for(U in r)if(V=r[U],G=l[U],r.hasOwnProperty(U)&&V!==G&&(V!=null||G!=null))switch(U){case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(o(137,n));break;default:ke(e,n,U,V,r,G)}return;default:if(Ls(n)){for(var $e in l)V=l[$e],l.hasOwnProperty($e)&&V!==void 0&&!r.hasOwnProperty($e)&&Sc(e,n,$e,void 0,r,V);for(Y in r)V=r[Y],G=l[Y],!r.hasOwnProperty(Y)||V===G||V===void 0&&G===void 0||Sc(e,n,Y,V,r,G);return}}for(var N in l)V=l[N],l.hasOwnProperty(N)&&V!=null&&!r.hasOwnProperty(N)&&ke(e,n,N,null,r,V);for(X in r)V=r[X],G=l[X],!r.hasOwnProperty(X)||V===G||V==null&&G==null||ke(e,n,X,V,r,G)}var bc=null,xc=null;function wo(e){return e.nodeType===9?e:e.ownerDocument}function Xh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ih(e,n){if(e===0)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&n==="foreignObject"?0:e}function wc(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.children=="bigint"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Cc=null;function A1(){var e=window.event;return e&&e.type==="popstate"?e===Cc?!1:(Cc=e,!0):(Cc=null,!1)}var Kh=typeof setTimeout=="function"?setTimeout:void 0,M1=typeof clearTimeout=="function"?clearTimeout:void 0,Zh=typeof Promise=="function"?Promise:void 0,O1=typeof queueMicrotask=="function"?queueMicrotask:typeof Zh<"u"?function(e){return Zh.resolve(null).then(e).catch(D1)}:Kh;function D1(e){setTimeout(function(){throw e})}function rl(e){return e==="head"}function Qh(e,n){var l=n,r=0,s=0;do{var f=l.nextSibling;if(e.removeChild(l),f&&f.nodeType===8)if(l=f.data,l==="/$"){if(0<r&&8>r){l=r;var p=e.ownerDocument;if(l&1&&Fr(p.documentElement),l&2&&Fr(p.body),l&4)for(l=p.head,Fr(l),p=l.firstChild;p;){var y=p.nextSibling,T=p.nodeName;p[or]||T==="SCRIPT"||T==="STYLE"||T==="LINK"&&p.rel.toLowerCase()==="stylesheet"||l.removeChild(p),p=y}}if(s===0){e.removeChild(f),ei(n);return}s--}else l==="$"||l==="$?"||l==="$!"?s++:r=l.charCodeAt(0)-48;else r=0;l=f}while(l);ei(n)}function Rc(e){var n=e.firstChild;for(n&&n.nodeType===10&&(n=n.nextSibling);n;){var l=n;switch(n=n.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Rc(l),Ms(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function N1(e,n,l,r){for(;e.nodeType===1;){var s=l;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!r&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(r){if(!e[or])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(f=e.getAttribute("rel"),f==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(f!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(f=e.getAttribute("src"),(f!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&f&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(n==="input"&&e.type==="hidden"){var f=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===f)return e}else return e;if(e=nn(e.nextSibling),e===null)break}return null}function z1(e,n,l){if(n==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=nn(e.nextSibling),e===null))return null;return e}function Ec(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function H1(e,n){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")n();else{var r=function(){n(),l.removeEventListener("DOMContentLoaded",r)};l.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}function nn(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?"||n==="F!"||n==="F")break;if(n==="/$")return null}}return e}var _c=null;function Wh(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(n===0)return e;n--}else l==="/$"&&n++}e=e.previousSibling}return null}function Jh(e,n,l){switch(n=wo(l),e){case"html":if(e=n.documentElement,!e)throw Error(o(452));return e;case"head":if(e=n.head,!e)throw Error(o(453));return e;case"body":if(e=n.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Fr(e){for(var n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Ms(e)}var Wt=new Map,ep=new Set;function Co(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Ln=B.d;B.d={f:L1,r:U1,D:j1,C:V1,L:B1,m:G1,X:k1,S:P1,M:$1};function L1(){var e=Ln.f(),n=mo();return e||n}function U1(e){var n=ea(e);n!==null&&n.tag===5&&n.type==="form"?bm(n):Ln.r(e)}var Ha=typeof document>"u"?null:document;function tp(e,n,l){var r=Ha;if(r&&typeof n=="string"&&n){var s=Yt(n);s='link[rel="'+e+'"][href="'+s+'"]',typeof l=="string"&&(s+='[crossorigin="'+l+'"]'),ep.has(s)||(ep.add(s),e={rel:e,crossOrigin:l,href:n},r.querySelector(s)===null&&(n=r.createElement("link"),vt(n,"link",e),ut(n),r.head.appendChild(n)))}}function j1(e){Ln.D(e),tp("dns-prefetch",e,null)}function V1(e,n){Ln.C(e,n),tp("preconnect",e,n)}function B1(e,n,l){Ln.L(e,n,l);var r=Ha;if(r&&e&&n){var s='link[rel="preload"][as="'+Yt(n)+'"]';n==="image"&&l&&l.imageSrcSet?(s+='[imagesrcset="'+Yt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(s+='[imagesizes="'+Yt(l.imageSizes)+'"]')):s+='[href="'+Yt(e)+'"]';var f=s;switch(n){case"style":f=La(e);break;case"script":f=Ua(e)}Wt.has(f)||(e=v({rel:"preload",href:n==="image"&&l&&l.imageSrcSet?void 0:e,as:n},l),Wt.set(f,e),r.querySelector(s)!==null||n==="style"&&r.querySelector(Xr(f))||n==="script"&&r.querySelector(Ir(f))||(n=r.createElement("link"),vt(n,"link",e),ut(n),r.head.appendChild(n)))}}function G1(e,n){Ln.m(e,n);var l=Ha;if(l&&e){var r=n&&typeof n.as=="string"?n.as:"script",s='link[rel="modulepreload"][as="'+Yt(r)+'"][href="'+Yt(e)+'"]',f=s;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=Ua(e)}if(!Wt.has(f)&&(e=v({rel:"modulepreload",href:e},n),Wt.set(f,e),l.querySelector(s)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Ir(f)))return}r=l.createElement("link"),vt(r,"link",e),ut(r),l.head.appendChild(r)}}}function P1(e,n,l){Ln.S(e,n,l);var r=Ha;if(r&&e){var s=ta(r).hoistableStyles,f=La(e);n=n||"default";var p=s.get(f);if(!p){var y={loading:0,preload:null};if(p=r.querySelector(Xr(f)))y.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":n},l),(l=Wt.get(f))&&Tc(e,l);var T=p=r.createElement("link");ut(T),vt(T,"link",e),T._p=new Promise(function(U,Y){T.onload=U,T.onerror=Y}),T.addEventListener("load",function(){y.loading|=1}),T.addEventListener("error",function(){y.loading|=2}),y.loading|=4,Ro(p,n,r)}p={type:"stylesheet",instance:p,count:1,state:y},s.set(f,p)}}}function k1(e,n){Ln.X(e,n);var l=Ha;if(l&&e){var r=ta(l).hoistableScripts,s=Ua(e),f=r.get(s);f||(f=l.querySelector(Ir(s)),f||(e=v({src:e,async:!0},n),(n=Wt.get(s))&&Ac(e,n),f=l.createElement("script"),ut(f),vt(f,"link",e),l.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},r.set(s,f))}}function $1(e,n){Ln.M(e,n);var l=Ha;if(l&&e){var r=ta(l).hoistableScripts,s=Ua(e),f=r.get(s);f||(f=l.querySelector(Ir(s)),f||(e=v({src:e,async:!0,type:"module"},n),(n=Wt.get(s))&&Ac(e,n),f=l.createElement("script"),ut(f),vt(f,"link",e),l.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},r.set(s,f))}}function np(e,n,l,r){var s=(s=re.current)?Co(s):null;if(!s)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(n=La(l.href),l=ta(s).hoistableStyles,r=l.get(n),r||(r={type:"style",instance:null,count:0,state:null},l.set(n,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=La(l.href);var f=ta(s).hoistableStyles,p=f.get(e);if(p||(s=s.ownerDocument||s,p={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(e,p),(f=s.querySelector(Xr(e)))&&!f._p&&(p.instance=f,p.state.loading=5),Wt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Wt.set(e,l),f||q1(s,e,l,p.state))),n&&r===null)throw Error(o(528,""));return p}if(n&&r!==null)throw Error(o(529,""));return null;case"script":return n=l.async,l=l.src,typeof l=="string"&&n&&typeof n!="function"&&typeof n!="symbol"?(n=Ua(l),l=ta(s).hoistableScripts,r=l.get(n),r||(r={type:"script",instance:null,count:0,state:null},l.set(n,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function La(e){return'href="'+Yt(e)+'"'}function Xr(e){return'link[rel="stylesheet"]['+e+"]"}function lp(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function q1(e,n,l,r){e.querySelector('link[rel="preload"][as="style"]['+n+"]")?r.loading=1:(n=e.createElement("link"),r.preload=n,n.addEventListener("load",function(){return r.loading|=1}),n.addEventListener("error",function(){return r.loading|=2}),vt(n,"link",l),ut(n),e.head.appendChild(n))}function Ua(e){return'[src="'+Yt(e)+'"]'}function Ir(e){return"script[async]"+e}function ap(e,n,l){if(n.count++,n.instance===null)switch(n.type){case"style":var r=e.querySelector('style[data-href~="'+Yt(l.href)+'"]');if(r)return n.instance=r,ut(r),r;var s=v({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return r=(e.ownerDocument||e).createElement("style"),ut(r),vt(r,"style",s),Ro(r,l.precedence,e),n.instance=r;case"stylesheet":s=La(l.href);var f=e.querySelector(Xr(s));if(f)return n.state.loading|=4,n.instance=f,ut(f),f;r=lp(l),(s=Wt.get(s))&&Tc(r,s),f=(e.ownerDocument||e).createElement("link"),ut(f);var p=f;return p._p=new Promise(function(y,T){p.onload=y,p.onerror=T}),vt(f,"link",r),n.state.loading|=4,Ro(f,l.precedence,e),n.instance=f;case"script":return f=Ua(l.src),(s=e.querySelector(Ir(f)))?(n.instance=s,ut(s),s):(r=l,(s=Wt.get(f))&&(r=v({},l),Ac(r,s)),e=e.ownerDocument||e,s=e.createElement("script"),ut(s),vt(s,"link",r),e.head.appendChild(s),n.instance=s);case"void":return null;default:throw Error(o(443,n.type))}else n.type==="stylesheet"&&(n.state.loading&4)===0&&(r=n.instance,n.state.loading|=4,Ro(r,l.precedence,e));return n.instance}function Ro(e,n,l){for(var r=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=r.length?r[r.length-1]:null,f=s,p=0;p<r.length;p++){var y=r[p];if(y.dataset.precedence===n)f=y;else if(f!==s)break}f?f.parentNode.insertBefore(e,f.nextSibling):(n=l.nodeType===9?l.head:l,n.insertBefore(e,n.firstChild))}function Tc(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.title==null&&(e.title=n.title)}function Ac(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.integrity==null&&(e.integrity=n.integrity)}var Eo=null;function rp(e,n,l){if(Eo===null){var r=new Map,s=Eo=new Map;s.set(l,r)}else s=Eo,r=s.get(l),r||(r=new Map,s.set(l,r));if(r.has(e))return r;for(r.set(e,null),l=l.getElementsByTagName(e),s=0;s<l.length;s++){var f=l[s];if(!(f[or]||f[St]||e==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var p=f.getAttribute(n)||"";p=e+p;var y=r.get(p);y?y.push(f):r.set(p,[f])}}return r}function ip(e,n,l){e=e.ownerDocument||e,e.head.insertBefore(l,n==="title"?e.querySelector("head > title"):null)}function Y1(e,n,l){if(l===1||n.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof n.precedence!="string"||typeof n.href!="string"||n.href==="")break;return!0;case"link":if(typeof n.rel!="string"||typeof n.href!="string"||n.href===""||n.onLoad||n.onError)break;switch(n.rel){case"stylesheet":return e=n.disabled,typeof n.precedence=="string"&&e==null;default:return!0}case"script":if(n.async&&typeof n.async!="function"&&typeof n.async!="symbol"&&!n.onLoad&&!n.onError&&n.src&&typeof n.src=="string")return!0}return!1}function op(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Kr=null;function F1(){}function X1(e,n,l){if(Kr===null)throw Error(o(475));var r=Kr;if(n.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(n.state.loading&4)===0){if(n.instance===null){var s=La(l.href),f=e.querySelector(Xr(s));if(f){e=f._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(r.count++,r=_o.bind(r),e.then(r,r)),n.state.loading|=4,n.instance=f,ut(f);return}f=e.ownerDocument||e,l=lp(l),(s=Wt.get(s))&&Tc(l,s),f=f.createElement("link"),ut(f);var p=f;p._p=new Promise(function(y,T){p.onload=y,p.onerror=T}),vt(f,"link",l),n.instance=f}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(n,e),(e=n.state.preload)&&(n.state.loading&3)===0&&(r.count++,n=_o.bind(r),e.addEventListener("load",n),e.addEventListener("error",n))}}function I1(){if(Kr===null)throw Error(o(475));var e=Kr;return e.stylesheets&&e.count===0&&Mc(e,e.stylesheets),0<e.count?function(n){var l=setTimeout(function(){if(e.stylesheets&&Mc(e,e.stylesheets),e.unsuspend){var r=e.unsuspend;e.unsuspend=null,r()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(l)}}:null}function _o(){if(this.count--,this.count===0){if(this.stylesheets)Mc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var To=null;function Mc(e,n){e.stylesheets=null,e.unsuspend!==null&&(e.count++,To=new Map,n.forEach(K1,e),To=null,_o.call(e))}function K1(e,n){if(!(n.state.loading&4)){var l=To.get(e);if(l)var r=l.get(null);else{l=new Map,To.set(e,l);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<s.length;f++){var p=s[f];(p.nodeName==="LINK"||p.getAttribute("media")!=="not all")&&(l.set(p.dataset.precedence,p),r=p)}r&&l.set(null,r)}s=n.instance,p=s.getAttribute("data-precedence"),f=l.get(p)||r,f===r&&l.set(null,s),l.set(p,s),this.count++,r=_o.bind(this),s.addEventListener("load",r),s.addEventListener("error",r),f?f.parentNode.insertBefore(s,f.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),n.state.loading|=4}}var Zr={$$typeof:z,Provider:null,Consumer:null,_currentValue:j,_currentValue2:j,_threadCount:0};function Z1(e,n,l,r,s,f,p,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Es(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Es(0),this.hiddenUpdates=Es(null),this.identifierPrefix=r,this.onUncaughtError=s,this.onCaughtError=f,this.onRecoverableError=p,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function sp(e,n,l,r,s,f,p,y,T,U,Y,X){return e=new Z1(e,n,l,p,y,T,U,X),n=1,f===!0&&(n|=24),f=Lt(3,null,null,n),e.current=f,f.stateNode=e,n=cu(),n.refCount++,e.pooledCache=n,n.refCount++,f.memoizedState={element:r,isDehydrated:l,cache:n},mu(f),e}function up(e){return e?(e=ga,e):ga}function cp(e,n,l,r,s,f){s=up(s),r.context===null?r.context=s:r.pendingContext=s,r=Fn(n),r.payload={element:l},f=f===void 0?null:f,f!==null&&(r.callback=f),l=Xn(e,r,n),l!==null&&(Gt(l,e,n),_r(l,e,n))}function fp(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<n?l:n}}function Oc(e,n){fp(e,n),(e=e.alternate)&&fp(e,n)}function dp(e){if(e.tag===13){var n=da(e,67108864);n!==null&&Gt(n,e,67108864),Oc(e,67108864)}}var Ao=!0;function Q1(e,n,l,r){var s=H.T;H.T=null;var f=B.p;try{B.p=2,Dc(e,n,l,r)}finally{B.p=f,H.T=s}}function W1(e,n,l,r){var s=H.T;H.T=null;var f=B.p;try{B.p=8,Dc(e,n,l,r)}finally{B.p=f,H.T=s}}function Dc(e,n,l,r){if(Ao){var s=Nc(r);if(s===null)yc(e,n,r,Mo,l),mp(e,r);else if(eb(s,e,n,l,r))r.stopPropagation();else if(mp(e,r),n&4&&-1<J1.indexOf(e)){for(;s!==null;){var f=ea(s);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var p=Rl(f.pendingLanes);if(p!==0){var y=f;for(y.pendingLanes|=2,y.entangledLanes|=2;p;){var T=1<<31-mt(p);y.entanglements[1]|=T,p&=~T}pn(f),(je&6)===0&&(fo=Ge()+500,$r(0))}}break;case 13:y=da(f,2),y!==null&&Gt(y,f,2),mo(),Oc(f,2)}if(f=Nc(r),f===null&&yc(e,n,r,Mo,l),f===s)break;s=f}s!==null&&r.stopPropagation()}else yc(e,n,r,null,l)}}function Nc(e){return e=js(e),zc(e)}var Mo=null;function zc(e){if(Mo=null,e=Jl(e),e!==null){var n=c(e);if(n===null)e=null;else{var l=n.tag;if(l===13){if(e=d(n),e!==null)return e;e=null}else if(l===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return Mo=e,null}function gp(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(cn()){case Fe:return 2;case st:return 8;case _t:case Si:return 32;case ar:return 268435456;default:return 32}default:return 32}}var Hc=!1,il=null,ol=null,sl=null,Qr=new Map,Wr=new Map,ul=[],J1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function mp(e,n){switch(e){case"focusin":case"focusout":il=null;break;case"dragenter":case"dragleave":ol=null;break;case"mouseover":case"mouseout":sl=null;break;case"pointerover":case"pointerout":Qr.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wr.delete(n.pointerId)}}function Jr(e,n,l,r,s,f){return e===null||e.nativeEvent!==f?(e={blockedOn:n,domEventName:l,eventSystemFlags:r,nativeEvent:f,targetContainers:[s]},n!==null&&(n=ea(n),n!==null&&dp(n)),e):(e.eventSystemFlags|=r,n=e.targetContainers,s!==null&&n.indexOf(s)===-1&&n.push(s),e)}function eb(e,n,l,r,s){switch(n){case"focusin":return il=Jr(il,e,n,l,r,s),!0;case"dragenter":return ol=Jr(ol,e,n,l,r,s),!0;case"mouseover":return sl=Jr(sl,e,n,l,r,s),!0;case"pointerover":var f=s.pointerId;return Qr.set(f,Jr(Qr.get(f)||null,e,n,l,r,s)),!0;case"gotpointercapture":return f=s.pointerId,Wr.set(f,Jr(Wr.get(f)||null,e,n,l,r,s)),!0}return!1}function hp(e){var n=Jl(e.target);if(n!==null){var l=c(n);if(l!==null){if(n=l.tag,n===13){if(n=d(l),n!==null){e.blockedOn=n,Xy(e.priority,function(){if(l.tag===13){var r=Bt();r=_s(r);var s=da(l,r);s!==null&&Gt(s,l,r),Oc(l,r)}});return}}else if(n===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Oo(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var l=Nc(e.nativeEvent);if(l===null){l=e.nativeEvent;var r=new l.constructor(l.type,l);Us=r,l.target.dispatchEvent(r),Us=null}else return n=ea(l),n!==null&&dp(n),e.blockedOn=l,!1;n.shift()}return!0}function pp(e,n,l){Oo(e)&&l.delete(n)}function tb(){Hc=!1,il!==null&&Oo(il)&&(il=null),ol!==null&&Oo(ol)&&(ol=null),sl!==null&&Oo(sl)&&(sl=null),Qr.forEach(pp),Wr.forEach(pp)}function Do(e,n){e.blockedOn===n&&(e.blockedOn=null,Hc||(Hc=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,tb)))}var No=null;function vp(e){No!==e&&(No=e,t.unstable_scheduleCallback(t.unstable_NormalPriority,function(){No===e&&(No=null);for(var n=0;n<e.length;n+=3){var l=e[n],r=e[n+1],s=e[n+2];if(typeof r!="function"){if(zc(r||l)===null)continue;break}var f=ea(l);f!==null&&(e.splice(n,3),n-=3,zu(f,{pending:!0,data:s,method:l.method,action:r},r,s))}}))}function ei(e){function n(T){return Do(T,e)}il!==null&&Do(il,e),ol!==null&&Do(ol,e),sl!==null&&Do(sl,e),Qr.forEach(n),Wr.forEach(n);for(var l=0;l<ul.length;l++){var r=ul[l];r.blockedOn===e&&(r.blockedOn=null)}for(;0<ul.length&&(l=ul[0],l.blockedOn===null);)hp(l),l.blockedOn===null&&ul.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(r=0;r<l.length;r+=3){var s=l[r],f=l[r+1],p=s[At]||null;if(typeof f=="function")p||vp(l);else if(p){var y=null;if(f&&f.hasAttribute("formAction")){if(s=f,p=f[At]||null)y=p.formAction;else if(zc(s)!==null)continue}else y=p.action;typeof y=="function"?l[r+1]=y:(l.splice(r,3),r-=3),vp(l)}}}function Lc(e){this._internalRoot=e}zo.prototype.render=Lc.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(o(409));var l=n.current,r=Bt();cp(l,r,e,n,null,null)},zo.prototype.unmount=Lc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;cp(e.current,2,null,e,null,null),mo(),n[Wl]=null}};function zo(e){this._internalRoot=e}zo.prototype.unstable_scheduleHydration=function(e){if(e){var n=Hd();e={blockedOn:null,target:e,priority:n};for(var l=0;l<ul.length&&n!==0&&n<ul[l].priority;l++);ul.splice(l,0,e),l===0&&hp(e)}};var yp=a.version;if(yp!=="19.1.0")throw Error(o(527,yp,"19.1.0"));B.findDOMNode=function(e){var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=m(n),e=e!==null?h(e):null,e=e===null?null:e.stateNode,e};var nb={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:H,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ho=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ho.isDisabled&&Ho.supportsFiber)try{Rt=Ho.inject(nb),gt=Ho}catch{}}return ni.createRoot=function(e,n){if(!u(e))throw Error(o(299));var l=!1,r="",s=Hm,f=Lm,p=Um,y=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(s=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(y=n.unstable_transitionCallbacks)),n=sp(e,1,!1,null,null,l,r,s,f,p,y,null),e[Wl]=n.current,vc(e),new Lc(n)},ni.hydrateRoot=function(e,n,l){if(!u(e))throw Error(o(299));var r=!1,s="",f=Hm,p=Lm,y=Um,T=null,U=null;return l!=null&&(l.unstable_strictMode===!0&&(r=!0),l.identifierPrefix!==void 0&&(s=l.identifierPrefix),l.onUncaughtError!==void 0&&(f=l.onUncaughtError),l.onCaughtError!==void 0&&(p=l.onCaughtError),l.onRecoverableError!==void 0&&(y=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(T=l.unstable_transitionCallbacks),l.formState!==void 0&&(U=l.formState)),n=sp(e,1,!0,n,l??null,r,s,f,p,y,T,U),n.context=up(null),l=n.current,r=Bt(),r=_s(r),s=Fn(r),s.callback=null,Xn(l,s,r),l=r,n.current.lanes=l,ir(n,l),pn(n),e[Wl]=n.current,vc(e),new zo(n)},ni.version="19.1.0",ni}var Ap;function db(){if(Ap)return Vc.exports;Ap=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(a){console.error(a)}}return t(),Vc.exports=fb(),Vc.exports}var gb=db();const mb=Symbol(),Qf=Symbol(),ii="a",Uv="f",Mp="p",jv="c",Vv="t",Wf="h",si="w",Jf="o",ed="k";let hb=(t,a)=>new Proxy(t,a);const vf=Object.getPrototypeOf,yf=new WeakMap,Bv=t=>t&&(yf.has(t)?yf.get(t):vf(t)===Object.prototype||vf(t)===Array.prototype),es=t=>typeof t=="object"&&t!==null,pb=t=>Object.values(Object.getOwnPropertyDescriptors(t)).some(a=>!a.configurable&&!a.writable),vb=t=>{if(Array.isArray(t))return Array.from(t);const a=Object.getOwnPropertyDescriptors(t);return Object.values(a).forEach(i=>{i.configurable=!0}),Object.create(vf(t),a)},yb=(t,a)=>{const i={[Uv]:a};let o=!1;const u=(g,m)=>{if(!o){let h=i[ii].get(t);if(h||(h={},i[ii].set(t,h)),g===si)h[si]=!0;else{let v=h[g];v||(v=new Set,h[g]=v),v.add(m)}}},c=()=>{o=!0,i[ii].delete(t)},d={get(g,m){return m===Qf?t:(u(ed,m),Gv(Reflect.get(g,m),i[ii],i[jv],i[Vv]))},has(g,m){return m===mb?(c(),!0):(u(Wf,m),Reflect.has(g,m))},getOwnPropertyDescriptor(g,m){return u(Jf,m),Reflect.getOwnPropertyDescriptor(g,m)},ownKeys(g){return u(si),Reflect.ownKeys(g)}};return a&&(d.set=d.deleteProperty=()=>!1),[d,i]},td=t=>t[Qf]||t,Gv=(t,a,i,o)=>{if(!Bv(t))return t;let u=o&&o.get(t);if(!u){const m=td(t);pb(m)?u=[m,vb(m)]:u=[m],o==null||o.set(t,u)}const[c,d]=u;let g=i&&i.get(c);return(!g||g[1][Uv]!==!!d)&&(g=yb(c,!!d),g[1][Mp]=hb(d||c,g[0]),i&&i.set(c,g)),g[1][ii]=a,g[1][jv]=i,g[1][Vv]=o,g[1][Mp]},Sb=(t,a)=>{const i=Reflect.ownKeys(t),o=Reflect.ownKeys(a);return i.length!==o.length||i.some((u,c)=>u!==o[c])},Pv=(t,a,i,o,u=Object.is)=>{if(u(t,a))return!1;if(!es(t)||!es(a))return!0;const c=i.get(td(t));if(!c)return!0;if(o){if(o.get(t)===a)return!1;o.set(t,a)}let d=null;for(const g of c[Wf]||[])if(d=Reflect.has(t,g)!==Reflect.has(a,g),d)return d;if(c[si]===!0){if(d=Sb(t,a),d)return d}else for(const g of c[Jf]||[]){const m=!!Reflect.getOwnPropertyDescriptor(t,g),h=!!Reflect.getOwnPropertyDescriptor(a,g);if(d=m!==h,d)return d}for(const g of c[ed]||[])if(d=Pv(t[g],a[g],i,o,u),d)return d;if(d===null)throw new Error("invalid used");return d},bb=t=>Bv(t)&&t[Qf]||null,Op=(t,a=!0)=>{yf.set(t,a)},xb=(t,a,i)=>{const o=[],u=new WeakSet,c=(d,g)=>{var m,h,v;if(u.has(d))return;es(d)&&u.add(d);const b=es(d)&&a.get(td(d));if(b){if((m=b[Wf])===null||m===void 0||m.forEach(S=>{const x=`:has(${String(S)})`;o.push(g?[...g,x]:[x])}),b[si]===!0){const S=":ownKeys";o.push(g?[...g,S]:[S])}else(h=b[Jf])===null||h===void 0||h.forEach(S=>{const x=`:hasOwn(${String(S)})`;o.push(g?[...g,x]:[x])});(v=b[ed])===null||v===void 0||v.forEach(S=>{"value"in(Object.getOwnPropertyDescriptor(d,S)||{})&&c(d[S],g?[...g,S]:[S])})}else g&&o.push(g)};return c(t),o},ts={},nd=t=>typeof t=="object"&&t!==null,wb=t=>nd(t)&&!ld.has(t)&&(Array.isArray(t)||!(Symbol.iterator in t))&&!(t instanceof WeakMap)&&!(t instanceof WeakSet)&&!(t instanceof Error)&&!(t instanceof Number)&&!(t instanceof Date)&&!(t instanceof String)&&!(t instanceof RegExp)&&!(t instanceof ArrayBuffer)&&!(t instanceof Promise),kv=(t,a)=>{const i=Dp.get(t);if((i==null?void 0:i[0])===a)return i[1];const o=Array.isArray(t)?[]:Object.create(Object.getPrototypeOf(t));return Op(o,!0),Dp.set(t,[a,o]),Reflect.ownKeys(t).forEach(u=>{if(Object.getOwnPropertyDescriptor(o,u))return;const c=Reflect.get(t,u),{enumerable:d}=Reflect.getOwnPropertyDescriptor(t,u),g={value:c,enumerable:d,configurable:!0};if(ld.has(c))Op(c,!1);else if(Yl.has(c)){const[m,h]=Yl.get(c);g.value=kv(m,h())}Object.defineProperty(o,u,g)}),Object.preventExtensions(o)},Cb=(t,a,i,o)=>({deleteProperty(u,c){const d=Reflect.get(u,c);i(c);const g=Reflect.deleteProperty(u,c);return g&&o(["delete",[c],d]),g},set(u,c,d,g){const m=!t()&&Reflect.has(u,c),h=Reflect.get(u,c,g);if(m&&(Np(h,d)||ns.has(d)&&Np(h,ns.get(d))))return!0;i(c),nd(d)&&(d=bb(d)||d);const v=!Yl.has(d)&&Eb(d)?Qa(d):d;return a(c,v),Reflect.set(u,c,v,g),o(["set",[c],d,h]),!0}}),Yl=new WeakMap,ld=new WeakSet,Dp=new WeakMap,Lo=[1,1],ns=new WeakMap;let Np=Object.is,Rb=(t,a)=>new Proxy(t,a),Eb=wb,_b=kv,Tb=Cb;function Qa(t={}){if(!nd(t))throw new Error("object required");const a=ns.get(t);if(a)return a;let i=Lo[0];const o=new Set,u=(A,_=++Lo[0])=>{i!==_&&(i=_,o.forEach(D=>D(A,_)))};let c=Lo[1];const d=(A=++Lo[1])=>(c!==A&&!o.size&&(c=A,m.forEach(([_])=>{const D=_[1](A);D>i&&(i=D)})),i),g=A=>(_,D)=>{const z=[..._];z[1]=[A,...z[1]],u(z,D)},m=new Map,h=(A,_)=>{const D=!ld.has(_)&&Yl.get(_);if(D){if((ts?"production":void 0)!=="production"&&m.has(A))throw new Error("prop listener already exists");if(o.size){const z=D[2](g(A));m.set(A,[D,z])}else m.set(A,[D])}},v=A=>{var _;const D=m.get(A);D&&(m.delete(A),(_=D[1])==null||_.call(D))},b=A=>(o.add(A),o.size===1&&m.forEach(([D,z],k)=>{if((ts?"production":void 0)!=="production"&&z)throw new Error("remove already exists");const P=D[2](g(k));m.set(k,[D,P])}),()=>{o.delete(A),o.size===0&&m.forEach(([D,z],k)=>{z&&(z(),m.set(k,[D]))})});let S=!0;const x=Tb(()=>S,h,v,u),R=Rb(t,x);ns.set(t,R);const w=[t,d,b];return Yl.set(R,w),Reflect.ownKeys(t).forEach(A=>{const _=Object.getOwnPropertyDescriptor(t,A);"value"in _&&_.writable&&(R[A]=t[A])}),S=!1,R}function Ab(t,a,i){const o=Yl.get(t);(ts?"production":void 0)!=="production"&&!o&&console.warn("Please use proxy object");let u;const c=[],d=o[2];let g=!1;const h=d(v=>{c.push(v),u||(u=Promise.resolve().then(()=>{u=void 0,g&&a(c.splice(0))}))});return g=!0,()=>{g=!1,h()}}function zp(t){const a=Yl.get(t);(ts?"production":void 0)!=="production"&&!a&&console.warn("Please use proxy object");const[i,o]=a;return _b(i,o())}const Mb={},Ob=(t,a)=>{const i=C.useRef(void 0);C.useEffect(()=>{i.current=xb(t,a)}),C.useDebugValue(i.current)},Db=Ob,Nb=new WeakMap;function pl(t,a){const o=C.useMemo(()=>t&&new WeakMap,[t]),u=C.useRef(void 0);let c=!0;const d=C.useSyncExternalStore(C.useCallback(m=>{const h=Ab(t,m);return m(),h},[t,void 0]),()=>{const m=zp(t);try{if(!c&&u.current&&!Pv(u.current,m,o,new WeakMap))return u.current}catch{}return m},()=>zp(t));c=!1,C.useLayoutEffect(()=>{u.current=d}),(Mb?"production":void 0)!=="production"&&Db(d,o);const g=C.useMemo(()=>new WeakMap,[]);return Gv(d,o,g,Nb)}var kc={exports:{}},$c={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hp;function zb(){if(Hp)return $c;Hp=1;var t=cs();function a(b,S){return b===S&&(b!==0||1/b===1/S)||b!==b&&S!==S}var i=typeof Object.is=="function"?Object.is:a,o=t.useState,u=t.useEffect,c=t.useLayoutEffect,d=t.useDebugValue;function g(b,S){var x=S(),R=o({inst:{value:x,getSnapshot:S}}),w=R[0].inst,A=R[1];return c(function(){w.value=x,w.getSnapshot=S,m(w)&&A({inst:w})},[b,x,S]),u(function(){return m(w)&&A({inst:w}),b(function(){m(w)&&A({inst:w})})},[b]),d(x),x}function m(b){var S=b.getSnapshot;b=b.value;try{var x=S();return!i(b,x)}catch{return!0}}function h(b,S){return S()}var v=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?h:g;return $c.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:v,$c}var Lp;function Hb(){return Lp||(Lp=1,kc.exports=zb()),kc.exports}var Lb=Hb();const $v=0,qv=1,Yv=2,Up=3;var jp=Object.prototype.hasOwnProperty;function Sf(t,a){var i,o;if(t===a)return!0;if(t&&a&&(i=t.constructor)===a.constructor){if(i===Date)return t.getTime()===a.getTime();if(i===RegExp)return t.toString()===a.toString();if(i===Array){if((o=t.length)===a.length)for(;o--&&Sf(t[o],a[o]););return o===-1}if(!i||typeof t=="object"){o=0;for(i in t)if(jp.call(t,i)&&++o&&!jp.call(a,i)||!(i in a)||!Sf(t[i],a[i]))return!1;return Object.keys(a).length===o}}return t!==t&&a!==a}const Vn=new WeakMap,gl=()=>{},Ct=gl(),ls=Object,Ne=t=>t===Ct,rn=t=>typeof t=="function",Bn=(t,a)=>({...t,...a}),Fv=t=>rn(t.then),qc={},Uo={},ad="undefined",hi=typeof window!=ad,bf=typeof document!=ad,Ub=hi&&"Deno"in window,jb=()=>hi&&typeof window.requestAnimationFrame!=ad,Xv=(t,a)=>{const i=Vn.get(t);return[()=>!Ne(a)&&t.get(a)||qc,o=>{if(!Ne(a)){const u=t.get(a);a in Uo||(Uo[a]=u),i[5](a,Bn(u,o),u||qc)}},i[6],()=>!Ne(a)&&a in Uo?Uo[a]:!Ne(a)&&t.get(a)||qc]};let xf=!0;const Vb=()=>xf,[wf,Cf]=hi&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[gl,gl],Bb=()=>{const t=bf&&document.visibilityState;return Ne(t)||t!=="hidden"},Gb=t=>(bf&&document.addEventListener("visibilitychange",t),wf("focus",t),()=>{bf&&document.removeEventListener("visibilitychange",t),Cf("focus",t)}),Pb=t=>{const a=()=>{xf=!0,t()},i=()=>{xf=!1};return wf("online",a),wf("offline",i),()=>{Cf("online",a),Cf("offline",i)}},kb={isOnline:Vb,isVisible:Bb},$b={initFocus:Gb,initReconnect:Pb},Vp=!We.useId,ci=!hi||Ub,qb=t=>jb()?window.requestAnimationFrame(t):setTimeout(t,1),Zo=ci?C.useEffect:C.useLayoutEffect,Yc=typeof navigator<"u"&&navigator.connection,Bp=!ci&&Yc&&(["slow-2g","2g"].includes(Yc.effectiveType)||Yc.saveData),jo=new WeakMap,Fc=(t,a)=>ls.prototype.toString.call(t)===`[object ${a}]`;let Yb=0;const Rf=t=>{const a=typeof t,i=Fc(t,"Date"),o=Fc(t,"RegExp"),u=Fc(t,"Object");let c,d;if(ls(t)===t&&!i&&!o){if(c=jo.get(t),c)return c;if(c=++Yb+"~",jo.set(t,c),Array.isArray(t)){for(c="@",d=0;d<t.length;d++)c+=Rf(t[d])+",";jo.set(t,c)}if(u){c="#";const g=ls.keys(t).sort();for(;!Ne(d=g.pop());)Ne(t[d])||(c+=d+":"+Rf(t[d])+",");jo.set(t,c)}}else c=i?t.toJSON():a=="symbol"?t.toString():a=="string"?JSON.stringify(t):""+t;return c},rd=t=>{if(rn(t))try{t=t()}catch{t=""}const a=t;return t=typeof t=="string"?t:(Array.isArray(t)?t.length:t)?Rf(t):"",[t,a]};let Fb=0;const Ef=()=>++Fb;async function Iv(...t){const[a,i,o,u]=t,c=Bn({populateCache:!0,throwOnError:!0},typeof u=="boolean"?{revalidate:u}:u||{});let d=c.populateCache;const g=c.rollbackOnError;let m=c.optimisticData;const h=S=>typeof g=="function"?g(S):g!==!1,v=c.throwOnError;if(rn(i)){const S=i,x=[],R=a.keys();for(const w of R)!/^\$(inf|sub)\$/.test(w)&&S(a.get(w)._k)&&x.push(w);return Promise.all(x.map(b))}return b(i);async function b(S){const[x]=rd(S);if(!x)return;const[R,w]=Xv(a,x),[A,_,D,z]=Vn.get(a),k=()=>{const se=A[x];return(rn(c.revalidate)?c.revalidate(R().data,S):c.revalidate!==!1)&&(delete D[x],delete z[x],se&&se[0])?se[0](Yv).then(()=>R().data):R().data};if(t.length<3)return k();let P=o,Z;const I=Ef();_[x]=[I,0];const q=!Ne(m),ae=R(),ce=ae.data,me=ae._c,oe=Ne(me)?ce:me;if(q&&(m=rn(m)?m(oe,ce):m,w({data:m,_c:oe})),rn(P))try{P=P(oe)}catch(se){Z=se}if(P&&Fv(P))if(P=await P.catch(se=>{Z=se}),I!==_[x][0]){if(Z)throw Z;return P}else Z&&q&&h(Z)&&(d=!0,w({data:oe,_c:Ct}));if(d&&!Z)if(rn(d)){const se=d(P,oe);w({data:se,error:Ct,_c:Ct})}else w({data:P,error:Ct,_c:Ct});if(_[x][1]=Ef(),Promise.resolve(k()).then(()=>{w({_c:Ct})}),Z){if(v)throw Z;return}return P}}const Gp=(t,a)=>{for(const i in t)t[i][0]&&t[i][0](a)},Kv=(t,a)=>{if(!Vn.has(t)){const i=Bn($b,a),o=Object.create(null),u=Iv.bind(Ct,t);let c=gl;const d=Object.create(null),g=(v,b)=>{const S=d[v]||[];return d[v]=S,S.push(b),()=>S.splice(S.indexOf(b),1)},m=(v,b,S)=>{t.set(v,b);const x=d[v];if(x)for(const R of x)R(b,S)},h=()=>{if(!Vn.has(t)&&(Vn.set(t,[o,Object.create(null),Object.create(null),Object.create(null),u,m,g]),!ci)){const v=i.initFocus(setTimeout.bind(Ct,Gp.bind(Ct,o,$v))),b=i.initReconnect(setTimeout.bind(Ct,Gp.bind(Ct,o,qv)));c=()=>{v&&v(),b&&b(),Vn.delete(t)}}};return h(),[t,u,h,c]}return[t,Vn.get(t)[4]]},Xb=(t,a,i,o,u)=>{const c=i.errorRetryCount,d=u.retryCount,g=~~((Math.random()+.5)*(1<<(d<8?d:8)))*i.errorRetryInterval;!Ne(c)&&d>c||setTimeout(o,g,u)},Ib=Sf,[id,Kb]=Kv(new Map),Zv=Bn({onLoadingSlow:gl,onSuccess:gl,onError:gl,onErrorRetry:Xb,onDiscarded:gl,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Bp?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:Bp?5e3:3e3,compare:Ib,isPaused:()=>!1,cache:id,mutate:Kb,fallback:{}},kb),Qv=(t,a)=>{const i=Bn(t,a);if(a){const{use:o,fallback:u}=t,{use:c,fallback:d}=a;o&&c&&(i.use=o.concat(c)),u&&d&&(i.fallback=Bn(u,d))}return i},_f=C.createContext({}),Zb=t=>{const{value:a}=t,i=C.useContext(_f),o=rn(a),u=C.useMemo(()=>o?a(i):a,[o,i,a]),c=C.useMemo(()=>o?u:Qv(i,u),[o,i,u]),d=u&&u.provider,g=C.useRef(Ct);d&&!g.current&&(g.current=Kv(d(c.cache||id),u));const m=g.current;return m&&(c.cache=m[0],c.mutate=m[1]),Zo(()=>{if(m)return m[2]&&m[2](),m[3]},[]),C.createElement(_f.Provider,Bn(t,{value:c}))},Qb="$inf$",Wv=hi&&window.__SWR_DEVTOOLS_USE__,Wb=Wv?window.__SWR_DEVTOOLS_USE__:[],Jb=()=>{Wv&&(window.__SWR_DEVTOOLS_REACT__=We)},ex=t=>rn(t[1])?[t[0],t[1],t[2]||{}]:[t[0],null,(t[1]===null?t[2]:t[1])||{}],tx=()=>Bn(Zv,C.useContext(_f)),nx=t=>(a,i,o)=>t(a,i&&((...c)=>{const[d]=rd(a),[,,,g]=Vn.get(id);if(d.startsWith(Qb))return i(...c);const m=g[d];return Ne(m)?i(...c):(delete g[d],m)}),o),lx=Wb.concat(nx),ax=t=>function(...i){const o=tx(),[u,c,d]=ex(i),g=Qv(o,d);let m=t;const{use:h}=g,v=(h||[]).concat(lx);for(let b=v.length;b--;)m=v[b](m);return m(u,c||g.fetcher||null,g)},rx=(t,a,i)=>{const o=a[t]||(a[t]=[]);return o.push(i),()=>{const u=o.indexOf(i);u>=0&&(o[u]=o[o.length-1],o.pop())}};Jb();const Xc=We.use||(t=>{switch(t.status){case"pending":throw t;case"fulfilled":return t.value;case"rejected":throw t.reason;default:throw t.status="pending",t.then(a=>{t.status="fulfilled",t.value=a},a=>{t.status="rejected",t.reason=a}),t}}),Ic={dedupe:!0},ix=(t,a,i)=>{const{cache:o,compare:u,suspense:c,fallbackData:d,revalidateOnMount:g,revalidateIfStale:m,refreshInterval:h,refreshWhenHidden:v,refreshWhenOffline:b,keepPreviousData:S}=i,[x,R,w,A]=Vn.get(o),[_,D]=rd(t),z=C.useRef(!1),k=C.useRef(!1),P=C.useRef(_),Z=C.useRef(a),I=C.useRef(i),q=()=>I.current,ae=()=>q().isVisible()&&q().isOnline(),[ce,me,oe,se]=Xv(o,_),fe=C.useRef({}).current,de=Ne(d)?Ne(i.fallback)?Ct:i.fallback[_]:d,H=(be,we)=>{for(const Xe in fe){const Ue=Xe;if(Ue==="data"){if(!u(be[Ue],we[Ue])&&(!Ne(be[Ue])||!u(pe,we[Ue])))return!1}else if(we[Ue]!==be[Ue])return!1}return!0},B=C.useMemo(()=>{const be=!_||!a?!1:Ne(g)?q().isPaused()||c?!1:m!==!1:g,we=st=>{const _t=Bn(st);return delete _t._k,be?{isValidating:!0,isLoading:!0,..._t}:_t},Xe=ce(),Ue=se(),Ge=we(Xe),cn=Xe===Ue?Ge:we(Ue);let Fe=Ge;return[()=>{const st=we(ce());return H(st,Fe)?(Fe.data=st.data,Fe.isLoading=st.isLoading,Fe.isValidating=st.isValidating,Fe.error=st.error,Fe):(Fe=st,st)},()=>cn]},[o,_]),j=Lb.useSyncExternalStore(C.useCallback(be=>oe(_,(we,Xe)=>{H(Xe,we)||be()}),[o,_]),B[0],B[1]),ee=!z.current,M=x[_]&&x[_].length>0,$=j.data,Q=Ne($)?de&&Fv(de)?Xc(de):de:$,K=j.error,J=C.useRef(Q),pe=S?Ne($)?Ne(J.current)?Q:J.current:$:Q,re=M&&!Ne(K)?!1:ee&&!Ne(g)?g:q().isPaused()?!1:c?Ne(Q)?!1:m:Ne(Q)||m,W=!!(_&&a&&ee&&re),ue=Ne(j.isValidating)?W:j.isValidating,ze=Ne(j.isLoading)?W:j.isLoading,xe=C.useCallback(async be=>{const we=Z.current;if(!_||!we||k.current||q().isPaused())return!1;let Xe,Ue,Ge=!0;const cn=be||{},Fe=!w[_]||!cn.dedupe,st=()=>Vp?!k.current&&_===P.current&&z.current:_===P.current,_t={isValidating:!1,isLoading:!1},Si=()=>{me(_t)},ar=()=>{const Tt=w[_];Tt&&Tt[1]===Ue&&delete w[_]},bi={isValidating:!0};Ne(ce().data)&&(bi.isLoading=!0);try{if(Fe&&(me(bi),i.loadingTimeout&&Ne(ce().data)&&setTimeout(()=>{Ge&&st()&&q().onLoadingSlow(_,i)},i.loadingTimeout),w[_]=[we(D),Ef()]),[Xe,Ue]=w[_],Xe=await Xe,Fe&&setTimeout(ar,i.dedupingInterval),!w[_]||w[_][1]!==Ue)return Fe&&st()&&q().onDiscarded(_),!1;_t.error=Ct;const Tt=R[_];if(!Ne(Tt)&&(Ue<=Tt[0]||Ue<=Tt[1]||Tt[1]===0))return Si(),Fe&&st()&&q().onDiscarded(_),!1;const Rt=ce().data;_t.data=u(Rt,Xe)?Rt:Xe,Fe&&st()&&q().onSuccess(Xe,_,i)}catch(Tt){ar();const Rt=q(),{shouldRetryOnError:gt}=Rt;Rt.isPaused()||(_t.error=Tt,Fe&&st()&&(Rt.onError(Tt,_,Rt),(gt===!0||rn(gt)&&gt(Tt))&&(!q().revalidateOnFocus||!q().revalidateOnReconnect||ae())&&Rt.onErrorRetry(Tt,_,Rt,fn=>{const mt=x[_];mt&&mt[0]&&mt[0](Up,fn)},{retryCount:(cn.retryCount||0)+1,dedupe:!0})))}return Ge=!1,Si(),!0},[_,o]),Re=C.useCallback((...be)=>Iv(o,P.current,...be),[]);if(Zo(()=>{Z.current=a,I.current=i,Ne($)||(J.current=$)}),Zo(()=>{if(!_)return;const be=xe.bind(Ct,Ic);let we=0;q().revalidateOnFocus&&(we=Date.now()+q().focusThrottleInterval);const Ue=rx(_,x,(Ge,cn={})=>{if(Ge==$v){const Fe=Date.now();q().revalidateOnFocus&&Fe>we&&ae()&&(we=Fe+q().focusThrottleInterval,be())}else if(Ge==qv)q().revalidateOnReconnect&&ae()&&be();else{if(Ge==Yv)return xe();if(Ge==Up)return xe(cn)}});return k.current=!1,P.current=_,z.current=!0,me({_k:D}),re&&(Ne(Q)||ci?be():qb(be)),()=>{k.current=!0,Ue()}},[_]),Zo(()=>{let be;function we(){const Ue=rn(h)?h(ce().data):h;Ue&&be!==-1&&(be=setTimeout(Xe,Ue))}function Xe(){!ce().error&&(v||q().isVisible())&&(b||q().isOnline())?xe(Ic).then(we):we()}return we(),()=>{be&&(clearTimeout(be),be=-1)}},[h,v,b,_]),C.useDebugValue(pe),c&&Ne(Q)&&_){if(!Vp&&ci)throw new Error("Fallback data is required when using Suspense in SSR.");Z.current=a,I.current=i,k.current=!1;const be=A[_];if(!Ne(be)){const we=Re(be);Xc(we)}if(Ne(K)){const we=xe(Ic);Ne(pe)||(we.status="fulfilled",we.value=!0),Xc(we)}else throw K}return{mutate:Re,get data(){return fe.data=!0,pe},get error(){return fe.error=!0,K},get isValidating(){return fe.isValidating=!0,ue},get isLoading(){return fe.isLoading=!0,ze}}},ox=ls.defineProperty(Zb,"defaultValue",{value:Zv}),Pp=ax(ix),sx=1e4,Tf=async(t,a)=>{const i=t,o=new AbortController,u=(a==null?void 0:a.timeout)??sx,c=setTimeout(()=>{o.abort()},u),d={...a,signal:o.signal};a!=null&&a.data&&(d.body=JSON.stringify(a.data),d.headers?typeof d.headers=="object"&&d.headers!==null&&!Object.prototype.hasOwnProperty.call(d.headers,"Content-Type")&&(d.headers instanceof Headers||(d.headers["Content-Type"]="application/json")):d.headers={"Content-Type":"application/json"});let g;try{g=await fetch(i,d)}catch(m){clearTimeout(c);const h=new Error(m.message);throw h.status=0,h.info=`Fetch error: ${m.message} (Request method: ${d.method||"unknown"}, URL: ${i.toString()})`,m.name==="AbortError"?h.isTimeoutError=!0:h.isNetworkError=!0,h}finally{clearTimeout(c)}if(!g.ok){const m=await g.text(),h=new Error(`Fetch error: ${g.status} ${g.statusText}`);throw h.status=g.status,h.info=m,g.status>=500&&(h.isServerError=!0),h}try{return await g.json()}catch(m){const h=new Error(`JSON parsing error: ${m.message}`);throw h.status=g.status,h.info=`Failed to parse JSON response: ${m.message}`,h}};function Wa(t,a){const{endpointConfig:i,key:o,pathReplace:u}=t,{data:c,timeout:d,method:g,headers:m,noRetry:h=!1,errorRetryCount:v=3,onErrorRetry:b,...S}=a||{},x={...S};if(h&&v===void 0?x.errorRetryCount=0:v!==void 0?x.errorRetryCount=v:x.errorRetryCount=2,b?x.onErrorRetry=b:x.onErrorRetry=(D,z,k,P,Z)=>{D!=null&&D.isTimeoutError&&P(Z)},!(i!=null&&i.path)&&!o)return Pp(null,()=>Tf("",{}),x);let R=o||(i==null?void 0:i.path);const w={method:g||(i==null?void 0:i.method),headers:m||(i==null?void 0:i.headers),data:c,timeout:d};u&&R&&Object.keys(u).forEach(D=>{R=R==null?void 0:R.replace(D,u[D])}),Object.keys(w).forEach(D=>{w[D]===void 0&&delete w[D]});const A=Object.keys(w).length===0?void 0:w,_=R&&t.shouldFetch!==!1?[R,A]:null;return Pp(_,D=>Tf(D[0],D[1]),x)}const Fl="en",Jv="span",qa=`<${Jv} class="ph-highlight">`,kp=`</${Jv}>`,od="__aa-highlight__",ux="__/aa-highlight__",Xa="Wiki",fs="Store",ds="Marketing",Ia="CCTV",gs="Hub",cx="Unknown",ms="WikiTopics",hl="RecentlyViewed",fx="Wiki Resources",dx="Store Products",gx="Marketing & Print",mx="CCTV People",hx="Performance Hub Features",px="Unknown",vx="Wiki Topics & Categories",yx="Recently Viewed",sd="In Stock",e0="Out of Stock",$a=Qa({language:Fl,translationMap:new Map,loading:!1,error:null}),Sx=t=>{const a=document.cookie.match(new RegExp(`(?:^|; )${t}=([^;]*)`));return a?decodeURIComponent(a[1]):null},bx=()=>{const t=Sx("UserLang");let a=Fl;if(t&&(a=t.split(/[-_]/)[0]),a===Fl){const o=new URLSearchParams(window.location.search).get("lang");o&&(a=o.split(/[-_]/)[0])}return a},xx=(t,a)=>{if(!t)return new Map;const i=new Map;return t.forEach(o=>{i.set(o[Fl].toLowerCase(),o[a]??o[Fl])}),i},wx=(t,a)=>i=>a===Fl?i:t.get(i.toLowerCase())||i;function Cx(){const t=bx();$a.language=t;const a=t===Fl?null:`/assets/translations/${t}.json`,{data:i,error:o}=Wa({key:a});a?$a.translationMap=xx(i,t):$a.translationMap=new Map,$a.loading=!1,$a.error=(o==null?void 0:o.message)??null}function hs(){const t=pl($a),{language:a}=t;return{t:C.useMemo(()=>wx(t.translationMap,a),[t.translationMap,a]),loading:t.loading,error:t.error,lang:a}}const ud="-",Rx=t=>{const a=_x(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:d=>{const g=d.split(ud);return g[0]===""&&g.length!==1&&g.shift(),t0(g,a)||Ex(d)},getConflictingClassGroupIds:(d,g)=>{const m=i[d]||[];return g&&o[d]?[...m,...o[d]]:m}}},t0=(t,a)=>{var d;if(t.length===0)return a.classGroupId;const i=t[0],o=a.nextPart.get(i),u=o?t0(t.slice(1),o):void 0;if(u)return u;if(a.validators.length===0)return;const c=t.join(ud);return(d=a.validators.find(({validator:g})=>g(c)))==null?void 0:d.classGroupId},$p=/^\[(.+)\]$/,Ex=t=>{if($p.test(t)){const a=$p.exec(t)[1],i=a==null?void 0:a.substring(0,a.indexOf(":"));if(i)return"arbitrary.."+i}},_x=t=>{const{theme:a,classGroups:i}=t,o={nextPart:new Map,validators:[]};for(const u in i)Af(i[u],o,u,a);return o},Af=(t,a,i,o)=>{t.forEach(u=>{if(typeof u=="string"){const c=u===""?a:qp(a,u);c.classGroupId=i;return}if(typeof u=="function"){if(Tx(u)){Af(u(o),a,i,o);return}a.validators.push({validator:u,classGroupId:i});return}Object.entries(u).forEach(([c,d])=>{Af(d,qp(a,c),i,o)})})},qp=(t,a)=>{let i=t;return a.split(ud).forEach(o=>{i.nextPart.has(o)||i.nextPart.set(o,{nextPart:new Map,validators:[]}),i=i.nextPart.get(o)}),i},Tx=t=>t.isThemeGetter,Ax=t=>{if(t<1)return{get:()=>{},set:()=>{}};let a=0,i=new Map,o=new Map;const u=(c,d)=>{i.set(c,d),a++,a>t&&(a=0,o=i,i=new Map)};return{get(c){let d=i.get(c);if(d!==void 0)return d;if((d=o.get(c))!==void 0)return u(c,d),d},set(c,d){i.has(c)?i.set(c,d):u(c,d)}}},Mf="!",Of=":",Mx=Of.length,Ox=t=>{const{prefix:a,experimentalParseClassName:i}=t;let o=u=>{const c=[];let d=0,g=0,m=0,h;for(let R=0;R<u.length;R++){let w=u[R];if(d===0&&g===0){if(w===Of){c.push(u.slice(m,R)),m=R+Mx;continue}if(w==="/"){h=R;continue}}w==="["?d++:w==="]"?d--:w==="("?g++:w===")"&&g--}const v=c.length===0?u:u.substring(m),b=Dx(v),S=b!==v,x=h&&h>m?h-m:void 0;return{modifiers:c,hasImportantModifier:S,baseClassName:b,maybePostfixModifierPosition:x}};if(a){const u=a+Of,c=o;o=d=>d.startsWith(u)?c(d.substring(u.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(i){const u=o;o=c=>i({className:c,parseClassName:u})}return o},Dx=t=>t.endsWith(Mf)?t.substring(0,t.length-1):t.startsWith(Mf)?t.substring(1):t,Nx=t=>{const a=Object.fromEntries(t.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const u=[];let c=[];return o.forEach(d=>{d[0]==="["||a[d]?(u.push(...c.sort(),d),c=[]):c.push(d)}),u.push(...c.sort()),u}},zx=t=>({cache:Ax(t.cacheSize),parseClassName:Ox(t),sortModifiers:Nx(t),...Rx(t)}),Hx=/\s+/,Lx=(t,a)=>{const{parseClassName:i,getClassGroupId:o,getConflictingClassGroupIds:u,sortModifiers:c}=a,d=[],g=t.trim().split(Hx);let m="";for(let h=g.length-1;h>=0;h-=1){const v=g[h],{isExternal:b,modifiers:S,hasImportantModifier:x,baseClassName:R,maybePostfixModifierPosition:w}=i(v);if(b){m=v+(m.length>0?" "+m:m);continue}let A=!!w,_=o(A?R.substring(0,w):R);if(!_){if(!A){m=v+(m.length>0?" "+m:m);continue}if(_=o(R),!_){m=v+(m.length>0?" "+m:m);continue}A=!1}const D=c(S).join(":"),z=x?D+Mf:D,k=z+_;if(d.includes(k))continue;d.push(k);const P=u(_,A);for(let Z=0;Z<P.length;++Z){const I=P[Z];d.push(z+I)}m=v+(m.length>0?" "+m:m)}return m};function Ux(){let t=0,a,i,o="";for(;t<arguments.length;)(a=arguments[t++])&&(i=n0(a))&&(o&&(o+=" "),o+=i);return o}const n0=t=>{if(typeof t=="string")return t;let a,i="";for(let o=0;o<t.length;o++)t[o]&&(a=n0(t[o]))&&(i&&(i+=" "),i+=a);return i};function jx(t,...a){let i,o,u,c=d;function d(m){const h=a.reduce((v,b)=>b(v),t());return i=zx(h),o=i.cache.get,u=i.cache.set,c=g,g(m)}function g(m){const h=o(m);if(h)return h;const v=Lx(m,i);return u(m,v),v}return function(){return c(Ux.apply(null,arguments))}}const ot=t=>{const a=i=>i[t]||[];return a.isThemeGetter=!0,a},l0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,a0=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Vx=/^\d+\/\d+$/,Bx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Gx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Px=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,kx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$x=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ja=t=>Vx.test(t),_e=t=>!!t&&!Number.isNaN(Number(t)),fl=t=>!!t&&Number.isInteger(Number(t)),Kc=t=>t.endsWith("%")&&_e(t.slice(0,-1)),Un=t=>Bx.test(t),qx=()=>!0,Yx=t=>Gx.test(t)&&!Px.test(t),r0=()=>!1,Fx=t=>kx.test(t),Xx=t=>$x.test(t),Ix=t=>!ne(t)&&!le(t),Kx=t=>Ja(t,s0,r0),ne=t=>l0.test(t),kl=t=>Ja(t,u0,Yx),Zc=t=>Ja(t,ew,_e),Yp=t=>Ja(t,i0,r0),Zx=t=>Ja(t,o0,Xx),Vo=t=>Ja(t,c0,Fx),le=t=>a0.test(t),li=t=>er(t,u0),Qx=t=>er(t,tw),Fp=t=>er(t,i0),Wx=t=>er(t,s0),Jx=t=>er(t,o0),Bo=t=>er(t,c0,!0),Ja=(t,a,i)=>{const o=l0.exec(t);return o?o[1]?a(o[1]):i(o[2]):!1},er=(t,a,i=!1)=>{const o=a0.exec(t);return o?o[1]?a(o[1]):i:!1},i0=t=>t==="position"||t==="percentage",o0=t=>t==="image"||t==="url",s0=t=>t==="length"||t==="size"||t==="bg-size",u0=t=>t==="length",ew=t=>t==="number",tw=t=>t==="family-name",c0=t=>t==="shadow",nw=()=>{const t=ot("color"),a=ot("font"),i=ot("text"),o=ot("font-weight"),u=ot("tracking"),c=ot("leading"),d=ot("breakpoint"),g=ot("container"),m=ot("spacing"),h=ot("radius"),v=ot("shadow"),b=ot("inset-shadow"),S=ot("text-shadow"),x=ot("drop-shadow"),R=ot("blur"),w=ot("perspective"),A=ot("aspect"),_=ot("ease"),D=ot("animate"),z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...k(),le,ne],Z=()=>["auto","hidden","clip","visible","scroll"],I=()=>["auto","contain","none"],q=()=>[le,ne,m],ae=()=>[ja,"full","auto",...q()],ce=()=>[fl,"none","subgrid",le,ne],me=()=>["auto",{span:["full",fl,le,ne]},fl,le,ne],oe=()=>[fl,"auto",le,ne],se=()=>["auto","min","max","fr",le,ne],fe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],de=()=>["start","end","center","stretch","center-safe","end-safe"],H=()=>["auto",...q()],B=()=>[ja,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],j=()=>[t,le,ne],ee=()=>[...k(),Fp,Yp,{position:[le,ne]}],M=()=>["no-repeat",{repeat:["","x","y","space","round"]}],$=()=>["auto","cover","contain",Wx,Kx,{size:[le,ne]}],Q=()=>[Kc,li,kl],K=()=>["","none","full",h,le,ne],J=()=>["",_e,li,kl],pe=()=>["solid","dashed","dotted","double"],re=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[_e,Kc,Fp,Yp],ue=()=>["","none",R,le,ne],ze=()=>["none",_e,le,ne],xe=()=>["none",_e,le,ne],Re=()=>[_e,le,ne],Te=()=>[ja,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Un],breakpoint:[Un],color:[qx],container:[Un],"drop-shadow":[Un],ease:["in","out","in-out"],font:[Ix],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Un],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Un],shadow:[Un],spacing:["px",_e],text:[Un],"text-shadow":[Un],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ja,ne,le,A]}],container:["container"],columns:[{columns:[_e,ne,le,g]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:Z()}],"overflow-x":[{"overflow-x":Z()}],"overflow-y":[{"overflow-y":Z()}],overscroll:[{overscroll:I()}],"overscroll-x":[{"overscroll-x":I()}],"overscroll-y":[{"overscroll-y":I()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ae()}],"inset-x":[{"inset-x":ae()}],"inset-y":[{"inset-y":ae()}],start:[{start:ae()}],end:[{end:ae()}],top:[{top:ae()}],right:[{right:ae()}],bottom:[{bottom:ae()}],left:[{left:ae()}],visibility:["visible","invisible","collapse"],z:[{z:[fl,"auto",le,ne]}],basis:[{basis:[ja,"full","auto",g,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[_e,ja,"auto","initial","none",ne]}],grow:[{grow:["",_e,le,ne]}],shrink:[{shrink:["",_e,le,ne]}],order:[{order:[fl,"first","last","none",le,ne]}],"grid-cols":[{"grid-cols":ce()}],"col-start-end":[{col:me()}],"col-start":[{"col-start":oe()}],"col-end":[{"col-end":oe()}],"grid-rows":[{"grid-rows":ce()}],"row-start-end":[{row:me()}],"row-start":[{"row-start":oe()}],"row-end":[{"row-end":oe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":se()}],"auto-rows":[{"auto-rows":se()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...fe(),"normal"]}],"justify-items":[{"justify-items":[...de(),"normal"]}],"justify-self":[{"justify-self":["auto",...de()]}],"align-content":[{content:["normal",...fe()]}],"align-items":[{items:[...de(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...de(),{baseline:["","last"]}]}],"place-content":[{"place-content":fe()}],"place-items":[{"place-items":[...de(),"baseline"]}],"place-self":[{"place-self":["auto",...de()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:H()}],mx:[{mx:H()}],my:[{my:H()}],ms:[{ms:H()}],me:[{me:H()}],mt:[{mt:H()}],mr:[{mr:H()}],mb:[{mb:H()}],ml:[{ml:H()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:B()}],w:[{w:[g,"screen",...B()]}],"min-w":[{"min-w":[g,"screen","none",...B()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[d]},...B()]}],h:[{h:["screen",...B()]}],"min-h":[{"min-h":["screen","none",...B()]}],"max-h":[{"max-h":["screen",...B()]}],"font-size":[{text:["base",i,li,kl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,le,Zc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Kc,ne]}],"font-family":[{font:[Qx,ne,a]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[u,le,ne]}],"line-clamp":[{"line-clamp":[_e,"none",le,Zc]}],leading:[{leading:[c,...q()]}],"list-image":[{"list-image":["none",le,ne]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",le,ne]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pe(),"wavy"]}],"text-decoration-thickness":[{decoration:[_e,"from-font","auto",le,kl]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[_e,"auto",le,ne]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",le,ne]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",le,ne]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ee()}],"bg-repeat":[{bg:M()}],"bg-size":[{bg:$()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},fl,le,ne],radial:["",le,ne],conic:[fl,le,ne]},Jx,Zx]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:Q()}],"gradient-via-pos":[{via:Q()}],"gradient-to-pos":[{to:Q()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:K()}],"rounded-s":[{"rounded-s":K()}],"rounded-e":[{"rounded-e":K()}],"rounded-t":[{"rounded-t":K()}],"rounded-r":[{"rounded-r":K()}],"rounded-b":[{"rounded-b":K()}],"rounded-l":[{"rounded-l":K()}],"rounded-ss":[{"rounded-ss":K()}],"rounded-se":[{"rounded-se":K()}],"rounded-ee":[{"rounded-ee":K()}],"rounded-es":[{"rounded-es":K()}],"rounded-tl":[{"rounded-tl":K()}],"rounded-tr":[{"rounded-tr":K()}],"rounded-br":[{"rounded-br":K()}],"rounded-bl":[{"rounded-bl":K()}],"border-w":[{border:J()}],"border-w-x":[{"border-x":J()}],"border-w-y":[{"border-y":J()}],"border-w-s":[{"border-s":J()}],"border-w-e":[{"border-e":J()}],"border-w-t":[{"border-t":J()}],"border-w-r":[{"border-r":J()}],"border-w-b":[{"border-b":J()}],"border-w-l":[{"border-l":J()}],"divide-x":[{"divide-x":J()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":J()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...pe(),"hidden","none"]}],"divide-style":[{divide:[...pe(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...pe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[_e,le,ne]}],"outline-w":[{outline:["",_e,li,kl]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",v,Bo,Vo]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",b,Bo,Vo]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:J()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[_e,kl]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":J()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",S,Bo,Vo]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[_e,le,ne]}],"mix-blend":[{"mix-blend":[...re(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":re()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[_e]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[le,ne]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[_e]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ee()}],"mask-repeat":[{mask:M()}],"mask-size":[{mask:$()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",le,ne]}],filter:[{filter:["","none",le,ne]}],blur:[{blur:ue()}],brightness:[{brightness:[_e,le,ne]}],contrast:[{contrast:[_e,le,ne]}],"drop-shadow":[{"drop-shadow":["","none",x,Bo,Vo]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",_e,le,ne]}],"hue-rotate":[{"hue-rotate":[_e,le,ne]}],invert:[{invert:["",_e,le,ne]}],saturate:[{saturate:[_e,le,ne]}],sepia:[{sepia:["",_e,le,ne]}],"backdrop-filter":[{"backdrop-filter":["","none",le,ne]}],"backdrop-blur":[{"backdrop-blur":ue()}],"backdrop-brightness":[{"backdrop-brightness":[_e,le,ne]}],"backdrop-contrast":[{"backdrop-contrast":[_e,le,ne]}],"backdrop-grayscale":[{"backdrop-grayscale":["",_e,le,ne]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[_e,le,ne]}],"backdrop-invert":[{"backdrop-invert":["",_e,le,ne]}],"backdrop-opacity":[{"backdrop-opacity":[_e,le,ne]}],"backdrop-saturate":[{"backdrop-saturate":[_e,le,ne]}],"backdrop-sepia":[{"backdrop-sepia":["",_e,le,ne]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",le,ne]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[_e,"initial",le,ne]}],ease:[{ease:["linear","initial",_,le,ne]}],delay:[{delay:[_e,le,ne]}],animate:[{animate:["none",D,le,ne]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[w,le,ne]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:ze()}],"rotate-x":[{"rotate-x":ze()}],"rotate-y":[{"rotate-y":ze()}],"rotate-z":[{"rotate-z":ze()}],scale:[{scale:xe()}],"scale-x":[{"scale-x":xe()}],"scale-y":[{"scale-y":xe()}],"scale-z":[{"scale-z":xe()}],"scale-3d":["scale-3d"],skew:[{skew:Re()}],"skew-x":[{"skew-x":Re()}],"skew-y":[{"skew-y":Re()}],transform:[{transform:[le,ne,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Te()}],"translate-x":[{"translate-x":Te()}],"translate-y":[{"translate-y":Te()}],"translate-z":[{"translate-z":Te()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",le,ne]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",le,ne]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[_e,li,kl,Zc]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},lw=jx(nw);function f0(t){var a,i,o="";if(typeof t=="string"||typeof t=="number")o+=t;else if(typeof t=="object")if(Array.isArray(t)){var u=t.length;for(a=0;a<u;a++)t[a]&&(i=f0(t[a]))&&(o&&(o+=" "),o+=i)}else for(i in t)t[i]&&(o&&(o+=" "),o+=i);return o}function aw(){for(var t,a,i=0,o="",u=arguments.length;i<u;i++)(t=arguments[i])&&(a=f0(t))&&(o&&(o+=" "),o+=a);return o}const Be=(...t)=>lw(aw(t)),rw=({title:t,subtitle:a,className:i=""})=>{const{t:o}=hs();return E.jsxs("header",{className:Be("flex flex-col text-start",i),children:[E.jsx("h1",{className:"text-2xl font-semibold text-fg-header",children:o(t)}),a&&E.jsx("div",{className:"text-md text-start text-fg-body",children:o(a)})]})};/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function pi(){return{accessor:(t,a)=>typeof t=="function"?{...a,accessorFn:t}:{...a,accessorKey:t},display:t=>t,group:t=>t}}function ml(t,a){return typeof t=="function"?t(a):t}function $t(t,a){return i=>{a.setState(o=>({...o,[t]:ml(i,o[t])}))}}function ps(t){return t instanceof Function}function iw(t){return Array.isArray(t)&&t.every(a=>typeof a=="number")}function ow(t,a){const i=[],o=u=>{u.forEach(c=>{i.push(c);const d=a(c);d!=null&&d.length&&o(d)})};return o(t),i}function ye(t,a,i){let o=[],u;return c=>{let d;i.key&&i.debug&&(d=Date.now());const g=t(c);if(!(g.length!==o.length||g.some((v,b)=>o[b]!==v)))return u;o=g;let h;if(i.key&&i.debug&&(h=Date.now()),u=a(...g),i==null||i.onChange==null||i.onChange(u),i.key&&i.debug&&i!=null&&i.debug()){const v=Math.round((Date.now()-d)*100)/100,b=Math.round((Date.now()-h)*100)/100,S=b/16,x=(R,w)=>{for(R=String(R);R.length<w;)R=" "+R;return R};console.info(`%c⏱ ${x(b,5)} /${x(v,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*S,120))}deg 100% 31%);`,i==null?void 0:i.key)}return u}}function Se(t,a,i,o){return{debug:()=>{var u;return(u=t==null?void 0:t.debugAll)!=null?u:t[a]},key:!1,onChange:o}}function sw(t,a,i,o){const u=()=>{var d;return(d=c.getValue())!=null?d:t.options.renderFallbackValue},c={id:`${a.id}_${i.id}`,row:a,column:i,getValue:()=>a.getValue(o),renderValue:u,getContext:ye(()=>[t,i,a,c],(d,g,m,h)=>({table:d,column:g,row:m,cell:h,getValue:h.getValue,renderValue:h.renderValue}),Se(t.options,"debugCells"))};return t._features.forEach(d=>{d.createCell==null||d.createCell(c,i,a,t)},{}),c}function uw(t,a,i,o){var u,c;const g={...t._getDefaultColumnDef(),...a},m=g.accessorKey;let h=(u=(c=g.id)!=null?c:m?typeof String.prototype.replaceAll=="function"?m.replaceAll(".","_"):m.replace(/\./g,"_"):void 0)!=null?u:typeof g.header=="string"?g.header:void 0,v;if(g.accessorFn?v=g.accessorFn:m&&(m.includes(".")?v=S=>{let x=S;for(const w of m.split(".")){var R;x=(R=x)==null?void 0:R[w]}return x}:v=S=>S[g.accessorKey]),!h)throw new Error;let b={id:`${String(h)}`,accessorFn:v,parent:o,depth:i,columnDef:g,columns:[],getFlatColumns:ye(()=>[!0],()=>{var S;return[b,...(S=b.columns)==null?void 0:S.flatMap(x=>x.getFlatColumns())]},Se(t.options,"debugColumns")),getLeafColumns:ye(()=>[t._getOrderColumnsFn()],S=>{var x;if((x=b.columns)!=null&&x.length){let R=b.columns.flatMap(w=>w.getLeafColumns());return S(R)}return[b]},Se(t.options,"debugColumns"))};for(const S of t._features)S.createColumn==null||S.createColumn(b,t);return b}const wt="debugHeaders";function Xp(t,a,i){var o;let c={id:(o=i.id)!=null?o:a.id,column:a,index:i.index,isPlaceholder:!!i.isPlaceholder,placeholderId:i.placeholderId,depth:i.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const d=[],g=m=>{m.subHeaders&&m.subHeaders.length&&m.subHeaders.map(g),d.push(m)};return g(c),d},getContext:()=>({table:t,header:c,column:a})};return t._features.forEach(d=>{d.createHeader==null||d.createHeader(c,t)}),c}const cw={createTable:t=>{t.getHeaderGroups=ye(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.left,t.getState().columnPinning.right],(a,i,o,u)=>{var c,d;const g=(c=o==null?void 0:o.map(b=>i.find(S=>S.id===b)).filter(Boolean))!=null?c:[],m=(d=u==null?void 0:u.map(b=>i.find(S=>S.id===b)).filter(Boolean))!=null?d:[],h=i.filter(b=>!(o!=null&&o.includes(b.id))&&!(u!=null&&u.includes(b.id)));return Go(a,[...g,...h,...m],t)},Se(t.options,wt)),t.getCenterHeaderGroups=ye(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.left,t.getState().columnPinning.right],(a,i,o,u)=>(i=i.filter(c=>!(o!=null&&o.includes(c.id))&&!(u!=null&&u.includes(c.id))),Go(a,i,t,"center")),Se(t.options,wt)),t.getLeftHeaderGroups=ye(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.left],(a,i,o)=>{var u;const c=(u=o==null?void 0:o.map(d=>i.find(g=>g.id===d)).filter(Boolean))!=null?u:[];return Go(a,c,t,"left")},Se(t.options,wt)),t.getRightHeaderGroups=ye(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.right],(a,i,o)=>{var u;const c=(u=o==null?void 0:o.map(d=>i.find(g=>g.id===d)).filter(Boolean))!=null?u:[];return Go(a,c,t,"right")},Se(t.options,wt)),t.getFooterGroups=ye(()=>[t.getHeaderGroups()],a=>[...a].reverse(),Se(t.options,wt)),t.getLeftFooterGroups=ye(()=>[t.getLeftHeaderGroups()],a=>[...a].reverse(),Se(t.options,wt)),t.getCenterFooterGroups=ye(()=>[t.getCenterHeaderGroups()],a=>[...a].reverse(),Se(t.options,wt)),t.getRightFooterGroups=ye(()=>[t.getRightHeaderGroups()],a=>[...a].reverse(),Se(t.options,wt)),t.getFlatHeaders=ye(()=>[t.getHeaderGroups()],a=>a.map(i=>i.headers).flat(),Se(t.options,wt)),t.getLeftFlatHeaders=ye(()=>[t.getLeftHeaderGroups()],a=>a.map(i=>i.headers).flat(),Se(t.options,wt)),t.getCenterFlatHeaders=ye(()=>[t.getCenterHeaderGroups()],a=>a.map(i=>i.headers).flat(),Se(t.options,wt)),t.getRightFlatHeaders=ye(()=>[t.getRightHeaderGroups()],a=>a.map(i=>i.headers).flat(),Se(t.options,wt)),t.getCenterLeafHeaders=ye(()=>[t.getCenterFlatHeaders()],a=>a.filter(i=>{var o;return!((o=i.subHeaders)!=null&&o.length)}),Se(t.options,wt)),t.getLeftLeafHeaders=ye(()=>[t.getLeftFlatHeaders()],a=>a.filter(i=>{var o;return!((o=i.subHeaders)!=null&&o.length)}),Se(t.options,wt)),t.getRightLeafHeaders=ye(()=>[t.getRightFlatHeaders()],a=>a.filter(i=>{var o;return!((o=i.subHeaders)!=null&&o.length)}),Se(t.options,wt)),t.getLeafHeaders=ye(()=>[t.getLeftHeaderGroups(),t.getCenterHeaderGroups(),t.getRightHeaderGroups()],(a,i,o)=>{var u,c,d,g,m,h;return[...(u=(c=a[0])==null?void 0:c.headers)!=null?u:[],...(d=(g=i[0])==null?void 0:g.headers)!=null?d:[],...(m=(h=o[0])==null?void 0:h.headers)!=null?m:[]].map(v=>v.getLeafHeaders()).flat()},Se(t.options,wt))}};function Go(t,a,i,o){var u,c;let d=0;const g=function(S,x){x===void 0&&(x=1),d=Math.max(d,x),S.filter(R=>R.getIsVisible()).forEach(R=>{var w;(w=R.columns)!=null&&w.length&&g(R.columns,x+1)},0)};g(t);let m=[];const h=(S,x)=>{const R={depth:x,id:[o,`${x}`].filter(Boolean).join("_"),headers:[]},w=[];S.forEach(A=>{const _=[...w].reverse()[0],D=A.column.depth===R.depth;let z,k=!1;if(D&&A.column.parent?z=A.column.parent:(z=A.column,k=!0),_&&(_==null?void 0:_.column)===z)_.subHeaders.push(A);else{const P=Xp(i,z,{id:[o,x,z.id,A==null?void 0:A.id].filter(Boolean).join("_"),isPlaceholder:k,placeholderId:k?`${w.filter(Z=>Z.column===z).length}`:void 0,depth:x,index:w.length});P.subHeaders.push(A),w.push(P)}R.headers.push(A),A.headerGroup=R}),m.push(R),x>0&&h(w,x-1)},v=a.map((S,x)=>Xp(i,S,{depth:d,index:x}));h(v,d-1),m.reverse();const b=S=>S.filter(R=>R.column.getIsVisible()).map(R=>{let w=0,A=0,_=[0];R.subHeaders&&R.subHeaders.length?(_=[],b(R.subHeaders).forEach(z=>{let{colSpan:k,rowSpan:P}=z;w+=k,_.push(P)})):w=1;const D=Math.min(..._);return A=A+D,R.colSpan=w,R.rowSpan=A,{colSpan:w,rowSpan:A}});return b((u=(c=m[0])==null?void 0:c.headers)!=null?u:[]),m}const fw=(t,a,i,o,u,c,d)=>{let g={id:a,index:o,original:i,depth:u,parentId:d,_valuesCache:{},_uniqueValuesCache:{},getValue:m=>{if(g._valuesCache.hasOwnProperty(m))return g._valuesCache[m];const h=t.getColumn(m);if(h!=null&&h.accessorFn)return g._valuesCache[m]=h.accessorFn(g.original,o),g._valuesCache[m]},getUniqueValues:m=>{if(g._uniqueValuesCache.hasOwnProperty(m))return g._uniqueValuesCache[m];const h=t.getColumn(m);if(h!=null&&h.accessorFn)return h.columnDef.getUniqueValues?(g._uniqueValuesCache[m]=h.columnDef.getUniqueValues(g.original,o),g._uniqueValuesCache[m]):(g._uniqueValuesCache[m]=[g.getValue(m)],g._uniqueValuesCache[m])},renderValue:m=>{var h;return(h=g.getValue(m))!=null?h:t.options.renderFallbackValue},subRows:[],getLeafRows:()=>ow(g.subRows,m=>m.subRows),getParentRow:()=>g.parentId?t.getRow(g.parentId,!0):void 0,getParentRows:()=>{let m=[],h=g;for(;;){const v=h.getParentRow();if(!v)break;m.push(v),h=v}return m.reverse()},getAllCells:ye(()=>[t.getAllLeafColumns()],m=>m.map(h=>sw(t,g,h,h.id)),Se(t.options,"debugRows")),_getAllCellsByColumnId:ye(()=>[g.getAllCells()],m=>m.reduce((h,v)=>(h[v.column.id]=v,h),{}),Se(t.options,"debugRows"))};for(let m=0;m<t._features.length;m++){const h=t._features[m];h==null||h.createRow==null||h.createRow(g,t)}return g},dw={createColumn:(t,a)=>{t._getFacetedRowModel=a.options.getFacetedRowModel&&a.options.getFacetedRowModel(a,t.id),t.getFacetedRowModel=()=>t._getFacetedRowModel?t._getFacetedRowModel():a.getPreFilteredRowModel(),t._getFacetedUniqueValues=a.options.getFacetedUniqueValues&&a.options.getFacetedUniqueValues(a,t.id),t.getFacetedUniqueValues=()=>t._getFacetedUniqueValues?t._getFacetedUniqueValues():new Map,t._getFacetedMinMaxValues=a.options.getFacetedMinMaxValues&&a.options.getFacetedMinMaxValues(a,t.id),t.getFacetedMinMaxValues=()=>{if(t._getFacetedMinMaxValues)return t._getFacetedMinMaxValues()}}},d0=(t,a,i)=>{var o,u;const c=i==null||(o=i.toString())==null?void 0:o.toLowerCase();return!!(!((u=t.getValue(a))==null||(u=u.toString())==null||(u=u.toLowerCase())==null)&&u.includes(c))};d0.autoRemove=t=>on(t);const g0=(t,a,i)=>{var o;return!!(!((o=t.getValue(a))==null||(o=o.toString())==null)&&o.includes(i))};g0.autoRemove=t=>on(t);const m0=(t,a,i)=>{var o;return((o=t.getValue(a))==null||(o=o.toString())==null?void 0:o.toLowerCase())===(i==null?void 0:i.toLowerCase())};m0.autoRemove=t=>on(t);const h0=(t,a,i)=>{var o;return(o=t.getValue(a))==null?void 0:o.includes(i)};h0.autoRemove=t=>on(t);const p0=(t,a,i)=>!i.some(o=>{var u;return!((u=t.getValue(a))!=null&&u.includes(o))});p0.autoRemove=t=>on(t)||!(t!=null&&t.length);const v0=(t,a,i)=>i.some(o=>{var u;return(u=t.getValue(a))==null?void 0:u.includes(o)});v0.autoRemove=t=>on(t)||!(t!=null&&t.length);const y0=(t,a,i)=>t.getValue(a)===i;y0.autoRemove=t=>on(t);const S0=(t,a,i)=>t.getValue(a)==i;S0.autoRemove=t=>on(t);const cd=(t,a,i)=>{let[o,u]=i;const c=t.getValue(a);return c>=o&&c<=u};cd.resolveFilterValue=t=>{let[a,i]=t,o=typeof a!="number"?parseFloat(a):a,u=typeof i!="number"?parseFloat(i):i,c=a===null||Number.isNaN(o)?-1/0:o,d=i===null||Number.isNaN(u)?1/0:u;if(c>d){const g=c;c=d,d=g}return[c,d]};cd.autoRemove=t=>on(t)||on(t[0])&&on(t[1]);const jn={includesString:d0,includesStringSensitive:g0,equalsString:m0,arrIncludes:h0,arrIncludesAll:p0,arrIncludesSome:v0,equals:y0,weakEquals:S0,inNumberRange:cd};function on(t){return t==null||t===""}const gw={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:t=>({columnFilters:[],...t}),getDefaultOptions:t=>({onColumnFiltersChange:$t("columnFilters",t),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(t,a)=>{t.getAutoFilterFn=()=>{const i=a.getCoreRowModel().flatRows[0],o=i==null?void 0:i.getValue(t.id);return typeof o=="string"?jn.includesString:typeof o=="number"?jn.inNumberRange:typeof o=="boolean"||o!==null&&typeof o=="object"?jn.equals:Array.isArray(o)?jn.arrIncludes:jn.weakEquals},t.getFilterFn=()=>{var i,o;return ps(t.columnDef.filterFn)?t.columnDef.filterFn:t.columnDef.filterFn==="auto"?t.getAutoFilterFn():(i=(o=a.options.filterFns)==null?void 0:o[t.columnDef.filterFn])!=null?i:jn[t.columnDef.filterFn]},t.getCanFilter=()=>{var i,o,u;return((i=t.columnDef.enableColumnFilter)!=null?i:!0)&&((o=a.options.enableColumnFilters)!=null?o:!0)&&((u=a.options.enableFilters)!=null?u:!0)&&!!t.accessorFn},t.getIsFiltered=()=>t.getFilterIndex()>-1,t.getFilterValue=()=>{var i;return(i=a.getState().columnFilters)==null||(i=i.find(o=>o.id===t.id))==null?void 0:i.value},t.getFilterIndex=()=>{var i,o;return(i=(o=a.getState().columnFilters)==null?void 0:o.findIndex(u=>u.id===t.id))!=null?i:-1},t.setFilterValue=i=>{a.setColumnFilters(o=>{const u=t.getFilterFn(),c=o==null?void 0:o.find(v=>v.id===t.id),d=ml(i,c?c.value:void 0);if(Ip(u,d,t)){var g;return(g=o==null?void 0:o.filter(v=>v.id!==t.id))!=null?g:[]}const m={id:t.id,value:d};if(c){var h;return(h=o==null?void 0:o.map(v=>v.id===t.id?m:v))!=null?h:[]}return o!=null&&o.length?[...o,m]:[m]})}},createRow:(t,a)=>{t.columnFilters={},t.columnFiltersMeta={}},createTable:t=>{t.setColumnFilters=a=>{const i=t.getAllLeafColumns(),o=u=>{var c;return(c=ml(a,u))==null?void 0:c.filter(d=>{const g=i.find(m=>m.id===d.id);if(g){const m=g.getFilterFn();if(Ip(m,d.value,g))return!1}return!0})};t.options.onColumnFiltersChange==null||t.options.onColumnFiltersChange(o)},t.resetColumnFilters=a=>{var i,o;t.setColumnFilters(a?[]:(i=(o=t.initialState)==null?void 0:o.columnFilters)!=null?i:[])},t.getPreFilteredRowModel=()=>t.getCoreRowModel(),t.getFilteredRowModel=()=>(!t._getFilteredRowModel&&t.options.getFilteredRowModel&&(t._getFilteredRowModel=t.options.getFilteredRowModel(t)),t.options.manualFiltering||!t._getFilteredRowModel?t.getPreFilteredRowModel():t._getFilteredRowModel())}};function Ip(t,a,i){return(t&&t.autoRemove?t.autoRemove(a,i):!1)||typeof a>"u"||typeof a=="string"&&!a}const mw=(t,a,i)=>i.reduce((o,u)=>{const c=u.getValue(t);return o+(typeof c=="number"?c:0)},0),hw=(t,a,i)=>{let o;return i.forEach(u=>{const c=u.getValue(t);c!=null&&(o>c||o===void 0&&c>=c)&&(o=c)}),o},pw=(t,a,i)=>{let o;return i.forEach(u=>{const c=u.getValue(t);c!=null&&(o<c||o===void 0&&c>=c)&&(o=c)}),o},vw=(t,a,i)=>{let o,u;return i.forEach(c=>{const d=c.getValue(t);d!=null&&(o===void 0?d>=d&&(o=u=d):(o>d&&(o=d),u<d&&(u=d)))}),[o,u]},yw=(t,a)=>{let i=0,o=0;if(a.forEach(u=>{let c=u.getValue(t);c!=null&&(c=+c)>=c&&(++i,o+=c)}),i)return o/i},Sw=(t,a)=>{if(!a.length)return;const i=a.map(c=>c.getValue(t));if(!iw(i))return;if(i.length===1)return i[0];const o=Math.floor(i.length/2),u=i.sort((c,d)=>c-d);return i.length%2!==0?u[o]:(u[o-1]+u[o])/2},bw=(t,a)=>Array.from(new Set(a.map(i=>i.getValue(t))).values()),xw=(t,a)=>new Set(a.map(i=>i.getValue(t))).size,ww=(t,a)=>a.length,Qc={sum:mw,min:hw,max:pw,extent:vw,mean:yw,median:Sw,unique:bw,uniqueCount:xw,count:ww},Cw={getDefaultColumnDef:()=>({aggregatedCell:t=>{var a,i;return(a=(i=t.getValue())==null||i.toString==null?void 0:i.toString())!=null?a:null},aggregationFn:"auto"}),getInitialState:t=>({grouping:[],...t}),getDefaultOptions:t=>({onGroupingChange:$t("grouping",t),groupedColumnMode:"reorder"}),createColumn:(t,a)=>{t.toggleGrouping=()=>{a.setGrouping(i=>i!=null&&i.includes(t.id)?i.filter(o=>o!==t.id):[...i??[],t.id])},t.getCanGroup=()=>{var i,o;return((i=t.columnDef.enableGrouping)!=null?i:!0)&&((o=a.options.enableGrouping)!=null?o:!0)&&(!!t.accessorFn||!!t.columnDef.getGroupingValue)},t.getIsGrouped=()=>{var i;return(i=a.getState().grouping)==null?void 0:i.includes(t.id)},t.getGroupedIndex=()=>{var i;return(i=a.getState().grouping)==null?void 0:i.indexOf(t.id)},t.getToggleGroupingHandler=()=>{const i=t.getCanGroup();return()=>{i&&t.toggleGrouping()}},t.getAutoAggregationFn=()=>{const i=a.getCoreRowModel().flatRows[0],o=i==null?void 0:i.getValue(t.id);if(typeof o=="number")return Qc.sum;if(Object.prototype.toString.call(o)==="[object Date]")return Qc.extent},t.getAggregationFn=()=>{var i,o;if(!t)throw new Error;return ps(t.columnDef.aggregationFn)?t.columnDef.aggregationFn:t.columnDef.aggregationFn==="auto"?t.getAutoAggregationFn():(i=(o=a.options.aggregationFns)==null?void 0:o[t.columnDef.aggregationFn])!=null?i:Qc[t.columnDef.aggregationFn]}},createTable:t=>{t.setGrouping=a=>t.options.onGroupingChange==null?void 0:t.options.onGroupingChange(a),t.resetGrouping=a=>{var i,o;t.setGrouping(a?[]:(i=(o=t.initialState)==null?void 0:o.grouping)!=null?i:[])},t.getPreGroupedRowModel=()=>t.getFilteredRowModel(),t.getGroupedRowModel=()=>(!t._getGroupedRowModel&&t.options.getGroupedRowModel&&(t._getGroupedRowModel=t.options.getGroupedRowModel(t)),t.options.manualGrouping||!t._getGroupedRowModel?t.getPreGroupedRowModel():t._getGroupedRowModel())},createRow:(t,a)=>{t.getIsGrouped=()=>!!t.groupingColumnId,t.getGroupingValue=i=>{if(t._groupingValuesCache.hasOwnProperty(i))return t._groupingValuesCache[i];const o=a.getColumn(i);return o!=null&&o.columnDef.getGroupingValue?(t._groupingValuesCache[i]=o.columnDef.getGroupingValue(t.original),t._groupingValuesCache[i]):t.getValue(i)},t._groupingValuesCache={}},createCell:(t,a,i,o)=>{t.getIsGrouped=()=>a.getIsGrouped()&&a.id===i.groupingColumnId,t.getIsPlaceholder=()=>!t.getIsGrouped()&&a.getIsGrouped(),t.getIsAggregated=()=>{var u;return!t.getIsGrouped()&&!t.getIsPlaceholder()&&!!((u=i.subRows)!=null&&u.length)}}};function Rw(t,a,i){if(!(a!=null&&a.length)||!i)return t;const o=t.filter(c=>!a.includes(c.id));return i==="remove"?o:[...a.map(c=>t.find(d=>d.id===c)).filter(Boolean),...o]}const Ew={getInitialState:t=>({columnOrder:[],...t}),getDefaultOptions:t=>({onColumnOrderChange:$t("columnOrder",t)}),createColumn:(t,a)=>{t.getIndex=ye(i=>[ui(a,i)],i=>i.findIndex(o=>o.id===t.id),Se(a.options,"debugColumns")),t.getIsFirstColumn=i=>{var o;return((o=ui(a,i)[0])==null?void 0:o.id)===t.id},t.getIsLastColumn=i=>{var o;const u=ui(a,i);return((o=u[u.length-1])==null?void 0:o.id)===t.id}},createTable:t=>{t.setColumnOrder=a=>t.options.onColumnOrderChange==null?void 0:t.options.onColumnOrderChange(a),t.resetColumnOrder=a=>{var i;t.setColumnOrder(a?[]:(i=t.initialState.columnOrder)!=null?i:[])},t._getOrderColumnsFn=ye(()=>[t.getState().columnOrder,t.getState().grouping,t.options.groupedColumnMode],(a,i,o)=>u=>{let c=[];if(!(a!=null&&a.length))c=u;else{const d=[...a],g=[...u];for(;g.length&&d.length;){const m=d.shift(),h=g.findIndex(v=>v.id===m);h>-1&&c.push(g.splice(h,1)[0])}c=[...c,...g]}return Rw(c,i,o)},Se(t.options,"debugTable"))}},Wc=()=>({left:[],right:[]}),_w={getInitialState:t=>({columnPinning:Wc(),...t}),getDefaultOptions:t=>({onColumnPinningChange:$t("columnPinning",t)}),createColumn:(t,a)=>{t.pin=i=>{const o=t.getLeafColumns().map(u=>u.id).filter(Boolean);a.setColumnPinning(u=>{var c,d;if(i==="right"){var g,m;return{left:((g=u==null?void 0:u.left)!=null?g:[]).filter(b=>!(o!=null&&o.includes(b))),right:[...((m=u==null?void 0:u.right)!=null?m:[]).filter(b=>!(o!=null&&o.includes(b))),...o]}}if(i==="left"){var h,v;return{left:[...((h=u==null?void 0:u.left)!=null?h:[]).filter(b=>!(o!=null&&o.includes(b))),...o],right:((v=u==null?void 0:u.right)!=null?v:[]).filter(b=>!(o!=null&&o.includes(b)))}}return{left:((c=u==null?void 0:u.left)!=null?c:[]).filter(b=>!(o!=null&&o.includes(b))),right:((d=u==null?void 0:u.right)!=null?d:[]).filter(b=>!(o!=null&&o.includes(b)))}})},t.getCanPin=()=>t.getLeafColumns().some(o=>{var u,c,d;return((u=o.columnDef.enablePinning)!=null?u:!0)&&((c=(d=a.options.enableColumnPinning)!=null?d:a.options.enablePinning)!=null?c:!0)}),t.getIsPinned=()=>{const i=t.getLeafColumns().map(g=>g.id),{left:o,right:u}=a.getState().columnPinning,c=i.some(g=>o==null?void 0:o.includes(g)),d=i.some(g=>u==null?void 0:u.includes(g));return c?"left":d?"right":!1},t.getPinnedIndex=()=>{var i,o;const u=t.getIsPinned();return u?(i=(o=a.getState().columnPinning)==null||(o=o[u])==null?void 0:o.indexOf(t.id))!=null?i:-1:0}},createRow:(t,a)=>{t.getCenterVisibleCells=ye(()=>[t._getAllVisibleCells(),a.getState().columnPinning.left,a.getState().columnPinning.right],(i,o,u)=>{const c=[...o??[],...u??[]];return i.filter(d=>!c.includes(d.column.id))},Se(a.options,"debugRows")),t.getLeftVisibleCells=ye(()=>[t._getAllVisibleCells(),a.getState().columnPinning.left],(i,o)=>(o??[]).map(c=>i.find(d=>d.column.id===c)).filter(Boolean).map(c=>({...c,position:"left"})),Se(a.options,"debugRows")),t.getRightVisibleCells=ye(()=>[t._getAllVisibleCells(),a.getState().columnPinning.right],(i,o)=>(o??[]).map(c=>i.find(d=>d.column.id===c)).filter(Boolean).map(c=>({...c,position:"right"})),Se(a.options,"debugRows"))},createTable:t=>{t.setColumnPinning=a=>t.options.onColumnPinningChange==null?void 0:t.options.onColumnPinningChange(a),t.resetColumnPinning=a=>{var i,o;return t.setColumnPinning(a?Wc():(i=(o=t.initialState)==null?void 0:o.columnPinning)!=null?i:Wc())},t.getIsSomeColumnsPinned=a=>{var i;const o=t.getState().columnPinning;if(!a){var u,c;return!!((u=o.left)!=null&&u.length||(c=o.right)!=null&&c.length)}return!!((i=o[a])!=null&&i.length)},t.getLeftLeafColumns=ye(()=>[t.getAllLeafColumns(),t.getState().columnPinning.left],(a,i)=>(i??[]).map(o=>a.find(u=>u.id===o)).filter(Boolean),Se(t.options,"debugColumns")),t.getRightLeafColumns=ye(()=>[t.getAllLeafColumns(),t.getState().columnPinning.right],(a,i)=>(i??[]).map(o=>a.find(u=>u.id===o)).filter(Boolean),Se(t.options,"debugColumns")),t.getCenterLeafColumns=ye(()=>[t.getAllLeafColumns(),t.getState().columnPinning.left,t.getState().columnPinning.right],(a,i,o)=>{const u=[...i??[],...o??[]];return a.filter(c=>!u.includes(c.id))},Se(t.options,"debugColumns"))}};function Tw(t){return t||(typeof document<"u"?document:null)}const Po={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},Jc=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Aw={getDefaultColumnDef:()=>Po,getInitialState:t=>({columnSizing:{},columnSizingInfo:Jc(),...t}),getDefaultOptions:t=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:$t("columnSizing",t),onColumnSizingInfoChange:$t("columnSizingInfo",t)}),createColumn:(t,a)=>{t.getSize=()=>{var i,o,u;const c=a.getState().columnSizing[t.id];return Math.min(Math.max((i=t.columnDef.minSize)!=null?i:Po.minSize,(o=c??t.columnDef.size)!=null?o:Po.size),(u=t.columnDef.maxSize)!=null?u:Po.maxSize)},t.getStart=ye(i=>[i,ui(a,i),a.getState().columnSizing],(i,o)=>o.slice(0,t.getIndex(i)).reduce((u,c)=>u+c.getSize(),0),Se(a.options,"debugColumns")),t.getAfter=ye(i=>[i,ui(a,i),a.getState().columnSizing],(i,o)=>o.slice(t.getIndex(i)+1).reduce((u,c)=>u+c.getSize(),0),Se(a.options,"debugColumns")),t.resetSize=()=>{a.setColumnSizing(i=>{let{[t.id]:o,...u}=i;return u})},t.getCanResize=()=>{var i,o;return((i=t.columnDef.enableResizing)!=null?i:!0)&&((o=a.options.enableColumnResizing)!=null?o:!0)},t.getIsResizing=()=>a.getState().columnSizingInfo.isResizingColumn===t.id},createHeader:(t,a)=>{t.getSize=()=>{let i=0;const o=u=>{if(u.subHeaders.length)u.subHeaders.forEach(o);else{var c;i+=(c=u.column.getSize())!=null?c:0}};return o(t),i},t.getStart=()=>{if(t.index>0){const i=t.headerGroup.headers[t.index-1];return i.getStart()+i.getSize()}return 0},t.getResizeHandler=i=>{const o=a.getColumn(t.column.id),u=o==null?void 0:o.getCanResize();return c=>{if(!o||!u||(c.persist==null||c.persist(),ef(c)&&c.touches&&c.touches.length>1))return;const d=t.getSize(),g=t?t.getLeafHeaders().map(_=>[_.column.id,_.column.getSize()]):[[o.id,o.getSize()]],m=ef(c)?Math.round(c.touches[0].clientX):c.clientX,h={},v=(_,D)=>{typeof D=="number"&&(a.setColumnSizingInfo(z=>{var k,P;const Z=a.options.columnResizeDirection==="rtl"?-1:1,I=(D-((k=z==null?void 0:z.startOffset)!=null?k:0))*Z,q=Math.max(I/((P=z==null?void 0:z.startSize)!=null?P:0),-.999999);return z.columnSizingStart.forEach(ae=>{let[ce,me]=ae;h[ce]=Math.round(Math.max(me+me*q,0)*100)/100}),{...z,deltaOffset:I,deltaPercentage:q}}),(a.options.columnResizeMode==="onChange"||_==="end")&&a.setColumnSizing(z=>({...z,...h})))},b=_=>v("move",_),S=_=>{v("end",_),a.setColumnSizingInfo(D=>({...D,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},x=Tw(i),R={moveHandler:_=>b(_.clientX),upHandler:_=>{x==null||x.removeEventListener("mousemove",R.moveHandler),x==null||x.removeEventListener("mouseup",R.upHandler),S(_.clientX)}},w={moveHandler:_=>(_.cancelable&&(_.preventDefault(),_.stopPropagation()),b(_.touches[0].clientX),!1),upHandler:_=>{var D;x==null||x.removeEventListener("touchmove",w.moveHandler),x==null||x.removeEventListener("touchend",w.upHandler),_.cancelable&&(_.preventDefault(),_.stopPropagation()),S((D=_.touches[0])==null?void 0:D.clientX)}},A=Mw()?{passive:!1}:!1;ef(c)?(x==null||x.addEventListener("touchmove",w.moveHandler,A),x==null||x.addEventListener("touchend",w.upHandler,A)):(x==null||x.addEventListener("mousemove",R.moveHandler,A),x==null||x.addEventListener("mouseup",R.upHandler,A)),a.setColumnSizingInfo(_=>({..._,startOffset:m,startSize:d,deltaOffset:0,deltaPercentage:0,columnSizingStart:g,isResizingColumn:o.id}))}}},createTable:t=>{t.setColumnSizing=a=>t.options.onColumnSizingChange==null?void 0:t.options.onColumnSizingChange(a),t.setColumnSizingInfo=a=>t.options.onColumnSizingInfoChange==null?void 0:t.options.onColumnSizingInfoChange(a),t.resetColumnSizing=a=>{var i;t.setColumnSizing(a?{}:(i=t.initialState.columnSizing)!=null?i:{})},t.resetHeaderSizeInfo=a=>{var i;t.setColumnSizingInfo(a?Jc():(i=t.initialState.columnSizingInfo)!=null?i:Jc())},t.getTotalSize=()=>{var a,i;return(a=(i=t.getHeaderGroups()[0])==null?void 0:i.headers.reduce((o,u)=>o+u.getSize(),0))!=null?a:0},t.getLeftTotalSize=()=>{var a,i;return(a=(i=t.getLeftHeaderGroups()[0])==null?void 0:i.headers.reduce((o,u)=>o+u.getSize(),0))!=null?a:0},t.getCenterTotalSize=()=>{var a,i;return(a=(i=t.getCenterHeaderGroups()[0])==null?void 0:i.headers.reduce((o,u)=>o+u.getSize(),0))!=null?a:0},t.getRightTotalSize=()=>{var a,i;return(a=(i=t.getRightHeaderGroups()[0])==null?void 0:i.headers.reduce((o,u)=>o+u.getSize(),0))!=null?a:0}}};let ko=null;function Mw(){if(typeof ko=="boolean")return ko;let t=!1;try{const a={get passive(){return t=!0,!1}},i=()=>{};window.addEventListener("test",i,a),window.removeEventListener("test",i)}catch{t=!1}return ko=t,ko}function ef(t){return t.type==="touchstart"}const Ow={getInitialState:t=>({columnVisibility:{},...t}),getDefaultOptions:t=>({onColumnVisibilityChange:$t("columnVisibility",t)}),createColumn:(t,a)=>{t.toggleVisibility=i=>{t.getCanHide()&&a.setColumnVisibility(o=>({...o,[t.id]:i??!t.getIsVisible()}))},t.getIsVisible=()=>{var i,o;const u=t.columns;return(i=u.length?u.some(c=>c.getIsVisible()):(o=a.getState().columnVisibility)==null?void 0:o[t.id])!=null?i:!0},t.getCanHide=()=>{var i,o;return((i=t.columnDef.enableHiding)!=null?i:!0)&&((o=a.options.enableHiding)!=null?o:!0)},t.getToggleVisibilityHandler=()=>i=>{t.toggleVisibility==null||t.toggleVisibility(i.target.checked)}},createRow:(t,a)=>{t._getAllVisibleCells=ye(()=>[t.getAllCells(),a.getState().columnVisibility],i=>i.filter(o=>o.column.getIsVisible()),Se(a.options,"debugRows")),t.getVisibleCells=ye(()=>[t.getLeftVisibleCells(),t.getCenterVisibleCells(),t.getRightVisibleCells()],(i,o,u)=>[...i,...o,...u],Se(a.options,"debugRows"))},createTable:t=>{const a=(i,o)=>ye(()=>[o(),o().filter(u=>u.getIsVisible()).map(u=>u.id).join("_")],u=>u.filter(c=>c.getIsVisible==null?void 0:c.getIsVisible()),Se(t.options,"debugColumns"));t.getVisibleFlatColumns=a("getVisibleFlatColumns",()=>t.getAllFlatColumns()),t.getVisibleLeafColumns=a("getVisibleLeafColumns",()=>t.getAllLeafColumns()),t.getLeftVisibleLeafColumns=a("getLeftVisibleLeafColumns",()=>t.getLeftLeafColumns()),t.getRightVisibleLeafColumns=a("getRightVisibleLeafColumns",()=>t.getRightLeafColumns()),t.getCenterVisibleLeafColumns=a("getCenterVisibleLeafColumns",()=>t.getCenterLeafColumns()),t.setColumnVisibility=i=>t.options.onColumnVisibilityChange==null?void 0:t.options.onColumnVisibilityChange(i),t.resetColumnVisibility=i=>{var o;t.setColumnVisibility(i?{}:(o=t.initialState.columnVisibility)!=null?o:{})},t.toggleAllColumnsVisible=i=>{var o;i=(o=i)!=null?o:!t.getIsAllColumnsVisible(),t.setColumnVisibility(t.getAllLeafColumns().reduce((u,c)=>({...u,[c.id]:i||!(c.getCanHide!=null&&c.getCanHide())}),{}))},t.getIsAllColumnsVisible=()=>!t.getAllLeafColumns().some(i=>!(i.getIsVisible!=null&&i.getIsVisible())),t.getIsSomeColumnsVisible=()=>t.getAllLeafColumns().some(i=>i.getIsVisible==null?void 0:i.getIsVisible()),t.getToggleAllColumnsVisibilityHandler=()=>i=>{var o;t.toggleAllColumnsVisible((o=i.target)==null?void 0:o.checked)}}};function ui(t,a){return a?a==="center"?t.getCenterVisibleLeafColumns():a==="left"?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns()}const Dw={createTable:t=>{t._getGlobalFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,"__global__"),t.getGlobalFacetedRowModel=()=>t.options.manualFiltering||!t._getGlobalFacetedRowModel?t.getPreFilteredRowModel():t._getGlobalFacetedRowModel(),t._getGlobalFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,"__global__"),t.getGlobalFacetedUniqueValues=()=>t._getGlobalFacetedUniqueValues?t._getGlobalFacetedUniqueValues():new Map,t._getGlobalFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,"__global__"),t.getGlobalFacetedMinMaxValues=()=>{if(t._getGlobalFacetedMinMaxValues)return t._getGlobalFacetedMinMaxValues()}}},Nw={getInitialState:t=>({globalFilter:void 0,...t}),getDefaultOptions:t=>({onGlobalFilterChange:$t("globalFilter",t),globalFilterFn:"auto",getColumnCanGlobalFilter:a=>{var i;const o=(i=t.getCoreRowModel().flatRows[0])==null||(i=i._getAllCellsByColumnId()[a.id])==null?void 0:i.getValue();return typeof o=="string"||typeof o=="number"}}),createColumn:(t,a)=>{t.getCanGlobalFilter=()=>{var i,o,u,c;return((i=t.columnDef.enableGlobalFilter)!=null?i:!0)&&((o=a.options.enableGlobalFilter)!=null?o:!0)&&((u=a.options.enableFilters)!=null?u:!0)&&((c=a.options.getColumnCanGlobalFilter==null?void 0:a.options.getColumnCanGlobalFilter(t))!=null?c:!0)&&!!t.accessorFn}},createTable:t=>{t.getGlobalAutoFilterFn=()=>jn.includesString,t.getGlobalFilterFn=()=>{var a,i;const{globalFilterFn:o}=t.options;return ps(o)?o:o==="auto"?t.getGlobalAutoFilterFn():(a=(i=t.options.filterFns)==null?void 0:i[o])!=null?a:jn[o]},t.setGlobalFilter=a=>{t.options.onGlobalFilterChange==null||t.options.onGlobalFilterChange(a)},t.resetGlobalFilter=a=>{t.setGlobalFilter(a?void 0:t.initialState.globalFilter)}}},zw={getInitialState:t=>({expanded:{},...t}),getDefaultOptions:t=>({onExpandedChange:$t("expanded",t),paginateExpandedRows:!0}),createTable:t=>{let a=!1,i=!1;t._autoResetExpanded=()=>{var o,u;if(!a){t._queue(()=>{a=!0});return}if((o=(u=t.options.autoResetAll)!=null?u:t.options.autoResetExpanded)!=null?o:!t.options.manualExpanding){if(i)return;i=!0,t._queue(()=>{t.resetExpanded(),i=!1})}},t.setExpanded=o=>t.options.onExpandedChange==null?void 0:t.options.onExpandedChange(o),t.toggleAllRowsExpanded=o=>{o??!t.getIsAllRowsExpanded()?t.setExpanded(!0):t.setExpanded({})},t.resetExpanded=o=>{var u,c;t.setExpanded(o?{}:(u=(c=t.initialState)==null?void 0:c.expanded)!=null?u:{})},t.getCanSomeRowsExpand=()=>t.getPrePaginationRowModel().flatRows.some(o=>o.getCanExpand()),t.getToggleAllRowsExpandedHandler=()=>o=>{o.persist==null||o.persist(),t.toggleAllRowsExpanded()},t.getIsSomeRowsExpanded=()=>{const o=t.getState().expanded;return o===!0||Object.values(o).some(Boolean)},t.getIsAllRowsExpanded=()=>{const o=t.getState().expanded;return typeof o=="boolean"?o===!0:!(!Object.keys(o).length||t.getRowModel().flatRows.some(u=>!u.getIsExpanded()))},t.getExpandedDepth=()=>{let o=0;return(t.getState().expanded===!0?Object.keys(t.getRowModel().rowsById):Object.keys(t.getState().expanded)).forEach(c=>{const d=c.split(".");o=Math.max(o,d.length)}),o},t.getPreExpandedRowModel=()=>t.getSortedRowModel(),t.getExpandedRowModel=()=>(!t._getExpandedRowModel&&t.options.getExpandedRowModel&&(t._getExpandedRowModel=t.options.getExpandedRowModel(t)),t.options.manualExpanding||!t._getExpandedRowModel?t.getPreExpandedRowModel():t._getExpandedRowModel())},createRow:(t,a)=>{t.toggleExpanded=i=>{a.setExpanded(o=>{var u;const c=o===!0?!0:!!(o!=null&&o[t.id]);let d={};if(o===!0?Object.keys(a.getRowModel().rowsById).forEach(g=>{d[g]=!0}):d=o,i=(u=i)!=null?u:!c,!c&&i)return{...d,[t.id]:!0};if(c&&!i){const{[t.id]:g,...m}=d;return m}return o})},t.getIsExpanded=()=>{var i;const o=a.getState().expanded;return!!((i=a.options.getIsRowExpanded==null?void 0:a.options.getIsRowExpanded(t))!=null?i:o===!0||o!=null&&o[t.id])},t.getCanExpand=()=>{var i,o,u;return(i=a.options.getRowCanExpand==null?void 0:a.options.getRowCanExpand(t))!=null?i:((o=a.options.enableExpanding)!=null?o:!0)&&!!((u=t.subRows)!=null&&u.length)},t.getIsAllParentsExpanded=()=>{let i=!0,o=t;for(;i&&o.parentId;)o=a.getRow(o.parentId,!0),i=o.getIsExpanded();return i},t.getToggleExpandedHandler=()=>{const i=t.getCanExpand();return()=>{i&&t.toggleExpanded()}}}},Df=0,Nf=10,tf=()=>({pageIndex:Df,pageSize:Nf}),Hw={getInitialState:t=>({...t,pagination:{...tf(),...t==null?void 0:t.pagination}}),getDefaultOptions:t=>({onPaginationChange:$t("pagination",t)}),createTable:t=>{let a=!1,i=!1;t._autoResetPageIndex=()=>{var o,u;if(!a){t._queue(()=>{a=!0});return}if((o=(u=t.options.autoResetAll)!=null?u:t.options.autoResetPageIndex)!=null?o:!t.options.manualPagination){if(i)return;i=!0,t._queue(()=>{t.resetPageIndex(),i=!1})}},t.setPagination=o=>{const u=c=>ml(o,c);return t.options.onPaginationChange==null?void 0:t.options.onPaginationChange(u)},t.resetPagination=o=>{var u;t.setPagination(o?tf():(u=t.initialState.pagination)!=null?u:tf())},t.setPageIndex=o=>{t.setPagination(u=>{let c=ml(o,u.pageIndex);const d=typeof t.options.pageCount>"u"||t.options.pageCount===-1?Number.MAX_SAFE_INTEGER:t.options.pageCount-1;return c=Math.max(0,Math.min(c,d)),{...u,pageIndex:c}})},t.resetPageIndex=o=>{var u,c;t.setPageIndex(o?Df:(u=(c=t.initialState)==null||(c=c.pagination)==null?void 0:c.pageIndex)!=null?u:Df)},t.resetPageSize=o=>{var u,c;t.setPageSize(o?Nf:(u=(c=t.initialState)==null||(c=c.pagination)==null?void 0:c.pageSize)!=null?u:Nf)},t.setPageSize=o=>{t.setPagination(u=>{const c=Math.max(1,ml(o,u.pageSize)),d=u.pageSize*u.pageIndex,g=Math.floor(d/c);return{...u,pageIndex:g,pageSize:c}})},t.setPageCount=o=>t.setPagination(u=>{var c;let d=ml(o,(c=t.options.pageCount)!=null?c:-1);return typeof d=="number"&&(d=Math.max(-1,d)),{...u,pageCount:d}}),t.getPageOptions=ye(()=>[t.getPageCount()],o=>{let u=[];return o&&o>0&&(u=[...new Array(o)].fill(null).map((c,d)=>d)),u},Se(t.options,"debugTable")),t.getCanPreviousPage=()=>t.getState().pagination.pageIndex>0,t.getCanNextPage=()=>{const{pageIndex:o}=t.getState().pagination,u=t.getPageCount();return u===-1?!0:u===0?!1:o<u-1},t.previousPage=()=>t.setPageIndex(o=>o-1),t.nextPage=()=>t.setPageIndex(o=>o+1),t.firstPage=()=>t.setPageIndex(0),t.lastPage=()=>t.setPageIndex(t.getPageCount()-1),t.getPrePaginationRowModel=()=>t.getExpandedRowModel(),t.getPaginationRowModel=()=>(!t._getPaginationRowModel&&t.options.getPaginationRowModel&&(t._getPaginationRowModel=t.options.getPaginationRowModel(t)),t.options.manualPagination||!t._getPaginationRowModel?t.getPrePaginationRowModel():t._getPaginationRowModel()),t.getPageCount=()=>{var o;return(o=t.options.pageCount)!=null?o:Math.ceil(t.getRowCount()/t.getState().pagination.pageSize)},t.getRowCount=()=>{var o;return(o=t.options.rowCount)!=null?o:t.getPrePaginationRowModel().rows.length}}},nf=()=>({top:[],bottom:[]}),Lw={getInitialState:t=>({rowPinning:nf(),...t}),getDefaultOptions:t=>({onRowPinningChange:$t("rowPinning",t)}),createRow:(t,a)=>{t.pin=(i,o,u)=>{const c=o?t.getLeafRows().map(m=>{let{id:h}=m;return h}):[],d=u?t.getParentRows().map(m=>{let{id:h}=m;return h}):[],g=new Set([...d,t.id,...c]);a.setRowPinning(m=>{var h,v;if(i==="bottom"){var b,S;return{top:((b=m==null?void 0:m.top)!=null?b:[]).filter(w=>!(g!=null&&g.has(w))),bottom:[...((S=m==null?void 0:m.bottom)!=null?S:[]).filter(w=>!(g!=null&&g.has(w))),...Array.from(g)]}}if(i==="top"){var x,R;return{top:[...((x=m==null?void 0:m.top)!=null?x:[]).filter(w=>!(g!=null&&g.has(w))),...Array.from(g)],bottom:((R=m==null?void 0:m.bottom)!=null?R:[]).filter(w=>!(g!=null&&g.has(w)))}}return{top:((h=m==null?void 0:m.top)!=null?h:[]).filter(w=>!(g!=null&&g.has(w))),bottom:((v=m==null?void 0:m.bottom)!=null?v:[]).filter(w=>!(g!=null&&g.has(w)))}})},t.getCanPin=()=>{var i;const{enableRowPinning:o,enablePinning:u}=a.options;return typeof o=="function"?o(t):(i=o??u)!=null?i:!0},t.getIsPinned=()=>{const i=[t.id],{top:o,bottom:u}=a.getState().rowPinning,c=i.some(g=>o==null?void 0:o.includes(g)),d=i.some(g=>u==null?void 0:u.includes(g));return c?"top":d?"bottom":!1},t.getPinnedIndex=()=>{var i,o;const u=t.getIsPinned();if(!u)return-1;const c=(i=u==="top"?a.getTopRows():a.getBottomRows())==null?void 0:i.map(d=>{let{id:g}=d;return g});return(o=c==null?void 0:c.indexOf(t.id))!=null?o:-1}},createTable:t=>{t.setRowPinning=a=>t.options.onRowPinningChange==null?void 0:t.options.onRowPinningChange(a),t.resetRowPinning=a=>{var i,o;return t.setRowPinning(a?nf():(i=(o=t.initialState)==null?void 0:o.rowPinning)!=null?i:nf())},t.getIsSomeRowsPinned=a=>{var i;const o=t.getState().rowPinning;if(!a){var u,c;return!!((u=o.top)!=null&&u.length||(c=o.bottom)!=null&&c.length)}return!!((i=o[a])!=null&&i.length)},t._getPinnedRows=(a,i,o)=>{var u;return((u=t.options.keepPinnedRows)==null||u?(i??[]).map(d=>{const g=t.getRow(d,!0);return g.getIsAllParentsExpanded()?g:null}):(i??[]).map(d=>a.find(g=>g.id===d))).filter(Boolean).map(d=>({...d,position:o}))},t.getTopRows=ye(()=>[t.getRowModel().rows,t.getState().rowPinning.top],(a,i)=>t._getPinnedRows(a,i,"top"),Se(t.options,"debugRows")),t.getBottomRows=ye(()=>[t.getRowModel().rows,t.getState().rowPinning.bottom],(a,i)=>t._getPinnedRows(a,i,"bottom"),Se(t.options,"debugRows")),t.getCenterRows=ye(()=>[t.getRowModel().rows,t.getState().rowPinning.top,t.getState().rowPinning.bottom],(a,i,o)=>{const u=new Set([...i??[],...o??[]]);return a.filter(c=>!u.has(c.id))},Se(t.options,"debugRows"))}},Uw={getInitialState:t=>({rowSelection:{},...t}),getDefaultOptions:t=>({onRowSelectionChange:$t("rowSelection",t),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:t=>{t.setRowSelection=a=>t.options.onRowSelectionChange==null?void 0:t.options.onRowSelectionChange(a),t.resetRowSelection=a=>{var i;return t.setRowSelection(a?{}:(i=t.initialState.rowSelection)!=null?i:{})},t.toggleAllRowsSelected=a=>{t.setRowSelection(i=>{a=typeof a<"u"?a:!t.getIsAllRowsSelected();const o={...i},u=t.getPreGroupedRowModel().flatRows;return a?u.forEach(c=>{c.getCanSelect()&&(o[c.id]=!0)}):u.forEach(c=>{delete o[c.id]}),o})},t.toggleAllPageRowsSelected=a=>t.setRowSelection(i=>{const o=typeof a<"u"?a:!t.getIsAllPageRowsSelected(),u={...i};return t.getRowModel().rows.forEach(c=>{zf(u,c.id,o,!0,t)}),u}),t.getPreSelectedRowModel=()=>t.getCoreRowModel(),t.getSelectedRowModel=ye(()=>[t.getState().rowSelection,t.getCoreRowModel()],(a,i)=>Object.keys(a).length?lf(t,i):{rows:[],flatRows:[],rowsById:{}},Se(t.options,"debugTable")),t.getFilteredSelectedRowModel=ye(()=>[t.getState().rowSelection,t.getFilteredRowModel()],(a,i)=>Object.keys(a).length?lf(t,i):{rows:[],flatRows:[],rowsById:{}},Se(t.options,"debugTable")),t.getGroupedSelectedRowModel=ye(()=>[t.getState().rowSelection,t.getSortedRowModel()],(a,i)=>Object.keys(a).length?lf(t,i):{rows:[],flatRows:[],rowsById:{}},Se(t.options,"debugTable")),t.getIsAllRowsSelected=()=>{const a=t.getFilteredRowModel().flatRows,{rowSelection:i}=t.getState();let o=!!(a.length&&Object.keys(i).length);return o&&a.some(u=>u.getCanSelect()&&!i[u.id])&&(o=!1),o},t.getIsAllPageRowsSelected=()=>{const a=t.getPaginationRowModel().flatRows.filter(u=>u.getCanSelect()),{rowSelection:i}=t.getState();let o=!!a.length;return o&&a.some(u=>!i[u.id])&&(o=!1),o},t.getIsSomeRowsSelected=()=>{var a;const i=Object.keys((a=t.getState().rowSelection)!=null?a:{}).length;return i>0&&i<t.getFilteredRowModel().flatRows.length},t.getIsSomePageRowsSelected=()=>{const a=t.getPaginationRowModel().flatRows;return t.getIsAllPageRowsSelected()?!1:a.filter(i=>i.getCanSelect()).some(i=>i.getIsSelected()||i.getIsSomeSelected())},t.getToggleAllRowsSelectedHandler=()=>a=>{t.toggleAllRowsSelected(a.target.checked)},t.getToggleAllPageRowsSelectedHandler=()=>a=>{t.toggleAllPageRowsSelected(a.target.checked)}},createRow:(t,a)=>{t.toggleSelected=(i,o)=>{const u=t.getIsSelected();a.setRowSelection(c=>{var d;if(i=typeof i<"u"?i:!u,t.getCanSelect()&&u===i)return c;const g={...c};return zf(g,t.id,i,(d=o==null?void 0:o.selectChildren)!=null?d:!0,a),g})},t.getIsSelected=()=>{const{rowSelection:i}=a.getState();return fd(t,i)},t.getIsSomeSelected=()=>{const{rowSelection:i}=a.getState();return Hf(t,i)==="some"},t.getIsAllSubRowsSelected=()=>{const{rowSelection:i}=a.getState();return Hf(t,i)==="all"},t.getCanSelect=()=>{var i;return typeof a.options.enableRowSelection=="function"?a.options.enableRowSelection(t):(i=a.options.enableRowSelection)!=null?i:!0},t.getCanSelectSubRows=()=>{var i;return typeof a.options.enableSubRowSelection=="function"?a.options.enableSubRowSelection(t):(i=a.options.enableSubRowSelection)!=null?i:!0},t.getCanMultiSelect=()=>{var i;return typeof a.options.enableMultiRowSelection=="function"?a.options.enableMultiRowSelection(t):(i=a.options.enableMultiRowSelection)!=null?i:!0},t.getToggleSelectedHandler=()=>{const i=t.getCanSelect();return o=>{var u;i&&t.toggleSelected((u=o.target)==null?void 0:u.checked)}}}},zf=(t,a,i,o,u)=>{var c;const d=u.getRow(a,!0);i?(d.getCanMultiSelect()||Object.keys(t).forEach(g=>delete t[g]),d.getCanSelect()&&(t[a]=!0)):delete t[a],o&&(c=d.subRows)!=null&&c.length&&d.getCanSelectSubRows()&&d.subRows.forEach(g=>zf(t,g.id,i,o,u))};function lf(t,a){const i=t.getState().rowSelection,o=[],u={},c=function(d,g){return d.map(m=>{var h;const v=fd(m,i);if(v&&(o.push(m),u[m.id]=m),(h=m.subRows)!=null&&h.length&&(m={...m,subRows:c(m.subRows)}),v)return m}).filter(Boolean)};return{rows:c(a.rows),flatRows:o,rowsById:u}}function fd(t,a){var i;return(i=a[t.id])!=null?i:!1}function Hf(t,a,i){var o;if(!((o=t.subRows)!=null&&o.length))return!1;let u=!0,c=!1;return t.subRows.forEach(d=>{if(!(c&&!u)&&(d.getCanSelect()&&(fd(d,a)?c=!0:u=!1),d.subRows&&d.subRows.length)){const g=Hf(d,a);g==="all"?c=!0:(g==="some"&&(c=!0),u=!1)}}),u?"all":c?"some":!1}const Lf=/([0-9]+)/gm,jw=(t,a,i)=>b0(vl(t.getValue(i)).toLowerCase(),vl(a.getValue(i)).toLowerCase()),Vw=(t,a,i)=>b0(vl(t.getValue(i)),vl(a.getValue(i))),Bw=(t,a,i)=>dd(vl(t.getValue(i)).toLowerCase(),vl(a.getValue(i)).toLowerCase()),Gw=(t,a,i)=>dd(vl(t.getValue(i)),vl(a.getValue(i))),Pw=(t,a,i)=>{const o=t.getValue(i),u=a.getValue(i);return o>u?1:o<u?-1:0},kw=(t,a,i)=>dd(t.getValue(i),a.getValue(i));function dd(t,a){return t===a?0:t>a?1:-1}function vl(t){return typeof t=="number"?isNaN(t)||t===1/0||t===-1/0?"":String(t):typeof t=="string"?t:""}function b0(t,a){const i=t.split(Lf).filter(Boolean),o=a.split(Lf).filter(Boolean);for(;i.length&&o.length;){const u=i.shift(),c=o.shift(),d=parseInt(u,10),g=parseInt(c,10),m=[d,g].sort();if(isNaN(m[0])){if(u>c)return 1;if(c>u)return-1;continue}if(isNaN(m[1]))return isNaN(d)?-1:1;if(d>g)return 1;if(g>d)return-1}return i.length-o.length}const ai={alphanumeric:jw,alphanumericCaseSensitive:Vw,text:Bw,textCaseSensitive:Gw,datetime:Pw,basic:kw},$w={getInitialState:t=>({sorting:[],...t}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:t=>({onSortingChange:$t("sorting",t),isMultiSortEvent:a=>a.shiftKey}),createColumn:(t,a)=>{t.getAutoSortingFn=()=>{const i=a.getFilteredRowModel().flatRows.slice(10);let o=!1;for(const u of i){const c=u==null?void 0:u.getValue(t.id);if(Object.prototype.toString.call(c)==="[object Date]")return ai.datetime;if(typeof c=="string"&&(o=!0,c.split(Lf).length>1))return ai.alphanumeric}return o?ai.text:ai.basic},t.getAutoSortDir=()=>{const i=a.getFilteredRowModel().flatRows[0];return typeof(i==null?void 0:i.getValue(t.id))=="string"?"asc":"desc"},t.getSortingFn=()=>{var i,o;if(!t)throw new Error;return ps(t.columnDef.sortingFn)?t.columnDef.sortingFn:t.columnDef.sortingFn==="auto"?t.getAutoSortingFn():(i=(o=a.options.sortingFns)==null?void 0:o[t.columnDef.sortingFn])!=null?i:ai[t.columnDef.sortingFn]},t.toggleSorting=(i,o)=>{const u=t.getNextSortingOrder(),c=typeof i<"u"&&i!==null;a.setSorting(d=>{const g=d==null?void 0:d.find(x=>x.id===t.id),m=d==null?void 0:d.findIndex(x=>x.id===t.id);let h=[],v,b=c?i:u==="desc";if(d!=null&&d.length&&t.getCanMultiSort()&&o?g?v="toggle":v="add":d!=null&&d.length&&m!==d.length-1?v="replace":g?v="toggle":v="replace",v==="toggle"&&(c||u||(v="remove")),v==="add"){var S;h=[...d,{id:t.id,desc:b}],h.splice(0,h.length-((S=a.options.maxMultiSortColCount)!=null?S:Number.MAX_SAFE_INTEGER))}else v==="toggle"?h=d.map(x=>x.id===t.id?{...x,desc:b}:x):v==="remove"?h=d.filter(x=>x.id!==t.id):h=[{id:t.id,desc:b}];return h})},t.getFirstSortDir=()=>{var i,o;return((i=(o=t.columnDef.sortDescFirst)!=null?o:a.options.sortDescFirst)!=null?i:t.getAutoSortDir()==="desc")?"desc":"asc"},t.getNextSortingOrder=i=>{var o,u;const c=t.getFirstSortDir(),d=t.getIsSorted();return d?d!==c&&((o=a.options.enableSortingRemoval)==null||o)&&(!(i&&(u=a.options.enableMultiRemove)!=null)||u)?!1:d==="desc"?"asc":"desc":c},t.getCanSort=()=>{var i,o;return((i=t.columnDef.enableSorting)!=null?i:!0)&&((o=a.options.enableSorting)!=null?o:!0)&&!!t.accessorFn},t.getCanMultiSort=()=>{var i,o;return(i=(o=t.columnDef.enableMultiSort)!=null?o:a.options.enableMultiSort)!=null?i:!!t.accessorFn},t.getIsSorted=()=>{var i;const o=(i=a.getState().sorting)==null?void 0:i.find(u=>u.id===t.id);return o?o.desc?"desc":"asc":!1},t.getSortIndex=()=>{var i,o;return(i=(o=a.getState().sorting)==null?void 0:o.findIndex(u=>u.id===t.id))!=null?i:-1},t.clearSorting=()=>{a.setSorting(i=>i!=null&&i.length?i.filter(o=>o.id!==t.id):[])},t.getToggleSortingHandler=()=>{const i=t.getCanSort();return o=>{i&&(o.persist==null||o.persist(),t.toggleSorting==null||t.toggleSorting(void 0,t.getCanMultiSort()?a.options.isMultiSortEvent==null?void 0:a.options.isMultiSortEvent(o):!1))}}},createTable:t=>{t.setSorting=a=>t.options.onSortingChange==null?void 0:t.options.onSortingChange(a),t.resetSorting=a=>{var i,o;t.setSorting(a?[]:(i=(o=t.initialState)==null?void 0:o.sorting)!=null?i:[])},t.getPreSortedRowModel=()=>t.getGroupedRowModel(),t.getSortedRowModel=()=>(!t._getSortedRowModel&&t.options.getSortedRowModel&&(t._getSortedRowModel=t.options.getSortedRowModel(t)),t.options.manualSorting||!t._getSortedRowModel?t.getPreSortedRowModel():t._getSortedRowModel())}},qw=[cw,Ow,Ew,_w,dw,gw,Dw,Nw,$w,Cw,zw,Hw,Lw,Uw,Aw];function Yw(t){var a,i;const o=[...qw,...(a=t._features)!=null?a:[]];let u={_features:o};const c=u._features.reduce((S,x)=>Object.assign(S,x.getDefaultOptions==null?void 0:x.getDefaultOptions(u)),{}),d=S=>u.options.mergeOptions?u.options.mergeOptions(c,S):{...c,...S};let m={...{},...(i=t.initialState)!=null?i:{}};u._features.forEach(S=>{var x;m=(x=S.getInitialState==null?void 0:S.getInitialState(m))!=null?x:m});const h=[];let v=!1;const b={_features:o,options:{...c,...t},initialState:m,_queue:S=>{h.push(S),v||(v=!0,Promise.resolve().then(()=>{for(;h.length;)h.shift()();v=!1}).catch(x=>setTimeout(()=>{throw x})))},reset:()=>{u.setState(u.initialState)},setOptions:S=>{const x=ml(S,u.options);u.options=d(x)},getState:()=>u.options.state,setState:S=>{u.options.onStateChange==null||u.options.onStateChange(S)},_getRowId:(S,x,R)=>{var w;return(w=u.options.getRowId==null?void 0:u.options.getRowId(S,x,R))!=null?w:`${R?[R.id,x].join("."):x}`},getCoreRowModel:()=>(u._getCoreRowModel||(u._getCoreRowModel=u.options.getCoreRowModel(u)),u._getCoreRowModel()),getRowModel:()=>u.getPaginationRowModel(),getRow:(S,x)=>{let R=(x?u.getPrePaginationRowModel():u.getRowModel()).rowsById[S];if(!R&&(R=u.getCoreRowModel().rowsById[S],!R))throw new Error;return R},_getDefaultColumnDef:ye(()=>[u.options.defaultColumn],S=>{var x;return S=(x=S)!=null?x:{},{header:R=>{const w=R.header.column.columnDef;return w.accessorKey?w.accessorKey:w.accessorFn?w.id:null},cell:R=>{var w,A;return(w=(A=R.renderValue())==null||A.toString==null?void 0:A.toString())!=null?w:null},...u._features.reduce((R,w)=>Object.assign(R,w.getDefaultColumnDef==null?void 0:w.getDefaultColumnDef()),{}),...S}},Se(t,"debugColumns")),_getColumnDefs:()=>u.options.columns,getAllColumns:ye(()=>[u._getColumnDefs()],S=>{const x=function(R,w,A){return A===void 0&&(A=0),R.map(_=>{const D=uw(u,_,A,w),z=_;return D.columns=z.columns?x(z.columns,D,A+1):[],D})};return x(S)},Se(t,"debugColumns")),getAllFlatColumns:ye(()=>[u.getAllColumns()],S=>S.flatMap(x=>x.getFlatColumns()),Se(t,"debugColumns")),_getAllFlatColumnsById:ye(()=>[u.getAllFlatColumns()],S=>S.reduce((x,R)=>(x[R.id]=R,x),{}),Se(t,"debugColumns")),getAllLeafColumns:ye(()=>[u.getAllColumns(),u._getOrderColumnsFn()],(S,x)=>{let R=S.flatMap(w=>w.getLeafColumns());return x(R)},Se(t,"debugColumns")),getColumn:S=>u._getAllFlatColumnsById()[S]};Object.assign(u,b);for(let S=0;S<u._features.length;S++){const x=u._features[S];x==null||x.createTable==null||x.createTable(u)}return u}function Fw(){return t=>ye(()=>[t.options.data],a=>{const i={rows:[],flatRows:[],rowsById:{}},o=function(u,c,d){c===void 0&&(c=0);const g=[];for(let h=0;h<u.length;h++){const v=fw(t,t._getRowId(u[h],h,d),u[h],h,c,void 0,d==null?void 0:d.id);if(i.flatRows.push(v),i.rowsById[v.id]=v,g.push(v),t.options.getSubRows){var m;v.originalSubRows=t.options.getSubRows(u[h],h),(m=v.originalSubRows)!=null&&m.length&&(v.subRows=o(v.originalSubRows,c+1,v))}}return g};return i.rows=o(a),i},Se(t.options,"debugTable","getRowModel",()=>t._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Kp(t,a){return t?Xw(t)?C.createElement(t,a):t:null}function Xw(t){return Iw(t)||typeof t=="function"||Kw(t)}function Iw(t){return typeof t=="function"&&(()=>{const a=Object.getPrototypeOf(t);return a.prototype&&a.prototype.isReactComponent})()}function Kw(t){return typeof t=="object"&&typeof t.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(t.$$typeof.description)}function Zw(t){const a={state:{},onStateChange:()=>{},renderFallbackValue:null,...t},[i]=C.useState(()=>({current:Yw(a)})),[o,u]=C.useState(()=>i.current.initialState);return i.current.setOptions(c=>({...c,...t,state:{...o,...t.state},onStateChange:d=>{u(d),t.onStateChange==null||t.onStateChange(d)}})),i.current}function Qw({data:t,columns:a,className:i,isLoading:o=!1,emptyMessage:u="No data available",columnVisibility:c,onRowClick:d}){if(o)return E.jsx("div",{className:Be("w-full",i),children:E.jsxs("table",{className:"w-full border-collapse mt-2",children:[E.jsx("thead",{children:E.jsx("tr",{children:Array(4).fill(0).map((m,h)=>E.jsx("th",{className:"border-t border-b border-surface-75 p-2",children:E.jsx("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-24"})},h))})}),E.jsx("tbody",{children:Array(5).fill(0).map((m,h)=>E.jsx(pC,{columns:4},h))})]})});if(!t||t.length===0)return E.jsx("div",{className:Be("w-full",i),children:E.jsx(fi,{message:u})});const g=Zw({data:t,columns:a,getCoreRowModel:Fw(),state:{columnVisibility:c||{}}});return E.jsx("div",{className:Be("overflow-x-auto w-full",i),children:E.jsxs("table",{className:"w-full border-collapse mt-2",children:[E.jsx("thead",{children:g.getHeaderGroups().map(m=>E.jsx("tr",{children:m.headers.map(h=>E.jsx("th",{className:"border-t border-b border-surface-75 p-2 text-start text-xs uppercase font-medium text-fg-subheader bg-slate-25",children:h.isPlaceholder?null:Kp(h.column.columnDef.header,h.getContext())},h.id))},m.id))}),E.jsx("tbody",{children:g.getRowModel().rows.map(m=>E.jsx("tr",{className:Be("hover:bg-slate-50",d?"cursor-pointer":""),onClick:d?()=>d(m.original):void 0,children:m.getVisibleCells().map(h=>E.jsx("td",{className:"p-2 border-collapse min-w-[8rem] border-b pl-2 pr-3 py-3 border-surface-75 text-sm text-fg-body text-start",children:Kp(h.column.columnDef.cell,h.getContext())},h.id))},m.id))})]})})}function x0({data:t,columns:a,className:i,isLoading:o=!1,emptyMessage:u="No data available",columnVisibility:c,onRowClick:d}){const g=pi(),m=C.useMemo(()=>{if(a)return a;if(!t||t.length===0)return[];const h=t[0];return Object.keys(h).map(v=>g.accessor(b=>b[v],{id:v,header:v.charAt(0).toUpperCase()+v.slice(1),cell:b=>{const S=b.getValue();return typeof S=="string"?E.jsx("span",{dangerouslySetInnerHTML:{__html:S}}):S==null?E.jsx("span",{className:"text-fg-tertiary",children:"--"}):String(S)}}))},[t,g,a]);return E.jsx(Qw,{data:t,columns:m,className:i,isLoading:o,emptyMessage:u,columnVisibility:c,onRowClick:d})}/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ww=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Jw=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,i,o)=>o?o.toUpperCase():i.toLowerCase()),Zp=t=>{const a=Jw(t);return a.charAt(0).toUpperCase()+a.slice(1)},w0=(...t)=>t.filter((a,i,o)=>!!a&&a.trim()!==""&&o.indexOf(a)===i).join(" ").trim(),eC=t=>{for(const a in t)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var tC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nC=C.forwardRef(({color:t="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:u="",children:c,iconNode:d,...g},m)=>C.createElement("svg",{ref:m,...tC,width:a,height:a,stroke:t,strokeWidth:o?Number(i)*24/Number(a):i,className:w0("lucide",u),...!c&&!eC(g)&&{"aria-hidden":"true"},...g},[...d.map(([h,v])=>C.createElement(h,v)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xl=(t,a)=>{const i=C.forwardRef(({className:o,...u},c)=>C.createElement(nC,{ref:c,iconNode:a,className:w0(`lucide-${Ww(Zp(t))}`,`lucide-${t}`,o),...u}));return i.displayName=Zp(t),i};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lC=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],aC=xl("check",lC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rC=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],C0=xl("chevron-down",rC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iC=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],oC=xl("chevron-left",iC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sC=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],R0=xl("chevron-right",sC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uC=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],cC=xl("chevron-up",uC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fC=[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],gd=xl("search-x",fC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dC=[["path",{d:"M21 6H3",key:"1jwq7v"}],["path",{d:"M10 12H3",key:"1ulcyk"}],["path",{d:"M10 18H3",key:"13769t"}],["circle",{cx:"17",cy:"15",r:"3",key:"1upz2a"}],["path",{d:"m21 19-1.9-1.9",key:"dwi7p8"}]],gC=xl("text-search",dC);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mC=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],hC=xl("x",mC),fi=({message:t="No data available",icon:a=gd})=>E.jsxs("div",{className:"p-6 flex flex-col items-center justify-center text-center",children:[E.jsx(a,{size:48,className:"text-gray-300 mb-3",strokeWidth:1}),E.jsx("p",{className:"text-fg-tertiary text-sm",children:t})]}),pC=({columns:t})=>E.jsx("tr",{children:Array(t).fill(0).map((a,i)=>E.jsx("td",{className:"p-2 border-b border-surface-75",children:E.jsx("div",{className:"h-4 bg-gray-200 rounded animate-pulse"})},i))}),E0={"Content-Type":"application/json"},vC={path:"/api/algolia/search-wiki/",method:"POST",headers:E0},yC={path:"/openkb/api/topics",method:"GET"},SC={path:"/api/store/clubs/:clubId/products",method:"GET"},bC={path:"/api/algolia/search-marketing-print",method:"POST",headers:E0},xC={path:"/api/cctv/people/:clubId",method:"GET"},Xl={wiki:vC,topics:yC,storeProducts:SC,marketingPrint:bC,cctvPeople:xC},Uf=t=>{if(!t)return"";let a=t.replace(new RegExp(od,"g"),qa).replace(new RegExp(ux,"g"),kp);const i=[],o=new RegExp(`${qa}(.*?)${kp}`,"g");let u;for(;(u=o.exec(a))!==null;)i.push(u[0]);let c=0;a=a.replace(o,()=>`__HIGHLIGHT_PLACEHOLDER_${c++}__`);let g=_0(a);for(let m=0;m<i.length;m++)g=g.replace(`__HIGHLIGHT_PLACEHOLDER_${m}__`,i[m]);return g.replace(/__HIGHLIGHT_PLACEHOLDER_(\d+)__/g,(m,h)=>i[Number(h)]||"")},_0=t=>t?t.replace(/<[^>]+>/g,""):"",Qp=t=>t?t.includes(qa)||t.includes(od):!1,wC=t=>t?`/wiki/#/openkb/kb/${encodeURIComponent(t)}`:"",CC=(t,a=200)=>{if(!t)return{summary:"",shownHighlights:0};const i=Uf(t),o=(i.match(new RegExp(qa,"g"))||[]).length;if(o===0)return{summary:i.length<=100?i:i.substring(0,100)+"...",shownHighlights:0};const u=o<=1?100:a;if(i.length<=u)return{summary:i,shownHighlights:0};const c=Math.min(u*(1+Math.min(o,5)*.3),u*2.5),d=i.indexOf(qa);let g=0;if(d>=0){const x=o>1?Math.floor(c*.3):Math.floor(c*.5);if(g=Math.max(0,d-x),g>0){const R=i.lastIndexOf(" ",g);R>=0&&g-R<20&&(g=R+1)}}let m=g+c;if(m<i.length){const x=i.indexOf(" ",m-10);x>=0&&x-m<20&&(m=x)}else m=i.length;const h=g>0?"...":"",v=m<i.length?"...":"",b=h+i.substring(g,m)+v,S=(b.match(new RegExp(qa,"g"))||[]).length;return{summary:b,shownHighlights:S}},RC=t=>{var x,R,w,A,_,D,z;if(!t||typeof t!="object")return{id:"",title:"",plainTitle:"",summary:"",content:"",viewCount:0,url:"",hasHighlightedTitle:!1,hasHighlightedContent:!1,hiddenHighlights:0};const a=t._id||t.objectID||"",i=((R=(x=t._highlightResult)==null?void 0:x.kb_title)==null?void 0:R.value)||t.kb_title||"",o=Uf(i),u=_0(i),c=Qp(i),d=((A=(w=t._snippetResult)==null?void 0:w.kb_body)==null?void 0:A.value)||t.kb_body||"",g=Uf(d),m=Qp(d),{summary:h,shownHighlights:v}=CC(d),S=(((z=(D=(_=t._highlightResult)==null?void 0:_.kb_body)==null?void 0:D.value)==null?void 0:z.match(new RegExp(od,"g")))||[]).length-v;return{id:a,title:o,plainTitle:u,summary:h,content:g,viewCount:t.kb_viewcount||0,url:wC(a),hasHighlightedTitle:c,hasHighlightedContent:m,hiddenHighlights:S}},EC=t=>{if(!t||typeof t!="object")return{articles:[],pagination:{currentPageHits:0,page:1,totalPages:0,totalHits:0,hitsPerPage:10}};const a=Array.isArray(t.suggestedArticles)?t.suggestedArticles.map(RC):[];if(!t.pagination)return{articles:a,pagination:{currentPageHits:a.length,page:1,totalPages:a.length>0?1:0,totalHits:a.length,hitsPerPage:a.length>0?a.length:10}};const{totalHits:i=0,page:o=1,totalPages:u=1,hitsPerPage:c=10}=t.pagination,d={currentPageHits:a.length,page:o,totalPages:u,totalHits:i,hitsPerPage:c};return{articles:a,pagination:d}},_C=t=>{if(!t||!t.articles||t.articles.length===0)return;const a=t.articles.map(i=>({title:i.title,content:i.summary,url:i.url}));return{category:Xa,pagination:t.pagination,content:a}},TC={highlightPreTag:"__aa-highlight__",highlightPostTag:"__/aa-highlight__",hitsPerPage:10,attributesToHighlight:["kb_title","kb_body"],attributesToSnippet:["kb_body:60"],attributesToRetrieve:["kb_title","kb_body","kb_viewcount","kb_seo_description","kb_regional_visibility"]},AC=(t,a={})=>{const i={...TC,page:a.page||1},u={data:{searchTerms:t||"",algoliaArgs:i}},c={revalidateOnFocus:!1,revalidateIfStale:!0,dedupingInterval:3e3},d=!a.disabled,g=Wa({endpointConfig:Xl.wiki,shouldFetch:d},{...u,...c}),m=g.data?_C(EC(g.data)):void 0;return{...g,data:m}},Ka=t=>t.toLowerCase().replace(/&/g,"and"),di=(t,a)=>{if(!t)return"";if(!a||a.trim()==="")return t;const i=a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),o=new RegExp(i,"gi");return t.replace(o,u=>`<mark>${u}</mark>`)},MC=t=>`/store-ui/#%2Fstore%2F%3FsearchTerm%3D${encodeURIComponent(t)}`,OC=(t,a)=>({product:di(t.title,a),desc:di(t.description,a),price:t.defaultPrice,thumbnail:t.images&&t.images.length>0?t.images[0]:"",status:t.isAvailableInRegion?sd:e0,brand:t.brand,category:t.category,currency:t.currency,id:t.id,images:t.images||[],rowUrl:MC(t.id)}),DC=(t,a=1,i=null)=>{if(!t||typeof t!="object"||!t.data)return{products:[],pagination:{currentPageHits:0,page:a,totalPages:0,totalHits:0,hitsPerPage:10,hasNextPage:!1}};const o=Array.isArray(t.data.products)?t.data.products.map(m=>OC(m,i)):[],u=t.data.totalItems||0,c=t.data.itemsPerPage||(u>0?u:10),d=c>0?Math.ceil(u/c):u>0?1:0,g={currentPageHits:o.length,page:a,totalPages:d,totalHits:u,hitsPerPage:c,hasNextPage:d>a};return{products:o,pagination:g}},NC=t=>{if(!t||!t.products||t.products.length===0)return;const a=t.products.map(i=>({...i,product:i.product.replace(/<(?!\/?mark\b)[^>]+(>|$)/g,""),desc:i.desc.replace(/<(?!\/?mark\b)[^>]+(>|$)/g,"")}));return{category:fs,pagination:t.pagination,content:a}},zC=(t,a={})=>{const i=a.limit||10,o=a.page||1,u=a.clubId||"",c={revalidateOnFocus:!1,revalidateIfStale:!0,dedupingInterval:3e3},d=!a.disabled,g=new URLSearchParams;t&&g.append("search",t),g.append("limit",i.toString()),g.append("page",o.toString());const m=Wa({endpointConfig:Xl.storeProducts,pathReplace:{":clubId":u},shouldFetch:d,key:`${Xl.storeProducts.path}?${g.toString()}`},c),h=m.data?NC(DC(m.data,void 0,t)):void 0;return{...m,data:h}},HC=t=>{try{return`/designer/designs?tag=${encodeURIComponent(t.tags[0])}&designId=${encodeURIComponent(t.id)}`}catch{return}},LC=t=>{var c,d,g,m,h,v;const a=t.height&&t.width?`${t.width}×${t.height}`:"",i=t.format||"",o=t.resolution||0,u=((d=(c=t._snippetResult)==null?void 0:c.name)==null?void 0:d.value)||t.name;return{folder_filename:t.name,file_type:t.designType,dimensions:a,output_type:i,resolution:o,preview:((m=(g=t.thumbnails)==null?void 0:g.find(b=>b.type==="thumbnail.sm"))==null?void 0:m.location)||t.thumbnail,id:t.id,name:u,designType:t.designType,isPublished:t.isPublished,tags:t.tags,updated:t.updated,url:t.url,objectID:t.objectID,htmlContent:((v=(h=t._snippetResult)==null?void 0:h.htmlContent)==null?void 0:v.value)||t.htmlContent,rowUrl:HC(t)}},UC=t=>{var u,c,d,g,m,h,v,b,S,x;const a=(t.designs||[]).map(LC),i={currentPageHits:a.length,totalHits:((u=t.pagination)==null?void 0:u.totalHits)||((c=t.results)==null?void 0:c.nbHits)||0,totalPages:((d=t.pagination)==null?void 0:d.totalPages)||((g=t.results)==null?void 0:g.nbPages)||0,page:((m=t.pagination)==null?void 0:m.currentPage)||((h=t.results)==null?void 0:h.page)||0,hitsPerPage:((v=t.pagination)==null?void 0:v.hitsPerPage)||((b=t.results)==null?void 0:b.hitsPerPage)||10},o={...t.facets,designType:((S=t.facets)==null?void 0:S.designType)||((x=t.facets)==null?void 0:x.designTypes)};return{designs:a,pagination:i,facets:o,tags:t.tags||[]}},jC=t=>{if(!t||!t.designs||t.designs.length===0)return;const a=t.designs.map(i=>({...i,name:i.name.replace(/<(?!\/?em\b)[^>]+(>|$)/g,""),folder_filename:i.name.replace(/<(?!\/?em\b)[^>]+(>|$)/g,""),tags:i.tags||[]}));return{category:ds,pagination:t.pagination,content:a}},VC=(t,a={})=>{const{limit:i=10,page:o=1,designType:u,category:c,club:d,adminQuery:g}=a,m={revalidateOnFocus:!1,revalidateIfStale:!0,dedupingInterval:3e3},h=!a.disabled,v={searchTerms:t||"",hitsPerPage:i,page:o,club:d||"",adminQuery:g||localStorage.getItem("designerAllDesigns")==="admin"};u&&(v.designType=u),c&&(v.category=c);const b={data:v},S=Wa({endpointConfig:Xl.marketingPrint,shouldFetch:h},{...b,...m}),x=S.data?jC(UC(S.data)):void 0;return{...S,data:x}};function BC(t){try{if(typeof t=="string"){if(!isNaN(parseInt(t,10))){if(t.length===10)return new Date(parseInt(t,10)*1e3);if(t.length===13)return new Date(parseInt(t,10))}if(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(t)){const i=new Date(t);return isNaN(i.getTime())?null:i}if(/^\d{1,2}\/\d{1,2}\/\d{2}$/.test(t)){const[i,o,u]=t.split("/").map(Number),c=u+(u>=50?1900:2e3),d=new Date(c,i-1,o);return isNaN(d.getTime())?null:d}const a=new Date(t);return isNaN(a.getTime())?null:a}else{if(typeof t=="number")return t.toString().length===10?new Date(t*1e3):new Date(t);if(t instanceof Date)return isNaN(t.getTime())?null:t}return null}catch(a){return console.error("Error converting to Date:",a),null}}function T0(t,a="medium"){if(!t)return"";const i=BC(t);if(!i)return String(t);try{switch(a){case"relative":return GC(i);case"short":return new Intl.DateTimeFormat("en-US",{month:"numeric",day:"numeric",year:"2-digit"}).format(i);case"long":return new Intl.DateTimeFormat("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric",hour:"numeric",minute:"numeric"}).format(i);case"medium":default:return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric"}).format(i)}}catch(o){return console.error("Error formatting date:",o),String(t)}}function GC(t){const i=new Date().getTime()-t.getTime(),o=Math.floor(i/1e3),u=Math.floor(o/60),c=Math.floor(u/60),d=Math.floor(c/24),g=Math.floor(d/7),m=Math.floor(d/30),h=Math.floor(d/365);return o<60?"just now":u<60?`${u} minute${u!==1?"s":""} ago`:c<24?`${c} hour${c!==1?"s":""} ago`:d<7?`${d} day${d!==1?"s":""} ago`:g<4?`${g} week${g!==1?"s":""} ago`:m<12?`${m} month${m!==1?"s":""} ago`:`${h} year${h!==1?"s":""} ago`}const md=(t,a,i)=>{if(!t||t.length===0)return{paginatedItems:[],pagination:{currentPageHits:0,page:a,totalPages:0,totalHits:0,hitsPerPage:i}};const o=t.length,u=Math.ceil(o/i),c=Math.max(1,Math.min(a,u)),d=(c-1)*i,g=d+i,m=t.slice(d,g),h={currentPageHits:m.length,page:c,totalPages:u,totalHits:o,hitsPerPage:i};return{paginatedItems:m,pagination:h}},PC=t=>{var v,b,S,x,R,w,A,_;const a=t.firstName?`${t.firstName} ${t.lastName||""}`.trim():t.personID,i=t.avatar||t.shadowPhoto||"",o=t.avatar&&t.shadowPhoto?i===t.avatar?t.shadowPhoto:t.avatar:"",u=t.age||((v=t.demographics)!=null&&v.ageRange?Math.round((t.demographics.ageRange.High+t.demographics.ageRange.Low)/2):void 0),c=(b=t.demographics)!=null&&b.ageRange?`${t.demographics.ageRange.Low}-${t.demographics.ageRange.High}`:void 0,d=t.gender||((x=(S=t.demographics)==null?void 0:S.gender)==null?void 0:x.Value)||void 0,g=(R=t.membership)==null?void 0:R.name,m=((A=(w=t.membership)==null?void 0:w.accessType)==null?void 0:A.name)||((_=t.membership)==null?void 0:_.divisionname);let h;return u&&c?h=`${u} yrs. old (${c})`:c?h=`${c} yrs. old`:u&&(h=`${u} yrs. old`),{id:t.personID,name:a,clubID:t.clubID||"",status:t.membershipStatus||"",memberID:t.memberID,avatar:i,thumbnailFallback:o,gender:d,age:u,ageRange:c,parseAge:h,isIdentified:t.isIdentified===1,lastUpdated:t.updated?T0(t.updated,"medium"):void 0,membershipName:g,membershipType:m,rowUrl:`/cctv-ui/#${encodeURIComponent(`/cctv/people/${t.personID}`)}`,category:Ia}},kC=(t,a=1,i=10)=>{if(!t||typeof t!="object"||!t.data)return{people:[],pagination:{currentPageHits:0,page:a,totalPages:0,totalHits:0,hitsPerPage:i}};const o=Array.isArray(t.data)?t.data.map(PC):[],{paginatedItems:u,pagination:c}=md(o,a,i);return{people:u,pagination:c}},$C=t=>{if(!(!t||!t.people||!Array.isArray(t.people)||t.people.length===0))return{category:Ia,pagination:t.pagination,content:t.people.map(a=>({...a,category:Ia}))}},qC=(t,a={})=>{var v;const{limit:i=10,page:o=1,clubId:u="",disabled:c=!1}=a,d=!c,g=new URLSearchParams;t&&g.append("search",t);const m=Wa({endpointConfig:Xl.cctvPeople,pathReplace:{":clubId":u},shouldFetch:d,key:`${Xl.cctvPeople.path.replace(":clubId",u)}?${g.toString()}`}),h=m.data?$C(kC(m.data,o,i)):void 0;return{...m,data:h,nextToken:((v=m.data)==null?void 0:v.nextToken)??null}},Wp=(t,a)=>{const i=Ka(t),o=Ka(a);return i.indexOf(o)},YC=(t,a,i=3)=>{if(!t||!t.trim())return{items:[],totalCount:0};const o=Ka(t),c=[...a.filter(d=>{const g=Ka(d.title);return o.split(/\s+/).some(h=>h.length<2?!1:g.includes(h))||g.includes(o)})].sort((d,g)=>{const m=Wp(d.title,t),h=Wp(g.title,t);return m-h});return{items:c.slice(0,i),totalCount:c.length}},Jp=(t,a=1,i=10)=>{if(!t||!t.items||t.items.length===0)return{features:[],pagination:{currentPageHits:0,page:a,totalPages:0,totalHits:0,hitsPerPage:i}};const o=t.items,{paginatedItems:u,pagination:c}=md(o,a,i);return{features:u,pagination:c}},jf=Qa({features:null,isLoaded:!1}),FC=t=>{jf.features=t,jf.isLoaded=!0},XC=()=>!1,ev=(t,a)=>{if(!t||!t.features||t.features.length===0)return;const i=t.pagination;return{category:gs,pagination:i,content:t.features.map(o=>({content:di(o.title,a),url:o.url}))}},IC=(t,a={})=>{const[i,o]=C.useState(!1),[u,c]=C.useState(null),[d,g]=C.useState(void 0),m=pl(jf).features||[],h=a.page||1,v=a.limit||3;return C.useEffect(()=>{const b=async()=>{try{o(!0),c(null),setTimeout(()=>{const S=YC(t,[...m]),x=Jp(S,h,v),R=ev(x,t||"");g(R),o(!1)},50)}catch(S){c(S instanceof Error?S:new Error(String(S))),o(!1)}};a.disabled?(g(ev(Jp({items:[]},h,v),t||"")),o(!1)):b()},[t,m,h,v,a.disabled]),{data:d,isLoading:i,error:u}},KC=t=>{try{return btoa(unescape(encodeURIComponent(t)))}catch(a){return console.error("Failed to btoa string:",a),""}},ZC=(t,a)=>{if(!t||!t.trim().startsWith("<svg"))return t;const i=t.match(/<svg[^>]*>/);if(!i)return t;let o=i[0];const u=o,c=/\sfill=(["'])(.*?)\1/;if(c.test(o))o=o.replace(c,` fill="${a}"`);else if(o.endsWith("/>"))o=o.replace(/\/>$/,` fill="${a}"/>`);else if(o.endsWith(">"))o=o.replace(/>$/,` fill="${a}">`);else return t;return t.replace(u,o)},QC=(t,a,i=1,o=10)=>{if(!t||t.length===0)return;let u=[];t.forEach(m=>{const h=ZC(m.svg,"oklch(63.28% 0.0388 258.86)"),v=`data:image/svg+xml;base64,${KC(h)}`,b=Ka(a);m.subtopics.forEach(S=>{const x=`${m.topic} - ${S.subtopic}`,R=Ka(x);if(R.includes(b)){const w=di(m.topic,a),A=di(S.subtopic,a);u.push({id:`${m._id}-${S.id}`,topic:w,subtopic:A,icon:v,label:R,url:`/openkb/topic/${m._id}/articles?subtopic=${S.id}`})}})});const c=u.map(m=>({title:m.subtopic,content:m.topic,url:`/wiki/#${encodeURIComponent(m.url)}`,icon:m.icon})),{paginatedItems:d,pagination:g}=md(c,i,o);return{category:ms,pagination:g,content:d}},WC=(t,a)=>{const i=!(a!=null&&a.disabled),o=a==null?void 0:a.page,u=a==null?void 0:a.itemsPerPage,{data:c,error:d,isLoading:g,isValidating:m}=Wa({endpointConfig:Xl.topics,shouldFetch:i});return{data:c!=null&&c.data&&i?QC(c.data,t,o,u):void 0,isLoading:g||i&&m&&!c,error:d}},Vf=({className:t,value:a,size:i="md",leadingEl:o})=>{const u={sm:"text-[10px] font-medium px-1 my-px",md:"text-[11px] px-1.5 my-px"};return E.jsxs("div",{className:Be("rounded-sm text-slate-450 bg-slate-75 border border-slate-200 flex overflow-x-hidden",t),children:[E.jsx("div",{className:Be(u[i],"flex items-center align-self-center"),children:a}),o&&E.jsx("div",{className:"align-self-stretch",children:o})]})},Jt=Qa({selectedCategory:void 0,searchTerm:"",page:1}),vs=t=>{const a=new URL(window.location.href),i=a.searchParams;t.selectedCategory?i.set("category",t.selectedCategory):t.selectedCategory===void 0&&"selectedCategory"in t&&i.delete("category"),t.page!==void 0&&(t.page>1?i.set("page",t.page.toString()):i.delete("page")),window.history.replaceState({},"",`${a.pathname}${i.toString()?`?${i.toString()}`:""}`)},JC=()=>{const a=new URL(window.location.href).searchParams,i=a.get("category"),o=a.get("page");if(i&&(Jt.selectedCategory=i),o){const u=parseInt(o,10);!isNaN(u)&&u>0&&(Jt.page=u)}},A0=t=>{t?(Jt.selectedCategory=t,Jt.page=1,vs({selectedCategory:t,page:1})):nR()},eR=t=>{Jt.searchTerm=t,vs({searchTerm:t})},tR=t=>{Jt.page=t,vs({page:t})},nR=()=>{Jt.selectedCategory=void 0,Jt.page=1,vs({selectedCategory:void 0,page:1})},lR=({label:t})=>E.jsxs("div",{className:"text-[11px] text-slate-450 pl-1.5 flex items-center bg-slate-200 h-full",children:[t,E.jsx(R0,{className:"size-3.5 stroke-slate-350"})]}),Va=({title:t,paginationInfo:a,category:i})=>{const o=pl(Jt),u=a==null?void 0:a.currentPageHits,c=a==null?void 0:a.totalHits,d=a==null?void 0:a.page,g=a==null?void 0:a.hasNextPage,m=a==null?void 0:a.totalPages,h=u&&d?u*d:u;let v;m&&d&&h&&(m>d?v=h:m===d&&(v=c));let b=v&&`${v}${v&&c?" of ":""}${c}`;g&&(b=`${v} & more...`);const S=()=>{i&&A0(i)};return E.jsxs("div",{className:"flex items-center justify-start gap-2",children:[E.jsx("h3",{className:"text-sm font-bold text-fg-header",children:t}),!!b&&a&&a.hidePagination!==!0&&E.jsx("span",{className:"text-xs text-fg-secondary cursor-pointer",onClick:S,children:E.jsx(Vf,{value:b,leadingEl:!o.selectedCategory&&E.jsx(lR,{label:"View All"})})})]})},aR=({value:t,className:a="",withHighlight:i=!1,maxLength:o})=>{if(!t)return E.jsx("span",{className:"text-gray-400",children:"--"});let u=t;return o&&t.length>o&&(u=t.substring(0,o)+"..."),i?E.jsx("div",{className:`text-fg-body ${a}`,dangerouslySetInnerHTML:{__html:u}}):E.jsx("div",{className:`text-fg-body ${a}`,children:u})},rR=({value:t,text:a,className:i="",withHighlight:o=!1,maxLength:u,enableTruncation:c=!1,onClick:d})=>{if(!t)return E.jsx("span",{className:"text-gray-400",children:"--"});const g=a||t;let m=g;u&&g.length>u&&(m=g.substring(0,u)+"...");const h=b=>{d&&(b.preventDefault(),d(t,b))},v=Be(i,{"block truncate":c});return E.jsx("div",{className:v,children:E.jsx("a",{href:t,className:`hover:underline cursor-pointer ${i}`,onClick:h,target:"_blank",rel:"noopener noreferrer",title:g,...o?{dangerouslySetInnerHTML:{__html:m}}:{children:m}})})},iR=({value:t,format:a="medium",className:i="",maxLength:o,withHighlight:u=!1})=>{if(!t)return E.jsx("span",{className:"text-gray-400",children:"--"});try{let c=T0(t,a);return o&&c.length>o&&(c=c.substring(0,o)+"..."),u?E.jsx("span",{className:`text-fg-body ${i}`,dangerouslySetInnerHTML:{__html:c}}):E.jsx("span",{className:`text-fg-body ${i}`,children:c})}catch(c){return console.error("Error in DateCell:",c),E.jsx("span",{className:`text-fg-body ${i}`,children:String(t)})}},oR=({value:t,format:a="number",minimumFractionDigits:i=0,maximumFractionDigits:o=2,currencyCode:u="USD",className:c=""})=>{if(t==null||isNaN(t))return E.jsx("span",{className:"text-gray-400",children:"--"});let d;switch(a){case"percent":d=new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:i,maximumFractionDigits:o}).format(t);break;case"currency":d=new Intl.NumberFormat("en-US",{style:"currency",currency:u,minimumFractionDigits:i,maximumFractionDigits:o}).format(t);break;case"compact":d=new Intl.NumberFormat("en-US",{notation:"compact",compactDisplay:"short",minimumFractionDigits:i,maximumFractionDigits:o}).format(t);break;case"number":default:d=new Intl.NumberFormat("en-US",{minimumFractionDigits:i,maximumFractionDigits:o}).format(t);break}return E.jsx("span",{className:`text-fg-body ${c}`,children:d})},sR=({value:t,statusMapping:a={},className:i=""})=>{if(!t)return E.jsx("span",{className:"text-gray-400",children:"--"});const c={...{active:"success",[sd]:"available"},...a}[t]||"unavailable",g=(m=>{switch(m){case"success":case"available":return{containerClass:"text-emerald-600 bg-emerald-100",dotClass:"bg-emerald-600"};case"unavailable":default:return{containerClass:"text-fg-body bg-slate-100",dotClass:"bg-fg-secondary"}}})(c);return E.jsxs("span",{className:`inline-flex items-center text-xs px-2 py-1 rounded-full text-nowrap 
      ${g.containerClass} ${i}`,children:[E.jsx("span",{className:`w-2 h-2 mr-1.5 rounded-full ${g.dotClass}`}),t]})},uR=({value:t,alt:a="",width:i=48,height:o=48,fallback:u="",className:c="",onClick:d})=>{const[g,m]=We.useState(!1);if(!t&&u&&We.isValidElement(u))return E.jsx("div",{className:`overflow-hidden rounded ${c}`,style:{width:`${i}px`,height:`${o}px`},children:u});if(!t)return E.jsx("span",{className:"text-gray-400",children:"--"});const h=b=>{var x;m(!0);const S=b.target;typeof u=="string"&&u&&S.src!==u?S.src=u:(S.style.display="none",(x=S.parentElement)==null||x.classList.add("bg-gray-200"))},v=b=>{d&&(b.preventDefault(),d(t,b))};return E.jsx("div",{className:`overflow-hidden rounded ${c}`,style:{width:`${i}px`,height:`${o}px`},children:g?We.isValidElement(u)?u:E.jsx("span",{className:"text-gray-400",children:"--"}):E.jsx("img",{src:t,alt:a,width:i,height:o,onError:h,onClick:d?v:void 0,className:`object-cover w-full h-full ${d?"cursor-pointer hover:opacity-90":""}`})})},tv=({product:t,brand:a,categories:i,className:o=""})=>E.jsxs("div",{className:"flex flex-col gap-1",children:[E.jsx("div",{className:Be("font-medium",o),dangerouslySetInnerHTML:{__html:t}}),E.jsxs("div",{className:"flex flex-wrap gap-1.5 mt-1",children:[a&&E.jsx(Vf,{value:a,className:"uppercase",size:"sm"}),i.map((u,c)=>E.jsx(Vf,{value:u,className:"uppercase",size:"sm"},c))]})]}),hd={maxLength:500,className:"",withHighlight:!1,isNoteUI:!1},ln=t=>{const a={...hd,...t};return i=>{const o=i.getValue();return We.createElement(aR,{value:o,withHighlight:a.withHighlight,maxLength:a.maxLength,className:Be(a.isNoteUI&&"bg-slate-50 border-l-[3px] border-line p-2",a.className)})}},cR=t=>{const a={...hd,...t};return i=>{const o=i.getValue(),u=t!=null&&t.textAccessor?String(i.row.original[t.textAccessor]):void 0;return We.createElement(rR,{value:o,text:u,withHighlight:a.withHighlight,maxLength:a.maxLength,className:a.className,enableTruncation:t==null?void 0:t.enableTruncation,onClick:t==null?void 0:t.onClick})}},fR=t=>{const a={...hd,...t};return i=>{const o=i.getValue();return We.createElement(iR,{value:o,format:t==null?void 0:t.format,className:a.className,maxLength:a.maxLength,withHighlight:a.withHighlight})}},dR=t=>a=>{const i=a.getValue();return We.createElement(oR,{value:i,format:t==null?void 0:t.format,minimumFractionDigits:t==null?void 0:t.minimumFractionDigits,maximumFractionDigits:t==null?void 0:t.maximumFractionDigits,currencyCode:t==null?void 0:t.currencyCode,className:""})},nv=t=>a=>{const i=a.getValue();return We.createElement(sR,{value:i,statusMapping:t==null?void 0:t.statusMapping,className:(t==null?void 0:t.className)||""})},af=t=>a=>{const i=a.getValue()||(t==null?void 0:t.fallback)||"",o=t!=null&&t.altAccessor?String(a.row.original[t.altAccessor]):"";return We.createElement(uR,{value:i,alt:o,width:t==null?void 0:t.width,height:t==null?void 0:t.height,fallback:t==null?void 0:t.fallback,className:(t==null?void 0:t.className)||"",onClick:t==null?void 0:t.onClick})};var $o={exports:{}},rf,lv;function gR(){return lv||(lv=1,rf={AED:"د.إ",AFN:"؋",ALL:"L",AMD:"֏",ANG:"ƒ",AOA:"Kz",ARS:"$",AUD:"$",AWG:"ƒ",AZN:"₼",BAM:"KM",BBD:"$",BDT:"৳",BGN:"лв",BHD:".د.ب",BIF:"FBu",BMD:"$",BND:"$",BOB:"$b",BOV:"BOV",BRL:"R$",BSD:"$",BTC:"₿",BTN:"Nu.",BWP:"P",BYN:"Br",BYR:"Br",BZD:"BZ$",CAD:"$",CDF:"FC",CHE:"CHE",CHF:"CHF",CHW:"CHW",CLF:"CLF",CLP:"$",CNH:"¥",CNY:"¥",COP:"$",COU:"COU",CRC:"₡",CUC:"$",CUP:"₱",CVE:"$",CZK:"Kč",DJF:"Fdj",DKK:"kr",DOP:"RD$",DZD:"دج",EEK:"kr",EGP:"£",ERN:"Nfk",ETB:"Br",ETH:"Ξ",EUR:"€",FJD:"$",FKP:"£",GBP:"£",GEL:"₾",GGP:"£",GHC:"₵",GHS:"GH₵",GIP:"£",GMD:"D",GNF:"FG",GTQ:"Q",GYD:"$",HKD:"$",HNL:"L",HRK:"kn",HTG:"G",HUF:"Ft",IDR:"Rp",ILS:"₪",IMP:"£",INR:"₹",IQD:"ع.د",IRR:"﷼",ISK:"kr",JEP:"£",JMD:"J$",JOD:"JD",JPY:"¥",KES:"KSh",KGS:"лв",KHR:"៛",KMF:"CF",KPW:"₩",KRW:"₩",KWD:"KD",KYD:"$",KZT:"₸",LAK:"₭",LBP:"£",LKR:"₨",LRD:"$",LSL:"M",LTC:"Ł",LTL:"Lt",LVL:"Ls",LYD:"LD",MAD:"MAD",MDL:"lei",MGA:"Ar",MKD:"ден",MMK:"K",MNT:"₮",MOP:"MOP$",MRO:"UM",MRU:"UM",MUR:"₨",MVR:"Rf",MWK:"MK",MXN:"$",MXV:"MXV",MYR:"RM",MZN:"MT",NAD:"$",NGN:"₦",NIO:"C$",NOK:"kr",NPR:"₨",NZD:"$",OMR:"﷼",PAB:"B/.",PEN:"S/.",PGK:"K",PHP:"₱",PKR:"₨",PLN:"zł",PYG:"Gs",QAR:"﷼",RMB:"￥",RON:"lei",RSD:"Дин.",RUB:"₽",RWF:"R₣",SAR:"﷼",SBD:"$",SCR:"₨",SDG:"ج.س.",SEK:"kr",SGD:"S$",SHP:"£",SLL:"Le",SOS:"S",SRD:"$",SSP:"£",STD:"Db",STN:"Db",SVC:"$",SYP:"£",SZL:"E",THB:"฿",TJS:"SM",TMT:"T",TND:"د.ت",TOP:"T$",TRL:"₤",TRY:"₺",TTD:"TT$",TVD:"$",TWD:"NT$",TZS:"TSh",UAH:"₴",UGX:"USh",USD:"$",UYI:"UYI",UYU:"$U",UYW:"UYW",UZS:"лв",VEF:"Bs",VES:"Bs.S",VND:"₫",VUV:"VT",WST:"WS$",XAF:"FCFA",XBT:"Ƀ",XCD:"$",XOF:"CFA",XPF:"₣",XSU:"Sucre",XUA:"XUA",YER:"﷼",ZAR:"R",ZMW:"ZK",ZWD:"Z$",ZWL:"$"}),rf}var av;function mR(){if(av)return $o.exports;av=1;const t=gR();return $o.exports=function(i){if(typeof i!="string")return;const o=i.toUpperCase();if(Object.prototype.hasOwnProperty.call(t,o))return t[o]},$o.exports.currencySymbolMap=t,$o.exports}var hR=mR();const pR=Zf(hR),vR=(t="USD")=>{let a="$";try{a=pR(t.toUpperCase())||a}catch(i){console.error("Error getting currency symbol:",i)}return a},yR=["BIF","CLP","DJF","GNF","JPY","KMF","KRW","MGA","PYG","RWF","UGX","VND","VUV","XAF","XOF","XPF"],SR=(t,a="USD",i={})=>{const{locale:o="en-US"}=i,u=i.decimals!==void 0?i.decimals:yR.includes(a.toUpperCase())?0:2,c=vR(a),d=new Intl.NumberFormat(o,{minimumFractionDigits:u,maximumFractionDigits:u}).format(t);return c==="د.إ"?`${d} ${c}`:`${c}${d}`},qo=(t,a)=>{if(!t[a])return;const i=t[a],o=window.location.origin,u=new URL(i,o).href;window.open(u,"_blank")},bR="data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20opacity='0.5'%3e%3cpath%20d='M10.04%2018.3333C5.43219%2018.3333%201.70676%2014.6078%201.70676%2010.0001C1.70676%205.3923%205.43219%201.66687%2010.04%201.66687C14.6477%201.66687%2018.3731%205.3923%2018.3731%2010.0001C18.3731%2014.6078%2014.5987%2018.3333%2010.04%2018.3333ZM10.04%202.64725C5.97139%202.64725%202.68713%205.93151%202.68713%2010.0001C2.68713%2014.0686%205.97139%2017.3529%2010.04%2017.3529C14.1085%2017.3529%2017.3928%2014.0686%2017.3928%2010.0001C17.3928%205.93151%2014.0595%202.64725%2010.04%202.64725Z'%20fill='%23A3ACBA'/%3e%3cpath%20d='M5.23613%2016.0293L4.35379%2015.6372C4.59888%2015.0489%205.38318%2014.7058%206.2165%2014.3137C7.04982%2013.9215%208.07922%2013.4803%208.07922%2012.9411V12.2059C7.78511%2011.9608%207.29492%2011.4216%207.19688%2010.6373C6.95179%2010.3922%206.55964%209.95099%206.55964%209.36276C6.55964%209.01963%206.70669%208.72552%206.80473%208.52944C6.70669%208.13729%206.60866%207.40201%206.60866%206.81379C6.60866%204.90205%207.93216%203.62756%2010.04%203.62756C10.6282%203.62756%2011.3635%203.77462%2011.7556%204.21579C12.687%204.41186%2013.4713%205.49028%2013.4713%206.81379C13.4713%207.64711%2013.3242%208.33337%2013.2262%208.6765C13.3242%208.82356%2013.4223%209.06865%2013.4223%209.36276C13.4223%2010%2013.0791%2010.4412%2012.785%2010.6373C12.687%2011.4216%2012.2458%2011.9117%2011.9517%2012.1568V12.9411C11.9517%2013.3823%2012.834%2013.7254%2013.6183%2014.0195C14.5497%2014.3627%2015.5301%2014.7548%2015.8732%2015.5391L14.9419%2015.8823C14.7948%2015.4901%2014.0105%2015.196%2013.2752%2014.9509C12.1968%2014.5588%2010.9713%2014.1176%2010.9713%2012.9902V11.7157L11.2164%2011.5686C11.2164%2011.5686%2011.8046%2011.1765%2011.8046%2010.3922V10.049L12.0988%209.90197C12.1478%209.90197%2012.3929%209.75491%2012.3929%209.36276C12.3929%209.26473%2012.2948%209.11767%2012.2458%209.06865L12.0497%208.87258L12.1478%208.62748C12.1478%208.62748%2012.3929%207.84318%2012.3929%206.8628C12.3929%205.93145%2011.8537%205.24518%2011.4125%205.24518H11.1184L10.9713%205.00009C10.9713%204.80401%2010.6282%204.60794%2010.04%204.60794C8.52039%204.60794%207.58903%205.44126%207.58903%206.81379C7.58903%207.45103%207.83413%208.52944%207.83413%208.52944L7.88314%208.77454L7.68707%209.01963C7.63805%209.01963%207.54001%209.16669%207.54001%209.36276C7.54001%209.60786%207.83413%209.90197%207.98118%2010L8.17726%2010.1471V10.3922C8.17726%2011.1274%208.8145%2011.5196%208.8145%2011.5686L9.0596%2011.7157V12.9902C9.0596%2014.1666%207.78511%2014.7548%206.60866%2015.245C6.06945%2015.4411%205.33417%2015.7842%205.23613%2016.0293Z'%20fill='%23A3ACBA'/%3e%3c/g%3e%3c/svg%3e",rv=pi(),ri=pi(),$l=pi(),ql=pi(),M0={[Xa]:fx,[fs]:dx,[ds]:gx,[Ia]:mx,[gs]:hx,[ms]:vx,[hl]:yx},of=t=>M0[t]||px,xR={[Xa]:{columns:[rv.accessor("url",{header:"Title",cell:t=>cR({textAccessor:"title",withHighlight:!0,className:"max-w-[18rem] w-max"})(t)}),rv.accessor("content",{header:"Content",cell:ln({withHighlight:!0,isNoteUI:!0})})],getRowClickHandler:t=>qo(t,"url")},[fs]:{columns:[ri.accessor("thumbnail",{header:"Image",cell:af({altAccessor:"product",width:80,height:80,className:"shadow-sm rounded-md"})}),ri.accessor("product",{header:"Product",cell:({row:t})=>{const{product:a,brand:i,category:o}=t.original;return We.createElement(tv,{product:a,brand:i,categories:[o],className:"font-medium"})}}),ri.accessor("desc",{header:"Description",cell:ln({withHighlight:!0,maxLength:150,isNoteUI:!0})}),ri.accessor(t=>SR(t.price,t.currency),{id:"formattedPrice",header:"Price",cell:ln({className:"text-sm"})}),ri.accessor("status",{header:"Availability",cell:nv({statusMapping:{[sd]:"available",[e0]:"unavailable"}})})],getRowClickHandler:t=>qo(t,"rowUrl")},[ds]:{columns:[$l.accessor("folder_filename",{header:"Filename",cell:({row:t})=>{const{folder_filename:a,tags:i=[]}=t.original;return We.createElement(tv,{product:a,brand:"",categories:i,className:"font-medium"})}}),$l.accessor("preview",{header:"Preview",cell:af({width:60,height:40})}),$l.accessor("dimensions",{header:"Dimensions (PX)",cell:ln()}),$l.accessor("htmlContent",{header:"Html Content",cell:ln({withHighlight:!0,isNoteUI:!0})}),$l.accessor("output_type",{header:"Output",cell:ln()}),$l.accessor("resolution",{header:"Resolution (DPI)",cell:dR()}),$l.accessor("updated",{header:"Date",cell:fR({format:"medium"})})],getRowClickHandler:t=>qo(t,"rowUrl")},[Ia]:{columns:[ql.accessor("avatar",{header:"Image",cell:af({altAccessor:"name",width:60,height:60,fallback:bR,className:"rounded-full shadow-sm"})}),ql.accessor("name",{header:"Name",cell:ln({withHighlight:!0,className:"font-medium"})}),ql.accessor("status",{header:"Status",cell:nv({statusMapping:{Current:"success"}})}),ql.accessor("parseAge",{header:"Age",cell:ln()}),ql.accessor("gender",{header:"Gender",cell:ln()}),ql.accessor("lastUpdated",{header:"Last Updated",cell:ln()}),ql.accessor("membershipType",{header:"Membership Type",cell:ln()})],getRowClickHandler:t=>qo(t,"rowUrl")},[gs]:[],[cx]:[],[ms]:[],[hl]:[]},O0=t=>{const a=xR[t];return a?Array.isArray(a)?{columns:a}:{columns:a.columns,getRowClickHandler:a.getRowClickHandler}:(console.warn(`No column configuration found for category: ${t}`),{columns:[]})},wR=({header:t,subHeader:a,link:i,iconUrl:o,iconAlt:u,ariaLabel:c})=>{const d=`${t} - ${a}`;return E.jsx("a",{href:i,target:"_blank",rel:"noopener noreferrer",className:"block px-4 py-3 bg-white border border-line rounded-md shadow-natural-2xs hover:bg-slate-25 focus:outline-none focus:ring-2 focus:ring-accent-blue-400 focus:ring-opacity-50 transition-colors duration-150 ease-in-out h-full","aria-label":c||d,children:E.jsxs("div",{className:"flex space-x-3 items-center",children:[o&&E.jsx("img",{src:o,alt:u||"",className:"w-10 h-10 object-contain flex-shrink-0"}),E.jsx("div",{className:"flex flex-col flex-grow justify-between h-full",children:E.jsxs("div",{className:"text-start",children:[a&&E.jsx("span",{className:"text-xs line-clamp-3 text-fg-secondary",title:a,dangerouslySetInnerHTML:{__html:a}}),E.jsx("h3",{className:"text-sm font-semibold text-fg-body leading-tight group-hover:text-accent-blue transition-colors duration-150 ease-in-out line-clamp-2",title:t,dangerouslySetInnerHTML:{__html:t}})]})})]})})},CR=()=>E.jsx("div",{className:"block p-4 bg-white border border-gray-200 rounded-lg shadow-md h-full",children:E.jsxs("div",{className:"flex items-start space-x-3 h-full animate-pulse",children:[E.jsx("div",{className:"w-10 h-10 bg-slate-300 rounded flex-shrink-0 mt-1"}),E.jsx("div",{className:"flex flex-col flex-grow justify-between h-full w-full",children:E.jsxs("div",{children:[E.jsx("div",{className:"h-2.5 bg-slate-300 rounded-full w-1/3 mb-1.5"}),E.jsx("div",{className:"h-4 bg-slate-300 rounded-full w-3/4 mb-2"}),E.jsx("div",{className:"h-2.5 bg-slate-300 rounded-full w-full mb-1"}),E.jsx("div",{className:"h-2.5 bg-slate-300 rounded-full w-5/6 mb-1"}),E.jsx("div",{className:"h-2.5 bg-slate-300 rounded-full w-full"})]})})]})}),RR=({items:t,containerAriaLabel:a="Scrollable list of search results cards",onItemClick:i})=>!t||t.length===0?null:E.jsx("div",{className:"py-4","aria-label":a,role:"region",children:E.jsx("div",{className:"flex flex-wrap gap-2",children:t.map((o,u)=>E.jsx("div",{className:"flex-shrink-0 w-64 md:w-72",onClick:()=>i==null?void 0:i(o),children:E.jsx(wR,{...o})},o.link+u))})}),ER=(t,a)=>t?t.map(i=>{var o,u,c;return a==="RecentlyViewed"?{subHeader:i.breadcrumb||"",header:i.label||"",link:i.url||"#"}:a=="WikiTopics"?{subHeader:i.title,header:i.description||i.snippet||((o=i.meta)==null?void 0:o.summary)||i.content,link:i.url||"#",iconUrl:i.icon||((u=i.meta)==null?void 0:u.iconUrl),iconAlt:i.icon||(c=i.meta)!=null&&c.iconUrl?i.title||"Icon":void 0}:a=="Hub"?{header:i.content||"",link:i.url||"#"}:{subHeader:i.title,header:i.label||"",link:i.url||"#"}}):[],_R=({title:t,pagination:a,content:i,category:o,isLoading:u=!1,viewType:c,onItemClick:d})=>{const g=u&&!a?void 0:a?{...a}:void 0,m=a?{currentPageHits:0,totalHits:a.totalHits}:{currentPageHits:0,totalHits:0};if(c==="card"){if(u)return E.jsxs("div",{className:"py-2 w-full",children:[E.jsx(Va,{title:t,paginationInfo:g,category:o}),E.jsx("div",{className:"flex overflow-x-auto space-x-4 p-1 mt-2",children:[1,2,3].map((v,b)=>E.jsx("div",{className:"flex-shrink-0 w-72 md:w-80 h-36",children:E.jsx(CR,{})},b))})]});if(!i||i.length===0)return E.jsxs("div",{className:"py-2 w-full",children:[E.jsx(Va,{title:t,paginationInfo:m,category:o}),E.jsx("div",{className:"mt-2",children:E.jsx(fi,{message:`No results to display in card view for ${t}.`,icon:gd})})]});const h=ER(i,o);return E.jsxs("div",{className:"py-2 w-full",children:[E.jsx(Va,{title:t,paginationInfo:g,category:o}),E.jsx(RR,{items:h,containerAriaLabel:`Results for ${t}`,onItemClick:d})]})}else{if(u){const h=O0(o);return E.jsxs("div",{className:"flex flex-col gap-1",children:[E.jsx(Va,{title:t,paginationInfo:g,category:o}),E.jsx(x0,{data:[],isLoading:!0,columns:h.columns})]})}return!i||i.length===0?E.jsxs("div",{className:"flex flex-col gap-1",children:[E.jsx(Va,{title:t,paginationInfo:m,category:o}),E.jsx(iv,{content:[],category:o})]}):E.jsxs("div",{className:"flex flex-col gap-1",children:[E.jsx(Va,{title:t,paginationInfo:g,category:o}),E.jsx(iv,{content:i,category:o})]})}},iv=({content:t,category:a})=>{if(!t||t.length===0)return E.jsx(fi,{});const{columns:i,getRowClickHandler:o}=O0(a),u=C.useMemo(()=>{if(i.length>0)return i;const c=t[0];return c?Object.keys(c).map(d=>({accessorKey:d,header:d.charAt(0).toUpperCase()+d.slice(1),cell:g=>{const m=g.getValue();return typeof m=="string"?E.jsx("span",{dangerouslySetInnerHTML:{__html:m}}):String(m)}})):[]},[t,i]);return E.jsx(x0,{data:t,columns:u,onRowClick:o})};const pd="viewedArticles",ov=10,TR=t=>{try{if(!t.url||!t.label)return console.error("Article must have url and label properties"),!1;const a=Bf(ov),i=a.findIndex(o=>o.url===t.url);return i!==-1&&a.splice(i,1),a.unshift(t),localStorage.setItem(pd,JSON.stringify(a.slice(0,ov))),!0}catch(a){return console.error("Error tracking viewed article:",a),!1}},Bf=(t=4)=>{if(typeof localStorage>"u")return[];try{const a=JSON.parse(localStorage.getItem(pd)||"[]");return a==null||!Array.isArray(a)||a.length,Array.isArray(a)?a.slice(0,t):[]}catch(a){return console.error("Error retrieving recently viewed articles:",a),[]}},AR=()=>{try{return localStorage.removeItem(pd),!0}catch(t){return console.error("Error clearing recently viewed articles:",t),!1}},sv=t=>t?t.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/&amp;/g,"&"):"",MR=t=>{const a=[];return t.topic&&a.push(sv(t.topic)),t.subtopic&&a.push(sv(t.subtopic)),a.join(" > ")},OR=t=>({id:t.id,topic:t.topic,subtopic:t.subsopic,label:t.label,url:t.url,content:t.label||"",breadcrumb:MR({topic:t.topic,subtopic:t.subsopic})}),uv=t=>!t||!Array.isArray(t)?{category:hl,pagination:{totalHits:0,page:1,hitsPerPage:4,totalPages:0,currentPageHits:0,hidePagination:!0},content:[]}:{category:hl,pagination:{totalHits:t.length,page:1,hitsPerPage:t.length,totalPages:1,currentPageHits:t.length,hidePagination:!0},content:t.map(a=>OR(a))},DR=t=>t?{id:t.id,url:t.url,topic:t.topic||"",subsopic:t.subtopic||""}:null,NR=(t={})=>{const{limit:a=4,disabled:i=!1}=t,[o,u]=C.useState(void 0),[c,d]=C.useState(!0),[g,m]=C.useState(void 0);C.useEffect(()=>{if(i){d(!1),u(void 0);return}try{d(!0);const b=Bf(a),S=uv(b);u(S),m(void 0)}catch(b){console.error("Error fetching recently viewed articles:",b),m(b instanceof Error?b:new Error(String(b)))}finally{d(!1)}},[a,i]);const h=C.useCallback(b=>{if(i)return!1;try{const S=TR(b);if(S){const x=Bf(a),R=uv(x);u(R)}return S}catch(S){return console.error("Error tracking recently viewed article:",S),m(S instanceof Error?S:new Error(String(S))),!1}},[a,i]),v=C.useCallback(()=>{if(i)return!1;try{const b=AR();return b&&u({category:(o==null?void 0:o.category)||Xa,pagination:{totalHits:0,page:1,hitsPerPage:a,totalPages:0,currentPageHits:0},content:[]}),b}catch(b){return console.error("Error clearing recently viewed articles:",b),m(b instanceof Error?b:new Error(String(b))),!1}},[i,o==null?void 0:o.category,a]);return{data:o,error:g,isLoading:c,trackArticle:h,clearArticles:v}},D0=Qa({clubId:null}),zR=t=>{D0.clubId=t},HR=()=>{const{clubId:t}=pl(D0);return t},vd=Qa({hasDataCategorySearchMap:{}}),LR=(t,a)=>{vd.hasDataCategorySearchMap[t]=a},UR=()=>{vd.hasDataCategorySearchMap={}},jR=({currentPage:t,totalPages:a,hasNextPage:i,onPageChange:o,className:u})=>{const{t:c}=hs(),d="flex items-center px-3 py-1 cursor-pointer transition-colors text-slate-450 text-sm focus:outline-none rounded-md focus:ring-2 focus:ring-accent-blue-400",g=()=>{t>1&&o(t-1)},m=()=>{(t<a||i)&&o(t+1)},h=t===a&&!i;let v=`${t} of ${a}`;i&&(v=`${t} & more...`);const S=(()=>{if(a<=10)return Array.from({length:a},(A,_)=>_+1);const x=[];let R=Math.max(1,t-4),w=Math.min(a,t+5);t<=5?(R=1,w=10):t>a-5&&(R=a-9,w=a);for(let A=R;A<=w;A++)x.push(A);return x})();return a<=1?null:(i&&(v=`${t} & more...`),E.jsxs("div",{className:Be("flex items-center justify-center gap-4 mt-4 text-slate-400",u),children:[E.jsxs("button",{onClick:g,disabled:t===1,className:Be(d,t===1?"opacity-50 cursor-not-allowed":"hover:bg-slate-100"),"aria-label":c("Previous page"),children:[E.jsx(oC,{className:"size-4 mr-1"}),c("Previous")]}),a>0&&E.jsxs("div",{className:"flex items-center gap-1",children:[S[0]>1&&E.jsxs(E.Fragment,{children:[E.jsx("button",{onClick:()=>o(1),className:Be("px-2 py-1 text-sm rounded cursor-pointer hover:bg-slate-100","focus:outline-none focus:ring-2 focus:ring-accent-blue-400"),"aria-label":c("Go to page 1"),children:"1"}),S[0]>2&&E.jsx("span",{className:"px-1",children:"..."})]}),i&&E.jsxs("div",{className:"text-sm ml-2",children:[c("Page")," ",v]}),!i&&S.map(x=>E.jsx("button",{onClick:()=>o(x),disabled:x===t,className:Be("px-2 py-1 transition-colors text-sm rounded cursor-pointer focus:outline-none focus:ring-2 focus:ring-accent-blue-400",x===t?"bg-accent-blue-400 text-white font-bold hover:bg-accent-blue-300":"hover:bg-accent-blue-400/20 hover:text-accent-blue-400"),tabIndex:0,children:x},x)),S[S.length-1]<a&&E.jsxs(E.Fragment,{children:[S[S.length-1]<a-1&&E.jsx("span",{className:"px-1 text-slate-300",children:"..."}),E.jsx("button",{onClick:()=>o(a),className:Be("px-2 py-1 text-sm rounded cursor-pointer focus:outline-none focus:ring-2 focus:ring-accent-blue-400","hover:bg-accent-blue-400/20 hover:text-accent-blue-400"),"aria-label":c("Jump to page")+` ${a}`,children:a})]})]}),E.jsxs("button",{onClick:m,disabled:h,className:Be(d,h?"opacity-50 cursor-not-allowed":"hover:bg-slate-100"),"aria-label":c("Next page"),children:[c("Next"),E.jsx(R0,{className:"size-4 ml-1"})]})]}))},Ba=t=>t==="",VR=({className:t})=>{const[a,i]=C.useState(!1),o=HR(),{searchTerm:u,selectedCategory:c,page:d=1}=pl(Jt),{data:g,isLoading:m,trackArticle:h}=NR({disabled:u!==""}),{data:v,error:b,isLoading:S}=AC(u,{page:d,disabled:Ba(u)}),{data:x,error:R,isLoading:w}=zC(u,{limit:10,page:d,clubId:o||void 0,disabled:Ba(u)}),{data:A,error:_,isLoading:D}=VC(u,{limit:10,page:d,club:o||void 0,disabled:Ba(u)}),{data:z,error:k,isLoading:P}=qC(u,{limit:10,page:d,clubId:o||void 0,disabled:Ba(u)}),{data:Z,error:I,isLoading:q}=IC(u,{limit:3,page:d,disabled:Ba(u)}),{data:ae,error:ce,isLoading:me}=WC(u,{disabled:Ba(u)}),se=[{id:hl,data:g,isLoading:m,error:void 0,viewType:"card"},{id:Xa,data:v,isLoading:S,error:b||void 0,viewType:"table"},{id:fs,data:x,isLoading:w,error:R||void 0,viewType:"table"},{id:ds,data:A,isLoading:D,error:_||void 0,viewType:"table"},{id:Ia,data:z,isLoading:P,error:k||void 0,viewType:"table"},{id:gs,data:Z,isLoading:q,error:I||void 0,viewType:"card"},{id:ms,data:ae,isLoading:me,error:ce||void 0,viewType:"card"}],fe=!se.some(B=>B.id!==hl&&(B.isLoading||B.data)),de=(B,j)=>{B===Xa&&H(j)},H=B=>{const j=DR(B);j&&h(j)};if(C.useEffect(()=>{se.forEach(B=>{var j;if(!B.isLoading){const ee=Array.isArray((j=B.data)==null?void 0:j.content)&&B.data.content.length>0;d===1&&LR(B.id,ee),c&&B.id===c&&B.id!==hl&&i(ee)}})},[se,c]),C.useEffect(()=>{UR()},[u]),!u||u===""){if(!m&&(!g||!g.content||g.content.length===0)||!u&&fe&&c){let B="Wiki Resources, Performance Hub, Marketing & Print, etc.";return c&&(B=of(c)),E.jsx("div",{className:Be("mt-4"),children:E.jsx(fi,{message:`Search anything on ${B}`,icon:gC})})}}else if((fe||c&&!a)&&u)return E.jsx("div",{className:Be("mt-4"),children:E.jsx(fi,{message:`No search results found for "${u}"${c?" in "+of(c):""}.`,icon:gd})});return E.jsx("div",{className:Be("flex flex-col gap-10 w-full mt-4",t),children:se.map(B=>{var ee,M;if(c&&B.id!==c||!c&&!B.isLoading&&(!B.data||!B.data.content||B.data.content.length===0))return null;const j=of(B.id);return E.jsxs(E.Fragment,{children:[E.jsx(_R,{title:j,pagination:(ee=B.data)==null?void 0:ee.pagination,content:B.data?B.data.content:[],category:B.id,isLoading:B.isLoading,viewType:B.viewType,onItemClick:$=>de(B.id,$)},B.id+"-results"),c&&B.id===c&&((M=B.data)==null?void 0:M.pagination)&&E.jsx(jR,{currentPage:d,totalPages:B.data.pagination.totalPages||1,hasNextPage:B.data.pagination.hasNextPage||!1,onPageChange:tR,className:"px-4"},B.id+"-pagination-"+d)]})})})};var vi=Lv();const BR=Zf(vi);function cv(t,[a,i]){return Math.min(i,Math.max(a,t))}function at(t,a,{checkForDefaultPrevented:i=!0}={}){return function(u){if(t==null||t(u),i===!1||!u.defaultPrevented)return a==null?void 0:a(u)}}function yd(t,a=[]){let i=[];function o(c,d){const g=C.createContext(d),m=i.length;i=[...i,d];const h=b=>{var _;const{scope:S,children:x,...R}=b,w=((_=S==null?void 0:S[t])==null?void 0:_[m])||g,A=C.useMemo(()=>R,Object.values(R));return E.jsx(w.Provider,{value:A,children:x})};h.displayName=c+"Provider";function v(b,S){var w;const x=((w=S==null?void 0:S[t])==null?void 0:w[m])||g,R=C.useContext(x);if(R)return R;if(d!==void 0)return d;throw new Error(`\`${b}\` must be used within \`${c}\``)}return[h,v]}const u=()=>{const c=i.map(d=>C.createContext(d));return function(g){const m=(g==null?void 0:g[t])||c;return C.useMemo(()=>({[`__scope${t}`]:{...g,[t]:m}}),[g,m])}};return u.scopeName=t,[o,GR(u,...a)]}function GR(...t){const a=t[0];if(t.length===1)return a;const i=()=>{const o=t.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(c){const d=o.reduce((g,{useScope:m,scopeName:h})=>{const b=m(c)[`__scope${h}`];return{...g,...b}},{});return C.useMemo(()=>({[`__scope${a.scopeName}`]:d}),[d])}};return i.scopeName=a.scopeName,i}function fv(t,a){if(typeof t=="function")return t(a);t!=null&&(t.current=a)}function N0(...t){return a=>{let i=!1;const o=t.map(u=>{const c=fv(u,a);return!i&&typeof c=="function"&&(i=!0),c});if(i)return()=>{for(let u=0;u<o.length;u++){const c=o[u];typeof c=="function"?c():fv(t[u],null)}}}}function yt(...t){return C.useCallback(N0(...t),t)}function as(t){const a=PR(t),i=C.forwardRef((o,u)=>{const{children:c,...d}=o,g=C.Children.toArray(c),m=g.find($R);if(m){const h=m.props.children,v=g.map(b=>b===m?C.Children.count(h)>1?C.Children.only(null):C.isValidElement(h)?h.props.children:null:b);return E.jsx(a,{...d,ref:u,children:C.isValidElement(h)?C.cloneElement(h,void 0,v):null})}return E.jsx(a,{...d,ref:u,children:c})});return i.displayName=`${t}.Slot`,i}function PR(t){const a=C.forwardRef((i,o)=>{const{children:u,...c}=i;if(C.isValidElement(u)){const d=YR(u),g=qR(c,u.props);return u.type!==C.Fragment&&(g.ref=o?N0(o,d):d),C.cloneElement(u,g)}return C.Children.count(u)>1?C.Children.only(null):null});return a.displayName=`${t}.SlotClone`,a}var kR=Symbol("radix.slottable");function $R(t){return C.isValidElement(t)&&typeof t.type=="function"&&"__radixId"in t.type&&t.type.__radixId===kR}function qR(t,a){const i={...a};for(const o in a){const u=t[o],c=a[o];/^on[A-Z]/.test(o)?u&&c?i[o]=(...g)=>{c(...g),u(...g)}:u&&(i[o]=u):o==="style"?i[o]={...u,...c}:o==="className"&&(i[o]=[u,c].filter(Boolean).join(" "))}return{...t,...i}}function YR(t){var o,u;let a=(o=Object.getOwnPropertyDescriptor(t.props,"ref"))==null?void 0:o.get,i=a&&"isReactWarning"in a&&a.isReactWarning;return i?t.ref:(a=(u=Object.getOwnPropertyDescriptor(t,"ref"))==null?void 0:u.get,i=a&&"isReactWarning"in a&&a.isReactWarning,i?t.props.ref:t.props.ref||t.ref)}function FR(t){const a=t+"CollectionProvider",[i,o]=yd(a),[u,c]=i(a,{collectionRef:{current:null},itemMap:new Map}),d=w=>{const{scope:A,children:_}=w,D=We.useRef(null),z=We.useRef(new Map).current;return E.jsx(u,{scope:A,itemMap:z,collectionRef:D,children:_})};d.displayName=a;const g=t+"CollectionSlot",m=as(g),h=We.forwardRef((w,A)=>{const{scope:_,children:D}=w,z=c(g,_),k=yt(A,z.collectionRef);return E.jsx(m,{ref:k,children:D})});h.displayName=g;const v=t+"CollectionItemSlot",b="data-radix-collection-item",S=as(v),x=We.forwardRef((w,A)=>{const{scope:_,children:D,...z}=w,k=We.useRef(null),P=yt(A,k),Z=c(v,_);return We.useEffect(()=>(Z.itemMap.set(k,{ref:k,...z}),()=>void Z.itemMap.delete(k))),E.jsx(S,{[b]:"",ref:P,children:D})});x.displayName=v;function R(w){const A=c(t+"CollectionConsumer",w);return We.useCallback(()=>{const D=A.collectionRef.current;if(!D)return[];const z=Array.from(D.querySelectorAll(`[${b}]`));return Array.from(A.itemMap.values()).sort((Z,I)=>z.indexOf(Z.ref.current)-z.indexOf(I.ref.current))},[A.collectionRef,A.itemMap])}return[{Provider:d,Slot:h,ItemSlot:x},R,o]}var XR=C.createContext(void 0);function IR(t){const a=C.useContext(XR);return t||a||"ltr"}var KR=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],tt=KR.reduce((t,a)=>{const i=as(`Primitive.${a}`),o=C.forwardRef((u,c)=>{const{asChild:d,...g}=u,m=d?i:a;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),E.jsx(m,{...g,ref:c})});return o.displayName=`Primitive.${a}`,{...t,[a]:o}},{});function ZR(t,a){t&&vi.flushSync(()=>t.dispatchEvent(a))}function Il(t){const a=C.useRef(t);return C.useEffect(()=>{a.current=t}),C.useMemo(()=>(...i)=>{var o;return(o=a.current)==null?void 0:o.call(a,...i)},[])}function QR(t,a=globalThis==null?void 0:globalThis.document){const i=Il(t);C.useEffect(()=>{const o=u=>{u.key==="Escape"&&i(u)};return a.addEventListener("keydown",o,{capture:!0}),()=>a.removeEventListener("keydown",o,{capture:!0})},[i,a])}var WR="DismissableLayer",Gf="dismissableLayer.update",JR="dismissableLayer.pointerDownOutside",e2="dismissableLayer.focusOutside",dv,z0=C.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),H0=C.forwardRef((t,a)=>{const{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:o,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:d,onDismiss:g,...m}=t,h=C.useContext(z0),[v,b]=C.useState(null),S=(v==null?void 0:v.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,x]=C.useState({}),R=yt(a,I=>b(I)),w=Array.from(h.layers),[A]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),_=w.indexOf(A),D=v?w.indexOf(v):-1,z=h.layersWithOutsidePointerEventsDisabled.size>0,k=D>=_,P=l2(I=>{const q=I.target,ae=[...h.branches].some(ce=>ce.contains(q));!k||ae||(u==null||u(I),d==null||d(I),I.defaultPrevented||g==null||g())},S),Z=a2(I=>{const q=I.target;[...h.branches].some(ce=>ce.contains(q))||(c==null||c(I),d==null||d(I),I.defaultPrevented||g==null||g())},S);return QR(I=>{D===h.layers.size-1&&(o==null||o(I),!I.defaultPrevented&&g&&(I.preventDefault(),g()))},S),C.useEffect(()=>{if(v)return i&&(h.layersWithOutsidePointerEventsDisabled.size===0&&(dv=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(v)),h.layers.add(v),gv(),()=>{i&&h.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=dv)}},[v,S,i,h]),C.useEffect(()=>()=>{v&&(h.layers.delete(v),h.layersWithOutsidePointerEventsDisabled.delete(v),gv())},[v,h]),C.useEffect(()=>{const I=()=>x({});return document.addEventListener(Gf,I),()=>document.removeEventListener(Gf,I)},[]),E.jsx(tt.div,{...m,ref:R,style:{pointerEvents:z?k?"auto":"none":void 0,...t.style},onFocusCapture:at(t.onFocusCapture,Z.onFocusCapture),onBlurCapture:at(t.onBlurCapture,Z.onBlurCapture),onPointerDownCapture:at(t.onPointerDownCapture,P.onPointerDownCapture)})});H0.displayName=WR;var t2="DismissableLayerBranch",n2=C.forwardRef((t,a)=>{const i=C.useContext(z0),o=C.useRef(null),u=yt(a,o);return C.useEffect(()=>{const c=o.current;if(c)return i.branches.add(c),()=>{i.branches.delete(c)}},[i.branches]),E.jsx(tt.div,{...t,ref:u})});n2.displayName=t2;function l2(t,a=globalThis==null?void 0:globalThis.document){const i=Il(t),o=C.useRef(!1),u=C.useRef(()=>{});return C.useEffect(()=>{const c=g=>{if(g.target&&!o.current){let m=function(){L0(JR,i,h,{discrete:!0})};const h={originalEvent:g};g.pointerType==="touch"?(a.removeEventListener("click",u.current),u.current=m,a.addEventListener("click",u.current,{once:!0})):m()}else a.removeEventListener("click",u.current);o.current=!1},d=window.setTimeout(()=>{a.addEventListener("pointerdown",c)},0);return()=>{window.clearTimeout(d),a.removeEventListener("pointerdown",c),a.removeEventListener("click",u.current)}},[a,i]),{onPointerDownCapture:()=>o.current=!0}}function a2(t,a=globalThis==null?void 0:globalThis.document){const i=Il(t),o=C.useRef(!1);return C.useEffect(()=>{const u=c=>{c.target&&!o.current&&L0(e2,i,{originalEvent:c},{discrete:!1})};return a.addEventListener("focusin",u),()=>a.removeEventListener("focusin",u)},[a,i]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function gv(){const t=new CustomEvent(Gf);document.dispatchEvent(t)}function L0(t,a,i,{discrete:o}){const u=i.originalEvent.target,c=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:i});a&&u.addEventListener(t,a,{once:!0}),o?ZR(u,c):u.dispatchEvent(c)}var sf=0;function r2(){C.useEffect(()=>{const t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",t[0]??mv()),document.body.insertAdjacentElement("beforeend",t[1]??mv()),sf++,()=>{sf===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),sf--}},[])}function mv(){const t=document.createElement("span");return t.setAttribute("data-radix-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}var uf="focusScope.autoFocusOnMount",cf="focusScope.autoFocusOnUnmount",hv={bubbles:!1,cancelable:!0},i2="FocusScope",U0=C.forwardRef((t,a)=>{const{loop:i=!1,trapped:o=!1,onMountAutoFocus:u,onUnmountAutoFocus:c,...d}=t,[g,m]=C.useState(null),h=Il(u),v=Il(c),b=C.useRef(null),S=yt(a,w=>m(w)),x=C.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;C.useEffect(()=>{if(o){let w=function(z){if(x.paused||!g)return;const k=z.target;g.contains(k)?b.current=k:dl(b.current,{select:!0})},A=function(z){if(x.paused||!g)return;const k=z.relatedTarget;k!==null&&(g.contains(k)||dl(b.current,{select:!0}))},_=function(z){if(document.activeElement===document.body)for(const P of z)P.removedNodes.length>0&&dl(g)};document.addEventListener("focusin",w),document.addEventListener("focusout",A);const D=new MutationObserver(_);return g&&D.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",w),document.removeEventListener("focusout",A),D.disconnect()}}},[o,g,x.paused]),C.useEffect(()=>{if(g){vv.add(x);const w=document.activeElement;if(!g.contains(w)){const _=new CustomEvent(uf,hv);g.addEventListener(uf,h),g.dispatchEvent(_),_.defaultPrevented||(o2(d2(j0(g)),{select:!0}),document.activeElement===w&&dl(g))}return()=>{g.removeEventListener(uf,h),setTimeout(()=>{const _=new CustomEvent(cf,hv);g.addEventListener(cf,v),g.dispatchEvent(_),_.defaultPrevented||dl(w??document.body,{select:!0}),g.removeEventListener(cf,v),vv.remove(x)},0)}}},[g,h,v,x]);const R=C.useCallback(w=>{if(!i&&!o||x.paused)return;const A=w.key==="Tab"&&!w.altKey&&!w.ctrlKey&&!w.metaKey,_=document.activeElement;if(A&&_){const D=w.currentTarget,[z,k]=s2(D);z&&k?!w.shiftKey&&_===k?(w.preventDefault(),i&&dl(z,{select:!0})):w.shiftKey&&_===z&&(w.preventDefault(),i&&dl(k,{select:!0})):_===D&&w.preventDefault()}},[i,o,x.paused]);return E.jsx(tt.div,{tabIndex:-1,...d,ref:S,onKeyDown:R})});U0.displayName=i2;function o2(t,{select:a=!1}={}){const i=document.activeElement;for(const o of t)if(dl(o,{select:a}),document.activeElement!==i)return}function s2(t){const a=j0(t),i=pv(a,t),o=pv(a.reverse(),t);return[i,o]}function j0(t){const a=[],i=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const u=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||u?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;i.nextNode();)a.push(i.currentNode);return a}function pv(t,a){for(const i of t)if(!u2(i,{upTo:a}))return i}function u2(t,{upTo:a}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(a!==void 0&&t===a)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function c2(t){return t instanceof HTMLInputElement&&"select"in t}function dl(t,{select:a=!1}={}){if(t&&t.focus){const i=document.activeElement;t.focus({preventScroll:!0}),t!==i&&c2(t)&&a&&t.select()}}var vv=f2();function f2(){let t=[];return{add(a){const i=t[0];a!==i&&(i==null||i.pause()),t=yv(t,a),t.unshift(a)},remove(a){var i;t=yv(t,a),(i=t[0])==null||i.resume()}}}function yv(t,a){const i=[...t],o=i.indexOf(a);return o!==-1&&i.splice(o,1),i}function d2(t){return t.filter(a=>a.tagName!=="A")}var zt=globalThis!=null&&globalThis.document?C.useLayoutEffect:()=>{},g2=Hv[" useId ".trim().toString()]||(()=>{}),m2=0;function Sd(t){const[a,i]=C.useState(g2());return zt(()=>{i(o=>o??String(m2++))},[t]),t||(a?`radix-${a}`:"")}const h2=["top","right","bottom","left"],yl=Math.min,Pt=Math.max,rs=Math.round,Yo=Math.floor,yn=t=>({x:t,y:t}),p2={left:"right",right:"left",bottom:"top",top:"bottom"},v2={start:"end",end:"start"};function Pf(t,a,i){return Pt(t,yl(a,i))}function Gn(t,a){return typeof t=="function"?t(a):t}function Pn(t){return t.split("-")[0]}function tr(t){return t.split("-")[1]}function bd(t){return t==="x"?"y":"x"}function xd(t){return t==="y"?"height":"width"}function Sl(t){return["top","bottom"].includes(Pn(t))?"y":"x"}function wd(t){return bd(Sl(t))}function y2(t,a,i){i===void 0&&(i=!1);const o=tr(t),u=wd(t),c=xd(u);let d=u==="x"?o===(i?"end":"start")?"right":"left":o==="start"?"bottom":"top";return a.reference[c]>a.floating[c]&&(d=is(d)),[d,is(d)]}function S2(t){const a=is(t);return[kf(t),a,kf(a)]}function kf(t){return t.replace(/start|end/g,a=>v2[a])}function b2(t,a,i){const o=["left","right"],u=["right","left"],c=["top","bottom"],d=["bottom","top"];switch(t){case"top":case"bottom":return i?a?u:o:a?o:u;case"left":case"right":return a?c:d;default:return[]}}function x2(t,a,i,o){const u=tr(t);let c=b2(Pn(t),i==="start",o);return u&&(c=c.map(d=>d+"-"+u),a&&(c=c.concat(c.map(kf)))),c}function is(t){return t.replace(/left|right|bottom|top/g,a=>p2[a])}function w2(t){return{top:0,right:0,bottom:0,left:0,...t}}function V0(t){return typeof t!="number"?w2(t):{top:t,right:t,bottom:t,left:t}}function os(t){const{x:a,y:i,width:o,height:u}=t;return{width:o,height:u,top:i,left:a,right:a+o,bottom:i+u,x:a,y:i}}function Sv(t,a,i){let{reference:o,floating:u}=t;const c=Sl(a),d=wd(a),g=xd(d),m=Pn(a),h=c==="y",v=o.x+o.width/2-u.width/2,b=o.y+o.height/2-u.height/2,S=o[g]/2-u[g]/2;let x;switch(m){case"top":x={x:v,y:o.y-u.height};break;case"bottom":x={x:v,y:o.y+o.height};break;case"right":x={x:o.x+o.width,y:b};break;case"left":x={x:o.x-u.width,y:b};break;default:x={x:o.x,y:o.y}}switch(tr(a)){case"start":x[d]-=S*(i&&h?-1:1);break;case"end":x[d]+=S*(i&&h?-1:1);break}return x}const C2=async(t,a,i)=>{const{placement:o="bottom",strategy:u="absolute",middleware:c=[],platform:d}=i,g=c.filter(Boolean),m=await(d.isRTL==null?void 0:d.isRTL(a));let h=await d.getElementRects({reference:t,floating:a,strategy:u}),{x:v,y:b}=Sv(h,o,m),S=o,x={},R=0;for(let w=0;w<g.length;w++){const{name:A,fn:_}=g[w],{x:D,y:z,data:k,reset:P}=await _({x:v,y:b,initialPlacement:o,placement:S,strategy:u,middlewareData:x,rects:h,platform:d,elements:{reference:t,floating:a}});v=D??v,b=z??b,x={...x,[A]:{...x[A],...k}},P&&R<=50&&(R++,typeof P=="object"&&(P.placement&&(S=P.placement),P.rects&&(h=P.rects===!0?await d.getElementRects({reference:t,floating:a,strategy:u}):P.rects),{x:v,y:b}=Sv(h,S,m)),w=-1)}return{x:v,y:b,placement:S,strategy:u,middlewareData:x}};async function gi(t,a){var i;a===void 0&&(a={});const{x:o,y:u,platform:c,rects:d,elements:g,strategy:m}=t,{boundary:h="clippingAncestors",rootBoundary:v="viewport",elementContext:b="floating",altBoundary:S=!1,padding:x=0}=Gn(a,t),R=V0(x),A=g[S?b==="floating"?"reference":"floating":b],_=os(await c.getClippingRect({element:(i=await(c.isElement==null?void 0:c.isElement(A)))==null||i?A:A.contextElement||await(c.getDocumentElement==null?void 0:c.getDocumentElement(g.floating)),boundary:h,rootBoundary:v,strategy:m})),D=b==="floating"?{x:o,y:u,width:d.floating.width,height:d.floating.height}:d.reference,z=await(c.getOffsetParent==null?void 0:c.getOffsetParent(g.floating)),k=await(c.isElement==null?void 0:c.isElement(z))?await(c.getScale==null?void 0:c.getScale(z))||{x:1,y:1}:{x:1,y:1},P=os(c.convertOffsetParentRelativeRectToViewportRelativeRect?await c.convertOffsetParentRelativeRectToViewportRelativeRect({elements:g,rect:D,offsetParent:z,strategy:m}):D);return{top:(_.top-P.top+R.top)/k.y,bottom:(P.bottom-_.bottom+R.bottom)/k.y,left:(_.left-P.left+R.left)/k.x,right:(P.right-_.right+R.right)/k.x}}const R2=t=>({name:"arrow",options:t,async fn(a){const{x:i,y:o,placement:u,rects:c,platform:d,elements:g,middlewareData:m}=a,{element:h,padding:v=0}=Gn(t,a)||{};if(h==null)return{};const b=V0(v),S={x:i,y:o},x=wd(u),R=xd(x),w=await d.getDimensions(h),A=x==="y",_=A?"top":"left",D=A?"bottom":"right",z=A?"clientHeight":"clientWidth",k=c.reference[R]+c.reference[x]-S[x]-c.floating[R],P=S[x]-c.reference[x],Z=await(d.getOffsetParent==null?void 0:d.getOffsetParent(h));let I=Z?Z[z]:0;(!I||!await(d.isElement==null?void 0:d.isElement(Z)))&&(I=g.floating[z]||c.floating[R]);const q=k/2-P/2,ae=I/2-w[R]/2-1,ce=yl(b[_],ae),me=yl(b[D],ae),oe=ce,se=I-w[R]-me,fe=I/2-w[R]/2+q,de=Pf(oe,fe,se),H=!m.arrow&&tr(u)!=null&&fe!==de&&c.reference[R]/2-(fe<oe?ce:me)-w[R]/2<0,B=H?fe<oe?fe-oe:fe-se:0;return{[x]:S[x]+B,data:{[x]:de,centerOffset:fe-de-B,...H&&{alignmentOffset:B}},reset:H}}}),E2=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(a){var i,o;const{placement:u,middlewareData:c,rects:d,initialPlacement:g,platform:m,elements:h}=a,{mainAxis:v=!0,crossAxis:b=!0,fallbackPlacements:S,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:w=!0,...A}=Gn(t,a);if((i=c.arrow)!=null&&i.alignmentOffset)return{};const _=Pn(u),D=Sl(g),z=Pn(g)===g,k=await(m.isRTL==null?void 0:m.isRTL(h.floating)),P=S||(z||!w?[is(g)]:S2(g)),Z=R!=="none";!S&&Z&&P.push(...x2(g,w,R,k));const I=[g,...P],q=await gi(a,A),ae=[];let ce=((o=c.flip)==null?void 0:o.overflows)||[];if(v&&ae.push(q[_]),b){const fe=y2(u,d,k);ae.push(q[fe[0]],q[fe[1]])}if(ce=[...ce,{placement:u,overflows:ae}],!ae.every(fe=>fe<=0)){var me,oe;const fe=(((me=c.flip)==null?void 0:me.index)||0)+1,de=I[fe];if(de)return{data:{index:fe,overflows:ce},reset:{placement:de}};let H=(oe=ce.filter(B=>B.overflows[0]<=0).sort((B,j)=>B.overflows[1]-j.overflows[1])[0])==null?void 0:oe.placement;if(!H)switch(x){case"bestFit":{var se;const B=(se=ce.filter(j=>{if(Z){const ee=Sl(j.placement);return ee===D||ee==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(ee=>ee>0).reduce((ee,M)=>ee+M,0)]).sort((j,ee)=>j[1]-ee[1])[0])==null?void 0:se[0];B&&(H=B);break}case"initialPlacement":H=g;break}if(u!==H)return{reset:{placement:H}}}return{}}}};function bv(t,a){return{top:t.top-a.height,right:t.right-a.width,bottom:t.bottom-a.height,left:t.left-a.width}}function xv(t){return h2.some(a=>t[a]>=0)}const _2=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(a){const{rects:i}=a,{strategy:o="referenceHidden",...u}=Gn(t,a);switch(o){case"referenceHidden":{const c=await gi(a,{...u,elementContext:"reference"}),d=bv(c,i.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:xv(d)}}}case"escaped":{const c=await gi(a,{...u,altBoundary:!0}),d=bv(c,i.floating);return{data:{escapedOffsets:d,escaped:xv(d)}}}default:return{}}}}};async function T2(t,a){const{placement:i,platform:o,elements:u}=t,c=await(o.isRTL==null?void 0:o.isRTL(u.floating)),d=Pn(i),g=tr(i),m=Sl(i)==="y",h=["left","top"].includes(d)?-1:1,v=c&&m?-1:1,b=Gn(a,t);let{mainAxis:S,crossAxis:x,alignmentAxis:R}=typeof b=="number"?{mainAxis:b,crossAxis:0,alignmentAxis:null}:{mainAxis:b.mainAxis||0,crossAxis:b.crossAxis||0,alignmentAxis:b.alignmentAxis};return g&&typeof R=="number"&&(x=g==="end"?R*-1:R),m?{x:x*v,y:S*h}:{x:S*h,y:x*v}}const A2=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(a){var i,o;const{x:u,y:c,placement:d,middlewareData:g}=a,m=await T2(a,t);return d===((i=g.offset)==null?void 0:i.placement)&&(o=g.arrow)!=null&&o.alignmentOffset?{}:{x:u+m.x,y:c+m.y,data:{...m,placement:d}}}}},M2=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(a){const{x:i,y:o,placement:u}=a,{mainAxis:c=!0,crossAxis:d=!1,limiter:g={fn:A=>{let{x:_,y:D}=A;return{x:_,y:D}}},...m}=Gn(t,a),h={x:i,y:o},v=await gi(a,m),b=Sl(Pn(u)),S=bd(b);let x=h[S],R=h[b];if(c){const A=S==="y"?"top":"left",_=S==="y"?"bottom":"right",D=x+v[A],z=x-v[_];x=Pf(D,x,z)}if(d){const A=b==="y"?"top":"left",_=b==="y"?"bottom":"right",D=R+v[A],z=R-v[_];R=Pf(D,R,z)}const w=g.fn({...a,[S]:x,[b]:R});return{...w,data:{x:w.x-i,y:w.y-o,enabled:{[S]:c,[b]:d}}}}}},O2=function(t){return t===void 0&&(t={}),{options:t,fn(a){const{x:i,y:o,placement:u,rects:c,middlewareData:d}=a,{offset:g=0,mainAxis:m=!0,crossAxis:h=!0}=Gn(t,a),v={x:i,y:o},b=Sl(u),S=bd(b);let x=v[S],R=v[b];const w=Gn(g,a),A=typeof w=="number"?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(m){const z=S==="y"?"height":"width",k=c.reference[S]-c.floating[z]+A.mainAxis,P=c.reference[S]+c.reference[z]-A.mainAxis;x<k?x=k:x>P&&(x=P)}if(h){var _,D;const z=S==="y"?"width":"height",k=["top","left"].includes(Pn(u)),P=c.reference[b]-c.floating[z]+(k&&((_=d.offset)==null?void 0:_[b])||0)+(k?0:A.crossAxis),Z=c.reference[b]+c.reference[z]+(k?0:((D=d.offset)==null?void 0:D[b])||0)-(k?A.crossAxis:0);R<P?R=P:R>Z&&(R=Z)}return{[S]:x,[b]:R}}}},D2=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(a){var i,o;const{placement:u,rects:c,platform:d,elements:g}=a,{apply:m=()=>{},...h}=Gn(t,a),v=await gi(a,h),b=Pn(u),S=tr(u),x=Sl(u)==="y",{width:R,height:w}=c.floating;let A,_;b==="top"||b==="bottom"?(A=b,_=S===(await(d.isRTL==null?void 0:d.isRTL(g.floating))?"start":"end")?"left":"right"):(_=b,A=S==="end"?"top":"bottom");const D=w-v.top-v.bottom,z=R-v.left-v.right,k=yl(w-v[A],D),P=yl(R-v[_],z),Z=!a.middlewareData.shift;let I=k,q=P;if((i=a.middlewareData.shift)!=null&&i.enabled.x&&(q=z),(o=a.middlewareData.shift)!=null&&o.enabled.y&&(I=D),Z&&!S){const ce=Pt(v.left,0),me=Pt(v.right,0),oe=Pt(v.top,0),se=Pt(v.bottom,0);x?q=R-2*(ce!==0||me!==0?ce+me:Pt(v.left,v.right)):I=w-2*(oe!==0||se!==0?oe+se:Pt(v.top,v.bottom))}await m({...a,availableWidth:q,availableHeight:I});const ae=await d.getDimensions(g.floating);return R!==ae.width||w!==ae.height?{reset:{rects:!0}}:{}}}};function ys(){return typeof window<"u"}function nr(t){return B0(t)?(t.nodeName||"").toLowerCase():"#document"}function kt(t){var a;return(t==null||(a=t.ownerDocument)==null?void 0:a.defaultView)||window}function bn(t){var a;return(a=(B0(t)?t.ownerDocument:t.document)||window.document)==null?void 0:a.documentElement}function B0(t){return ys()?t instanceof Node||t instanceof kt(t).Node:!1}function sn(t){return ys()?t instanceof Element||t instanceof kt(t).Element:!1}function Sn(t){return ys()?t instanceof HTMLElement||t instanceof kt(t).HTMLElement:!1}function wv(t){return!ys()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof kt(t).ShadowRoot}function yi(t){const{overflow:a,overflowX:i,overflowY:o,display:u}=un(t);return/auto|scroll|overlay|hidden|clip/.test(a+o+i)&&!["inline","contents"].includes(u)}function N2(t){return["table","td","th"].includes(nr(t))}function Ss(t){return[":popover-open",":modal"].some(a=>{try{return t.matches(a)}catch{return!1}})}function Cd(t){const a=Rd(),i=sn(t)?un(t):t;return["transform","translate","scale","rotate","perspective"].some(o=>i[o]?i[o]!=="none":!1)||(i.containerType?i.containerType!=="normal":!1)||!a&&(i.backdropFilter?i.backdropFilter!=="none":!1)||!a&&(i.filter?i.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(i.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(i.contain||"").includes(o))}function z2(t){let a=bl(t);for(;Sn(a)&&!Za(a);){if(Cd(a))return a;if(Ss(a))return null;a=bl(a)}return null}function Rd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Za(t){return["html","body","#document"].includes(nr(t))}function un(t){return kt(t).getComputedStyle(t)}function bs(t){return sn(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function bl(t){if(nr(t)==="html")return t;const a=t.assignedSlot||t.parentNode||wv(t)&&t.host||bn(t);return wv(a)?a.host:a}function G0(t){const a=bl(t);return Za(a)?t.ownerDocument?t.ownerDocument.body:t.body:Sn(a)&&yi(a)?a:G0(a)}function mi(t,a,i){var o;a===void 0&&(a=[]),i===void 0&&(i=!0);const u=G0(t),c=u===((o=t.ownerDocument)==null?void 0:o.body),d=kt(u);if(c){const g=$f(d);return a.concat(d,d.visualViewport||[],yi(u)?u:[],g&&i?mi(g):[])}return a.concat(u,mi(u,[],i))}function $f(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function P0(t){const a=un(t);let i=parseFloat(a.width)||0,o=parseFloat(a.height)||0;const u=Sn(t),c=u?t.offsetWidth:i,d=u?t.offsetHeight:o,g=rs(i)!==c||rs(o)!==d;return g&&(i=c,o=d),{width:i,height:o,$:g}}function Ed(t){return sn(t)?t:t.contextElement}function Ya(t){const a=Ed(t);if(!Sn(a))return yn(1);const i=a.getBoundingClientRect(),{width:o,height:u,$:c}=P0(a);let d=(c?rs(i.width):i.width)/o,g=(c?rs(i.height):i.height)/u;return(!d||!Number.isFinite(d))&&(d=1),(!g||!Number.isFinite(g))&&(g=1),{x:d,y:g}}const H2=yn(0);function k0(t){const a=kt(t);return!Rd()||!a.visualViewport?H2:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function L2(t,a,i){return a===void 0&&(a=!1),!i||a&&i!==kt(t)?!1:a}function Kl(t,a,i,o){a===void 0&&(a=!1),i===void 0&&(i=!1);const u=t.getBoundingClientRect(),c=Ed(t);let d=yn(1);a&&(o?sn(o)&&(d=Ya(o)):d=Ya(t));const g=L2(c,i,o)?k0(c):yn(0);let m=(u.left+g.x)/d.x,h=(u.top+g.y)/d.y,v=u.width/d.x,b=u.height/d.y;if(c){const S=kt(c),x=o&&sn(o)?kt(o):o;let R=S,w=$f(R);for(;w&&o&&x!==R;){const A=Ya(w),_=w.getBoundingClientRect(),D=un(w),z=_.left+(w.clientLeft+parseFloat(D.paddingLeft))*A.x,k=_.top+(w.clientTop+parseFloat(D.paddingTop))*A.y;m*=A.x,h*=A.y,v*=A.x,b*=A.y,m+=z,h+=k,R=kt(w),w=$f(R)}}return os({width:v,height:b,x:m,y:h})}function _d(t,a){const i=bs(t).scrollLeft;return a?a.left+i:Kl(bn(t)).left+i}function $0(t,a,i){i===void 0&&(i=!1);const o=t.getBoundingClientRect(),u=o.left+a.scrollLeft-(i?0:_d(t,o)),c=o.top+a.scrollTop;return{x:u,y:c}}function U2(t){let{elements:a,rect:i,offsetParent:o,strategy:u}=t;const c=u==="fixed",d=bn(o),g=a?Ss(a.floating):!1;if(o===d||g&&c)return i;let m={scrollLeft:0,scrollTop:0},h=yn(1);const v=yn(0),b=Sn(o);if((b||!b&&!c)&&((nr(o)!=="body"||yi(d))&&(m=bs(o)),Sn(o))){const x=Kl(o);h=Ya(o),v.x=x.x+o.clientLeft,v.y=x.y+o.clientTop}const S=d&&!b&&!c?$0(d,m,!0):yn(0);return{width:i.width*h.x,height:i.height*h.y,x:i.x*h.x-m.scrollLeft*h.x+v.x+S.x,y:i.y*h.y-m.scrollTop*h.y+v.y+S.y}}function j2(t){return Array.from(t.getClientRects())}function V2(t){const a=bn(t),i=bs(t),o=t.ownerDocument.body,u=Pt(a.scrollWidth,a.clientWidth,o.scrollWidth,o.clientWidth),c=Pt(a.scrollHeight,a.clientHeight,o.scrollHeight,o.clientHeight);let d=-i.scrollLeft+_d(t);const g=-i.scrollTop;return un(o).direction==="rtl"&&(d+=Pt(a.clientWidth,o.clientWidth)-u),{width:u,height:c,x:d,y:g}}function B2(t,a){const i=kt(t),o=bn(t),u=i.visualViewport;let c=o.clientWidth,d=o.clientHeight,g=0,m=0;if(u){c=u.width,d=u.height;const h=Rd();(!h||h&&a==="fixed")&&(g=u.offsetLeft,m=u.offsetTop)}return{width:c,height:d,x:g,y:m}}function G2(t,a){const i=Kl(t,!0,a==="fixed"),o=i.top+t.clientTop,u=i.left+t.clientLeft,c=Sn(t)?Ya(t):yn(1),d=t.clientWidth*c.x,g=t.clientHeight*c.y,m=u*c.x,h=o*c.y;return{width:d,height:g,x:m,y:h}}function Cv(t,a,i){let o;if(a==="viewport")o=B2(t,i);else if(a==="document")o=V2(bn(t));else if(sn(a))o=G2(a,i);else{const u=k0(t);o={x:a.x-u.x,y:a.y-u.y,width:a.width,height:a.height}}return os(o)}function q0(t,a){const i=bl(t);return i===a||!sn(i)||Za(i)?!1:un(i).position==="fixed"||q0(i,a)}function P2(t,a){const i=a.get(t);if(i)return i;let o=mi(t,[],!1).filter(g=>sn(g)&&nr(g)!=="body"),u=null;const c=un(t).position==="fixed";let d=c?bl(t):t;for(;sn(d)&&!Za(d);){const g=un(d),m=Cd(d);!m&&g.position==="fixed"&&(u=null),(c?!m&&!u:!m&&g.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||yi(d)&&!m&&q0(t,d))?o=o.filter(v=>v!==d):u=g,d=bl(d)}return a.set(t,o),o}function k2(t){let{element:a,boundary:i,rootBoundary:o,strategy:u}=t;const d=[...i==="clippingAncestors"?Ss(a)?[]:P2(a,this._c):[].concat(i),o],g=d[0],m=d.reduce((h,v)=>{const b=Cv(a,v,u);return h.top=Pt(b.top,h.top),h.right=yl(b.right,h.right),h.bottom=yl(b.bottom,h.bottom),h.left=Pt(b.left,h.left),h},Cv(a,g,u));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function $2(t){const{width:a,height:i}=P0(t);return{width:a,height:i}}function q2(t,a,i){const o=Sn(a),u=bn(a),c=i==="fixed",d=Kl(t,!0,c,a);let g={scrollLeft:0,scrollTop:0};const m=yn(0);if(o||!o&&!c)if((nr(a)!=="body"||yi(u))&&(g=bs(a)),o){const S=Kl(a,!0,c,a);m.x=S.x+a.clientLeft,m.y=S.y+a.clientTop}else u&&(m.x=_d(u));const h=u&&!o&&!c?$0(u,g):yn(0),v=d.left+g.scrollLeft-m.x-h.x,b=d.top+g.scrollTop-m.y-h.y;return{x:v,y:b,width:d.width,height:d.height}}function ff(t){return un(t).position==="static"}function Rv(t,a){if(!Sn(t)||un(t).position==="fixed")return null;if(a)return a(t);let i=t.offsetParent;return bn(t)===i&&(i=i.ownerDocument.body),i}function Y0(t,a){const i=kt(t);if(Ss(t))return i;if(!Sn(t)){let u=bl(t);for(;u&&!Za(u);){if(sn(u)&&!ff(u))return u;u=bl(u)}return i}let o=Rv(t,a);for(;o&&N2(o)&&ff(o);)o=Rv(o,a);return o&&Za(o)&&ff(o)&&!Cd(o)?i:o||z2(t)||i}const Y2=async function(t){const a=this.getOffsetParent||Y0,i=this.getDimensions,o=await i(t.floating);return{reference:q2(t.reference,await a(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function F2(t){return un(t).direction==="rtl"}const X2={convertOffsetParentRelativeRectToViewportRelativeRect:U2,getDocumentElement:bn,getClippingRect:k2,getOffsetParent:Y0,getElementRects:Y2,getClientRects:j2,getDimensions:$2,getScale:Ya,isElement:sn,isRTL:F2};function F0(t,a){return t.x===a.x&&t.y===a.y&&t.width===a.width&&t.height===a.height}function I2(t,a){let i=null,o;const u=bn(t);function c(){var g;clearTimeout(o),(g=i)==null||g.disconnect(),i=null}function d(g,m){g===void 0&&(g=!1),m===void 0&&(m=1),c();const h=t.getBoundingClientRect(),{left:v,top:b,width:S,height:x}=h;if(g||a(),!S||!x)return;const R=Yo(b),w=Yo(u.clientWidth-(v+S)),A=Yo(u.clientHeight-(b+x)),_=Yo(v),z={rootMargin:-R+"px "+-w+"px "+-A+"px "+-_+"px",threshold:Pt(0,yl(1,m))||1};let k=!0;function P(Z){const I=Z[0].intersectionRatio;if(I!==m){if(!k)return d();I?d(!1,I):o=setTimeout(()=>{d(!1,1e-7)},1e3)}I===1&&!F0(h,t.getBoundingClientRect())&&d(),k=!1}try{i=new IntersectionObserver(P,{...z,root:u.ownerDocument})}catch{i=new IntersectionObserver(P,z)}i.observe(t)}return d(!0),c}function K2(t,a,i,o){o===void 0&&(o={});const{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:g=typeof IntersectionObserver=="function",animationFrame:m=!1}=o,h=Ed(t),v=u||c?[...h?mi(h):[],...mi(a)]:[];v.forEach(_=>{u&&_.addEventListener("scroll",i,{passive:!0}),c&&_.addEventListener("resize",i)});const b=h&&g?I2(h,i):null;let S=-1,x=null;d&&(x=new ResizeObserver(_=>{let[D]=_;D&&D.target===h&&x&&(x.unobserve(a),cancelAnimationFrame(S),S=requestAnimationFrame(()=>{var z;(z=x)==null||z.observe(a)})),i()}),h&&!m&&x.observe(h),x.observe(a));let R,w=m?Kl(t):null;m&&A();function A(){const _=Kl(t);w&&!F0(w,_)&&i(),w=_,R=requestAnimationFrame(A)}return i(),()=>{var _;v.forEach(D=>{u&&D.removeEventListener("scroll",i),c&&D.removeEventListener("resize",i)}),b==null||b(),(_=x)==null||_.disconnect(),x=null,m&&cancelAnimationFrame(R)}}const Z2=A2,Q2=M2,W2=E2,J2=D2,eE=_2,Ev=R2,tE=O2,nE=(t,a,i)=>{const o=new Map,u={platform:X2,...i},c={...u.platform,_c:o};return C2(t,a,{...u,platform:c})};var Qo=typeof document<"u"?C.useLayoutEffect:C.useEffect;function ss(t,a){if(t===a)return!0;if(typeof t!=typeof a)return!1;if(typeof t=="function"&&t.toString()===a.toString())return!0;let i,o,u;if(t&&a&&typeof t=="object"){if(Array.isArray(t)){if(i=t.length,i!==a.length)return!1;for(o=i;o--!==0;)if(!ss(t[o],a[o]))return!1;return!0}if(u=Object.keys(t),i=u.length,i!==Object.keys(a).length)return!1;for(o=i;o--!==0;)if(!{}.hasOwnProperty.call(a,u[o]))return!1;for(o=i;o--!==0;){const c=u[o];if(!(c==="_owner"&&t.$$typeof)&&!ss(t[c],a[c]))return!1}return!0}return t!==t&&a!==a}function X0(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function _v(t,a){const i=X0(t);return Math.round(a*i)/i}function df(t){const a=C.useRef(t);return Qo(()=>{a.current=t}),a}function lE(t){t===void 0&&(t={});const{placement:a="bottom",strategy:i="absolute",middleware:o=[],platform:u,elements:{reference:c,floating:d}={},transform:g=!0,whileElementsMounted:m,open:h}=t,[v,b]=C.useState({x:0,y:0,strategy:i,placement:a,middlewareData:{},isPositioned:!1}),[S,x]=C.useState(o);ss(S,o)||x(o);const[R,w]=C.useState(null),[A,_]=C.useState(null),D=C.useCallback(j=>{j!==Z.current&&(Z.current=j,w(j))},[]),z=C.useCallback(j=>{j!==I.current&&(I.current=j,_(j))},[]),k=c||R,P=d||A,Z=C.useRef(null),I=C.useRef(null),q=C.useRef(v),ae=m!=null,ce=df(m),me=df(u),oe=df(h),se=C.useCallback(()=>{if(!Z.current||!I.current)return;const j={placement:a,strategy:i,middleware:S};me.current&&(j.platform=me.current),nE(Z.current,I.current,j).then(ee=>{const M={...ee,isPositioned:oe.current!==!1};fe.current&&!ss(q.current,M)&&(q.current=M,vi.flushSync(()=>{b(M)}))})},[S,a,i,me,oe]);Qo(()=>{h===!1&&q.current.isPositioned&&(q.current.isPositioned=!1,b(j=>({...j,isPositioned:!1})))},[h]);const fe=C.useRef(!1);Qo(()=>(fe.current=!0,()=>{fe.current=!1}),[]),Qo(()=>{if(k&&(Z.current=k),P&&(I.current=P),k&&P){if(ce.current)return ce.current(k,P,se);se()}},[k,P,se,ce,ae]);const de=C.useMemo(()=>({reference:Z,floating:I,setReference:D,setFloating:z}),[D,z]),H=C.useMemo(()=>({reference:k,floating:P}),[k,P]),B=C.useMemo(()=>{const j={position:i,left:0,top:0};if(!H.floating)return j;const ee=_v(H.floating,v.x),M=_v(H.floating,v.y);return g?{...j,transform:"translate("+ee+"px, "+M+"px)",...X0(H.floating)>=1.5&&{willChange:"transform"}}:{position:i,left:ee,top:M}},[i,g,H.floating,v.x,v.y]);return C.useMemo(()=>({...v,update:se,refs:de,elements:H,floatingStyles:B}),[v,se,de,H,B])}const aE=t=>{function a(i){return{}.hasOwnProperty.call(i,"current")}return{name:"arrow",options:t,fn(i){const{element:o,padding:u}=typeof t=="function"?t(i):t;return o&&a(o)?o.current!=null?Ev({element:o.current,padding:u}).fn(i):{}:o?Ev({element:o,padding:u}).fn(i):{}}}},rE=(t,a)=>({...Z2(t),options:[t,a]}),iE=(t,a)=>({...Q2(t),options:[t,a]}),oE=(t,a)=>({...tE(t),options:[t,a]}),sE=(t,a)=>({...W2(t),options:[t,a]}),uE=(t,a)=>({...J2(t),options:[t,a]}),cE=(t,a)=>({...eE(t),options:[t,a]}),fE=(t,a)=>({...aE(t),options:[t,a]});var dE="Arrow",I0=C.forwardRef((t,a)=>{const{children:i,width:o=10,height:u=5,...c}=t;return E.jsx(tt.svg,{...c,ref:a,width:o,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?i:E.jsx("polygon",{points:"0,0 30,0 15,10"})})});I0.displayName=dE;var gE=I0;function mE(t){const[a,i]=C.useState(void 0);return zt(()=>{if(t){i({width:t.offsetWidth,height:t.offsetHeight});const o=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const c=u[0];let d,g;if("borderBoxSize"in c){const m=c.borderBoxSize,h=Array.isArray(m)?m[0]:m;d=h.inlineSize,g=h.blockSize}else d=t.offsetWidth,g=t.offsetHeight;i({width:d,height:g})});return o.observe(t,{box:"border-box"}),()=>o.unobserve(t)}else i(void 0)},[t]),a}var Td="Popper",[K0,Z0]=yd(Td),[hE,Q0]=K0(Td),W0=t=>{const{__scopePopper:a,children:i}=t,[o,u]=C.useState(null);return E.jsx(hE,{scope:a,anchor:o,onAnchorChange:u,children:i})};W0.displayName=Td;var J0="PopperAnchor",ey=C.forwardRef((t,a)=>{const{__scopePopper:i,virtualRef:o,...u}=t,c=Q0(J0,i),d=C.useRef(null),g=yt(a,d);return C.useEffect(()=>{c.onAnchorChange((o==null?void 0:o.current)||d.current)}),o?null:E.jsx(tt.div,{...u,ref:g})});ey.displayName=J0;var Ad="PopperContent",[pE,vE]=K0(Ad),ty=C.forwardRef((t,a)=>{var W,ue,ze,xe,Re,Te;const{__scopePopper:i,side:o="bottom",sideOffset:u=0,align:c="center",alignOffset:d=0,arrowPadding:g=0,avoidCollisions:m=!0,collisionBoundary:h=[],collisionPadding:v=0,sticky:b="partial",hideWhenDetached:S=!1,updatePositionStrategy:x="optimized",onPlaced:R,...w}=t,A=Q0(Ad,i),[_,D]=C.useState(null),z=yt(a,be=>D(be)),[k,P]=C.useState(null),Z=mE(k),I=(Z==null?void 0:Z.width)??0,q=(Z==null?void 0:Z.height)??0,ae=o+(c!=="center"?"-"+c:""),ce=typeof v=="number"?v:{top:0,right:0,bottom:0,left:0,...v},me=Array.isArray(h)?h:[h],oe=me.length>0,se={padding:ce,boundary:me.filter(SE),altBoundary:oe},{refs:fe,floatingStyles:de,placement:H,isPositioned:B,middlewareData:j}=lE({strategy:"fixed",placement:ae,whileElementsMounted:(...be)=>K2(...be,{animationFrame:x==="always"}),elements:{reference:A.anchor},middleware:[rE({mainAxis:u+q,alignmentAxis:d}),m&&iE({mainAxis:!0,crossAxis:!1,limiter:b==="partial"?oE():void 0,...se}),m&&sE({...se}),uE({...se,apply:({elements:be,rects:we,availableWidth:Xe,availableHeight:Ue})=>{const{width:Ge,height:cn}=we.reference,Fe=be.floating.style;Fe.setProperty("--radix-popper-available-width",`${Xe}px`),Fe.setProperty("--radix-popper-available-height",`${Ue}px`),Fe.setProperty("--radix-popper-anchor-width",`${Ge}px`),Fe.setProperty("--radix-popper-anchor-height",`${cn}px`)}}),k&&fE({element:k,padding:g}),bE({arrowWidth:I,arrowHeight:q}),S&&cE({strategy:"referenceHidden",...se})]}),[ee,M]=ay(H),$=Il(R);zt(()=>{B&&($==null||$())},[B,$]);const Q=(W=j.arrow)==null?void 0:W.x,K=(ue=j.arrow)==null?void 0:ue.y,J=((ze=j.arrow)==null?void 0:ze.centerOffset)!==0,[pe,re]=C.useState();return zt(()=>{_&&re(window.getComputedStyle(_).zIndex)},[_]),E.jsx("div",{ref:fe.setFloating,"data-radix-popper-content-wrapper":"",style:{...de,transform:B?de.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:pe,"--radix-popper-transform-origin":[(xe=j.transformOrigin)==null?void 0:xe.x,(Re=j.transformOrigin)==null?void 0:Re.y].join(" "),...((Te=j.hide)==null?void 0:Te.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:E.jsx(pE,{scope:i,placedSide:ee,onArrowChange:P,arrowX:Q,arrowY:K,shouldHideArrow:J,children:E.jsx(tt.div,{"data-side":ee,"data-align":M,...w,ref:z,style:{...w.style,animation:B?void 0:"none"}})})})});ty.displayName=Ad;var ny="PopperArrow",yE={top:"bottom",right:"left",bottom:"top",left:"right"},ly=C.forwardRef(function(a,i){const{__scopePopper:o,...u}=a,c=vE(ny,o),d=yE[c.placedSide];return E.jsx("span",{ref:c.onArrowChange,style:{position:"absolute",left:c.arrowX,top:c.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[c.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[c.placedSide],visibility:c.shouldHideArrow?"hidden":void 0},children:E.jsx(gE,{...u,ref:i,style:{...u.style,display:"block"}})})});ly.displayName=ny;function SE(t){return t!==null}var bE=t=>({name:"transformOrigin",options:t,fn(a){var A,_,D;const{placement:i,rects:o,middlewareData:u}=a,d=((A=u.arrow)==null?void 0:A.centerOffset)!==0,g=d?0:t.arrowWidth,m=d?0:t.arrowHeight,[h,v]=ay(i),b={start:"0%",center:"50%",end:"100%"}[v],S=(((_=u.arrow)==null?void 0:_.x)??0)+g/2,x=(((D=u.arrow)==null?void 0:D.y)??0)+m/2;let R="",w="";return h==="bottom"?(R=d?b:`${S}px`,w=`${-m}px`):h==="top"?(R=d?b:`${S}px`,w=`${o.floating.height+m}px`):h==="right"?(R=`${-m}px`,w=d?b:`${x}px`):h==="left"&&(R=`${o.floating.width+m}px`,w=d?b:`${x}px`),{data:{x:R,y:w}}}});function ay(t){const[a,i="center"]=t.split("-");return[a,i]}var xE=W0,wE=ey,CE=ty,RE=ly,EE="Portal",ry=C.forwardRef((t,a)=>{var g;const{container:i,...o}=t,[u,c]=C.useState(!1);zt(()=>c(!0),[]);const d=i||u&&((g=globalThis==null?void 0:globalThis.document)==null?void 0:g.body);return d?BR.createPortal(E.jsx(tt.div,{...o,ref:a}),d):null});ry.displayName=EE;var _E=Hv[" useInsertionEffect ".trim().toString()]||zt;function Tv({prop:t,defaultProp:a,onChange:i=()=>{},caller:o}){const[u,c,d]=TE({defaultProp:a,onChange:i}),g=t!==void 0,m=g?t:u;{const v=C.useRef(t!==void 0);C.useEffect(()=>{const b=v.current;b!==g&&console.warn(`${o} is changing from ${b?"controlled":"uncontrolled"} to ${g?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),v.current=g},[g,o])}const h=C.useCallback(v=>{var b;if(g){const S=AE(v)?v(t):v;S!==t&&((b=d.current)==null||b.call(d,S))}else c(v)},[g,t,c,d]);return[m,h]}function TE({defaultProp:t,onChange:a}){const[i,o]=C.useState(t),u=C.useRef(i),c=C.useRef(a);return _E(()=>{c.current=a},[a]),C.useEffect(()=>{var d;u.current!==i&&((d=c.current)==null||d.call(c,i),u.current=i)},[i,u]),[i,o,c]}function AE(t){return typeof t=="function"}function ME(t){const a=C.useRef({value:t,previous:t});return C.useMemo(()=>(a.current.value!==t&&(a.current.previous=a.current.value,a.current.value=t),a.current.previous),[t])}var iy=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),OE="VisuallyHidden",DE=C.forwardRef((t,a)=>E.jsx(tt.span,{...t,ref:a,style:{...iy,...t.style}}));DE.displayName=OE;var NE=function(t){if(typeof document>"u")return null;var a=Array.isArray(t)?t[0]:t;return a.ownerDocument.body},Ga=new WeakMap,Fo=new WeakMap,Xo={},gf=0,oy=function(t){return t&&(t.host||oy(t.parentNode))},zE=function(t,a){return a.map(function(i){if(t.contains(i))return i;var o=oy(i);return o&&t.contains(o)?o:(console.error("aria-hidden",i,"in not contained inside",t,". Doing nothing"),null)}).filter(function(i){return!!i})},HE=function(t,a,i,o){var u=zE(a,Array.isArray(t)?t:[t]);Xo[i]||(Xo[i]=new WeakMap);var c=Xo[i],d=[],g=new Set,m=new Set(u),h=function(b){!b||g.has(b)||(g.add(b),h(b.parentNode))};u.forEach(h);var v=function(b){!b||m.has(b)||Array.prototype.forEach.call(b.children,function(S){if(g.has(S))v(S);else try{var x=S.getAttribute(o),R=x!==null&&x!=="false",w=(Ga.get(S)||0)+1,A=(c.get(S)||0)+1;Ga.set(S,w),c.set(S,A),d.push(S),w===1&&R&&Fo.set(S,!0),A===1&&S.setAttribute(i,"true"),R||S.setAttribute(o,"true")}catch(_){console.error("aria-hidden: cannot operate on ",S,_)}})};return v(a),g.clear(),gf++,function(){d.forEach(function(b){var S=Ga.get(b)-1,x=c.get(b)-1;Ga.set(b,S),c.set(b,x),S||(Fo.has(b)||b.removeAttribute(o),Fo.delete(b)),x||b.removeAttribute(i)}),gf--,gf||(Ga=new WeakMap,Ga=new WeakMap,Fo=new WeakMap,Xo={})}},LE=function(t,a,i){i===void 0&&(i="data-aria-hidden");var o=Array.from(Array.isArray(t)?t:[t]),u=NE(t);return u?(o.push.apply(o,Array.from(u.querySelectorAll("[aria-live]"))),HE(o,u,i,"aria-hidden")):function(){return null}},vn=function(){return vn=Object.assign||function(a){for(var i,o=1,u=arguments.length;o<u;o++){i=arguments[o];for(var c in i)Object.prototype.hasOwnProperty.call(i,c)&&(a[c]=i[c])}return a},vn.apply(this,arguments)};function sy(t,a){var i={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&a.indexOf(o)<0&&(i[o]=t[o]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,o=Object.getOwnPropertySymbols(t);u<o.length;u++)a.indexOf(o[u])<0&&Object.prototype.propertyIsEnumerable.call(t,o[u])&&(i[o[u]]=t[o[u]]);return i}function UE(t,a,i){if(i||arguments.length===2)for(var o=0,u=a.length,c;o<u;o++)(c||!(o in a))&&(c||(c=Array.prototype.slice.call(a,0,o)),c[o]=a[o]);return t.concat(c||Array.prototype.slice.call(a))}var Wo="right-scroll-bar-position",Jo="width-before-scroll-bar",jE="with-scroll-bars-hidden",VE="--removed-body-scroll-bar-size";function mf(t,a){return typeof t=="function"?t(a):t&&(t.current=a),t}function BE(t,a){var i=C.useState(function(){return{value:t,callback:a,facade:{get current(){return i.value},set current(o){var u=i.value;u!==o&&(i.value=o,i.callback(o,u))}}}})[0];return i.callback=a,i.facade}var GE=typeof window<"u"?C.useLayoutEffect:C.useEffect,Av=new WeakMap;function PE(t,a){var i=BE(null,function(o){return t.forEach(function(u){return mf(u,o)})});return GE(function(){var o=Av.get(i);if(o){var u=new Set(o),c=new Set(t),d=i.current;u.forEach(function(g){c.has(g)||mf(g,null)}),c.forEach(function(g){u.has(g)||mf(g,d)})}Av.set(i,t)},[t]),i}function kE(t){return t}function $E(t,a){a===void 0&&(a=kE);var i=[],o=!1,u={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:t},useMedium:function(c){var d=a(c,o);return i.push(d),function(){i=i.filter(function(g){return g!==d})}},assignSyncMedium:function(c){for(o=!0;i.length;){var d=i;i=[],d.forEach(c)}i={push:function(g){return c(g)},filter:function(){return i}}},assignMedium:function(c){o=!0;var d=[];if(i.length){var g=i;i=[],g.forEach(c),d=i}var m=function(){var v=d;d=[],v.forEach(c)},h=function(){return Promise.resolve().then(m)};h(),i={push:function(v){d.push(v),h()},filter:function(v){return d=d.filter(v),i}}}};return u}function qE(t){t===void 0&&(t={});var a=$E(null);return a.options=vn({async:!0,ssr:!1},t),a}var uy=function(t){var a=t.sideCar,i=sy(t,["sideCar"]);if(!a)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=a.read();if(!o)throw new Error("Sidecar medium not found");return C.createElement(o,vn({},i))};uy.isSideCarExport=!0;function YE(t,a){return t.useMedium(a),uy}var cy=qE(),hf=function(){},xs=C.forwardRef(function(t,a){var i=C.useRef(null),o=C.useState({onScrollCapture:hf,onWheelCapture:hf,onTouchMoveCapture:hf}),u=o[0],c=o[1],d=t.forwardProps,g=t.children,m=t.className,h=t.removeScrollBar,v=t.enabled,b=t.shards,S=t.sideCar,x=t.noIsolation,R=t.inert,w=t.allowPinchZoom,A=t.as,_=A===void 0?"div":A,D=t.gapMode,z=sy(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=S,P=PE([i,a]),Z=vn(vn({},z),u);return C.createElement(C.Fragment,null,v&&C.createElement(k,{sideCar:cy,removeScrollBar:h,shards:b,noIsolation:x,inert:R,setCallbacks:c,allowPinchZoom:!!w,lockRef:i,gapMode:D}),d?C.cloneElement(C.Children.only(g),vn(vn({},Z),{ref:P})):C.createElement(_,vn({},Z,{className:m,ref:P}),g))});xs.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};xs.classNames={fullWidth:Jo,zeroRight:Wo};var FE=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function XE(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var a=FE();return a&&t.setAttribute("nonce",a),t}function IE(t,a){t.styleSheet?t.styleSheet.cssText=a:t.appendChild(document.createTextNode(a))}function KE(t){var a=document.head||document.getElementsByTagName("head")[0];a.appendChild(t)}var ZE=function(){var t=0,a=null;return{add:function(i){t==0&&(a=XE())&&(IE(a,i),KE(a)),t++},remove:function(){t--,!t&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},QE=function(){var t=ZE();return function(a,i){C.useEffect(function(){return t.add(a),function(){t.remove()}},[a&&i])}},fy=function(){var t=QE(),a=function(i){var o=i.styles,u=i.dynamic;return t(o,u),null};return a},WE={left:0,top:0,right:0,gap:0},pf=function(t){return parseInt(t||"",10)||0},JE=function(t){var a=window.getComputedStyle(document.body),i=a[t==="padding"?"paddingLeft":"marginLeft"],o=a[t==="padding"?"paddingTop":"marginTop"],u=a[t==="padding"?"paddingRight":"marginRight"];return[pf(i),pf(o),pf(u)]},e_=function(t){if(t===void 0&&(t="margin"),typeof window>"u")return WE;var a=JE(t),i=document.documentElement.clientWidth,o=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,o-i+a[2]-a[0])}},t_=fy(),Fa="data-scroll-locked",n_=function(t,a,i,o){var u=t.left,c=t.top,d=t.right,g=t.gap;return i===void 0&&(i="margin"),`
  .`.concat(jE,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(g,"px ").concat(o,`;
  }
  body[`).concat(Fa,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([a&&"position: relative ".concat(o,";"),i==="margin"&&`
    padding-left: `.concat(u,`px;
    padding-top: `).concat(c,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(g,"px ").concat(o,`;
    `),i==="padding"&&"padding-right: ".concat(g,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Wo,` {
    right: `).concat(g,"px ").concat(o,`;
  }
  
  .`).concat(Jo,` {
    margin-right: `).concat(g,"px ").concat(o,`;
  }
  
  .`).concat(Wo," .").concat(Wo,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(Jo," .").concat(Jo,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(Fa,`] {
    `).concat(VE,": ").concat(g,`px;
  }
`)},Mv=function(){var t=parseInt(document.body.getAttribute(Fa)||"0",10);return isFinite(t)?t:0},l_=function(){C.useEffect(function(){return document.body.setAttribute(Fa,(Mv()+1).toString()),function(){var t=Mv()-1;t<=0?document.body.removeAttribute(Fa):document.body.setAttribute(Fa,t.toString())}},[])},a_=function(t){var a=t.noRelative,i=t.noImportant,o=t.gapMode,u=o===void 0?"margin":o;l_();var c=C.useMemo(function(){return e_(u)},[u]);return C.createElement(t_,{styles:n_(c,!a,u,i?"":"!important")})},qf=!1;if(typeof window<"u")try{var Io=Object.defineProperty({},"passive",{get:function(){return qf=!0,!0}});window.addEventListener("test",Io,Io),window.removeEventListener("test",Io,Io)}catch{qf=!1}var Pa=qf?{passive:!1}:!1,r_=function(t){return t.tagName==="TEXTAREA"},dy=function(t,a){if(!(t instanceof Element))return!1;var i=window.getComputedStyle(t);return i[a]!=="hidden"&&!(i.overflowY===i.overflowX&&!r_(t)&&i[a]==="visible")},i_=function(t){return dy(t,"overflowY")},o_=function(t){return dy(t,"overflowX")},Ov=function(t,a){var i=a.ownerDocument,o=a;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var u=gy(t,o);if(u){var c=my(t,o),d=c[1],g=c[2];if(d>g)return!0}o=o.parentNode}while(o&&o!==i.body);return!1},s_=function(t){var a=t.scrollTop,i=t.scrollHeight,o=t.clientHeight;return[a,i,o]},u_=function(t){var a=t.scrollLeft,i=t.scrollWidth,o=t.clientWidth;return[a,i,o]},gy=function(t,a){return t==="v"?i_(a):o_(a)},my=function(t,a){return t==="v"?s_(a):u_(a)},c_=function(t,a){return t==="h"&&a==="rtl"?-1:1},f_=function(t,a,i,o,u){var c=c_(t,window.getComputedStyle(a).direction),d=c*o,g=i.target,m=a.contains(g),h=!1,v=d>0,b=0,S=0;do{var x=my(t,g),R=x[0],w=x[1],A=x[2],_=w-A-c*R;(R||_)&&gy(t,g)&&(b+=_,S+=R),g instanceof ShadowRoot?g=g.host:g=g.parentNode}while(!m&&g!==document.body||m&&(a.contains(g)||a===g));return(v&&Math.abs(b)<1||!v&&Math.abs(S)<1)&&(h=!0),h},Ko=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},Dv=function(t){return[t.deltaX,t.deltaY]},Nv=function(t){return t&&"current"in t?t.current:t},d_=function(t,a){return t[0]===a[0]&&t[1]===a[1]},g_=function(t){return`
  .block-interactivity-`.concat(t,` {pointer-events: none;}
  .allow-interactivity-`).concat(t,` {pointer-events: all;}
`)},m_=0,ka=[];function h_(t){var a=C.useRef([]),i=C.useRef([0,0]),o=C.useRef(),u=C.useState(m_++)[0],c=C.useState(fy)[0],d=C.useRef(t);C.useEffect(function(){d.current=t},[t]),C.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(u));var w=UE([t.lockRef.current],(t.shards||[]).map(Nv),!0).filter(Boolean);return w.forEach(function(A){return A.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),w.forEach(function(A){return A.classList.remove("allow-interactivity-".concat(u))})}}},[t.inert,t.lockRef.current,t.shards]);var g=C.useCallback(function(w,A){if("touches"in w&&w.touches.length===2||w.type==="wheel"&&w.ctrlKey)return!d.current.allowPinchZoom;var _=Ko(w),D=i.current,z="deltaX"in w?w.deltaX:D[0]-_[0],k="deltaY"in w?w.deltaY:D[1]-_[1],P,Z=w.target,I=Math.abs(z)>Math.abs(k)?"h":"v";if("touches"in w&&I==="h"&&Z.type==="range")return!1;var q=Ov(I,Z);if(!q)return!0;if(q?P=I:(P=I==="v"?"h":"v",q=Ov(I,Z)),!q)return!1;if(!o.current&&"changedTouches"in w&&(z||k)&&(o.current=P),!P)return!0;var ae=o.current||P;return f_(ae,A,w,ae==="h"?z:k)},[]),m=C.useCallback(function(w){var A=w;if(!(!ka.length||ka[ka.length-1]!==c)){var _="deltaY"in A?Dv(A):Ko(A),D=a.current.filter(function(P){return P.name===A.type&&(P.target===A.target||A.target===P.shadowParent)&&d_(P.delta,_)})[0];if(D&&D.should){A.cancelable&&A.preventDefault();return}if(!D){var z=(d.current.shards||[]).map(Nv).filter(Boolean).filter(function(P){return P.contains(A.target)}),k=z.length>0?g(A,z[0]):!d.current.noIsolation;k&&A.cancelable&&A.preventDefault()}}},[]),h=C.useCallback(function(w,A,_,D){var z={name:w,delta:A,target:_,should:D,shadowParent:p_(_)};a.current.push(z),setTimeout(function(){a.current=a.current.filter(function(k){return k!==z})},1)},[]),v=C.useCallback(function(w){i.current=Ko(w),o.current=void 0},[]),b=C.useCallback(function(w){h(w.type,Dv(w),w.target,g(w,t.lockRef.current))},[]),S=C.useCallback(function(w){h(w.type,Ko(w),w.target,g(w,t.lockRef.current))},[]);C.useEffect(function(){return ka.push(c),t.setCallbacks({onScrollCapture:b,onWheelCapture:b,onTouchMoveCapture:S}),document.addEventListener("wheel",m,Pa),document.addEventListener("touchmove",m,Pa),document.addEventListener("touchstart",v,Pa),function(){ka=ka.filter(function(w){return w!==c}),document.removeEventListener("wheel",m,Pa),document.removeEventListener("touchmove",m,Pa),document.removeEventListener("touchstart",v,Pa)}},[]);var x=t.removeScrollBar,R=t.inert;return C.createElement(C.Fragment,null,R?C.createElement(c,{styles:g_(u)}):null,x?C.createElement(a_,{gapMode:t.gapMode}):null)}function p_(t){for(var a=null;t!==null;)t instanceof ShadowRoot&&(a=t.host,t=t.host),t=t.parentNode;return a}const v_=YE(cy,h_);var hy=C.forwardRef(function(t,a){return C.createElement(xs,vn({},t,{ref:a,sideCar:v_}))});hy.classNames=xs.classNames;var y_=[" ","Enter","ArrowUp","ArrowDown"],S_=[" ","Enter"],Zl="Select",[ws,Cs,b_]=FR(Zl),[lr,fT]=yd(Zl,[b_,Z0]),Rs=Z0(),[x_,wl]=lr(Zl),[w_,C_]=lr(Zl),py=t=>{const{__scopeSelect:a,children:i,open:o,defaultOpen:u,onOpenChange:c,value:d,defaultValue:g,onValueChange:m,dir:h,name:v,autoComplete:b,disabled:S,required:x,form:R}=t,w=Rs(a),[A,_]=C.useState(null),[D,z]=C.useState(null),[k,P]=C.useState(!1),Z=IR(h),[I,q]=Tv({prop:o,defaultProp:u??!1,onChange:c,caller:Zl}),[ae,ce]=Tv({prop:d,defaultProp:g,onChange:m,caller:Zl}),me=C.useRef(null),oe=A?R||!!A.closest("form"):!0,[se,fe]=C.useState(new Set),de=Array.from(se).map(H=>H.props.value).join(";");return E.jsx(xE,{...w,children:E.jsxs(x_,{required:x,scope:a,trigger:A,onTriggerChange:_,valueNode:D,onValueNodeChange:z,valueNodeHasChildren:k,onValueNodeHasChildrenChange:P,contentId:Sd(),value:ae,onValueChange:ce,open:I,onOpenChange:q,dir:Z,triggerPointerDownPosRef:me,disabled:S,children:[E.jsx(ws.Provider,{scope:a,children:E.jsx(w_,{scope:t.__scopeSelect,onNativeOptionAdd:C.useCallback(H=>{fe(B=>new Set(B).add(H))},[]),onNativeOptionRemove:C.useCallback(H=>{fe(B=>{const j=new Set(B);return j.delete(H),j})},[]),children:i})}),oe?E.jsxs(Vy,{"aria-hidden":!0,required:x,tabIndex:-1,name:v,autoComplete:b,value:ae,onChange:H=>ce(H.target.value),disabled:S,form:R,children:[ae===void 0?E.jsx("option",{value:""}):null,Array.from(se)]},de):null]})})};py.displayName=Zl;var vy="SelectTrigger",yy=C.forwardRef((t,a)=>{const{__scopeSelect:i,disabled:o=!1,...u}=t,c=Rs(i),d=wl(vy,i),g=d.disabled||o,m=yt(a,d.onTriggerChange),h=Cs(i),v=C.useRef("touch"),[b,S,x]=Gy(w=>{const A=h().filter(z=>!z.disabled),_=A.find(z=>z.value===d.value),D=Py(A,w,_);D!==void 0&&d.onValueChange(D.value)}),R=w=>{g||(d.onOpenChange(!0),x()),w&&(d.triggerPointerDownPosRef.current={x:Math.round(w.pageX),y:Math.round(w.pageY)})};return E.jsx(wE,{asChild:!0,...c,children:E.jsx(tt.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:g,"data-disabled":g?"":void 0,"data-placeholder":By(d.value)?"":void 0,...u,ref:m,onClick:at(u.onClick,w=>{w.currentTarget.focus(),v.current!=="mouse"&&R(w)}),onPointerDown:at(u.onPointerDown,w=>{v.current=w.pointerType;const A=w.target;A.hasPointerCapture(w.pointerId)&&A.releasePointerCapture(w.pointerId),w.button===0&&w.ctrlKey===!1&&w.pointerType==="mouse"&&(R(w),w.preventDefault())}),onKeyDown:at(u.onKeyDown,w=>{const A=b.current!=="";!(w.ctrlKey||w.altKey||w.metaKey)&&w.key.length===1&&S(w.key),!(A&&w.key===" ")&&y_.includes(w.key)&&(R(),w.preventDefault())})})})});yy.displayName=vy;var Sy="SelectValue",by=C.forwardRef((t,a)=>{const{__scopeSelect:i,className:o,style:u,children:c,placeholder:d="",...g}=t,m=wl(Sy,i),{onValueNodeHasChildrenChange:h}=m,v=c!==void 0,b=yt(a,m.onValueNodeChange);return zt(()=>{h(v)},[h,v]),E.jsx(tt.span,{...g,ref:b,style:{pointerEvents:"none"},children:By(m.value)?E.jsx(E.Fragment,{children:d}):c})});by.displayName=Sy;var R_="SelectIcon",xy=C.forwardRef((t,a)=>{const{__scopeSelect:i,children:o,...u}=t;return E.jsx(tt.span,{"aria-hidden":!0,...u,ref:a,children:o||"▼"})});xy.displayName=R_;var E_="SelectPortal",wy=t=>E.jsx(ry,{asChild:!0,...t});wy.displayName=E_;var Ql="SelectContent",Cy=C.forwardRef((t,a)=>{const i=wl(Ql,t.__scopeSelect),[o,u]=C.useState();if(zt(()=>{u(new DocumentFragment)},[]),!i.open){const c=o;return c?vi.createPortal(E.jsx(Ry,{scope:t.__scopeSelect,children:E.jsx(ws.Slot,{scope:t.__scopeSelect,children:E.jsx("div",{children:t.children})})}),c):null}return E.jsx(Ey,{...t,ref:a})});Cy.displayName=Ql;var an=10,[Ry,Cl]=lr(Ql),__="SelectContentImpl",T_=as("SelectContent.RemoveScroll"),Ey=C.forwardRef((t,a)=>{const{__scopeSelect:i,position:o="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:c,onPointerDownOutside:d,side:g,sideOffset:m,align:h,alignOffset:v,arrowPadding:b,collisionBoundary:S,collisionPadding:x,sticky:R,hideWhenDetached:w,avoidCollisions:A,..._}=t,D=wl(Ql,i),[z,k]=C.useState(null),[P,Z]=C.useState(null),I=yt(a,W=>k(W)),[q,ae]=C.useState(null),[ce,me]=C.useState(null),oe=Cs(i),[se,fe]=C.useState(!1),de=C.useRef(!1);C.useEffect(()=>{if(z)return LE(z)},[z]),r2();const H=C.useCallback(W=>{const[ue,...ze]=oe().map(Te=>Te.ref.current),[xe]=ze.slice(-1),Re=document.activeElement;for(const Te of W)if(Te===Re||(Te==null||Te.scrollIntoView({block:"nearest"}),Te===ue&&P&&(P.scrollTop=0),Te===xe&&P&&(P.scrollTop=P.scrollHeight),Te==null||Te.focus(),document.activeElement!==Re))return},[oe,P]),B=C.useCallback(()=>H([q,z]),[H,q,z]);C.useEffect(()=>{se&&B()},[se,B]);const{onOpenChange:j,triggerPointerDownPosRef:ee}=D;C.useEffect(()=>{if(z){let W={x:0,y:0};const ue=xe=>{var Re,Te;W={x:Math.abs(Math.round(xe.pageX)-(((Re=ee.current)==null?void 0:Re.x)??0)),y:Math.abs(Math.round(xe.pageY)-(((Te=ee.current)==null?void 0:Te.y)??0))}},ze=xe=>{W.x<=10&&W.y<=10?xe.preventDefault():z.contains(xe.target)||j(!1),document.removeEventListener("pointermove",ue),ee.current=null};return ee.current!==null&&(document.addEventListener("pointermove",ue),document.addEventListener("pointerup",ze,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ue),document.removeEventListener("pointerup",ze,{capture:!0})}}},[z,j,ee]),C.useEffect(()=>{const W=()=>j(!1);return window.addEventListener("blur",W),window.addEventListener("resize",W),()=>{window.removeEventListener("blur",W),window.removeEventListener("resize",W)}},[j]);const[M,$]=Gy(W=>{const ue=oe().filter(Re=>!Re.disabled),ze=ue.find(Re=>Re.ref.current===document.activeElement),xe=Py(ue,W,ze);xe&&setTimeout(()=>xe.ref.current.focus())}),Q=C.useCallback((W,ue,ze)=>{const xe=!de.current&&!ze;(D.value!==void 0&&D.value===ue||xe)&&(ae(W),xe&&(de.current=!0))},[D.value]),K=C.useCallback(()=>z==null?void 0:z.focus(),[z]),J=C.useCallback((W,ue,ze)=>{const xe=!de.current&&!ze;(D.value!==void 0&&D.value===ue||xe)&&me(W)},[D.value]),pe=o==="popper"?Yf:_y,re=pe===Yf?{side:g,sideOffset:m,align:h,alignOffset:v,arrowPadding:b,collisionBoundary:S,collisionPadding:x,sticky:R,hideWhenDetached:w,avoidCollisions:A}:{};return E.jsx(Ry,{scope:i,content:z,viewport:P,onViewportChange:Z,itemRefCallback:Q,selectedItem:q,onItemLeave:K,itemTextRefCallback:J,focusSelectedItem:B,selectedItemText:ce,position:o,isPositioned:se,searchRef:M,children:E.jsx(hy,{as:T_,allowPinchZoom:!0,children:E.jsx(U0,{asChild:!0,trapped:D.open,onMountAutoFocus:W=>{W.preventDefault()},onUnmountAutoFocus:at(u,W=>{var ue;(ue=D.trigger)==null||ue.focus({preventScroll:!0}),W.preventDefault()}),children:E.jsx(H0,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:W=>W.preventDefault(),onDismiss:()=>D.onOpenChange(!1),children:E.jsx(pe,{role:"listbox",id:D.contentId,"data-state":D.open?"open":"closed",dir:D.dir,onContextMenu:W=>W.preventDefault(),..._,...re,onPlaced:()=>fe(!0),ref:I,style:{display:"flex",flexDirection:"column",outline:"none",..._.style},onKeyDown:at(_.onKeyDown,W=>{const ue=W.ctrlKey||W.altKey||W.metaKey;if(W.key==="Tab"&&W.preventDefault(),!ue&&W.key.length===1&&$(W.key),["ArrowUp","ArrowDown","Home","End"].includes(W.key)){let xe=oe().filter(Re=>!Re.disabled).map(Re=>Re.ref.current);if(["ArrowUp","End"].includes(W.key)&&(xe=xe.slice().reverse()),["ArrowUp","ArrowDown"].includes(W.key)){const Re=W.target,Te=xe.indexOf(Re);xe=xe.slice(Te+1)}setTimeout(()=>H(xe)),W.preventDefault()}})})})})})})});Ey.displayName=__;var A_="SelectItemAlignedPosition",_y=C.forwardRef((t,a)=>{const{__scopeSelect:i,onPlaced:o,...u}=t,c=wl(Ql,i),d=Cl(Ql,i),[g,m]=C.useState(null),[h,v]=C.useState(null),b=yt(a,I=>v(I)),S=Cs(i),x=C.useRef(!1),R=C.useRef(!0),{viewport:w,selectedItem:A,selectedItemText:_,focusSelectedItem:D}=d,z=C.useCallback(()=>{if(c.trigger&&c.valueNode&&g&&h&&w&&A&&_){const I=c.trigger.getBoundingClientRect(),q=h.getBoundingClientRect(),ae=c.valueNode.getBoundingClientRect(),ce=_.getBoundingClientRect();if(c.dir!=="rtl"){const Re=ce.left-q.left,Te=ae.left-Re,be=I.left-Te,we=I.width+be,Xe=Math.max(we,q.width),Ue=window.innerWidth-an,Ge=cv(Te,[an,Math.max(an,Ue-Xe)]);g.style.minWidth=we+"px",g.style.left=Ge+"px"}else{const Re=q.right-ce.right,Te=window.innerWidth-ae.right-Re,be=window.innerWidth-I.right-Te,we=I.width+be,Xe=Math.max(we,q.width),Ue=window.innerWidth-an,Ge=cv(Te,[an,Math.max(an,Ue-Xe)]);g.style.minWidth=we+"px",g.style.right=Ge+"px"}const me=S(),oe=window.innerHeight-an*2,se=w.scrollHeight,fe=window.getComputedStyle(h),de=parseInt(fe.borderTopWidth,10),H=parseInt(fe.paddingTop,10),B=parseInt(fe.borderBottomWidth,10),j=parseInt(fe.paddingBottom,10),ee=de+H+se+j+B,M=Math.min(A.offsetHeight*5,ee),$=window.getComputedStyle(w),Q=parseInt($.paddingTop,10),K=parseInt($.paddingBottom,10),J=I.top+I.height/2-an,pe=oe-J,re=A.offsetHeight/2,W=A.offsetTop+re,ue=de+H+W,ze=ee-ue;if(ue<=J){const Re=me.length>0&&A===me[me.length-1].ref.current;g.style.bottom="0px";const Te=h.clientHeight-w.offsetTop-w.offsetHeight,be=Math.max(pe,re+(Re?K:0)+Te+B),we=ue+be;g.style.height=we+"px"}else{const Re=me.length>0&&A===me[0].ref.current;g.style.top="0px";const be=Math.max(J,de+w.offsetTop+(Re?Q:0)+re)+ze;g.style.height=be+"px",w.scrollTop=ue-J+w.offsetTop}g.style.margin=`${an}px 0`,g.style.minHeight=M+"px",g.style.maxHeight=oe+"px",o==null||o(),requestAnimationFrame(()=>x.current=!0)}},[S,c.trigger,c.valueNode,g,h,w,A,_,c.dir,o]);zt(()=>z(),[z]);const[k,P]=C.useState();zt(()=>{h&&P(window.getComputedStyle(h).zIndex)},[h]);const Z=C.useCallback(I=>{I&&R.current===!0&&(z(),D==null||D(),R.current=!1)},[z,D]);return E.jsx(O_,{scope:i,contentWrapper:g,shouldExpandOnScrollRef:x,onScrollButtonChange:Z,children:E.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:E.jsx(tt.div,{...u,ref:b,style:{boxSizing:"border-box",maxHeight:"100%",...u.style}})})})});_y.displayName=A_;var M_="SelectPopperPosition",Yf=C.forwardRef((t,a)=>{const{__scopeSelect:i,align:o="start",collisionPadding:u=an,...c}=t,d=Rs(i);return E.jsx(CE,{...d,...c,ref:a,align:o,collisionPadding:u,style:{boxSizing:"border-box",...c.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Yf.displayName=M_;var[O_,Md]=lr(Ql,{}),Ff="SelectViewport",Ty=C.forwardRef((t,a)=>{const{__scopeSelect:i,nonce:o,...u}=t,c=Cl(Ff,i),d=Md(Ff,i),g=yt(a,c.onViewportChange),m=C.useRef(0);return E.jsxs(E.Fragment,{children:[E.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),E.jsx(ws.Slot,{scope:i,children:E.jsx(tt.div,{"data-radix-select-viewport":"",role:"presentation",...u,ref:g,style:{position:"relative",flex:1,overflow:"hidden auto",...u.style},onScroll:at(u.onScroll,h=>{const v=h.currentTarget,{contentWrapper:b,shouldExpandOnScrollRef:S}=d;if(S!=null&&S.current&&b){const x=Math.abs(m.current-v.scrollTop);if(x>0){const R=window.innerHeight-an*2,w=parseFloat(b.style.minHeight),A=parseFloat(b.style.height),_=Math.max(w,A);if(_<R){const D=_+x,z=Math.min(R,D),k=D-z;b.style.height=z+"px",b.style.bottom==="0px"&&(v.scrollTop=k>0?k:0,b.style.justifyContent="flex-end")}}}m.current=v.scrollTop})})})]})});Ty.displayName=Ff;var Ay="SelectGroup",[D_,N_]=lr(Ay),z_=C.forwardRef((t,a)=>{const{__scopeSelect:i,...o}=t,u=Sd();return E.jsx(D_,{scope:i,id:u,children:E.jsx(tt.div,{role:"group","aria-labelledby":u,...o,ref:a})})});z_.displayName=Ay;var My="SelectLabel",H_=C.forwardRef((t,a)=>{const{__scopeSelect:i,...o}=t,u=N_(My,i);return E.jsx(tt.div,{id:u.id,...o,ref:a})});H_.displayName=My;var us="SelectItem",[L_,Oy]=lr(us),Dy=C.forwardRef((t,a)=>{const{__scopeSelect:i,value:o,disabled:u=!1,textValue:c,...d}=t,g=wl(us,i),m=Cl(us,i),h=g.value===o,[v,b]=C.useState(c??""),[S,x]=C.useState(!1),R=yt(a,D=>{var z;return(z=m.itemRefCallback)==null?void 0:z.call(m,D,o,u)}),w=Sd(),A=C.useRef("touch"),_=()=>{u||(g.onValueChange(o),g.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return E.jsx(L_,{scope:i,value:o,disabled:u,textId:w,isSelected:h,onItemTextChange:C.useCallback(D=>{b(z=>z||((D==null?void 0:D.textContent)??"").trim())},[]),children:E.jsx(ws.ItemSlot,{scope:i,value:o,disabled:u,textValue:v,children:E.jsx(tt.div,{role:"option","aria-labelledby":w,"data-highlighted":S?"":void 0,"aria-selected":h&&S,"data-state":h?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1,...d,ref:R,onFocus:at(d.onFocus,()=>x(!0)),onBlur:at(d.onBlur,()=>x(!1)),onClick:at(d.onClick,()=>{A.current!=="mouse"&&_()}),onPointerUp:at(d.onPointerUp,()=>{A.current==="mouse"&&_()}),onPointerDown:at(d.onPointerDown,D=>{A.current=D.pointerType}),onPointerMove:at(d.onPointerMove,D=>{var z;A.current=D.pointerType,u?(z=m.onItemLeave)==null||z.call(m):A.current==="mouse"&&D.currentTarget.focus({preventScroll:!0})}),onPointerLeave:at(d.onPointerLeave,D=>{var z;D.currentTarget===document.activeElement&&((z=m.onItemLeave)==null||z.call(m))}),onKeyDown:at(d.onKeyDown,D=>{var k;((k=m.searchRef)==null?void 0:k.current)!==""&&D.key===" "||(S_.includes(D.key)&&_(),D.key===" "&&D.preventDefault())})})})})});Dy.displayName=us;var oi="SelectItemText",Ny=C.forwardRef((t,a)=>{const{__scopeSelect:i,className:o,style:u,...c}=t,d=wl(oi,i),g=Cl(oi,i),m=Oy(oi,i),h=C_(oi,i),[v,b]=C.useState(null),S=yt(a,_=>b(_),m.onItemTextChange,_=>{var D;return(D=g.itemTextRefCallback)==null?void 0:D.call(g,_,m.value,m.disabled)}),x=v==null?void 0:v.textContent,R=C.useMemo(()=>E.jsx("option",{value:m.value,disabled:m.disabled,children:x},m.value),[m.disabled,m.value,x]),{onNativeOptionAdd:w,onNativeOptionRemove:A}=h;return zt(()=>(w(R),()=>A(R)),[w,A,R]),E.jsxs(E.Fragment,{children:[E.jsx(tt.span,{id:m.textId,...c,ref:S}),m.isSelected&&d.valueNode&&!d.valueNodeHasChildren?vi.createPortal(c.children,d.valueNode):null]})});Ny.displayName=oi;var zy="SelectItemIndicator",Hy=C.forwardRef((t,a)=>{const{__scopeSelect:i,...o}=t;return Oy(zy,i).isSelected?E.jsx(tt.span,{"aria-hidden":!0,...o,ref:a}):null});Hy.displayName=zy;var Xf="SelectScrollUpButton",Ly=C.forwardRef((t,a)=>{const i=Cl(Xf,t.__scopeSelect),o=Md(Xf,t.__scopeSelect),[u,c]=C.useState(!1),d=yt(a,o.onScrollButtonChange);return zt(()=>{if(i.viewport&&i.isPositioned){let g=function(){const h=m.scrollTop>0;c(h)};const m=i.viewport;return g(),m.addEventListener("scroll",g),()=>m.removeEventListener("scroll",g)}},[i.viewport,i.isPositioned]),u?E.jsx(jy,{...t,ref:d,onAutoScroll:()=>{const{viewport:g,selectedItem:m}=i;g&&m&&(g.scrollTop=g.scrollTop-m.offsetHeight)}}):null});Ly.displayName=Xf;var If="SelectScrollDownButton",Uy=C.forwardRef((t,a)=>{const i=Cl(If,t.__scopeSelect),o=Md(If,t.__scopeSelect),[u,c]=C.useState(!1),d=yt(a,o.onScrollButtonChange);return zt(()=>{if(i.viewport&&i.isPositioned){let g=function(){const h=m.scrollHeight-m.clientHeight,v=Math.ceil(m.scrollTop)<h;c(v)};const m=i.viewport;return g(),m.addEventListener("scroll",g),()=>m.removeEventListener("scroll",g)}},[i.viewport,i.isPositioned]),u?E.jsx(jy,{...t,ref:d,onAutoScroll:()=>{const{viewport:g,selectedItem:m}=i;g&&m&&(g.scrollTop=g.scrollTop+m.offsetHeight)}}):null});Uy.displayName=If;var jy=C.forwardRef((t,a)=>{const{__scopeSelect:i,onAutoScroll:o,...u}=t,c=Cl("SelectScrollButton",i),d=C.useRef(null),g=Cs(i),m=C.useCallback(()=>{d.current!==null&&(window.clearInterval(d.current),d.current=null)},[]);return C.useEffect(()=>()=>m(),[m]),zt(()=>{var v;const h=g().find(b=>b.ref.current===document.activeElement);(v=h==null?void 0:h.ref.current)==null||v.scrollIntoView({block:"nearest"})},[g]),E.jsx(tt.div,{"aria-hidden":!0,...u,ref:a,style:{flexShrink:0,...u.style},onPointerDown:at(u.onPointerDown,()=>{d.current===null&&(d.current=window.setInterval(o,50))}),onPointerMove:at(u.onPointerMove,()=>{var h;(h=c.onItemLeave)==null||h.call(c),d.current===null&&(d.current=window.setInterval(o,50))}),onPointerLeave:at(u.onPointerLeave,()=>{m()})})}),U_="SelectSeparator",j_=C.forwardRef((t,a)=>{const{__scopeSelect:i,...o}=t;return E.jsx(tt.div,{"aria-hidden":!0,...o,ref:a})});j_.displayName=U_;var Kf="SelectArrow",V_=C.forwardRef((t,a)=>{const{__scopeSelect:i,...o}=t,u=Rs(i),c=wl(Kf,i),d=Cl(Kf,i);return c.open&&d.position==="popper"?E.jsx(RE,{...u,...o,ref:a}):null});V_.displayName=Kf;var B_="SelectBubbleInput",Vy=C.forwardRef(({__scopeSelect:t,value:a,...i},o)=>{const u=C.useRef(null),c=yt(o,u),d=ME(a);return C.useEffect(()=>{const g=u.current;if(!g)return;const m=window.HTMLSelectElement.prototype,v=Object.getOwnPropertyDescriptor(m,"value").set;if(d!==a&&v){const b=new Event("change",{bubbles:!0});v.call(g,a),g.dispatchEvent(b)}},[d,a]),E.jsx(tt.select,{...i,style:{...iy,...i.style},ref:c,defaultValue:a})});Vy.displayName=B_;function By(t){return t===""||t===void 0}function Gy(t){const a=Il(t),i=C.useRef(""),o=C.useRef(0),u=C.useCallback(d=>{const g=i.current+d;a(g),function m(h){i.current=h,window.clearTimeout(o.current),h!==""&&(o.current=window.setTimeout(()=>m(""),1e3))}(g)},[a]),c=C.useCallback(()=>{i.current="",window.clearTimeout(o.current)},[]);return C.useEffect(()=>()=>window.clearTimeout(o.current),[]),[i,u,c]}function Py(t,a,i){const u=a.length>1&&Array.from(a).every(h=>h===a[0])?a[0]:a,c=i?t.indexOf(i):-1;let d=G_(t,Math.max(c,0));u.length===1&&(d=d.filter(h=>h!==i));const m=d.find(h=>h.textValue.toLowerCase().startsWith(u.toLowerCase()));return m!==i?m:void 0}function G_(t,a){return t.map((i,o)=>t[(a+o)%t.length])}var P_=py,k_=yy,$_=by,q_=xy,Y_=wy,F_=Cy,X_=Ty,I_=Dy,K_=Ny,Z_=Hy,Q_=Ly,W_=Uy;function J_({...t}){return E.jsx(P_,{"data-slot":"select",...t})}function eT({className:t,...a}){return E.jsx($_,{"data-slot":"select-value",className:Be(t),...a})}function tT({className:t,size:a="sm",children:i,...o}){return E.jsxs(k_,{"data-slot":"select-trigger","data-size":a,className:Be("border-input cursor-pointer bg-white data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-accent-blue focus-visible:ring-accent-blue-500/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-slate-25 dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center shadow-xs justify-between gap-2 rounded-md border px-3 py-1.5 text-sm whitespace-nowrap transition-[color] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[i,E.jsx(q_,{asChild:!0,children:E.jsx(C0,{className:"size-3 opacity-50"})})]})}function nT({className:t,children:a,position:i="popper",viewPortClassName:o,...u}){return E.jsx(Y_,{children:E.jsxs(F_,{"data-slot":"select-content",className:Be("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",i==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...u,children:[E.jsx(aT,{}),E.jsx(X_,{className:Be("p-1",i==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1",o),children:a}),E.jsx(rT,{})]})})}function lT({className:t,children:a,...i}){return E.jsxs(I_,{"data-slot":"select-item",className:Be("focus:bg-accent/15 text-fg-body focus:text-accent [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2 data-[state=checked]:bg-accent/15 data-[state=checked]:text-accent-blue",t),...i,children:[E.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:E.jsx(Z_,{children:E.jsx(aC,{className:"size-4 text-accent-blue"})})}),E.jsx(K_,{className:"text-sm",children:a})]})}function aT({className:t,...a}){return E.jsx(Q_,{"data-slot":"select-scroll-up-button",className:Be("flex cursor-default items-center justify-center py-1",t),...a,children:E.jsx(cC,{className:"size-4"})})}function rT({className:t,...a}){return E.jsx(W_,{"data-slot":"select-scroll-down-button",className:Be("flex cursor-default items-center justify-center py-1",t),...a,children:E.jsx(C0,{className:"size-4"})})}const iT=({id:t,label:a,options:i,onChange:o,placeholder:u="Select...",disabled:c=!1,className:d,value:g})=>{const[m,h]=C.useState(g!==void 0?g:void 0),v=g!==void 0?g:m,b=!!v&&!c,S=R=>{const w=i.find(A=>String(A.value)===R);w&&(o(w.value),h(w.value))},x=v!==void 0?String(v):void 0;return E.jsxs("div",{className:Be("relative inline-flex items-center",d),children:[E.jsxs(J_,{value:x,onValueChange:S,disabled:c,children:[E.jsxs(tT,{id:t,"aria-label":a,"data-size":"xs",className:Be("border-dashed rounded-full shadow-none text-xs w-auto pl-3 pr-2 py-1.5 gap-1.5",{"pr-6":b,"text-accent-blue-400 font-medium":!!x}),children:[a&&E.jsx("div",{className:"text-xs text-fg-secondary font-medium",children:a}),E.jsx("div",{className:"h-full w-px mx-1.5 bg-slate-300"}),E.jsx(eT,{placeholder:u})]}),E.jsx(nT,{className:"p-0",viewPortClassName:"p-0",children:i.map(R=>E.jsx(lT,{value:String(R.value),disabled:R.disabled,className:Be("rounded-none text-sm",{"opacity-50 cursor-not-allowed":R.disabled}),children:R.label},String(R.value)))})]}),b&&E.jsx("button",{type:"button",onClick:()=>{const R="";o(R),h(R)},className:"absolute right-2 flex items-center justify-center w-4 h-4 rounded-full hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-accent-blue-500/50 cursor-pointer transition-colors","aria-label":"Clear selection",children:E.jsx(hC,{className:"w-3 h-3 text-slate-400"})})]})},oT=({})=>{const{t}=hs(),{hasDataCategorySearchMap:a}=pl(vd),{selectedCategory:i,searchTerm:o}=pl(Jt),u=Object.entries(M0).filter(([c])=>c!==hl).map(([c,d])=>({label:d,value:c,disabled:o!==""&&!a[c]}));return E.jsx("div",{className:"flex mt-2 p-2 rounded-md w-full justify-between bg-slate-50 items-center gap-2",children:E.jsxs("div",{className:"flex gap-2 text-sm items-center text-fg-secondary px-2",children:[E.jsx("div",{children:t("Show all results in")}),E.jsx(iT,{id:"category-select",label:"Category",options:u,onChange:c=>A0(c),placeholder:t("Select category"),value:i,className:""})]})})},sT=()=>{const{loading:t,error:a}=hs(),{searchTerm:i}=pl(Jt),[o,u]=C.useState(!1);return C.useEffect(()=>{o||(JC(),u(!0))},[o]),t?E.jsx("div",{children:"Loading translations..."}):a?E.jsx("div",{className:"text-red-500",children:E.jsx("div",{className:"text-red-500",children:E.jsxs("p",{children:["Error loading translations: ",a]})})}):E.jsxs("div",{className:"px-4 py-8 flex flex-col gap-2 items-start",children:[E.jsxs("div",{className:"px-4 flex flex-col justify-start w-full",children:[E.jsx(rw,{title:"Search Results"}),i&&E.jsx("div",{className:"flex gap-2",children:E.jsxs("div",{className:"text-sm text-fg-secondary",children:['for "',E.jsx("span",{className:"font-medium",children:i}),'"']})})]}),E.jsx(oT,{}),E.jsx("div",{className:"w-full px-4 mt-4",children:E.jsx(VR,{className:"mt-2"})})]})},zv=window.location.origin,uT=()=>(Cx(),C.useEffect(()=>{const t=a=>{if(a.data&&a.data.type==="quickNavRoutes"&&Array.isArray(a.data.data))try{FC(a.data.data)}catch(i){console.error("Error processing hub features data from parent:",i)}else if(a.data&&a.data.type==="clubId"&&typeof a.data.data=="string")try{zR(XC()?"TestClubSG":a.data.data),console.log("ClubId received and stored:",a.data.data)}catch(i){console.error("Error processing clubId data from parent:",i)}else if(a.data&&a.data.type==="searchTerm"&&typeof a.data.data=="string")try{eR(a.data.data),console.log("Search term received and stored:",a.data.data)}catch(i){console.error("Error processing searchTerm data from parent:",i)}else a.origin===zv?console.debug("Received message from parent with different structure:",a):console.debug("Received message from an unexpected origin or with different structure:",a.data,a.origin)};return window.addEventListener("message",t),window.parent!==window&&(console.log("send iframeReady message to parent"),window.parent.postMessage({type:"iframeReady",payload:{status:"ready"}},zv)),()=>{window.removeEventListener("message",t)}},[]),E.jsx(sT,{})),cT={fetcher:Tf,dedupingInterval:2e3,errorRetryCount:3,errorRetryInterval:5e3,revalidateOnFocus:!0,revalidateOnReconnect:!0,refreshInterval:0};gb.createRoot(document.getElementById("root")).render(E.jsx(C.StrictMode,{children:E.jsx(ox,{value:cT,children:E.jsx(uT,{})})}));
