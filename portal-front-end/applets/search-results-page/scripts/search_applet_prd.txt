# Overview
This project involves creating a React-based front-end applet designed to display search results from multiple sources. It will be rendered as an iframe within a larger, legacy front-end application. The applet aims to replicate and modernize the search functionality described in `react_search_migration_plan.md` and `algoliaSearch.js`, leveraging modern web technologies like React, TypeScript, Vite, TailwindCSS, SWR for data fetching, Valtio for state management where needed beyond local state/SWR, and TanStack Table for result display. The project structure will strictly adhere to the Feature-Sliced Design (FSD) architecture. **A consistent user experience guideline is to use skeleton UI components for all loading states.**

# Core Features
- **Search Input Integration:** (Assumption: Input UI exists) Integrate with the existing search input component.
- **Dynamic Data Fetching:** Implement SWR hooks to fetch data from various sources as outlined in `react_search_migration_plan.md` and `algoliaSearch.js`:
    - Internal Page Routes (`functions`)
    - Recently Viewed Wiki Articles (`recent wiki searches`)
    - Wiki Topics & Categories (`topics`)
    - Wiki Article Search Results (`hits`)
    - Store Products (`store-products`)
    - Marketing & Print Designs (`marketingprint`)
    - CCTV People (`cctv-people`)
- **Multi-Source Result Display:** Utilize TanStack Table (`@tanstack/react-table`) within the `ResultList` component to dynamically render heterogeneous search results grouped by their source. Implement custom column definitions and cell rendering logic tailored to each data source's structure. Display skeleton loaders while data is loading.
- **Cookie-Based Translation:** Modify the existing translation mechanism (likely using a custom hook like `useTranslation`) to read the desired language from a cookie set by the parent legacy application domain. **Crucially, if the cookie cannot be read (e.g., due to cross-domain restrictions in local development or missing cookie), the applet MUST default to the 'en' language code.** JSON translation files are fetched from `/assets/translations`.
- **Result Navigation:** Handle user interaction (clicks) on search results to communicate the desired navigation URL back to the parent iframe (likely via `window.postMessage`).

# User Experience
- **Target Audience:** Users of the main legacy application performing searches.
- **User Journey:** User types query -> Applet fetches results from multiple sources -> **Skeleton UI is shown** -> Results are displayed grouped by source in a dynamic table -> User clicks a result -> Applet notifies parent frame to navigate.
- **UI/UX:** Clean, efficient display of search results. Clear visual separation between different data sources. **Use skeleton loaders for all loading states.** Appropriate "no results" states. Basic keyboard navigation support within the results list is desirable.

# Technical Architecture
- **Framework/Libraries:** React, TypeScript, Vite, TailwindCSS, SWR, TanStack Table (`@tanstack/react-table`), Valtio, clsx/tailwind-merge (`cn` utility), Vitest/React Testing Library.
- **State Management:** Primarily local component state and SWR for server state. **Valtio will be used for any required shared client-side state beyond what SWR/Context provides.**
- **Project Structure:** Feature-Sliced Design (FSD). Key slices: `features/translation`, `features/DynamicSearch` (for core search logic/UI), `shared` (utilities, types, base components).
- **Translation:** Custom hook (`useTranslation`), configured to load language from cookies. **Defaults to 'en' if cookie is inaccessible.** JSON translation files fetched from `/assets/translations`.
- **API Interaction:** SWR hooks encapsulate fetch logic for REST/Algolia endpoints defined in the migration plan and `algoliaSearch.js`.
- **Parent Communication:** `window.postMessage` for sending navigation events/URLs to the parent frame. Potential for receiving initial configuration (like `selectedID` or language) via `postMessage` as well.
- **Loading States:** **Skeleton UI components MUST be used consistently across the application to indicate loading.**

# Development Roadmap (Phased Approach)
- **Phase 1 (Foundation & Core Search):**
    - Implement cookie-based translation with **mandatory 'en' fallback**.
    - Set up `features/DynamicSearch` slice structure (api, hooks, model, types, ui).
    - Implement SWR hooks and data transformation logic for 2-3 core sources (e.g., `hits`, `topics`, `functions`).
    - Integrate TanStack Table into `ResultList.tsx` to display results from these initial sources with basic, distinct rendering per source. **Implement skeleton loading for the table.**
    - Implement basic tests for translation and initial SWR hooks.
- **Phase 2 (Expand Sources & Navigation):**
    - Implement SWR hooks for the remaining data sources.
    - Enhance TanStack Table configuration to handle all source types with appropriate custom cell renderers (icons, images, highlighting).
    - Implement "Show More" footer logic based on total results vs. displayed.
    - Implement `postMessage` navigation for result clicks.
    - Add comprehensive tests for all data hooks and table rendering logic. **Ensure tests cover skeleton loading states.**
- **Phase 3 (Refinement):**
    - Implement result term highlighting within the table.
    - Add keyboard navigation within the result list.
    - Refine loading states (skeletons) and error handling.
    - Optimize performance.

# Logical Dependency Chain
1. Translation (Cookie/Fallback 'en')
2. `features/DynamicSearch` Slice Setup
3. SWR Hooks (Initial Sources) + Tests
4. TanStack Table Integration (Basic + Skeleton Loading)
5. SWR Hooks (Remaining Sources) + Tests
6. TanStack Table Enhancement (All Sources Rendering)
7. Navigation (`postMessage`)
8. UI/UX Refinements (Highlighting, Keyboard Nav, Loaders)

# Risks and Mitigations
- **Cross-Domain Cookie Access (Local):** Difficulty accessing cookies from `localhost:3000` during local dev. **Mitigation:** Implement the mandatory 'en' fallback. Use browser dev tools to manually set cookies for testing specific languages if needed. Consider `postMessage` from parent for language if coordination is feasible.
- **TanStack Table Complexity:** Customizing columns/cells for many different data structures can be complex. **Mitigation:** Implement incrementally, source by source. Create reusable cell rendering components. Leverage TanStack Table's features for conditional rendering.
- **Parent Frame Communication:** Ensuring reliable `postMessage` communication requires coordination with the legacy front-end. **Mitigation:** Define a clear message structure/protocol. Implement robust error handling and origin checks.
- **Data Transformation Logic:** Accurately replicating the transformation logic from `algoliaSearch.js` requires careful implementation. **Mitigation:** Refer closely to `react_search_migration_plan.md` and the provided `algoliaSearch.js`. Write unit tests for transformation functions within SWR hooks.

# Appendix
- `react_search_migration_plan.md`: Details target API endpoints/logic.
- `algoliaSearch.js`: (Located in project root) Legacy JavaScript implementation for reference.
- `fsd-architecture.mdc`: Project's Feature-Sliced Design guidelines. 