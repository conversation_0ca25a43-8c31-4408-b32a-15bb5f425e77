{"$schema": "http://json-schema.org/draft-04/schema#", "description": "", "type": "object", "properties": {"designs": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["usageGuidelines", "approvalStatus", "brand", "designVisibility", "club", "publishingAutomationPublishNow", "url", "name", "updated", "deleted", "publishingAutomationNeverExpire", "id", "thumbnail", "imageContent", "isPublished", "htmlContent", "designType", "objectID"], "properties": {"usageGuidelines": {"type": "string"}, "approvalStatus": {"type": "boolean"}, "brand": {"type": "string", "minLength": 1}, "internalComments": {"type": "array", "items": {"required": [], "properties": {}}}, "designVisibility": {"type": "string", "minLength": 1}, "club": {"type": "string", "minLength": 1}, "publishingAutomationPublishNow": {"type": "boolean"}, "url": {"type": "string", "minLength": 1}, "name": {"type": "string", "minLength": 1}, "updated": {"type": "string", "minLength": 1}, "deleted": {"type": "number"}, "designRegion": {"type": "array", "items": {"required": [], "properties": {}}}, "publishingAutomation": {"type": "object", "properties": {"autoUnPublish": {"type": "number"}, "autoPublish": {"type": "number"}}, "required": ["autoUnPublish", "autoPublish"]}, "publishingAutomationNeverExpire": {"type": "boolean"}, "thumbnails_large": {"type": "array", "items": {"required": [], "properties": {}}}, "id": {"type": "string", "minLength": 1}, "thumbnail": {"type": "string", "minLength": 1}, "imageContent": {"type": "string", "minLength": 1}, "tags": {"type": "array", "items": {"required": [], "properties": {}}}, "isPublished": {"type": "boolean"}, "htmlContent": {"type": "string"}, "designType": {"type": "string", "minLength": 1}, "objectID": {"type": "string", "minLength": 1}, "_snippetResult": {"type": "object", "properties": {"usageGuidelines": {"type": "object", "properties": {"value": {"type": "string"}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "brand": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "designVisibility": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "club": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "url": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "name": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "updated": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "deleted": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "designRegion": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["value", "matchLevel"], "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}}}, "publishingAutomation": {"type": "object", "properties": {"autoUnPublish": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "autoPublish": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}}, "required": ["autoUnPublish", "autoPublish"]}, "thumbnails_large": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["value", "matchLevel"], "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}}}, "id": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "thumbnail": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "imageContent": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "tags": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["value", "matchLevel"], "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}}}, "htmlContent": {"type": "object", "properties": {"value": {"type": "string"}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "designType": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}}, "required": ["usageGuidelines", "brand", "designVisibility", "club", "url", "name", "updated", "deleted", "designRegion", "publishingAutomation", "thumbnails_large", "id", "thumbnail", "imageContent", "tags", "htmlContent", "designType"]}, "_highlightResult": {"type": "object", "properties": {"name": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}, "fullyHighlighted": {"type": "boolean"}, "matchedWords": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["value", "matchLevel", "fullyHighlighted", "matched<PERSON>ords"]}, "imageContent": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}, "fullyHighlighted": {"type": "boolean"}, "matchedWords": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["value", "matchLevel", "fullyHighlighted", "matched<PERSON>ords"]}, "htmlContent": {"type": "object", "properties": {"value": {"type": "string"}, "matchLevel": {"type": "string", "minLength": 1}, "matchedWords": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["value", "matchLevel", "matched<PERSON>ords"]}}, "required": ["name", "imageContent", "htmlContent"]}, "_rankingInfo": {"type": "object", "properties": {"nbTypos": {"type": "number"}, "firstMatchedWord": {"type": "number"}, "proximityDistance": {"type": "number"}, "userScore": {"type": "number"}, "geoDistance": {"type": "number"}, "geoPrecision": {"type": "number"}, "nbExactWords": {"type": "number"}, "words": {"type": "number"}, "filters": {"type": "number"}}, "required": ["nbTypos", "firstMatchedWord", "proximityDistance", "userScore", "geoDistance", "geoPrecision", "nbExactWords", "words", "filters"]}}}}, "tags": {"type": "array", "items": {"required": [], "properties": {}}}, "results": {"type": "object", "properties": {"hits": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["usageGuidelines", "approvalStatus", "brand", "designVisibility", "club", "publishingAutomationPublishNow", "url", "name", "updated", "deleted", "publishingAutomationNeverExpire", "id", "thumbnail", "imageContent", "isPublished", "htmlContent", "designType", "objectID"], "properties": {"usageGuidelines": {"type": "string"}, "approvalStatus": {"type": "boolean"}, "brand": {"type": "string", "minLength": 1}, "internalComments": {"type": "array", "items": {"required": [], "properties": {}}}, "designVisibility": {"type": "string", "minLength": 1}, "club": {"type": "string", "minLength": 1}, "publishingAutomationPublishNow": {"type": "boolean"}, "url": {"type": "string", "minLength": 1}, "name": {"type": "string", "minLength": 1}, "updated": {"type": "string", "minLength": 1}, "deleted": {"type": "number"}, "designRegion": {"type": "array", "items": {"required": [], "properties": {}}}, "publishingAutomation": {"type": "object", "properties": {"autoUnPublish": {"type": "number"}, "autoPublish": {"type": "number"}}, "required": ["autoUnPublish", "autoPublish"]}, "publishingAutomationNeverExpire": {"type": "boolean"}, "thumbnails_large": {"type": "array", "items": {"required": [], "properties": {}}}, "id": {"type": "string", "minLength": 1}, "thumbnail": {"type": "string", "minLength": 1}, "imageContent": {"type": "string", "minLength": 1}, "tags": {"type": "array", "items": {"required": [], "properties": {}}}, "isPublished": {"type": "boolean"}, "htmlContent": {"type": "string"}, "designType": {"type": "string", "minLength": 1}, "objectID": {"type": "string", "minLength": 1}, "_snippetResult": {"type": "object", "properties": {"usageGuidelines": {"type": "object", "properties": {"value": {"type": "string"}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "brand": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "designVisibility": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "club": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "url": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "name": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "updated": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "deleted": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "designRegion": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["value", "matchLevel"], "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}}}, "publishingAutomation": {"type": "object", "properties": {"autoUnPublish": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "autoPublish": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}}, "required": ["autoUnPublish", "autoPublish"]}, "thumbnails_large": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["value", "matchLevel"], "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}}}, "id": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "thumbnail": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "imageContent": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "tags": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"required": ["value", "matchLevel"], "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}}}, "htmlContent": {"type": "object", "properties": {"value": {"type": "string"}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}, "designType": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}}, "required": ["value", "matchLevel"]}}, "required": ["usageGuidelines", "brand", "designVisibility", "club", "url", "name", "updated", "deleted", "designRegion", "publishingAutomation", "thumbnails_large", "id", "thumbnail", "imageContent", "tags", "htmlContent", "designType"]}, "_highlightResult": {"type": "object", "properties": {"name": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}, "fullyHighlighted": {"type": "boolean"}, "matchedWords": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["value", "matchLevel", "fullyHighlighted", "matched<PERSON>ords"]}, "imageContent": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}, "matchLevel": {"type": "string", "minLength": 1}, "fullyHighlighted": {"type": "boolean"}, "matchedWords": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["value", "matchLevel", "fullyHighlighted", "matched<PERSON>ords"]}, "htmlContent": {"type": "object", "properties": {"value": {"type": "string"}, "matchLevel": {"type": "string", "minLength": 1}, "matchedWords": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["value", "matchLevel", "matched<PERSON>ords"]}}, "required": ["name", "imageContent", "htmlContent"]}, "_rankingInfo": {"type": "object", "properties": {"nbTypos": {"type": "number"}, "firstMatchedWord": {"type": "number"}, "proximityDistance": {"type": "number"}, "userScore": {"type": "number"}, "geoDistance": {"type": "number"}, "geoPrecision": {"type": "number"}, "nbExactWords": {"type": "number"}, "words": {"type": "number"}, "filters": {"type": "number"}}, "required": ["nbTypos", "firstMatchedWord", "proximityDistance", "userScore", "geoDistance", "geoPrecision", "nbExactWords", "words", "filters"]}}}}, "nbHits": {"type": "number"}, "page": {"type": "number"}, "nbPages": {"type": "number"}, "hitsPerPage": {"type": "number"}, "facets": {"type": "object", "properties": {"club": {"type": "object", "properties": {"global": {"type": "number"}}, "required": ["global"]}, "designType": {"type": "object", "properties": {"static": {"type": "number"}, "dynamic": {"type": "number"}}, "required": ["static", "dynamic"]}, "isPublished": {"type": "object", "properties": {"true": {"type": "number"}}, "required": ["true"]}, "designRegion": {"type": "object", "properties": {"SG": {"type": "number"}, "AU": {"type": "number"}, "NZ": {"type": "number"}, "US": {"type": "number"}, "GB": {"type": "number"}, "IE": {"type": "number"}}, "required": ["SG", "AU", "NZ", "US", "GB", "IE"]}, "approvalStatus": {"type": "object", "properties": {"true": {"type": "number"}, "false": {"type": "number"}}, "required": ["true", "false"]}, "designVisibility": {"type": "object", "properties": {"region": {"type": "number"}, "global": {"type": "number"}}, "required": ["region", "global"]}, "publishingAutomationPublishNow": {"type": "object", "properties": {"true": {"type": "number"}}, "required": ["true"]}, "publishingAutomationNeverExpire": {"type": "object", "properties": {"true": {"type": "number"}}, "required": ["true"]}}, "required": ["club", "designType", "isPublished", "designRegion", "approvalStatus", "designVisibility", "publishingAutomationPublishNow", "publishingAutomationNeverExpire"]}, "exhaustiveFacetsCount": {"type": "boolean"}, "exhaustiveNbHits": {"type": "boolean"}, "exhaustiveTypo": {"type": "boolean"}, "exhaustive": {"type": "object", "properties": {"facetsCount": {"type": "boolean"}, "nbHits": {"type": "boolean"}, "typo": {"type": "boolean"}}, "required": ["facetsCount", "nbHits", "typo"]}, "query": {"type": "string", "minLength": 1}, "params": {"type": "string", "minLength": 1}, "serverUsed": {"type": "string", "minLength": 1}, "indexUsed": {"type": "string", "minLength": 1}, "parsedQuery": {"type": "string", "minLength": 1}, "timeoutCounts": {"type": "boolean"}, "timeoutHits": {"type": "boolean"}, "renderingContent": {"type": "object", "properties": {}, "required": []}, "processingTimeMS": {"type": "number"}, "processingTimingsMS": {"type": "object", "properties": {"_request": {"type": "object", "properties": {"roundTrip": {"type": "number"}}, "required": ["roundTrip"]}, "getIdx": {"type": "object", "properties": {"total": {"type": "number"}}, "required": ["total"]}, "total": {"type": "number"}}, "required": ["_request", "getIdx", "total"]}, "serverTimeMS": {"type": "number"}}, "required": ["hits", "nbHits", "page", "nbPages", "hitsPerPage", "facets", "exhaustiveFacetsCount", "exhaustiveNbHits", "exhaustiveTypo", "exhaustive", "query", "params", "serverUsed", "indexUsed", "parsed<PERSON><PERSON><PERSON>", "timeoutCounts", "timeoutHits", "renderingContent", "processingTimeMS", "processingTimingsMS", "serverTimeMS"]}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "number"}, "totalPages": {"type": "number"}, "totalHits": {"type": "number"}, "hitsPerPage": {"type": "number"}}, "required": ["currentPage", "totalPages", "totalHits", "hitsPerPage"]}, "config": {"type": "object", "properties": {"algoliaIndex": {"type": "string", "minLength": 1}, "algoliaArgs": {"type": "object", "properties": {"attributesToRetrieve": {"type": "array", "items": {"required": [], "properties": {}}}, "hitsPerPage": {"type": "number"}, "page": {"type": "number"}, "facets": {"type": "array", "items": {"required": [], "properties": {}}}, "facetFilters": {"type": "array", "items": {"required": [], "properties": {"0": {"type": "array", "uniqueItems": true, "items": {"required": [], "properties": {}}}, "1": {"type": "array", "uniqueItems": true, "items": {"required": [], "properties": {}}}}}}, "attributesToSnippet": {"type": "array", "items": {"required": [], "properties": {}}}, "attributesToHighlight": {"type": "array", "items": {"required": [], "properties": {}}}, "getRankingInfo": {"type": "boolean"}, "restrictSearchableAttributes": {"type": "array", "items": {"required": [], "properties": {}}}}, "required": ["attributesToRetrieve", "hitsPerPage", "page", "facets", "facetFilters", "attributesToSnippet", "attributesToHighlight", "getRankingInfo", "restrictSearchableAttributes"]}}, "required": ["algoliaIndex", "algoliaArgs"]}}, "required": ["designs", "tags", "results", "pagination", "config"]}