# React Migration Plan for Algolia Search

**Goal:** Replicate the multi-source search functionality of `algoliaSearch.js` in a modern React application, preserving the core business logic for fetching, transforming, and displaying results, while adhering to the project's `react_migration_strategy.md`.

**Analysis of Legacy Implementation (`algoliaSearch.js`)**

This section details the core functionalities of the existing vanilla JavaScript search implementation.

**1. Data Sources & APIs:**

The search integrates multiple data sources:

- **Internal Page Routes (`functions` source):**
  - **Source:** Predefined JavaScript array `quickNavagateRoutes` (filtered locally).
  - **Logic:** Filters based on query matching `title`. Active only when query is not empty.
- **Recently Viewed Wiki Articles (`recent wiki searches` source):**
  - **Source:** `localStorage.getItem('viewedArticles')`.
  - **Logic:** Retrieves the first 4 items. Active only when query is empty.
- **Wiki Topics & Categories (`topics` source):**
  - **Source:** `fetch('/openkb/api/topics')` (GET).
  - **Logic:** Fetches topics/subtopics, flattens the structure. Active only when query is not empty.
- **Wiki Article Search Results (`hits` source):**
  - **Source (Empty Query):** Direct Algolia client (`getAlgoliaResults`) using `algoliaConfig.index`.
  - **Source (With Query):** Internal endpoint `fetch('/api/algolia/search-wiki/', { method: 'POST' })`. Proxies to Algolia, adds context (e.g., `selectedID`).
  - **Logic:** Fetches top articles (excluding recent) for empty query. Fetches search results via backend for non-empty query.
- **Store Products (`store-products` source):**
  - **Source:** `fetch(\`/api/store/clubs/\${selectedID}/products?search=\${query}\`)` (GET).
  - **Logic:** Searches products within a specific club (`selectedID`). Active only when query is not empty.
- **Marketing & Print Designs (`marketingprint` source):**
  - **Source:** `fetch('/api/algolia/search-marketing-print', { method: 'POST' })`.
  - **Logic:** Backend proxies to Algolia, adds context (`selectedID`, `adminQuery`). Active only when query is not empty.
- **CCTV People (`cctv-people` source):**
  - **Source:** `fetch(\`/api/cctv/people/\${selectedID}?search=\${query}\`)` (GET).
  - **Logic:** Searches activity logs within a specific club (`selectedID`). Active only when query is not empty.

**2. Data Transformation & Usage per Source:**

- **`functions`:** Filters `availablePageRoutes`, sorts by relevance, takes top 3. Stores `item.url` for navigation.
- **`recent wiki searches`:** Parses JSON, builds breadcrumb string, decodes HTML entities in `label`. Sets URL for navigation.
- **`topics`:** Flattens hierarchy, converts SVG icon to Base64, creates combined `label`, filters by query match. Sets URL for navigation.
- **`hits` (Wiki):** Uses Algolia highlighting/snippeting (via backend or components). Sets URL for navigation.
- **`store-products`:** Stores `totalItems`. Formats description (strips HTML, truncates). Sets URL for navigation. Uses `item.images[0]`.
- **`marketingprint`:** Calculates `mpTotalHits`. Uses Algolia highlight result or plain name. Manually highlights tags matching query. Sets URL for navigation. Uses thumbnail URL.
- **`cctv-people`:** Sets URL for navigation. Displays name, club ID, status. Uses avatar/shadow photo.

**3. Result Display & Navigation Logic:**

- **Conditional Display:** Sources are shown based on whether the search query is empty or not.
- **Rendering:** Algolia Autocomplete's `templates` render HTML for headers, items, and footers.
- **Highlighting:** Wiki and Marketing/Print results highlight search terms.
- **No Results:** Shows skeleton loader or "no results" message.
- **Footers:** Conditionally show "Show more results" links based on total hits vs. displayed count.
- **Debouncing:** API calls are debounced by 300ms.
- **Navigation:** Item clicks set `urlToLoadOnStateChange`. Actual navigation (using `sammy.setLocation` or `window.open`) occurs in `onStateChange` after selection and widget close. Special handling for CCTV iframe communication.
- **Lazy Loading:** Uses `MutationObserver` to detect new images in the results and set `src` from `data-src` for basic lazy loading.

**Migration Plan**

**Phase 1: Component Structure & State Management**

1.  **`GlobalSearchController` (Container Component):**
    - Manages the overall search state: `query`, `isOpen`, `isLoading`, `resultsBySource`, `activeItemId`, `urlToNavigate`.
    - Handles opening/closing the search modal/widget.
    - Manages focus and keyboard shortcuts (e.g., Cmd/Ctrl+K). Consider using a context or state management library (like Zustand or Redux Toolkit) if state needs to be accessed/modified deeply or globally, as outlined in `react_migration_strategy.md`.
2.  **`SearchBarInput` (Presentational Component):**
    - Renders the actual `<input>` element.
    - Receives `query` and `placeholder` as props.
    - Calls `onQueryChange` prop (debounced in the controller) when the input value changes.
    - Handles keyboard events (Enter for selection, Up/Down for navigation - managed by a hook/controller).
3.  **`SearchResultsDropdown` (Presentational Component):**
    - Displays the list of results grouped by source.
    - Receives `resultsBySource`, `isLoading`, `query`, `activeItemId` as props.
    - Renders `SourceSection` components for each source that has results or is loading.
    - Renders loading skeletons or "no results" message based on state.
4.  **`SourceSection` (Presentational Component):**
    - Renders the header for a specific source (e.g., "Wiki Resources").
    - Maps over the items for that source and renders a `ResultItem` for each.
    - Renders the source-specific footer (e.g., "Show more results" link).
    - Receives `sourceId`, `title`, `items`, `isLoading`, `query`, `footerComponent` as props.
5.  **`ResultItem` (Presentational Component):**
    - Renders a single search result item based on its source type.
    - Uses conditional rendering or specific sub-components (e.g., `WikiResultItem`, `ProductResultItem`) to handle different data structures and layouts (icon, image, title, description, badges).
    - Highlights matching terms in title/description based on `query` or pre-highlighted data.
    - Receives `item`, `sourceId`, `query`, `isActive` as props.
    - Calls `onItemClick` prop when clicked.

**Phase 2: Data Fetching & Logic**

1.  **Data Fetching Hooks:** Create custom hooks (e.g., `useWikiSearch`, `useStoreSearch`, `useMarketingPrintSearch`, `useCctvSearch`, `useTopics`) to encapsulate the logic for fetching data from each respective API endpoint.
    - These hooks will take the `query` and necessary context (like `selectedID` or `club`) as arguments.
    - Use `useEffect` and `fetch` (or a library like `axios`, `react-query`, `swr` as per `react_migration_strategy.md`) for making API calls.
    - Implement the same data transformation logic as found in the original `getItems` functions within these hooks.
    - Handle loading and error states within each hook.
    - Consider using `react-query` or `swr` for caching, refetching, and managing server state effectively.
2.  **Debouncing:** Implement debouncing (e.g., using `lodash/debounce` or a custom hook like `useDebounce`) in the `GlobalSearchController` before triggering the data fetching hooks based on the `query` state.
3.  **Source Aggregation:** In `GlobalSearchController`, call the data fetching hooks conditionally based on whether the `query` is empty or not, similar to the original logic. Aggregate the results from all active hooks into the `resultsBySource` state.
4.  **Local Data:**
    - Manage `availablePageRoutes` (for the `functions` source) possibly via context or import if it's static config. Filter this array directly in the controller based on the debounced query.
    - Fetch `recentlyViewedArticles` from `localStorage` directly within the controller when the query is empty. Create a utility function `getRecentlyViewedArticles()` similar to the original.
5.  **Highlighting:** Create a utility component or function (e.g., `<HighlightText text={item.title} query={query} />`) to handle highlighting matched terms within result items if the API doesn't provide pre-highlighted fields (like for `functions` or `topics`
