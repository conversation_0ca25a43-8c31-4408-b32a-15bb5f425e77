---
description: follow this architecture when everytime you have new or update files 
globs: 
alwaysApply: false
---
# Feature-Sliced Design Architecture

This project follows the Feature-Sliced Design (FSD) methodology, adapted for React conventions.

## Directory Structure

```
/src
  main.tsx         # Application entry point
  /pages           # Route-based components
    App.tsx        # Main page component
    App.css
  /features        # Business logic features
    /translation   # Translation feature
      /hooks       # Feature-specific hooks
        index.ts   # Translation hook implementation
        index.test.ts  # Tests for hooks
      /model       # State management
        translationModel.ts
      types.ts     # Feature-specific types
      model.ts     # State management
      utils.ts     # Helper functions
  /components      # Shared UI components
  /entities        # Domain models, DTOs, types
  /shared          # Cross-feature utilities
    /api           # API utilities
      fetcher.ts   # SWR fetcher
    /hooks         # Common hooks
      useApi.ts    # Data fetching hook
      useMutation.ts  # Mutation hook
    /ui            # UI utilities
    constants.ts   # Global constants
  /assets          # Static assets
  /__tests__       # Global tests
```

## Layer Import Rules

- Only import from the same layer or layers below
- Imports should follow this pattern:
  1. Root/App → can import from any layer
  2. `pages` → can import from `components`, `features`, `entities`, `shared`
  3. `features` → can import from `components`, `entities`, `shared`
  4. `components` → can import from `entities`, `shared`
  5. `entities` → can import from `shared`
  6. `shared` → can only import from within `shared`

## Naming Conventions

- Use domain terminology in file and component naming
- Tests should be co-located with the files they test (e.g., `Component.test.tsx` next to `Component.tsx`)
- Use barrel files (`index.ts`) for organizing exports within feature segments

## Path Aliases

Use the configured path aliases for imports:
```typescript
import { useTranslationFetch } from '@/features/translation/hooks';
import { Button } from '@/shared/ui/Button';
```

## State Management

- Feature-specific state should live within the feature
- Global state should be in the app layer
- Use appropriate state management based on scope

## Reference

For more details, see [Feature-Sliced Design documentation](mdc:https:/feature-sliced.github.io/docs).
