---
description: when agent is working on API-related queries
globs: 
alwaysApply: false
---
# SWR Integration Implementation Plan

## Overview
SWR (Stale-While-Revalidate) is a React Hooks library for data fetching that provides:
- Automatic caching and stale-while-revalidate
- Focus and network reconnection revalidation
- Deduplication of requests
- Easy error handling and retry logic

By centralizing configuration and the fetcher, we reduce boilerplate and enforce consistency across the app.

## Implementation Steps

1. **Install SWR**
   ```bash
   npm install swr
   ```

2. **Global Configuration**
   - Wrap the root component (`src/main.tsx`) with `<SWRConfig>` from `swr`.
   - Default settings:
     - `fetcher`: centralized `fetcher` from `src/shared/api/fetcher.ts`
     - `dedupingInterval`: `2000` (ms)
     - `errorRetryCount`: `3`
     - `errorRetryInterval`: `5000` (ms)
     - `revalidateOnFocus`: `true`
     - `revalidateOnReconnect`: `true`
     - `refreshInterval`: `0` (no polling)

3. **Fetcher Utility**
   - Location: `src/shared/api/fetcher.ts` (already exists)
   - Ensure it:
     - Uses `BASE_URL` from `import.meta.env.VITE_API_BASE_URL`
     - Throws on non-2xx responses with `status` & `info`
     - Parses and returns JSON payloads

4. **Global SWR Hook Wrapper**
   - File: `src/shared/hooks/useApi.ts`
   - Implement:
     ```ts
     import { useSWR, SWRConfiguration, SWRResponse } from 'swr';
     import { fetcher } from '../api/fetcher';

     export interface FetchError extends Error {
       status: number;
       info: string;
     }

     const globalConfig: SWRConfiguration = {
       dedupingInterval: 2000,
       errorRetryCount: 3,
       errorRetryInterval: 5000,
       revalidateOnFocus: true,
       revalidateOnReconnect: true,
       refreshInterval: 0,
     };

     export const useApi = <T>(
       key: string | null,
       options?: SWRConfiguration
     ): SWRResponse<T, FetchError> => {
       return useSWR<T, FetchError>(
         key,
         fetcher,
         { ...globalConfig, ...options }
       );
     };
     ```

5. **Wrap Root Component**
   - In `src/main.tsx`, import and wrap `<App />`:
     ```tsx
     import React from 'react';
     import ReactDOM from 'react-dom/client';
     import App from './App';
     import { SWRConfig } from 'swr';
     import { fetcher } from './shared/api/fetcher';

     const globalSWRConfig = {
       fetcher,
       dedupingInterval: 2000,
       errorRetryCount: 3,
       errorRetryInterval: 5000,
       revalidateOnFocus: true,
       revalidateOnReconnect: true,
       refreshInterval: 0,
     };

     ReactDOM.createRoot(document.getElementById('root')!).render(
       <SWRConfig value={globalSWRConfig}>
         <App />
       </SWRConfig>
     );
     ```

6. **Usage Example**
   ```tsx
   import { useApi } from '@shared/hooks/useApi';

   const UsersList = () => {
     const { data: users, error, isValidating, mutate } = useApi<User[]>('/api/users');
     if (error) return <div>Error: {error.info}</div>;
     if (!users) return <div>Loading...</div>;
     return (
       <ul>
         {users.map((u) => (<li key={u.id}>{u.name}</li>))}
       </ul>
     );
   };
   ```

3. **Mutations with SWR**
   - SWR v2 provides `useSWRMutation` from `'swr/mutation'` for side-effectful requests (POST/PUT/DELETE).
   - **File:** `src/shared/hooks/useMutation.ts`
   - **Implementation (v2):**
     ```ts
     import useSWRMutation from 'swr/mutation';
     import { fetcher } from '../api/fetcher';

     export interface FetchError extends Error {
       status: number;
       info: string;
     }

     export const useMutation = <TData = any, TVars = unknown>(
       key: string,
       fetcherFn?: (url: string, { arg }: { arg: TVars }) => Promise<TData>
     ) => {
       const mutationFn =
         fetcherFn ?? ((url, { arg }) =>
           fetcher<TData>(new Request(url, arg as RequestInit))
         );
       return useSWRMutation<TData, FetchError, string, TVars>(
         key,
         mutationFn
       );
     };
     ```
   - **Fallback (v1):**
     ```ts
     import { mutate } from 'swr';
     import { fetcher } from '../api/fetcher';

     export const createItem = async (payload: any) => {
       await fetcher(
         new Request('/api/items', {
           method: 'POST',
           headers: { 'Content-Type': 'application/json' },
           body: JSON.stringify(payload),
         })
       );
       // Revalidate GET cache
       await mutate('/api/items');
     };
     ```

## Updated File Structure (Feature Slicing)
```
src/
  shared/
    api/
      fetcher.ts
    hooks/
      useApi.ts
      useMutation.ts
  features/
    ...
  components/
  App.tsx
  main.tsx
```

## Best Practices, Gotchas & Tips
- **Key Naming**: Use stable string or array keys; avoid objects/functions as keys.
- **Conditional Fetching**: Pass `null` key to skip.
- **Cache Invalidation**: Use `mutate(key, newData?)` to update or revalidate.
- **Deduplication**: Tune `dedupingInterval` to prevent spamming network.
- **Error Handling**: Configure `errorRetryCount` & `errorRetryInterval` for better UX.
- **Polling**: Use `refreshInterval` for real-time data if needed.
- **SSR Hydration**: Provide `fallback` data in `<SWRConfig>` for Next.js.
- **Infinite Loading**: Use `useSWRInfinite` for pagination patterns.
- **Custom Cache Providers**: Use the `provider` option in `<SWRConfig>` for custom cache stores per user/session.

## Caveats
- SWR's global cache is a singleton by default; to isolate per user/session, supply a custom provider.
- Avoid over-caching huge payloads; paginate large data sets.
- Manage `AbortController` and request cancellation when necessary.

## Next Steps
1. Implement `fetcher.ts` and `useApi.ts` in `src/shared/api` and `src/shared/hooks`.
2. Wrap the root component with `<SWRConfig>` in `src/main.tsx` to apply global settings.
3. Refactor existing data-fetching code to use `useApi` for consistency.
4. Add usage examples and component-level documentation for future reference.
5. Monitor and adjust SWR global settings as usage patterns evolve.
