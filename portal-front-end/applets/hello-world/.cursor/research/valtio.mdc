---
description: if valtio or state management is mentioned or requested by the AI agent
globs: 
alwaysApply: false
---
# Valtio State Management

## 1. Core Concept

Valtio uses the ES Proxy API to wrap a plain JavaScript object. You mutate this proxy directly, and React components re‑render only when the properties they read change via `useSnapshot`.

```ts
import { proxy } from 'valtio';
import { useSnapshot } from 'valtio';

export const store = proxy({ count: 0 });

const Counter = () => {
  const snap = useSnapshot(store);
  return <button onClick={() => ++store.count}>Count: {snap.count}</button>;
};
```

## 2. Use Cases

- **Global UI State**: modals, drawers, theme, language preferences.
- **Complex Nested Data**: forms with deep structures, drag‑and‑drop lists.
- **Cross‑Framework Subscribers**: non‑React listeners via patches or subscriptions.
- **Prototyping & Small/Mid‑Size Apps**: minimal boilerplate and fast iteration.

## 3. Advanced Patterns & Code Examples

### 3.1 Derived / Computed State

```ts
import { proxy, derive } from 'valtio/vanilla';

const base = proxy({ price: 100, quantity: 2 });
export const derived = derive({
  total: (get) => get(base).price * get(base).quantity,
});
```

In React:

```tsx
import { useSnapshot } from 'valtio';
import { base, derived } from './store';

const Cart = () => {
  const b = useSnapshot(base);
  const d = useSnapshot(derived);
  return (
    <>
      <p>Price: {b.price}</p>
      <p>Qty: {b.quantity}</p>
      <p>Total: {d.total}</p>
    </>
  );
};
```

### 3.2 Async Actions

```ts
import { proxy } from 'valtio';

export const apiStore = proxy({
  data: null as any,
  loading: false,
  error: '' as string,
});

export const fetchData = async () => {
  apiStore.loading = true;
  apiStore.error = '';
  try {
    const res = await fetch('/api/data');
    apiStore.data = await res.json();
  } catch (e: any) {
    apiStore.error = e.message;
  } finally {
    apiStore.loading = false;
  }
};
```

### 3.3 Persistence (localStorage)

```ts
import { subscribe } from 'valtio';
import { store } from './store';

// Hydrate on init
const json = localStorage.getItem('app-state');
if (json) Object.assign(store, JSON.parse(json));

// Subscribe to changes
subscribe(store, () => {
  localStorage.setItem('app-state', JSON.stringify(store));
});
```

### 3.4 Subscriptions & Patches

```ts
import { subscribeKey } from 'valtio/utils';
import { store } from './store';

const unsubscribe = subscribeKey(store, 'count', () => {
  console.log('count changed to', store.count);
});

// Later…
unsubscribe();
```

### 3.5 DevTools Integration

```ts
import { proxyWithDevtools } from 'valtio/utils';

export const devStore = proxyWithDevtools(
  { value: 0 },
  { name: 'MyStore' }
);
// View changes in Redux DevTools
```

## 4. Caveats & Gotchas

1. **SSR & Serialization**: Proxies aren't serializable; use `snapshot(store)` when hydrating.
2. **Stale Snapshots in Concurrent Mode**: Always call `useSnapshot` at top of render, avoid reading `store` directly in JSX.
3. **Referential Equality**: Nested objects/arrays may change identity; use `useMemo` or stable refs for child props.
4. **Array Mutations**: Methods like `push`/`splice` trigger updates, but always read from snapshot in render.
5. **Memory Leaks**: External subscriptions must unsubscribe using the returned function.

## 5. Tips & Best Practices

- **Flat Store Shape**: Flatten your proxy where possible to reduce nested traps.
- **Co‑locate Actions**: Define related functions next to state definitions.
- **Immer Plugin**: Use `valtio/immer` for immutable‑style updates:
  ```ts
  import { proxy } from 'valtio';
  import { immer } from 'valtio/immer';
  export const store = proxy({}, {}, { plugins: [immer()] });
  ```
- **Clear Naming**: Use descriptive property names to ease debugging.
- **Split Large Stores**: For big apps, use multiple proxies instead of one monolithic object.
