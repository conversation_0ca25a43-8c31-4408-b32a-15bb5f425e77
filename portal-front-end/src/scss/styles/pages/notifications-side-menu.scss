.notifications-side-menu {
  width: 100%;
  max-width: 0px;
  height: calc(100vh - 70px);
  margin-top: 70px;
  transition: all 0.5s;
  font-family: "Roboto", sans-serif;
  flex-grow: 1;
  overflow: hidden;
  z-index: 9;

  &.open:not(.loading) {
    max-width: 300px;
  }

  @include respond-below(sm) {
    position: absolute;
    bottom: 0px;
    right: 0px;
    top: 0px;
    margin-top: 0px;
    transition: none;

    &.open:not(.loading) {
      max-width: 100vw;
      z-index: 1091;
    }
  }

  & > .card {
    max-width: 300px;
    width: 100%;
    height: calc(100vh - 70px);
    overflow: hidden;
    position: relative;
    background: #fff;
    position: fixed;

    @include respond-below(sm) {
      max-width: 100vw;
    }
  }
  
  .notification-side-header-container {
    padding: 16px 16px 5px 16px;
    border: 1px solid #EEEEEE;
    display: flex;
    justify-content: space-between;

    .notification-side-header-title {
      font-family: 'Source Sans Pro', sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      text-transform: uppercase;
    }

    .notification-side-header-settings {
      flex-grow: 1;
      text-align: right;
    }

    .notification-settings-button {
      border: none;
      background: none;
      font-size: 18px;

      #notif-bar-close {
        transition: opacity 200ms;
      }
    }
  }

  .notification-view-all-container {

    bottom: 0px;
    position: fixed;
    max-width: 300px;
    width: 100%;
    padding: 1rem;
    padding-bottom: 20px;
    background-color: white;

    &::before {
      content: '';
      position: absolute;
      bottom: 65px;
      left: 0px;
      right: 0px;
      height: 150px;
      background-image: linear-gradient(to bottom, rgba(255, 255, 255,0), rgba(255, 255, 255,1));
      pointer-events: none;
    }

    @include respond-below(sm) {
      max-width: 100vw;
    }

    .notification-view-all-btn {
      // width: 28rem;
      margin: 0 auto;
      display: block;
      font-family: 'Source Sans Pro', sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 14px !important;
      text-align: center;
      text-transform: uppercase;
      padding: 10px 0;
    }
  }
}

.views-info-dialog {
  fieldset {
      margin-left: auto;
      margin-right: auto;
      text-align: left;
      border: 1px solid #999999;
      margin: 2px;
      padding: 10px;
      label {
          font-weight: 400;
      }
  }
  legend {
      padding-left: 5px;
      padding-right: 5px;
      font-weight: bold;
      width: auto;
      padding: inherit;
      margin-bottom: auto;
      font-size: 15px;
      line-height: 1px;
      border: none;
      border-bottom: none;
  }
  .copy-clipboard {
      code {
          background: none;
          color: #6a7383;
          font-size: 12px;
      }
      svg {
          color: #6a7383;
          transform: translateY(2px);
      }
      @media (max-width: map-get($breakpoints, 'sm')) {
          code {
              display: inline-block;
              width: 80px;
          }
          svg {
              transform: translateY(-2px);
          }
      }
  }
}

#notifications-right-side-list {
  height: 100%;
  overflow-y: scroll;
  padding-bottom: 200px;
}
