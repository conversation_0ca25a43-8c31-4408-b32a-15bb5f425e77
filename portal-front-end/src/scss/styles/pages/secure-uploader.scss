#secure-uploader-page {
    .uploads-container {
        min-height: 350px;
    }
}

.file-type-dialog {
    .options {
        display: grid;
        grid-auto-columns: repeat(auto-fit, minmax(240px, 1fr));
        grid-auto-flow: column;
        grid-gap: 1rem;

        @include respond-below(xs) {
            grid-auto-columns: 1fr;
            grid-auto-flow: row;
        }

        & > .btn {
            padding: 3rem 1rem;
            overflow: hidden;
            gap: 1rem;
            small {
                text-align: left;
                white-space: normal;
            }
        }
    }

    .upload-btn {
        padding: 0.5rem 2rem;
        margin: 0.5rem 0;

        .material-icons {
            font-size: 18px;
            opacity: 0.85;
        }
    }

    .public-upload-desc {
        color: $amber-700;
    } 

    .internal-upload-desc {
        color: $slate-500;
    }

    .upload-desc {
        font-size: 14px;
        p {
            margin-bottom: 6px;
        }

        ul {
            padding-inline-start: 16px;
        }
    }
}

.upload-dialog {
    .custom-dz {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
        background-color: #f5f5f5;
        border: 2px dashed #ccc;
        cursor: pointer;
        margin-bottom: 1rem;
    }

    .upload-status-list {
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .upload-status-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid #ccc;
        background-color: #fff;
    }
}
