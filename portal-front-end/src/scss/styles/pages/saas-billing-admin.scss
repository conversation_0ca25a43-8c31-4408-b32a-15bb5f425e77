#saas-billing-admin {
    .container-fluid {
        padding: 32px;
        background-color: #fff;
        overflow: auto;

        @include respond-below(md) {
            padding: 32px 16px;
        }
    }
    .blocks-container {
        width: 100%;
        display: grid;
        grid-gap: 10px;
        grid-template-columns: repeat(4, 1fr);
        grid-auto-rows: 1fr;

        @media (max-width: map-get($breakpoints, 'md')) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: map-get($breakpoints, 'sm')) {
            grid-template-columns: repeat(1, 1fr);
        }
    }
    .config-block {
        transition: all 200ms;
        min-height: 210px;

        .action-buttons {
            position: absolute;
            right: 20px;
            top: 20px;
            display: flex;
            gap: 5px;
        }

        .action-btn {
            opacity: 0;
            padding: 0;
            cursor: pointer;
            transition: opacity 200ms;
            color: #8d8a8a;

            i {
                font-size: 16px;
            }

            &.btn-edit-config-block:hover {
                color: $btn-color-success;
            }

            &.btn-delete-config-block:not(.disabled):hover {
                color: $btn-color-danger;
            }

            &.btn-delete-config-block.disabled {
                pointer-events: none;
            }

            &.btn-view-logs:hover {
                color: $btn-color-info;
            }

            &.btn-new-block-fee:hover {
                color: $btn-color-primary;
            }
        }

        &:not(.isLoading):hover {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            .action-btn {
                opacity: 1;
            }

            .btn-delete-config-block.disabled {
                opacity: 0.5;
            }
        }

        &:not(.isLoading) {
            .btn-edit-block-fee:hover {
                cursor: pointer;
                background-color: #ebe7e777;
            }
            .block-fee:hover {
                background-color: #ebe7e777;
            }
        }
    }
    .custom-alert {
        padding: 16px !important;
    }

    #additional-charges-table_wrapper {
        .dataTables_processing {
            top: 40px;
            left: 0px;
            margin: 0px;
            padding: 0px !important;
            border: 0px;
            box-shadow: none;
            width: 100%;

            & > div {
                display: flex;
                flex-direction: column;

                & > div {
                    height: 43.1px;
                    padding: 10px;

                    &::after {
                        content: '';
                        display: block;
                        width: 100%;
                        height: 100%;
                        animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
                        background-color: map-get($colors-v2, 'border');
                    }
                }
            }
        }
    }

    #saas-billing-invoices-table_wrapper .dataTables_processing {
        display: none !important;
    }

    .table-control-wrapper {
        position: relative;
        display: inline-flex;

        .invoice-filters-right-container {
            margin-right: 10px;
        }
    }

}

.config-block-dialog {
    .row {
        padding: 2px;
        .col-sm-1 {
            margin-top: 5px;
        }
    }
    input[type='text'],
    input[type='number'] {
        padding: 4px;
        width: 100%;
    }
    label {
        font-weight: 400;
    }
    textarea {
        padding: 4px;
    }
    .bootstrap-tagsinput {
        padding: 0px;
        border: none !important;
        input {
            border-radius: 4px;
            padding: 4px !important;
            height: 34px;
        }
        .tag {
            border-radius: 4px;
            padding: 4px 8px;
        }
    }
}

.additional-charge-dialog {
    input::-webkit-calendar-picker-indicator {
        pointer-events: none;
    }
}

.saas-billing-record-dialog {
    .selectpicker[name="status"] + button {
        padding: 2px 10px;
    }
    .communication-logs-container {
        padding-top: 6px;
        max-height: 300px;
        overflow-y: scroll;
    }
    .stripe-timeline {
        position: relative;
        display: flex;
        gap: 1rem;
        min-height: 60px;
        padding-bottom: 1rem;

        &::before {
            content: '';
            position: absolute;
            top: 22px;
            bottom: 0;
            left: 5px;
            width: 1px;
            background-color: #ebeef1;
        }

        .type {
            margin-top: 5px;
        }
        .label {
            font-size: 14px;
            color: #404442;
            font-weight: bold;
            padding: 0;
        }
        .date {
            font-size: 12px;
            color: #6a7383;
        }
    }
    .admin-note {
        border: 1px solid map-get($colors-v2, 'border');
        padding: 6px;
        border-radius: 6px;

        .content {
            background: inherit;
            border: none;
            padding: 0px;
            font-family: 'Source Sans Pro', sans-serif;
            unicode-bidi: normal;
            margin-bottom: 0px;
        }
    }
}