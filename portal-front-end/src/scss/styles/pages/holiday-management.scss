.holidays-management {
    padding-bottom: 10rem;

    .countries-tabs {
        margin-bottom: 3rem;
    }
    #holidays-nav {
        display: flex;
        justify-content: center;

        .search-bar-wrapper {
            max-width: 768px;
            width: 100%;
        }
    }
    .table-loading-overlay {
        width: 100%;
        text-align: center;
    }
    .save-overlay {
        display: none;
        width: 100vw;
        height: 100vh;
        top: 0px;
        left: 0px;
        background-color: rgba(100, 100, 100, 0.3);
        position: fixed;
        z-index: 9999;

        .spinner-container {
            position: fixed;
            opacity: 1;
            width: 500px;
            padding: 20px;
            height: 200px;
            background-color: white;
            text-align: center;
            margin-top: 200px;
            left: calc(50% - 250px);
            .preloader {
                margin-top: 10px;
            }
        }
    }
    #holidays-table {
        .date-col {
            min-width: 100px;
        }
        .locations-col {
            width: 30%;

            div {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
            }
        }
        .name-col .name:hover {
            cursor: pointer;
            text-decoration: underline;
        }
    }
}

#holiday-dialog {
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border: 1px solid #999999;
        margin: 2px;
        padding: 10px;

        .row {
            padding: 2px;
        }
        input[type='password'],
        input[type='text'],
        input[type='number'] {
            padding: 4px;
            width: 100%;
        }
        label {
            font-weight: 400;
        }
        .help-subtext {
            font-size: 10px;
            font-style: italic;
        }
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
}
