// designer
@font-face {
    font-family: 'Gotham';
    src: url('/assets/documents/fonts/Gotham-Bold.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
}
.gjs-fixed-toolbar-icon {
    height: 30px;
}
.quick-render-preview {
    img {
        max-height: 80vh;
    }
}
.btn-quick-render,
.btn-quick-download {
    width: 150px;
}

.render-design-gallery {
    .gallery-container {
        padding: 1rem;
        background: #e9e9e9;

        height: calc(90vh - 190px);

        //max-width: 1000px;
        //max-height: 90vh;

        .row {
            // max-height: 90vh;
            .col-preview-image {
                overflow-y: scroll;
                overflow-x: hidden;
            }
            .col-page-thumbnails {
                max-height: calc(90vh - 210px);

                overflow-y: scroll;
                overflow-x: hidden;
                img {
                    border-radius: 0.75rem;
                }
            }
        }
        div[class^='col-'] {
            padding-left: 10px;
            padding-right: 10px;
        }

        .slides {
            display: none;
        }

        .column {
            &:not(:first-of-type) {
                margin-top: 1rem;
            }

            &-thumbnail {
                width: 100%;
                cursor: pointer;

                &.active {
                    border: 3px solid #1f91f3;
                }
            }
        }
    }
}

.editor-help-dialog {
    // height: 600px;
    max-height: 90vh;
    overflow-y: auto;
    padding-top: 1rem;
}

.editor-help {
    margin: 0 1.5rem 1.5rem;

    .help-container {
        text-align: left;

        &:not(:first-of-type) {
            margin-top: 1rem;
        }

        h5 {
            display: inline-block;
        }

        .row {
            ul {
                padding-left: 1.25rem;
            }

            .help-code {
                background-color: #eceff1;
                border: 1px solid #d1d2d2;
                border-radius: 4px;
                padding: 0.5rem;
            }

            img {
                margin-top: 1rem;
                max-width: 400px;
                // zoom: 0.5;
                // max-width: 100%;
            }

            p {
                margin-bottom: 5px;
            }
        }

        table {
            td {
                text-align: left;
            }
        }
    }
}

.panel-accordian-group {
    h4 {
        margin-bottom: 20px;
    }
    .error {
        color: $red;
    }

    .panel-group {
        .panel-disabled {
            pointer-events: none;
            filter: opacity(0.5);
        }
        .panel {
            + .panel {
                margin-top: 0.675rem;
            }

            border: unset;
            box-shadow: unset;

            .panel-heading {
                font-weight: bold;
                text-align: left;

                .inputGroup {
                    background-color: #fff;
                    display: block;
                    position: relative;

                    label {
                        font-size: 14px;
                        padding: 30px 55px 30px;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        position: relative;
                        z-index: 2;
                        transition: color 200ms ease-in;
                        overflow: hidden;
                        text-transform: uppercase;
                        margin-bottom: 0;
                        border: 1px solid #0000001a;
                        color: #30313d;

                        &:before {
                            // width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            content: '';
                            background-color: #1f91f3;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%) scale3d(1, 1, 1);
                            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
                            opacity: 0;
                            z-index: -1;
                        }

                        &:after {
                            width: 32px;
                            height: 32px;
                            content: '';
                            border: 2px solid #d1d7dc;
                            background-color: #fff;
                            background-image: url("data:image/svg+xml,%3Csvg width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.414 11L4 12.414l5.414 5.414L20.828 6.414 19.414 5l-10 10z' fill='%23fff' fill-rule='nonzero'/%3E%3C/svg%3E ");
                            background-repeat: no-repeat;
                            background-position: 2px 3px;
                            border-radius: 50%;
                            z-index: 2;
                            position: absolute;
                            left: 10px;
                            top: 50%;
                            transform: translateY(-50%);
                            cursor: pointer;
                            transition: all 200ms ease-in;
                            font-family: 'Material Icons';
                            margin-top: 0;
                        }
                    }

                    input:checked ~ label {
                        color: #fff;

                        &:before {
                            transform: translate(-50%, -50%) scale3d(56, 56, 1);
                            opacity: 1;
                        }

                        &:after {
                            background-color: #00acff;
                            border-color: #ffffff;
                        }
                    }

                    input {
                        width: 32px;
                        height: 32px;
                        order: 1;
                        z-index: 2;
                        position: absolute;
                        right: 30px;
                        top: 50%;
                        transform: translateY(-50%);
                        cursor: pointer;
                        visibility: hidden;
                    }
                }
            }

            .panel-body {
                border-left: 1px solid #0000001a;
                border-right: 1px solid #0000001a;
                border-bottom: 1px solid #0000001a;
                border-top: unset;
                text-align: left;
                padding: 12px 30px;
            }
        }
    }
}

.doc-setup-dialog {
    .select2-search {
        width: 100%;
    }
}
.upload-asset-dialog,
.create-static-design-dialog {
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-top: 1px solid #d3d3d3;
        margin: 2px;
        padding: 10px;

        [class*='-error'] {
            color: $red;
        }

        label.error {
            font-size: 12px;
            font-weight: 400;
            color: $red;
        }

        legend {
            padding-left: 5px;
            padding-right: 5px;
            font-weight: bold;
            width: auto;
            padding: inherit;
            margin-bottom: auto;
            font-size: 15px;
            line-height: 1px;
            border: none;
            border-bottom: none;
        }

        .select2-search {
            width: 100%;
        }
        .asset-tags__container,
        .design-tags__container {
            .select2-selection {
                background-color: #fbfbfb;
                border-color: rgb(221, 221, 221);

                .select2-selection__rendered {
                    padding: 0 12px;

                    .select2-search__field::placeholder {
                        color: #a9a9a9;
                    }
                }
            }
        }

        #asset_brand {
            border: 1px solid;
            border-radius: 1px;
            border-color: #dddddd;
        }

        #imageDZUploads {
            border-style: dashed;

            &.error {
                border-color: $red;
            }
        }
    }
}

.create-static-design-dialog {
    .design-type,
    .design-brand {
        width: 100%;
        border-color: rgb(221, 221, 221);
    }

    .selected-club {
        margin-top: 10px;
        font-size: 12px;
        text-align: right;
        transition: all 0.1s ease;
        color: #4caf50;
        font-weight: 600;
        height: 0;
        opacity: 0;
        text-decoration: underline;

        &.active {
            height: 15px;
            opacity: 1;
        }
    }
}

#editor.active.in {
    display: flex;
}

.assets-tags__dropdown,
.design-tags__dropdown {
    border-color: rgb(221, 221, 221);

    .select2-results__options {
        li {
            padding: 6px 12px;
        }
    }
}

#asset_manager {
    min-height: 400px;

    &-upload {
        .card {
            height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            cursor: pointer;

            &:hover {
                i {
                    color: darken($color: #1f91f3, $amount: 9);
                }
            }

            i {
                color: #1f91f3;
                font-size: 6rem;
                transition: color 0.4s ease;
            }

            i,
            small {
                margin-top: 0.5rem;
            }
        }
    }

    &-list {
        .row {
            margin-top: 40px;

            .asset-holder {
                .thumbnail {
                    min-height: unset;
                    padding: 0;
                    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
                    border-color: #fff;

                    .caption {
                        height: 150px;
                    }

                    &-img {
                        position: relative;

                        img {
                            max-width: 100%;
                            object-fit: contain;
                            width: 100vw;
                            height: 130px;
                            background-color: #000;
                        }

                        .asset-actions {
                            position: absolute;
                            top: 6%;
                            right: 3%;
                            display: flex;

                            .btn-circle {
                                width: 30px;
                                height: 30px;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                i {
                                    left: unset;
                                }
                            }
                        }
                    }
                }

                .asset-tags {
                    width: 100%;
                    overflow: auto;
                    padding-top: 1.5rem;
                    padding-bottom: 1rem;

                    .badge {
                        margin-right: 1rem;
                        background-color: #00b0e4;
                        color: #fff;
                    }
                }
            }
        }
    }
}

.doc-tags__container,
.asset-tags__container,
.design-tags__container {
    .select2-container--default {
        &.error {
            .select2-selection {
                border-color: $red;
            }
        }

        .select2-selection--multiple {
            border: 1px solid;
            border-radius: 1px;
        }
        .select2-search__field::placeholder {
            color: rgb(84, 84, 84);
        }
        .select2-search__field {
            width: 100% !important;
        }
        .select2-selection__choice {
            font-size: 12px;
            background-color: #0094f7;
            border: 1px solid #0094f7;
            color: #fff;

            .select2-selection__choice__remove {
                color: #fff;
            }
        }
    }
}

.select2-container--default {
    .select-tags__dropdown {
        .select2-results__option[aria-selected='true'] {
            display: none;
        }
        .select2-results__option--highlighted[aria-selected] {
            background-color: #1e91f3;
        }
    }
}

.doc-input-settings {
    .p-0 {
        padding: 0;
    }
    input[type='checkbox'] {
        margin-top: 0;
        margin-right: 0.25rem;
        vertical-align: middle;
    }
}

.materialize {
    .doc-input-settings {
        input[type='checkbox'] {
            & + label {
                padding-left: 25px;
                font-size: unset;

                &::before {
                    width: 16px;
                    height: 16px;
                }
            }

            &:checked + label:before {
                width: 9px;
                height: 18px;
            }
        }
    }
}

.sort-container {
    display: flex;
    margin-bottom: 35px;

    button {
        display: flex;

        &.sort-date {
            margin-left: 0.5rem;
        }

        .sort-toggle {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100%;
            margin-left: 0.25rem;

            span {
                color: #8e8e8e;
                transition: color 0.3s ease;

                &.asc {
                    transform: rotate(-180deg);
                    margin-bottom: 1px;
                    margin-top: 2px;
                }

                &.desc {
                    margin-top: 1px;
                }
            }

            &.asc {
                span {
                    &.asc {
                        color: #1f91f3;
                    }
                }
            }

            &.desc {
                span {
                    &.desc {
                        color: #1f91f3;
                    }
                }
            }
        }
    }
}

.designer {
    .nav-help-container {
        position: absolute;
        z-index: 9;
        right: 30px;
        margin-top: -34px;
    }

    .files-toolbar {
        width: 100%;
        border-bottom: 2px dashed #eeeeee;
        margin-top: -15px;
        padding-top: 20px;
        margin-bottom: 30px;
        padding-bottom: 30px;

        @media (min-width: map-get($breakpoints, 'sm')) {
            display: flex;
            gap: 2rem;

            .design-wrapper-container {
                flex-grow: 1;
                flex-basis: 0;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
            }
            .search-bar-wrapper {
                flex-grow: 3;
                flex-basis: 0;
                display: flex;
                justify-content: flex-end;

                & > .form-group {
                    width: 66%;
                }
            }
        }

        [class*='col-'] {
            margin-bottom: 0px;
        }

        .admin-designs-wrapper {
            padding: 5px;
            padding-top: 7px;
            label {
                font-size: 12px;
            }
        }

        .admin-designs-wrapper {
            visibility: hidden;
        }

        .search-bar-wrapper {
            .input-group {
                position: relative;
                margin-bottom: 0px;
                .input-group-addon {
                    position: absolute;
                    z-index: 10;
                }
                input {
                    padding-left: 30px;
                }
            }
        }
        .search-bar {
            font-size: 14px;
        }

        input[type='search']::-webkit-search-cancel-button {
            -webkit-appearance: none;
            cursor: pointer;
            height: 12px;
            width: 12px;
            opacity: 0.75;

            background-image: url('data:image/svg+xml;utf8,
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>
            ');
        }
    }
}

.swal2-close,
.swal2-close:hover {
    background-color: white;
}

.swal2-content {
    .preview-render-modal {
        display: flex;
        flex-direction: column;
        height: 90vh;
        width: 90vw;
        max-height: 900px;
        max-width: 1250px;

        #design_status, #design_edit {
            padding: 20px;
            padding-top: 0px;
        }

        .design-title {
            text-align: left;
        }

        .tab-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding-bottom: 0px;
            overflow-y: hidden;
        }

        .tab-pane {
            overflow-y: auto;
            overflow-x: hidden;
            height: 100%;
        }

        .comment-log {
            border: 1px dotted #ececec;
            padding: 10px;
            width: 100%;
            overflow-y: scroll;
            max-height: calc(90vh - 450px);

            .comment-block {
                padding-bottom: 5px;
            }
        }

        .publish-automation-container {
            padding-left: 20px;
        }

        .design-metadata {
            .trumbowyg-box,
            .trumbowyg-editor {
                min-height: 150px;
                margin-bottom: 10px;
            }
            .trumbowyg-button-pane::after {
                background: none;
            }
            .doc-folders-container {
                .select2:hover {
                    background-color: #f7f7f7;
                }
                .select2 {
                    background-color: #fbfbfb;

                    .select2-selection {
                        border-radius: 0;
                        // border: none;
                        border: 1px solid #dddddd;
                        // background-color: #f7f7f7;
                    }
                    .select2-dropdown {
                        border-radius: 0;
                    }
                }
            }
            .edit-small-img-preview {
                border: 1px dashed lightgrey;
                min-height: 200px;
                max-height: 70vh;
                overflow-y: auto;
                display: flex;
                justify-content: center;

                img {
                    max-width: 200px;
                    width: 100%;
                    height: auto;
                }
            }
            .edit-delete-buttons {
                width: 100%;
                text-align: right;
            }
        }

        .nav-tabs {
            // margin-right: 50px;
            margin-left: 10px;
            padding-bottom: 10px;
        }
        .checkbox-label {
            font-size: 14px;
            font-weight: 100;
        }

        .overview-metadata {
            @media (max-width: map-get($breakpoints, 'sm')) {
                min-height: auto;
                max-height: unset;
                height: auto;
            }

            .usage-guidelines-container {
                max-height: 150px;
                overflow-y: scroll;
            }
            border-left: 1px solid #eeeeee;

            height: calc(90vh - 200px);
            max-height: 820px;

            min-height: 400px;
            // max-height: 900px;
            // max-height: calc(900px - 190px);
            margin-top: 20px;

            text-align: left;

            .badge-tag {
                background-color: #1f91f3;
            }
            .badge-variable {
                background-color: unset;
                border: 1px solid grey;
                color: grey;
                margin-bottom: 10px;
            }
            .download-button {
                margin-top: px;
            }
            .metadata-content {
                margin-top: 10px;
                p {
                    margin-bottom: 0px;
                }
                .input-variables-title {
                    font-size: 12px;
                    margin-bottom: 5px;
                }
                span {
                    padding-left: 5px;
                    margin-top: -5px;
                }
                .material-icons {
                    font-size: 12px;
                }
                font-size: 10px;
            }
            // background-color: red;
        }
        .design-metadata,
        .preview-image,
        .render-design-gallery {
            .preview-image-no-image {
                text-align: left;
                padding: 10px;

                svg {
                    height: 50px;
                }
                p {
                    white-space: nowrap;
                    font-size: 11px;
                    width: 100%;
                }
            }
        }
        .preview-image,
        .render-design-gallery {
            display: flex;
            justify-content: center;
            align-items: center;
            // border: 3px solid green;

            @media (max-width: map-get($breakpoints, 'sm')) {
                min-height: auto;
                max-height: unset;
                height: auto;
            }

            .preview-image-no-image {
                width: 200px;
                height: 200px;
                border: 1px dashed grey;
            }

            #previewRenderImg,
            .individual-thumbnail {
                background-color: #fefefe;
                border: 1px dashed lightgrey;

                // background-image: -webkit-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB), -webkit-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB);
                // background-image: -moz-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB), -moz-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB);
                // background-image: -o-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB), -o-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB);
                // background-image: -ms-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB), -ms-linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB);
                // background-image: linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB), linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB);
                // -webkit-background-size:30px 30px;
                // -moz-background-size:30px 30px;
                // background-size:30px 30px;
                // background-position:0 0, 15px 15px;

                display: block;
                // max-width:calc(90vw - 75px);
                // max-height: calc(100vh - 150px);
                // max-height:90vh;

                width: auto;
                height: auto;

                max-width: 100%;
                max-height: 100%;

                margin-left: auto;
                margin-right: auto;
                overflow: hidden;
            }
            // img {
            //    max-height: calc(100% - 100px);
            //    max-width: calc(100% - 100px);
            // }
        }
        button {
            margin-top: 10px;
        }
    }
}

.swal2-footer {
    .btn-margin {
        margin: 3px;
    }
}

.gjs-mdl-container {
    z-index: 1000 !important;
}
.render-design-dialog {
    // WYSIWYG should not be as high by default...
    .trumbowyg-box,
    .trumbowyg-editor {
        min-height: 100px;
    }

    .render-design-tagline {
        .tagline-option {
            padding-left: 1rem;
            label {
                display: flex;
                align-items: flex-end;
                padding-left: 25px;
                height: 20px;

                &::before,
                &::after {
                    height: 14px;
                    width: 14px;
                }

                > div {
                    h1 {
                        font-size: 14px;
                        font-weight: normal;
                        padding: 0;
                        margin: 0;
                    }
                }
            }
        }
    }

    .asset-holder {
        cursor: pointer;
        position: relative;
        margin-right: 1rem;

        i {
            opacity: 0;
            position: absolute;
            z-index: 1;
            color: #1f91f3 !important;
            background: #fff;
            padding: 0;
            right: -5px;
            top: -2px;
            font-size: 29px;
            transition: opacity 0.2s ease;
            box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        }

        &.selected {
            img {
                transform: scale(0.99);
                box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
            }
            i {
                opacity: 1;
            }

            &:after {
                content: '';
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: 4px solid #1f91f3;
            }
        }
    }

    .render-summary {
        text-align: left;
    }
    .expand-export-format {
        position: absolute;
        margin-top: -35px;
        right: 30px;
        z-index: 9999;
        background: white;
    }
    .t-export-format {
        overflow: hidden;
        margin-top: 15px;
    }
    .toggle-formats {
        height: 35px;
        .row {
            display: none;
        }
    }
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border: 1px solid #999999;
        margin: 2px;
        padding: 10px;
        margin-top: 10px;
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
    .design-toggles {
        padding: 25px;
        padding-top: 5px;
        padding-bottom: 0px;

        .switch {
            padding-top: 5px;
            padding-bottom: 5px;
        }
    }
    .text-variable-inputs {
        padding: 25px;
        padding-top: 5px;
        padding-bottom: 5px;
        input {
            width: 100%;
        }
        .form-group {
            margin-bottom: 0px !important;
        }
    }
    .export-format {
        // margin-top: -25px;
        padding: 25px;
        padding-top: 0px;
        padding-bottom: 0px;
        label {
            font-size: inherit !important;
            font-weight: 200;
            padding-left: 25px !important;
            padding-right: 20px;
        }
    }
    .colour-profile-selector,
    .colour-mode-selector {
        margin-left: 10px;
        margin-top: 5px;
        //margin-bottom: 5px;
    }
    .help-subtext {
        font-size: 10px;
        font-style: italic;
    }
    select[name='colour-mode'] {
        margin-left: 7px;
    }
}
.doc-setup-dialog {
    .col-doc-type {
        select {
            float: left;
        }
        .club-name-quickview {
            float: left;
            font-size: 12px;
            font-style: italic;
            padding: 4px;
        }
    }
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border: 1px solid #999999;
        margin: 2px;
        padding: 10px;

        .resolution-label {
            padding-right: 0px;
        }
        .row {
            padding: 2px;
        }
        input[type='text'],
        input[type='number'] {
            padding: 4px;
            width: 100%;
        }
        label {
            font-weight: 400;
        }
        .help-subtext {
            font-size: 10px;
            font-style: italic;
        }
        .slider {
            width: calc(100% - 30px);
            margin-left: 10px;
            margin-top: 15px;
        }
        .slider-wrapper {
            margin-bottom: 40px;
        }
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
}

.designer,
.ics {
    // padding: 0px !important;

    @keyframes blinkingBackground {
        0% {
            background-color: #63bbff;
            color: white !important;
        }
        50% {
            background-color: transparent;
        }
        100% {
            background-color: #63bbff;
            color: white !important;
        }
    }

    #nav-editor-tab.active {
        a,
        a:hover,
        a:active,
        a:focus {
            animation: none;
            background-color: #63bbff !important;
            color: white !important;
        }
    }

    // Hide editor by default...
    #nav-asset-manager-tab {
        margin-right: 40px;
    }

    #nav-editor-tab {
        display: none;

        a {
            padding-bottom: 6px;
            animation: blinkingBackground 2s infinite;
            padding-left: 10px;
            padding-right: 10px;
        }
    }

    .design-exports {
        position: relative;
        .export-filters-toolbar {
            background-color: white;
            z-index: 100;
            top: 20px;
            width: 240px;
            position: absolute;
            right: 10px;
            padding-right: 10px;
            text-align: right;
        }
        .inline-gallery-container {
            width: 100%;
            height: auto;
            min-height: calc(100vh - 200px);
            // position: relative;
            margin-top: -10px;
            margin-left: -20px;
            margin-bottom: -25px;

            .lg-outer {
                max-height: calc(100vh - 180px);
            }

            .lg-container {
                height: calc(100vh - 180px);
                button[id^='lg-actual-size'] {
                    display: none;
                }
            }
            .lg-backdrop {
                background-color: unset;
            }
            .lg-media-overlap .lg-toolbar {
                background-image: none;
            }
            .lg-media-overlap .lg-sub-html {
                background-image: none;
                background: white;
            }
            .lg-sub-html {
                color: black;
            }

            .lg-image {
                border: 1px dotted grey;
            }
            .lg-next,
            .lg-prev {
                background: none;
                border: 2px solid grey;
            }
            .lg-next:hover,
            .lg-prev:hover {
                color: #1f91f3;
            }
            .metadata {
                border-top: 2px dashed lightgrey;
                padding-top: 10px;

                .metadata-buttons {
                    text-align: right;
                    @media (max-width: map-get($breakpoints, 'md')) {
                        text-align: left;
                    }
                }

                .metadata-title,
                .metadata-title h3 {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .metadata-container {
                    float: left;
                    width: 100%;
                    overflow: auto;

                    @media (max-width: map-get($breakpoints, 'lg')) {
                        font-size: 12px;
                    }

                    .icon {
                        float: left;
                        .file-img {
                            height: 40px;
                        }
                    }
                    .data {
                        float: left;
                        padding: 7px;
                        padding-top: 0px;
                    }
                }
            }
            .lg-custom-thumbnails {
                @media (max-width: map-get($breakpoints, 'md')) {
                    .lg-sub-html {
                        padding: 0px;
                    }
                }
                .lg-outer {
                    width: auto;
                    // Set space for the container to occupy thumbnails
                    right: 225px;

                    // Add some spacing on the left to match with the right hand side spacing
                    left: 10px;

                    @media (min-width: map-get($breakpoints, 'md')) {
                        position: relative;
                        display: flex;
                        justify-content: flex-end;

                        .lg-content {
                            width: calc(100% - 250px);
                        }
                    }

                    @media (max-width: map-get($breakpoints, 'md')) {
                        right: -30px;
                        margin-left: 10px;
                        margin-right: 10px;
                    }

                    @media (max-width: map-get($breakpoints, 'lg')) {
                        .lg-object.lg-image {
                            vertical-align: top;
                        }
                    }

                    .lg-thumb-outer {
                        // Set the position of the thumbnails
                        // left: auto;
                        z-index: 999999999;
                        width: 240px;
                        position: sticky;
                        right: 52px;
                        padding-top: 59px;

                        // Reset max height
                        max-height: none;

                        // Customize the layout (Optional)
                        background-color: unset;
                        padding-left: 5px;
                        padding-right: 5px;
                        margin: 0 -10px;
                        overflow-y: auto;

                        // Update transition values
                        // By default thumbnails animates from bottom to top
                        // Change that from right to left.
                        // Also, add a tiny opacity transition to look better
                        transform: translate3d(30%, 0, 0);
                        // opacity: 0;
                        will-change: transform opacity;
                        transition: transform 0.15s cubic-bezier(0, 0, 0.25, 1) 0s, cubic-bezier(0, 0, 0.25, 1) 0.15s;

                        @media (max-width: map-get($breakpoints, 'md')) {
                            display: none;

                            // top: unset;
                            // right: unset;
                            // position: fixed;
                            // bottom: unset;
                            // padding: unset;
                            // height: 150px;
                            // width: calc(100vw - 40px);
                            // left: 0px;
                            // margin-left: -83px
                        }
                    }

                    .lg-thumb-open {
                        .lg-thumb-outer {
                            transform: translate3d(0, 0, 0);
                            opacity: 1;
                        }
                    }

                    // Add hove effect (Optional)
                    .lg-thumb-item {
                        border: 1px solid grey;
                        filter: grayscale(100%);
                        will-change: filter;
                        transition: filter 0.12s ease-in, border-color 0.12s ease;
                        &:hover,
                        &.active {
                            filter: grayscale(0);
                            // border-color: #1f91f3;
                            border: 2px solid #1f91f3;
                        }
                    }

                    .lg-thumb {
                        padding: 5px 0;
                        max-height: calc(100vh - 235px);
                        background-color: white;
                        overflow-y: auto;
                    }
                }
            }
        }
    }

    .designs-list,
    .tags-list,
    .exports-list {
        width: 100%;
        max-width: 900px;
        margin: 0 auto;

        .design-objs {
            position: relative;
            border-radius: 2px;
            margin-top: 0.4em;
            width: 100%;
            background: #fff;
            border: 1px solid #e3e3e3;

            .file-icon {
                align-self: flex-end;
                padding: 5px;

                .file-img {
                    height: 30px;
                }
            }
        }
        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .design-text {
            width: 100%;

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .title-stack {
            font-weight: 600;
            font-size: 1.5em;
        }
        .metadata-container {
            padding-top: 5px;
            .metadata-text {
                font-size: 12px;
                color: #8e8e8e;
                display: flex;
            }
            .material-icons {
                font-size: 12px;
                margin-right: 5px;
                line-height: 1.3;
            }
        }
        .badge {
            margin-right: 5px;
        }
        .badge-club {
            background-color: #6fcc41;
        }
        .badge-global {
            i {
                font-size: 10px;
            }
        }
        .badge-multi-brand {
            background-color: #6fcc41; // #ffb91d;
        }
        .badge-tag {
            background-color: #1f91f3;
        }
        .filter {
            cursor: pointer;
        }
        .export-link,
        .design-link,
        .design-tag {
            color: #555;
            cursor: pointer;
        }
        .export-link:hover,
        .design-link:hover,
        .design-tag:hover {
            text-decoration: none;
            .flex.items-center {
                background-color: #f5f5f5;
            }
        }
        .design-buttons {
            // align-self: flex-end;
            // margin: 10px;
            // min-width: 300px;
            text-align: right;

            position: absolute;
            right: 10px;
            bottom: 10px;
            button {
                margin-top: 5px;
                margin-left: 5px;
                width: auto;
            }
        }

        .group-count-badge {
            position: absolute;
            left: 84px;
            top: 0px;
            background: #2196f3;
            text-align: center;
            color: white;
            padding: 5px 10px;
            font-size: 10px;
            zoom: 0.9;
        }
    }

    .tags-list {
        display: flex;
        flex-wrap: wrap;

        .design-tag {
            flex: 1 0 40%;
            margin: 5px;
            height: 100px;

            min-width: 290px;
            max-width: 440px;

            .title-stack {
                font-size: 1.3em;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .design-text {
                top: -10px;
                position: relative;

                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .tag-buttons {
                left: 105px;
                bottom: 15px;
                text-align: left;
                position: absolute;

                button {
                    margin-top: 5px;
                    margin-left: 5px;
                }
            }
        }
    }
    .designs-list {
        padding: 5px;
        min-height: 200px;

        .all-designs-container {
            width: 100%;
            text-align: right;
            .btn {
                position: relative;
                right: 10px;
                top: 29px;
            }
        }
    }
    .group-title {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid #000;
        line-height: 0.1em;
        margin: 10px 0 20px;

        span {
            background: #fff;
            padding: 0 10px;
        }
    }

    .hidden {
        display: none;
    }

    .tab-content {
        padding: 0px !important;
    }
    #gjs-footer-nav {
        width: 100%;
        background-color: white;
        padding-top: 10px;
        text-align: left;
        padding-right: 75px;
        border-top: 1px solid #e9e9e9;
    }
    .grapejs {
        height: calc(100vh - 200px);
        padding: 0px !important;
        // font-size: 14px;

        .gjs-blocks-c {
            .fa.fa-map-o {
                font-size: 3em !important;
                .gjs-block-label {
                    padding: 10px;
                }
            }
            [title^='Code'] {
                background-color: yellow;
                svg {
                    zoom: 0.5;
                }
            }
        }
        .rul_corner {
            padding-left: 3px;
        }
        .s-ruler-icon {
            margin: 0;
            padding: 4px;
            overflow: hidden;
            height: 25px;
        }
        .s-cropmarks-icon {
            overflow: hidden;
            height: 25px;
        }
        .s-absolute-mode-icon {
            overflow: hidden;
            height: 25px;
        }
        .s-quick-render-icon {
            overflow: hidden;
            height: 25px;
            zoom: 0.9;
        }
        .gjs-one-bg {
            background-color: #fff;
        }
        .gjs-two-color {
            color: #353a40;
        }
        .gjs-four-color {
            color: #2196f3;
        }
        .gjs-four-color-h:hover {
            color: #2196f3;
        }
        .gjs-field,
        .gjs-sm-sector .gjs-sm-field.gjs-sm-composite,
        .gjs-clm-tags .gjs-sm-field.gjs-sm-composite,
        .gjs-sm-sector .gjs-sm-composite.gjs-clm-field,
        .gjs-clm-tags .gjs-sm-composite.gjs-clm-field,
        .gjs-sm-sector .gjs-sm-composite.gjs-clm-select,
        .gjs-clm-tags .gjs-sm-composite.gjs-clm-select {
            background-color: rgba(125, 125, 125, 0.1);
        }
        .gjs-sm-sector .gjs-sm-field input,
        .gjs-clm-tags .gjs-sm-field input,
        .gjs-sm-sector .gjs-clm-field input,
        .gjs-clm-tags .gjs-clm-field input,
        .gjs-sm-sector .gjs-clm-select input,
        .gjs-clm-tags .gjs-clm-select input,
        .gjs-sm-sector .gjs-sm-field select,
        .gjs-clm-tags .gjs-sm-field select,
        .gjs-sm-sector .gjs-clm-field select,
        .gjs-clm-tags .gjs-clm-field select,
        .gjs-sm-sector .gjs-clm-select select,
        .gjs-clm-tags .gjs-clm-select select {
            color: #000;
        }
        .gjs-color-main,
        .gjs-off-prv,
        .gjs-sm-sector .gjs-sm-stack #gjs-sm-add,
        .gjs-clm-tags .gjs-sm-stack #gjs-sm-add {
            color: #343a40;
            fill: #343a40;
        }
        .gjs-field-colorp {
            border-left: 1px solid #343a4059;
        }
        .gjs-field-arrow-u {
            border-bottom: 4px solid rgba(76, 76, 76, 0.7);
        }
        .gjs-field-arrow-d {
            border-top: 4px solid rgba(76, 76, 76, 0.7);
        }
        .gjs-category-title,
        .gjs-sm-sector .gjs-sm-title,
        .gjs-clm-tags .gjs-sm-title,
        .gjs-block-category .gjs-title,
        .gjs-layer-title {
            background-color: rgba(0, 0, 0, 0.04);
        }
        .rul_ruler {
            background-color: #ffffff9e;
        }

        .gjs-pn-views-container,
        .gjs-pn-views {
            width: 300px;
        }
        .gjs-pn-options {
            right: 300px;
            background-color: white;
            z-index: 99;
        }
        .gjs-pn-buttons {
            justify-content: flex-end;
        }
        .rul_line {
            z-index: 999999;
        }
        .gjs-pn-commands {
            box-shadow: unset;
        }
        .gjs-pn-views-container {
            box-shadow: unset;
            //border-left: 2px solid #d2d2d2;
        }
        .gjs-off-prv {
            color: #ffffff !important;
            background-color: #2196f3;
        }

        label {
            font-weight: inherit;
            margin-bottom: inherit;
        }
        .fa,
        .glyphicon {
            font: normal normal normal 14px/1 FontAwesome;
            font-size: inherit;
        }
        .gjs-pn-btn {
            line-height: 21px;
            font-size: 18px;
        }
        .gjs-pn-btn:hover {
            background-color: #efefef;
        }
        .gjs-pn-btn.gjs-pn-active {
            background-color: rgba(206, 206, 206, 0.15);
        }
        .gjs-layer-caret {
            left: -12px;
            top: 3px;
        }
        .gjs-devices-c {
            padding-bottom: 0px;
        }
        .gjs-cv-canvas {
            width: calc(100% - 300px);
            overflow: scroll;
        }
        .gjs-editor-cont ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        .gjs-editor-cont ::-webkit-scrollbar-thumb {
            background-color: rgba(118, 118, 118, 0.4);
        }
        .gjs-editor-cont ::-webkit-scrollbar-track {
            background: rgba(238, 238, 238, 0.5);
        }
        // .panel__additional-actions {
        //     position: initial;
        // }
        .document-display-title {
            cursor: pointer;
            position: absolute;
            top: 0px;
            left: 0px;
            //width: calc(100% - 300px - 360px);
            height: 37px;
            font-size: 14px;
            font-weight: 600;
            padding: 10px;
            z-index: 99;
            padding-left: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .editor-close-container {
            position: absolute;
            z-index: 10;
            right: 30px;
            margin-top: -34px;
            background-color: white;
            width: 31px;
        }
    }
    .tab-pane {
        padding: 20px 0;
    }
    .tab-pane.variables {
        padding: 20px;
        .btn-add-user-variable {
            margin: 10px;
        }
    }

    .tab-pane.assets {
        padding: 20px;

        .assets-details {
            .col-sm-6,
            .col-md-2 {
                padding-left: 5px;
                padding-right: 5px;
            }
        }

        .thumbnail {
            padding: 4px;
            min-height: 390px;
            h4 {
                font-size: 14px;
            }
            .caption {
                ul {
                    margin-left: -10px;
                    min-height: 80px;
                }
            }
            .downloads {
                position: absolute;
                bottom: 30px;
            }
        }

        .colours-details {
            .bg-logo-green {
                background-color: #73b944;
                color: white;
            }
            .bg-highlight-green {
                background-color: #8ac900;
                color: white;
            }
            .bg-dark-grey {
                background-color: #999999;
                color: white;
            }
            .bg-black {
                background-color: #000000;
                color: white;
            }
            .bg-white {
                background-color: #ffffff;
                color: black;
                border: 1px solid black;
            }
            .demo-color-box {
                padding: 15px 0;
                text-align: center;
                margin-bottom: 20px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                -ms-border-radius: 3px;
                border-radius: 3px;
            }
        }

        .fonts-details {
            .gotham-bold {
                font-family: 'Gotham';
                font-weight: 800;
                margin-bottom: -10px;
            }
            .btn {
                margin-bottom: 10px;
                margin-top: 10px;
            }
        }
    }

    .user-variables,
    .global-variables {
        .col-md-4 {
            margin-bottom: 0px;
        }
        .col-md-4.global {
            border: 1px solid #e4e4e4;
            padding-top: 10px;
            margin-left: 10px;
            margin-bottom: 10px;
        }
        .btn.bg-red {
            padding: 2px;
            padding-left: 10px;
            padding-right: 10px;
        }
        textarea {
            resize: vertical;
            min-height: 24px;
        }
    }
    .user-variables {
        .variable .form-control {
            width: calc(100% - 50px) !important;
        }
    }
}
.new-variable-dialog {
    .info-icon {
        font-size: 14px;
        margin-top: -2px;
    }
    text-align: left;
    h4 {
        width: 100%;
        border-bottom: 1px solid #eee;
        padding: 5px;
        margin-bottom: 20px;
    }
    .permissions {
        [class*='col-'] {
            padding: 5px;
            input,
            select {
                width: 100%;
            }
        }
        .col-sm-4 {
            text-align: right;
        }
    }
    .left-padding {
        padding-left: 10px;
    }
    .right-margin {
        margin-right: 20px;
    }
    .form-group {
        margin-bottom: 0px;
    }
    .club-associations {
        width: 100%;
    }
}

.items-center {
  -ms-flex-align: center;
  align-items: center;
}
.flex {
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}
.m-r-3 {
  margin-right: 0.9em;
}

.sk-design-loader,
.sk-search-loader {
  .loader-wrapper {
    flex-wrap: wrap;
    margin: 20px auto;
    justify-content: center;
    width: 900px;
    max-width: 100%;
  }
  .design-tag {
    flex: 1 0 40%;
    margin: 5px;
    height: 100px;
  
    min-width: 290px;
    max-width: 440px;
  
    .title-stack {
        font-size: 1.3em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .design-text {
        top: -10px;
        position: relative;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
    }
    .tag-buttons {
        left: 105px;
        bottom: 15px;
        text-align: left;
        position: absolute;
  
        button {
            margin-top: 5px;
            margin-left: 5px;
        }
    }
  }
  .design-objs {
    position: relative;
    border-radius: 2px;
    margin-top: 0.4em;
    width: 100%;
    background: #fff;
    border: 1px solid #e3e3e3;

    .file-icon {
        align-self: flex-end;
        padding: 5px;

        .file-img {
            height: 30px;
        }
    }
  }
}


.mp-designer {
  .container-fluid {
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
  }
  .designer {
    padding: 0 24px 24px;
  }
  &__tab-wrapper {
    padding-top: 24px;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 8;
    display: flex;
    border-bottom: 1px solid #eee;
    gap: 20px;

    .nav-tabs {
      flex: 1;
      border-bottom: none;
    }
  }

  &__tab-helper {
    flex: 0;
  }
  &__new-design {
    border-radius: 4px;
    border-color: currentColor !important;
    color: map-get($colors-v2, 'blue-alt') !important;
    display: inline-flex;
    background: transparent !important;
    text-transform: none !important;
    padding: 4px 0 4px 8px !important;
    min-height: 0 !important;
    line-height: 20px;
    height: 28px;

    span {
      margin-right: 4px !important
    }
  }

  &__filter-section,
  &__filter-and-sort,
  &__filter-wrapper,
  &__filters-container,
  &__view-selection {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  &__filter-and-sort {
    flex: 1 0;
  }
  &__filter-section {
    padding: 20px 0 16px;
    margin-top: -20px;
    position: sticky;
    top: 58px;
    left: 0;
    z-index: 4;
    background: #fff;

    &__contents {
      background: map-get($colors-v2, 'container');
      border-radius: 6px;
      padding: 4px 12px;
      display: inherit;
      gap: inherit;
      align-items: inherit;
      justify-content: inherit;
      width: 100%;
    }
  }
  &__filter-label {
    color: map-get($colors-v2, 'secondary');
    font-size: 12px;
    font-weight: 600;
    line-height: 1.5;
    text-transform: uppercase;
  }
  &__sort-by {
    border-radius: 24px;
    background: transparent;
    border: 1px dashed rgba(48, 49, 61, 0.2);
    height: 24px;
    line-height: 24px;
    display: inline-flex;
    align-items: center;
    padding: 0 8px;
    color: map-get($colors-v2, 'secondary');
    transition: color .2s;
    gap: 6px;
    font-weight: 600;
    font-size: 12px;

    &--active {
      color: map-get($colors-v2, 'blue-alt');
    }

    &__icon {
      width: 16px;
      height: 16px;

      &:empty {
        display: none;
      }

      svg {
        width: inherit;
        height: inherit;
      }
    }
  }

  &__search {
    position: relative;
    width: 24px;
    height: 24px;

    &-field {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        height: 24px;
        border: 0;
        border-bottom: 1px solid transparent;
        transition: 0.3s linear;
        width: 24px;
        padding-left: 24px;

        &::-webkit-search-cancel-button {
          -webkit-appearance: none;
          cursor: pointer;
          height: 10px;
          width: 10px;
          margin-right: 30px;
  
          background-image: url('data:image/svg+xml;utf8,
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>
          ');
        }

        &.filled,
        &:focus {
            border-color: map-get($colors-v2, 'border-dark');
            padding-left: 8px;
            width: 160px;
        }

        &.filled {
          & + .support-page__search-field__clear {
            display: inline-flex;
          }
        }

        &__clear {
          position: absolute;
          top: 50%;
          right: 24px;
          border:0;
          background: none;
          transform: translateY(-50%);
          height: auto;
          display: none;

          .material-icons {
            font-size: 16px;
          }
          
        }
    }

    svg {
        position: absolute;
        right: 0;
        top: 0;
        pointer-events: none;
        background: #fff;
        color: map-get($colors-v2, 'secondary');
    }
  }

  &__view__option {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 28px;
    border: 1px solid map-get($colors-v2, 'border') !important;
    transition: .2s;
    color: map-get($colors-v2, 'secondary');
    background: transparent !important;

    &--active {
      border-color: map-get($colors-v2, 'blue-alt') !important;
      color: map-get($colors-v2, 'blue-alt');
    }

    svg {
      margin: 0 !important;
    }
  }

  &__items {
    &__thumb-view {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 20px;
      align-items: start;
      justify-items: stretch;

      &--no-grid {
        grid-template-columns: 1fr;
      }
    }
  }

  &__divider-text {
    grid-column: 1/-1;
    padding-bottom: 4px;
    background: #fff;
    position: sticky;
    top: 146px;
    left: 0;
    z-index: 3;

    .view-all-designs {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
    }

    .view-all-designs:not(.btn-link):not(.btn-circle).btn-default {
      height: 32px;
      border: none;
      padding-left: 0;
      padding-right: 16px;
      font-size: 12px;

      &:hover {
        background-color: transparent;
      }
    }
  }

  &__thumb {
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    flex-shrink: 0;
    background: transparent;
    transition: .2s;
    cursor: pointer;

    &:last-child {
      margin-bottom: 48px;
    }

    &:hover {
      box-shadow: 0px 2px 5px 0px rgba(60, 66, 87, 0.12), 0px 1px 1px 0px rgba(0, 0, 0, 0.08);  
      background: map-get($colors-v2, 'container');
    }

    &__count {
      min-width: 32px;
      min-height: 32px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      position: absolute !important;
      z-index: 2;
      right: 22px;
      top: 22px;
      background: map-get($colors-v2, 'blue-alt');
      font-size: 14px;
      font-weight: 600;
      line-height: 1.25
    }

    &__images {
      min-height: 200px;
      overflow: hidden;
      border-radius: 4px;
      background: map-get($colors-v2, 'border');
      width: 100%;
      position: relative;
      z-index: 1;

      svg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: .5;
      }

      &--single {
        .mp-designer__thumb__img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          margin: 0;
          top: 0;
          left: 0;
          transform: translateX(0);

          img {
            border: none;
          }
        }
      }
    }

    $base-top: 38;
    $diff: 12;
    $topMod: 10;
    $baseOpacity: 0;

    &__img {
      width: 200px;
      aspect-ratio: 200/185;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);
      margin: -38px 0 0;
      border-radius: 4px;
      background: #fff url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22400%22%20height%3D%22400%22%20fill-opacity%3D%22.10%22%20%3E%3Crect%20x%3D%22200%22%20width%3D%22200%22%20height%3D%22200%22%20%2F%3E%3Crect%20y%3D%22200%22%20width%3D%22200%22%20height%3D%22200%22%20%2F%3E%3C%2Fsvg%3E") repeat;
      background-size: 10px;
      z-index: 10;
      overflow: hidden;
      transform-origin: top center;
      transition: 1s;
      
      &::after {
        content: '';
        display: block;
        top: 0;
        left: 0;
        position: absolute;
        width: 100%;
        height: 100%;
        background: map-get($colors-v2, 'border');
        opacity: 0;
        border-radius: inherit;
      }

      img {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        object-fit: contain;
        border: 4px solid #FFF;
        box-shadow: 0px 6.479px 18.512px -0.926px rgba(0, 0, 0, 0.15), 0px -1.851px 7.405px 0px rgba(0, 0, 0, 0.25) inset;
        border-radius: inherit;
      }

      &:has(> .img-not-found) {
        background: #EBEEF1 url('../../../assets/images/carbon_no-image.png') no-repeat center center;
        background-size: 100px;
        border: 4px solid #FFF;
        box-shadow: 0px 6.479px 18.512px -0.926px rgba(0, 0, 0, 0.15), 0px -1.851px 7.405px 0px rgba(0, 0, 0, 0.25) inset;

        img {
          display: none;
        }
      }

      &:has(> .img-loading) {
        background-image: none;

        &::after {
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: #B4BFCA;
          animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
          border: 4px solid #FFF;
          box-shadow: 0px 6.479px 18.512px -0.926px rgba(0, 0, 0, 0.15), 0px -1.851px 7.405px 0px rgba(0, 0, 0, 0.25) inset;
          border-radius: inherit;
        }

        img {
          opacity: 0;
        }
      }

      @for $i from 1 through 12 {
        &:nth-child(#{$i + 1}) {
          width: calc(200px - #{$diff * $i}px);
          margin-top: -#{$base-top + $topMod * $i}px;
          z-index: $topMod - $i;
  
          &::after {
            opacity: ($i * 15) / 100;
          }
        }
      }
      &--sm {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        background: #EBEEF1;
        flex-shrink: 0;
        margin-right: -30px;
        display: flex;
        align-items: center;
        justify-content: center;

        .material-icons {
          opacity: .75;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
          border-radius: inherit;
          border: 1px solid #FFF;
          box-shadow: 0px 6.479px 18.512px -0.926px rgba(0, 0, 0, 0.15), 0px -1.851px 7.405px 0px rgba(0, 0, 0, 0.25) inset;
          transition: opacity .25s;
          opacity: 1;
        }

        &:has(> .img-not-found) {
          background: #EBEEF1 url('../../../assets/images/carbon_no-image.png') no-repeat center center;
          background-size: 24px;
          border: 1px solid #FFF;

          img {
            display: none;
          }
        }

        &:has(> .img-loading) {
          position: relative;
          
          &::after {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #B4BFCA;
            animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
            border: 1px solid #FFF;
            box-shadow: 0px 6.479px 18.512px -0.926px rgba(0, 0, 0, 0.15), 0px -1.851px 7.405px 0px rgba(0, 0, 0, 0.25) inset;
            border-radius: inherit;
          }
          img {
            opacity: 0;
          }
        }

        @for $i from 1 through 5 {
          &:nth-child(#{$i}) {
            z-index: 6 - $i;
            order: 6 - $i;

            img {
              // from 1 to .1
              opacity: 1 - (.2 * ($i - 1));
            }
          }
        }
      }
    }
    
    &__details {
      display: flex;
      flex-direction: column;
      gap: 4px;

      &__title {
        overflow: hidden;
        color: map-get($colors-v2, 'primary');
        text-overflow: ellipsis;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.5; 
        margin: 0;
      }

      &__meta {
        display: flex;
        align-items: flex-start;
        overflow: hidden;
        line-height: 16px;
        color: map-get($colors-v2, 'secondary');
        gap: 4px;

        &--highlight {
          color: map-get($colors-v2, 'blue-alt');
          font-weight: 600;
        }

        &-icon {
          width: 16px;
          height: 16px;
        }

        &-text {
          font-size: 12px;
          line-height: 1.25; /* 15px */
        }
      }

      &__tags {
        display: flex;
        align-items: flex-start;
        gap: 4px;
      }

      &__tag {
        display: flex;
        height: 18px;
        padding: 1px 6px;
        align-items: center;
        gap: 2px;
        border-radius: 4px;
        border: 1px solid map-get($colors-v2, 'border');
        color: map-get($colors-v2, 'secondary');
        font-size: 11px;
        font-weight: 600;
        line-height: 16px;
        transition: .2s;

        &:hover {
          border-color: map-get($colors-v2, 'secondary' )
        }
      }
    }

    &--sm {
      display: inline-flex;
      align-items: flex-start;
    }

    $base-width: 230;

    &.showing-active {

      .mp-designer {
        &__thumb {
          &__img {
            &:nth-child(1):not(.mp-designer__thumb__img--active) {
              transform: translateX(-50%) translateY(150%); 
            }
            &--active {
              margin-top: -#{$base-top}px;
              width: #{$base-width}px;
              z-index: 999;
      
              &::after {
                opacity: 0;
              }
            }
            @for $the_rest from 2 through 12 {
              &:nth-child(#{$the_rest}):not(.mp-designer__thumb__img--active) {
                @if $the_rest == 2 {
                  width: calc(#{$base-width}px - #{$diff * ($the_rest - 1)}px);
                  margin-top: -#{$base-top + $topMod * ($the_rest - 1)}px;
                }

                @if $the_rest > 2 {
                  width: calc(#{$base-width}px - #{$diff * ($the_rest - 2)}px);
                  margin-top: -#{$base-top + $topMod * ($the_rest - 2)}px;
                }

                &::after {
                  opacity: (($the_rest - 2) * 15) / 100;
                }
              }
            }
          }
        }
      }
      
      // @for $i from 3 through 10 {
      //   .mp-designer {
      //     &__thumb {
      //       &__img {
      //         $startCount: $i + 1;
              

      //       }
      //     }
      //   }
      // }
    }
    
  }

  .isLoading {
    .mp-designer {
      &__thumb {
        &__count {
          background: transparent;
          opacity: .1;

          &::after {
            background: map-get($colors-v2, 'border-dark')
          }
        }
      }
    }
  }

  .mp-designer__items__thumb-view {
    &:has(> .divider-text) {
      table.dataTable {
        thead {
          top: 178px;
        }
      }
    }

    table.dataTable {
      thead {
        position: sticky;
        background: #fff;
        z-index: 10;
        top: 134px;
      }
    }

    .dataTables_paginate {
      position: sticky;
      bottom: 0;
      z-index: 4;
      background: #fff;
    }
  }

  table.dataTable {
    position: relative;
    z-index: 1;
    thead {

      th {
        padding-left: 6px;
        padding-right: 6px;

        &.sorting {
          &:after {
            display: none;
          }
        }

        &:first-child {
            padding-left: 16px;
            pointer-events: none;
            max-width: 160px;

            .dataTable__header {
              display: none;
            }
        }

        &:last-child {
            padding-right: 16px;
        }

        .dataTable {
            &__header {
                display: flex;
                white-space: nowrap;
                align-items: center;
                font-size: 11px;
                text-transform: uppercase;
                line-height: 20px;
                font-weight: 600;
                color: #30313d;

                &--justify-end {
                    justify-content: flex-end;
                }

                span:first-child {
                    padding-right: 4px;
                }

                &__icon {
                    width: 18px;
                    height: 18px;
                    background: url(./../../../assets/images/arrow-down-icon.svg) no-repeat center center;
                    background-size: cover;
                    opacity: 0.2;
                }
            }
        }

        &.sorting {
            &_asc,
            &_desc {
                .dataTable__header__icon {
                    opacity: 1;
                }
            }

            &_desc {
                .dataTable__header__icon {
                    transform: rotate(180deg);
                }
            }
        }

        .sorting &:after {
            display: none;
        }
      }
    }

    tbody {
        td {
            padding: 18px 6px;
            border-top: none;
            vertical-align: middle;

            &:first-child {
                padding-left: 16px !important;
                max-width: 160px;
            }

            &:last-child {
                padding-right: 16px !important;
            }
        }

        tr {
            cursor: pointer;

            &.device {
                &.d-status-false {
                    background-color: rgba(0, 0, 0, 0.02);
                }
                &.alarm-triggered {
                    animation: bgPulse 1s linear 0s infinite alternate-reverse;
                }
            }
        }
    }
  }

  .mp-designer {
    @media (max-width: 1400px) {
      &__filter-section {
        &__contents {
          flex-direction: column;
          padding: 12px;
          gap: 12px;
        }
      }
      &__divider-text {
        top: 198px;
      }
      &__items__thumb-view table.dataTable thead {
        top: 184px;
      }
    }
    @media (max-width: 767px) {
      &__filter-and-sort {
        flex-direction: column;
      }
    }
    @media (max-width: 400px) {
      &__filter-and-sort {
        align-items: stretch;
      }
      &__filter-wrapper {
        flex-direction: column;
        align-items: flex-start;
  
        &--sort {
          display: none;
        }
      }
      &__filter-section {
        &__contents {
          align-items: stretch;
        }
      }
    }
    @media (max-width: 600px) {
      &__divider-text::after {
        display: none;
      }
    }
  }

  .hidden-xxs {
    @media (max-width: 600px) {
      display: none !important;
    }
  }

  .search-bar__submit {
    position: absolute;
    top: 16px;
    right: 0;
    border: none;
    background: transparent;
  }

  &__filters--club {
    .filter-grp:nth-child(2) {
      display: none;
    }
  }
}

.smallSidebarContent {
  .mp-designer {
    &__filter-section {
      &__contents {
        @media (max-width: 1400px)  {
          flex-direction: row;
          padding: 4px 12px;
          gap: inherit;
        }
        @media (max-width: 1100px) {
          flex-direction: column;
          padding: 12px; 
          gap: 12px;
        }
      }
    }
    &__items__thumb-view {
      table.dataTable {
        thead {
          @media (max-width: 1100px) {
            top: 184px;
          }
        }
      }
    }  
  }
  
}

.divider-text {
  display: flex;
  align-items: center;
  gap: 16px;
  color: map-get($colors-v2, 'primary');
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  text-transform: uppercase;
  height: 36px;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: map-get($colors-v2, 'border');
  }
}

.preview-render-modal .loader {
  .magnify.use-skeleton {
    aspect-ratio: 2 / 3;
    height: 500px;
    max-height: 100%;
  }
  h3 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    opacity: 0;
    animation: fadeIn .2s forwards 5s;
  }
}

.designer-custom-page-content {
    iframe, embed {
        width: 100%;
        height: calc(100vh - 200px) !important;
        border: none;
    }
}