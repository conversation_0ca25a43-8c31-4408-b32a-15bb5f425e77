#facility-variables-page {
    .variable-item {
        box-shadow: $input-shadow;
        overflow: hidden;
        border-radius: 6px;
        height: min-content;

        .ph-badge {
            background-color: $accent-blue-100;
            border: 1px solid $accent-blue-200;
            color: $accent-blue-500;
        }

        code {
            background-color: transparent;
            color: $accent-blue-500;
            font-weight: 600;
        }

        .copy-clipboard {
            color: $accent-blue-500;
        }

        .form-line {
            display: flex;
        }

        textarea {
            border-radius: 0px !important;
            border: none;
            padding-left: 1rem;
            color: $slate-600;
        }

        small {
            color: $slate-450;
            font-size: 10px;
            font-weight: 500;
        }

        .lever {
            margin: 0 0.75rem;
        }

        .delete-btn {
            padding: 4px 1rem;

            i.material-icons {
                margin: 0;
                font-size: 14px;
            }
        }
    }
    .preset-variables-card {
        .list-group-item {
            border-left: 0;
            border-right: 0;

            &:first-child {
                border-top: 0;
            }
            &:last-child {
                border-bottom: 0;
            }
        }
    }

    // style modification
    flex-wrap: wrap;

    .cv-variables {
        width: 100%;

        &.isLoading {
            .card {
                display: block !important;
            }
            .list-group-item {
                border: 0 !important;
            }
        }
    }

    .variables-card {
        &.isLoading {
            .variable-item_body {
                height: 100%;
                width: 100%;
            }
        }
    }

    .variable-item_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: $slate-75;
        padding: 4px;
        border: 1px solid $border-color;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    .variable-item_body {
        border-radius: 6px;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        overflow: hidden;
        border: 1px solid $border-color;
        border-top: none;

        .__static-value {
            line-height: 1.4em;
            padding: 6px 12px;
            background-color: #f5f5f5;
            color: $slate-600;
            opacity: 0.65;
        }
    }

    .preset-variables-list {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        padding: 12px;
        column-gap: 1rem;
        row-gap: 1rem;

        @include respond-below('sm') {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }
    }

    .variables-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .variables-list-wrapper {
        display: flex;
        gap: 1rem;

        @include respond-below('sm') {
            flex-direction: column;
        }
    }

    .variables-empty {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        color: $slate-400;
        font-size: 14px;
        font-weight: 500;
        font-style: italic;
    }
}
