.website-management {
    padding-bottom: 10rem;

    .arial {
        font-family: Arial, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell,
            Helvetica Neue, sans-serif;
    }

    .table-loading-overlay {
        width: 100%;
        text-align: center;
    }
    .save-overlay {
        display: none;
        width: 100vw;
        height: 100vh;
        top: 0px;
        left: 0px;
        background-color: rgba(100, 100, 100, 0.3);
        position: fixed;
        z-index: 9999;

        .spinner-container {
            position: fixed;
            opacity: 1;
            width: 500px;
            padding: 20px;
            height: 200px;
            background-color: white;
            text-align: center;
            margin-top: 200px;
            left: calc(50% - 250px);
            .preloader {
                margin-top: 10px;
            }
        }
    }

    .tab-content {
        // btns
        .btn {
            span {
                top: 0;
                &.caret,
                &.glyphicon {
                    display: block;
                    top: -2px;
                    margin: 0 4px;
                }
            }

            &-cancel {
                color: red;
                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
            }
        }

        .input-group-btn {
            &:first-child {
                .btn {
                    border-radius: 4px 0 0 4px;
                }
            }
        }

        // input-group
        .input-group {
            border: 1px solid #ccc;
            padding: 0;
            display: flex;
            clear: both;

            .input-group-addon,
            .input-group-btn,
            .form-control {
                float: none;
                width: auto;
                display: block;
            }

            .input-group-btn {
                display: flex;
                flex-grow: 0;
            }

            .form-control {
                width: auto;
                min-width: 130px;
                flex-grow: 1;
                height: 32px;
                padding: 0 12px;
                border-left: 1px solid #ccc;
            }
        }

        .dropdown-menu {
            margin-top: 4px !important;
            padding: 4px;
            border-radius: 0;
            border: 1px solid #ccc;

            li:not(:last-child) {
                border-bottom: 1px solid #ddd;
            }

            .faq-category {
                width: 100%;
                border-radius: 0;
                border: 0;
                background: none;
                padding: 8px;
                text-align: left;
            }
        }
    }

    // faq item
    .faq {
        &-items {
            display: grid;
            gap: 20px;

            @media screen and (min-width: 991px) {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }

            @media screen and (min-width: 1100px) {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }
        &-item {
            border: 2px solid rgba(203, 213, 225, 1);
            padding: 80px 20px 30px;
            display: grid;
            gap: 15px;
            transition: 0.5s;
            place-content: start;
            position: relative;
            cursor: pointer;

            &:hover {
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            }

            p {
                margin: 0;
                font: inherit;
            }
        }
        &-question {
            font-size: 16px;
            color: #000;
        }
        &-answer {
            display: grid;
            gap: 12px;
        }
        &-categories {
            span {
                display: inline-block;
                border: 1px solid #ddd;
                padding: 2px 10px;
                border-radius: 10px;
                opacity: 0.8;
                margin: 0 4px 4px 0;
            }
        }
        &-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 20px;
        }
        &-delete {
            width: 27px;
            height: 27px;
            padding: 0;
            opacity: 0.5;
            transition: 0.3s;
            font-size: 14px;

            &:hover {
                opacity: 1;
            }
        }
    }

    .faqs-filter-summary {
        font-size: 16px;
        color: #000;

        &::after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background: #1f91f3;
            margin: 15px 0 20px;
        }
    }

    // form
    .fld {
        padding: 15px 0;
        position: relative;

        &-label {
            font-size: 16px;
            margin: 0 0 10px;
            display: block;
        }

        &-question {
            .trumbowyg-box,
            .trumbowyg-editor {
                min-height: 20px;
            }
        }

        &-input {
            width: 100%;
            padding: 10px 16px;
            border: 1px solid #ddd;
            border-radius: 0;
            transition: 0.5s;
            outline: 0;
            position: relative;

            &:focus {
                border-color: rgb(79, 103, 133);
            }
        }

        &-drop {
            small {
                font-size: 13px;
                display: block;
            }

            .drop-area {
                border-radius: 0;
                border: 2px dashed #ddd;
                text-align: center;
                margin: 0 0 12px;

                p {
                    text-align: inherit;
                    font-weight: 700;
                }
            }
        }
    }

    .form {
        &-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 24px;

            &::after {
                content: '';
                display: block;
                width: 80px;
                height: 4px;
                background: #1f91f3;
                margin: 15px 0 20px;
            }
        }

        &-wrap {
            padding: 30px;
            border-radius: 6px;
            border: 2px solid rgba(203, 213, 225, 1);
            background: #fff;
            position: relative;
        }

        &-btn-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px 0 0;

            .btn {
                text-transform: uppercase;
                padding: 6px 16px;
                font-size: 16px;
            }
        }
    }

    // faq add form
    .faq {
        &-category {
            &-option {
                display: inline-block;
                margin: 0 40px 10px 0;
            }
            &-label {
                font-size: 14px !important;
            }
        }
        &-selectpicker {
            .bootstrap-select {
                border: none !important;
            }

            .dropdown-toggle {
                height: 50px;
                border: 1px solid #ddd;
            }

            .filter {
                &-option {
                    display: flex;
                    flex-grow: 1;
                    align-items: center;
                    font-size: 14px;
                }
            }
        }

        &-status {
            display: flex;

            & > strong {
                margin-right: 10px;
            }

            & > span {
                text-transform: uppercase;
                color: #337ab7;
                font-weight: 700;
            }

            &-label {
                font-size: 14px !important;
            }

            &-option {
                margin-right: 16px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        &-location-val {
            span {
                color: #337ab7;
                font-style: italic;
            }
        }
    }

    // press section
    .press {
        &-grid {
            display: grid;
            gap: 20px;
        }

        &-display {
            place-items: end;
            width: 100%;
        }

        &-items {
            width: 100%;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        &-item {
            padding: 50px 16px 16px;
            border-radius: 0;
            border: 2px solid rgba(203, 213, 225, 1);
            background: #fff;
            position: relative;
            transition: 0.5s;

            &:hover {
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            }

            p,
            h6 {
                font-family: Arial, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell,
                    Helvetica Neue, sans-serif;
            }

            &-actions {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                padding: 16px 16px 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
                opacity: 0.6;
                transition: 0.5s;

                &:hover {
                    opacity: 1;
                }
            }
        }

        &-details {
            display: block;
            text-decoration: none;
            font-size: 16px;
            color: rgba(71, 85, 105, 1);

            &:hover {
                .press-thumb {
                    opacity: 1;
                }
            }

            p {
                margin: 0 0 5px;
                opacity: 0.7;
            }
        }

        &-thumb {
            opacity: 0.6;
            transition: 0.7s;
            img {
                width: 100%;
                height: auto;
            }
        }

        &-title {
            font: inherit;

            font-weight: 700;
            margin: 10px 0 5px;
        }

        &-date,
        &-publisher {
            font: inherit;
            font-size: 12px;
        }

        &-publisher {
            text-transform: uppercase;
            font-weight: 700;
        }

        &-date {
            padding: 0 0 6px;
            margin: 0 0 8px;
            border-bottom: 1px solid rgba(203, 213, 225, 1);
        }

        &-quote {
            font-size: 14px;
        }

        &-add {
            text-transform: uppercase;
            font-weight: 700;
            padding: 10px;
        }

        &-form {
            display: grid;

            .fld {
                display: grid;
                gap: 5px;

                &-label {
                    margin: 0;
                }

                @media screen and (min-width: 700px) {
                    gap: 20px;
                    grid-template-columns: minmax(200px, 1fr) 3fr;
                }
            }
        }

        &-selectpicker {
            .bootstrap-select {
                border: none !important;
            }

            .dropdown-toggle {
                height: 50px;
                border: 1px solid #ddd;
            }

            .filter {
                &-option {
                    display: flex;
                    flex-grow: 1;
                    align-items: center;
                    font-size: 14px;
                }
            }
        }

        &-status {
            display: flex;

            & > strong {
                margin-right: 10px;
            }

            & > span {
                text-transform: uppercase;
                color: #337ab7;
                font-weight: 700;
            }

            &-label {
                font-size: 14px !important;
            }

            &-option {
                margin-right: 16px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        &-location-val {
            span {
                color: #337ab7;
                font-style: italic;
            }
        }
    }

    .status-location {
        border-top: 1px solid #cbd5e1;
        margin-top: 16px;
        padding: 20px 0 0;
    }

    // mappings
    .mappings {
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));

        &-item {
            padding: 70px 16px 16px;
            border: 2px solid rgba(203, 213, 225, 1);
            background: #fff;
            position: relative;
            transition: 0.5s;

            &:hover {
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            }
        }

        &-detail {
            strong {
                text-transform: uppercase;
                font-weight: 700;
                opacity: 0.4;
            }
            p {
                font-size: 18px;
            }
        }

        &-actions {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 16px 16px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            opacity: 0.6;
            transition: 0.5s;

            &:hover {
                opacity: 1;
            }
        }

        &-add {
            display: flex;
            justify-content: flex-end;
            margin: 0 0 20px;

            .btn {
                font-weight: 700;
                text-transform: uppercase;
            }
        }

        &-fields {
            display: grid;
            gap: 20px;
            margin: 0 0 20px;
        }
    }

    .files-toolbar {
        border-bottom: 2px dashed #eeeeee;
        padding-top: 0;
        margin-bottom: 30px;
        padding-bottom: 10px;

        [class*='col-'] {
            margin-bottom: 0px;
        }

        .admin-designs-wrapper,
        .new-design-wrapper {
            float: right;
            padding: 5px 0 0;
            margin: 0;
        }

        .admin-designs-wrapper {
            padding: 10px 30px 5px 5px;

            label {
                font-size: 12px;
            }
        }

        .search-bar-wrapper {
            .input-group {
                border: 0 !important;
                .material-icons {
                    margin-left: 10px;
                    position: absolute;
                    z-index: 10;
                    margin-top: -13px;
                }
                input {
                    padding-left: 30px;
                    border: 0;
                }

                // width: calc(100% - 200px);
                max-width: 500px;
                float: left;
                margin-bottom: 0px;
            }
        }
        .search-bar {
            font-size: 14px;
        }

        .input-group-addon {
            position: static;
            padding: 0;
        }

        input[type='search']::-webkit-search-cancel-button {
            -webkit-appearance: none;
            cursor: pointer;
            height: 12px;
            width: 12px;
            opacity: 0.75;
            margin-right: 8px;

            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>');
        }

        .form-control {
            width: 100% !important;
        }

        .btn {
            span {
                display: block;
            }
        }
    }

    .no-search-results {
        padding: 32px;
        border: 1px solid rgba(255, 0, 0, 0.3);
        margin: 0 0 32px;
    }

    &-tabs {
        margin-bottom: 30px;
    }

    // lp
    .dataTables_wrapper {
      max-width: 100%;
      overflow-x: auto;

      .btn.lp-item__action {
        padding: 2px 12px !important;
        height: 24px !important;
        border: 1px solid map-get($colors-v2, 'secondary' ) !important;
        color: map-get($colors-v2, 'secondary' ) !important;

        &.lp-item__delete {
          padding-right: 8px !important;
          padding-left: 8px !important;
        }

        i {
          margin-right: 0;
          font-size: 12px;
        }

        &:hover, &:focus {
          border: 1px solid map-get($colors-v2, 'blue-alt' ) !important;
          color: map-get($colors-v2, 'blue-alt') !important;
        }
      }

      table.table thead th.sorting:after, table.table thead th.sorting_asc:after, table.table thead th.sorting_desc:after {
        display: none !important;
      }

      .justify-end {
        justify-content: flex-end;
      }

      .items-start {
        align-items: flex-start;
      }
      
      .lp-hide-inactive {
        .lp-tr-inactive {
          display: none !important;
        }
      }
    } 

    .isLoading {
      .use-skeleton {
        pointer-events: none !important;
      }

      .btn.lp-item__action {
        &.use-skeleton {
          border: none !important
        }
      }
    }
}

#club-lp tbody tr {
    cursor: pointer;
}

// lp modal
.club-lp__modal {
  .ph-dialog-v2 {
    padding-top: 20px;

    .form-control {
      padding-left: 0;
    }

    .error {
      border: 1px solid $red;
    }
  }

  .drop-area {
      border-radius: 0;
      border: 2px dashed #ddd;
      text-align: center;
      margin: 0 0 12px;
      background-size: cover;

      p {
          text-align: inherit;
          font-weight: 700;
      }
  }

  .club-lp-droparea {
    padding-bottom: 20px;
    .drop-area {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .dz-message {
      font-size: 12px;
    }
  }

  .lp-video-delete {
    cursor: pointer;
    flex-shrink: 0;
    color: map-get($colors-v2, 'red-alt');
  }
}
