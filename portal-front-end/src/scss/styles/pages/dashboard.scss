.ph-welcome-text {
    z-index: 9999;
    font-size: 100px;
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 50px;
}

// Custom hover labels/tooltips for our revenue charts...
.loadingoverlay_element {
    width: 50px !important;
    height: 50px !important;
}

application {
    .noclubs {
        .content {
            margin-left: 20px;
        }
        .no-clubs-nav-options {
            padding: 10px;
            h3 {
                margin-top: 10px;
                margin-left: 10px;
            }
            .btn {
                margin: 10px;
            }
        }
    }
}

.dashboard {
    background: #fff;

    &.content {
        padding: 0;
        margin: 70px 0 0 300px;

        &.smallSidebarContent {
            margin: 70px 0 0 70px !important;
        }

        .container-fluid {
            padding: 32px;
            background: #fff;
            min-height: calc(100vh - 70px);

            @media (max-width: 991px) {
                padding: 32px 16px;
            }
        }
    }

    .dashboard-alerts-banner {
        @media (max-width: map-get($breakpoints, 'sm')) {
            padding-left: 5px;
            padding-right: 5px;
        }

        &:empty + .missing-club-kyc-config-alert-container {
            display: block;
        }

        &:not(:empty) + .missing-club-kyc-config-alert-container {
            display: none;
        }
    }

    .header {
        color: #555;
        padding: 15px !important;
        padding-bottom: 10px !important;
        position: relative;
        border-bottom: 3px solid #f8fafb;

        h2 {
            color: #555;
            font-size: 15px;
            font-weight: bold;

            .reporting-period {
                color: #868b93;
            }
        }
        .btn {
            position: relative;
            float: right;
            margin-top: -5px;
        }
    }

    #member_split_legend {
        position: absolute;
        font-size: 12px;
    }
    #currentMembers {
        float: left;
    }
    .sub-content {
        margin-top: -8px;
        font-size: 11px;
        color: #868b93;
        margin-left: 0;
        text-transform: capitalize;
        line-height: 1.2s;
    }
    .container-fluid > .row > [class*='col-'] {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }
    .info-box-4 {
        margin-bottom: 15px;
        width: 100%;
    }
    .single-stat {
        max-width: 300px;
        @media only screen and (max-width: 584px) {
            max-width: 100%;
        }
    }

    // ------------------------------------------------------------------------

    // Default "display none" components
    .fb-here-count,
    .fb-talking-count,
    .container-no-gm-api {
        display: none;
    }
    .graph {
        height: 250px;
    }
    .tcBarGraph {
        height: 100px;
    }
    .fixedrow {
        height: 350px;
        display: flex;
        flex-direction: column;

        .body {
            position: relative;
            flex-grow: 1;
        }

        #revenue_chart {
            top: 20px;
            right: 20px;
            bottom: 20px;
            left: 20px;
            position: absolute;
        }

        #member_split_chart {
            top: 30px;
            right: 20px;
            bottom: 30px;
            left: 20px;
            position: absolute;
        }
    }
    .info-icon {
        font-size: 14px;
        margin-top: -2px;
        // position: absolute;
        // margin-left: 2px;
    }
    .payout-period {
        text-transform: capitalize;
    }

    .dashboard-flot-chart {
        height: 275px;
    }

    .dashboard-donut-chart {
        height: 265px;
    }

    .dashboard-line-chart {
        height: 250px;
    }

    .dashboard-stat-list {
        list-style: none;
        padding-left: 0;

        &__title {
            font-size: 11px;
            line-height: 1.8;
            margin: 0 0 4px;
            display: block;
            font-weight: 600;
            text-transform: uppercase;
            color: map-get($colors-v2, 'secondary');
        }

        &__values {
            font-size: 16px;
            line-height: 1;
            font-weight: 600;
            display: flex;
            flex-wrap: wrap;
            color: map-get($colors-v2, 'primary');

            b {
                font: inherit;
            }

            & > span {
                padding: 4px 12px;
                border-left: 1px solid map-get($colors-v2, 'border');
                margin-bottom: 4px;

                &:first-child {
                    padding-left: 0;
                    border-left: 0;
                }
            }
        }

        li {
            padding: 0;
            margin-bottom: 16px;

            small {
                font-size: 8px;
            }
        }
    }
    .dashboard-task-infos {
        .progress {
            height: 10px;
            margin-bottom: 0;
            position: relative;
            top: 6px;
        }
    }
    .todo {
        .body {
            padding-top: 0px;
        }
        .header {
            border-bottom: none;
        }
    }
    .upcoming-bookings-widget {
        .body {
            & > ul > li {
                display: flex;
                justify-content: space-between;
            }

            #upcoming-bookings-chart {
                height: 150px;
                margin-top: -30px;

                svg {
                    width: 100%;
                }
            }

            #todays-bookings {
                height: 10rem;
                overflow: auto;
                border: 1px solid #ebeef1;

                li.list-group-item {
                    border-left: 0;
                    border-right: 0;
                    border-color: #ebeef1;
                    font-size: 11px;
                    line-height: 16px;
                    color: map-get($colors-v2, 'primary');

                    &--day {
                        line-height: 20px;
                        color: #6a7383;
                        font-weight: 600;
                        text-transform: uppercase;
                        position: sticky;
                        top: 0;
                        left: 0;
                        z-index: 2;
                    }

                    &:first-child {
                        border-top: 0;
                    }
                    &:last-child {
                        border-bottom: 0;
                    }
                    &.row-span-3 {
                        min-height: 3em;
                    }
                }
            }
        }
        &-chart {
            height: 118px;
            margin-bottom: 32px;
            max-width: 100%;
        }
        canvas {
            cursor: pointer;
        }
        .bookings-nav {
            flex: 0;
            width: 32px;
            display: flex;
            align-items: center;
            border-radius: 0;
            background: #fff;
            border: 1px dashed #ebeef1;
            outline: 0;
            color: map-get($colors-v2, 'secondary');

            &:first-child {
                border-right-width: 0;
            }

            &:last-child {
                border-left-width: 0;
            }

            &:disabled {
                i {
                    opacity: 0.2;
                }
            }

            &-section {
                display: flex;
                align-items: stretch;
                margin: 0 0 10px;

                .list-group {
                    flex: 1;
                    margin: 0;
                }
            }
        }
    }

    .container-social-metrics {
        .badge-danger {
            background-color: red;
        }
        .badge-warning {
            background-color: #ff5b00;
        }
        .header {
            padding-bottom: 0px;
        }
        .reviews-without-responses {
            margin-top: -1px;
            font-size: 11px;
            .badge {
                line-height: 11px;
            }
        }
        .reviews-tag-cloud {
            height: 65px;
            overflow: hidden;
            zoom: 0.9;

            .badge {
                margin: 1px;
                line-height: 1.2;
                cursor: pointer;
            }
        }
    }

    #reviews-ratings-graph {
        canvas {
            margin-top: -50px;
            margin-bottom: 25px;
            width: 100%;
        }
    }

    .reviews-ratings-graph {
        height: 60px;
        margin-bottom: 16px;
        margin-left: -10px;
    }

    .popular-times-tab {
        min-height: 200px;

        @media screen and (max-width: 767px) {
            min-height: 180px;
            height: 180px;

            #busy-hours-chart {
                height: 100%;
            }
        }

        .nav li a {
            padding: 8px;
        }
        .nav-tabs {
            border: none !important;
            margin-bottom: 13px;
        }
        .peak-time-nav {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            border-bottom: 2px solid #eee;

            li {
                display: flex;
                flex: 1 0;
            }

            button {
                flex: 1 0;
                display: flex;
                justify-content: center;
                background: none;
                border: 0;
                outline: 0;
                position: relative;
                height: 36px;
                color: #999;
                text-align: center;
                text-transform: uppercase;
                font-size: 14px;

                &::after {
                    content: '';
                    display: block;
                    position: absolute;
                    bottom: -2px;
                    height: 2px;
                    width: 100%;
                    background: #2196f3;
                    transform-origin: bottom center;
                    transform: scaleX(0);
                    transition: 0.5s;
                }

                &.active {
                    color: #222;
                    &::after {
                        transform: scaleX(1);
                    }
                }
            }
        }

        .peak-time-chart {
            max-height: 220px;
        }
    }
}

.custom-alert {
    display: flex;
    align-items: center;
    animation: fadeIn 1s;
    padding: 32px 46px;
    background-color: $container-color;
    border-radius: $border_radius-m;
    gap: 32px;
    max-width: 100%;
    margin-bottom: 24px;

    @media (max-width: 1200px) {
        padding: 16px;
        gap: 16px;
    }

    @media (max-width: 991px) {
        flex-direction: column;
        align-items: flex-end;
    }

    &__body {
        color: $secondary-text-color;
    }

    &__content {
        display: flex;
        align-items: flex-start;
        gap: 24px;
        flex-grow: 1;
        color: map-get($colors-v2, 'secondary');
        font-size: 12px;
        width: 100%;

        @media (max-width: 991px) {
            align-items: flex-start;
            gap: 16px;
        }

        h6 {
            font-size: 24px;
            line-height: 1;
            font-weight: 600;
            margin: 0 0 16px;
            color: $primary-text-color;

            @media (max-width: 991px) {
                font-size: 20px;
            }
        }

        p {
            line-height: 1.2;
            max-width: 750px;
            margin: 0;
        }

        ul.holidays {
            padding-left: 30px;
        }
    }

    &__btn {
        flex-shrink: 0;
    }

    &__icon {
        width: 64px;
        height: 64px;
        flex-shrink: 0;
        position: relative;

        @media (max-width: 991px) {
            width: 32px;
            height: 32px;

            svg,
            img {
                width: inherit;
                height: inherit;
            }
        }

        .custom-icon {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }
    }

    &--danger {
        background: map-get($colors-v2, 'red-light');
    }

    &--updates {
        color: #2196f3;
        background-color: #cfeaff;
    }

    &--warning {
        background-color: map-get($colors-v2, 'yellow-light');
    }
}

.custom-icon {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    text-decoration: none;
    font-size: 18px;

    s {
        text-decoration: inherit;
    }

    &--payment {
        background-image: url(./../../../assets/images/payments-icon.svg) !important;
    }
}

.bell-icon {
    background-image: url('./../../../assets/images/bell-icon.svg');
}

.warning-icon {
    background-image: url('./../../../assets/images/warning-icon.svg');
}

.warning-icon-yellow {
    background-image: url('./../../../assets/images/warning-icon-yellow.svg');
}

.dashboard-section {
    padding: 0 0 24px;
}

.payment-icon {
    background-image: url(./../../../assets/images/payments-icon.svg) !important;
}

.chart-icon {
    background-image: url(./../../../assets/images/chart-icon.svg) !important;
}

.screen-icon {
    background-image: url(./../../../assets/images/screen-icon.svg) !important;
}

.duress-icon {
    background-image: url(./../../../assets/images/duress-icon.svg) !important;
}

.cctv-icon {
    background-image: url(./../../../assets/images/cctv-icon.svg) !important;
}

.audio-icon {
    background-image: url(./../../../assets/images/music-mod-icon.svg) !important;
}

.temp-icon {
    background-image: url(./../../../assets/images/thermostat-icon.svg) !important;
}

.myzone-icon {
    background-image: url(./../../../assets/images/myzone-icon.svg) !important;
}

.the-info-box {
    border: 1px solid map-get($colors-v2, 'border-dark');
    border-radius: 4px;
    padding: 24px 16px;
    display: flex;
    flex-direction: column;

    @media screen and (max-width: 767px) {
        padding: 12px;
    }

    &--lg {
        padding: 24px;
        height: 100%;

        @media screen and (max-width: 767px) {
            padding: 16px;
        }
    }

    &__row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(185px, 1fr));
        gap: 24px;
        margin-bottom: 24px;

        &.lg {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        @media (max-width: 420px) {
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
        }
    }

    &__data {
        display: flex;
        gap: 12px;

        @media screen and (max-width: 420px) {
            position: relative;
            padding: 21px 0 0;
        }
    }

    &__icon {
        background: no-repeat center;
        width: 40px;
        height: 40px;

        // &--current-members {
        //     background-image: url('./../../../assets/images/current-members-icon.svg');
        // }

        // &--new-members {
        //     background-image: url('./../../../assets/images/new-members-icon.svg');
        // }

        // &--ubx-gym {
        //     background-image: url('./../../../assets/images/ubx-gym-icon.svg');
        // }

        // &--on-hold {
        //     background-image: url('./../../../assets/images/on-hold-icon.svg');
        // }

        &--money {
            background-image: url('./../../../assets/images/money-icon.svg');
        }

        // &--non-visiting {
        //     background-image: url('./../../../assets/images/non-visiting-icon.svg');
        // }

        &--total-payments {
            background-image: url('./../../../assets/images/total-payments-icon.svg');
        }

        &--charges {
            background-image: url('./../../../assets/images/charges-icon.svg');
        }

        &--pending-payouts {
            background-image: url('./../../../assets/images/pending-payouts-icon.svg');
        }

        &--unique-members {
            background-image: url('./../../../assets/images/unique-members-icon.svg');
        }

        &--avg-spend {
            background-image: url('./../../../assets/images/avg-spend-icon.svg');
        }

        &__wrapper {
            border-radius: 4px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            &--blue {
                background-color: map-get($colors-v2, 'blue-alt');
            }

            &--green {
                background-color: map-get($colors-v2, 'green-2');
            }

            &--pink {
                background-color: map-get($colors-v2, 'pink-2');
            }

            &--yellow {
                background-color: map-get($colors-v2, 'yellow-2');
            }

            &--red {
                background-color: map-get($colors-v2, 'red-2');
            }

            &--stroked {
                background: transparent !important;
                border-width: 1px;
                border-style: solid;

                &--blue {
                    border-color: map-get($colors-v2, 'blue-alt');
                    .icon-primary {
                        &.icon-stroke,
                        & > .icon-stroke {
                            stroke: map-get($colors-v2, 'blue-alt');
                        }
                        .icon-fill {
                            fill: map-get($colors-v2, 'blue-alt');
                        }
                    }
                }

                &--green {
                    border-color: map-get($colors-v2, 'green-2');

                    .icon-primary {
                        &.icon-stroke,
                        & > .icon-stroke {
                            stroke: map-get($colors-v2, 'green-2');
                        }
                        .icon-fill {
                            fill: map-get($colors-v2, 'green-2');
                        }
                    }
                }

                &--pink {
                    border-color: map-get($colors-v2, 'pink-2');

                    .icon-primary {
                        &.icon-stroke,
                        & > .icon-stroke {
                            stroke: map-get($colors-v2, 'pink-2');
                        }
                        &.icon-fill,
                        & > .icon-fill {
                            fill: map-get($colors-v2, 'pink-2');
                        }
                    }
                }

                &--yellow {
                    border-color: map-get($colors-v2, 'yellow-2');

                    .icon-primary {
                        &.icon-stroke,
                        & > .icon-stroke {
                            stroke: map-get($colors-v2, 'yellow-2');
                        }
                        .icon-fill {
                            fill: map-get($colors-v2, 'yellow-2');
                        }
                    }
                }

                &--red {
                    border-color: map-get($colors-v2, 'red-2');

                    .icon-primary {
                        &.icon-stroke,
                        & > .icon-stroke {
                            stroke: map-get($colors-v2, 'red-2');
                        }
                        .icon-fill {
                            fill: map-get($colors-v2, 'red-2');
                        }
                    }
                }
            }
        }
    }

    &__name {
        position: relative;
        padding: 0 24px 0 0;

        @media screen and (max-width: 420px) {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }

        &__wrapper {
            display: block;
            flex-grow: 1;
        }

        h6 {
            font-size: 11px;
            line-height: 1.05;
            color: map-get($colors-v2, 'secondary');
            margin: 0;
            font-weight: 600;
        }
    }

    &__info {
        position: absolute;
        width: 16px;
        height: 16px;
        background: url('./../../../assets/images/help-icon.svg') no-repeat center;
        right: 0;
        top: 50%;
        margin-top: -8px;
        cursor: pointer;
        display: block;

        &--relative {
            position: relative;
        }
    }

    &__content {
        display: flex;
        align-items: flex-end;
        gap: 4px;
        flex-wrap: wrap;

        .number {
            font-size: 40px;
            line-height: 1;
            color: map-get($colors-v2, 'primary');
            margin: 0;
        }
    }

    &__chart {
        &-wrapper {
            min-height: 400px;
            margin: 0 0 40px;
            max-width: 100%;
            width: 100%;

            @media screen and (max-width: 600px) {
                width: 1100px;
                max-width: none;
                flex-shrink: 0;
            }

            &--labels {
                display: none;
                background: #fff;

                @media screen and (max-width: 600px) {
                    display: block;
                    flex-shrink: 0;
                    // position: absolute;
                    position: sticky;
                    top: 0;
                    left: 0;
                    padding: 0 0 0 15px;
                    width: 50px;
                    overflow: hidden;
                    z-index: 20;
                }
            }

            .graph-chart {
                max-width: 100%;
            }
        }
        &-section {
            overflow-x: auto;
            display: flex;
            width: 100%;
            flex-grow: 1;

            @media screen and (max-width: 600px) {
                background: #fff;
                width: 100vw;
                margin: 0 0 0 -32px;
                padding: 15px 10px 0 0;
                border-top: 1px solid map-get($colors-v2, 'border-dark');
                border-bottom: 1px solid map-get($colors-v2, 'border-dark');
            }
        }
    }

    &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 16px;

        &--lg {
            margin-bottom: 50px;
        }

        &--bordered {
            border-bottom: 1px solid map-get($colors-v2, 'border');
            padding-bottom: 12px;
            margin: 0 0 16px;
        }

        h2 {
            font-size: 24px;
            margin: 0;
            display: flex;
            position: relative;
            padding: 0 24px 0 0;
            max-width: 100%;

            @media screen and (max-width: 600px) {
                flex-grow: 1;
            }
        }

        h3 {
            font-size: 20px;
            line-height: 1;
            margin: 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        &__btn-grp {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            flex-wrap: wrap;

            .btn {
                padding: 4px 8px;
                height: 28px;
            }
        }

        &__content {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &-col-left {
                display: flex;
                align-items: center;
                gap: 10px;
                flex-wrap: wrap;
            }
        }
    }
}

.dashboard {
    .percentage-stat {
        &__wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 20px;
            line-height: 1;
            color: map-get($colors-v2, 'grey-light');

            @media screen and (max-width: 500px) {
                padding: 10px 0 0;
            }

            &::before {
                content: '';
                display: block;
                flex-grow: 1;
                width: 100%;
                height: 1px;
                background-color: map-get($colors-v2, 'border');
            }

            span {
                white-space: nowrap;
                flex-shrink: 0;
                display: flex;
                align-items: center;

                &.is-up {
                    color: map-get($colors-v2, 'green-3');
                }

                &.is-down {
                    color: map-get($colors-v2, 'red-alt');
                }

                &.has-icon {
                    &::after {
                        background: no-repeat center;
                        background-size: cover;
                        width: 24px;
                        height: 24px;
                        content: '';
                        display: block;
                    }

                    &.is-up-icon {
                        &::after {
                            background-image: url('./../../../assets/images/diagonal-up.svg');
                        }
                    }

                    &.is-up-icon-alt {
                        &::after {
                            background-image: url('./../../../assets/images/diagonal-up-alt.svg');
                        }
                    }

                    &.is-down-icon {
                        &::after {
                            background-image: url('./../../../assets/images/diagonal-down.svg');
                        }
                    }

                    &.is-down-icon-alt {
                        &::after {
                            background-image: url('./../../../assets/images/diagonal-down-alt.svg');
                        }
                    }
                }
            }
        }
    }
}

.use-skeleton.show-on-loading {
    display: none;
}

.loading-animation {
    animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
}

.isLoading {
    .use-skeleton {
        position: relative;
        overflow: hidden;
        animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
        border: 0 !important;
        pointer-events: none !important;
        display: inline-block;
        border-radius: 2px;

        &.show-on-loading {
            display: block;
        }

        &--absolute {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        &::after {
            content: '';
            display: block;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            background-color: map-get($colors-v2, 'border');
            z-index: 3;
            border-radius: inherit;
        }
    }
    .the-info-box__info {
        background-image: none !important;
    }
    .percentage-stat--wrapper {
        &::before {
            animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
        }
    }
    dt.use-skeleton {
        margin-bottom: 8px;
    }
}

.isInlineBlock {
    display: inline-block;
}

.rounded {
    border-radius: 4px;

    &--full {
        border-radius: 100%;
    }
}

.revenue {
    &__section {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 24px;
        max-width: 100%;

        &.col-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }

        &.no-revenue-graph {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }

        @media (max-width: 1700px) {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }

        @media (max-width: 991px) {
            grid-template-columns: minmax(0, 1fr);

            &.col-2 {
                grid-template-columns: minmax(0, 1fr);
            }
        }
    }
    &__wrapper {
        @media screen and (min-width: 992px) {
            grid-column: span 2 / span 2;
            grid-row: span 2 / span 2;
        }
    }
    &-outlook {
        padding: 16px 0 0;
    }

    &-stat {
        flex: 1 0 180px;
        text-align: center;
        padding: 0 16px;
        border-right: 1px solid map-get($colors-v2, 'border');

        &:last-child {
            border-right: none;
        }

        @media screen and (max-width: 620px) {
            &:nth-child(2n) {
                border-right: none;
                position: relative;
                margin-bottom: 20px;

                &::after {
                    content: '';
                    width: 50%;
                    display: block;
                    height: 1px;
                }
            }

            flex: 1 0 50%;
        }

        dt {
            font-size: 11px;
            line-height: 20px;
            color: map-get($colors-v2, 'secondary');
            font-weight: 600;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
            margin-bottom: 4px;

            .info-icon {
                margin-top: 0;
                cursor: pointer;
            }
        }

        dd {
            font-size: 24px;
            line-height: 1;
            color: map-get($colors-v2, 'primary');
            text-transform: lowercase;
        }

        &s {
            display: flex;
            align-items: stretch;
            flex-wrap: wrap;
            row-gap: 20px;
            margin: 0 auto;
            width: 600px;
            max-width: 100%;
            padding: 10px 0 20px;

            // @media screen and (max-width: 620px) {
            //     margin: 0 -16px;
            // }
        }
    }

    &-chart-section {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        @media screen and (max-width: 600px) {
            position: relative;
        }
    }
}
.btn {
    &__icon {
        width: 20px;
        height: 20px;
        background: no-repeat center;
        background-size: contain;
        margin-right: 4px !important;
    }
    &__badge {
        border-radius: 4px;
        background-color: map-get($colors-v2, 'border');
        color: map-get($colors-v2, 'secondary');
        padding: 0 4px;
        line-height: 20px;
        font-size: 11px;
        min-width: 20px;
        min-height: 20px;
        text-align: center;

        &:empty {
            display: none;
        }

        &--semi {
            font-weight: 500;
        }

        &--failed {
            background: map-get($colors-v2, 'red-alt');
            color: #fff;
        }

        &--sm {
            line-height: 16px;
            min-height: 16px;
            min-width: 1px;
        }

        &--success {
            background: map-get($colors-v2, 'green-light');
            color: map-get($colors-v2, 'green-1');
        }

        &--stroked {
            background: transparent;
            border: 1px solid map-get($colors-v2, 'border-dark');
            color: map-get($colors-v2, 'secondary');
        }

        &--highlight {
            background: transparent;
            border: 1px solid map-get($colors-v2, 'green-light');
            color: map-get($colors-v2, 'green-1');
        }
    }

    &--mini {
        font-size: 11px !important;
        height: 16px;
        line-height: 16px;
        min-width: 1px;
        padding: 0 6px !important;

        &.btn-border {
            border-color: #6d7382 !important;
            color: #6d7382 !important;
        }
    }
}

.heading-w-badge {
    font-size: 20px;
    line-height: 1;
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    margin: 0 0 16px;
}

.custom-badge {
    padding: 0 6px;
    font-size: 11px;
    line-height: 16px;
    border-radius: 4px;
    text-transform: uppercase;
    font-weight: 600;
    background: map-get($colors-v2, 'border');

    &--success {
        color: map-get($colors-v2, 'green-1');
        background: map-get($colors-v2, 'green-light');
    }

    &--fatal {
        color: map-get($colors-v2, 'red-alt');
        background: map-get($colors-v2, 'red-light');
    }

    &.normal-case {
        text-transform: none;
    }

    &--bordered {
        background: transparent;
        border: 1px solid currentColor;
    }
}

.review-management {
    &__content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 24px;
        flex-grow: 1;
    }
    &__tags-section {
        background: map-get($colors-v2, 'container');
        padding: 0 0 20px;
        display: flex;
        flex-direction: column;
        max-height: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;

        &__wrapper {
            overflow: hidden;
            position: relative;
            max-height: 220px;

            &::after {
                content: '';
                display: block;
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                box-shadow: inset 0 -30px 10px -10px map-get($colors-v2, 'container');
            }
        }

        h5 {
            font-size: 11px;
            line-height: 1.3;
            text-transform: uppercase;
            color: map-get($colors-v2, 'secondary');
            flex-shrink: 0;
            position: sticky;
            top: 0;
            left: 0;
            background: map-get($colors-v2, 'container');
            padding: 20px 12px 12px;
            margin: 0;
        }

        .reviews-tag-cloud {
            padding: 0 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .badge-green-outline {
            border-radius: 10px;
            background: map-get($colors-v2, 'green-light');
            border-color: transparent;
            font-size: 11px;
            line-height: 1.45;
            text-transform: none;
        }
    }

    &__wrapper {
        .social-overview {
            display: flex;
            flex-direction: column;

            .the-info-box {
                &__header {
                    flex-shrink: 0;
                }
                &__body {
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                }
            }

            .reviews-ratings-graph {
                flex-shrink: 0;
                max-width: 100%;
            }
        }
        .reviews-without-responses {
            font-size: 11px;
            line-height: 1.8;
            color: map-get($colors-v2, 'secondary');
            text-transform: uppercase;

            label {
                display: flex;
                gap: 4px;
                align-items: center;
            }

            .badge {
                display: inline-flex;
                gap: 2px;
                font-weight: 600;
                align-items: center;
                padding: 1px 4px 1px 6px;
                border-radius: 4px;
            }
        }
    }
}

.badge {
    display: inline-flex;
    align-items: center;

    &__icon {
        width: 16px;
        height: 16px;
        display: block;
        background-size: contain;
    }

    *[class^='fa'] {
        margin-left: 6px;
    }
}

.membership-peak-time {
    &__section {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 24px;
        max-width: 100%;

        @media screen and (max-width: 768px) {
            grid-template-columns: minmax(0, 1fr);
        }
    }
}

.members-split {
    &__content {
        display: flex;
        align-items: center;
        gap: 24px;
        flex-grow: 1;
        padding: 16px 0;
        justify-content: center;
        flex-wrap: wrap;

        #member_split_legend {
            position: relative;
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
    }
    &-chart {
        width: 180px;
        height: 180px;

        &__wrapper {
            max-width: 100%;
            order: -1;
            display: flex;
            justify-content: flex-end;
        }
    }
    &-legend {
        display: flex;
        align-items: center;
        gap: 6px;
        color: map-get($colors-v2, 'secondary');
        font-size: 12px;

        &::before {
            display: block;
            content: '';
            width: 10px;
            height: 10px;
            border-radius: 10px;
        }

        &--male {
            &::before {
                border: 1px solid rgba(33, 150, 243, 1);
                background: rgba(33, 150, 243, 0.2);
            }
        }

        &--female {
            &::before {
                border: 1px solid rgba(255, 106, 183, 1);
                background: rgba(255, 106, 183, 0.2);
            }
        }

        &-values {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 130px;
            border-right: 1px solid map-get($colors-v2, 'border');
            gap: 4px;
            color: map-get($colors-v2, 'secondary');
            padding: 0 20px;
            margin: 0;
            white-space: nowrap;

            &:nth-child(2) {
                border-right: none;
            }

            @media screen and (max-width: 350px) {
                padding: 0 10px;
            }
        }

        &__name {
            font-size: 11px;
            text-transform: uppercase;
            line-height: 1.8;
        }

        &__value {
            font-size: 24px;
            line-height: 1;
            color: map-get($colors-v2, 'primary');

            @media screen and (max-width: 350px) {
                font-size: 18px;
            }
        }

        &-wrap {
            grid-column: span 2;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding-top: 32px;
            margin: 0;
        }
    }
    &-widget {
        display: flex;
        flex-direction: column;

        .the-info-box {
            &__header {
                flex-shrink: 0;
            }
            &__body {
                flex-grow: 1;
                display: inherit;
                flex-direction: inherit;
                align-items: center;
            }
        }
    }
}

.peak-time {
    &__wrapper {
        max-width: 100%;

        canvas {
            width: 100%;
            height: 100%;
        }
    }
    &-widget {
        display: flex;
        flex-direction: column;

        .the-info-box {
            &__header--bordered {
                margin-bottom: 0;
                flex-shrink: 0;
            }
            &__body {
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                width: 100%;
                .peak-time {
                    &-nav {
                        border-bottom: 1px solid map-get($colors-v2, 'border');
                        margin-bottom: 14px;
                        flex-shrink: 0;

                        button {
                            align-items: center;
                            font-size: 14px;
                            line-height: 1.72;
                            color: map-get($colors-v2, 'secondary');
                            font-weight: 600;

                            &.active {
                                color: map-get($colors-v2, 'blue-alt');
                                &:after {
                                    background: map-get($colors-v2, 'blue-alt');
                                }
                            }
                        }
                    }
                    &-chart {
                        flex-grow: 1;
                        overflow-x: auto;
                        max-width: 100%;

                        &__wrapper {
                            max-width: 100%;
                            background: #fff;
                            height: 100%;
                        }

                        @media screen and (max-width: 600px) {
                            background: transparent;
                        }
                    }
                }
                .popular-times-tab {
                    flex-grow: 1;
                    display: inherit;
                    flex-direction: inherit;

                    #busy-hours-chart {
                        flex-grow: 1;
                        display: inherit;
                        flex-direction: inherit;
                    }
                }
            }
        }
    }
}

.lead-events {
    &-data {
        color: map-get($colors-v2, 'secondary');

        @media screen and (max-width: 991px) {
            text-align: center;
            width: 100%;
        }

        &__chart {
            width: 200px;
            height: 200px;
            border-radius: 100%;
        }

        &__wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 56px;
            flex-wrap: wrap;
            padding: 20px 0;
            width: 100%;

            @media screen and (max-width: 991px) {
                gap: 20px;
            }
        }

        &__title {
            font-size: 11px;
            line-height: 20px;
            margin: 0 0 4px;
            font-weight: 600;
            text-transform: uppercase;
        }

        &__count {
            font-size: 20px;
            line-height: 1;
            color: map-get($colors-v2, 'primary');
            margin: 0 0 1em;
        }

        &__values {
            list-style: none;
            margin: 0 0 20px;
            padding: 0;

            &-grp {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                line-height: 1.2;
                color: map-get($analytics-chart, 'item1');
                margin: 0 0 10px;

                @media screen and (max-width: 991px) {
                    justify-content: center;
                }

                &__pill {
                    border-radius: 20px;
                    padding: 0 8px;
                    border: 1px solid currentColor;
                    position: relative;
                    font-weight: 600;

                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        border-radius: 100px;
                        background: currentColor;
                        opacity: 0.2;
                    }
                }

                &__label {
                    color: map-get($colors-v2, 'secondary');
                }

                &:nth-child(2) {
                    color: map-get($analytics-chart, 'item2');
                }

                &:nth-child(3) {
                    color: map-get($analytics-chart, 'item3');
                }

                &:nth-child(4) {
                    color: map-get($analytics-chart, 'item4');
                }
            }
        }

        .btn-outline {
            padding: 4px 8px !important;
        }
    }

    &-widget {
        display: flex;
        flex-direction: column;

        .the-info-box {
            &__header {
                flex: 0;
            }
            &__body {
                flex: 1 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    &__wrapper {
        &.is-hidden {
            display: none !important;
        }
    }
}

.smart-club {
    &__content {
        display: flex;
        flex-wrap: wrap;
        gap: 0 20px;
        width: 100%;
        padding: 0;
    }
    &__values {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
        gap: 0 20px;
        margin: 0;
        padding: 0;
        list-style: none;
        width: 100%;

        li {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            justify-content: flex-start;
            color: map-get($colors-v2, 'secondary');

            & > .smart-club__value__details {
                display: flex;
                align-items: flex-start;
                gap: 16px;
                justify-content: flex-start;
                color: map-get($colors-v2, 'secondary');
                padding: 16px 12px;
                text-decoration: none;
                border-radius: 6px;
                transition: all 0.3s ease-in-out;
                background-color: rgba(0, 0, 0, 0);
                border: 1px solid rgba(0, 0, 0, 0);
                width: 100%;

                &:not(div):hover {
                    text-decoration: none;
                    background: rgba(0, 0, 0, 0.05);
                    border-color: rgba(0, 0, 0, 0.05);
                }
            }
        }

        &-icon {
            display: block;
            width: 24px;
            height: 24px;

            &__wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                background: mix(rgba(255, 255, 255, 0.8), map-get($colors-v2, 'secondary'));
                margin: 0;
                border-radius: 4px;
                flex-shrink: 0;
            }
        }
    }
    &__label {
        font-size: 14px;
        line-height: 1.43;
        text-transform: uppercase;
        font-weight: 600;
        margin: 0 0 4px;
    }
    &__value {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
        color: map-get($colors-v2, 'blue-alt');
        font-weight: 600;
        font-size: 18px;
        line-height: 1;
        margin: 0 0 4px;

        &--fatal {
            color: map-get($colors-v2, 'red-alt');
        }
    }
    &__size {
        font-size: 18px;
        color: map-get($colors-v2, 'primary');
        margin: 0;
        font-weight: 600;

        &-value {
            &--fatal {
                color: map-get($colors-v2, 'red-alt');
            }
        }

        &-limit {
            font-size: 14px;
            color: map-get($colors-v2, 'secondary');
        }
    }
}

.checkin-history {
    &__wrapper {
        // hiding this temporarily so we can push to prod
        display: none !important;
        grid-row: span 2 / span 2;

        @media screen and (max-width: 991px) {
            order: 999;
            grid-column: span 2 / span 2;
        }

        @media screen and (max-width: 767px) {
            grid-column: 1;
        }

        .the-info-box__header {
            margin: 0;
        }
    }

    &__table {
        // min-width: 530px;
        width: 100%;

        thead tr {
            position: sticky;
            left: 0;
            top: 0;
            background: #fff;
            z-index: 2;

            &::after {
                content: '';
                position: absolute;
                left: 0;
                top: 100%;
                height: 1px;
                background: map-get($colors-v2, 'border');
                width: 100%;
            }
        }

        th,
        td {
            padding: 8px 16px;
            color: map-get($colors-v2, 'secondary');
            text-transform: uppercase;
            vertical-align: top;

            &:last-child {
                text-align: right;
            }

            // &:first-child {
            //     width: 160px;
            // }

            p {
                margin: 0;
            }
        }

        th {
            line-height: 1.81;
            font-size: 11px;
            color: map-get($colors-v2, 'primary');
            font-weight: 600;
        }

        td {
            padding: 12px 16px;
        }

        &-wrapper {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
    }

    &-widget {
        display: flex;
        flex-direction: column;

        .the-info-box {
            &__header {
                flex: 0;
            }
            &__body {
                flex: 1 0;
            }
        }
    }

    &__content {
        height: 100%;
        min-height: 600px;
        overflow: auto;
        position: relative;

        @media screen and (max-width: 991px) {
            min-height: 480px;
            overflow: hidden;
        }
    }

    &__btn-wrapper {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 200px;
        padding: 16p 16px 10px;
        z-index: 2;
        display: flex;
        align-items: flex-end;
        justify-content: center;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 70.83%);
            pointer-events: none;
        }

        .btn {
            padding: 8px 16px !important;
            position: relative;
            z-index: 3;
        }
    }
}

.access {
    &-status {
        text-transform: none;
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: flex-end;

        span:not(.access-status__indicator) {
            width: 50px;
        }

        &__indicator {
            width: 10px;
            height: 10px;
            border: 1px solid map-get($colors-v2, 'secondary');

            &--online {
                background: map-get($colors-v2, 'green-3');
                border-color: map-get($colors-v2, 'green-3');

                &-idle {
                    border-color: map-get($colors-v2, 'green-3');
                }
            }

            &--denied {
                background: map-get($colors-v2, 'red-alt');
                border-color: map-get($colors-v2, 'red-alt');
            }
        }
    }
}

.card {
    &.modified {
        box-shadow: none;
        border-radius: 6px;
        border: 1px solid map-get($colors-v2, 'border-dark');

        .body {
            color: $primary-text-color;

            .number {
                line-height: 0.85;
                margin-bottom: 0.15em;
            }

            label {
                font-size: 1.125rem;
                font-weight: 600;
                text-transform: uppercase;
                color: $secondary-text-color;
            }
        }
    }
}

.aspect {
    &-square {
        aspect-ratio: 1 / 1;
    }
}

.social-media-calendar-btn {
    border: 1px solid $border-color !important;
    color: $slate-500;
}
