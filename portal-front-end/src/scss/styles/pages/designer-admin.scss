#designer-admin {
    & > .container-fluid {
        padding: 32px;
        background-color: #fff;
        overflow: auto;

        @media (max-width: 767px) {
            padding: 16px;
        }
    }

    .remove-page:hover {
        i {
            color: $red-400;
        }
    }
}

.designer-custom-page-dialog {
    iframe,
    embed {
        width: 100%;
        height: 400px !important;
        border: 1px solid #ddd;
        border-radius: 8px;
        background-color: #f6f9fc;
    }

    .upload-status-item {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 10px;
        display: flex;
        gap: 2rem;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .custom-dz {
        height: 467px;
        cursor: pointer;
        border: 2px dashed #ddd;
        background-color: #f6f9fc;
        border-radius: 8px;
        &::after {
            content: 'Drop files here or click to upload';
            display: block;
            text-align: center;
            padding-top: 200px;
            color: #999;
        }
    }
}
