// training-camp.scss

.training-camp {
    #tc-leads-table-container {
        min-height: 200px;
    }

    .leads-search-nav {
        width: 100%;
        max-width: 900px;
        margin: 0 auto;

        .search-bar {
            font-size: 14px;
        }
    }
    .search-bar-wrapper {
        .input-group {
            width: calc(100% - 200px);
            float: left;
            margin-bottom: 0px;
        }
        .btn-new-sms {
            margin-bottom: 20px;
            margin-top: -2px;
            margin-left: 30px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
            // visibility: hidden;
            .material-icons {
                padding-right: 5px;
            }
        }
    }
    .tc-limit-results-checkbox {
        padding-top: -20px;
    }
}

#training-camp-members-page {
    tbody tr {
        cursor: pointer;
    }

    .col-current-camp {
        max-width: 14rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .has-data-dot-container {
        position: relative;
        display: inline-block;
        width: 10px;
        height: 10px;
    }

    .has-data-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        z-index: 1;
        position: absolute;
        top: 50%;
        left: 0;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        transform: translateY(-50%);

        &.--inactive {
            background-color: transparent;
        }

        &.--inactive {
            background-color: transparent;
        }

        &.--level-25 {
            background-image: conic-gradient($accent-blue-400 0% 25%, transparent 25% 100%);
        }

        &.--level-50 {
            background-image: conic-gradient($accent-blue-400 0% 50%, transparent 50% 100%);
        }

        &.--level-75 {
            background-image: conic-gradient($accent-blue-400 0% 75%, transparent 75% 100%);
        }

        &.--level-100 {
            background-color: $accent-blue-400;
        }
        &.--bg {
            background-color: $accent-blue-50;
            z-index: 0;
            // border: 1px dashed $accent-blue-400;
        }
    }

    .table {
        thead {
            .col-data-status {
                padding-right: 1.75rem;
                &:after {
                    right: 0px;
                }
            }
        }
        .col-data-status {
            padding: 0 4px;
            vertical-align: middle;
            text-align: center;
        }
    }
}

#training-camp-member-info-page {
    hr {
        background-color: $surface-75;
        border-color: $surface-75;
        margin: 0;
    }

    .mem-info_body {
        max-width: 1200px;
        gap: 1rem;
    }

    .mem-info_item {
        font-size: 14px;
        max-width: 22rem;
    }

    .mem-info_label {
        font-weight: 600;
        color: $slate-400;
    }

    .mem-info_value {
        font-weight: 400;
        color: $slate-500;
    }

    .tc-metric_header {
        display: flex;
        align-items: center;
        gap: 4px;
        color: $slate-400;
        padding-left: 8px;
        font-weight: 500;

        i {
            font-size: 16px;
            color: $slate-300;
        }
    }

    .tc-metric-card {
        box-shadow: 1px 3px 8px -2px rgba(80, 92, 104, 0.08);
        border-radius: 8px;
        border: 1px solid $border-color;
        padding: 6px 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 1.75rem;
        min-width: 14rem;
        min-height: 108px;

        .ph-badge {
            i {
                font-size: 12px;
            }
        }

        .tc-metric-card_header {
            font-size: 14px;
            font-weight: 500;
            color: $slate-400;
            padding-right: 1.5rem;
            display: flex;
            align-items: center;
            gap: 4px;

            .material-icons {
                font-size: 14px;
                color: $slate-300;
            }
        }

        .tc-metric-card_body {
            font-size: 20px;
            font-weight: 500;
            color: $slate-450;
            display: flex;
            flex-direction: column;
            justify-content: end;
            gap: 6px;
            line-height: 1.1em;
            align-items: flex-end;

            &.--size-small.--size-small {
                font-size: 14px;
                color: $slate-450;
                font-weight: 600;
            }

            .__body-empty {
                color: $slate-300;
                font-size: 18px;
            }

            .__body-start_value {
                color: $slate-450;
                font-size: 14px;
                font-weight: 500;
            }
            .__body-type {
                font-size: 12px;
                color: $slate-400;
            }
        }

        &.--size-small {
            min-width: 12rem;
            min-height: 8rem;

            .tc-metric-card_body {
                font-weight: 500;
                font-size: 20px;
            }
        }

        &.--color-yellow {
            background-color: $yellow-50;
            border-color: $yellow-200;

            .tc-metric-card_header,
            .tc-metric-card_body {
                color: $yellow-600;
            }
        }

        &.--color-gray {
            background-color: $slate-50;
            border-color: $slate-200;

            .tc-metric-card_header,
            .tc-metric-card_body {
                color: $slate-450;
            }
        }

        &:has(+ .metric-comparison-card) {
            height: 100%;
        }
    }

    .note-entry {
        display: flex;
        flex-direction: column;
        gap: 4px;
        width: 100%;
    }

    .note-entry_footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        color: $slate-400;
        font-size: 12px;

        .__footer {
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .note-entry_content {
        font-size: 14px;
        font-weight: 500;
        color: $slate-450;
        background-color: $slate-50;
        padding: 6px 10px;
        border-left: 2px solid $surface-100;

        &.__content-empty {
            color: $slate-350;
        }
    }

    .journal-level {
        display: flex;
        align-items: center;
        gap: 2px;
        color: $slate-450;
        border-radius: 6px;
        padding: 2px 4px;

        .__level-icon {
            display: flex;
            align-items: center;
        }

        i {
            font-size: 14px;
            color: $red-300;
        }

        // &.--mood-3 {
        //     background-color: $yellow-75;
        // }

        &.--journal-mood {
            img {
                width: 14px;
                height: auto;
            }
        }

        // &.--energy-4,
        // &.--energy-5 {
        //     background-color: $red-50;
        // }
    }

    .comparison-card_range-bar {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .comparison-card_details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 4px;
        width: 100%;
        font-size: 11px;
        font-weight: 600;
        color: $accent-blue-400;
    }

    .metric-comparison-card {
        border-radius: 8px;
        border: 1px solid $surface-75;
        padding: 6px 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .comparison-tc-icon {
        height: 12px;
        width: auto;
        padding: 0 1px;
    }

    .comparison-card_chevron {
        height: 10px;
        width: auto;
    }

    .ph-card_header {
        .select-picker {
            width: fit-content;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1rem;

            .form-label {
                white-space: nowrap;
            }
        }

        .select-icon {
            width: 10px;
        }
    }
}

#training-camp-info-card {
    min-width: 80rem;
}
