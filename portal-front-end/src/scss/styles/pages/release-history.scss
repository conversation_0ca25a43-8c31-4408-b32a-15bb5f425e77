/* release-history.css */

.release-history-container {

    max-width: 1000px;
    margin: 0 auto;
    padding-top: 20px;
    background: inherit;

    .container {
        width: 100%;
    }

    .release-history-body {
        background-color: #f6f9fc;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .release-history-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .release-history-header h1 {
        font-size: 2em;
        margin-bottom: 10px;
    }

    .release-history-header p {
        font-size: 1em;
        color: #777;
    }

    .release-history-card {

        position: relative; // Needed for absolute positioning of child elements

        background-color: #ffffff;
        border-radius: 5px;
        margin-bottom: 20px;
        padding: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

        .release-date {
            position: absolute;
            top: 10px;
            right: 10px;
            font-weight: bold;
            font-size: 18px;
            padding: 10px;
            color: #555b62;
        }


    }

    .list-group {
        margin-bottom: 0px;
    }

    .list-group-item {
        border: none;
        padding-left: 0;
        padding-right: 0;
        color: #333;
        font-weight: 400;
        background-color: inherit;
    }

    .list-group-item:last-child {
        border-bottom: none;
    }

    .release-history-service {
        margin-top: 0px;
        font-weight: 600;
        margin-bottom: 15px;

        border-radius: 10px;
        padding: 10px;
        display: inline-block;
        font-size: 20px;
        text-transform: capitalize;

        // color: #0073e6;

        &.general {
            background-color: #f8f4ff;
            color: #9966ff;
        }

        &.websites {
            background-color: #f4f3ff;
            color: #0073e6;
        }

        &.performance-hub {
            background-color: #eefaf3;
            color: #3ec971;
        }

        &.training-camp-app {
            background-color: #f9f6ee;
            color: #c28b00;
        }

        &.train-on-demand-app {
            background-color: #f5f3f7;
            color: #b06086;
        }

        &.smart-club-technology {
            background-color: #f4f8ff;
            color: #0077b6;
        }

        &.member-management {
            background-color: #f2f6f5;
            color: #16817a;
        }
    }

    // Mobile adjustments
    @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
    }

    .content {
        flex: 1;
        padding-right: 20px;
    }

    .no-results-message {
        text-align: center;
        border: 1px solid lightgrey;
        border-radius: 10px;
        padding: 20px;
        margin: 20px;
    }

    .release-history-sidebar {
        // Remove fixed position to keep within container
        width: 200px;
        margin-left: 20px;

        &.pinned-sidebar {
            margin-left: -25px;
            position: fixed;
            right: auto; // to ensure it's not fixed to the right of the viewport
            bottom: auto; // to ensure it doesn't stretch to the bottom
        }

        .filter-checkbox {
            label {
                text-transform: capitalize;
            }
        }

        // Add date range filters
        .date-filter {
            margin-top: 15px;
            
            button {
                display: block;
                width: 100%;
                margin-bottom: 10px;
                padding: 5px 10px;
                background-color: transparent;
                border: 1px solid #0073e6;
                color: #0073e6;
                border-radius: 5px;
                cursor: pointer;

                &:hover {
                    background-color: #0073e6;
                    color: white;
                }
            }
            .filter-active {
                background-color: #0073e6;
                color: white;
            }
        }
    }
}
