.device-logs-dialog {
    width: 95vw;
    max-width: 800px;
}

.devices-color-ok {
    color: #2ac134; /* Stripe-style green for OK status */
    font-weight: 500; /* Slightly bold to make it stand out */
}

.devices-color-warning {
    color: #fc9500; /* Stripe-style yellow/orange for Warning status */
    font-weight: bold;
}

.devices-color-bad {
    color: #dc3545; /* Stripe-style red for Bad status */
    font-weight: bold;
}
.devices-color-default {
    color: #545454;
    font-weight: normal;
}

.device-logs-terminal {
    /* Ensure the terminal takes up 100% width of the dialog */
    .terminal {
        width: 100%;
        box-sizing: border-box; /* To include padding in the width */

        border: 1px solid #ebeef1;
        padding: 5px;

        overflow-y: scroll;

        display: flex;
        flex-direction: column;
        justify-content: flex-start; /* Aligns content to the top */
        height: 100%; /* Ensures the terminal takes full height of the container */
        min-height: 250px;
        max-height: 400px;


    }

    /* Set a monospace font for the terminal */
    .terminal, .terminal-line {
        font-family: 'Courier New', Courier, monospace;
        font-size: 0.875em; /* Adjust the size as needed */
        line-height: 1.5; /* Adjust line height for better readability */
        white-space: pre-wrap; /* Allows text to wrap */
        word-wrap: break-word;
    }

    /* Handling width and scrollbars */
    @media (max-width: 500px) {
        .terminal {
            overflow-x: auto; /* Horizontal scrollbar if needed */
            white-space: pre; /* Prevent wrapping at very small widths */
        }
    }

    .cursor {
      display: inline-block;
      background-color: #333;
      margin-left: -4px;
      width: 8px;
      height: 16px;
      margin-bottom: -5px;
      animation: blink 1s step-start infinite;
    }
    @keyframes blink {
      50% {
        opacity: 0;
      }
    }

}

.motion-zone-dialog, .brightness-contrast-dialog{
    fieldset {
        legend {
            padding-bottom: 10px;
        }
    }
}
.device-management-screen, .device-details-dialog {

    // .tab-content {
    //     overflow-y: scroll;
    //     overflow-x: hidden;
    // }

    .bluetooth-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url(../../../assets/images/bluetooth-icon.svg) no-repeat center;
        position: relative;
        top: 2px;
    }

    .ethernet-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url(../../../assets/images/ethernet-icon.svg) no-repeat center;
        position: relative;
        top: 2px;
    }

    .filesystem-state-stats {
        text-transform: capitalize;
        &.state-clean {
            color: #2ac134; /* Stripe-style green for OK status */
            font-weight: 500; /* Slightly bold to make it stand out */
        }
        &.state-dirty, &.state-orphan-inodes, &.state-non-contiguous, &.state-recovering, &.state-needs-recovery {
            color: #ffc107; /* Stripe-style yellow/orange for Warning status */
            font-weight: bold;
        }
        &.state-errors, &.state-read-only {
            color: #dc3545; /* Stripe-style red for Bad status */
            font-weight: bold;
        }
    }

    .device-cell {
        color: #6a7383;
        font-size: 14px;

        &--right {
            text-align: right;
        }

        &__tag {
            border-radius: 4px;
            padding: 1px 4px 1px 6px;
            margin-right: 4px;
            font-size: 12px;
            line-height: 16px;
            display: inline-flex;
            white-space: nowrap;
            height: 18px;

            i {
                margin-left: 4px;
                width: 16px;
                height: 16px;
                background: url(../../../assets/images/check-icon.svg) no-repeat center;
            }

            &--success {
                background: #d7f7c2;
                color: #006908;
            }

            &--updates {
                //background: #e9e9ff;
                //color: #0094f7;
                
                color: #2196f3;
                background-color: #cfeaff;

                i {
                    background-image: url(../../../assets/images/updates-icon.svg) !important;
                    filter: invert(60%) sepia(82%) saturate(4272%) hue-rotate(184deg) brightness(98%) contrast(94%);
                }
            }

            &--provisioning {
                
                color: #6a7383;
                background-color: #ebeef1;

                i {
                    background-image: url(../../../assets/images/provisioning-device.svg) !important;
                }
            }

            &--danger {
                background: #ffe7f2;
                color: #b3093c;

                i {
                    background-image: url(../../../assets/images/close-icon.svg);
                }
            }

            &--outlined {
                border: 1px solid #d5dbe1;
            }

            &--sonos {
                border: 1px solid #000;
                width: auto;
                height: 20px;
                background-repeat: no-repeat;
                background-position: center;

                i {
                    width: 50px;
                    margin-left: -2px;
                    background-size: 50px 50px;
                    background-image: url(../../../assets/images/sonos.svg);
                }
            }

            &--masterclock {
                border: 1px solid #000;
                width: auto;
                height: 20px;
                background-repeat: no-repeat;
                background-position: center;

                i {
                    width: 45px;
                    margin-left: -2px;
                    background-size: 42px 50px;
                    background-image: url(../../../assets/images/master-clock.svg);
                }
            }

            &--icon {
                width: 24px;
                height: 18px;
                background-repeat: no-repeat;
                background-position: center;

                &.sonos {
                    background-image: url(../../../assets/images/music-icon.svg);
                }
            }

            &--no-icon {
                padding-right: 6px;
            }

            &--battery {
                width: 24px;
                height: 14px;
                margin-top: 2px;
                border: 1px solid #aeb3b8;
                position: relative;
                background: #ffe7f2;
                color: #b3093c;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 100%;
                    background-image: linear-gradient(to right, #fff 1px, transparent 1px),
                        linear-gradient(to right, transparent 0, transparent 6px);
                    background-size: 7px 100%;
                    background-position: -7px center;
                    z-index: 2;
                    box-shadow: inset 0 0 0 2px #fff;
                    border-radius: 4px;
                }

                span {
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 0;
                    transition: 1s linear 0.2s;
                    background: currentColor;
                    border-radius: 2px;
                    z-index: 1;
                }

                &.battery {
                    &-bad {
                        background: #ffe7f2;
                        color: #b3093c;
                    }
                    &-warning-bad {
                        background: #f6e6b9;
                        color: #ff9d4d;
                    }
                    &-warning {
                        background: #ffecc5;
                        color: #ffcc63;
                    }
                    &-ok-medium {
                        background: #a5d2a9;
                        color: #57bf5f;
                    }
                    &-ok {
                        background: #d7f7c2;
                        color: #00a40d;
                    }

                    &-offline {
                        background: #aeb3b870 !important;
                        span {
                            display: none;
                        }
                    }
                }

            }
        }

        &__device {
            min-width: 240px;
            display: flex;

            &__icon {
                flex-shrink: 0;
                margin-right: 10px;
                border-radius: 4px;
                margin: auto 10px auto 10px;
                width: 32px;
                height: 32px;
                display: inline-flex;
                align-items: center;
                justify-items: center;
                overflow: hidden;
                text-indent: 999px;
                background: rgba(106, 115, 131, 0.8) no-repeat center;

                &.screen {
                    background-image: url(../../../assets/images/screen-icon.svg);
                }

                &.camera {
                    background-image: url(../../../assets/images/cctv-icon.svg);
                }

                &.duress {
                    background-image: url(../../../assets/images/duress-icon.svg);
                }
            }

            &__details {
                &__name {
                    line-height: 20px;
                    color: #30313d;
                }
                &__status {
                    line-height: 1;
                    display: flex;
                }
                &__tag {
                    border-radius: 4px;
                    padding: 1px 4px 1px 6px;
                    margin-right: 4px;
                    font-size: 12px;
                    line-height: 16px;
                    display: inline-flex;
                    white-space: nowrap;
                    height: 18px;

                    i {
                        margin-left: 4px;
                        width: 16px;
                        height: 16px;
                        background: url(../../../assets/images/check-icon.svg) no-repeat center;
                    }

                    &--success {
                        background: #d7f7c2;
                        color: #006908;
                    }

                    &--danger {
                        background: #ffe7f2;
                        color: #b3093c;

                        i {
                            background-image: url(../../../assets/images/close-icon.svg);
                        }
                    }

                    &--outlined {
                        border: 1px solid #d5dbe1;
                    }

                    &--icon {
                        width: 24px;
                        height: 18px;
                        background-repeat: no-repeat;
                        background-position: center;

                        &.sonos {
                            background-image: url(../../../assets/images/music-icon.svg);
                        }
                    }
                }
            }
        }

        &__meta {
            min-width: 200px;
        }

        &__ip {
            min-width: 150px;
        }

        &__signal {
            &__wifi {
                margin-bottom: 6px;
            }
            &__ethernet {
                margin-top: 15px;
            }

            border-radius: 4px;

            &-grp {
                display: flex;
                align-items: center;
                height: 4px;
                width: 180px;

                span {
                    line-height: 4px;
                    font-size: 10px;
                }
            }

            &__bar {
                position: relative;
                width: 120px;
                height: 4px;
                margin-right: 4px;
                background: #ffe7f2;
                color: #b3093c;
                border-radius: 4px;
                overflow: hidden;

                &.signal-good {
                    background: #d7f7c2;
                    color: #00a40d;
                }

                &.signal-ok {
                    background: #f6e6b9;
                    color: #ff9d4d;
                }
            }

            &__quality {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: currentColor;
                border-radius: 4px;
                // animation: growWidth .75s ease-in-out;
            }

            .wifi-experience-bar-sm {
                margin-top: 0 !important;
            }
        }

        &__activity {
            min-width: 140px;
        }

        &__uptime {
            min-width: 120px;
        }

        &__serial {
            min-width: 130px;
            .device-type {
                font-size: 11px;
                font-weight: bold;
            }
        }
    }
    
    @media screen and (min-width: 768px) {
      .is-fullscreen {
        height: calc(100vh - 70px);
        display: flex;
        flex-direction: column;
        overflow: hidden;
  
        .tab-pane, .tab-content, #ics-devices-container, .body {
          display: flex;
          flex-direction: column;
          flex: 1 0;
          overflow: hidden;
        }
  
        .nav-tabs, .search-and-filters-container  {
          flex: 0
        }
  
        #ics-devices-container {
          overflow: auto;
        }
  
        thead {
          position: sticky;
          left: 0;
          top: 0;
          background: #fff;
          z-index: 99;
        }
      }
    }
    .slider-wrapper-col {
        @include respond-below(sm) {
            width: 100%;
        }
    }
}


.device-management-screen {
    padding: 0;
    background: #fff;
    margin-right: 0 !important;

    &.content {
        padding-top: 0;
    }

    &.smallSidebarContent {
        margin: 70px 0 0 70px !important;
    }

    .nopadding {
        padding: 0;
    }

    .noboxshadow {
        box-shadow: none;
        margin-bottom: 0;
    }

    .body {
        padding: 32px 32px 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }

    .tab-content {
        position: relative;
        flex-grow: 1;
        overflow-y: auto;

        .nav-help-container {
            right: 0;
        }
    }

    .tab-pane {
      &.iframe-wrapper {
        padding: 0;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: calc(100% - 10px);
      }
    }

    #devices,
    #live-view {
        padding: 16px 0 0;

        .search-and-filters-container {
            @include respond-below(xs) {
                display: flex;
                flex-direction: column-reverse;
            }
        }

        .devices-search-nav {
            max-width: 100%;
            background: #f6f8fa;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;

            input[type='search']::-webkit-search-cancel-button {
                width: 12px;
                height: 12px;
                background-size: cover;
                margin-right: 30px;
            }

            .buttons {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;

                @include respond-below(xs) {
                    a > span {
                        display: none;
                    }                   
                    justify-content: space-around;
                    margin-bottom: 1rem; // flex-direction: column;
                }
            }

            @include respond-below(xs) {
                flex-direction: column;
            }
        }

        .search-bar-wrapper {
            width: 400px;
            max-width: 100%;
            height: 30px;

            .input-group {
                width: 100%;
                position: relative;

                &-addon {
                    position: absolute;
                    padding: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 0;
                    z-index: 10;
                    width: 20px;
                    height: 20px;
                    margin-right: 8px;
                    color: #6a7383;
                }

                .form-control {
                    padding: 5px 8px;
                    border-width: 1px;
                    border-color: #ebeef1;
                    height: 30px;

                    &:focus {
                        border-color: #0094f7;
                    }
                }
            }
        }

        .d-status-false {
            background-color: transparent;

            &:hover {
                background-color: #f9f9f9;
            }
        }

        .hide-devices-group,
        .styled-checkbox {
            margin-bottom: 10px;
            [type='checkbox'] {
                & + label {
                    padding-left: 28px;
                    height: 16px;
                    line-height: 16px;
                    font-size: 12px;
                    font-weight: 600;
                }

                &.filled-in {
                    & + label {
                        &:before {
                            transform: rotate(0deg);
                            left: 0;
                            top: 0;
                            width: 16px;
                            height: 16px;
                            border: none !important;
                            background: url(../../../assets/images/check-white.svg) no-repeat center;
                            margin: 0;
                        }

                        &:after {
                            width: 16px;
                            height: 16px;
                            border-radius: 4px;
                        }
                    }

                    &:not(:checked) {
                        & + label:after {
                            border: 2px solid #d5dbe1;
                        }
                    }
                }
            }
        }
    }

    .btn:not(.btn-link):not(.btn-circle) {
        height: 30px;
        font-size: 12px;
        color: #30313d;
        padding-right: 2px;

        span {
            margin-right: 4px;
        }

        svg {
            width: 20px;
            height: 20px;
            margin: 0;
            color: #6a7383;
        }
    }

    #ics-devices-container {
        width: 100%;
        max-width: 100%;
        // overflow-x: auto;
    }

    table.devices-table {
        thead th {
            padding-left: 6px;
            padding-right: 6px;

            &:first-child {
                padding-left: 16px;
            }

            &:last-child {
                padding-right: 16px;
            }

            .device-screen-table {
                &__header {
                    display: flex;
                    white-space: nowrap;
                    align-items: center;
                    font-size: 11px;
                    text-transform: uppercase;
                    line-height: 20px;
                    font-weight: 600;
                    color: #30313d;

                    &--justify-end {
                        justify-content: flex-end;
                    }

                    span:first-child {
                        padding-right: 4px;
                    }

                    &__icon {
                        width: 18px;
                        height: 18px;
                        background: url(./../../../assets/images/arrow-down-icon.svg) no-repeat center center;
                        background-size: cover;
                        opacity: 0.2;
                    }
                }
            }

            &.sorting {
                &_asc,
                &_desc {
                    .device-screen-table__header__icon {
                        opacity: 1;
                    }
                }

                &_desc {
                    .device-screen-table__header__icon {
                        transform: rotate(180deg);
                    }
                }
            }

            .sorting &:after {
                display: none;
            }
        }

        tbody {
            td {
                padding: 18px 6px;
                border-top: none;

                &:first-child {
                    padding-left: 16px !important;
                }

                &:last-child {
                    padding-right: 16px !important;
                }
            }

            tr {
                &.device {
                    &.d-status-false {
                        background-color: rgba(0, 0, 0, 0.02);
                    }
                    &.alarm-triggered {
                        animation: bgPulse 1s linear 0s infinite alternate-reverse;
                    }
                }
            }
        }
    }

    // cells
    .device-cell {
        color: #6a7383;
        font-size: 14px;

        &--right {
            text-align: right;
        }

        &__tag {
            border-radius: 4px;
            padding: 1px 4px 1px 6px;
            margin-right: 4px;
            font-size: 12px;
            line-height: 16px;
            display: inline-flex;
            white-space: nowrap;
            height: 18px;

            i {
                margin-left: 4px;
                width: 16px;
                height: 16px;
                background: url(../../../assets/images/check-icon.svg) no-repeat center;
            }

            &--success {
                background: #d7f7c2;
                color: #006908;
            }

            &--danger {
                background: #ffe7f2;
                color: #b3093c;

                i {
                    background-image: url(../../../assets/images/close-icon.svg);
                }
            }

            &--outlined {
                border: 1px solid #d5dbe1;
            }

            &--sonos {
                border: 1px solid #000;
                width: auto;
                height: 20px;
                background-repeat: no-repeat;
                background-position: center;

                i {
                    width: 50px;
                    margin-left: -2px;
                    background-size: 50px 50px;
                    background-image: url(../../../assets/images/sonos.svg);
                }
            }

            &--masterclock {
                border: 1px solid #000;
                width: auto;
                height: 20px;
                background-repeat: no-repeat;
                background-position: center;

                i {
                    width: 45px;
                    margin-left: -2px;
                    background-size: 42px 50px;
                    background-image: url(../../../assets/images/master-clock.svg);
                }
            }

            &--icon {
                width: 24px;
                height: 18px;
                background-repeat: no-repeat;
                background-position: center;

                &.sonos {
                    background-image: url(../../../assets/images/music-icon.svg);
                }
            }

            &--no-icon {
                padding-right: 6px;
            }

            &--battery {
                width: 24px;
                height: 14px;
                margin-top: 2px;
                border: 1px solid #aeb3b8;
                position: relative;
                background: #d7f7c2;
                color: #00a40d;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 100%;
                    background-image: linear-gradient(to right, #fff 1px, transparent 1px),
                        linear-gradient(to right, transparent 0, transparent 6px);
                    background-size: 7px 100%;
                    background-position: -7px center;
                    z-index: 2;
                    box-shadow: inset 0 0 0 2px #fff;
                    border-radius: 4px;
                }

                &.battery {
                    &-bad {
                        background: #ffe7f2;
                        color: #b3093c;
                    }
                    &-warning-bad {
                        background: #f6e6b9;
                        color: #ff9d4d;
                    }
                    &-warning {
                        background: #ffecc5;
                        color: #ffcc63;
                    }
                    &-ok-medium {
                        background: #a5d2a9;
                        color: #57bf5f;
                    }
                    &-ok {
                        background: #d7f7c2;
                        color: #00a40d;
                    }
                }

                span {
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 0;
                    transition: 1s linear 0.2s;
                    background: currentColor;
                    border-radius: 2px;
                    z-index: 1;
                }

                &.battery-offline {
                    background: #aeb3b870 !important;
                    span {
                        display: none;
                    }
                }
            }
        }

        &__device {
            min-width: 240px;
            display: flex;

            &__icon {
                flex-shrink: 0;
                margin-right: 10px;
                border-radius: 4px;
                margin: auto 10px auto 10px;
                width: 32px;
                height: 32px;
                display: inline-flex;
                align-items: center;
                justify-items: center;
                overflow: hidden;
                text-indent: 999px;
                background: rgba(106, 115, 131, 0.8) no-repeat center;

                &.screen {
                    background-image: url(../../../assets/images/screen-icon.svg);
                }

                &.camera {
                    background-image: url(../../../assets/images/cctv-icon.svg);
                }

                &.duress {
                    background-image: url(../../../assets/images/duress-icon.svg);
                }
            }

            &__details {
                &__name {
                    line-height: 20px;
                    color: #30313d;
                }
                &__status {
                    line-height: 1;
                    display: flex;
                }
                &__tag {
                    border-radius: 4px;
                    padding: 1px 4px 1px 6px;
                    margin-right: 4px;
                    font-size: 12px;
                    line-height: 16px;
                    display: inline-flex;
                    white-space: nowrap;
                    height: 18px;

                    i {
                        margin-left: 4px;
                        width: 16px;
                        height: 16px;
                        background: url(../../../assets/images/check-icon.svg) no-repeat center;
                    }

                    &--success {
                        background: #d7f7c2;
                        color: #006908;
                    }

                    &--danger {
                        background: #ffe7f2;
                        color: #b3093c;

                        i {
                            background-image: url(../../../assets/images/close-icon.svg);
                        }
                    }

                    &--outlined {
                        border: 1px solid #d5dbe1;
                    }

                    &--icon {
                        width: 24px;
                        height: 18px;
                        background-repeat: no-repeat;
                        background-position: center;

                        &.sonos {
                            background-image: url(../../../assets/images/music-icon.svg);
                        }
                    }
                }
            }
        }

        &__meta {
            min-width: 200px;
        }

        &__ip {
            min-width: 150px;
        }

        &__signal {
            &__wifi {
                margin-bottom: 6px;
            }

            border-radius: 4px;

            &-grp {
                display: flex;
                align-items: center;
                height: 4px;
                width: 180px;

                span {
                    line-height: 4px;
                    font-size: 10px;
                }
            }

            &__bar {
                position: relative;
                width: 120px;
                height: 4px;
                margin-right: 4px;
                background: #d7f7c2;
                color: #00a40d;
                border-radius: 4px;
                overflow: hidden;

                &.signal {
                    &-bad {
                        background: #ffe7f2;
                        color: #b3093c;
                    }
                    &-warning-bad {
                        background: #f6e6b9;
                        color: #ff9d4d;
                    }
                    &-warning {
                        background: #ffecc5;
                        color: #ffcc63;
                    }
                    &-ok-medium {
                        background: #a5d2a9;
                        color: #57bf5f;
                    }
                    &-ok {
                        background: #d7f7c2;
                        color: #00a40d;
                    }
                }
            }

            &__quality {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: currentColor;
                width: 0;
                transition: 1s ease all 0.4s;
                border-radius: 4px;
            }

            .wifi-experience-bar-sm {
                margin-top: 0 !important;
            }
        }

        &__activity {
            min-width: 140px;
        }

        &__uptime {
            min-width: 120px;
        }

        &__serial {
            min-width: 130px;
        }
    }

    .internet-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        @media (max-width: map-get($breakpoints, 'md')) {
            margin: 10px;
            width: 100%;
            justify-content: flex-end;
        }

        .netSpeedContainer {
            color: #999999;
            font-size: 11px;
            text-align: right;
            
            .speedtest-header {
                font-weight: 900;
            }
        }
    }
}

.filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    @include respond-below(xs) {
        flex-direction: column;
        width: 100%;
    }
}

.filter {
    position: relative;

    &-grp__add {
        padding-left: 1rem;
        color: #6A7383;
        font-weight: 600;
        opacity: 0.6;
    }

    &-grp__remove {
        padding-left: 1rem;
        color: #B3093C;
        opacity: 0.6;
        cursor: pointer;
    }

    &-section {
        display: flex;
        align-items: start;
        justify-content: space-between;
        width: 100%;
        padding: 1rem 0;

        @media (max-width: 991px) {
            flex-direction: column;
            align-items: start;
        }

        &.hidden {
            display: none;
        }
    }
    &-grp {
        height: 24px;
        border-radius: 24px;
        display: flex;
        align-items: center;
        border: 1px dashed rgba(48, 49, 61, 0.2);
        line-height: 24px;
        font-size: 12px;
        font-weight: 600;
        color: #6a7383;

        p {
            margin: 0;
        }

        &__divider {
            width: 1px;
            height: 12px;
            border-radius: 12px;
            background-color: #c1c9d2;
        }

        &.add-filter {
            position: relative;
            cursor: pointer;
        }

        &.disabled {
            opacity: 0.6;
            pointer-events: none;
        }
    }
    &-label,
    &__selected {
        padding: 0 10px;
    }
    &__selected {
        border: none;
        outline: none;
        color: #0094f7;
        padding-right: 22px;
        background: url(../../../assets/images/chevron-down-sm.svg) no-repeat calc(100% - 8px) center;
    }
    &__option {
        padding: 3px 10px;
        cursor: pointer;
        background: rgba(0, 0, 0, 0);
        white-space: nowrap;

        &:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        &--active {
            color: #0094f7;
            background: rgba(0, 148, 247, 0.05);
            pointer-events: none;
        }
    }
    &__options {
        position: absolute;
        left: 0;
        top: 100%;
        list-style: none;
        margin: 0;
        padding: 0;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        z-index: 2000;
        min-width: fit-content;
        width: 100%;
        margin: 4px 0 0;
        transform: translateY(32px);
        opacity: 0;
        transition: 0.75s ease-in-out;
        pointer-events: none;
        max-height: 260px;
        overflow-y: auto;

        &--active {
            animation: fadeInUp 0.1s ease-in-out 0s forwards;
            pointer-events: auto;
        }
    }

    &-reset {
      height: 24px;
      display: inline-flex;
      border-radius: 12px;
      font-weight: 600;
      color: map-get($colors-v2, 'secondary');
      background: map-get($colors-v2, 'container');
      transition: .3s;
      border: 1px dashed map-get($colors-v2, 'border-dark');
      font-size: 12px;
      align-items: center;
      padding: 0 12px;

      &:hover {
        color: map-get($colors-v2, 'blue-alt');
      }
    }

    &__search {
      padding: 8px 2px;
      &-input {
        width: 100%;
        border-radius: 4px;
        border: 1px solid map-get($colors-v2, 'border');
        padding-left: 8px;
        padding-right: 8px;
        font-weight: 400;
      }
    }

    &__no-result {
      padding: 4px;
      span {
        display: block;
        border-radius: 4px;
        background: map-get($colors-v2, 'red-light');
        color: map-get($colors-v2, 'red-2');
        padding: 2px 6px;
        pointer-events: none;
      }
    }
}

.materialize .styled-checkbox {
    
    [type='checkbox'] {
        & + label {
            padding-left: 28px;
            height: 16px;
            line-height: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        &.filled-in {
            & + label {
                &:before {
                    transform: rotate(0deg);
                    left: 0;
                    top: 0 !important;
                    width: 16px;
                    height: 16px;
                    border: none !important;
                    background: url(../../../assets/images/check-white.svg) no-repeat center;
                    margin: 0;
                }

                &:after {
                    width: 16px;
                    height: 16px;
                    border-radius: 4px;
                    top: 0 !important;
                }
            }

            &:not(:checked) {
                & + label:after {
                    border: 2px solid #d5dbe1;
                }
            }
        }
    }
}

.speed-req-modal {
  width: 960px;
  height: 660px;
  border-radius: 6px;

  &__container {
    padding: 8vh 2vw;
  }

  .speed-req-iframe {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  @media (max-width: 767px) {
    width: 400px;
    height: 1920px;

    .speed-req-iframe {
      left: -4px;
      right: -4px;
    }
  }

  .swal2-header, .swal2-actions, .swal2-footer {
    display: none !important;
  }
}

.custom-swal-close {
  position: fixed;
  left: calc(50% + 480px);
  top: 50px;
  margin: 0 0 0 16px;
  border: 0;
  outline: 0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: map-get($colors-v2, 'secondary' );
  cursor: pointer;
  opacity: 0;
  transition: opacity .3s ease-in-out;

  @media (max-width: 767px) {
    position: absolute;
    top: 0;
    left: 100%;
    margin: -20px 0 0 -30px;
  }
}


@keyframes bgPulse {
    from {
        background-color: rgba(200, 0, 0, 0.1);
    }
    to {
        background-color: rgba(200, 0, 0, 0);
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(32px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes growWidth {
    from {
        width: 0
    }
}

#skeleton-loader {
  .device-screen-table__header {
    gap: 4px !important;
  }
}