.screens-main-container {
    .nav-help-container {
        position: absolute;
        z-index: 9;
        right: 10px;
        margin-top: -34px;
    }
}
#live-view {
    .device-screenshots {
        overflow-y: scroll;
        max-height: calc(100vh - 280px);

        .device-preview {
            cursor: pointer;

            border: 1px solid #eeeeee;
            float: left;

            background-color: #f7f7f7;
            border-radius: 6px;

            width: 250px;
            height: 250px;
            margin: 5px;
            text-align: center;

            // display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            img.offline {
                height: 30%;
                margin-top: 70px;
            }
            img {
                height: 100%;
                width: auto;
            }
            .metadata-bar {
                top: 0px;
                width: 100%;
                left: 0px;
                position: relative;
                color: #000;
                font-size: 11px;
                padding-left: 5px;
                padding-right: 5px;
                margin-top: 0;
                padding-top: 5px;
                padding-bottom: 5px;
                border-bottom: 1px solid #eeeeee;
                display: flex;

                .left,
                .center,
                .right {
                    display: inline-block;
                }
                .center {
                    flex: 1;
                }
            }
            .refresh-bar {
                width: 100%;
                height: 10px;
                margin-top: -5px;
            }
        }
    }
}
#programs {
    padding: 20px;

    .programs-no-devices {
        .no-devices {
            width: 500px;
            text-align: center;
            background-color: #fafcff;
            display: initial;
        }
    }

    .club-control-container {
        min-height: 90px;
    }

    .programs-no-devices {
        text-align: center;
    }
    .program-nav,
    .programs-no-devices {
        display: none;

        .controls {
            display: flex;
            @media (max-width: map-get($breakpoints, 'md')) {
                display: grid;
            }
            justify-content: center;
            align-items: stretch;
        }

        width: 100%;
        max-width: 920px;
        text-align: center;
        margin: 0 auto;

        .control-wrapper {
            text-align: center;
            // margin-left: 20px;
            .btn {
                margin: 2px;
            }
        }

        fieldset {
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            border: 1px solid #999999;
            margin: 2px;
            padding: 10px;

            width: 350px;
            display: inline;

            display: flex;
            align-items: center;
            justify-content: center;
        }
        legend {
            padding-left: 5px;
            padding-right: 5px;
            font-weight: bold;
            width: auto;
            padding: inherit;
            margin-bottom: auto;
            font-size: 15px;
            line-height: 1px;
            border: none;
            border-bottom: none;
        }
        .boxing-timer {
            width: 200px;
            .timer-container {
                margin-top: 10px;
            }
        }
        .display-control {
            .control-wrapper {
                padding-left: 0px;
            }
        }
    }
}
#backup-media {
    .backup-media {
        padding: 10px;
        .download-backup-media {
            padding-right: 10px;
            padding-bottom: 5px;
        }
        li {
            padding: 3px;
        }
    }
}

#live-view,
#devices {
    padding: 20px;

    .device-status-badges {
        .badge {
            margin-right: 5px;
        }
    }
    .d-status-false {
        background-color: #fff7f7;
    }
    .dot-online,
    .dot-offline {
        height: 15px;
        width: 15px;
        border-radius: 50%;
        display: inline-block;
    }
    .dot-online {
        background-color: #6fcc41;
    }
    .dot-offline {
        background-color: #f44336;
    }

    .device {
        cursor: pointer;
    }

    .device:hover {
        cursor: pointer;
        background-color: #f9f9f9;
    }

    .devices-search-nav {
        width: 100%;
        max-width: 900px;
        margin: 0 auto;

        .search-bar {
            font-size: 14px;
        }

        input[type='search']::-webkit-search-cancel-button {
            -webkit-appearance: none;
            cursor: pointer;
            height: 12px;
            width: 12px;
            opacity: 0.75;
            margin-right: 8px;

            background-image: url('data:image/svg+xml;utf8,
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>
            ');
        }
        .hide-devices-group {
            margin-bottom: 10px;
            margin: 0px;
            margin-top: 10px;
            // background-color: blue;
        }
    }
    .search-bar-wrapper {
        .input-group {
            margin-bottom: 0px;
            width: calc(100% - 500px);
            float: left;
        }
        .btn-new-device {
            margin-top: -2px;
            margin-left: 30px;
            padding: 6px 12px;
            // visibility: hidden;
            .material-icons {
                padding-right: 5px;
            }
        }
    }
    .devices-table {
        tbody {
            tr {
                font-size: 12px;
                padding-top: 4px;
            }
        }
        .wifi-exp-col {
            width: 150px;
            @media (max-width: 480px) {
                width: 100px;
            }
        }
        .wifi-experience-container-sm {
            .color-ok {
                color: #2ac134;
            }
            .color-ok-medium {
                color: #bae774;
            }
            .color-warning {
                color: #f4d03f;
            }
            .color-warning-bad {
                color: #d35400;
            }
            .color-bad {
                color: #e74c3c;
            }
            .color-ok {
                color: green;
            }
            .code-90 {
                max-width: 90%;
            }

            display: flex;
            // padding: 3px;
            // margin-top: 3px;

            .wifi-experience-bar-sm {
                width: calc(100% - 40px);
                margin-top: 8px;
            }
            .wifi-experience-text-sm {
                text-align: center;
                width: 40px;
                font-size: 12px;
            }
        }
    }
    .no-devices-table-message {
        text-align: center;
        padding: 30px;
    }
}

.device-management__dropdown {
    // border-color: #767676;

    .select2-results__options {
        li {
            padding: 6px 12px;
        }
    }
}

.add-new-device-dialog {
    .btn {
        margin: 2px;
    }
}

.add-new-device-dialog,
.device-details-dialog,
.display-automation-dialog,
.change-wifi-networks-dialog {
    .html5-qrcode-element {
        width: auto !important;
    }
    #qr-reader > div:nth-child(1) {
        display: none;
    }
    #qr-reader {
        margin-bottom: 10px;
        margin-top: 10px;
    }
    .p-display {
        align-items: center; 
        justify-content: center; 
        position: relative; 
        box-sizing: border-box; 
        -webkit-tap-highlight-color: transparent; 
        outline: 0px; 
        border: 0px; 
        margin: 0px; 
        cursor: pointer; 
        user-select: none; 
        vertical-align: middle; 
        appearance: none; 
        text-decoration: none; 
        font-family: "Source Sans Pro", "Segoe UI", sans-serif; 
        // line-height: 1.75; 
        min-width: 64px; 
        padding: 6px 16px; 
        border-radius: 4px; 
        transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; 
        color: rgb(255, 255, 255); 
        box-shadow: 0 .125rem .3125rem #3c42571f,0 .0625rem .0625rem #00000014,inset 0 -.0625rem .0625rem #0000001f;
        font-weight: 900;
        font-size: 13px;
        text-transform: inherit;
    }
    .p-display:hover {
        // box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px -1px, rgba(0, 0, 0, 0.14) 0px 4px 5px 0px, rgba(0, 0, 0, 0.12) 0px 1px 10px 0px;
        color: white;
    }

    .p-display-on {
        border: 1px solid #2da55d;
        color: #2da55d;
    }
    .p-display-on:hover {
        background-color: #2cc265;
    }

    .p-display-off {
        border: 1px solid #99113c;
        color: #99113c;
    }
    .p-display-off:hover { 
        background-color: #b3093c;
    } 

    .location-group-container {
        .location-group {
            height: 50px;
        }
        .select2:hover {
            background-color: #f7f7f7;
        }
        .select2 {
            background-color: #fbfbfb;

            &-selection {
                border-radius: 4px;
                // border: none;
                border: 1px solid #ebeef1;
                background-color: #fff;
                transition: 0.5s ease-in-out;

                &:hover {
                    border-color: #262626;
                }

                &--default,
                &--single,
                &__rendered {
                    line-height: 33px;
                }

                &__rendered {
                    padding: 0 6px;
                    color: $btn-color-primary;
                    font-weight: 600;
                }

                &--single {
                    height: 33px;
                }

                &__clear {
                    right: 24px;
                }

                &__arrow {
                    height: 33px;
                    width: 5px;
                    right: 16px;
                }
            }

            &-dropdown {
                border-radius: 0;
            }

            &-container {
                &--open {
                    .select2-selection {
                        border-color: #262626;
                        transition: none !important;
                    }

                    &.select2-container {
                        &--above {
                            .select2-selection {
                                border-top-left-radius: 0;
                                border-top-right-radius: 0;
                                border-top-color: transparent !important;
                            }
                        }
                        &--below {
                            //.select2-selection {
                            //    border-bottom-left-radius: 0;
                            //    border-bottom-right-radius: 0;
                            //    // border-bottom-color: transparent !important;
                            //}
                        }
                    }
                }
            }
        }
    }

    // Select picker dropdowns...
    .selectpicker-border,
    .equipment-round,
    .display-orientation,
    .display-resolution,
    .scheduled-reboot,
    .preferred-resolution,
    .recording-mode {
        border-radius: 0px;
        margin-bottom: 3px;
        margin-top: 3px;
        width: 100% !important;
        padding: 1px;
    }

    .selected-ssid {
        border-bottom: 0px !important;
        // Smaller search icon..
        .bs-searchbox:after {
            font-size: 15px;
            top: 10px;
            left: 15px;
        }
        .dropdown-toggle.btn-default {
            border: 1px solid #999999;
            border-radius: 0;
            width: calc(100% + 27px);
        }

        li {
            a {
                // margin: 0px;
                padding: 0px;
                padding-left: 10px;
                padding-right: 10px;
            }
        }
        .wifi-option {
            width: 100%;
            .row {
                padding: 0px;
                p {
                    padding: 0px;
                    margin: 0px;
                    padding-top: 15px;
                    font-weight: bold;
                }
                .macaddr {
                    margin: 0px;
                    padding: 0px;
                    float: left;
                    font-size: 11px;
                    font-family: monospace;
                }
            }
            .security-stats {
                width: 50px;

                .wifi-ssid-secured {
                    height: 20px;
                    width: 15px;
                    float: left;
                    margin-top: 23px;

                    .material-icons {
                        font-size: 15px;
                    }
                }
                .wifi-strength-wrapper {
                    height: 20px;
                    width: 20px;
                    float: left;
                    margin-top: 5px;
                    margin-left: -7px;

                    .wifi-strength {
                        height: 24px;
                        position: absolute;
                        width: 24px;
                        margin-top: 10px;
                        -webkit-transform: scale(0.25, 0.25);
                        -moz-transform: scale(0.25, 0.25);
                    }
                }
            }
        }
    }

    .ssid-password {
        border: 1px solid #999999;
        border-radius: 0px;
    }
    .toggle-ssid-password {
        position: absolute;
        right: 16px;
        top: 1px;
        height: 28px;
        border-radius: 0px;
        box-shadow: none !important;
    }
    .ssid-info {
        font-size: 10px;
        font-style: italic;
        .badge {
            font-size: 9px;
            margin-left: 10px;
        }
    }
    .bg-wifi-2 {
        background-color: #ff9800;
        font-size: 9px;
        margin-left: 10px;
    }
    .bg-wifi-5 {
        background-color: #1f91f3;
        font-size: 9px;
        margin-left: 10px;
    }
    .device-toggles:first-child {
        padding-top: 20px !important;
    }
    .device-toggles {
        // padding-top: 20px !important;
        // padding-left: 15px !important;
        .col-sm-5 {
            padding-top: 5px;
        }
    }
    .screens-advanced-settings {
        position: relative;
        top: 10px;
        margin-top: 6px;

        // left: 150px;
        // left: calc(100% - 312px);
        left: calc(100% - 150px);

        z-index: 9999;
        background: white;
    }
    .t-screens-advanced-settings {
        overflow: hidden;
        margin-top: -5px;
        height: 0px;
    }
    .toggle-device-advanced {
        height: auto;
        //@media only screen and (max-width: 767px) {
        //    height: 260px;
        //}
    }
    .color-ok {
        color: #49d100;
    }
    .color-ok-medium {
        color: #bae774;
    }
    .color-warning {
        color: #f4d03f;
    }
    .color-warning-bad {
        color: #d35400;
    }
    .color-bad {
        color: #e74c3c;
    }
    .code-90 {
        max-width: 90%;
    }

    code {
        font-family: 'Ubuntu Mono', 'Courier New', monospace;
        color: rgb(199, 37, 78);
        background-color: rgb(249, 242, 244);
        white-space: normal;
        overflow-wrap: break-word;
        font-size: 1em;
        margin-right: 4px;
        display: inline-block;
        padding: 2px 4px;
        border-radius: 2px;
    }

    .cpu-used-percent {
        display: inline;
    }
    .cpu-used-percent.color-ok {
        color: #49d100;
    }
    .cpu-used-percent.color-warning {
        color: orange;
    }
    .cpu-used-percent.color-bad {
        color: #e74c3c;
    }

    .wifi-experience-container {
        display: flex;
        padding: 3px;
        .wifi-experience-bar {
            width: calc(100% - 40px);
            margin-top: 5px;
        }
        .wifi-experience-text {
            text-align: center;
            width: 40px;
            font-size: 12px;
        }
    }

    .smallText {
        font-size: 11px;
        font-style: italic;
    }
    .ssidName {
        margin-bottom: 3px;
    }
    .light-info {
        margin-top: 20px;
        margin-bottom: 0px;

        background-color: #ececec;
        color: black !important;
        zoom: 0.8;
        font-style: italic;

        .btn {
            margin-top: 10px;
            font-style: normal;
        }
    }
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border: 1px solid #999999;
        margin: 2px;
        padding: 10px;

        .row {
            padding: 2px;
        }
        input[type='password'],
        input[type='text'],
        input[type='number'] {
            padding: 6px;
            width: 100%;
            border: 1px solid #ebeef1;
            border-radius: 4px;
            height: 33px;
            transition: 0.5s ease-in-out;
            color: #30313d;

            &:hover,
            &:focus {
                border-color: #262626;
            }
        }
        label {
            font-weight: 400;
        }
        .help-subtext {
            font-size: 10px;
            font-style: italic;
        }
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }

    select {
        padding: 5px;
        width: 100%;
    }
    .display-name {
        padding: 5px !important;
    }

    #deviceIDInput {
        .field-wrapper {
            position: relative;
            width: 100%;
        }
        .form-group {
            // min-width: 300px;
            // width: 50%;
            // margin: 8em auto;
            display: flex;
            border: 1px solid $border-color;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        .letter {
            height: 43px;
            border-radius: 0;
            border: none;
            text-align: center;
            // max-width: calc((100% / 10) - 1px);
            flex-grow: 1;
            flex-shrink: 1;
            flex-basis: calc(100% / 10);
            outline-style: none;
            padding: 0 !important;
            font-size: 18px;
            font-weight: bold;
            text-transform: uppercase;
            color: $slate-600;
        }
        .letter + .letter {
            border-left: 1px solid $border-color;
        }
        @media (max-width: 480px) {
            .letter {
                font-size: 16px;
                padding: 2px 0;
                height: 38px;
            }
        }
    }

    &--v2 {
      padding: 0 20px 20px;

      fieldset {
        border: 0
      }

      .nav-tabs {
        padding-top: 20px;
        background: #fff;
        position: sticky;
        z-index: 5;
        top: 40px;
        left: 0
      }

      .tab-content {
        padding-bottom: 0;
        height: 600px;
      }

      &.device-details-dialog {
        .tab-content {
          margin: 0 -20px;
          padding-left: 20px;
          padding-right: 20px;
        }
        &--camera {
          .tab-content {
            // height: 530px;
            overflow-x: visible;
            // overflow-y: auto;

            .select2-container,
            .dropdown-menu.open,
            .dropdown-menu {
              z-index: 3;
            }
          }
        }
        &--duress {
          .tab-content {
            height: 250px
          }
        }

        // &--screen, &--camera {
        //    .device-dialog {
        //       &__details {
        //         overflow-y: scroll;
        //         overflow-x: hidden;
        //       }
        //    }
        // }

      }

      .device-toggles:first-child {
        padding-top: 0 !important
      }

      .nav-tabs > li {
        &.device-dialog__advanced-tab {
          margin-left: auto;
          margin-right: 0;

          & > a {
            margin-right: 0;
          }
        }
        & > a {
          font-size: 14px;
        }
      }
    }

    &__container {
      padding-top: 0;
      padding-bottom: 0;

      .swal2-header,
      .swal2-actions {
        position: sticky;
        z-index: 2;
        left: 0;
      }

      .swal2-header {
        top: 0;
        z-index: 5;
      }

      .swal2-actions {
        background: #fff;
        bottom: 0;
        border-radius: 0 0 4px 4px;
      }

      .swal2-content {
        z-index: auto
      }

      @media (max-width: 767px) {
        padding: 0;

        .swal2-header,
        .swal2-actions {
          flex: 0;
        }

        .swal2-header,
        .swal2-actions,
        .swal2-popup {
          border-radius: 0 !important;
        }

        .swal2-modal {
          height: 100dvh;
          width: 100vw !important;
          overflow-y: auto;
        }
        
        .swal2-content {
          flex: 1 0;
          overflow-y: auto;
          overflow-x: hidden;
        }

        .tab-content {
          height: auto !important;
        }
        .nav-tabs {
          top: 0;
        }
        .motion-threshold {
          div[class^="col-"] {
            width: 100%;
          }
          .slider-wrapper {
            padding-top: 12px;
            margin: 0 0 30px;
          }
        }
        .device-dialog__advanced {
          b {
            display: block;
            margin: 0 0 6px;
          }
        }
      }

      #compliance-link {
        margin-right: auto;
      }
    }
}

.device-compliance-modal {
  padding: 24px 20px;
  font-size: 14px;
  
  h6 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 16px;
  }

  &__details {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    flex-wrap: wrap;

    &-row {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 16px;
    }

    &-col {
      font-size: 12px;
      font-weight: 600;

      p {
        margin: 0
      }

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
      
      }
    }

    &-cerifications {
      display: flex;
      flex-wrap: wrap;
      width: 280px;
      gap: 8px 4px;
      justify-content: space-between;
      padding-top: 30px;

      @media (max-width: 767px) {
        padding-top: 16px;
      }
    }
  }

  &__icon {
    height: 24px;
    opacity: .7;

    &-1,
    &-2 {
      height: 18px;
      margin-top: 2px;
    }

    &-11 {
      height: 32px
    }
  }

  &__badge {
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid map-get($colors-v2, 'primary');
    display: inline-block;
    padding: 0 6px;
    margin: 0 0 6px !important
  }

  &__giteki {
    display: flex;
    align-items: center;
    margin-bottom: 11px;

    div {
      padding-left: 6px;
    }
    
    span {
      display: block;
    }
  }

  &__highlight {
    margin: 20px 0;
    padding: 16px 0;
    border-top: 1px solid map-get($colors-v2, 'border');
    border-bottom: 1px solid map-get($colors-v2, 'border');

    p {
      &:last-child {
        margin: 0
      }
    }
  }

  &__content {
    line-height: 1.5;
  }
}

.ics {
    #order {
        .light-info {
            background-color: #ececec;
            color: black !important;
        }
        .ics-displays {
            .thumbnail {
                min-height: 440px !important;
            }
        }
    }
}
