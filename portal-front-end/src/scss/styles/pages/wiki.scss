#wikiOverlay {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 1090;
    display: none;
    background-color: black;
    filter: alpha(opacity=30);
    opacity: 0.5;
}
* html #wikiOverlay {
    position: absolute;
}
#wikiContainer {
    // display: block;
    // border: none;
    // height: calc(100vh - 120px);
    // width: 100%;
}

#wikiContent {
    padding-top: 0;

    .nopadding {
        padding: 0;
    }
}

#wiki-loader {
    height: 100vh;
    position: absolute;
    left: 0;
    top: 0;
    background: #fff;
    width: 100%;

    .main-content {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        background: #fff;
        overflow: auto;
        padding: 20px;
    }

    @media (min-width: 768px) {
        .main-content {
            padding: 48px 48px 0;
            overflow-y: visible;
            height: 100vh;
        }
    }

    h2.main-title {
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 42px;
        color: #30313d;
        margin: 0;
    }

    h3.subtitle-text {
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-top: 40px;
        max-width: 700px;
        color: #30313d;
    }

    .home-view {
        margin-top: 47px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }

    #home-header-container {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }

    #home-header-container .row {
        max-width: 1260px;
        width: 100%;
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end;
    }

    #divAllTopics {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
        -webkit-box-align: stretch;
        -ms-flex-align: stretch;
        align-items: stretch;
        gap: 32px;
        justify-content: flex-start;
        max-width: 1260px;
        width: 100%;
    }

    #divAllTopics .topic-items[data-topickeyword='1'] {
        background-color: #5393f4;
    }

    #divAllTopics .topic-item[data-topickeyword='1'] .svg-wrap {
        border: 1px solid #5393f4;
    }

    #divAllTopics .topic-item[data-topickeyword='1'] .svg-wrap .icon svg {
        fill: #5393f4;
    }

    #divAllTopics .topic-items[data-topickeyword='2'] {
        background-color: #75a139;
    }

    #divAllTopics .topic-item[data-topickeyword='2'] .svg-wrap {
        border: 1px solid #75a139;
    }

    #divAllTopics .topic-item {
        border-radius: 6px;
        border: 1px solid #ebeef1;
        background: #fff;
        padding: 16px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
        gap: 16px;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        cursor: pointer;
        width: 100%;
    }

    @media (min-width: 576px) {
        #divAllTopics .topic-item {
            width: auto;
        }
    }

    #divAllTopics .topic-item .svg-wrap {
        width: 48px;
        height: 48px;
        border-radius: 6px;
        background: #fff;
    }

    #divAllTopics .topic-item .svg-wrap svg {
        margin: 8px;
    }

    #divAllTopics .topic-item .svg-wrap {
        width: 52px;
        height: 52px;
        border-radius: 6px;
        border-width: 1px;
    }

    #divAllTopics .topic-item .svg-wrap svg {
        margin: 8px;
    }

    #divAllTopics .topic-item .topic-text {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
        gap: 10px;
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0px;
        flex: 1 0 0;
        width: 295px;
    }

    #divAllTopics .topic-item .topic-text a {
        color: #6a7383;
        font-size: 16px;
        letter-spacing: 0.25px;
        font-style: normal;
        font-weight: 600;
        line-height: 100%;
        text-transform: uppercase;
        margin-bottom: 2px;
    }

    #divAllTopics .topic-item .topic-text a:hover {
        text-decoration: none;
    }

    #divAllTopics .topic-item .topic-text ul {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    #divAllTopics .topic-item .topic-text ul li {
        padding-left: 24px;
        background: url(../../../assets/images/subtopic-chevron-right.svg) 0 50% no-repeat;
        line-height: 16px;
    }

    #divAllTopics .topic-item .topic-text ul li:not(:last-child) {
        margin-bottom: 8px;
    }

    #divAllTopics .topic-item .topic-text ul li a {
        color: #30313d;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
        text-transform: none;
        display: inline-block;
    }

    #divAllTopics .topic-item .topic-text ul li a:hover {
        color: #2196f3;
    }

    ul#topic-page-nav {
        list-style-type: none;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        padding: 12px 0;
        margin-top: 6;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
        gap: 16px;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        margin-bottom: 0;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        border-bottom: solid 1px #ebeef1;

        @media (min-width: 768px) {
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -ms-flex-direction: row;
            flex-direction: row;
            gap: 28px;
            border-bottom: none;
        }

        li a {
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            color: #6a7383;
        }
    }

    #topic-page-wrap {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;

        @media (min-width: 768px) {
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -ms-flex-direction: row;
            flex-direction: row;
        }

        #filters {
            gap: 12px;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            -webkit-box-align: start;
            -ms-flex-align: start;
            align-items: flex-start;
            width: 100%;
            margin-top: 20px;
            border-top: solid 1px #ebeef1;
            padding-top: 20px;

            @media (min-width: 768px) {
                width: 208px;
                padding: 24px 0;
                -ms-flex-negative: 0;
                flex-shrink: 0;
                -webkit-box-flex: 0;
                -ms-flex-positive: 0;
                flex-grow: 0;
            }

            ul {
                list-style-type: none;
                padding: 0;
                margin: 0;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-orient: vertical;
                -webkit-box-direction: normal;
                -ms-flex-direction: column;
                flex-direction: column;
                gap: 12px;

                padding-bottom: 20px;
                border-bottom: solid 1px #ebeef1;
                width: 100%;
                margin-bottom: 0;

                @media (min-width: 768px) {
                    padding-bottom: 28px;
                    margin-bottom: 16px;
                    border-bottom: solid 1px #ebeef1;
                }

                li {
                    line-height: 16px;
                    background: url(../../../assets/images/subtopic-chevron-right.svg) 0 50% no-repeat;
                    padding-left: 24px;
                }
            }
        }
    }

    #articles-list-wrap {
        min-height: 100vh;
        word-wrap: break-word;

        @media (min-width: 768px) {
            border-left: solid 1px #ebeef1;
            padding: 8px 40px 8px 40px;
        }

        #breadcrumb {
            padding: 20px 0;

            @media (min-width: 768px) {
                padding: 20px 24px;
            }

            ul {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                list-style-type: none;
                padding: 0;
                margin: 0 0 26px;

                li {
                    line-height: 12px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &:not(:first-child):before {
                        content: '/';
                        padding: 0 12px;
                        opacity: 0.2;
                    }
                }
            }
        }
    }
}
