.notifications-page {
  .notif-header-container {
    height: 4rem;
    hr {
      border-top: 2px solid #dcdcdc;
    }
    .notif-settings {
      button {
        border: none;
        background: none;
        margin-top: 0.5rem;
        .notif-settings-text {
          font-weight: 700;
          font-size: 14px;
          margin-right: 0.5rem;
          line-height: 15px;
          color: #616C82;
        }
        .notif-settings-icon {
          font-size: 20px;
          i { vertical-align: middle;}
        }
      }
    }
  }
  .notif-heading {
    font-family: 'Source Sans Pro', sans-serif;
    font-style: normal;
    font-weight: 700;
    color: #1C2A4B;

    font-size: 18px;
    padding-top: 10px;
  }
}

.notifs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4rem;

  @include respond-below(lg) {
    flex-wrap: wrap-reverse;
    gap: 1rem;
  }
}

.notification-filters {
  font-family: $sidebar-font-family;
  background: #f8fafb;
  overflow: hidden;
  display: inline-block;
  position: relative;
  max-width: 350px;
  width: 100%;

  @include respond-below(md) {
    max-width: 100vw;
  }

  .menu {
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    .list {
      list-style: none;
      padding-left: 0;

      li {
        background: #ffffff;
        margin-bottom: 1rem;
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);

        &.active {
          > :first-child {
            span {
              font-weight: bold;
              // color: #2196F3 !important;
            }
            .material-icons {
              // color: #2196F3 !important;
              color: black !important;
            }
          }
          > :first-child:after, :first-child:before {
            // color: #2196F3 !important;
            color: black !important;
          }
        }
      }
      // .header {
      //   margin-top: 20px;
      //   color: #a9aeb8;
      //   font-size: 12px;
      //   font-weight: bold;
      //   text-transform: uppercase;
      //   letter-spacing: 0.5px;
      //   padding: 5px 16px;
      // }

      i.material-icons {
        margin-top: 7px;
        margin-right: 7px;
        font-size: 18px;
        color: #555b62;
      }

      .menu-toggle {
        &:after, &:before {
          position: absolute;
          top: calc(50% - 9px);
          right: 17px;
          // font-size: 19px;
          font-size: 16px;

          @include transform(scale(0));
          @include transition(all .3s);
        }

        &:before {
          content: '\25BC'; // ▾ - U+25BC SMALL BLACK DOWN-POINTING TRIANGLE
          font-size: 11px;
          @include transform(scale(1));
        }

        &:after {
          content: '\25B2'; // ▴ - U+25B2 SMALL BLACK UP-POINTING TRIANGLE
          font-size: 11px;
          @include transform(scale(0));
        }
      }

      .menu-toggle.toggled {
        &:before {
          @include transform(scale(0));
        }

        &:after {
          @include transform(scale(1));
        }
      }

      a {
        color: #747474;
        position: relative;
        display: inline-flex;
        vertical-align: middle;
        width: 100%;
        padding: 4px 15px;

        &:hover,
        &:active,
        &:focus {
          text-decoration: none !important;
        }

        small {
          position: absolute;
          top: calc(50% - 7.5px);
          right: 15px;
        }

        span {
          margin: 7px 0;
          color: #555b62;
          font-weight: bold;
          font-size: 14px;
          overflow: hidden;
        }
      }

      .fa {
        padding-left: 5px;
        font-size: 13px;
      }

      .ml-menu {
        list-style: none;
        padding-left: 0;

        span {
          font-weight: normal;
          font-size: 14px;
          margin: 3px 0 1px 6px;
        }

        li:hover {
          background-color: rgba(179, 179, 179, 0.16)
        }
        li {
          transition: 0.1s;
          margin: 0;
          box-shadow: none;
          a {
            padding-left: 55px;
            padding-top: 5px;
            padding-bottom: 5px;
          }

          &.active {
            a.toggled:not(.menu-toggle) {
              // font-weight: 600;
              // margin-left: 5px;
              background-color: rgba(161, 194, 250,.16);
            }
          }

          .ml-menu {
            li {
              a {
                  padding-left: 80px;
              }
            }

            .ml-menu {
              li {
                a {
                    padding-left: 95px;
                }
              }
            }
          }
        }
      }
    }
  }
}

#notif-read-filters {
  display: flex;
  gap: 1rem;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
  background: none;
  box-shadow: none;

  button {
    flex-grow: 1;
    flex-basis: 0;
  }
}

.notifications-list-container {
    height: calc(100vh - 180px);
    overflow-y: scroll;
    @media (max-width: map-get($breakpoints, 'lg')) {
        height: 100%;
    }
}

.notifications-list {
  .notif-thumbnail {
    width: 40px;
    height: 40px;
    img {
      border: 1px solid #eeeeee;
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 10px;
    }
  }

  .notif-row {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 16px;
    gap: 12px;
    border-bottom: 1px solid #EEEEEE;
    &:hover {
      background-color: #f7f7f7;
    }
  }

  .notif-unread-background {
    background: rgba(36, 151, 244, 0.06);
  }
  .notif-unread-text {
    color: #1C2A4B !important;
  }
  .notif-unread-dot {
    font-size: 0.7rem;
    color: #2D87E9;
    vertical-align: top;
  }

  .notif-title {
    font-family: 'Source Sans Pro', sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    align-items: flex-end;
    color: #747474;
    cursor: pointer;
  }

  .notif-time-origin {
    font-family: 'Source Sans Pro', sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 11px;
    line-height: 12px;
    display: flex;
    align-items: center;
    color: #898989;
    margin-bottom: 15px;
  }

  .notif-views {
    display: flex;
    align-items: flex-end;
    font-family: 'Source Sans Pro', sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 11px;
    line-height: 12px;
    color: #a5a5a5;
    transition: color 200ms;
    max-width: 80px;

    &:hover {
      color: #696969;
    }
  }
}

.notification-label {
  height: 12px;
  width: 12px;
  border-radius: 5px;
}