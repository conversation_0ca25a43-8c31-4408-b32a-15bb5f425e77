.review-management {
    .no-reviews {
        .col-sm-12 {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .reviews-example-placeholder {
            img {
                max-width: 800px;
            }
        }
    }

    // #reportrange {
    //     background: #fff;
    //     padding: 8px;
    //     line-height: 18px;
    //     border-radius: 4px;
    //     border-width: 0 1px 4px;
    //     border: 1px solid #ebeef1;
    //     cursor: pointer;
    //     float: right !important;
    //     transition: 0.5s ease-in-out;
    //     color: #6a7383;

    //     .material-icons {
    //       font-size: 15px;
    //       position: absolute;
    //     }
    //     span {
    //         padding-left: 23px;
    //         padding-right: 5px;
    //         font-weight: 600;
    //     }
    //     @include respond-below(xs) {
    //         margin-top: 1rem;
    //     }

    //     &:hover,
    //     &:focus,
    //     &:active {
    //         box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.12) !important;
    //         // border-color: map-get($colors-v2, 'border-dark');
    //     }
    // }

    .agreements-sources-title {
        text-align: center;
        margin-top: -10px;
        margin-bottom: -5px;
    }
    .reviews-tags {
        padding: 10px;
    }
    .tags {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 2rem;

        .badge {
            font-weight: 300;
        }
        .badge .count {
            margin-left: 1rem;
            font-weight: 900;
        }
    }
    .tags > span {
        cursor: pointer;
    }
    .reviews-overview {
        & .card {
            .body {
                max-height: 190px;
                min-height: 190px;
            }
            &.modified {
              display: flex;
              flex-direction: column;
              margin: 0;

              .body {
                max-height: 100%;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;

                &--no-padding {
                  padding-bottom: 0;
                }
              }
            }
            .stat-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 150px;

                .number {
                    font-size: 5rem;
                }

                .stars > span {
                    font-size: 1.75rem !important;
                }

            }
            #reviews-ratings-graph svg {
                // margin-top: -10px;
                height: 150px;
            }
            #sources-graph svg {
                height: 145px;
            }
            .filter-out-responded {
              padding: 1px 4px 1px 6px;
              border-radius: 4px;
            }
        }

        &__row {
          display: flex;
          flex-wrap: wrap;
          gap: 24px;
          margin-bottom: 24px;

          .modified {
            flex-shrink: 0;
            min-width: 200px;
            width: 15%;
            overflow: hidden;
            
            .body {
              display: flex;
              flex-direction: column;
            }

            &.lg {
              flex: 1 0 30%;

              &.review-sources {
                flex: 1 0 22%
              }
            }

            @media (max-width: 600px)  {
              width: 100%;

              &.lg {
                flex: 1 0 100%;
  
                &.review-sources {
                  flex: 1 0 100%
                }
              }
            }

            .agreements-sources-title {
              font-size: 1.8rem;
              line-height: 1;
              color: $primary-text-color;
              padding-bottom: .5em;
              border-bottom: 1px solid $border-color;
              text-align: left;
              margin: 0;
            }
          }
        }
    }

    #reviews-table-container {
        display: flex;
        flex-direction: column;
    }

    .auto-publish-notif > div {
        display: flex;

        p {
            margin: 0px;
        }
        a {
            color: white;
            cursor: pointer;
        }
    }

    #reviews-table {
        margin: 0 !important;
    }
    
    .empty-table {
        text-align: center;
        background-color: #f9f9f9;
        padding: 10px;
        font-weight: 700;
    }

    .save-overlay {
        display: none;
        width: 100vw;
        height: 100vh;
        top: 0px;
        left: 0px;
        background-color: rgba(100, 100, 100, 0.3);
        position: fixed;
        z-index: 9999;

        .spinner-container {
            position: fixed;
            opacity: 1;
            width: 500px;
            padding: 20px;
            height: 200px;
            background-color: white;
            text-align: center;
            margin-top: 200px;
            left: calc(50% - 250px);
            .preloader {
                margin-top: 10px;
            }
        }
    }

    section.content {
      @media screen and (max-width: 991px) {
        margin-right: 15px !important;
        margin-left: 15px !important;
      }
    }
    
}

.review-response-dialog {
    .use-canned-response {
        .dropdown-menu.open {
            max-width: 100%;
            li a {
                span {
                    white-space: normal;
                }
            }
        }
        .inner.open {
            overflow-x: hidden;
            // max-height: calc(100vh - 100px) !important;
        }
    }
}

.reviews-settings-dialog, .reviews-filter-dialog {
    .canned-response-list-container {
        border-bottom: 2px solid $border-color;
        max-height: 200px;
        overflow-y: scroll;
        margin-bottom: 6px;
        margin-top: 3px;
    }

    .canned-responses.list-group-item:first-child {
        border-top: 0;
    }
    .canned-responses.list-group-item:last-child {
        border-bottom: 0;
    }
    .canned-responses.list-group-item {
        border-left: 0;
        border-right: 0;
        border: 1px solid $border-color;

        span {
            max-width: 100%;
            word-wrap: break-word;
            white-space: normal;
            text-align: left;
            color: $slate-600;
        }
    }
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        margin: 2px;
        padding: 10px;
    
        .row {
            padding: 2px;
        }
        input[type=password], input[type=text], input[type=number] {
            padding: 4px;
            width: 100%;
        }
        label {
            font-weight: 400;
            font-size: 14px !important;
        }
        .help-subtext {
            font-size: 10px;
            font-style: italic;
        }
    
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }

    #minimum-reviews, #minimum-words {
        display: inline;
        width: 60px;
        text-align: center;
        border-bottom: 1px solid rgba(100, 100, 100, 0.493);
    }
    .form-check[disabled] {
        label, input {
            color:rgba(0, 0, 0, 0.3);
        }
    }
}

.print {
    &-review {
        margin: 20px 0;
    
        &-loading {
            animation: loading 2s linear infinite;
        }
    }
    &-design {

        &-list {
            display: grid;
            gap: 10px;
            padding: 0;
            margin: 0 0 10px;
            max-height: 386px;
            overflow-y: auto;
        }

        &-header {
            margin: 16px 0;
        }
    }
    
    &-template {
        border: 0;
        background: none;
        border: 1px solid #ccc;
        padding: 0;

        .flex {
            display: flex;
            align-items: center;
        }

        img {
            flex-shrink: 0;
            width: 120px;
            height: 120px;
            margin: 0;
        }

        .design-text {
            padding: 10px 20px;
            text-align: left;
        }
    }
}

@keyframes loading {
    to {
        transform: rotate(360deg);
    }
}


.review-source-chart {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  flex-grow: 1;
  flex-shrink: 0;

  &__wrapper {
    padding: 0;
  }

  &__legends {
    list-style: none;
    white-space: nowrap;
  }
}

.member-topics {
  padding: 32px;
  margin-bottom: 24px !important;

  h4 {
    font-size: 20px;
    color: map-get($colors-v2, 'primary');
    margin: 0 0 32px;
  }

  &__tags {
    max-width: 1200px;
    margin: 0 auto;
    gap: 8px !important;

    .badge {
      border-radius: 10px;
      border-color: transparent;
      font-size: 12px;
      line-height: 1.45;
      text-transform: uppercase;
      font-weight: 600 !important;

      .count {
        margin: 0 0 0 4px !important;
      }
    }
    .badge-green-outline {
      background: map-get($colors-v2, 'green-light');
    }
    .badge-red {
      background: map-get($colors-v2, 'red-2');
    }
  }
}

.review-report-date-range {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    margin-top: -10px;
}