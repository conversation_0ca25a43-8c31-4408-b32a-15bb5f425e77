#display-admin {
    &>.container-fluid {
        padding: 32px;
        background-color: #fff;
        overflow: auto;

        @media (max-width: 767px) {
            padding: 16px;
        }
    }

    .dt-buttons {
        margin-top: -38px;
        margin-left: 10px;
    }

    .ph-table-footer {
        margin-top: 2rem;
    }

    .json-preview {
      font-family: monospace;
      white-space: pre-wrap;        // allow newlines and wrapping
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;        // limit to 2 lines
      max-height: 2.6em;            // line-height * 2 for fallback
      line-height: 1.3em;
      text-overflow: ellipsis;
      background-color: #f8fafc;
      padding: 2px;
    }
}
