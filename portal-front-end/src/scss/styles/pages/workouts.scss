.workouts {

    .search-bar-wrapper {
        display: block;
        margin-left: auto;
        margin-right: auto;
        max-width: 500px;
    }

    .workouts-container {

        overflow-y: auto;

        @media (min-width: map-get($breakpoints, 'md')) {
            // Only 'contain' store items when on desktop...
            height: calc(100vh - 300px);
            overflow-y: scroll;
        }

        .item:hover {
            .item-frame {
                background-color: #f5f5f5;
            }
        }

        .current-period {
            background-color: #f4faff;
        }

        .item {
            padding: 0px;
            cursor: pointer;

            .workout-icons {
                svg {
                    max-height: 75px;
                }
            }
            .workout-date {
                font-size: 11px;
                font-style: italic;
            }
            .item-frame {

                margin: 4px;
                border: 1px solid #e8e8e8;
                padding: 10px;

                min-height: 190px;

                h4 {
                    width: 100%;
                    text-align: left;
                    padding: 10px;
                    min-height: 50px;
                    font-size: 15px;
                }

            }
        }
    }
}