#cost-breakdown {
    .saas-billing-report h4 {
        color: #333;
        margin-bottom: 20px;
    }

    .saas-billing-report table {
        width: 100%;
        margin-top: 20px;
        border-collapse: collapse;
    }

    .saas-billing-report table thead th {
        border-bottom: 2px solid #e7e7e7;
        padding: 10px;
        text-align: left;
    }

    .saas-billing-report table tbody td {
        padding: 10px;
        vertical-align: middle;
    }

    .saas-billing-report .charge-breakdown {
        margin-top: 30px;

        p {
            margin: 5px 0;
        }

        li {
            padding-bottom: 4px;
        }
    }

    .loading-indicator {
        text-align: center;
        padding: 20px;
        font-size: 16px;
        color: #999;
    }
    .no-devices-table-message {
        text-align: center;
        padding: 30px;
    }

    .table-nav-container {
        @include respond-above('md') {
            display: flex;
            align-items: flex-end;
        }

        .search-nav {
            @include respond-above('md') {
                display: flex;
                justify-content: flex-end;
            }
            @include respond-below('sm') {
                display: block;
                .dropdown {
                    margin-bottom: 10px;
                }
            }
        }
    }
}


.dots-loader {
    width: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: width 0.5s ease-in-out;

    &.on {
        width: 50px;
    }

    .dots {
        width: 8px;
        aspect-ratio: 1;
        border-radius: 50%;
        animation: dotsLoaderKeyFrames 1s infinite linear alternate;
    }
}

@keyframes dotsLoaderKeyFrames {
    0% { box-shadow: 12px 0 rgba(255, 255, 255, 0.5), -12px 0 rgba(255, 255, 255, 1); background: rgba(255, 255, 255, 0.5); }
    33% { box-shadow: 12px 0 rgba(255, 255, 255, 0.5), -12px 0 rgba(255, 255, 255, 1); background: rgba(255, 255, 255, 1); }
    66% { box-shadow: 12px 0 rgba(255, 255, 255, 1), -12px 0 rgba(255, 255, 255, 0.5); background: rgba(255, 255, 255, 1); }
    100% { box-shadow: 12px 0 rgba(255, 255, 255, 1), -12px 0 rgba(255, 255, 255, 0.5); background: rgba(255, 255, 255, 0.5); }
}

#SaaSBilling .unauthorized-access {
    img {
        max-width: 100%;
        pointer-events: none;
        filter: grayscale(100) blur(2px) opacity(0.5);
    }
}

#SaaSBilling .container-fluid {
    padding: 32px;
    background-color: #fff;
    overflow: auto;

    @media (max-width: 767px) {
        padding: 16px;
    }
}

#SaaSBilling #overview {
    .isLoading {
        canvas {
            opacity: 0.1;
            filter: grayscale(100%);
            pointer-events: none;
            animation: 1.5s ease-in-out 0.5s infinite normal none running canvasLoader;
        }
    }

    @keyframes canvasLoader {
        0%,
        100% {
            opacity: 0.1;
        }
        50% {
            opacity: 0.3;
        }
    }
}

.service-pricing {
    &__tab-panel {
        margin-top: -15px;
    }
    &__wrapper {
        display: flex;

        @media (max-width: 767px) {
            flex-direction: column;
        }
    }
    &__content {

        padding: 10px 5px 40px;
        font-size: 16px;
        color: map-get($colors-v2, 'primary');
        line-height: 1.5;
        flex-grow: 1;

        @media (max-width: 991px) {
            padding: 10px 20px 32px;
        }

        @media (max-width: 767px) {
            padding: 10px 0 32px;
        }

        & > * {
            margin-bottom: 24px;
        }

        h1 {
            font-size: 32px;
        }

        h2 {
            font-size: 30px;
        }

        h3 {
            font-size: 28px;
        }

        h4 {
            font-size: 20px;
        }

        h5 {
            font-size: 18px;
        }

        h6 {
            font-size: inherit;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            padding-top: 10px;
            font-weight: 600;
            line-height: 1.1;
        }

        ul,
        ol {
            li {
                margin-bottom: 20px;

                &:last-child {
                    margin-bottom: 0;
                }

                strong {
                    font-weight: 600;
                    display: block;
                }
            }
        }

        .alert-warning {
            padding: 16px;
            display: inline-flex;
            max-width: 100%;
            background: #fcf3cd !important;
            color: #826c20 !important;
            border-radius: 4px;
        }

        .btn-outline-secondary {
            background: transparent;
            border: 1px solid currentColor !important;
            color: map-get($colors-v2, 'secondary');
            font-size: 14px;
            font-weight: 600;
            line-height: 20px; /* 142.857% */
            padding: 4px 8px;
        }
    }
    &__table {
        width: 100%;
        min-width: 320px;
        &-wrapper {
            padding: 16px 32px 24px;
            overflow: auto;
            max-width: 100%;

            @media (max-width: 767px) {
                padding: 16px 0 24px;
            }
        }
        thead {
            th {
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
                text-transform: uppercase;
                padding: 12px 16px;
                background: map-get($colors-v2, 'container');
                border-bottom: 1px solid #e3e8ee;
            }
        }

        td,
        th {
            &:last-child {
                text-align: right;
            }
        }

        td {
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.25;
            border-bottom: 1px solid #e3e8ee;

            p {
                padding: 0 0 0 32px;
                color: map-get($colors-v2, 'secondary');
                font-size: 14px;
                font-weight: 600;
                line-height: 20px;
                margin: 0;

                @media (max-width: 767px) {
                    padding-left: 16px;
                }
            }
        }

        &:has(p) {
            td {
                padding-top: 10px;
                padding-bottom: 10px;
            }
        }
    }

    &__sidebar {
        flex-shrink: 0;
        width: 210px;
        border-left: 1px solid map-get($colors-v2, 'border');

        &-content {
            position: sticky;
            top: 0;
            left: 0;
            color: map-get($colors-v2, 'secondary');

            h6 {
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 1.5;
                text-transform: uppercase;
                padding-left: 15px;
            }

            ul {
                margin: 0;
                padding: 0;
                list-style-type: none;
            }

            li {
                width: 100%;
                margin: 0;
                padding: 0;
            }

            a {
                display: block;
                padding: 8px 16px;
                color: inherit;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 16px; /* 133.333% */
                transition: 0.15s ease-in-out;
                border-left: 1px solid transparent;

                &:hover,
                &:focus {
                    text-decoration: none;
                    color: map-get($colors-v2, 'blue-alt');
                }

                &.active {
                    color: map-get($colors-v2, 'primary');
                    font-weight: 600;
                    border-left-color: map-get($colors-v2, 'blue-alt');
                }
            }
        }

        @media (max-width: 767px) {
            display: none;
        }
    }
}

#SaaSBilling #payment-settings {
    .payment-settings-wrapper {
        display: flex;
        flex-direction: column;
        gap: 52px;

        h1 {
            font-size: 32px !important;
            margin-top: 0;
            padding-top: 10px;
            font-weight: 600;
            line-height: 1.1;
        }

    }

    .details-grid {
        .edit-button {
            top: 0;
            i {
                margin-right: 0;
            }
        }

    }

    section {
        display: flex;
        flex-direction: column;
        gap: 24px;

        h1,
        h3,
        p {
            margin: 0px;
        }
        & > h1 {
            font-size: 28px;
        }
        & > p {
            font-size: 16px;
            max-width: 700px;
        }
    }
    .page-error {
        pointer-events: none;
        padding: 20px;
        position: relative;
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.05);
            border: 1px solid map-get($colors-v2, 'border');
            border-radius: 4px;
        }
    }
    .page-error-alerts {
        margin-bottom: 2rem;
        &:empty {
            display: none;
        }
    }
    .cards-alerts, .page-error-alerts {
        display: flex;
        justify-content: start;

        .custom-alert {
            margin-bottom: 0px;
            padding: 16px !important;
        }
        .custom-alert__icon {
            width: 20px;
            height: 20px;
        }
        .custom-alert__content {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        .custom-alert__body {
            p {
                font-size: 16px;
                line-height: 16px;
                color: #826c20;
            }
        }
    }
    .cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        max-width: map-get($breakpoints, 'lg');
    }
    .card-thumbnail {
        height: 160px;
        border: 1px solid map-get($colors-v2, 'border-dark');
        border-radius: 6px;
        padding: 20px;
        color: #fff;
        text-transform: uppercase;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .card-label {
            font-size: 16px;
            line-height: 24px;
            font-weight: 600;
        }
        .card-brand {
            font-size: 14px;
            line-height: 18px;
            font-weight: 600;
        }
        .expiration:not(.expired) {
            color: rgba(255, 255, 255, 0.5);
        }
        .expiration.expired {
            color: #dc3545;
            font-weight: 600;
        }
        img.card-logo {
            width: 32px;
            height: 32px;
        }
        .set-primary {
            color: #fff;
            border: 1px solid #fff;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            line-height: 16px;
            padding: 0px 4px;
            transition: scale 100ms ease-in;
            background-color: unset;
            opacity: 1;
            cursor: not-allowed;

            &:not(.is-primary):is(:hover, :focus) {
                scale: 1.1;
                cursor: pointer;
            }
        }
        .card-actions {
            opacity: 0;
            display: flex;
            gap: 1rem;
            transition: opacity 100ms ease-in;
            button {
                color: #fff;
                padding: 0px;
                &:is(:hover, :focus) {
                    background-color: unset;
                    opacity: 1;
                }
                &[data-toggle='tooltip'] {
                    opacity: 0.5;
                }
                i {
                    margin: 0px;
                }
            }
        }
        &:hover {
            .card-actions {
                opacity: 1;
            }
        }
        &.visa {
            background-color: #2196f3;
        }
        &.mastercard {
            background-color: #30313d;
        }
        &.amex {
            background-color: #016fd0;
        }
        &.discover {
            background-color: #d9be93;
        }
        &.diners {
            background-color: #c1c2c3;
        }
        &.unionpay {
            background-color: #1c4867;
        }
        &.expired {
            background-color: #cccccc !important;
        }
        &.add-card {
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 100ms ease-in;
            &:hover {
                background-color: rgba(0, 0, 0, 0.05);
            }
            .add-icon {
                padding: 5px;
            }
            i {
                color: map-get($colors-v2, 'secondary');
                font-size: 18px;
            }
            h3 {
                font-size: 16px;
                line-height: 24px;
                font-weight: 600;
                text-transform: uppercase;
                color: map-get($colors-v2, 'primary');
            }
            p {
                font-size: 11px;
                line-height: 16px;
                font-weight: 600;
                color: map-get($colors-v2, 'secondary');
            }
        }
    }
    .details-grid, .admin-settings-grid {
        position: relative;
        display: flex;
        align-items: stretch;
        gap: 10px;
        border: 1px solid map-get($colors-v2, 'border');
        border-radius: 6px;
        padding: 20px;
        max-width: map-get($breakpoints, 'lg');
        .separator {
            width: 1px;
            background-color: map-get($colors-v2, 'border');
        }
        @include respond-below('md') {
            flex-direction: column;
            .separator {
                width: unset;
                height: 1px;
            }
        }
        .details-col {
            width: 240px;
            h3,
            p {
                font-size: 14px;
            }
            h3 {
                margin-bottom: 8px;
            }
            p {
                color: map-get($colors-v2, 'secondary');
            }
        }
        .edit-button {
            position: absolute;
            top: 10px;
            right: 0px;
        }
    }
}

.payment-method-dialog {
    .stripe-card-input {
        border: 1px solid map-get($colors-v2, 'border');
        border-radius: 4px !important;
        padding: 10px;
    }
    // matches the input field in the stripe card element
    input[name='label'] {
        color: #32325d;
        font-family: 'Source Sans Pro', 'Helvetica Neue', sans-serif;
        font-size: 16px;
        padding: 20px 10px;
        &::placeholder {
            color: #aab7c4;
        }
        &:focus {
            border: 1px solid map-get($colors-v2, 'border');
        }
    }
    .disabled {
        background-color: rgba(0, 0, 0, 0.05);
        cursor: not-allowed;
    }
}

.editBillingProfile {
    margin: 3px;

    .form-group {
        padding: 5px;
    }
    .no-left-margin {
        .bootstrap-select {
            margin-left: -5px;
        }
    }
    .billing-contacts-group {
        .select2.select2-container {
            z-index: 0;
        }
        .form-group {
            margin-left: 0px;
            margin-right: 0px;
        }
    }
}

