// Used for the new store model overlay hide/show events...
#modelOverlay,
#modalBackdrop {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 1090;
    display: none;
    background-color: black;
    filter: alpha(opacity=30);
    opacity: 0.5;
}
* html #modelOverlay {
    position: absolute;
}

.iframe-placeholder-zindexOn {
    z-index: 1091;
}

//  Old store code...
// .shop {
//     .filter-container:first-child {
//         margin-top: -20px;
//     }
//     .filter-container {
//         h4 {
//             font-weight: 400;
//             font-size: 16px;
//             padding-bottom: 10px;
//             padding-top: 10px;
//         }

//         padding-left: 10px;
//         padding-right: 10px;

//         .slider {
//             margin-left: 10px;
//             margin-right: 10px;
//         }
//         .product-filter-list, .brand-filter-list {
//             list-style: none;
//             li {
//                 margin-left: -40px;
//                 label {
//                     text-transform: uppercase;
//                 }
//             }
//         }
//         li {
//             label {
//                 font-size: 12px !important;
//             }
//         }
//     }

//     .checkout-card, .finalise-order{
//         display: none;
//     }
//     .finalise-order {
//         .order-img {
//             height: 60px;
//             object-fit: cover;
//         }
//         .item-details-small {
//             font-size: 11px;
//         }

//         .table-order-body {
//             .remove {
//                 a, a i {
//                     cursor: pointer;
//                 }
//                 i {
//                     font-size: 18px;
//                     color: black;
//                     cursor: pointer;
//                 }
//                 i:hover {
//                     transition-duration: 0.2s;
//                     color: #ca3a3a;
//                 }
//             }
//         }
//         @media (max-width: map-get($breakpoints, 'sm')) {
//             .table-description {
//                 max-width: 250px;
//             }
//         }
//         .delivery-details {
//             .shipment-address {
//                 font-style: italic;
//                 margin-left: 25px;
//             }
//         }
//         .order-request-user {
//             float: right;
//             clear: both;
//             text-align: right;
//             padding-top: 10px;
//             .order-request-email {
//                 font-weight: bold;
//             }
//         }
//         .special-instructions {
//             h5 {
//                 padding-top: 10px;
//             }
//             textarea {
//                 width: 100%;
//                 height: 100px;
//             }
//         }
//         .order-checkout-details {
//             .order-totals {
//                 background-color: #eeeeee;
//                 border: 1px solid #eeeeee;
//                 padding: 10px;
//                 width: 200px;
//                 float: right;
//                 font-size: 16px;
//                 font-weight: bold;
//                 clear: both;
//             }
//             .total-disclaimer {
//                 float: right;
//                 clear: both;
//                 max-width: 300px;
//                 text-align: right;
//                 font-size: 12px;
//                 font-style: italic;
//                 padding: 10px;
//             }
//             .btn {
//                 float: right;
//                 margin-left: 10px;
//                 // clear: both;
//             }
//         }
//     }
//     .checkout-container {
//         .total-number {
//             font-weight: 900;
//         }
//         .checkout-options {
//             padding-top: 10px;
//         }
//         .items-text {
//             color: #1f91f3;
//         }
//     }
//     .store-item-container {

//         overflow-y: auto;

//         @media (min-width: map-get($breakpoints, 'md')) {
//             // Only 'contain' store items when on desktop...
//             height: calc(100vh - 195px);
//             overflow-y: scroll;
//         }

//         .item {
//             padding: 0px;

//             .item-frame {

//                 margin: 4px;
//                 border: 1px solid #e8e8e8;

//                 .details-img {
//                     cursor: pointer;
//                 }
//                 img {
//                     object-fit: cover;
//                     // width:100%;
//                     // height:100%;
//                     //  max-height: 200px;
//                     height: 200px;
//                 }

//                 .item-details {
//                     background-color: #e8e8e8;
//                     min-height: 130px;
//                     padding-bottom: 1px;
//                     .additional-charges {
//                         font-size: 10px;
//                         margin-top: 5px;
//                         margin-bottom: 0px;
//                     }
//                 }

//                 h4 {
//                     width: 100%;
//                     text-align: left;
//                     padding: 10px;
//                     min-height: 50px;
//                 }
//                 .actions {
//                     padding-left: 10px;
//                     margin-top: 0px;
//                     padding-bottom: 10px;
//                     h5 {
//                         text-align: left;
//                         font-weight: 900;
//                         font-size: 25px;
//                         color: #000;
//                         font-style: italic;
//                         display: inline;
//                     }
//                     .btn {
//                         margin-left: 10px;
//                         height: 36px;
//                         margin-top: -10px;
//                     }
//                     .btn-default {
//                         padding-top: 8px;
//                     }
//                 }

//             }
//         }
//     }
// }
// .item-details-popup {
//     padding: 30px;

//     .item-picture {
//         object-fit: cover;
//         width:100%;
//         height:100%;
//         max-width: 400px;
//     }
//     .description-col {
//         text-align: left;
//         h4, h5 {
//             font-size: 22px;
//         }
//         h5 {
//             color: #000;
//             font-style: italic;
//         }
//         p {
//             font-size: 14px;
//         }
//         .item-description {
//             min-height: 135px;
//         }
//         input[type=number]::-webkit-inner-spin-button, 
//         input[type=number]::-webkit-outer-spin-button {
//            opacity: 1;
//         }
//         .order-qty {
//             width: 50px;

//             // Height should match the 'add to cart' button...
//             height: 39px;
//         }
//         .btn {
//             margin-top: -3px;
//         }
//     }
// }