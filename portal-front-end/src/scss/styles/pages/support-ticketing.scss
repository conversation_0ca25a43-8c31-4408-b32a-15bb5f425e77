.create-new-ticket-option {
    .bg-new-ticket-notice {
        background-color: #555b62;
        color: white;
    }

    .alert {
        a {
            color: black;
            font-weight: bold;
        }

        .font-weight-light {
            font-weight: 300;
        }

        ul {
            margin-top: 10px;
            max-height: 150px;
            overflow-y: scroll;
        }
    }

    .new-ticket-selection {
        padding: 15px;
        display: flex;
        flex-direction: column;
        border-radius: 6px;
        background-color: #f7f9fd; // Pale blue-grey background
        transition: 0.2s all ease-in-out;
        cursor: pointer;

        &:hover {
            background-color: #edf2f9; // Slightly darker pale blue-grey on hover
            transform: translateY(-2px); // Subtle lift effect
        }

        label {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
            color: #333; // Darker than standard black
        }

        input[type='radio'] {
            margin-right: 8px;
            cursor: pointer;
        }

        @media (max-width: map-get($breakpoints, 'xs')) {
            width: 100%;
        }

        small {
            font-weight: normal;
            font-size: 14px;
            color: #777; // A bit grayish for softer appearance
        }
    }

    .new-ticket-info-text {
        padding-top: 15px;
        font-style: italic;
        color: #555; // Slightly softer than black
    }
}

.new-support-ticket-dialog {
    margin-right: 0;
    margin-left: 0;

    @media (max-width: map-get($breakpoints, 'sm')) {
        margin-left: -10px;
        margin-right: -10px;
    }
}

.new-support-ticket-dialog,
.ticket-details-card {
    .trumbowyg-editor {
        max-height: 300px; /* Adjust to your preference */
        overflow-y: auto; /* Allow scroll on overflow */
    }

    .suggested-articles h4 {
        position: relative;
    }

    .ai-icon-container {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%); /* Center the icon vertically */
    }

    .ai-icon {
        width: 20px; /* Adjust as needed */
        height: 20px; /* Adjust as needed */
        cursor: pointer;
    }

    .suggested-articles {
        background-color: #f9fafc; /* Lighter background */
        background-image: linear-gradient(
            180deg,
            #f9fafc 0%,
            #ffffff 100%
        ); /* Soft gradient from a light gray to white */
        border: 1px solid #e1e4e8; /* Soft border */
        border-radius: 10px;
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.03); /* Lighter shadow */
        padding: 10px 15px;
        margin-bottom: 20px; /* Added spacing from subsequent elements */
        margin-top: 10px;
    }

    .suggested-articles h4 {
        // color: #717d8a;
        color: #0094f7;
        font-weight: 600;
        border-bottom: 1px solid #d2d9e0;
        padding-bottom: 10px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        margin-top: 0px;
    }
    .suggested-articles h4 i {
        margin-right: 10px;
    }

    // .suggested-articles h4::before {
    //     content: '\1F4D6';  /* Article/Book icon using Unicode */
    //     font-size: 24px;
    //     margin-right: 8px;
    //     color: #5a6671;
    // }

    .suggested-articles ul {
        list-style-type: none;
        padding: 0;
    }

    .suggested-articles ul li {
        margin-bottom: 8px;
    }

    .suggested-articles ul li a {
        color: #5a6671;
        text-decoration: none;
        transition: color 0.2s;
    }

    .suggested-articles ul li a:hover {
        color: #3f4d5a;
    }

    #upload-size-left-alert {
        margin-top: 10px;
    }
    .dropzone {
        border-style: dashed;
        .dz-button {
            margin-top: 10px;
        }
    }

    @media (max-width: map-get($breakpoints, 'sm')) {
        .trumbowyg-editor {
            min-height: 200px;
        }
    }

    .trumbowyg-editor {
        max-height: 300px; /* Adjust to your preference */
        overflow-y: auto; /* Allow scroll on overflow */
    }

    // Smaller existing attachments list group...
    .list-group-item {
        padding: 3px 10px;
        .btn {
            margin-left: 10px;
        }
    }

    // Fix for the SVG table icon's margin which seems broken in the current trumbowyg release..
    div.trumbowyg-button-pane .trumbowyg-table-button > svg {
        margin-top: -11px;
    }

    #wysiwyg-editor {
        max-height: 300px;
    }

    .selectbox {
        border-bottom: 1px solid #dddddd;
        background-color: #fbfbfb;
        padding: 2px;
        padding-left: 10px;
        padding-right: 10px;

        .material-icons {
            font-size: 16px;
        }
    }
    .selectbox:hover {
        background-color: #f7f7f7;
    }
    .materialize [type='checkbox'] + label {
        font-size: inherit;
    }
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-top: 1px solid lightgrey;
        margin: 2px;
        padding: 10px;

        .row {
            padding: 2px;
            .col-sm-1 {
                margin-top: 5px;
            }
        }
        input[type='text'],
        input[type='number'] {
            padding: 4px;
            width: 100%;
        }
        label {
            font-weight: 400;
        }
        textarea {
            padding: 4px;
        }
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: 600;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 24px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
}

.ticketing {
    overflow: hidden;

    .no-padding {
        padding: 0px;
    }

    .divTicketHeader {
        .ticket-badges {
            .col-sm-6 {
                @media (max-width: map-get($breakpoints, 'sm')) {
                    margin-top: 5px;
                }
            }
        }
    }

    .replyContainer {
        @media (max-width: map-get($breakpoints, 'sm')) {
            .col-sm-12 {
                margin-left: -15px;
                margin-right: -15px;
            }
        }
        .ticket-reply-closed {
            display: none;
        }
        .dropzone {
            min-height: 0;
        }
    }

    .ticket-details-card {
        margin-bottom: 0px;
        .header {
            border-bottom: none;
        }
        .no-padding {
            padding: 0px;
        }
        .ticket-details-header {
            margin: 10px;
            margin-bottom: 40px;

            code {
                font-size: 16px;
            }

            .ticket-action-buttons {
                @media (max-width: map-get($breakpoints, 'sm')) {
                    text-align: left;
                    margin-bottom: 10px;
                    border-bottom: 1px solid lightgrey;
                    padding-bottom: 10px;

                    margin-left: 10px;
                    padding-left: 10px;
                    padding-right: 10px;
                    margin-right: 10px;
                }
            }

            // #expandSubtractTickets {
            //     svg {
            //         height: 16px;
            //     }
            // }
            .ticket-dates {
                max-width: 300px;

                @media (max-width: map-get($breakpoints, 'md')) {
                    .ticket-summary-dates {
                        border-left: none;
                    }
                }
                @media (max-width: map-get($breakpoints, 'sm')) {
                    text-align: left;
                    width: 100%;
                    max-width: none;
                    padding: 0px;
                    padding-top: 20px;

                    .ticket-summary-dates {
                        border-left: none;
                    }
                }
            }

            .rowTicketSubject {
                margin-top: 10px;
                margin-bottom: 10px;

                h2 {
                    font-size: 24px;
                    font-weight: bold;
                }
            }

            .ticket-topic-departments {
                font-size: 12px;
            }
            .ticket-status-badge {
                line-height: 3;
                max-width: 100px;
                text-align: center;
            }
            .ticket-summary-dates,
            .ticket-status-badge {
                // margin-bottom: 5px;
                border-left: 1px solid #eeeeee;
            }
        }
    }
    .card-parent-bg {
        background-color: #f8fafb;
    }
    .ticket-searchbar {
        width: 100%;
        max-width: 900px;
        margin: 0 auto;
        margin-top: 10px;

        .search-bar {
            font-size: 14px;
        }
    }

    .search-bar-wrapper {
        display: flex;
        justify-content: space-between;
        gap: 2rem;
        margin-bottom: 2rem;

        @include respond-below(xs) {
            flex-wrap: wrap;
        }

        .input-group {
            margin-bottom: 0px;
            background-color: white;
            max-width: 300px;

            @media (max-width: map-get($breakpoints, 'sm')) {
                max-width: 100%;
            }
        }
    }

    .btnCreateNewTicket {
        display: flex;
        overflow: hidden;

        span {
            flex-grow: 1;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        @include respond-below(xs) {
            width: 100%;
        }
    }

    #divTicketsSection {
        .t-search-filters {
            padding: 15px;
        }
        .t-search-filters:hover {
            background-color: white !important;
        }
        .visible-results-counter {
            font-size: 16px;
        }
        #iconSort {
            float: right;
            padding-top: 1px;
        }
        .t-open {
            background-color: #1f91f3;
        }
        .t-closed {
            background: none;
            border: 1px solid #777777;
            color: #777777;
        }
        .t-resolved {
            background-color: #6fcc41;
        }
        .card {
            margin: 6px;
            cursor: pointer;
            margin-bottom: 15px;
            margin-top: 10px;
        }
        .card:hover {
            background-color: #eef3fc6b;
        }
        .ticket-summary {
            display: -webkit-box;
            max-width: 100%;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .ticket-badges {
            margin-top: 10px;
        }
        a:hover {
            text-decoration: none;
        }
        .department-details {
            font-size: 12px;
            font-style: italic;
            margin-right: 10px;
        }
    }

    #divAllTickets {
        overflow: auto;
        height: calc(100vh - 315px);
    }

    .search-wrap {
        position: relative;
    }

    #divSearchWiki {
        .card {
            // background: none;
            height: calc(100vh - 133px);
        }
        .wiki-header {
            padding: 40px;
            padding-top: 10px;
            p {
                height: 40px;
            }
        }
        .icon-search {
            input {
                padding-left: 5px;
            }
        }
        .wiki-results-container {
            margin: 0px;

            .wiki-summary {
                display: -webkit-box;
                max-width: 100%;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
            .resultItem {
                cursor: pointer;
                background: #ffffffc4;
                margin: 6px;
                h2 {
                    margin-bottom: 10px;
                }
            }
            .resultItem:hover {
                background-color: #eef3fc6b;
            }
        }
    }

    #divSearchResults {
        overflow: auto;
        height: calc(100vh - 560px);
        margin-bottom: 10px;
        margin-left: 10px;
    }

    .search-content {
        position: relative;
    }

    .supportSystemsBox.online {
        background-color: #ecf9e0;
    }
    .supportSystemsBox.online {
        border: 2px solid #e4e4e4;

        h4,
        small {
            color: #235b1c;
        }
        .status-pages {
            ul,
            a {
                color: #235b1c;
            }
        }
    }

    .supportSystemsBox.offline {
        background-color: #fcecf4;
    }
    .supportSystemsBox.offline {
        border: 2px solid #e4e4e4;

        h4,
        small {
            color: #47464f;
        }
        .status-pages {
            ul,
            a {
                color: #47464f;
            }
        }
    }

    .supportSystemsBox {
        color: white;
        background-color: #555b62;
        margin-left: 40px;
        margin-right: 40px;
        height: 190px;
        overflow-y: scroll;

        .status-pages {
            ul {
                list-style-type: square;
                color: white;
                padding-left: 20px;
                padding-top: 5px;

                li {
                    a {
                        color: white;
                    }
                }
            }
        }
    }

    .wikiGraident {
        background: radial-gradient(circle at top left, #ededed, white 40%);
    }

    .ticketBtn {
        margin: 20px;
    }

    .ticketBadgeAlign {
        font-size: 20px;
        vertical-align: middle;
    }

    .topSpacer {
        margin-top: 10px;
    }

    .rightSpacer {
        margin-right: 50px;
    }

    .thread-reply-updating {
        center {
            padding: 20px;
        }
    }
    .ticketThreadItem,
    .ticket-participant-badges,
    .ticket-participant-badges-small {
        overflow: hidden;

        .ticket-header {
            margin: -20px;
            padding: 20px;
            margin-bottom: 0px;
            padding-bottom: 15px;
            padding-top: 15px;
            cursor: pointer;
            border-bottom: 1px solid #eeeeee;
            min-height: 66px;

            span {
                float: left;
            }
            .ticket-small-summary-contents {
                margin-left: 10px;
                // float: left;
                // width: calc(100% - 420px);
                margin-top: 10px;
                // overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                // display: none;
            }
        }

        .resultItem:hover {
            background-color: #eef3fc6b;
        }
        .badge-staff-member {
            border: 1px solid #555b62;
            color: white;
            background-color: #555b62;
            text-transform: capitalize;
        }
        .staff-badge-icon {
            margin-left: 5px;
            font-size: 15px;
            top: 0;
            position: relative;
        }
        .thread-post-date {
            font-weight: bold;
            margin-top: 10px;
        }
        .badge-outline-dark {
            padding-bottom: 2px;
            padding-top: 2px;
            margin-top: 2px;
            text-transform: capitalize;
            line-height: 1.8;
            display: inline-flex;
            align-items: center;
        }
        .thread-attachments {
            .attachment {
                float: left;
                padding: 10px;
                border: 1px solid #f8fafb;
                margin: 5px;
                background-color: #f8fafb;
                box-shadow: 0 0px 3px rgba(0, 0, 0, 0.1);
                cursor: pointer;
            }
            .attachment:hover {
                background-color: rgba(179, 179, 179, 0.16);
                a:hover {
                    text-decoration: none;
                }
            }
        }
    }

    .ticket-participant-badges-small {
        .badge-outline-dark {
            line-height: 1;
        }
        .badge-staff-member {
            margin-bottom: 0;
            padding-top: 0;
        }
    }

    .file-img {
        width: 16px;
        height: 16px;
        margin: 0.5em 0;
    }

    .mb-1 {
        margin-bottom: 10px;
    }

    .mr-1 {
        margin-right: 10px;
    }

    .mr-4 {
        margin-right: 40px;
    }

    .ml-4 {
        margin-left: 40px;
    }

    .mr-2 {
        margin-right: 20px;
    }

    .ml-2 {
        margin-left: 20px;
    }
    .ml-1 {
        margin-left: 10px;
    }
    .ml-05 {
        margin-left: 5px;
    }

    .filterToggler {
        //position: absolute;
        // right: 45px;
        // top: 25px;

        // Only visible on small mobile devices (so they know what club is selected...)
        display: none;

        @media (max-width: map-get($breakpoints, 'sm')) {
            display: inherit;
        }
    }

    .filterColumn {
        display: flex;
        flex-direction: column;
        margin-right: 5px;
    }

    .ticketFilter {
        margin: 5px;
        // min-width: 120px;
        margin: 10px;
    }

    .ticketFilterContainer {
        background-color: white;
        padding-top: 10px;
        padding-bottom: 10px;
        width: 100%;
    }

    .filterDiv {
        background-color: white;
        padding-left: 20px;
        padding-right: 5px;
        padding-top: 5px;
        padding-bottom: 25px;
        // height: 200px;
        max-width: 400px;
        z-index: 120;
        position: absolute;
        border-radius: 5px;
        margin-top: 5px;
        // border: 1px solid gray ;
        // border-radius: 5px;
        //box-shadow: 1px 1px #888888;
    }

    .filterDiv {
        position: absolute;
        background-color: white;
        padding-left: 20px;
        padding-right: 5px;
        padding-top: 5px;
        padding-bottom: 25px;
        height: 200px;
        max-width: 400px;
        z-index: 120;
        border-radius: 5px;
        margin-top: 5px;
        border: 1px solid gray;
        border-radius: 5px;
    }

    .filterDropDiv {
        position: absolute;
        font-size: 10px;
        width: 300px;
        height: 200px;
        background-color: red;
    }

    .clearFilterBtn {
        width: 150px;
    }

    .blackBtn {
        background-color: #3a3e42;
        color: white;
        padding-right: 20px;
        padding-left: 20px;
    }

    .newFilterBtn {
        margin-right: 5px;
        margin-bottom: 5px;
        position: relative;
        min-width: 110px;
    }

    label.form-check-label {
        font-size: 12px;
        font-weight: lighter;
    }

    li.filterLi {
        margin-left: 10px;
        margin-right: 10px;
    }

    a.filterItem {
        font-size: 12px;
    }

    .filterUl {
        width: auto;
    }

    .filterMainContainer {
        display: flex;
        justify-content: start;
        flex-wrap: wrap;
    }

    .filterContainer {
        display: relative;
    }

    .filterInside {
        margin-bottom: -20px;
    }

    input[type='search']::-webkit-search-cancel-button {
        -webkit-appearance: none;
        cursor: pointer;
        height: 10px;
        width: 10px;

        background-image: url('data:image/svg+xml;utf8,
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>
        ');
    }
}

.ticketing {
    .support-page {
        padding: 0 24pX !important;
        background: #fff;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        overflow-x: hidden;

        &__sysmessage-wrapper {
            background: #fff;
            padding-bottom: 20px;
            display: none;
            position: sticky;
            top: 127px;
            left: 0;

            &.offline {
              display: block;
            }
        }

        &__heading {
            font-size: 32px;
            line-height: 42px;
            font-weight: 700;
            color: map-get($colors-v2, 'primary');
            margin: 0 0 18px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;

            @media (max-width: 767px) {
                font-size: 24px;
                line-height: 1.25;
                gap: 4px 16px;
            }
        }
        &__body {
            overflow: visible;

            .tab-content,
            .tab-pane {
                height: 100%;

                &.isRelative {
                    position: relative;
                    height: calc(100dvh - 90px - 87px);

                    @media (max-width: 767px) {
                      height: calc(100dvh - 90px - 123px);
                    }
                }
            }

            @media (max-width: 767px) {

                .tab-content,
                .tab-pane {
                    height: auto;
                }
            }
        }
        &__btn {
            position: absolute;
            right: 0;
            top: 0;
            color: map-get($colors-v2, 'blue-alt');
            text-transform: none !important;
            border: 1px solid map-get($colors-v2, 'blue-alt') !important;
            padding: 4px 8px;
            flex-shrink: 0;

            span {
                line-height: 20px;
                font-size: 14px;
            }

            @media (max-width: 1199px) {
                &.visible-md {
                    display: inline-flex !important;
                    position: relative;
                }
            }
            @media (min-width: 1200px) {
                &.visible-lg {
                    display: inline-flex !important;
                }
            }

            .material-icons {
                margin-right: 0;
            }

            &:hover {
                opacity: 0.8;
                background: rgba(33, 150, 243, 0.1) !important;
            }

            &--filter {
                p {
                    padding-right: 6px;
                }
                padding-right: 8px;
            }
        }
        .filter-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
            padding-bottom: 50px;
            background: #fff;
            z-index: 100;

            .filter-grp {
                cursor: pointer;
                svg {
                    margin-right: 8px;
                }
                &.sort-items {
                    p {
                        padding-right: 6px;
                    }
                    &.asc {
                        svg {
                            transform: rotate(180deg);
                        }
                    }
                    &.active {
                      color: map-get($colors-v2, 'blue-alt');
                    }
                }
            }

            @media (max-width: 767px) {
                #tickets-sorting {
                    width: 100%;

                    .support-page__search {
                        order: 999;
                    }
                }
            }
        }
        .filters-container {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        &__items {
            display: block;
            padding: 16px 0 48px;
            overflow-y: auto;
            flex-grow: 1;

            &.isLoading {
                pointer-events: none !important;
            }

            &__msg {
                h2 {
                    margin: 1.5em 0 1em;
                    font-weight: 600;
                }
                h4 {
                    color: map-get($colors-v2, 'secondary');
                }
            }
        }
        &__item {
            max-width: 100%; // 800px
            margin: 0 auto;
            display: block;
            padding: 24px;
            border-radius: 4px;
            text-decoration: none;
            color: map-get($colors-v2, 'primary');
            border: 1px solid transparent;
            background: map-get($colors-v2, 'container');
            transition: 0.5s ease-in;
            margin-bottom: 20px;
            cursor: pointer;

            & > *:not(:last-child) {
                margin-bottom: 16px;
            }

            &:hover {
                text-decoration: none;
                color: map-get($colors-v2, 'primary');
                border-color: map-get($colors-v2, 'border-dark');
            }

            &__status-wrapper {
                display: flex;
                align-items: center;
                gap: 12px;
                font-size: 11px;
                color: map-get($colors-v2, 'secondary');
                font-weight: 600;
            }
            &__ticket-id,
            &__ticket-status {
                display: flex;
                height: 18px;
                line-height: 16px;
                border-radius: 4px;
                padding: 0 6px;
            }

            &__ticket-id {
                border: 1px solid map-get($colors-v2, 'border-dark');
            }

            &__ticket-status {
                color: #fff;
                background: map-get($colors-v2, 'secondary');
            }

            &__title-wrapper {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            &__title {
                margin: 0;
                font-size: 20px;
                font-weight: 600;
                line-height: 1;
            }

            &__excerpt {
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.5;
                display: -webkit-box;
                max-width: 100%;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            &__meta {
                display: flex;
                align-items: flex-start;
                gap: 4px 24px;
                flex-wrap: wrap;

                &-item,
                &-author,
                &-date {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    color: map-get($colors-v2, 'secondary');
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 20px;
                    text-transform: uppercase;
                }
            }

            &-details {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                align-self: stretch;
                flex-wrap: wrap;
            }

            &__ticket-participants-wrapper,
            &__ticket-departments {
                display: flex;
                align-items: flex-start;
                gap: 6px;
                padding: 16px 0 0;
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
                flex-wrap: wrap;
            }

            &__ticket-participants-section {
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                gap: 32px;

                @media (max-width: 767px) {
                  align-items: flex-end;
                }

                &-col {
                    display: flex;
                    flex-direction: column;
                    gap: 6px;
                    align-items: flex-start;
                }

                &-row {
                  display: flex;
                  flex-direction: row;
                  gap: 6px;
                  align-items: flex-start;

                  @media (max-width: 767px) {
                    flex-direction: column;
                  }
                }

                &__label {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    color: map-get($colors-v2, 'secondary');
                    line-height: 1;

                    strong {
                        font-weight: inherit;
                        color: map-get($colors-v2, 'primary');
                    }
                }

                .support-page__item__ticket-participants-wrapper {
                    padding-top: 0;
                    color: map-get($colors-v2, 'secondary');
                }
            }

            &__ticket-message-count {
                display: flex;
                align-items: center;
                gap: 6px;
                color: map-get($colors-v2, 'secondary');
                text-transform: uppercase;
                margin-right: 6px;
            }

            &__ticket-participants {
                text-transform: capitalize;
            }

            &__ticket-departments {
                color: map-get($colors-v2, 'secondary');

                svg {
                    color: map-get($colors-v2, 'border-dark');
                }
            }

            &__ticket-badge {
                border-radius: 4px;
                display: flex;
                height: 18px;
                padding: 1px 6px;
                align-items: center;
                gap: 2px;
                height: 18px;
                background: map-get($colors-v2, 'border');
                line-height: 16px;

                .staff-badge-icon {
                  font-size: 12px;
                }
            }

            &--Resolved,
            &--Closed {
                background: transparent;

                &:hover {
                    background: color-opacity('green-light', 0.2);
                    border-color: map-get($colors-v2, 'green-1');
                }

                .support-page__item {
                    &__status-wrapper {
                        color: map-get($colors-v2, 'green-1');
                    }

                    &__ticket-id {
                        border: 1px solid map-get($colors-v2, 'green-light');
                    }

                    &__ticket-status {
                        color: map-get($colors-v2, 'green-1');
                        background: map-get($colors-v2, 'green-light');
                    }
                }
            }

            &-btn {
                display: flex;
                width: auto;
                align-items: center;
                gap: 4px;
                border: 0;
                background: transparent;
                padding: 0;
                color: map-get($colors-v2, 'grey-light');
                margin-bottom: 8px;
            }

            &--full {
                width: 100%;
                max-width: 100%;
                padding: 0 24px;
                border: 0 !important;
                display: flex;
                flex-direction: column;
                overflow-y: auto;
                height: 100%;
                background: #fff;
                cursor: auto;

                @media (max-width: 767px) {
                    padding: 0 20px;
                }

                .button {
                    flex-shrink: 0;
                    display: flex;
                    align-items: center;
                    background: transparent;
                    line-height: 20px;
                    font-size: 14px;
                    gap: 6px;
                    padding: 4px 8px;
                    border: 1px solid map-get($colors-v2, 'secondary');
                    border-radius: 4px;
                }

                .support-page__item {
                    &__title-wrapper {
                        margin-bottom: 8px;
                    }
                    &__status-section {
                        display: flex;
                        align-items: flex-start;
                        gap: 12px 24px;
                        flex-wrap: wrap;
                        font-size: 12px;

                        .support-page__item__ticket-participants-section-col {
                          align-items: flex-end;
                        }

                        @media (max-width: 991px) {
                          flex-direction: column;

                          .support-page__item__ticket-participants-section-col {
                            align-items: flex-start;
                          }

                          .support-page__item__ticket-participants-section__label {
                            svg {
                              order: -999
                            }
                          }
                        }

                        &-col {
                            display: grid;
                            flex: 1 1 auto;
                            gap: 16px 4px;
                        }
                    }

                    &__status-wrapper {
                        flex-grow: 1;
                        flex-shrink: 0;
                    }
                }
            }
        }

        &__tab-content {
            height: 100%;
            margin: 0 -24px;
            padding: 0 24px;

            .filter-section {
              position: sticky;
              top: 83px;
              left: 0;
              padding: 0 0 20px;
              background: #fff;

              @media (max-width: 991px) {
                position: relative;
                top: 0
              }
            }

            &__data {
                overflow-y: auto;
            }
        }

        .supportSystemsBox {
            flex-shrink: 0;
            height: auto;
            overflow-y: auto;
            overflow-x: hidden;
            border: none !important;
            border-radius: 6px;
            max-width: 100%;
            width: 800px;
            margin: 0 auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            background: map-get($colors-v2, 'container');
            color: map-get($colors-v2, 'secondary');

            &__header {
                display: flex;
                gap: 10px;
                width: 100%;
                justify-content: space-between;
                align-items: center;
            }

            &__collapse {
                display: none;
            }

            a,
            ul,
            .status-pages ul {
                color: inherit !important;
            }

            .status-pages a {
                cursor: pointer;
            }

            .saas-issue-icon {
                width: 24px;
                height: 24px;
                flex-shrink: 0;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .body {
                margin-left: 36px;
                max-width: 600px;
                transition: 0.3s ease-in-out;
                overflow: hidden;
                max-height: 100vh;
                padding-top: 10px;
            }

            h4 {
                font-size: 16px;
                font-weight: 600;
                line-height: 1.5;
                margin: 0 0 0;
                color: inherit;
                text-align: left;
                flex-grow: 1;
            }

            small {
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.42;
                margin: 0 0 1em;
                display: block;
                color: inherit;
            }

            &.isCollapsed {
                gap: 0px;

                .supportSystemsBox__collapse {
                    transform: rotateX(180deg);
                }

                .body {
                    max-height: 0;
                    padding-top: 0;
                }
            }

            &.online {
                background: map-get($colors-v2, 'green-light');
                color: map-get($colors-v2, 'green-1');
                // display: none;

                .supportSystemsBox__collapse {
                    display: block;
                    justify-self: flex-end;
                    transition: 0.3s ease-in-out;
                    transform: rotateX(0);
                    transform-origin: center;
                }
            }

            &.offline {
                background: map-get($colors-v2, 'red-light');
                color: map-get($colors-v2, 'red-alt');
            }
        }

        &__search {
            position: relative;
            width: 24px;

            &-field {
                position: absolute;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                height: 24px;
                border: 0;
                border-bottom: 1px solid transparent;
                transition: 0.3s linear;
                width: 24px;
                padding-left: 24px;

                &.filled,
                &:focus {
                    border-color: map-get($colors-v2, 'border-dark');
                    padding-left: 8px;
                    width: 160px;
                }

                &.filled {
                  & + .support-page__search-field__clear {
                    display: inline-flex;
                  }
                }

                &__clear {
                  position: absolute;
                  top: 50%;
                  right: 24px;
                  border:0;
                  background: none;
                  transform: translateY(-50%);
                  height: auto;
                  display: none;

                  .material-icons {
                    font-size: 16px;
                  }
                  
                }
            }

            svg {
                position: absolute;
                right: 0;
                top: 0;
                pointer-events: none;
                background: #fff;
                color: map-get($colors-v2, 'secondary');
            }
        }

        &__single-ticket {
            display: none;

            &--visible {
                display: block;
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background: #fff;
                z-index: 9999999;
            }

            &--error {
                display: grid;
                place-items: center;
            }

            &__data {
                display: grid;
                gap: 16px;
                padding: 20px 0 20px;
                top: 0;
                left: 0;
                background: #fff;
                z-index: 99;

                @media (max-width: 767px) {
                    padding: 20px 0;
                    position: relative;
                }
            }

            &__threads {
                // min-height: calc(100vh - 550px);
                min-height: 100px;
                overflow: visible;
                height: auto;
                flex-shrink: 0;
                
                #divTicketDetailsBody {
                    // width: 1400px;
                    max-width: 100%;
                    padding: 20px 0;
                    margin: 0 auto;
                    display: flex;
                    flex-direction: column;
                    padding-bottom: 20px;

                    &.sm-padding {
                      padding-bottom: 32px;
                    }

                    &.has-hidden-threads {
                      .support-page__ticket-msg-item {
                        &:first-child {
                          .ticketThreadItem {
                            border-bottom: 0;
                          }
                        }
                      }
                    }

                    .support-page__ticket-msg-item {
                      &:last-child {
                        .ticketThreadItem {
                          border-bottom: 0;
                        }
                      }
                      .thread-attachments {
                        margin-bottom: 16px;

                        &:empty {
                          margin-bottom: 0;
                        }
                      }
                    }
                }
            }

            .new-support-ticket-dialog {
                margin: 0;
                padding: 16px 0;

                &--closed {
                    margin: 0;
                    position: static;
                    bottom: 0;
                    left: 0;
                    z-index: 101;
                    border-top: 1px solid map-get($colors-v2, 'border-dark');
                    background: #fff;
                    width: 100%;
                    padding: 20px;

                    @media (max-width: 767px) {
                      position: static;
                    }
                }

                .replyContainer {
                    border: 0;
                    margin: 0;
                    box-shadow: none;

                    .body {
                        padding: 0;
                    }

                    fieldset {
                        padding: 0;
                        margin: 0;
                        border-top: 1px solid map-get($colors-v2, 'border-dark');

                        .row {
                            padding: 0;
                        }
                    }

                    legend {
                        color: map-get($colors-v2, 'secondary');
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 20px;
                        display: inline-block;
                        padding: 0 12px 0 0;
                        margin-bottom: 15px;
                        text-transform: uppercase;
                    }

                    .trumbowyg-box,
                    .dropzone {
                        box-shadow: none;
                    }

                    .ticketBtn {
                        margin: 20px 0;
                    }
                }
                .re-open-ticket-container {
                    b {
                        color: map-get($colors-v2, 'green-1');
                        font-family: 'Source Sans Pro';
                        font-size: 11px;
                        font-weight: 600;
                        line-height: 20px;
                        text-transform: uppercase;
                    }
                    small {
                        color: map-get($colors-v2, 'secondary');
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 24px;
                        display: block;
                        margin: 0 0 1em;
                    }
                }
            }
        }

        &__ticket-divider {
            display: flex;
            justify-content: center;
            align-items: center;
            position: sticky;
            margin: 0 !important;
            padding: 0 0 12px;
            background: #fff;
            top: 75px;
            left: 0;
            z-index: 101;

            @media (max-width: 767px) {
              top: 90px;
            }

            &::before {
                content: '';
                height: 1px;
                background: map-get($colors-v2, 'border-dark');
                position: absolute;
                top: 50%;
                left: 0;
                width: 100%;
                transform: translateY(-50%);
                z-index: 1;
                margin-top: -7px
            }

            // #expandSubtractTickets {
            //     display: flex;
            //     gap: 6px;
            //     line-height: 24px;
            //     padding: 0 12px;
            //     align-items: center;
            //     background: #fff;
            //     border: 0;
            //     position: relative;
            //     z-index: 2;
            //     color: map-get($colors-v2, 'secondary');
            //     text-transform: uppercase;
            //     font-weight: 600;
            //     font-size: 16px;

            //     @media (max-width: 767px) {
            //       font-size: 14px;
            //     }
            // }
        }

        &__ticket-msg-item {
            max-width: 100%;
            width: 100%;
            text-align: left;
            height: auto !important;
            overflow-x: hidden !important;
            overflow-y: auto;
            margin-bottom: 16px;

            // &--ticketPoster {
            //     align-self: flex-end;
            //     text-align: right;
            // }

            // @media (max-width: 767px) {
            //     max-width: calc(100% - 30px);
            // }

            .attachment {
                float: none !important;
                display: inline-flex;
                margin: 0 !important;
                padding: 4px 6px !important;
                gap: 2px;
                font-size: 11px;
                font-weight: 600;
                line-height: 16px;
                box-shadow: none !important;
                border-radius: 4px;
                border: 1px solid map-get($colors-v2, 'border-dark') !important;
                background: #fff;

                a {
                    color: map-get($colors-v2, 'blue-alt');
                }

                img {
                    margin: 0;
                }
            }

            &__meta {
                color: map-get($colors-v2, 'secondary');
                font-family: 'Source Sans Pro';
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;

                .staff-badge-icon {
                  font-size: 12px;
                  position: relative;
                  top: 2px;
                }

                span {
                    text-transform: capitalize;
                }
            }

            .ticketThreadItem {
                display: block;
                padding: 0;
                font-size: 16px;
                line-height: 1.5;
                // background: map-get($colors-v2, 'container');
                border-bottom: 1px solid map-get($colors-v2, 'border');
                // border-radius: 2px 12px 12px 12px;
                height: auto !important;
                cursor: pointer;
                margin: 0;
                overflow-x: hidden !important;
                overflow-y: auto  !important;

                @media (max-width: 991px) {
                  min-width: 200px;
                }

                iframe {
                    max-width: 100%;
                    display: none !important;
                }

                .ticket-header {
                    border: none;
                    min-height: 0;
                    padding: 0;
                    margin: 0 0 10px;

                    .ticket-small-summary-contents {
                        float: none;
                        margin-top: 0;
                        display: block !important;
                        display: -webkit-box !important;
                        max-width: 100%;
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        margin-left: 0;
                    }
                }

                &.expanded {
                    iframe {
                        display: block !important;
                    }

                    .ticket-header {
                        .ticket-small-summary-contents {
                            display: none !important;
                        }
                    }
                }
            }

            // &--ticketPoster {
            //     .ticketThreadItem {
            //         border-radius: 12px 2px 12px 12px;
            //     }
            // }
        }

        &__tabs-wrapper {
          padding: 24px 0;
          background: #fff;
          position: sticky;
          top: 0;
          left: 0;
          z-index: 99999;
        }
    
        .filter__options {
           min-width: 150px;
           width: auto;
        }
        
        &__sticky-data {
          display: flex;
          flex-direction: column;
          gap: 8px;
          background: #fff;
          padding: 20px 0;
          position: sticky;
          top: 0;
          left: 0;
          z-index: 100;
          margin-bottom: 0 !important;

          &__actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }

        &__ticket-count {
          display: flex;
          align-items: center;
          margin: -16px 0 10px;
          padding: 0 16px;
          position: relative;
          cursor: pointer;

          &::before {
            content: '';
            display: block;
            top: 50%;
            left: 0;
            width: 100%;
            height: 8px;
            transform: translateY(-50%);
            position: absolute;
            border-top: 1px solid map-get($colors-v2, 'border');
            border-bottom: 1px solid map-get($colors-v2, 'border');
          }

          span {
            aspect-ratio: 1/1;
            width: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            border: 1px solid map-get($colors-v2, 'border');
            font-weight: 600;
            color: map-get($colors-v2, 'secondary');
            background: #fff;
            position: relative;
          }
        }

        &__item-actions {
          display: flex;
          gap: 8px;

          .button {
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            color: map-get($colors-v2, 'primary');
            border: 0;
          }
        }
    }
    
}

.dropzone-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 0 !important;
    padding: 4px;

    p {
        margin: 0;
        font-size: 14px;
        line-height: 20px;

        strong {
            font-weight: 600;
            display: block;
        }
    }

    button {
        display: flex;
        padding: 4px 8px;
        align-items: center;
        border-radius: 4px;
        border: 1px solid map-get($colors-v2, 'secondary');
        background: map-get($colors-v2, 'secondary');
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        color: #fff;
    }
}

.support-page {
    &__dialog {
        .swal2-actions {
            justify-content: space-between !important;

            button {
                display: inline-flex !important;
                padding: 4px 8px;
                align-items: center;
                gap: 10px;
                border-radius: 4px;
                outline: 0;
                border: 1px solid map-get($colors-v2, 'secondary');
                background: #fff;
                color: map-get($colors-v2, 'secondary');
                transition: 0.3s;

                &:hover {
                    border-color: map-get($colors-v2, 'blue-alt');
                    color: map-get($colors-v2, 'blue-alt');
                }
            }

            .swal2-confirm {
                order: 999;
                background: map-get($colors-v2, 'blue-alt');
                border: none;
                color: #fff;

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                &:hover {
                    background: map-get($colors-v2, 'blue-dark');
                    color: #fff;
                }
            }
        }
        .swal2-content {
            padding: 20px;
            color: map-get($colors-v2, 'primary');
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;

            .content {
                max-width: 500px;
            }

            h4 {
                color: map-get($colors-v2, 'primary');
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                margin: 0 0 12px;
            }

            .container-fluid {
                padding: 0;
            }

            .custom-list {
                ul {
                    list-style: none;
                    padding: 0;
                }

                li {
                    padding: 0 0 0 24px;
                    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="m6.667 4-.94.94L8.78 8l-3.053 3.06.94.94 4-4-4-4Z"/></svg>')
                        no-repeat 0 3px;

                    b {
                        font-weight: normal;
                    }
                }
            }

            .new-ticket-option {
                padding: 24px 16px;
                border-top: 1px solid map-get($colors-v2, 'border');
                border-bottom: 1px solid map-get($colors-v2, 'border');
                color: map-get($colors-v2, 'primary');

                @media (max-width: 767px) {
                    gap: 8px 24px;
                }

                .new-ticket-selection {
                    text-align: left;
                    padding: 0;
                    border: 0;

                    &:hover {
                        border: 0;
                        background: none;
                    }

                    label {
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 24px;
                        text-transform: none;
                    }

                    small {
                        display: block;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        text-transform: uppercase;
                        color: map-get($colors-v2, 'blue-alt');
                        padding: 0 0 0 24px;
                    }
                }

                .divider {
                    align-self: stretch;
                    width: 1px;
                    background: map-get($colors-v2, 'border');
                }
            }
        }

        .highlighte-text {
            color: map-get($colors-v2, 'blue-alt');
            font-weight: 600;
        }

        .field-label {
            font-weight: 600;
            line-height: 20px;
            margin: 0 0 4px;
        }

        &--wide {
            .trumbowyg-box {
                margin-bottom: 10px;
                box-shadow: none;
            }
            .trumbowyg-editor {
                max-height: 256px;
                min-height: 256px;
            }
            .dropzone {
                box-shadow: none;
                margin-bottom: 20px;
                min-height: 0;
            }
            .bootstrap-select .filter-option-inner-inner {
                color: map-get($colors-v2, 'secondary');
            }
            .form-control {
                &::placeholder {
                    color: map-get($colors-v2, 'secondary');
                    font-weight: 600;
                }
            }
        }
    }
}
