// Club Radio / Sonos Control

.sRadio {
    .body {
        margin-bottom: 0;
        padding-bottom: 5px;
    }
    .media-config {
        height: calc(100vh - 210px);
    }
    .media-control {
        height: 80px;
        background-color: #353232;
    }
    .config-pane {
        margin-bottom: 0 !important;
    }
    .music-sources {
        padding: 0;
        margin-bottom: 0 !important;
        background-color: #353232;

        hr {
            margin: 0;
            padding-bottom: 2px;
        }
        h4 {
            color: white;
            background-color: #484848;
            margin: 0;
            text-align: center;
            padding: 7px;
            font-size: 14px;
        }

        .stream-sources {
            background-color: #353232;
            overflow-x: hidden;
            overflow-y: scroll;
            // Should calculate to exclude height of PH header & footer padding
            height: calc(100vh - 159px);
        }
    }
    .station-item {

        margin: 2px;
        cursor: pointer;
        max-height: 60px;

        .col-sm-12 {
            padding: 0;
            margin-bottom: 0;
        }
        .cover-icon {
            float: left;
            width: 50px;
            padding: 3px;
        }
        .metadata {
            color: white;

            float: right;
            width: calc(100% - 50px);
            padding: 3px;

            h6 {
                margin-top: 0;
                margin-bottom: 2px;
            }
            p {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 11px;
                margin-bottom: 0;
            }
        }
    }
    .station-item:hover {
        background-color: #484848;
    }

}