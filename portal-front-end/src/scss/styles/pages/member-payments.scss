#member-payments-page {
    .no-stripe {
        display: none;
    }
    .block-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-bottom: 10px;
        flex-wrap: wrap;

        &--center {
            align-items: center;
            gap: 20px;
        }
    }
    #payments-table_wrapper {
        padding-bottom: 6rem;
    }
    #payments-table-container,
    #settlement-summary-container,
    #dashboard-container {
        min-height: 200px;
    }
    #payments-table-container {
        .dt-buttons {
            display: none;
        }
        &-loader {
            overflow: hidden;
        }
    }
    #payments-table tbody tr {
        cursor: pointer;

        &:hover {
            background-color: #f5f7f9;
        }

        @media (max-width: map-get($breakpoints, 'md')) {
            .customer-email-col {
                width: 80px;
            }
        }
    }
    #dashboard-container {
        .header {
            color: #555;
            padding: 15px !important;
            padding-bottom: 10px !important;
            position: relative;
            border-bottom: 3px solid #f8fafb;

            h2 {
                color: #555;
                font-size: 15px;
                font-weight: bold;

                .reporting-period {
                    color: #868b93;
                }
            }
        }
        .show-all-failed-payments,
        .show-all-clearing-payments {
            i {
                font-size: 14px;
                transform: translateY(-3px);
            }
        }
        .single-stat-counter {
            @include respond-below(xs) {
                flex-basis: calc(50% - 1rem);
            }
            @include respond-below(xxs) {
                flex-basis: 100%;
            }
        }
    }

    .container-fluid {
        padding: 32px;
        background-color: #fff;
        overflow: auto;
    }

    .tab__wrapper {
        position: relative;

        .nav-tabs {
            padding-right: 250px;
        }

        #page-daterange {
            position: absolute;
            right: 0;
            top: 0;
            transform: translateY(-10px);
        }

        @media (max-width: 991px) {
            padding-top: 60px;

            .nav-tabs {
                padding-right: 0;
            }
            #page-daterange {
                transform: none;
            }
        }
    }

    .empty-data-message {
        h3 {
            color: $slate-500;
            font-size: 24px;
        }

        p {
            color: $slate-450;
            font-size: 14px;
        }
    }
}
.record-info-dialog {
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border: 1px solid #999999;
        margin: 2px;
        padding: 10px;
        label {
            font-weight: 400;
        }
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
    .copy-clipboard {
        code {
            background: none;
            color: #6a7383;
            font-size: 12px;
        }
        svg {
            color: #6a7383;
            transform: translateY(2px);
        }
        @media (max-width: map-get($breakpoints, 'sm')) {
            code {
                display: inline-block;
                width: 80px;
            }
            svg {
                transform: translateY(-2px);
            }
        }
    }
    .payment-timeline-container {
        padding-top: 6px;
        max-height: 300px;
        overflow-y: scroll;
    }
    .stripe-timeline {
        position: relative;
        display: flex;
        gap: 1rem;
        min-height: 60px;
        padding-bottom: 1rem;

        &::before {
            content: '';
            position: absolute;
            top: 22px;
            bottom: 0;
            left: 5px;
            width: 1px;
            background-color: #ebeef1;
        }

        img {
            height: 12px;
            width: 12px;
            margin-top: 5px;
        }
        .label {
            font-size: 14px;
            color: #404442;
            font-weight: bold;
            padding: 0;
        }
        .date {
            font-size: 12px;
            color: #6a7383;
        }
    }
}

#settlement-summary-container {
    .tab-content-overview__table-wrapper {
        padding: 16px 0 24px;
    }
}

.tab-content-overview {
    &__wrapper {
        display: flex;

        @media (max-width: 767px) {
            flex-direction: column;
        }
    }
    &__content {
        padding: 10px 5px 40px;
        font-size: 16px;
        color: map-get($colors-v2, 'primary');
        line-height: 1.5;
        flex-grow: 1;

        .multiple_emails-email {
            font-size: 14px;
        }

        @media (max-width: 991px) {
            padding: 10px 0px;
        }

        @media (max-width: 767px) {
            padding: 10px 0 32px;
        }

        & > * {
            margin-bottom: 24px;
        }

        h1 {
            font-size: 32px;
        }

        h2 {
            font-size: 30px;
        }

        h3 {
            font-size: 28px;
        }

        h4 {
            font-size: 20px;
        }

        h5 {
            font-size: 18px;
        }

        h6 {
            font-size: inherit;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            padding-top: 10px;
            font-weight: 600;
            line-height: 1.1;
        }

        ul,
        ol {
            li {
                margin-bottom: 20px;

                &:last-child {
                    margin-bottom: 0;
                }

                strong {
                    font-weight: 600;
                    display: block;
                }
            }
        }

        .dropdown-menu li {
            margin-bottom: 0;
        }

        .multiple_emails {
            &-ul {
                margin-bottom: 0;
            }
            &-email {
                margin-bottom: 5px;
            }
        }

        .alert-warning {
            padding: 16px;
            display: inline-flex;
            max-width: 100%;
            background: #fcf3cd !important;
            color: #826c20 !important;
            border-radius: 4px;
        }

        .btn-outline-secondary {
            background: transparent;
            border: 1px solid currentColor !important;
            color: map-get($colors-v2, 'secondary');
            font-size: 14px;
            font-weight: 600;
            line-height: 20px; /* 142.857% */
            padding: 4px 8px;
        }
    }
    &__table {
        width: 100%;
        min-width: 320px;
        &-wrapper {
            padding: 16px 32px 24px;
            overflow: auto;
            max-width: 100%;

            @media (max-width: 767px) {
                padding: 16px 0 24px;
            }
        }
        thead {
            th {
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
                text-transform: uppercase;
                padding: 12px 16px;
                background: map-get($colors-v2, 'container');
                border-bottom: 1px solid #e3e8ee;
            }
        }

        td,
        th {
            &:last-child {
                text-align: right;
            }
        }

        td {
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.25;
            border-bottom: 1px solid #e3e8ee;

            p {
                padding: 0 0 0 32px;
                color: map-get($colors-v2, 'secondary');
                font-size: 14px;
                font-weight: 600;
                line-height: 20px;
                margin: 0;

                @media (max-width: 767px) {
                    padding-left: 16px;
                }
            }
        }

        &:has(p) {
            td {
                padding-top: 10px;
                padding-bottom: 10px;
            }
        }
    }

    &__sidebar {
        flex-shrink: 0;
        width: 210px;
        border-left: 1px solid map-get($colors-v2, 'border');

        &-content {
            position: sticky;
            top: 0;
            left: 0;
            color: map-get($colors-v2, 'secondary');

            h6 {
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 1.5;
                text-transform: uppercase;
                padding-left: 15px;
            }

            ul {
                margin: 0;
                padding: 0;
                list-style-type: none;
            }

            li {
                width: 100%;
                margin: 0;
                padding: 0;
            }

            a {
                display: block;
                padding: 8px 16px;
                color: inherit;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 16px; /* 133.333% */
                transition: 0.15s ease-in-out;
                border-left: 1px solid transparent;

                &:hover,
                &:focus {
                    text-decoration: none;
                    color: map-get($colors-v2, 'blue-alt');
                }

                &.active {
                    color: map-get($colors-v2, 'primary');
                    font-weight: 600;
                    border-left-color: map-get($colors-v2, 'blue-alt');
                }
            }
        }

        @media (max-width: 767px) {
            display: none;
        }
    }
}
