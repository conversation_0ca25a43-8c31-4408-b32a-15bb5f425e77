.store-v2-sl {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // pointer-events: none;
    display: none;
    padding: 20px 20px 0;
    z-index: 99;
    background: #fff;
    flex-direction: column;

    &.isLoading {
      display: flex;
    }

    &__main {
      display: flex;
      padding: 20px 0 0;
      gap: 20px;
      flex: 1 0;
      overflow: hidden;
    }

    &__nav {
        display: flex;
        height: 43px;
        align-items: center;
        gap: 24px;
        grid-column: span 2;
        border-bottom: 1px solid map-get($colors-v2, 'border');
        flex: 0 0 43px;

        &__item {
            width: 64px;
            height: 16px;

            &:nth-child(2) {
                width: 90px;
            }

            &:nth-child(3) {
                width: 30px;
            }
        }
    }

    &__products {
        display: grid;
        gap: 16px;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        align-content: start;
        flex: 1;
        overflow-y: auto;
    }

    &__product {
        border: 1px solid map-get($colors-v2, 'border');
        border-radius: 4px;
        height: 285px;
        background: #fff;
        padding: 16px;

        &__image {
            height: 150px;
            width: 100%;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        &__name {
            height: 32px;
            width: 90%;
            border-radius: 4px;
        }

        &__price {
            height: 18px;
            width: 72px;
            border-radius: 4px;
        }

        &__btn {
            height: 28px;
            width: 100%;
            border-radius: 4px;
        }
    }

    &__search {
        height: 38px;
        width: 100%;
        margin-bottom: 16px;
    }

    &__range {
        line-height: 1;
        &__label {
            width: 80px;
            height: 16px;
            margin-left: 10px;
            margin-bottom: 1px;
        }

        &-bar {
            display: flex;
            align-items: center;

            &__indicator {
                height: 20px;
                width: 20px;
                flex: 0 0 20px;
            }

            &__progress {
                flex: 1 0;
                height: 4px;
            }
        }

        &__label-2 {
            margin: 16px 20px;
            width: 210px;
            max-width: 100%;
            height: 12px;
        }
    }

    &__filter {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 0 20px 12px;

        &__label {
            height: 16px;
            width: 150px;
            max-width: 100%;
            display: block !important;
            margin-bottom: 10px;
        }

        &__check {
          width: 15px;
          height: 15px;
        }

        &__text {
          height: 12px;
          width: 40px;
        }
    }

    &__sidebar {
      min-width: 250px;
      max-width: 350px;
      width: 30%;
    }
}

.container-sm {
  .store-v2-sl {
    &__sidebar {
      width: 100%;
      max-width: 100%;
      min-width: 0;
      height: 28px;
      flex: 0 0 28px;
      order: -99;
      overflow: hidden;
    }
    &__main {
      flex-direction: column;
    }
    &__nav {
      justify-content: center;
    }
  }
}