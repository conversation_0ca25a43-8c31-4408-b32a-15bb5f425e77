﻿@media (max-width: map-get($breakpoints, 'sm')) {
    .club-spoof-select {
        .dropdown-menu.open {
            background-color: white !important;
            // background-color: #fff !important;
            // position: absolute;
        }

        // Smaller search icon on bootstrap select...
        .bs-searchbox:after {
            top: 8px !important;
        }
    }
}

.club-spoof-select {
    max-width: 300px;
    .country-flag {
        float: right;
    }
}

.bootstrap-select.open {
    .btn.dropdown-toggle {
        border: 2px solid #0094f7 !important;
    }
}
.bootstrap-select {
    .btn.dropdown-toggle:focus:not(.disabled) {
        border: 2px solid #0094f7 !important;
    }
    .dropdown-menu {
        border-radius: 5px;
        li.active small {
            color: black !important;
        }
    }
    .inner.open {
        overflow-x: hidden;
    }

    box-shadow: none !important;

    @include border-radius(4px);

    button {
        box-shadow: none !important;
    }

    .bs-caret {
        top: 0px !important;
    }

    .dropdown-toggle {
        &:focus,
        &:active {
            outline: none !important;
        }
        &.btn-default:hover {
            border-color: #262626;
            background: #fff !important;
        }
        .caret {
            margin-right: 0 !important;
            right: 0;
        }
    }

    .badge-12RND {
        background-color: #6fcc41;
    }
    .badge-warning {
        background-color: red;
    }
    .badge-12RND,
    .badge-UBX {
        margin-left: 10px;
        margin-top: 4px;
        float: right;
    }
    .filter-option-inner-inner {
        color: $btn-color-primary;
        text-transform: none;
        .badge {
            margin-right: 5px !important;
        }
        max-height: 20px;
    }
    &.club-spoof-select {
        .filter-option-inner-inner {
            color: #6a7383;
        }
    }
    span.text {
        width: 100%;
    }
    .dropdown-menu.open {
        span.text {
            min-width: 250px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        span.country-flag {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        a.dropdown-item {
            display: flex;
            align-items: center;
            gap: 3px;
        }
    }

    .bs-searchbox,
    .bs-actionsbox,
    .bs-donebutton {
        padding: 0 0 5px 0;
        border-bottom: 1px solid #e9e9e9;

        .form-control {
            @include border-radius(0);
            @include box-shadow(none !important);
            border: none;
            margin-left: 30px;
        }
    }

    .bs-searchbox {
        position: relative;

        &:after {
            content: '\E8B6';
            font-family: 'Material Icons';
            position: absolute;

            // Smaller search icon...
            font-size: 15px;
            top: 10px;
            left: 15px;
        }
    }

    ul.dropdown-menu {
        margin-top: 0 !important;
    }

    .dropdown-menu {
        li.selected {
            a {
                background-color: rgba(0, 148, 247, 0.08) !important;
                color: #555 !important;
            }
        }

        .active {
            a {
                background-color: transparent;
                color: #333 !important;
            }
        }

        .notify {
            background-color: #f44336 !important;
            color: #fff !important;
            border: none !important;
        }
    }

    // center tick mark vertically for all options
    .dropdown-item .check-mark {
        top: 50% !important;
        transform: translateY(-50%) !important;
    }
}

.bootstrap-select.btn-group.show-tick {
    .dropdown-menu {
        li.selected {
            a {
                span.check-mark {
                    margin-top: 9px;
                }
            }
        }
    }
}

.bootstrap-select.swal2-select {
    display: none !important;
}