﻿.nav-tabs {
    border-bottom: 2px solid #eee;
    display: flex;

    > li {
        position: relative;
        top: 3px;

        > a {
            border: none !important;
            color: #6a7383 !important;
            padding: 0 0 10px;
            font-size: 16px;
            line-height: 1.5;
            text-transform: capitalize;
            font-weight: 600;
            margin-right: 20px;
            @include border-radius(0);

            &:hover,
            &:active,
            &:focus {
                background-color: transparent !important;
            }

            &:before {
                content: '';
                position: absolute;
                left: 0;
                width: 100%;
                height: 0;
                border-bottom: 2px solid $accent-blue-400;
                bottom: 2px;
                @include transform(scaleX(0));
                @include transition(0.1s ease-in);
            }

            .material-icons {
                position: relative;
                top: 7px;
                margin-bottom: 8px;
            }

            .fa {
                top: 0;
                margin-right: 8px;
            }
        }
    }

    li.active {
        a {
            color: $accent-blue-500 !important;

            &:hover,
            &:active,
            &:focus {
                background-color: transparent !important;
            }

            &:before {
                @include transform(scaleX(1));
            }
        }
    }

    + .tab-content {
        padding: 15px 0;
    }
}

@each $key, $val in $colors {
    .nav-tabs.tab-col-#{$key} {
        > li {
            > a {
                &:before {
                    border-bottom: 2px solid $val;
                }
            }
        }
    }
}
