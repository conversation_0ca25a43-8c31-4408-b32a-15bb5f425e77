﻿body {
    background-color: #f8fafb;
    @include transition(all 0.5s);
    font-family: '<PERSON><PERSON>', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
    color: #30313d;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
}

button,
input,
select,
a {
    outline: none !important;
}

.no-animate {
    -o-transition-property: none !important;
    -moz-transition-property: none !important;
    -ms-transition-property: none !important;
    -webkit-transition-property: none !important;
    transition-property: none !important;
    -o-transform: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -webkit-transform: none !important;
    transform: none !important;
    -webkit-animation: none !important;
    -moz-animation: none !important;
    -o-animation: none !important;
    -ms-animation: none !important;
    animation: none !important;
}

section {
    &.content {
        margin: 70px 15px 0 315px;
        padding-top: 20px;

        &.full-height-content {
            padding: 0;
            margin: 70px 0 0 300px;
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;

            &.enable-scroll {
                overflow-y: auto;
                overflow-x: hidden;
            }

            &.smallSidebarContent {
                margin: 70px 0 0 70px !important;
            }

            .container-fluid {
                padding: 0;
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                top: 0;
                max-width: none;
            }

            .iframe-placeholder {
                height: 100%;
                box-shadow: none;
                -webkit-box-shadow: none;
                margin: 0;
                border-radius: 0;
            }
            iframe:not([id^="ticketingThread"]) {
              height: 100%;
            }
        }

        @include transition(0.5s);
    }
}

// Styling adjustments when .stage-environment-active is present on a parent
.stage-environment {
    section.content.full-height-content {
        // Specify the different margin here
        margin-top: 106px !important;
    }
}


// Wider 'views' on mobile...
@media (max-width: map-get($breakpoints, 'sm')) {
    .content {
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    ._hj_feedback_container {
        visibility: hidden;
    }
}

label {
    font-weight: 600;
}

.is-relative {
    position: relative;
}

.skeleton-section {
    background-color: map-get($colors-v2, 'skeleton');
    border-radius: 4px;
    min-height: 1px;
    min-width: 10px;
    animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;

    &.circle {
        border-radius: 500px;
    }
}

@keyframes skeletonLoader {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.4;
    }
}

// fadeIn animation
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.5s ease-in-out forwards;
}

.grow-fade-in {
    animation: growFadeIn 0.5s ease-in-out forwards;
}

@keyframes growFadeIn {
    from {
        transform: scale(1, 0);
        opacity: 0;
    }
    to {
        transform: scale(1, 1);
        opacity: 1;
    }
}
