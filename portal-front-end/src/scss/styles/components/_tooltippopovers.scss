﻿.tooltip {
    font-size: 13px;

    .tooltip-inner {
        @include border-radius(0);
    }
}

.popover {
    @include border-radius(0);
    border: 1px solid rgba(0, 0, 0, 0.08);

    .popover-title {
        font-weight: bold;
        @include border-radius(0);
        background-color: #e9e9e9;
        border-bottom: 1px solid #ddd;
    }

    .popover-content {
        font-size: 13px;
        color: #777;
        @include border-radius(0);
    }
}


/* Copied to clipboard CSS tooltip */
.copied-tooltip {

    position: absolute !important;
    margin-top: -25px;
    margin-left: -10px;

    position: relative;
    background: #F2D1C9;

    border-radius: 3px;
    padding: 0 20px;

    display: inline-block;

    transition: all 0.3s ease-in-out;
    cursor: default;
    z-index: 2147483647;

    .ttip {
        z-index: 2147483647;
        visibility: visible;
        opacity: .40;

        padding: 4px 4px 4px 6px;
        background: #333;
        color: white;

        position: absolute;
        top:-140%;
        left: -25%;

        border-radius: 3px;
        font-size: 11px;

        transform: translateY(-10px);
        transition: all 0.5s ease-in-out;
        box-shadow: 0 0 3px rgba(56, 54, 54, 0.86);
        opacity: 1;
        // transition: .5s linear;
        p {
            margin: unset;
        }
    }

    .ttip::after {
        content: " ";
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 10px 0 10px;
        border-color: #333 transparent transparent transparent;
        position: absolute;
        left: 30%;
    }

}
