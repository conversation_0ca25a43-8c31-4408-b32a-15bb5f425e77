﻿.dz-max-files-reached {
    pointer-events: none !important;
    cursor: default !important;
}

// .dropzone {
//     border: 2px solid transparent !important;
//     background-color: #eee !important;

//     .dz-message {
//         .drag-icon-cph {
//             .material-icons {
//                 font-size: 80px;
//                 color: #777;
//             }
//         }
//     }
// }


// .dz-drag-hover {
//     border: 2px dashed #888 !important;
// }


/* Base styles for Dropzone */
.dropzone {
    border: 2px dashed #d2d9e0; /* Soft border color */
    border-radius: 8px;
    background-color: #f6f9fc; /* Soft background color */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); /* Subtle shadow */
    transition: border 0.2s; /* Smooth transitions for hover and active states */
}

.dropzone:hover {
    border-color: #c4ccd3; /* Change border color on hover */
}

.dropzone.dragover {
    border-color: #a4adb3; /* Change border color when a file is dragged over */
}

/* Message styling */
.dropzone .dz-message {
    color: #717d8a; /* Soft text color */
    font-size: 1.2em;
    transition: color 0.2s; /* Smooth transition for text color */
}

.dropzone:hover .dz-message {
    color: #5a6671; /* Slightly darker text on hover */
}

/* Thumbnail and close button */
.dropzone .dz-preview {
    border-radius: 4px; /* Rounded corners for thumbnails */
    overflow: hidden; /* Ensures rounded corners for images too */
}

.dropzone .dz-preview .dz-remove {
    color: #d2d9e0; /* Soft color for the close button */
    transition: color 0.2s; /* Smooth transition for the close button */
}

.dropzone .dz-preview .dz-remove:hover {
    color: #b0b9c6; /* Slightly darker color on hover for the close button */
}
