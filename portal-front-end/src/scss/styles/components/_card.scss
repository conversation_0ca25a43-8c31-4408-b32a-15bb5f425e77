﻿.container-fluid {
    padding-left: 7.5px;
    padding-right: 7.5px;
}
.iframe-placeholder {
    width: 100%;
    height: calc(100vh - 120px);
}
.card.noboxshadow {
    box-shadow: none;
}
.card {
    background: #fff;
    min-height: 50px;
    // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.20);
    box-shadow: 0 0px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    //margin-bottom: 30px;
    margin-bottom: 15px;
    @include border-radius(2px);

    &__no-ui {
        border: none;
        box-shadow: none;
    }

    .card-inside-title {
        display: block;
        font-size: 15px;
        color: $slate-700;

        &:not(.--compact) {
            margin-top: 25px;
            margin-bottom: 15px;
        }

        small {
            color: $slate-450;
            display: block;
            font-size: 11px;
            margin-top: 5px;

            a {
                color: #777;
                font-weight: bold;
            }
        }
    }

    .card-inside-title:first-child {
        margin-top: 0;
    }

    .bg-red,
    .bg-pink,
    .bg-purple,
    .bg-deep-purple,
    .bg-indigo,
    .bg-blue,
    .bg-light-blue,
    .bg-cyan,
    .bg-teal,
    .bg-green,
    .bg-light-green,
    .bg-lime,
    .bg-yellow,
    .bg-amber,
    .bg-orange,
    .bg-deep-orange,
    .bg-brown,
    .bg-grey,
    .bg-blue-grey,
    .bg-black {
        border-bottom: none !important;
        color: #fff !important;

        h2,
        small,
        .material-icons {
            color: #fff !important;
        }

        .badge {
            background-color: #fff;
            color: #555;
        }
    }

    .header {
        color: #555;
        padding: 20px;
        position: relative;
        border-bottom: 1px solid rgba(204, 204, 204, 0.35);

        .header-dropdown {
            position: absolute;
            top: 20px;
            right: 15px;
            list-style: none;

            .dropdown-menu {
                li {
                    display: block !important;
                }
            }

            li {
                display: inline-block;
            }

            i {
                font-size: 20px;
                color: #999;
                @include transition(all 0.5s);

                &:hover {
                    color: #000;
                }
            }
        }

        h2 {
            margin: 0;
            font-size: 18px;
            font-weight: normal;
            color: #111;

            small {
                display: block;
                font-size: 12px;
                margin-top: 5px;
                color: #999;
                line-height: 15px;

                a {
                    font-weight: bold;
                    color: #777;
                }
            }
        }

        .col-xs-12 {
            h2 {
                margin-top: 5px;
            }
        }
    }

    .body {
        font-size: 14px;
        color: #555;
        padding: 20px;

        @for $i from 1 through 12 {
            .col-xs-#{$i},
            .col-sm-#{$i},
            .col-md-#{$i},
            .col-lg-#{$i} {
                margin-bottom: 20px;
            }
        }
    }
}
