﻿.noUi-target {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -ms-touch-action: none;
    touch-action: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    direction: ltr;

    * {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -ms-touch-action: none;
        touch-action: none;
        -ms-user-select: none;
        -moz-user-select: none;
        user-select: none;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
}

.noUi-base {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}

.noUi-origin {
    position: absolute;
    right: 0;
    top: 6px;
    left: 0;
    bottom: 0;
}

.noUi-handle {
    z-index: 1;

    // right: auto !important;
    // left: auto !important;

}

.noUi-handle-lower {
    right: -7px !important;
}

// .noUi-stacking {
//     .noUi-handle {
//         z-index: 10;
//     }
// }

// .noUi-state-tap {
//     .noUi-origin {
//         -webkit-transition: left 0.25s, top 0.25s;
//         transition: left 0.25s, top 0.25s;
//     }
// }

// .noUi-state-drag * {
//     cursor: inherit !important;
// }

// .noUi-base {
//     -webkit-transform: translate3d(0, 0, 0);
//     transform: translate3d(0, 0, 0);
// }

// .noUi-horizontal {
//     height: 18px;

//     .noUi-handle {
//         width: 34px;
//         height: 28px;
//         left: -17px;
//         top: -6px;
//     }
// }

// .noUi-vertical {
//     width: 18px;

//     .noUi-handle {
//         width: 28px;
//         height: 34px;
//         left: -6px;
//         top: -17px;
//     }
// }

// .noUi-background {
//     background: #FAFAFA;
//     box-shadow: inset 0 1px 1px #f0f0f0;
// }

// .noUi-connect {
//     background: #3FB8AF;
//     box-shadow: inset 0 0 3px rgba(51, 51, 51, 0.45);
//     -webkit-transition: background 450ms;
//     transition: background 450ms;
// }

// .noUi-origin {
//     border-radius: 2px;
// }

// .noUi-target {
//     border-radius: 4px;
//     border: 1px solid #D3D3D3;
//     box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;

//     &.noUi-connect {
//         box-shadow: inset 0 0 3px rgba(51, 51, 51, 0.45), 0 3px 6px -5px #BBB;
//     }
// }

// .noUi-dragable {
//     cursor: w-resize;
// }

// .noUi-vertical .noUi-dragable {
//     cursor: n-resize;
// }

// .noUi-handle {
//     border: 1px solid #D9D9D9;
//     border-radius: 3px;
//     background: #FFF;
//     cursor: default;
//     box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
// }

// .noUi-active {
//     box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;
// }

.noUi-handle {
    &:before {
        content: "";
        display: block;
        position: absolute;
        height: 14px;
        width: 1px;
        background: #E8E7E6;
        left: 14px;
        top: 6px;
    }

    &:after {
        content: "";
        display: block;
        position: absolute;
        height: 14px;
        width: 1px;
        background: #E8E7E6;
        left: 14px;
        top: 6px;
        left: 17px;
    }
}

.noUi-vertical .noUi-handle {
    &:before {
        width: 14px;
        height: 1px;
        left: 6px;
        top: 14px;
    }

    &:after {
        width: 14px;
        height: 1px;
        left: 6px;
        top: 14px;
        top: 17px;
    }
}

[disabled] {
    &.noUi-connect, .noUi-connect {
        background: #B8B8B8;
    }

    &.noUi-origin, .noUi-handle {
        cursor: not-allowed;
    }
}

.noUi-target {
    box-shadow: none;
    border: none;
}

.noUi-base {
    height: 15px;
    top: -6px;
}

.noUi-background {
    height: 3px;
    top: 6px;
    background-color: #bfbfbf;
    box-shadow: none;
}

.noUi-horizontal {
    height: 3px;
}

.noUi-connect {
    height: 3px;
    top: 6px;
    background-color: #1f91f3;
    box-shadow: none;
}

.noUi-horizontal .noUi-handle {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    box-shadow: none;
    background-color: #1f91f3;
    border: none;
    // left: -5px;
    top: -6px;
    margin-left: 25px;
    transition: width 0.2s cubic-bezier(0.215, 0.61, 0.355, 1), height 0.2s cubic-bezier(0.215, 0.61, 0.355, 1), left 0.2s cubic-bezier(0.215, 0.61, 0.355, 1), top 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.noUi-handle {
    &:before, &:after {
        content: none;
    }
}

.noUi-target {
    .noUi-active {
        &.noUi-handle {
            @include box-shadow(0 0 20px rgba(0,0,0,0.5));
        }
    }

    .range-label {
        position: absolute;
        height: 30px;
        width: 30px;
        top: -17px;
        left: -2px;
        background-color: #1f91f3;
        border-radius: 50%;
        transition: border-radius 0.25s cubic-bezier(0.215, 0.61, 0.355, 1), transform 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transform: scale(0.5) rotate(-45deg);
        transform-origin: 50% 100%;
    }

    .noUi-active .range-label {
        border-radius: 15px 15px 15px 0;
        transform: rotate(-45deg) translate(23px, -25px);
    }
}

.range-label span {
    width: 100%;
    text-align: center;
    color: #fff;
    font-size: 12px;
    transform: rotate(45deg);
    opacity: 0;
    position: absolute;
    top: 7px;
    left: -1px;
    transition: opacity 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.noUi-active .range-label span {
    opacity: 1;
}

.noUi-value-horizontal {
    margin-top: 10px;
    margin-left: 1px;
}
