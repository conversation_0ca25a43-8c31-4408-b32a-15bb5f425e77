﻿.breadcrumb {
    @include border-radius(0);
    background-color: transparent;
    font-size: 13px;
    padding: 0px;
    margin-bottom: 1rem;

    li {
        a {
            text-decoration: none;
            margin: 0 !important;
            color: $slate-450;
            font-weight: normal;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;

            .material-icons {
                font-size: 18px;
                position: relative;
                top: 4px;
            }
        }

        .material-icons {
            font-size: 18px;
            position: relative;
            top: 4px;
        }
    }

    > li + li:before {
        content: '>\00a0';
    }
}

@each $key, $val in $colors {
    .breadcrumb-col-#{$key} {
        li {
            a {
                color: $val !important;
                font-weight: bold;
            }
        }
    }

    .breadcrumb-bg-#{$key} {
        background-color: $val !important;

        li {
            a {
                color: #fff;
                font-weight: bold;

                .material-icons {
                    padding-bottom: 8px;
                }
            }

            color: #fff !important;
        }

        li + li:before {
            color: #fff;
        }
    }
}
