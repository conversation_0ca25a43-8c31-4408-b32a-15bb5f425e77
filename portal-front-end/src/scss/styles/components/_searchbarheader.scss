.isLoading {
    .searchbar-header-container {
        width: 100%;
        height: 40px;
        border-radius: 4px;
    }
}

.searchbar-header {
    background-color: #f6f8fa;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    gap: 1rem;
    flex-wrap: wrap;

    button {
        font-size: 12px !important;
        color: #30313d;

        i {
            color: #6a7383;
        }
    }

    .search-container-wrapper {
        max-width: 350px;
        flex-grow: 1;

        @include respond-below(xs) {
            max-width: 100%;
        }
    }

    .search-container {
        width: 100%;
        position: relative;

        i {
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #9fa4af;
        }

        input {
            border: 1px solid #ebeef1;
            color: rgba(0, 0, 0, 0.87);
            font-family: 'Source Sans Pro', 'Segoe UI', sans-serif;
            font-weight: 400;
            border-radius: 4px !important;
            box-shadow: none;
            padding-right: 30px;
            transition: all 0.15s ease-in-out;

            &:focus {
                outline: 2px solid #0094f7 !important;
            }
        }
    }

    @include respond-below(xs) {
        & > .buttons-container {
            width: 100%;
            justify-content: space-around;
        }
        & > .search-container-wrapper {
            width: 100%;
            .search-container {
                width: 100%;
            }
        }

        & > *:empty {
            display: none;
        }

        button > span {
            display: none;
        }
    }
}

.ph-searchbar-header {
    .expandable-buttons-container {
        display: flex;
        width: 0;
        transition: all 0.25s ease-in-out;
        overflow: hidden;
        max-width: fit-content;
        background-color: $slate-100;
        align-self: stretch;
        border-radius: $border_radius-m;
        border: 0px solid transparent;

        &.expanded {
            width: 100%;
            padding: 0 6px;
            border: 1px solid $slate-150;

            & + .expand-btn {
                i {
                    transform: rotate(180deg);
                }
            }
        }
    }
}
