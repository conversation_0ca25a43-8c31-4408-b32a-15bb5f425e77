#navigation-bar {
    z-index: 1090;

    .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }
}
.faux-text-searchbar {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    --antd-wave-shadow-color: #2755f0;
    --scroll-bar: 0;
    --primary: #2755f0;
    --vh: 15.68px;
    font-weight: 400;
    font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', Segoe UI Symbol;
    font-variant: tabular-nums;
    font-feature-settings: 'tnum';
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    color: rgba(0, 0, 0, 0.85);
    box-sizing: border-box;

    display: flex;
    //display: none;

    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
    padding: 8px 16px;
    line-height: 1;
    font-size: 15px;
    min-width: 400px;
    max-width: 455px;
    width: 100%;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: rgba(0, 0, 0, 0.03) 0px 2px 2px;
    border: 1px solid rgb(226, 227, 229);
    background-color: white;
    height: 34px;

    @media (max-width: 1100px) {
        display: none;
    }

    .search-placeholder {
        padding: 0px 14px;
        color: rgb(159, 161, 168);
        flex: 1 1 0%;
    }
    .keyboard-shortcut {
        background-color: rgb(243, 244, 245);
        border-radius: 4px;
        padding: 4px;
        box-sizing: border-box;
        color: rgb(62, 67, 81);
    }
}
#commandbar-home {
    button[aria-label='feedback'] {
        display: none;
    }
}

.smallSidebar {
    width: 70px !important;
    .menu {
        height: inherit;

        @media screen and (max-height: 699px) {
            height: calc(100vh - 70px) !important;
            overflow: hidden !important;
        }

        @media screen and (min-height: 700px) {
            height: calc(100vh - 140px) !important;
            overflow: hidden !important;
        }
    }
    .slimScrollDiv > ul,
    .slimScrollDiv {
        height: calc(100vh - 240px);
    }
    .slimScrollDiv {
        margin-bottom: 0px !important;
        position: fixed;
    }
    .menu-toggle {
        &:before {
            content: '' !important;
        }

        &:after {
            content: '' !important;
        }
    }
    a {
        .material-icons {
            z-index: -100;
        }
    }
    li a {
        i {
            transition: background-color 0.35s ease-in-out;
        }

        &:hover i {
            background-color: $slate-75 !important;
        }
    }
    ul {
        li {
            ul {
                visibility: hidden !important;
                height: 0px;
            }
        }
    }
}
.smallSidebarContent {
    margin: 70px 15px 0 85px !important;
}

.iw-contextMenu {
    z-index: 1064;
    background-color: white !important;
    padding-top: 7px;
    padding-bottom: 7px;
    border: none;

    li {
        padding: 6px;
        padding-top: 7px;
        padding-bottom: 7px;
        border-bottom: none;
    }
    .iw-mSelected {
        background-color: rgba(179, 179, 179, 0.16) !important;
        color: black;
    }
}

.navbar {
    font-family: $navbar-font-family;
    @include border-radius(0);
    @include box-shadow(0 1px 5px rgba(0, 0, 0, 0.3));
    border: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    width: 100%;

    .navbar-notifications-container {
        margin-right: 5px;

        @include respond-below(sm) {
            margin-right: 0px;
        }

        & > a {
            display: block;
            position: relative;
        }

        .notif-bell {
            color: #fff;
            font-size: 24px;

            @include respond-below(sm) {
                font-size: 28px;
            }
        }
        .notif-bell-spinner {
            position: absolute;
            font-size: 16px;
            color: white;
            top: 0px;
            right: 0px;
            animation: spin 1s infinite linear;
            display: none;
        }
        .label-count {
            position: absolute;
            top: 2px;
            right: 1px;
            font-size: 12px;
            font-weight: 700;
            line-height: 15px;
            background-color: #e91d63;
            color: #fff;
            padding: 0 4px;
            @include border-radius(3px);

            @include respond-below(sm) {
                top: -4px;
                right: -4px;
            }
        }

        &.loading {
            pointer-events: none;

            .notif-bell {
                opacity: 0.4;
            }
            .notif-bell-spinner {
                display: unset;
            }
            .label-count {
                display: none;
            }
        }
    }

    .navbar-mobile-club-id {
        position: absolute;
        right: 8px;
        top: 22px;
        gap: 1.5rem;

        // Only visible on small mobile devices (so they know what club is selected...)
        display: none;

        @media (max-width: map-get($breakpoints, 'sm')) {
            display: flex;
        }

        i {
            color: white;
        }
    }
    .navbar-brand {
        @include three-dots-overflow();

        @media (max-width: map-get($breakpoints, 'xs')) {
            .navbar-stage {
                display: none;
            }
            .navbar-brand-text {
                font-size: 15px;
                margin-top: 2px;
            }
        }
    }
    .navbar-brand-text {
        display: inline-block;
        white-space: nowrap;
        position: absolute;
        margin-left: 10px;
        margin-top: 3px;
    }

    .navbar-stage {
        align-self: flex-start;
        height: auto;
        width: auto;
        visibility: hidden;
        white-space: nowrap;
        font-size: 11px;
        margin-left: 10px;
        padding: 0px;
        padding-left: 5px;
        padding-right: 5px;
        background-color: #000;
        color: red;

        @include respond-below(sm) {
            position: absolute;
            top: 50px;
            left: 55px;
        }
    }

    .navbar-custom-right-menu {
        float: right;
    }

    .navbar-toggle {
        text-decoration: none;
        color: #fff;
        width: 20px;
        height: 20px;
        margin-top: -4px;
        margin-right: 17px;

        &:before {
            content: '\E8D5';
            font-family: 'Material Icons';
            font-size: 26px;
        }

        // On mobile devices - push the button to the left slightly....
        @media (max-width: map-get($breakpoints, 'sm')) {
            margin-right: 30px;
        }
    }

    .navbar-collapse {
        &.in {
            overflow: visible;
        }
    }
}

#gymMasterPortalNav {
    display: none;
}

.ls-closed {
    .sidebar {
        margin-left: -300px;
    }

    section.content {
        margin-left: 0;
    }

    .bars {
        &:after,
        &:before {
            font-family: 'Material Icons';
            font-size: 24px;
            position: absolute;
            top: 18px;
            left: 20px;
            margin-right: 10px;
            @include transform(scale(0));
            @include transition(all 0.3s);
        }

        &:before {
            content: '\E5D2';
            @include transform(scale(1));
        }

        &:after {
            content: '\E5C4';
            @include transform(scale(0));
        }
    }

    .navbar-brand {
        margin-left: 30px;
    }
}

.overlay-open {
    .bars {
        &:before {
            @include transform(scale(0));
        }

        &:after {
            @include transform(scale(1));
        }
    }
}

.navbar-header {
    padding: 10px 7px;

    .bars {
        float: left;
        text-decoration: none;
    }
}

.profile-dropdown-container,
.profile-dropdown-container-mobile {
    & > button {
        background-color: unset;
        border: 0px;
    }
}

.profile-dropdown {
    margin-top: 0px !important;
    padding: 1rem;
    min-width: fit-content;

    i {
        color: #30313d;
    }

    .signed-in-user {
        b {
            font-style: normal;
        }
        width: max-content;
    }

    .profile-dropdown-actions {
        display: flex;
        flex-direction: column;

        & > a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            cursor: pointer;
            color: unset;

            span {
                transform: translateY(2px);
            }

            &:hover {
                text-decoration: none;
                color: #337ab7;
            }
        }
    }

    &::before {
        content: '';
        position: absolute;
        top: -10px;
        right: 10px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid #fff;
    }
}

.navbar-nav {
    & > li > *:is(a, button) {
        padding: 7px 7px 2px 7px;
        margin-top: 3px;
        margin-left: 2.5px;
        margin-right: 2.5px;
        background-color: unset;
        border: 0px;
    }
}

@each $key, $val in $colors {
    .col-#{$key} {
        .navbar {
            @include navbar-link-color(#fff, #000, 0.95);
            // @include navbar-link-color(rgba(0,0,0,0.85), #000, .95);
        }
    }
}
.navbar-right {
    margin-right: 0px;
}
.club-spoof-wrapper {
    display: none;
    min-width: 200px;
    max-width: 280px;
    padding-top: 3px;

    @include respond-below(sm) {
        min-width: 0px;
        padding-top: 0px;
        padding-left: 10px;
    }

    .dropdown-menu {
        .selected.active {
            .glyphicon-ok {
                // No checkmark on this element...
                display: none !important;
            }
        }
        .inner {
            // prevents "show unpublished facilities" from going off the screen
            max-height: 80vh !important;
            li {
                overflow: hidden;
            }
        }
        .dropdown-header {
            span {
                font-weight: bold;
                font-size: 12px;
                // margin-top: -2px;
                padding-bottom: 2px;
            }
        }
    }

    .facility-name-wrapper {
        position: relative;
    }

    .published-indicator {
        position: absolute;
        top: 50%;
        left: -15px;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        transform: translateY(-50%);
        background-color: $slate-350;

        &.on {
            background-color: $accent-blue-500;
        }
    }

    input[type='search']::-webkit-search-cancel-button {
        -webkit-appearance: none;
        cursor: pointer;
        height: 12px;
        width: 12px;
        opacity: 0.75;

        background-image: url('data:image/svg+xml;utf8,
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M929.8 10L499 440.3 70.2 12 10 72.3 438.8 500 10 928.2 70.2 988 499 560.2 929.8 990l60.2-59.7L559.2 500 990 70.2 929.8 10z"/></svg>
        ');
    }
}

.facility-select-custom-tooltip {
  position: fixed;
  background: #1a2631;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  z-index: 9999;
  white-space: nowrap;
  pointer-events: none;
  transition: opacity 0.2s ease;
  opacity: 0;
}

.facility-select-custom-tooltip .tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #1a2631;
}

// Fix to the dropdown menu going off the screen with latest bootstrap dropdown version...
.bootstrap-select .bs-searchbox .form-control {
    margin-left: auto !important;
    padding-left: 40px;
}

.swal2-container:has(.spoof-dialog, .profile-dialog) {
    padding: 0px;
    display: flex;

    .swal2-modal {
        justify-content: flex-start;

        @include respond-below(sm) {
            width: 100%;
            height: 100%;
        }
    }

    .profile-dropdown {
        padding: 0px;
    }
    .profile-dropdown::before {
        content: none;
    }

    fieldset {
        margin: 0;
        margin-top: 6px;
        overflow: hidden;
        max-height: calc(100vh - 48px);
        display: flex;
        flex-direction: column;
    }

    .options-container {
        overflow: auto;
        margin-top: 2rem;

        ul.options {
            list-style: none;
            padding: 0px;

            li {
                padding: 1rem 0px;
                border-bottom: 1px solid #eee;
            }
        }
    }

    .close-btn {
        position: absolute;
        top: 18px;
        right: 15px;
        background-color: #fff;
        font-size: 18px;
    }
}

.navbar-logo {
    display: flex;
    div {
        height: 50px;
        width: 230px;
        padding: 8px;
        color: white;
        img {
            width: '100%';
        }
    }
    $logo-width: 34.4px;
    img.org-logo {
        width: $logo-width;
        height: $logo-width;
        object-fit: contain;
        border-radius: 50%;
    }
}

.nav-tab-item.nav-tab-item {
    display: flex !important;
    align-items: center;
    justify-content: center;
    color: $slate-450;
    margin-right: 0 !important;
    padding: 0 !important;
    transition: color 0.3s ease-in-out;

    &.settings-icon-outline {
        width: 20px;
        height: 20px;
    }

    svg {
        margin-right: 0 !important;
    }

    &::before {
        display: none;
        content: none;
    }

    &:hover {
        color: $slate-500;
    }
}
