
// Algolia Overlay (background)
.aa-DetachedOverlay{
    z-index: 999999;
    position: absolute;
    background: rgba(0, 0, 0, 0.5); // Change to rgba for opacity

}

// Algolia search element on dashboard/articles...
.aa-Autocomplete {
    display: none;
    // .aa-DetachedSearchButton:focus {
    //     border-color: #0094f7 !important;
    //     box-shadow: #0094f721 0 0 0 3px, inset #0094f724 0 0 0 2px !important;
    // }
    // .aa-DetachedSearchButtonIcon {
    //     color: #308cea!important;
    // }
}

// Algolia Widget
.aa-DetachedContainer--modal {
    top: 15px !important;
    max-height: calc(100vh - 30px);

    .aa-PanelLayout {
      padding: 4px 12px 12px;
    }

    .aa-Panel {
      display: flex;
      flex-direction: column;

      &--scrollable {
        flex: 1;
        padding-bottom: 56px !important;
        scrollbar-color: unset !important;
      }
    }
}

.aa-Source {
  position: static !important;
}

.aa-Panel--stalled .aa-Source {
  filter: none !important;
  opacity: 1 !important;

  .aa-SourceHeader, .aa-List {
    opacity: .8;
    filter: grayscale(1);
  }
}

.aa-SourceFooter {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  width: 100%;
  height: 40px;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  z-index: 10;
  padding: 0 12px;
  border-top: 1px solid rgba(235, 238, 241, 0.8);
  gap: 20px;

  .aa-footer-icon {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 14px;
    color: #6A7383;

    svg {
      width: 16px;
      height: 16px;
      flex: 0 0 16px;
    }
  }

  .aa-grouped-shortcut {
    display: inherit;
    gap: inherit;
    align-items: inherit;
  }
}

.aa-DetachedContainer {
    .aa-SourceHeaderTitle {
        color: #6A7383 !important;
        font-size: 11px;
        text-transform: uppercase;
        font-weight: 600;
    }

    .aa-Label {
        margin-top: 8px;
        svg {
            color: #D9D9D9!important;
        }
    }

    .aa-SourceHeaderLine {
        border-bottom: 1px solid #D9D9D9!important;
        opacity: 1;
    }

    .aa-ItemLink{
        color: #30313D;
        &:hover {
            text-decoration: none;
        }
    }

    .aa-ItemIcon{
        height: 16px;
        width: 16px;
        box-shadow: none;
        color: inherit;
        opacity: .6;
    }

    .aa-ItemContentTitle {
        mark, em {
            color: #308cea;
        }
        em {
          font-weight: 600;
          font-style: normal;
        }
    }
    .aa-Item[aria-selected=true] {
        // background-color: rgb(173 193 214 / 21%) !important;
        background-color: rgba(173, 193, 214, 0.21) !important; // Use rgba with commas
    }


    .aa-ItemContentDescription{

        overflow-y: hidden;
        overflow-x: hidden;

        mark, em {
            color: #308cea;
            background: #e9f4ff;
        }
    }

    .aa-no-results {
        padding: 20px;
    }


    .aa-Form {
        margin: 1px;
        &:focus{
            border: solid 2px #0094f7!important;
            margin: 0px;
        }
        &:focus-within{
            border: solid 2px #0094f7!important;
            outline: 0 !important;
            box-shadow: none;
            margin: 0px;
        }

    }
}

.aa-DetachedSearchButton{
    &:focus {
        box-shadow: none;
        border: solid 2px #0094f7!important;
    }
}


.theme-black {
  --aa-input-border-color-rgb: 235, 238, 241;

  .aa-DetachedContainer {
      .aa-Form {
          height: 34px;
          font-size: 14px;
          display: flex;
          flex-direction: row-reverse;
      }
      .aa-Input {
          padding: 0 10px;
      }
      .aa-Label svg {
          color: #6a7383 !important;
      }
      .aa-SourceHeader {
        margin-top: 0;
        z-index: 9;
      }
      --modal {
          max-width: 620px;
      }
  }

  .aa-DetachedFormContainer {
      border-bottom: none !important;
      padding: 12px;
  }

  .aa-DetachedCancelButton {
      text-transform: uppercase;
      font-weight: 600;
      font-size: 14px;
      color: #30313D
  }

  .aa-Item {
    border-radius: 4px;
    padding: 10px;
    color: #30313D;

    // .aa-ItemIcon {
    //   display: none;
    // }

    .aa-ItemContentTitle {
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
    }

    .aa-badge {
      padding: 2px 4px;
      border-radius: 4px;
      background-color: #F5F6F7;
      font-size: 11px;
      line-height: 11px;

      &:empty {
        display: none;
      }
    }

    .aa-ItemContentBadges {
      display: flex;
      gap: 8px;
    }

    .aa-ItemContentDescription {
      color: #6A7383;
    }
  }

  .search-toggle-mobile {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    margin-top: 5px;
    display: none;

    @media (max-width: 1100px) {
      display: block;
    }
  }

  .navbar-mobile-club-id .search-toggle-mobile {
    margin-top: 0
  }

  .aa-ItemContentBody {
    gap: 0
  }
}

.aa-skeletonLoader {
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 12px;

  &__icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  &__title {
    width: 400px;
    max-width: 100%;
    height: 16px;
  }

  &__subtitle {
    width: 300px;
    max-width: 100%;
    height: 14px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-grow: 1;
  }
}

.view-design-search:not(.btn-link):not(.btn-circle).btn-default {
  height: 32px;
  border: none;
  padding-left: 16px;
  padding-right: 0;
  font-size: 11px;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -16px;
  background: #fff !important;
  z-index: calc(var(--aa-base-z-index) + 1);
  color: #6A7383 !important;
  text-transform: none;

  i {
    margin-right: 0;
    margin-left: 8px;
  }

  &:hover {
    border: none;
    color: #30313D !important;
  }
}

.aa-Source[data-autocomplete-source-id="marketingprint"],
.aa-Source[data-autocomplete-source-id="store-products"] {
  .aa-SourceFooter:empty {
    display: none;
  }
  .aa-SourceFooter {
    position: relative;
    bottom: auto;
    z-index: 9;
    padding: 0; /* Ensures consistent padding with regular items */
    border-top: none;

    .show-more-results {
      cursor: pointer;
      width: 100%;
      display: flex;
      align-items: center; /* Aligns content vertically */
      padding: 10px; /* Match padding with .aa-Item */
      margin: 0; /* Ensure no extra margin */
      border-radius: 4px; /* Consistent with search result items */
      text-decoration: none; /* Removes underline */

      &:hover {
        background-color: rgba(173, 193, 214, 0.21); /* Match hover background for results */
      }

      .aa-ItemContent {
        display: flex;
        align-items: center;
        width: 100%;

        .material-icons {
          margin-right: 8px; /* Match spacing with regular search result icon */
          color: #6A7383; /* Ensure consistent color */
          font-size: 22px; /* Match icon size */
        }

        .aa-ItemContentTitle {
          font-size: 14px; /* Ensure consistent font size */
          color: #30313D; /* Consistent text color */
        }
      }
    }
  }
  .aa-Item {
    border-radius: 4px;
    padding: 10px; /* Match padding here */
    margin: 0; /* Ensure no extra margin for uniformity */
    color: #30313D;

    &:hover {
      background-color: rgba(173, 193, 214, 0.21); /* Ensure hover background appears */
    }

    &[aria-selected=true]:hover {
      background-color: rgba(173, 193, 214, 0.21) !important; /* Match hover background */
    }

    &[aria-selected=true] {
      background-color: transparent !important; /* Ensure no background when not hovered */
    }
  }
  .aa-designImage {
    border-radius: 4px;
    overflow: hidden;
    background-color: map-get($colors-v2, 'border');
    width: 75px;
    max-width: 75px;

    img {
      transition: 0.5s;
    }
    &:has(.img-loading) {
      animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
    }

    &:has(.img-not-found) {
      background: map-get($colors-v2, 'border') url('../../../assets/images/carbon_no-image.png') no-repeat center center;
      background-size: 24px;
    }

    &:has(.img-loading),
    &:has(.img-not-found) {
      opacity: 0.5;
      height: 75px;

      img {
        opacity: 0;
      }
    }
    &:has(.img-loaded) {
      opacity: 1;
    }
  }
}

.aa-Source[data-autocomplete-source-id="store-products"] {
  .aa-designImage img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}


.aa-Source[data-autocomplete-source-id="cctv-people"] {
  .aa-designImage {
    background: none;
    width: 40px;
    height: 40px;
    max-width: 40px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 4px 4px rgba(0,0,0,0.1);
    position: relative;
    aspect-ratio: 1/1;
    overflow: hidden;

    &:has(.img-loading) {
      animation: 1.5s ease-in-out 0.5s infinite normal none running skeletonLoader;
      background: #d7dce3;
    }

    &:has(.img-not-found) {
      background-image: url('../../../assets/images/members-icon.svg');
      background-size: 48px;
      background-repeat: no-repeat;
      background-position: center;
      border: none;
    }
    
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition: opacity 0.5s;

      &.img-loaded {
        opacity: 1;
      }
    }
  }
  .aa-ItemContent {
    overflow: visible;
  }
}
