﻿
.table-hover > tbody > tr:hover {
    background-color: #f5f7f9;
}
.table {
    tbody {
        tr {
            td, th {
                padding: 10px;
                border-top: 1px solid #eee;
                border-bottom: 1px solid #eee;
            }
            td {
                color: #6A7383
            }
        }

        tr.primary {
            td, th {
                background-color: #1f91f3;
                color: #fff;
            }
        }

        tr.success {
            td, th {
                background-color: #2b982b;
                color: #fff;
            }
        }

        tr.info {
            td, th {
                background-color: #00b0e4;
                color: #fff;
            }
        }

        tr.warning {
            td, th {
                background-color: #ff9600;
                color: #fff;
            }
        }

        tr.danger {
            td, th {
                background-color: #fb483a;
                color: #fff;
            }
        }
    }

    thead {
        tr {
            th {
                padding: 10px;
                border-bottom: 1px solid #eee;
            }
        }
    }
}

.table-bordered {
    border-top: 1px solid #eee;

    tbody {
        tr {
            td, th {
                padding: 10px;
                border: 1px solid #eee;
            }
        }
    }

    thead {
        tr {
            th {
                padding: 10px;
                border: 1px solid #eee;
            }
        }
    }
}
