﻿$btn-color-primary: #0094f7;
$btn-color-success: #00a40d;
$btn-color-danger: #b3093c;
$btn-color-info: #00b0e4;

.copy-clipboard {
    display: inline-block;
    cursor: pointer;
    color: $icon-color;

    svg {
        width: 0.875em;
        transform: translateY(3px);
    }
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 6px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    justify-content: center;
    line-height: 1.1;

    &:focus {
        outline: none !important;
    }

    &-lg {
        padding: 10px;
    }

    &-lg-x {
        padding-left: 12px;
        padding-right: 12px;
    }

    &-edit-notice,
    &-delete-notice {
        padding: 6px;
    }

    i,
    svg {
        font-size: 18px;
        position: relative;
        margin-right: 6px;
    }

    &-square {
        span,
        i,
        svg {
            margin-right: 0 !important;
        }
    }

    &.animate-pulse {
        animation: pulse 0.4s ease infinite alternate;
    }
}

#html5-qrcode-button-camera-permission:hover,
#html5-qrcode-button-camera-start:hover,
#html5-qrcode-button-camera-stop:hover {
    border: 1px solid $slate-200;
    background: $slate-100;
    color: inherit;
}

#html5-qrcode-button-camera-permission,
#html5-qrcode-button-camera-start,
#html5-qrcode-button-camera-stop {
    -webkit-box-direction: normal;
    box-sizing: border-box;
    font: inherit;
    -webkit-appearance: button;
    font-family: inherit;
    outline: none !important;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    touch-action: manipulation;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-box-align: center;
    align-items: center;
    padding: 6px;
    font-weight: 600;
    -webkit-box-pack: center;
    justify-content: center;
    line-height: 1.1;
    margin: 5px;
    border-radius: 4px;
    font-size: 14px;
    transition: 0.3s ease-in-out;
    text-transform: uppercase;
    border: 1px solid #6d7382;
    color: #6d7382;
    background: #fff;
    display: inline-block;
}

.btn-icon {
    background-color: transparent;
}

.btn-cancel,
.btn-cancel:hover {
    color: red;
}
.btn-circle {
    border: none;
    outline: none !important;
    overflow: hidden;
    width: 40px;
    height: 40px;
    @include border-radius(50%);

    i {
        font-size: 18px;
        position: relative;
        margin-left: 0;
        margin-right: 0;
    }
}

.btn-link {
    font-weight: 600;
    color: rgb(106, 115, 131);
    @include transition(0.5s);

    &:active,
    &:focus,
    &:hover {
        text-decoration: none;
        opacity: 0.8;
        background: $slate-100;
        color: $btn-color-primary;
    }
}

.btn-circle-lg {
    border: none;
    outline: none !important;
    overflow: hidden;
    width: 50px;
    height: 50px;
    @include border-radius(50% !important);

    i {
        font-size: 26px !important;
        position: relative !important;
        left: 0px !important;
        top: 6px !important;
    }
}

.btn:not(.btn-link):not(.btn-circle) {
    @include border-radius(4px);
    border: none;
    font-size: 14px;
    outline: none;
    transition: 0.3s ease-in-out;
    text-transform: uppercase;

    &:hover,
    &:focus,
    &:active {
        outline: none;
    }

    span,
    svg {
        position: relative;
        // top: -2px;
        // margin-left: 8px;
        margin-right: 8px;
    }

    span {
        &:last-child,
        &:nth-child(2) {
            margin-right: 0;
        }
    }

    &.btn-xs {
        font-size: 12px;
    }

    &.btn-default {
        border: 1px solid $slate-200;
        color: $slate-500;
        background: #fff;

        &:hover,
        &:focus,
        &:active {
            border: 1px solid $slate-200;
            background: $slate-100;
            color: $slate-600;
            box-shadow: $natural-shadow-sm !important;
        }

        &.dropdown-toggle {
            border-color: #ebeef1;

            &:hover,
            &:focus,
            &:active {
                &:not(.disabled) {
                    border: 1px solid $slate-200;
                    box-shadow: $natural-shadow-sm !important;
                }
            }

            span.caret {
                margin-right: 0;
                margin-left: 8px;
            }
        }
    }

    &.btn-ghost {
        border: none;

        &:hover,
        &:focus,
        &:active {
            border: none;
        }
    }

    &.btn-primary {
        background: $btn-color-primary;

        &:hover,
        &:focus,
        &:active {
            background-color: $accent-blue-600;
            box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px -1px, rgba(0, 0, 0, 0.14) 0px 4px 5px 0px,
                rgba(0, 0, 0, 0.12) 0px 1px 10px 0px;
        }
    }

    &.btn-outline,
    &.btn-border {
        border: 1px solid #6d7382;
        color: #6d7382;
        background: #fff;
        box-shadow: none !important;

        &:hover,
        &:focus,
        &:active {
            background: $slate-100;
            border: 1px solid #6d7382;
            color: #6d7382;
        }

        &.btn-danger {
            border-color: $btn-color-danger;
            color: $btn-color-danger;
        }

        &.btn-success {
            border-color: $btn-color-success;
            color: $btn-color-success;
        }

        &.btn-primary,
        &.btn-warning {
            background: #fff !important;

            &:hover,
            &:focus,
            &:active {
                color: $btn-color-primary;
                background: $slate-100;
            }
        }
    }

    &:hover,
    &:focus,
    &:active {
        box-shadow: none;
    }

    &.normal-case {
        text-transform: none;
    }
}

// .btn:not(.btn-link):not(.btn-circle):not(.dropdown-toggle) {
//     box-shadow: none;
//     border: 1px solid #d2d5d7cc;
//     // font-family: "Source Sans Pro", "Segoe UI", sans-serif;
//     font-weight: 500;
//     // color: rgb(0, 148, 247);
//     // border-color: rgb(235 238 241 / 1);
//     // border: none;
// }

.btn-warning,
.btn-warning:hover,
.btn-warning:active,
.btn-warning:focus {
    background-color: #ff9600 !important;
}

// .btn-danger,
// .btn-danger:hover,
// .btn-danger:active,
// .btn-danger:focus {
//     background-color: $btn-color-danger !important;
// }

.btn-info,
.btn-info:hover,
.btn-info:active,
.btn-info:focus {
    background-color: $btn-color-info !important;
}

// .btn-success,
// .btn-success:hover,
// .btn-success:active,
// .btn-success:focus {
//     background-color: $btn-color-success !important;
// }

// .btn-primary,
// .btn-primary:hover,
// .btn-primary:active,
// .btn-primary:focus {
//     background-color: $btn-color-primary !important;
// }

.btn-default {
    color: #6a7383;
}

.btn-group,
.btn-group-vertical {
    // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.12);
    box-shadow: none !important;

    .btn:not(.btn-link):not(.btn-circle) {
        box-shadow: none !important;
        height: 32px;
        border-color: #6d7382;
        padding: 6px 10px;

        &.dropdown-toggle {
            border-color: #6d7382;

            span.caret {
                margin-right: 0;
                margin-left: 8px;
            }
        }

        .caret {
            position: relative;
            bottom: 1px;
        }
    }

    .btn + .dropdown-toggle {
        border-left: 1px solid rgba(0, 0, 0, 0.08) !important;
    }

    .btn-group-dropdown {
        .dropdown-menu {
            z-index: 2000;
            .btn-primary {
                background: rgba(0, 148, 247, 0.08);
                color: #6a7383 !important;
            }
        }
    }
}
// .multiple_emails-email {
//     border: 1px #c8fbbb solid;
//     background: #f7fdf3;
//     a {
//         color:$btn-color-primary;
//     }
// }
// li.ui-timepicker-selected, .ui-timepicker-list li:hover, .ui-timepicker-list .ui-timepicker-selected:hover {
//     background: #4fab20 !important;
// }

/*Bootstrap button outline override*/
.btn-border {
    color: inherit !important;
    border: 1px solid !important;
    border-color: inherit;
}

// .btn-default.btn-border,
// .btn-default.btn-outline,
// .btn-default.btn-outline:hover {
//     color: #30313d !important;
//     border-color: #30313d !important;
// }

.btn-primary.btn-border,
.btn-primary.btn-outline,
.btn-primary.btn-outline:hover {
    color: $btn-color-primary !important;
    border-color: $btn-color-primary !important;
}

.btn-success.btn-border,
.btn-success.btn-outline,
.btn-success.btn-outline:hover {
    color: #5cb85c !important;
    border-color: #5cb85c !important;
}

.btn-info.btn-border,
.btn-info.btn-outline,
.btn-info.btn-outline:hover {
    color: #5bc0de !important;
    border-color: #5bc0de !important;
}

.btn-warning.btn-border,
.btn-warning.btn-outline,
.btn-warning.btn-outline:hover {
    color: #f0ad4e !important;
    border-color: #f0ad4e !important;
}

// .btn-danger.btn-border,
// .btn-danger.btn-outline,
// .btn-danger.btn-outline:hover {
//     border-color: #d9534f !important;
//     color: #d9534f !important;
// }

// .btn-default.btn-border,
// // .btn-primary.btn-border,
// // .btn-success.btn-border,
// .btn-info.btn-border,
// .btn-warning.btn-border,
// // .btn-danger.btn-border,
// .btn-default.btn-outline:hover,
// // .btn-primary.btn-outline:hover,
// // .btn-success.btn-outline:hover,
// .btn-info.btn-outline:hover,
// .btn-warning.btn-outline:hover,
// // .btn-danger.btn-outline:hover,
// .btn-default.btn-outline:focus,
// // .btn-primary.btn-outline:focus,
// // .btn-success.btn-outline:focus,
// .btn-info.btn-outline:focus,
// // .btn-danger.btn-outline:focus
// .btn-warning.btn-outline:focus {
//     color: #fff !important;
// }

// .btn-default.btn-outline:hover {
//     background-color: #30313d !important;
// }
