﻿
.selectbox-form-control {
    border-bottom: 1px solid #dddddd;
    background-color: #fbfbfb;
    padding: 2px;
    padding-left: 10px;
    padding-right: 10px;

    .material-icons {
        font-size: 16px;
    }
}
.selectbox-form-control:hover {
    background-color: #f7f7f7;
}

.input-group,.form-group {
    width: 100%;
    margin-bottom: 20px;

    input[type="text"], .input-group .form-control {
        padding-left: 10px !important;
    }

    // Specific CSS Styling:
    .multiple_emails-container {
        border: 1px solid #ebeef1 !important;
        box-shadow: none !important;
        input {
            padding: 6px 12px;
            margin: 0px;
        }
    }
    .multiple_emails-container:hover {
        border: 1px solid #d5dbe1;
    }
    .multiple_emails-container:focus-within {
        border: 2px solid #0094f7;
    }
    .multiple_emails-ul{
        margin-bottom: 0px;
        &:empty {
            display: none;
        }
    }
    .form-line {
        display: inline-block;
        width: 100%;
        // border-bottom: 1px solid #ddd;
        position: relative;

        input, select, textarea {
            border: 1px solid #ebeef1;
            color: rgba(0, 0, 0, 0.87);
            font-family: "Source Sans Pro", "Segoe UI", sans-serif;
            font-weight: 400;
            border-radius: 4px !important;
        }

        input:hover, select:hover, textarea:hover {
            border: 1px solid #d5dbe1;
        }
        input:focus, select:focus, textarea:focus {
            border: 2px solid #0094f7;
        }

        &:after {
            content: '';
            position: absolute;
            left: 0;
            width: 100%;
            bottom: -2px;
            @include transform(scaleX(0));
            @include transition(0.25s ease-in);
            // border-bottom: 2px solid #1f91f3;
        }

        + .input-group-addon {
            padding-right: 0;
            padding-left: 10px;
        }
    }

    .help-info {
        float: right;
        font-size: 12px;
        margin-top: 5px;
        color: #999;
    }

    label.error {
        font-size: 12px;
        display: block;
        margin-top: 5px;
        font-weight: normal;
        color: #F44336;
    }

    .form-line.error {
        &:after {
            border-bottom: 2px solid #F44336;
        }
    }

    .form-line.success {
        &:after {
            border-bottom: 2px solid #4CAF50;
        }
    }

    .form-line.warning {
        &:after {
            border-bottom: 2px solid #FFC107;
        }
    }

    .form-line.focused {
        &:after {
            @include transform(scaleX(1));
        }

        .form-label {
            bottom: 25px;
            left: 0;
            font-size: 12px;
        }
    }

    .input-group-addon {
        border: none;
        background-color: transparent;
        padding-left: 0;
        font-weight: bold;

        .material-icons {
            font-size: 18px;
            color: #555;
        }
    }

    input[type="text"],
    .form-control {
        // border: none;
        box-shadow: none;
        padding-left: 0;
    }

    .form-control {
        &:focus {
            @include box-shadow(none !important);
        }
    }
}

.input-group.input-group-sm {
    .input-group-addon {
        i {
            font-size: 14px;
        }
    }

    .form-control {
        font-size: 12px;
    }
}

.input-group.input-group-lg {

    .input-group-addon {
        i {
            font-size: 26px;
        }
    }

    .form-control {
        font-size: 18px;
    }
}

.form-control-label {
    text-align: right;

    label {
        margin-top: 8px;
    }
}

.form-horizontal {
    .form-group {
        margin-bottom: 0;
    }
}

.form-group {
    width: 100%;
    margin-bottom: 25px;

    input, select, textarea {
        border: 1px solid #ebeef1;
        color: rgba(0, 0, 0, 0.87);
        font-family: "Source Sans Pro", "Segoe UI", sans-serif;
        font-weight: 400;
    }

    input:hover, select:hover, textarea:hover {
        border: 1px solid #d5dbe1;
    }
    input:focus, select:focus, textarea:focus {
        border: 2px solid #0094f7;
    }


    .form-control {
        width: 100%;
        //border: none;
        box-shadow: none;
        // border-bottom: 1px solid #ddd;
        @include border-radius(4);
        padding-left: 5px;
    }

    .help-info {
        float: right;
        font-size: 12px;
        margin-top: 5px;
        color: #999;
    }

    label.error {
        font-size: 12px;
        display: block;
        margin-top: 5px;
        font-weight: normal;
        color: #F44336;
    }

    .form-line {
        width: 100%;
        position: relative;
        // border-bottom: 1px solid #ddd;

        &:after {
            content: '';
            position: absolute;
            left: 0;
            width: 100%;
            height: 0;
            bottom: -1px;
            @include transform(scaleX(0));
            @include transition(0.25s ease-in);
            // border-bottom: 2px solid #1f91f3;
        }

        .form-label {
            font-weight: normal;
            color: #aaa;
            position: absolute;
            top: 10px;
            left: 0;
            cursor: text;
            @include transition(0.2s);
        }
    }

    .form-line.error {
        &:after {
            @include transform(scaleX(1));
            border-bottom: 2px solid #F44336;
        }
    }

    .form-line.success {
        &:after {
            border-bottom: 2px solid #4CAF50;
        }
    }

    .form-line.warning {
        &:after {
            @include transform(scaleX(1));
            border-bottom: 2px solid #FFC107;
        }
    }

    .form-line.focused {
        &:after {
            @include transform(scaleX(1));
        }

        .form-label {
            top: -10px;
            left: 0;
            font-size: 12px;
        }
    }
}

.form-group-sm {
    .form-label {
        font-size: 12px;
    }

    .form-line.focused {
        .form-label {
            bottom: 20px;
            font-size: 10px;
        }
    }
}

.form-group-lg {
    .form-label {
        font-size: 18px;
    }

    .form-line.focused {
        .form-label {
            bottom: 35px;
            font-size: 12px;
        }
    }
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
    background-color: transparent;
}

.form-group-no-margin {
    margin-bottom: 0px;
}

.bootstrap-tagsinput {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    & > .tag {
        padding-top: 8px;
    }
    input, input:hover {
        background-color: unset;
    }
}

.checkbox-label {
    font-size: 1.25rem !important;

    &::after,&::before {
        top: 2px !important;
    }
}


/* Base styles for Trumbowyg */
.trumbowyg-box {
    border-radius: 8px;
    // box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); /* subtle shadow */
}

.trumbowyg-box .trumbowyg-editor {
    border-radius: 0 0 8px 8px; /* Rounded corners for the bottom of the editor */
    border-top: none; /* Remove top border to merge toolbar and editor seamlessly */
    padding: 15px;
}

/* Toolbar styles */
.trumbowyg-box .trumbowyg-button-pane {
    background-color: #f6f9fc; /* Soft background color */
    border-bottom: none; /* Remove bottom border to merge toolbar and editor */
    border-radius: 8px 8px 0 0; /* Rounded corners for the top of the toolbar */
}

.trumbowyg-box .trumbowyg-button-pane button {
    margin: 0 5px;
    border-radius: 4px; /* Rounded corners for buttons */
    transition: background-color 0.2s, box-shadow 0.2s; /* Smooth transitions for hover and focus */
}

.trumbowyg-box .trumbowyg-button-pane button:hover {
    background-color: #e1e7ed; /* Change color on hover */
}

.trumbowyg-box .trumbowyg-button-pane button:active, 
.trumbowyg-box .trumbowyg-button-pane .trumbowyg-active-button {
    background-color: #c4ccd3; /* Active state color */
}

.trumbowyg-box .trumbowyg-button-pane button:focus {
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25); /* Focus outline, a soft blue glow */
    outline: none;
}

.trumbowyg-box,
.trumbowyg-modal {
    *,
    *::before,
    *::after {
        box-sizing: none;
        border: none;
    }
}

.form-control[disabled] {
    opacity: 0.65;
}

.form-check {
    .form-check-label {
        padding-left: 30px;
        font-size: 14px;
        color: #30313d;
        font-weight: 600;
    }

    &:has(input:not(:checked)) {
        .form-check-label::before {
            top: 1px;
            border: 1px solid #d5dbe1;
        }
    }

    &:has(input:is(:checked)) {
        .form-check-label::after {
            top: 4.5px;
            // left: 3.5px;
            width: 9px;
            height: 9px;
            border: 0px;
            outline: 1px solid #0094f7;
            outline-offset: 2px;
            background-color: #0094f7;
            padding: 3px;
        }
    }
}