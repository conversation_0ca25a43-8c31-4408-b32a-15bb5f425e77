﻿.overlay {
    position: fixed;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1079;
}

.overlay-open {
    .sidebar {
        margin-left: 0;
        z-index: 99999999;
    }
}

#toggleNavSmallBtn {
    cursor: pointer;
}

#leftsidebar {
    z-index: 1080 !important;
    display: flex;
    flex-direction: column;
}

.sidebar.smallSidebar.smallSidebar {
    background-color: white;
    border-right: 1px solid #eeeeee;

    .menu {
        scrollbar-width: none;
    }

    .list {
        padding: 1rem 0;
    }

    .menu-toggle {
        + ul {
            display: none !important;
        }
    }

    li {
        a {
            // z-index: 9;
            padding: 2px 10px !important;
            justify-content: center;
            gap: 0;

            i {
                padding: 12px;
                border-radius: $border_radius-m;
            }
        }

        &.active {
            a {
                background-color: rgba(0, 0, 0, 0) !important;

                i {
                    background-color: $slate-100 !important;
                }
            }
        }
    }
}

.sidebar {
    // ::-webkit-scrollbar {
    //     height: 12px;
    //     width: 7px;
    //     background: transparent;
    //     border-right: 1px solid #eee;
    // }

    // ::-webkit-scrollbar-thumb {
    //     // background: #e8fafb;
    //     -webkit-border-radius: 1ex;
    //     -webkit-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.75);
    //     // height: 100px;

    //     // background-color: transparent;
    //     // border-top: 60px solid green /*or any height and color you want*/;
    //     min-height: 40px;

    // }

    // ::-webkit-scrollbar-corner {
    //     background: #eee;
    // }

    &:not(.smallSidebar) {
        .menu .list li {
            margin-left: 1rem;
            margin-right: 1rem;
            &.active:not(:has(li.active)) > a,
            &.active:has(li.active) > a:not(.toggled) {
                background-color: $slate-150 !important;
            }
        }
    }

    .toggle-nav-small {
        button:hover {
            background-color: rgba(179, 179, 179, 0.16) !important;
        }
    }

    .slimScrollDiv {
        margin-bottom: 130px;
    }

    .divider-text {
        padding: 3px;
        padding-left: 30px;
        font-size: 10px;
        color: #656565;
        font-style: italic;
        background-color: #f1f1f1;
        margin-top: 10px;
    }

    // @include transition(all .1s);
    font-family: $sidebar-font-family;
    background: $slate-50;
    border-right: 1px solid $border-color;
    width: 300px;
    overflow: hidden;
    display: block;
    // height: calc(100vh - 70px);
    position: fixed;
    top: 70px;
    bottom: 0;
    left: 0;

    //@include box-shadow(2px 2px 5px rgba(0, 0, 0, 0.1));
    // @include box-shadow(0px 0px 5px rgba(0, 0, 0, 0.1));
    // box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1);

    z-index: 11 !important;

    .legal {
        width: 100%;
        border-top: 1px solid $border-color;
        padding: 15px;
        overflow: hidden;

        hr {
            margin-top: 7px;
            margin-bottom: 7px;
            border-color: $border-color;
        }

        .copyright {
            font-size: 11px;
            @include three-dots-overflow();

            a {
                font-weight: 600;
                text-decoration: none;
            }
        }

        .version {
            font-size: 11px;
            margin-bottom: 10px;
            color: $slate-600;

            // @include three-dots-overflow();
            // display: none;

            width: 83px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .systemStatusLink {
            // position: absolute;
            // bottom: 45px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: start;
            gap: 0.75rem;
            color: $slate-350;

            a {
                color: $accent-blue-500;
            }
        }
    }

    .user-info {
        padding: 13px 15px 12px 15px;
        white-space: nowrap;
        position: relative;
        border-bottom: 1px solid #e9e9e9;
        background: url('../images/user-img-background.jpg') no-repeat no-repeat;
        height: 135px;

        .image {
            margin-right: 12px;
            display: inline-block;

            img {
                @include border-radius(50%);
                vertical-align: bottom !important;
            }
        }

        .info-container {
            cursor: default;
            display: block;
            position: relative;
            top: 25px;

            .name {
                @include three-dots-overflow();
                font-size: 14px;
                max-width: 200px;
                color: #fff;
            }

            .email {
                @include three-dots-overflow();
                font-size: 12px;
                max-width: 200px;
                color: #fff;
            }

            .user-helper-dropdown {
                position: absolute;
                right: -3px;
                bottom: -12px;
                @include box-shadow(none);
                cursor: pointer;
                color: #fff;
            }
        }
    }

    & > .menu {
        flex-grow: 1;
    }
    .menu > div > ul > li a {
        transition: 0.1s;
    }
    .menu > div > ul > li a:hover,
    .menu > div > ul > div > li a:hover {
        background-color: rgba(179, 179, 179, 0.16) !important;
    }
    .menu {
        position: relative;
        overflow-x: hidden;
        height: calc(100vh - 70px);

        @media screen and (min-height: 700px) {
            height: calc(100vh - 240px);
        }

        @include respond-below(sm) {
            height: calc(100vh - 140px);
        }

        .list {
            list-style: none;
            padding-left: 0;

            li {
                margin: 2px 0;
                margin-right: 0;
                &.active {
                    > :first-child {
                        span:not(.badge) {
                            font-weight: 600;
                            color: $slate-600 !important;
                        }
                        .material-icons {
                            // color: #2196F3 !important;
                            color: $slate-600 !important;
                        }
                    }
                    > :first-child:after,
                    :first-child:before {
                        // color: #2196F3 !important;
                        color: $slate-600 !important;
                    }
                }
            }
            .header {
                // background: #eee;
                margin-top: 20px;
                color: $slate-400;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                padding: 5px 3.5rem;
            }

            i.material-icons {
                font-size: 18px;
                color: $slate-500;
            }

            .menu-toggle {
                position: relative;

                &:before {
                    content: '\203A';
                    color: $slate-400;
                    position: absolute;
                    top: 2px;
                    right: 17px;
                    font-size: 23px;
                    width: fit-content;
                    height: 100%;
                    padding: 1px 0;
                    transition: transform 0.3s ease-in-out;
                    transform-origin: center;
                    transform: rotate(270deg); // Default rotation
                }

                &.no-toggle {
                    &:before {
                        display: none; // Hide the chevron if no toggle
                    }
                }

                &.toggled:before {
                    transform-origin: center;
                    transform: rotate(90deg); // Rotate back when toggled
                }
            }

            a {
                color: $slate-500;
                position: relative;
                display: inline-flex;
                vertical-align: middle;
                width: 100%;
                padding: 10px 8px;
                border-radius: $border-radius-m;
                align-items: center;
                justify-content: start;
                gap: 8px;

                &:hover,
                &:active,
                &:focus {
                    text-decoration: none !important;
                }

                small {
                    position: absolute;
                    top: calc(50% - 7.5px);
                    right: 15px;
                }

                span {
                    // margin: 7px 0 7px 12px;
                    color: $slate-500;
                    font-weight: 600;
                    font-size: 14px;
                    overflow: hidden;
                }
            }

            .fa {
                padding-left: 5px;
                font-size: 13px;
            }

            .ml-menu {
                list-style: none;
                display: none;
                padding-left: 0;

                span {
                    font-weight: normal;
                    font-size: 14px;
                    margin: 3px 0 1px 6px;
                }

                li:hover {
                    background-color: rgba(179, 179, 179, 0.16);
                }
                li {
                    transition: 0.1s;
                    a {
                        padding-left: 25px;
                        padding-top: 5px;
                        padding-bottom: 5px;
                    }

                    &.active {
                        a.toggled:not(.menu-toggle) {
                            // font-weight: 600;
                            // margin-left: 5px;
                            background-color: rgba(161, 194, 250, 0.16);

                            // Remove Chevron from active links on side nav...
                            /*
                            &:before {
                                content: '\E315';
                                font-family: 'Material Icons';
                                position: relative;
                                font-size: 21px;
                                height: 20px;
                                top: -5px;
                                right: 0px;
                                color: #2196F3;
                            }
                            */
                        }
                    }

                    .ml-menu {
                        li {
                            a {
                                padding-left: 80px;
                            }
                        }

                        .ml-menu {
                            li {
                                a {
                                    padding-left: 95px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.right-sidebar {
    width: 280px;
    height: calc(100vh - 70px);
    position: fixed;
    right: -300px;
    top: 70px;
    background: #fdfdfd;
    z-index: 11 !important;
    @include box-shadow(-2px 2px 5px rgba(0, 0, 0, 0.1));
    overflow: hidden;
    // @include transition(.5s);

    &.open {
        right: 0;
    }

    .nav-tabs {
        font-weight: 600;
        font-size: 13px;
        width: 100%;
        margin-left: 2px;

        li {
            text-align: center;

            > a {
                margin-right: 0;
            }

            &:first-child {
                width: 45%;
            }

            &:last-child {
                width: 55%;
            }
        }
    }
}
