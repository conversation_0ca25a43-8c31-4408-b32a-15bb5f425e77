﻿.switch {
    label {
        font-weight: normal;
        font-size: 13px;

        .lever {
            margin: 0 14px;
        }

        input[type='checkbox'] {
            &:checked {
                @each $key, $val in $colors {
                    &:not(:disabled) {
                        ~ .lever.switch-col-#{$key} {
                            &:active {
                                &:after {
                                    box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.4), 0 0 0 15px rgba($val, 0.1);
                                }
                            }
                        }
                    }

                    + .lever.switch-col-#{$key} {
                        background-color: rgba($val, 0.5);

                        &:after {
                            background-color: $val;
                        }
                    }
                }
            }
        }
    }
}

.materialize .switch label input[type='checkbox'] {
    & + .lever {
        width: 34px;
        height: 14px;
        background: $slate-200;
        // opacity: 0.38;

        &::after {
            width: 16px;
            height: 16px;
            background: #fff !important;
            box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 1px -1px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px,
                rgba(0, 0, 0, 0.12) 0px 1px 3px 0px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    &:checked + .lever {
        background-color: rgb(0, 148, 247);
        opacity: 0.5;

        &.switch-col-red {
            background-color: rgb(244, 67, 54);
        }
    }
}
// .switch.primary label input[type='checkbox']:checked + .lever:after {
//     background-color: #2196f3;
// }
