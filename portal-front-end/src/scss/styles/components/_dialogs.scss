// .swal-modal {
//     @include border-radius(0 !important);

//     p {
//         font-size: 14px;
//     }

//     .sa-input-error {
//         top: 23px !important;
//         right: 13px !important;
//     }

//     h2 {
//         font-size: 18px !important;
//         margin: 0 0 5px 0 !important;
//     }

//     button {
//         font-size: 15px !important;
//         @include border-radius(0 !important);
//         padding: 5px 20px !important;
//     }
// }

.swal2-close {
    margin: 10px;
    font-size: 40px;
}
.swal2-content {
    font-size: 14px;
}
.swal2-actions {
    z-index: auto;
    button {
        margin: 5px;
    }
}
.swal2-popup {
    border-radius: 0px;
    font-size: inherit;
    // display: block !important;
}
.swal2-title {
    padding: 10px;
    color: $slate-500;
}
.swal2-container {
    background-color: #00000066;
    z-index: 2082;
}
// .swal2-html-container {
//     overflow: hidden;
// }

.swal2-content {
    .preview-image-modal {
        //height: 90vh;
        //width: 90vw;
        max-height: 900px;
        max-width: 1250px;

        img {
            max-width: 100%;
            max-height: inherit;
        }
    }
}

.swal2-container:has(.ph-dialog-v2) {
    .swal2-popup {
        border-radius: 4px;
        padding: 0px;
    }
    .swal2-header:not(:has(.swal2-title:empty)) {
        background-color: #f6f8fa;
        border-radius: 4px 4px 0px 0px;
        padding: 10px 20px 9px;
        align-items: flex-start;
        border-bottom: 1px solid #ebeef1;

        svg {
          margin-right: 12px
        }
    }
    .swal2-title {
        margin: 0px;
        padding: 0px;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        text-align: left;
    }
    .swal2-content {
        text-align: start;
        .body {
            padding: 20px;
        }
        .footer {
            padding: 10px 20px 10px 20px;
            border-top: 1px solid #EBEEF1;
            margin: 0px;

            &:is(:empty) {
                display: none;
            }
        }
    }
    .swal2-close {
        margin: 10px 18px 0px 0px;
        font-size: 16px;
        color: #000;
        background-color: unset;
    }
    .swal2-actions {
        margin: 0px;
        gap: 8px;
        border-top: 1px solid #EBEEF1;
        padding: 10px 20px 10px 20px;
        justify-content: flex-end;
        button {
            margin: 0px;
        }
    }
}

.ph-dialog-v2 {
  &__content {
    padding: 20px;
    min-height: 100px;

    &--prompt-confirmation {
      h6 {
        font-size: 1.8rem;
      }
    }
    .swal2-input,
    .swal2-textarea {
      margin: 0 0 1em;
    }
  }
}

.swal2-container:has(.ph-dialog) {
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-top: 1px solid lightgrey;
        margin: 2px;
        padding: 10px;

        label {
            font-weight: 400;
        }
        .bootstrap-tagsinput {
            border: none !important;
        }
    }
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
    .swal2-close {
        margin: 0px !important;
    }
}
