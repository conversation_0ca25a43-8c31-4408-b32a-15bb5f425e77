﻿.alert {
    @include border-radius(0);
    @include box-shadow(none);
    border: none;
    color: #fff !important;

    .alert-link {
        color: #fff;
        text-decoration: underline;
        font-weight: bold;
    }
}

.alert-success {
    background-color: #2b982b;
}

.alert-info {
    background-color: #00b0e4;
}

.alert-danger {
    background-color: #fb483a !important;
}


.alert-red {
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
    border-radius: 4px;
    color: #721c24 !important;
    font-size: 14px;
    font-weight: 400;
    padding: 10px 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
    display: block;
    text-align: left;

    a {
        color: #d9534f; /* Brighter red for links to make them stand out and suggest action */
        text-decoration: underline; /* Underlining to indicate clickability */
    }
}


.alert-blue {
    background-color: #e7f5ff;
    border: 1px solid #69a7d0;
    border-radius: 4px;
    color: #0056b3 !important;
    font-size: 14px;
    font-weight: 400;
    padding: 10px 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
    display: block;
    text-align: left;
}

.alert-yellow, .alert-warning {
    background-color: #fff3cd; /* Light yellow background */
    border: 1px solid #efd68b; /* Lighter yellow border */
    color: #856404 !important; /* Dark yellow text, for readability */
    font-weight: 400;
    padding: 10px 20px;
    border-radius: 4px; /* Rounded corners */
    display: block;
    text-align: left;

    i {
        // font-size: 18px; /* Size of the icon */
        // margin-right: 8px; /* Space between icon and text */
        color: #ffecb3 !important; /* Icon color to match the border */
    }
}



.alert-dismissible {
    .close {
        color: #fff;
        opacity: 1;
        border: none;
        text-shadow: none;
    }
}

.swal2-top-end {
    .swal2-popup.swal2-toast .swal2-title {
        font-size: 12px !important;
    }
    .swal2-popup.swal2-toast .swal2-icon {
        margin: 5px !important;
    }
}

.swal2-validation-message {
    background-color: #ffe5e6; /* Light pink background similar to Stripe's alert */
    border: 1px solid #e68082; /* Light red border that stands out */
    border-radius: 4px; /* Keeping the corners slightly rounded */
    color: #e12d39; /* Similar red used by Stripe for critical alerts */
    padding: 10px 20px; /* Proper padding for better visibility */
    font-size: 14px; /* Ensuring the font size is readable */
    font-weight: 400; /* Normal weight for better readability */
    box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* Subtle shadow for depth */
    display: block; /* Ensure it's a block to fill container or adjust as needed */
    text-align: center; /* Center text alignment for the content */
    margin: 10px 20px !important;
}

.pinned-notification {
    line-height: 1.5;
    text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    font-family: 'Source Sans Pro', Helvetica, sans-serif;
    font-size: 14px;
    box-sizing: border-box;
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-transform: none;
    z-index: 2000;

    .notification-text {
        margin-left: 5px;
        .btn {
            margin-left: 10px;
        }
    }
    .notification-container {
        line-height: 1.5;
        text-size-adjust: 100%;
        -webkit-font-smoothing: antialiased;
        font-family: 'Source Sans Pro', Helvetica, sans-serif;
        font-size: 14px;
        text-transform: none;
        box-sizing: border-box;
        display: flex;
        position: relative;
        margin: 0px;
        padding: 16px 38px 16px 16px;
        box-shadow: 0 0px 3px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        border: 1px solid rgb(221, 225, 240);
        background-color: white;
    }

    @include respond-below(sm) {
        width: 95% !important;
    }
}
