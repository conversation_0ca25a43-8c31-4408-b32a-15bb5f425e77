.select2-container {
    z-index: 2147483647;
}
.dropdown-menu.open {
    z-index: 2147483647;
}
.select2-container--open {
    z-index: 2147483647;
}
/**
 * Multiple Select2
 */

 .select2-selection.select2-selection--single:focus {
    border: 2px solid #0094f7 !important;
 }

.select2-container--default {
    .select2-dropdown {
        border: none;
        // box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
        box-shadow: rgba(0, 0, 0, 0.2) 0px 5px 5px -3px, rgba(0, 0, 0, 0.14) 0px 8px 10px 1px, rgba(0, 0, 0, 0.12) 0px 3px 14px 2px;
    }
    .select2-results__option {
        padding: 9px 12px !important;
        &--highlighted[aria-selected] {
            background-color: rgba(0, 0, 0, 0.04);
            color: #666;
        }
        &[aria-selected=true] {
            background-color: rgba(0, 148, 247, 0.08);
            color: #666;
        }
    }
}   


.select2-container--material {
    width: 100% !important;
    ::placeholder {
        color: inherit;
    }

    .select2-selection {
        /* @extend input */
        overflow: visible;
        font: inherit;
        touch-action: manipulation;
        margin: 0;
        line-height: inherit;
        border-radius: 0;
        box-sizing: inherit;
        /* @extend .form-control */
        display: block;
        width: 100%;
        color: #55595c;
        background-clip: padding-box;
        border: 1px solid rgba(0,0,0,.15);
        padding: .5rem 0 .6rem;
        font-size: 1rem;
        line-height: 1.5;
        background-color: transparent;
        background-image: none;
        border-radius: 0;
        margin-top: .2rem;
        margin-bottom: 1rem;
        /* @extend input[type=text] */
        background-color: transparent;
        border: none;
        border-bottom: 1px solid #ccc;
        border-radius: 0;
        outline: 0;
        //height: 2.1rem;
        width: 100%;
        font-size: 1rem;
        box-shadow: none;
        transition: all .3s;
        min-height: 2.1rem;
        .select2-selection__rendered {
            padding-left: 0;
        }
    }

    .select2-selection--single {
        .select2-selection__rendered {
            float: left;
        }
        .select2-selection__arrow {
            float: right;
        }
    }

    .select2-selection--multiple {
        .select2-selection__rendered {
            width: 100%;
                li {
                list-style: none;
            }
        }
        /**
         * Multiple selected options
         */
        .select2-selection__choice {
            /* @extend .mdl-chip */
            height: 32px;
            //font-family: "Roboto","Helvetica","Arial",sans-serif;
            line-height: 32px;
            padding: 0 12px;
            border: 0;
            border-radius: 16px;
            background-color: $slate-100;
            display: inline-block;
            color: $slate-600;
            margin: 2px 0;
            font-size: 0;
            white-space: nowrap;
            /* @extend .mdl-chip__text */
            font-size: 13px;
            vertical-align: middle;
            display: inline-block;
            float: left;
            margin-right: 8px;
            margin-bottom: 4px;
        }
        /**
         * Multiple selected option clear button
         */
        .select2-selection__choice__remove {
            /* Hide default content */
            font-size: 0;
            cursor: pointer;
            float: right;
            margin-top: 4px;
            margin-right: -6px;
            margin-left: 6px;
            transition: opacity;
            &::before {
                content: "cancel";
                        /* @extend .material-icons */
                font-family: 'Material Icons';
                font-weight: normal;
                font-style: normal;
                font-size: 24px;
                line-height: 1;
                letter-spacing: normal;
                text-transform: none;
                display: inline-block;
                white-space: nowrap;
                word-wrap: normal;
                direction: ltr;
                -webkit-font-feature-settings: 'liga';
                -webkit-font-smoothing: antialiased;
                color: $slate-400;
                transition: color 0.25s ease-in-out;
            }
            &:hover::before {
                color: $slate-500;
            }
        }
    }

    .select2-search--inline {
        .select2-search__field {
            width: 100%;
            margin-top: 0;
            font-size: 14px;
                /* Match input[type=text] */
            height: 34px;
            line-height: 1;
        }
    }

    /**
     * Dropdown
     */
    .select2-dropdown {
        border: 0;
        box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);

        // zoom: 1.2;
        .select2-search__field {
            min-height: 2.1rem;
            margin-bottom: 16px;
            border: 0;
            border-bottom: 1px solid #ccc;
                transition: all .3s;
                &:focus {
                border-bottom: 1px solid #4285f4;
                box-shadow: 0 1px 0 0 #4585f4;
            }
        }
    }
    .select2-results__options:not(.select2-results__options--nested) {
        /* @extend .zf-shadow-depth* */
        box-shadow: 0 2px 5px 0 rgba(0,0,0,.16),0 2px 10px 0 rgba(0,0,0,.12);
        /* @extend .dropdown-content */
        background-color: #fff;
        margin: 0;
        //display: none;
        min-width: 100px;
        max-height: 300px;
        overflow-y: auto;
        //opacity: 0;
        //position: absolute;
        z-index: 999;
        will-change: width,height;
        /* @extend .dropdown-content inline styles */
        //position: absolute;
        //top: 0;
        //left: 0;
        //opacity: 1;
        //display: block;
        &--above {
            //top: 50px;
        }
        &--below {
            //top: -50px;
        }
    }

    /**
     * Options
     */
    .select2-results__option {
        /* @extend .dropdown-content li */
        cursor: pointer;
        clear: both;
        color: rgba(0,0,0,.87);
        line-height: 1.5rem;
        //width: 100%;
        text-align: left;
        text-transform: none;
        /* @extend .dropdown-content li>a, .dropdown-content li>span */
        font-size: 14px;
        //color: #4285F4;
        display: block;
        padding: 1rem;
        /**
         * Disabled options
         */
        &[aria-disabled=true] {
            /* @extend .select-dropdown li.disabled */
            color: rgba(0,0,0,.3);
            background-color: transparent!important;
            cursor: context-menu;
                /* @extend .disabled */
            cursor: not-allowed;
        }
        /**
         * Selected option
         */
        &[aria-selected=true] {
            /* @extend .dropdown-content li:active, .dropdow-content li:hover */
            color: #4285f4;
                background-color: #eee;
        }
        /**
         * Active/hovered option
         */
        &--highlighted[aria-selected] {
            background-color: rgba(0, 148, 247, 0.08);
        }

        &:has(hr) {
            padding: 0px;

            hr {
                margin: 0px;
            }
        }
    }

    /**
     * Focused textbox
     */

    &.select2-container--focus {
        .select2-selection {
            /* @extend input[type=text]:focus */
            border-bottom: 1px solid #4285f4;
            box-shadow: 0 1px 0 0 #4585f4;
        }
    }

    /**
     * Disabled textbox
     */
    &.select2-container--disabled {
        .select2-selection {
            /* @extend .select-wrapper input.select-dropdown:disabled */
            color: rgba(0,0,0,.3);
            cursor: default;
            user-select: none;
            border-bottom: 1px solid rgba(0,0,0,.3);
        }
        &.select2-container--focus {
            .select2-selection {
                box-shadow: none;
            }
        }
    }
}
