﻿.badge {
    padding-top: 0;
}
.badge-outline {
    background: none;
    border: 1px solid white;
}
.badge-outline-dark {
    background: none;
    border: 1px solid #30313d;
    color: #30313d;
}

.badge {
    // @include border-radius(2px);
    // padding-bottom: 0px;
    border-radius: 4px; /* Adjusted border-radius to match the rounded corners */
    padding: 2px 6px 2px 6px; /* Added padding for better spacing */
}
.badge-beta {
    background-color: #ff9b07;
    color: white !important;
    font-size: 11px !important;
    font-weight: 100 !important;
    font-style: italic;
    padding-top: 0px;
    padding-left: 4px;
    height: 15px;
    margin-top: -2px !important;
}
.badge-updated {
    background-color: #2196f3;
    color: white !important;
    font-size: 11px !important;
    font-weight: 100 !important;
    font-style: italic;
    padding-top: 0px;
    padding-left: 4px;
    height: 15px;
    margin-top: -2px !important;
}

/* Green Badge */
.badge-green {
    background-color: #d1f7c4;
    color: #2a6f37;
    border: 1px solid #a0dca2;
}

.badge-green-outline {
    background-color: transparent; /* Transparent background */
    color: #2a6f37; /* Green text color */
    border: 1px solid #2a6f37; /* Green border color */
}

/* Blue Badge */
.badge-blue {
    background-color: #cfeaff; /* Blue background color */
    color: #2196f3; /* White text color */
    border: 1px solid #2196f3; /* Blue border color */
}

/* Blue Badge Outline */
.badge-outline-blue {
    background-color: transparent; /* Transparent background */
    color: #2196f3; /* Blue text color */
    border: 1px solid #2196f3; /* Blue border color */
}

/* Red Badge */
.badge-red {
    background-color: #fde8e8; /* Light red background color */
    color: #c53030; /* Darker red text color */
    border: 1px solid #c53030
}

/* Orange Badge */
.badge-orange {
    background-color: #ffe2ca;
    color: #e66c06;
    border: 1px solid #e66c06; /* Orange border color */
}

/* Yellow Badge */
.badge-yellow {
    background-color: #fff9c4;
    color: #c4b405;
    border: 1px solid #c4b405;
}

/* Light Grey Badge */
.badge-lightgrey,
.badge-grey {
    background-color: #f0f0f0; /* Light grey background color */
    color: #595959; /* Dark grey text color */
    border: 1px solid #d9d9d9; /* Light grey border color */
}

.list-group-item {
    @include border-radius(0);
    @include transition(0.5s);
}

.list-group {
    .active {
        background-color: #2196f3;
        border-color: #2196f3;

        &:hover,
        &:focus,
        &:active {
            background-color: #2196f3;
            border-color: #2196f3;
        }

        .list-group-item-text {
            color: #dfe9f1;
            font-size: 13px;

            &:hover,
            &:active,
            &:focus {
                color: #dfe9f1;
            }
        }
    }

    .list-group-item.active {
        &:hover,
        &:focus,
        &:active {
            .list-group-item-text {
                color: #dfe9f1;
            }
        }
    }

    .list-group-item {
        &:first-child,
        &:last-child {
            @include border-radius(0);
        }

        .list-group-item-heading {
            font-weight: bold;
            font-size: 17px;
        }
    }

    .list-group-item-success {
        background-color: #2b982b;
        border: none;
        color: #fff;

        &:hover,
        &:focus {
            background-color: #2b982b;
            color: #fff;
            opacity: 0.8;
        }
    }

    .list-group-item-info {
        background-color: #00b0e4;
        border: none;
        color: #fff;

        &:hover,
        &:focus {
            background-color: #00b0e4;
            color: #fff;
            opacity: 0.8;
        }
    }

    .list-group-item-warning {
        background-color: #ff9600;
        border: none;
        color: #fff;

        &:hover,
        &:focus {
            background-color: #ff9600;
            color: #fff;
            opacity: 0.8;
        }
    }

    .list-group-item-danger {
        background-color: #fb483a;
        border: none;
        color: #fff;

        &:hover,
        &:focus {
            background-color: #fb483a;
            color: #fff;
            opacity: 0.8;
        }
    }

    @each $key, $val in $colors {
        .pl-#{$key} {
            stroke: $val;
        }

        .list-group-bg-#{$key} {
            background-color: $val;
            border: none;
            color: #fff;

            &:hover,
            &:focus {
                background-color: $val;
                color: #fff;
                opacity: 0.8;
            }
        }
    }
}
.list-group-flush {
    .list-group-item {
        border-left: none;
        border-right: none;
        border-radius: 0;

        &:first-child {
            border-top: none;
        }

        &:last-child {
            border-bottom: none;
        }
    }
}
