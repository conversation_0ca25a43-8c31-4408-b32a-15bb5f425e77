﻿.dataTables_wrapper {
    position: relative;

    select {
        border: none;
        border-bottom: 1px solid #ddd;
        @include border-radius(0);
        @include box-shadow(none);

        &:active,
        &:focus {
            @include box-shadow(none);
        }
    }

    input[type='search'] {
        @include border-radius(0);
        @include box-shadow(none);
        border: none;
        font-size: 12px;
        border-bottom: 1px solid #ddd;

        &:focus,
        &:active {
            border-bottom: 2px solid #1f91f3;
        }
    }

    .dt-buttons {
        float: left;
        margin-bottom: 10px;

        .dt-button {
            appearance: none;
            background-color: #fafbfc;
            border: 1px solid #6a7383;
            border-radius: 4px;
            box-shadow: rgba(27, 31, 35, 0.04) 0 1px 0, rgba(255, 255, 255, 0.25) 0 1px 0 inset;
            box-sizing: border-box;
            color: #30313d;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            font-family: -apple-system, system-ui, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
                'Segoe UI Emoji';
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            list-style: none;
            padding: 6px 16px;
            position: relative;
            transition: background-color 0.2s cubic-bezier(0.3, 0, 0.5, 1);
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            vertical-align: middle;
            white-space: nowrap;
            word-wrap: break-word;

            &.btn {
                font-weight: 600;
                color: #6a7383;
                padding: 6px;
            }

            &:hover {
                background-color: #f3f4f6;
                text-decoration: none;
                transition-duration: 0.1s;
            }

            &:disabled {
                background-color: #fafbfc;
                border-color: rgba(27, 31, 35, 0.15);
                color: #959da5;
                cursor: default;
            }

            &:active {
                background-color: #edeff2;
                box-shadow: rgba(225, 228, 232, 0.2) 0 1px 0 inset;
                transition: none 0s;
            }

            &:focus {
                outline: 1px transparent;
            }

            &:before {
                display: none;
            }

            &:-webkit-details-marker {
                display: none;
            }

            & > span {
                display: inline-flex;
                align-items: center;
            }

            .btn-text {
                top: 0 !important;
            }
        }
    }
}

.dt-button-info {
    position: fixed;
    top: 50%;
    left: 50%;
    min-width: 400px;
    text-align: center;
    background-color: #fff;
    border: 2px solid #999;
    @include border-radius(3px);
    margin-top: -100px;
    margin-left: -200px;
    z-index: 21;

    h2 {
        color: #777;
    }

    div {
        color: #777;
        margin-bottom: 20px;
    }
}
