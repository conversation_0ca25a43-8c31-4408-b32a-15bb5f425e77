﻿.dtp {
    div.dtp-date,
    div.dtp-time {
        background: #007d72;
    }

    > .dtp-content {
        > .dtp-date-view {
            > header.dtp-header {
                background: #009688;
            }
        }
    }

    .dtp-buttons {
        .dtp-btn-ok {
            margin-left: 10px;
        }

        .dtp-btn-clear {
            margin-right: 10px !important;
        }
    }

    .p10 {
        > a {
            color: #fff;
        }
    }

    div.dtp-actual-year {
        font-size: 1.5em;
        color: #ffffff;
    }

    table.dtp-picker-days {
        tr {
            td {
                a.selected {
                    background: #007d72;
                    color: #fff;
                }
            }
        }
    }
}

.datepicker {
    &.datepicker-dropdown {
        &.dropdown-menu {
            margin-top: 0 !important;
        }
    }

    table {
        tr {
            td {
                &.active {
                    background-image: -webkit-gradient(
                        linear,
                        0 0,
                        0 100%,
                        from($datepicker-active-color),
                        to($datepicker-active-color)
                    );
                    background-image: -webkit-linear-gradient(
                        to bottom,
                        $datepicker-active-color,
                        $datepicker-active-color
                    );
                    background-image: -o-linear-gradient(to bottom, $datepicker-active-color, $datepicker-active-color);
                    background-image: linear-gradient(to bottom, $datepicker-active-color, $datepicker-active-color);
                    border: none;

                    &:hover {
                        &.active {
                            background-image: -webkit-gradient(
                                linear,
                                0 0,
                                0 100%,
                                from($datepicker-active-color),
                                to($datepicker-active-color)
                            );
                            background-image: -webkit-linear-gradient(
                                to bottom,
                                $datepicker-active-color,
                                $datepicker-active-color
                            );
                            background-image: -o-linear-gradient(
                                to bottom,
                                $datepicker-active-color,
                                $datepicker-active-color
                            );
                            background-image: linear-gradient(
                                to bottom,
                                $datepicker-active-color,
                                $datepicker-active-color
                            );
                            border: none;
                        }
                    }
                }

                &.selected {
                    background-image: -webkit-gradient(
                        linear,
                        0 0,
                        0 100%,
                        from($datepicker-active-color),
                        to($datepicker-active-color)
                    );
                    background-image: -webkit-linear-gradient(
                        to bottom,
                        $datepicker-active-color,
                        $datepicker-active-color
                    );
                    background-image: -o-linear-gradient(to bottom, $datepicker-active-color, $datepicker-active-color);
                    background-image: linear-gradient(to bottom, $datepicker-active-color, $datepicker-active-color);
                    border: none;
                }

                span {
                    &.active {
                        background-image: -webkit-gradient(
                            linear,
                            0 0,
                            0 100%,
                            from($datepicker-active-color),
                            to($datepicker-active-color)
                        );
                        background-image: -webkit-linear-gradient(
                            to bottom,
                            $datepicker-active-color,
                            $datepicker-active-color
                        );
                        background-image: -o-linear-gradient(
                            to bottom,
                            $datepicker-active-color,
                            $datepicker-active-color
                        );
                        background-image: linear-gradient(
                            to bottom,
                            $datepicker-active-color,
                            $datepicker-active-color
                        );
                        border: none;

                        &:hover {
                            &.active {
                                background-image: -webkit-gradient(
                                    linear,
                                    0 0,
                                    0 100%,
                                    from($datepicker-active-color),
                                    to($datepicker-active-color)
                                );
                                background-image: -webkit-linear-gradient(
                                    to bottom,
                                    $datepicker-active-color,
                                    $datepicker-active-color
                                );
                                background-image: -o-linear-gradient(
                                    to bottom,
                                    $datepicker-active-color,
                                    $datepicker-active-color
                                );
                                background-image: linear-gradient(
                                    to bottom,
                                    $datepicker-active-color,
                                    $datepicker-active-color
                                );
                                border: none;
                            }
                        }
                    }
                }
            }
        }

        &.table-condensed {
            > tbody {
                > tr {
                    > td {
                        padding: 6px 9px;
                    }
                }
            }
        }
    }
}

.input-daterange {
    .form-control {
        text-align: left;
    }

    .input-group-addon {
        padding-right: 10px !important;
    }
}

.daterange-arrow-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ph-daterange-picker > .selectbox {
    background: #fff;
    padding: 8px;
    line-height: 18px;
    border-radius: 6px;
    border-width: 0 1px 4px;
    border: 1px solid $border-color;
    cursor: pointer;
    float: right !important;
    transition: 0.35s ease-in-out;
    color: $slate-500;
    display: flex;
    align-items: center;

    .material-icons {
        font-size: 15px;
        position: absolute;
    }
    span {
        padding-left: 23px;
        padding-right: 5px;
    }
    @include respond-below(xs) {
        margin-top: 1rem;
    }

    &:hover,
    &:focus,
    &:active {
        box-shadow: $natural-shadow-lg !important;
    }
    .date-string {
        font-weight: 600;
    }
}

.daterangepicker {
    z-index: 2083;
    box-shadow: $natural-shadow-md;
    color: $slate-500;
    border: none !important;

    .drp-calendar {
        &.left {
            border-left: 1px solid $border-color !important;
        }
    }

    .drp-buttons {
        border-top: 1px solid $border-color !important;
    }

    &::before {
        border-bottom: 7px solid $border-color;
    }

    .prev span {
        border: solid $slate-400 !important;
        border-width: 0 2px 2px 0 !important;
    }

    .td,
    option {
        &.disabled {
            color: $slate-300 !important;
        }
    }

    @include respond-below(sm) {
        z-index: 2147483648;
    }
}
