/*! grapesjs-preset-webpage - 0.1.15 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("grapesjs")):"function"==typeof define&&define.amd?define(["grapesjs"],t):"object"==typeof exports?exports["grapesjs-preset-webpage"]=t(require("grapesjs")):e["grapesjs-preset-webpage"]=t(e.grapesjs)}("undefined"!=typeof self?self:this,function(e){return function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=2)}([function(t,n){t.exports=e},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.cmdImport="gjs-open-import-webpage",t.cmdDeviceDesktop="set-device-desktop",t.cmdDeviceTablet="set-device-tablet",t.cmdDeviceMobile="set-device-mobile",t.cmdClear="canvas-clear"},function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function r(e){if(null==e)throw new TypeError("Cannot destructure undefined")}Object.defineProperty(t,"__esModule",{value:!0});var a=n(0),i=o(a),c=n(3),s=(o(c),n(5)),l=o(s),u=n(7),d=o(u),f=n(8),p=o(f),m=n(9),b=o(m),g=n(10),y=o(g);t.default=i.default.plugins.add("gjs-preset-webpage",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,o={blocks:["link-block","quote","text-basic"],modalImportTitle:"Import",modalImportButton:"Import",modalImportLabel:"",modalImportContent:"",importViewerOptions:{},textCleanCanvas:"Are you sure to clean the canvas?",showStylesOnChange:1,textGeneral:"General",textLayout:"Layout",textTypography:"Typography",textDecorations:"Decorations",textExtra:"Extra",customStyleManager:[]};for(var a in o)a in n||(n[a]=o[a]);r(n),(0,p.default)(e,n),(0,d.default)(e,n),(0,l.default)(e,n),(0,b.default)(e,n),(0,y.default)(e,n)})},function(e,t,n){"use strict";(function(e){var o,r,a,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};/*! grapesjs-plugin-filestack - 0.1.1 */
!function(c,s){"object"==i(t)&&"object"==i(e)?e.exports=s(n(0)):(r=[n(0)],o=s,void 0!==(a="function"==typeof o?o.apply(t,r):o)&&(e.exports=a))}(0,function(e){return function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=0)}([function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(1),r=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=r.default.plugins.add("gjs-plugin-filestack",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,o=e.getConfig(),r=o.stylePrefix||"",a=void 0,i={key:"",btnEl:"",btnText:"Add images",filestackOpts:{accept:"image/*",maxFiles:10},onComplete:function(e,t){}};for(var c in i)c in n||(n[c]=i[c]);if(!filestack)throw new Error("Filestack instance not found");if(!n.key)throw new Error("Filestack's API key not found");var s=filestack.init(n.key);e.on("run:open-assets",function(){var t=e.Modal,o=t.getContentEl(),i=o.querySelector("."+r+"am-file-uploader"),c=o.querySelector("."+r+"am-assets-header"),u=o.querySelector("."+r+"am-assets-cont");i&&(i.style.display="none"),c&&(c.style.display="none"),u.style.width="100%",a||(a=n.btnEl,a||(a=document.createElement("button"),a.className=r+"btn-prim "+r+"btn-filestack",a.innerHTML=n.btnText),a.onclick=function(){s.pick(n.filestackOpts).then(function(e){var t=e.filesUploaded,o=t instanceof Array?t:[t],r=l(o);n.onComplete(o,r)})}),u.insertBefore(a,c)});var l=function(t){var n=t.map(function(e){return e.src=e.url,e});return e.AssetManager.add(n)}})},function(t,n){t.exports=e}])})}).call(t,n(4)(e))},function(e,t,n){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(6),r=function(e){return e&&e.__esModule?e:{default:e}}(o),a=n(1);t.default=function(e,t){var n=e.Commands,o=t.textCleanCanvas;n.add(a.cmdImport,(0,r.default)(e,t)),n.add(a.cmdClear,function(e){return confirm(o)&&e.runCommand("core:canvas-clear")})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e};t.default=function(e,t){var n=e.getConfig("stylePrefix"),r=e.Modal,a=e.CodeManager.getViewer("CodeMirror").clone(),i=document.createElement("div"),c=t.modalImportLabel,s=t.modalImportContent,l=a.editor,u=document.createElement("button");return u.type="button",u.innerHTML=t.modalImportButton,u.className=n+"btn-prim "+n+"btn-import",u.onclick=function(t){e.setComponents(l.getValue().trim()),r.close()},a.set(o({codeName:"htmlmixed",theme:"hopscotch",readOnly:0},t.importViewerOptions)),{run:function(e){var o=this;if(!l){var d=document.createElement("textarea");if(c){var f=document.createElement("div");f.className=n+"import-label",f.innerHTML=c,i.appendChild(f)}i.appendChild(d),i.appendChild(u),a.init(d),l=a.editor}r.setTitle(t.modalImportTitle),r.setContent(i);var p="function"==typeof s?s(e):s;a.setContent(p||""),r.open().getModel().once("change:open",function(){return e.stopCommand(o.id)}),l.refresh()},stop:function(){r.close()}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=e.BlockManager;(function(e){return t.blocks.indexOf(e)>=0})("text-basic")&&n.add("text-basic",{category:"Basic",label:"Text section",attributes:{class:"gjs-fonts gjs-f-h1p"},content:'<section class="bdg-sect">\n      <h1 class="heading">Insert title here</h1>\n      <p class="paragraph">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>\n      </section>'})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(arguments.length>1&&void 0!==arguments[1]&&arguments[1],e.DomComponents),n=t.getType("default");n.model,n.view}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(1);t.default=function(e,t){var n=e.Panels,r=(e.getConfig(),"sw-visibility"),a="export-template",i="open-sm",c="open-layers",s="open-blocks",l="fullscreen",u="preview";n.getPanels().reset([{id:"commands",buttons:[{}]},{id:"options",buttons:[{id:r,command:r,context:r,className:"fa fa-square-o"},{id:u,context:u,command:function(e){return e.runCommand(u)},className:"fa fa-eye"},{id:l,command:l,context:l,className:"fa fa-arrows-alt"},{id:a,className:"fa fa-code",command:function(e){return e.runCommand(a)}},{id:"undo",className:"fa fa-undo",command:function(e){return e.runCommand("core:undo")}},{id:"redo",className:"fa fa-repeat",command:function(e){return e.runCommand("core:redo")}},{id:o.cmdImport,className:"fa fa-download",command:function(e){return e.runCommand(o.cmdImport)}},{id:o.cmdClear,className:"fa fa-trash",command:function(e){return e.runCommand(o.cmdClear)}}]},{id:"views",buttons:[{id:i,command:i,active:!0,className:"fa fa-paint-brush"},{id:"open-tm",command:"open-tm",className:"fa fa-cog"},{id:c,command:c,className:"fa fa-bars"},{id:s,command:s,className:"fa fa-th-large"}]}]);var d=n.getButton("views",s);e.on("load",function(){return d&&d.set("active",1)}),t.showStylesOnChange&&e.on("component:selected",function(){var t=n.getButton("views",i),o=n.getButton("views",c);o&&o.get("active")||!e.getSelected()||t&&t.set("active",1)})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=e.StyleManager,o=t.customStyleManager;n.getSectors().reset(o&&o.length?o:[{name:t.textGeneral,open:!1,buildProps:["float","display","position","top","right","left","bottom"]},{name:t.textLayout,open:!1,buildProps:["width","height","max-width","min-height","margin","padding"]},{name:t.textTypography,open:!1,buildProps:["font-family","font-size","font-weight","letter-spacing","color","line-height","text-align","text-shadow"],properties:[{property:"text-align",list:[{value:"left",className:"fa fa-align-left"},{value:"center",className:"fa fa-align-center"},{value:"right",className:"fa fa-align-right"},{value:"justify",className:"fa fa-align-justify"}]}]},{name:t.textDecorations,open:!1,buildProps:["border-radius-c","background-color","border-radius","border","box-shadow","background"]},{name:t.textExtra,open:!1,buildProps:["transition","perspective","transform"]}])}}])});