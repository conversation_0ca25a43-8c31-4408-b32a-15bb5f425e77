{"name": "grapesjs-preset-webpage", "version": "0.1.15", "description": "GrapesJS Plugin Webpage Preset", "main": "dist/grapesjs-preset-webpage.min.js", "scripts": {"lint": "eslint src", "v:patch": "npm version --no-git-tag-version patch", "build": "npm run v:patch && webpack --env.production && npm run build:css", "start": "webpack-dev-server --open --progress --colors & npm run build:css -- -w", "build:css": "node-sass src/style/main.scss dist/grapesjs-preset-webpage.min.css --output-style compressed"}, "repository": {"type": "git", "url": "https://github.com/artf/grapesjs-preset-webpage.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "plugin", "webpage", "preset", "wysiwyg"], "author": "<PERSON><PERSON>", "license": "BSD-3-<PERSON><PERSON>", "babel": {"presets": ["env"], "plugins": ["transform-object-rest-spread"]}, "peerDependencies": {"grapesjs": "0.x"}, "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "eslint": "^4.19.1", "html-webpack-plugin": "^2.30.1", "node-sass": "^4.8.3", "webpack": "^3.11.0", "webpack-dev-server": "^2.11.2"}, "dependencies": {"grapesjs-aviary": "^0.1.2", "grapesjs-blocks-basic": "^0.1.7", "grapesjs-component-countdown": "^0.1.2", "grapesjs-navbar": "^0.1.5", "grapesjs-plugin-export": "^0.1.5", "grapesjs-plugin-filestack": "^0.1.1", "grapesjs-plugin-forms": "^0.3.5"}}