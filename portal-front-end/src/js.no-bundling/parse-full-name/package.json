{"name": "parse-full-name", "version": "1.2.6", "description": "A function that accepts the full name of a person in any format, parses it, and returns its parts { title, first name, middle name, last name, nickname, suffix, [and any parsing errors] }.", "main": "index.js", "scripts": {"test": "mocha test/"}, "repository": {"type": "git", "url": "https://github.com/RateGravity/parse-full-name"}, "keywords": ["parse", "parser", "name", "full name", "first name", "middle name", "last name", "nickname"], "author": "Own Up <<EMAIL>> (https://www,ownup.com)", "contributors": ["<PERSON> <<EMAIL>> (http://github.com/dschnelldavis)"], "license": "MIT", "bugs": {"url": "https://github.com/RateGravity/parse-full-name/issues"}, "homepage": "https://github.com/RateGravity/parse-full-name", "dependencies": {}, "devDependencies": {"mocha": "^2.5.0"}}