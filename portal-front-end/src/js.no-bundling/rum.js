var BEACON_DATA = true;
var BUILD_VERSION = "";

((window) => {
    if (!BEACON_DATA) {
        console.log('Development mode detected, RUM data won\'t be collected');
    }

    var addEventListener = (window, name, func) => {
        if(window.addEventListener){
            window.addEventListener(name, func)
        } else {
            window.attachEvent('on' + name, func)
        }
    };

    var postBeacon = (data) => {
        if (!JSON && !JSON.stringify) {
            return; //Does not support JSON stringify
        }

        if (!BEACON_DATA) {
            console.log('BEACON', data.type, data);
            return;
        }

        //Fire and forget
        var request = new XMLHttpRequest();
        request.open("POST", encodeURI('https://uapi.gymsystems.co/api/v1/analytics/beacon')),
        request.setRequestHeader("Content-Type", "application/json; charset=utf-8");
        request.send(JSON.stringify(data));
    };

    window.onerror = (message, filename, lineno, colno, error) => {

        // if (message === 'Script error.' || error) {
        //     return; //Skip as it will be already caught by angular
        // }

        const beaconData = {
            type: 'js-error',
            logger: 'window.onerror',
            severity: 'error',
            url: window.location.href,
            host: window.location.host,
            js_message: message,
            js_file: filename,
            js_line: lineno,
            js_stack: error ? (error.stack ? error.stack : 'No supported') : 'No error object',
            build_version: BUILD_VERSION // This comes from webpack define plugin
        };

        // Conditionally add properties if they exist
        if (typeof selectedID !== 'undefined' && selectedID !== null) {
            beaconData.clubID = selectedID;
        }

        if (typeof signedinUser !== 'undefined' && signedinUser !== null) {
            beaconData.signedinUser = signedinUser;
        }

        postBeacon(beaconData);

    };

    var toDuration = (timing, name) => {
        var start = timing.fetchStart;
        var result = timing[name] - start;
        return (result < 0) ? 0 : result;
    };

    var supportTiming = window.performance && window.performance.timing;

    addEventListener(window, 'load', () => {
        if (!supportTiming) {
            console.log('No support for performance timing')
            return;
        }

        var timing = window.performance.timing;
        postBeacon({
            type: 'rum',
            severity: 'info',
            url: window.location.href,
            host: window.location.host,
            connectEnd: toDuration(timing, 'connectEnd'),
            connectStart: toDuration(timing, 'connectStart'),
            domComplete: toDuration(timing, 'domComplete'),
            domContentLoadedEventEnd: toDuration(timing, 'domContentLoadedEventEnd'),
            domContentLoadedEventStart: toDuration(timing, 'domContentLoadedEventStart'),
            domInteractive: toDuration(timing, 'domInteractive'),
            domLoading: toDuration(timing, 'domLoading'),
            domainLookupEnd: toDuration(timing, 'domainLookupEnd'),
            domainLookupStart: toDuration(timing, 'domainLookupStart'),
            fetchStart: toDuration(timing, 'fetchStart'),
            loadEventEnd: toDuration(timing, 'loadEventEnd'),
            loadEventStart: toDuration(timing, 'loadEventStart'),
            navigationStart: toDuration(timing, 'navigationStart'),
            requestStart: toDuration(timing, 'requestStart'),
            responseEnd: toDuration(timing, 'responseEnd'),
            responseStart: toDuration(timing, 'responseStart'),
            secureConnectionStart: toDuration(timing, 'secureConnectionStart'),
            unloadEventEnd: toDuration(timing, 'unloadEventEnd'),
            unloadEventStart: toDuration(timing, 'unloadEventStart')
        });
    });

    //Test for browser support
    addEventListener(window, 'DOMContentLoaded', () => {
        postBeacon({
            type: 'support',
            severity: 'info',
            host: window.location.host,
            websockets: (() => {
                //Test for websockets
                var protocol = 'https:' == location.protocol ? 'wss' : 'ws',
                protoBin;

                if ('WebSocket' in window) {
                    protoBin = 'binaryType' in WebSocket.prototype
                    if (protoBin) {
                        return protoBin;
                    }
                    try {
                        return !!(new WebSocket(protocol + '://.').binaryType);
                    } catch (e) {}
                }

                return false;
            })(),
            cors: (() => {
                //Test for CORS
                return 'XMLHttpRequest' in window
                    && 'withCredentials' in new XMLHttpRequest();
            })(),
            svgInline: (() => {
                //Test for inline SVG support
                var div = document.createElement('div');
                div.innerHTML = '<svg/>';
                return (typeof SVGRect != 'undefined' && div.firstChild && div.firstChild.namespaceURI) == 'http://www.w3.org/2000/svg';
            })(),
            pushState: (() => {
                //Test for HTML5 Push State
                return !!(window.history && history.pushState);
            })(),
            adblock: (() => {
                var adsbox = document.getElementsByClassName('adsbox');
                if (!adsbox || adsbox.length === 0) {
                    return false;
                }
                return !!(adsbox[0]).attributes.style;
            })(),
            localStorage: !!window.localStorage,
            performanceTiming: !!supportTiming,
            screenHeight: window.innerHeight,
            screenWidth: window.innerWidth
        });
    });
})(window);
