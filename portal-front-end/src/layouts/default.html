<!DOCTYPE html>
<html lang="en" class="ph-scrollbar">
    {{> head }}

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap');
    </style>
    <style>
        body {
            margin: 0;
            width: 100%;
            height: 100%;
        }
        * {
            font-family: 'Source Sans Pro', sans-serif;
        }
        input,
        textarea,
        select {
            font-size: 100%;
        }
        firstLoader {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1000;
            background-color: #fff;
        }
        .msg {
            color: #757575;
            font: 20px/20px Arial, sans-serif;
            letter-spacing: 0.2px;
            text-align: center;
        }
        #nlpt {
            animation: a-s 0.5s 2.5s 1 forwards;
            background-color: #f1f1f1;
            height: 4px;
            margin: 56px auto 20px;
            opacity: 0;
            overflow: hidden;
            position: relative;
            width: 300px;
        }
        #nlpt::before {
            animation: a-lb 20s 3s linear forwards;
            background-color: #6fcc41;
            content: '';
            display: block;
            height: 100%;
            position: absolute;
            transform: translateX(-300px);
            width: 100%;
        }
        @keyframes a-lb {
            0% {
                transform: translateX(-300px);
            }
            5% {
                transform: translateX(-240px);
            }
            15% {
                transform: translateX(-30px);
            }
            25% {
                transform: translateX(-30px);
            }
            30% {
                transform: translateX(-20px);
            }
            45% {
                transform: translateX(-20px);
            }
            50% {
                transform: translateX(-15px);
            }
            65% {
                transform: translateX(-15px);
            }
            70% {
                transform: translateX(-10px);
            }
            95% {
                transform: translateX(-10px);
            }
            100% {
                transform: translateX(-5px);
            }
        }
        @keyframes a-s {
            100% {
                opacity: 1;
            }
        }
        @keyframes a-h {
            100% {
                opacity: 0;
            }
        }
        @keyframes a-nt {
            100% {
                transform: none;
            }
        }
        @keyframes a-e {
            43% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.2, 1);
                transform: scale(0.75);
            }
            60% {
                animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
                transform: translateY(-16px);
            }
            77% {
                animation-timing-function: cubic-bezier(0.16, 0, 0.2, 1);
                transform: none;
            }
            89% {
                animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
                transform: translateY(-5px);
            }
            100% {
                transform: none;
            }
        }
        @keyframes a-ef {
            24% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.6, 1);
                transform: scaleY(0.42);
            }
            52% {
                animation-timing-function: cubic-bezier(0.63, 0, 0.2, 1);
                transform: scaleY(0.98);
            }
            83% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.84, 1);
                transform: scaleY(0.96);
            }
            100% {
                transform: none;
            }
        }
        @keyframes a-efs {
            24% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.6, 1);
                opacity: 0.3;
            }
            52% {
                animation-timing-function: cubic-bezier(0.63, 0, 0.2, 1);
                opacity: 0.03;
            }
            83% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.84, 1);
                opacity: 0.05;
            }
            100% {
                opacity: 0;
            }
        }
        @keyframes a-es {
            24% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.6, 1);
                transform: rotate(-25deg);
            }
            52% {
                animation-timing-function: cubic-bezier(0.63, 0, 0.2, 1);
                transform: rotate(-42.5deg);
            }
            83% {
                animation-timing-function: cubic-bezier(0.8, 0, 0.84, 1);
                transform: rotate(-42deg);
            }
            100% {
                transform: rotate(-43deg);
            }
        }
        .invfr {
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
            width: 0;
            height: 0;
            border: 0;
        }
        .msgb {
            position: absolute;
            right: 0;
            font-size: 12px;
            font-weight: normal;
            color: #000;
            padding: 20px;
        }
        .firstload-logos {
            display: inline-flex;
        }
        .firstload-logos svg {
            fill: #343a40 !important;
        }
    </style>

    <body class="theme-black materialize">
        <application style="display: none">
            <!-- Overlay For Sidebars -->
            <div class="overlay"></div>

            <!-- invi overlay to cover iframes -->
            <div class="dropdown-overlay"></div>

            <div id="external-container" class="d-flex">
                <div id="main-body-container" style="flex-grow: 1; flex-basis: 0; overflow: hidden">
                    <!-- Top Bar -->
                    {{> top-bar }}
                    <section>{{> side-menu }}</section>
                    {{> body}}
                </div>
                {{> notifications-side-menu }}
            </div>

            {{> footer}}
        </application>
        <firstLoader>
            <div style="bottom: 0; left: 0; overflow: hidden; position: absolute; right: 0; top: 0">
                <div
                    style="
                        animation: a-h 0.5s 1.25s 1 linear forwards, a-nt 0.6s 1.25s 1 cubic-bezier(0, 0, 0.2, 1);
                        background: #eee;
                        border-radius: 50%;
                        height: 800px;
                        left: 50%;
                        margin: -448px -400px 0;
                        position: absolute;
                        top: 50%;
                        transform: scale(0);
                        width: 800px;
                    "
                ></div>
            </div>
            <div style="height: 100%; text-align: center">
                <div style="height: 50%; margin: 0 0 -140px"></div>
                <div style="height: 128px; margin: 0 auto; position: relative; width: 176px">
                    <div
                        style="
                            animation: a-s 0.5s 0.5s 1 linear forwards,
                                a-e 1.75s 0.5s 1 cubic-bezier(0, 0, 0.67, 1) forwards;
                            opacity: 0;
                            transform: scale(0.68);
                        "
                    >
                        <div class="firstload-logos" style="">
                            <svg
                                style="height: 150px"
                                viewBox="0 0 480 480"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M243.5 3.5C243.5 1.567 241.933 0 240 0C238.067 0 236.5 1.567 236.5 3.5V6.78757L6.8963 178.134C6.51656 176.612 5.14009 175.484 3.50021 175.484C1.56722 175.484 0.000212448 177.051 0.000212448 178.984V184.994L4.72988e-06 185.038L0.000212448 185.077V242.48L4.72988e-06 242.524L0.000212448 242.563V299.971C-7.06956e-05 299.997 -7.09362e-05 300.023 0.000212448 300.049V301.762L1.33581 302.759C1.38329 302.796 1.43159 302.832 1.48066 302.867L237.852 479.264C238.035 479.407 238.231 479.529 238.435 479.631C238.931 479.879 239.467 480 240 480C240.533 480 241.069 479.879 241.564 479.631C241.769 479.529 241.964 479.407 242.148 479.264L478.521 302.865C478.569 302.832 478.616 302.797 478.662 302.761L480 301.762V300.045C480 300.022 480 299.998 480 299.975V242.558L480 242.528L480 242.484V185.072C480 185.064 480 185.055 480 185.046L480 185.044V185.041C480 185.029 480 185.018 480 185.007L480 184.997V178.984C480 177.051 478.432 175.484 476.5 175.484C474.86 175.484 473.483 176.611 473.104 178.133L243.5 6.78725V3.5ZM243.5 15.5216V64.2733L437.984 209.411L470.648 185.035L243.5 15.5216ZM443.836 213.778L473 192.014V235.542L443.836 213.778ZM470.648 242.521L437.984 218.145L405.32 242.521L437.984 266.897L470.648 242.521ZM243.5 73.0076L432.132 213.778L399.468 238.154L243.5 121.76V73.0076ZM9.35244 300.007L236.5 469.52V420.769L42.0158 275.632L9.35244 300.007ZM47.8678 271.264L236.5 412.035V363.283L80.5314 246.889L47.8678 271.264ZM74.6793 242.521L42.0158 266.897L9.35222 242.521L42.0158 218.146L74.6793 242.521ZM86.3834 242.521L236.5 354.549V353.459C236.5 351.526 238.067 349.959 240 349.959C241.933 349.959 243.5 351.526 243.5 353.459V354.548L393.616 242.521L240.183 128.019C240.122 128.022 240.061 128.023 240 128.023C239.939 128.023 239.878 128.022 239.817 128.019L86.3834 242.521ZM236.5 121.76L80.5313 238.154L47.8678 213.778L236.5 73.0079V121.76ZM36.1638 271.264L7.00021 293.028V249.5L36.1638 271.264ZM443.836 271.264L473 249.501V293.028L443.836 271.264ZM470.647 300.007L437.984 275.632L243.5 420.769V469.52L470.647 300.007ZM432.132 271.264L399.468 246.888L243.5 363.283V412.035L432.132 271.264ZM9.35222 185.035L42.0158 209.411L236.5 64.2736V15.5219L9.35222 185.035ZM7.00021 192.014L36.1638 213.778L7.00021 235.542V192.014Z"
                                    fill="black"
                                />
                            </svg>
                        </div>
                    </div>
                </div>
                <div id="nlpt"></div>
                <div style="animation: a-s 0.25s 1.25s 1 forwards; opacity: 0" class="msg">
                    <span id='loadingPerformanceHub'>Loading Performance Hub</span><span>&hellip;</span>
                </div>
            </div>
        </firstLoader>
    </body>
</html>
