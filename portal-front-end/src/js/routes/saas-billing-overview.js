const saasBillingOverviewCharts = {
    dailyCostChart: null,
    servicesSplitDonut: null,
};

function sSaaSBillingOverview() {
    const rootElement = $('#overview');
    let dailyReportsOfMonth = [];
    let selectedMonthReport = null;
    let saasBillingPeriods = [];
    const qsObj = getQueryStringsObject();
    const flatRateChargeLabels = ['performanceHubSoftware', 'cameraSoftware', 'CoachingScreens', 'DuressButtons'];
    const knownChargeLabels = [...flatRateChargeLabels, 'storage'];
    const allChargeLabels = [];

    const filters = {
        selectedMonth: qsObj.selectedMonth ? moment(qsObj.selectedMonth, 'YYYY-MM', true) : moment(),
        groupBy: qsObj.groupBy || 'service',
        costGraphType: qsObj.costGraphType ?? 'cumulative',
    };

    const graphColors = [
        '0, 148, 247', // <PERSON>
        '92, 163, 42', // Green
        '242, 203, 106', // Yellow
        '205, 49, 96', // Red
        '237, 125, 49', // Orange: Bright, lively
        '128, 78, 162', // Purple: Deep, rich
        '91, 192, 235', // Cyan: Light, refreshing
        '214, 41, 118', // Magenta: Vibrant
        '140, 198, 62', // Lime Green: Bright, vivid
        '72, 207, 173', // Teal: Rich, balanced
        '173, 216, 230', // Light Blue: Soft, airy
        '203, 109, 81', // Burnt Orange: Warm, deep
        '153, 102, 204', // Violet: Soft, enchanting
        '112, 128, 144', // Slate Gray: Neutral, sophisticated
    ];

    const graphColorsHalfOpaque = [
        '128, 202, 251', // Blue
        '174, 209, 148', // Green
        '248, 229, 180', // Yellow
        '230, 152, 176', // Red
        '246, 190, 152', // Orange
        '192, 166, 208', // Purple
        '173, 224, 245', // Cyan
        '234, 148, 186', // Magenta
        '198, 226, 158', // Lime Green
        '164, 231, 214', // Teal
        '214, 236, 242', // Light Blue
        '229, 182, 168', // Burnt Orange
        '204, 178, 230', // Violet
        '184, 192, 200', // Slate Gray
    ];

    if (!filters.selectedMonth.isValid()) filters.selectedMonth = moment();

    const dailyReportsOfMonthSample = [
        {
            periodStart: moment().startOf('month').unix(),
            charges: [
                { label: 'skeleton1', total: 100 },
                { label: 'skeleton2', total: 200 },
                { label: 'skeleton3', total: 300 },
            ],
            grandTotal: 1500,
        },
    ];

    function init() {
        renderWidgets();
        renderProjectionPeriod(true);

        const currentMonth = moment();

        getClubSaasBillingMonthlyReportAPI(selectedID, currentMonth.month() + 1, currentMonth.year()).then((res) => {
            if (!res[0] && res[1] !== 'No records found') return instantNotification(res[1], 'error');
            rootElement.find('.widgets #this-month-widget').removeClass('isLoading');
            rootElement.find('.widgets #this-month-widget .number').text(formatGraphAmount(res[1].grandTotal ?? 0));
        });

        const lastMonth = moment().subtract(1, 'month');
        getClubSaasBillingMonthlyReportAPI(selectedID, lastMonth.month() + 1, lastMonth.year()).then((res) => {
            if (!res[0] && res[1] !== 'No records found') return instantNotification(res[1], 'error');
            rootElement.find('.widgets #last-month-widget').removeClass('isLoading');
            rootElement.find('.widgets #last-month-widget .number').text(formatGraphAmount(res[1].grandTotal ?? 0));
        });

        getClubSaasBillingPaymentSettingsAPI(selectedID).then(([success, response]) => {
            if (!success) return;
            saasBillingPeriods = getSaasBillingPeriodsBasedOnDailyLogStartMonth(response.dailyBillingStartMonth);
            renderProjectionPeriod();
        });

        listDailyReportsOfMonthAPI(selectedID, currentMonth.month() + 1, currentMonth.year()).then((res) => {
            if (!res[0]) return instantNotification(res[1], 'error');

            const reports = res[1];
            const latestReport = getLatestReport(reports);
            const latestReportDate = moment.unix(latestReport?.periodStart ?? moment().unix()).tz('UTC');
            const forecastTotals = getForecastTotals(reports);

            const actualTotals = reports.reduce((acc, report) => {
                report.charges.forEach((charge) => {
                    // get storage sum because its the only charge that should be cumulative
                    if (charge.key === 'storage') {
                        acc[charge.key] = (acc[charge.key] ?? 0) + parseFloat(charge.total);
                        return;
                    }

                    // other charges are flat rates so only get the max
                    const curVal = acc[charge.key] ?? 0;
                    acc[charge.key] = Math.max(curVal, parseFloat(charge.total));
                });

                return acc;
            }, {});

            const actualTotalsSum = Object.values(actualTotals).reduce((acc, total) => acc + total, 0);
            const daysLeft = moment().daysInMonth() - latestReportDate.date();
            const forecast = actualTotalsSum + (forecastTotals['storage'] ?? 0) * daysLeft;

            rootElement.find('.widgets #forecast-widget').removeClass('isLoading');
            rootElement.find('.widgets #forecast-widget .number').text(formatGraphAmount(forecast));
        });

        const lastMonthStart = moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
        const currentDay = moment().date();
        let sameDayLastMonth = moment().subtract(1, 'month').date(currentDay);
        if (sameDayLastMonth.isSame(moment(), 'month')) {
            sameDayLastMonth = sameDayLastMonth.subtract(1, 'month').endOf('month');
        }
        sameDayLastMonth = sameDayLastMonth.format('YYYY-MM-DD');

        getClubSaasBillingPeriodicalReportAPI(selectedID, lastMonthStart, sameDayLastMonth).then((res) => {
            if (!res[0] && res[1] !== 'No records found') return instantNotification(res[1], 'error');
            const grandTotal = res[1] === 'No records found' ? 0 : res[1].grandTotal;

            rootElement.find('.widgets #same-time-period-widget').removeClass('isLoading');
            rootElement.find('.widgets #same-time-period-widget .number').text(formatGraphAmount(grandTotal ?? 0));
        });

        loadGraphs();
    }

    function loadGraphs() {
        renderDailyCostChart(true);
        renderServicesSplitDonut(true);

        Promise.all([
            listDailyReportsOfMonthAPI(selectedID, filters.selectedMonth.month() + 1, filters.selectedMonth.year()),
            getClubSaasBillingMonthlyReportAPI(
                selectedID,
                filters.selectedMonth.month() + 1,
                filters.selectedMonth.year()
            ),
        ]).then(([dailyRes, monthlyRes]) => {
            rootElement.find('.filters-container').removeClass('isLoading');
            if (!dailyRes[0]) return instantNotification(dailyRes[1], 'error');
            if (!monthlyRes[0] && monthlyRes[1] !== 'No records found')
                return instantNotification(monthlyRes[1], 'error');

            // additional charges should be treated as flat rate charges
            const additionalCharges = dailyRes[1].reduce((acc, report) => {
                report.charges.forEach((charge) => {
                    const alreadyExists = acc.some((c) => c.key === charge.key);
                    if (!alreadyExists && !knownChargeLabels.includes(charge.key)) {
                        acc.push({ ...charge, total: 0 });
                    }
                });
                return acc;
            }, []);

            const flatRates = flatRateChargeLabels.reduce((acc, charge) => {
                acc[charge] = 0;
                return acc;
            }, {});

            dailyReportsOfMonth = dailyRes[1].map((report, index) => {
                let charges = report.charges.map((charge) => {
                    const chargeTotal = parseFloat(charge.total);

                    // if not a flat rate charge, get the daily cost
                    if (!(charge.key in flatRates)) {
                        return { ...charge, total: chargeTotal.toFixed(2) };
                    }

                    // flat rate charges should appear cumulative
                    const currentCharge = flatRates[charge.key] ?? 0;
                    const totalDiff = Math.max(chargeTotal - currentCharge, 0);
                    flatRates[charge.key] = currentCharge + totalDiff;
                    return { ...charge, total: totalDiff.toFixed(2) };
                });

                const missingCharges = additionalCharges.filter((charge) => !charges.some((c) => c.key === charge.key));

                charges = [...charges, ...missingCharges];
                const grandTotal = charges.reduce((acc, charge) => acc + parseFloat(charge.total), 0);
                return { ...report, charges, grandTotal: grandTotal.toFixed(2) };
            });

            // sort charges and bring flat rates to the bottom
            dailyReportsOfMonth = dailyReportsOfMonth.map((report) => {
                const sortedCharges = report.charges.sort((a, b) => a.label.localeCompare(b.label));
                const reportAdditionalCharges = sortedCharges.filter((charge) =>
                    additionalCharges.some((c) => c.key === charge.key)
                );
                const flatRateCharges = sortedCharges.filter((charge) => charge.key in flatRates);
                const nonFlatRateCharges = sortedCharges.filter((charge) => charge.key === 'storage');
                const orderedCharges = [...nonFlatRateCharges, ...flatRateCharges, ...reportAdditionalCharges];

                return { ...report, charges: orderedCharges };
            });

            const orderedMonthlyCharges = getOrderedMonthlyCharges(monthlyRes[1].charges);
            selectedMonthReport = { ...monthlyRes[1], charges: orderedMonthlyCharges };

            // populate all charge labels
            orderedMonthlyCharges.forEach((charge) => {
                if (allChargeLabels.some((c) => c.key === charge.key)) return;
                allChargeLabels.push({ key: charge.key, label: charge.label });
            });

            renderServicesSplitDonut();
            renderDailyCostChart();
        });
    }

    function getOrderedMonthlyCharges(charges) {
        const sortedCharges = charges?.sort((a, b) => a.label.localeCompare(b.label)) ?? [];
        const flatRateCharges = sortedCharges.filter((charge) => flatRateChargeLabels.includes(charge.key));
        const nonFlatRateCharges = sortedCharges.filter((charge) => charge.key === 'storage');
        const additionalCharges = sortedCharges.filter((charge) => !knownChargeLabels.includes(charge.key));
        return [...nonFlatRateCharges, ...flatRateCharges, ...additionalCharges];
    }

    function renderProjectionPeriod(loading = false) {
        const element = rootElement.find('.projection-period');
        element.html('');

        let monthOptions = loading
            ? [{ label: 'Loading...', value: 'loading', selected: true }]
            : saasBillingPeriods.map((period) => ({
                  label: period,
                  value: moment(period, 'MMMM YYYY').format('YYYY-MM'),
                  selected: period === filters.selectedMonth.format('MMMM YYYY'),
              }));

        if (!monthOptions.some((option) => option.selected)) {
            const newOption = { label: moment().format('MMMM YYYY'), value: 'no-data', selected: true };
            monthOptions = [newOption, ...monthOptions];
        }

        new PageFilters(element, [{ id: 'selectedMonth', label: t('Period'), options: monthOptions }], (newFilters) => {
            if (newFilters.selectedMonth) {
                filters.selectedMonth = moment(newFilters.selectedMonth, 'YYYY-MM');
            }

            loadGraphs();
        });

        if (loading) {
            element.addClass('isLoading');
        } else {
            element.removeClass('isLoading');
        }
    }

    function renderServicesSplitDonut(loading = false) {
        rootElement.find('.services-split-card').addClass('isLoading');
        if (!loading) rootElement.find('.services-split-card').removeClass('isLoading');
        if (saasBillingOverviewCharts.servicesSplitDonut) {
            saasBillingOverviewCharts.servicesSplitDonut.destroy();
        }

        rootElement.find('.services-split-card').html(`
            <div class="the-info-box__header the-info-box__header--bordered">
                <div class="the-info-box__header__content" style="padding-bottom: 4px;">
                    <div class="the-info-box__header__content-col-left">
                        <h3><span class="use-skeleton rounded">${t('Service Split')}</span></h3>
                        <i
                            class="the-info-box__info the-info-box__info--relative"
                            data-toggle="tooltip"
                            data-placement="right"
                            title="${t('The split of the total cost by service.')}"
                        >
                            <span class="use-skeleton use-skeleton--absolute rounded--full"></span>
                        </i>
                    </div>
                    <div class="the-info-box__header__content-col-right">
                        <div class="filters-container"></div>
                    </div>
                </div>
            </div>
            <div class="the-info-box__body body" style="min-height: 300px;">
                <canvas id="services-split-chart" height="300"></canvas>
            </div>
        `);

        rootElement.find('.services-split-card [data-toggle="tooltip"]').tooltip();

        const report = loading ? dailyReportsOfMonthSample[0] : selectedMonthReport;
        const labels = report.charges.map((charge) => charge.label);
        const data = report.charges.map((charge) => charge.total);

        new Chart(rootElement.find('#services-split-chart'), {
            type: 'doughnut',
            data: {
                labels,
                datasets: [
                    {
                        label: t('Services Split'),
                        data,
                        hoverOffset: 4,
                        backgroundColor: graphColors.map((color) => `rgba(${color}, .2)`),
                        borderColor: graphColors.map((color) => `rgba(${color}, 1)`),
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    datalabels: {
                        formatter: (value, ctx) => {
                            let sum = 0;
                            let dataArr = ctx.chart.data.datasets[0].data;
                            dataArr.map((data) => (sum += data));
                            let percentage = ((value * 100) / sum).toFixed(2) + '%';
                            return percentage;
                        },
                        color: '#000',
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = context.parsed;
                                const shortLabel = label.length > 20 ? `${label.slice(0, 20)}...` : label;
                                return `${shortLabel}: ${formatGraphAmount(value)}`;
                            },
                        },
                    },
                },
                scales: { y: { beginAtZero: true, display: false } },
            },
            plugins: [ChartDataLabels],
        });
    }

    function renderCostBreakdownFilters() {
        new PageFilters(
            rootElement.find('.daily-cost-card .filters-container'),
            [
                {
                    id: 'groupBy',
                    label: t('Group By'),
                    options: [
                        { label: t('Service'), value: 'service', selected: filters.groupBy === 'service' },
                        { label: t('No Grouping'), value: 'none', selected: filters.groupBy === 'none' },
                    ],
                },
                {
                    id: 'costGraphType',
                    label: 'Type',
                    options: [
                        { label: t('Cumulative'), value: 'cumulative', selected: filters.costGraphType === 'cumulative' },
                        { label: t('Discrete'), value: 'discrete', selected: filters.costGraphType !== 'cumulative' },
                    ],
                },
            ],
            (newFilters) => {
                Object.keys(newFilters).forEach((key) => {
                    filters[key] = newFilters[key];
                });

                if (newFilters.groupBy || newFilters.costGraphType) renderDailyCostChart();
            }
        );
    }

    function renderWidgets() {
        rootElement.find('.widgets').html('');
        [
            {
                title: 'Cost to Date',
                id: 'this-month-widget',
                info: 'The total cost for the selected month to date.',
                icon: 'avg-spend',
                color: 'yellow',
            },
            {
                title: "Last Month's Cost (for the same time period)",
                id: 'same-time-period-widget',
                info: 'The total cost for the same time period in the previous month.',
                icon: 'total-payments',
                color: 'blue',
            },
            {
                title: 'Forecasted Usage for the Month',
                id: 'forecast-widget',
                info: 'The forecasted cost for the selected month.',
                icon: 'pending-payouts',
                color: 'green',
            },
            {
                title: "Last Month's Total Cost",
                id: 'last-month-widget',
                info: 'The total cost for the previous month.',
                icon: 'charges',
                color: 'red',
            },
        ].forEach((widget) => {
            const widgetEl = $(`
                <div id="${widget.id}" class="w gm-api ph-the-info-box the-info-box isLoading">
                    <div class="the-info-box__data">
                        <div class="icon-container isLoading">
                            <div class="the-info-box__icon__wrapper the-info-box__icon__wrapper--stroked the-info-box__icon__wrapper--stroked--${widget.color} use-skeleton">
                                <img src="./../../../assets/images/${widget.icon}-icon.svg" alt="${t(widget.title)}" class="the-info-box__icon"></img>
                            </div>
                        </div>
                        <div class="the-info-box__name__wrapper">
                            <div class="the-info-box__name">
                            <h6><span class="use-skeleton isInlineBlock rounded">${t(widget.title)}</span></h6>
                            <i class="the-info-box__info" data-toggle="tooltip" data-placement="right" title="${t(widget.info)}">
                                <span class="use-skeleton use-skeleton--absolute rounded--full"></span>
                            </i>
                            </div>
                            <div class="the-info-box__content">
                            <div class="number use-skeleton isInlineBlock rounded">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            const icon = widgetEl.find('.the-info-box__icon');
            icon.on('load', () => {
                const container = icon.closest('.icon-container');
                container.removeClass('isLoading');
            });

            rootElement.find('.widgets').append(widgetEl);
        });

        rootElement.find('.widgets [data-toggle="tooltip"]').tooltip();
    }

    function getLatestReport(reportsList) {
        return reportsList.reduce((latest, report) => {
            if ((latest?.periodStart ?? 0) < report.periodStart) return report;
            return latest;
        }, null);
    }

    function generateMonthlyDays(monthMoment) {
        return Array.from({ length: monthMoment.daysInMonth() }, (_, i) =>
            moment(monthMoment)
                .date(i + 1)
                .format('YYYY-MM-DD')
        );
    }

    function getForecastTotals(reportsList) {
        return reportsList.reduce((acc, report) => {
            report.charges.forEach((charge) => {
                if (charge.key !== 'storage') {
                    acc[charge.key] = 0;
                    return;
                }

                const curVal = acc[charge.key] ?? 0;
                acc[charge.key] = Math.max(curVal, parseFloat(charge.total));
            });
            return acc;
        }, {});
    }

    function renderDailyCostChart(loading = false) {
        const isCumulative = filters.costGraphType === 'cumulative';

        rootElement.find('.daily-cost-card').addClass('isLoading');
        if (!loading) rootElement.find('.daily-cost-card').removeClass('isLoading');
        const reports = loading ? dailyReportsOfMonthSample : dailyReportsOfMonth;

        const latestReport = getLatestReport(reports);
        const latestReportDate = moment.unix(latestReport?.periodStart ?? moment().unix()).tz('UTC');
        const monthDays = generateMonthlyDays(filters.selectedMonth);
        const forecastTotals = getForecastTotals(reports);

        let datasets = [];
        if (filters.groupBy === 'service') {
            let cumulativeTotals = {};

            datasets = monthDays.reduce((acc, day) => {
                const dayMoment = moment.tz(day, 'YYYY-MM-DD', 'UTC');
                const isForecasted = dayMoment.isSameOrAfter(latestReportDate, 'day');
                const isSameDay = dayMoment.isSame(latestReportDate, 'day');

                const report = reports.find(
                    (report) => moment.unix(report.periodStart).tz('UTC').format('YYYY-MM-DD') === day
                );

                const charges = allChargeLabels.reduce((allCharges, { key, label }) => {
                    allCharges.push({ key, label, total: null });
                    allCharges.push({ key, label: `${label} (forecast)`, total: null, forecasted: true });
                    return allCharges;
                }, []);

                charges.forEach((charge, index) => {
                    // divide by 2 so the charge and forecasted charge are the same color
                    const colorIndex = Math.floor(index / 2);
                    const color = graphColorsHalfOpaque[colorIndex % graphColorsHalfOpaque.length];
                    const lineColor = graphColors[colorIndex % graphColors.length];
                    const datasetExists = acc.some((dataset) => dataset.label === charge.label);
                    const foundCharge = report?.charges.find((c) => c.key === charge.key);
                    const cumulativeTotal = cumulativeTotals[charge.key] ?? 0;
                    const hidePopup = isSameDay && charge.forecasted && report;
                    let total = null;

                    if (foundCharge && !charge.forecasted) {
                        total = isCumulative
                            ? cumulativeTotal + parseFloat(foundCharge.total)
                            : parseFloat(foundCharge.total);

                        cumulativeTotals[charge.key] = total;
                    } else if (isForecasted && charge.forecasted) {
                        const forecastTotal = isSameDay ? 0 : forecastTotals[charge.key];
                        total = isCumulative ? cumulativeTotal + forecastTotal : forecastTotal;
                        cumulativeTotals[charge.key] = total;
                    }

                    if (datasetExists) {
                        acc = acc.map((dataset) => {
                            if (dataset.label === charge.label) {
                                dataset.data.push({ x: day, y: total, hidePopup });
                                dataset.backgroundColor.push(`rgb(${color}`);
                                dataset.borderColor.push(`rgb(${lineColor}`);
                            }

                            return dataset;
                        });

                        return;
                    }

                    acc.push({
                        label: charge.label,
                        data: [{ x: day, y: total, hidePopup }],
                        backgroundColor: [`rgb(${color})`],
                        borderColor: [`rgb(${color})`],
                        borderWidth: 1,
                        borderDash: charge.forecasted ? [5, 5] : [],
                        pointRadius: charge.forecasted ? 0 : 3,
                        pointHoverRadius: charge.forecasted ? 0 : 3,
                        stack: charge.forecasted ? 'forecasted' : 'actual',
                        fill: !charge.forecasted,
                    });
                });

                return acc;
            }, []);
        } else {
            let cumulativeTotal = 0;
            const data = monthDays.reduce((acc, day) => {
                const isForecasted = moment(day, 'YYYY-MM-DD').isAfter(latestReportDate, 'day');
                const report = reports.find((r) => moment.unix(r.periodStart).tz('UTC').format('YYYY-MM-DD') === day);
                const total = report ? parseFloat(report.grandTotal) : isForecasted ? forecastTotals['storage'] : 0;
                cumulativeTotal = isCumulative ? cumulativeTotal + total : total;
                acc.push({ x: day, y: cumulativeTotal, isForecasted });
                return acc;
            }, []);

            datasets = [
                {
                    data,
                    backgroundColor: data.map(({ isForecasted }) =>
                        isForecasted ? `rgba(${graphColors[0]}, .1)` : `rgba(${graphColors[0]}, 1)`
                    ),
                    borderColor: data.map(({ isForecasted }) =>
                        isForecasted ? `rgba(${graphColors[0]}, .2)` : `rgba(${graphColors[0]}, 1)`
                    ),
                    borderWidth: 1,
                },
            ];
        }

        if (saasBillingOverviewCharts.dailyCostChart) {
            saasBillingOverviewCharts.dailyCostChart.destroy();
        }

        rootElement.find('.daily-cost-card').html(`
            <div class="the-info-box__header the-info-box__header--bordered">
                <div class="the-info-box__header__content">
                    <div class="the-info-box__header__content-col-left">
                        <h3><span class="use-skeleton rounded">${t('Cost Breakdown')}</span></h3>
                        <i
                            class="the-info-box__info the-info-box__info--relative"
                            data-toggle="tooltip"
                            data-placement="right"
                            title="${t('The daily usage cost breakdown for the selected month.')}"
                        >
                            <span class="use-skeleton use-skeleton--absolute rounded--full"></span>
                        </i>
                    </div>
                    <div class="the-info-box__header__content-col-right">
                        <div class="filters-container d-flex gap-1"></div>
                    </div>
                </div>
            </div>
            <div class="the-info-box__body body use-skeleton">
                <div class="upcoming-bookings-widget-chart use-skeleton rounded">
                    <div id="gross-volume-graph" style="position: relative; height: 300px;">
                        <canvas width="1092" height="600" style="display: block; box-sizing: border-box; height: 300px; width: 546px;"></canvas>
                    </div>
                </div>
            </div>
        `);

        rootElement.find('.daily-cost-card [data-toggle="tooltip"]').tooltip();
        renderCostBreakdownFilters();

        const yLine = {
            id: 'yLine',
            afterDraw: (chart) => {
                const { tooltip } = chart;

                if (tooltip._active && tooltip._active.length) {
                    const {
                        ctx,
                        chartArea: { top, bottom },
                    } = chart;
                    ctx.save();
                    const activePoint = tooltip._active[0];
                    // draw the vertical line chartjs
                    ctx.beginPath();
                    ctx.setLineDash([5, 5]);
                    ctx.moveTo(activePoint.element.x, top);
                    ctx.lineTo(activePoint.element.x, bottom);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = 'rgba(179,9,60,.7)';
                    ctx.stroke();
                    ctx.restore();
                }
            },
        };

        saasBillingOverviewCharts.dailyCostChart = new Chart(rootElement.find('.daily-cost-card canvas'), {
            type: isCumulative ? 'line' : 'bar',
            data: { datasets },
            options: {
                interaction: { intersect: false, axis: 'x', mode: 'index' },
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y;
                                if (value == null || context.raw.hidePopup) return null;
                                const { isForecasted } = context.dataset.data[context.dataIndex];
                                const forecastLabel = isForecasted ? ' (forecast)' : '';
                                return `${label}: ${formatGraphAmount(value)} ${forecastLabel}`;
                            },
                            footer: (tooltipItems) => {
                                if (filters.groupBy !== 'service') return null;
                                const currency = 'USD';
                                const total = tooltipItems.reduce((acc, item) => {
                                    if (item.raw.hidePopup) return acc;
                                    return acc + item.parsed.y;
                                }, 0);
                                const formatted = new Intl.NumberFormat('en-US', {
                                    style: 'currency',
                                    currency,
                                    currencyDisplay: 'narrowSymbol',
                                }).format(total);
                                return `${t('Grand Total')}: ${formatted}`;
                            },
                        },
                    },
                    legend: { display: false },
                },
                scales: {
                    y: {
                        stacked: true,
                        display: true, // Changed to true to display the Y-axis
                        beginAtZero: true,
                        ticks: {
                            // Format the tick labels as currency values
                            callback: function (value, index, values) {
                                return '$' + value.toLocaleString(); // Adjust according to your locale
                            },
                        },
                        grid: {
                            drawBorder: true, // Ensures the border is drawn
                            color: function (context) {
                                if (context.tick.value === 0) return '#EBEEF1'; // Color for the zero line, adjust as needed
                                return '#f0f0f0'; // Color for other grid lines, adjust as needed
                            },
                            borderColor: '#EBEEF1', // Border color for the Y-axis, adjust as needed
                        },
                    },

                    x: {
                        ticks: { font: { size: 10 }, maxRotation: 0, color: '#6A7383' },
                        grid: { color: '#EBEEF1', borderColor: '#EBEEF1' },
                    },
                },
            },
            plugins: [yLine],
        });
    }

    function formatGraphAmount(amount) {
        const abs = Math.abs(amount);
        const isNegative = amount < 0;
        return `${isNegative ? '-' : ''}$${abs.toFixed(2)}`;
    }

    init();
}

function getSaasBillingPeriodsBasedOnDailyLogStartMonth(startMonth = moment().format('YYYY-MM')) {
    const startMoment = moment(startMonth, 'YYYY-MM');
    const currentMoment = moment();
    let periods = [];

    while (startMoment.isSameOrBefore(currentMoment, 'month')) {
        periods = [startMoment.format('MMMM YYYY'), ...periods];
        startMoment.add(1, 'month');
    }

    return periods;
}