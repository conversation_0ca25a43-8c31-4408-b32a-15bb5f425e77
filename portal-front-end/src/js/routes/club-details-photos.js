function renderClubImages(clubData) {
    $('#additional-images .image-list').html(getImageContainer({ type: 'additional' }));
    $('.logo-image-container').replaceWith(getImageContainer({ type: 'logo' }));
    $('.cover-image-container').replaceWith(getImageContainer({ type: 'cover' }));
    $('.shop-front-image-container').replaceWith(getImageContainer({ type: 'shop-front' }));

    clubData.photos?.reverse().forEach((photo) => {
        if (photo.type === 'logo') {
            $('.logo-image-container').replaceWith(getImageContainer(photo));
        } else if (photo.type === 'cover') {
            $('.cover-image-container').replaceWith(getImageContainer(photo));
        } else if (photo.type === 'additional') {
            $('#additional-images .image-list').prepend(getImageContainer(photo));
        } else if (photo.type === 'shop-front') {
            $('.shop-front-image-container').replaceWith(getImageContainer(photo));
        }
    });

    $(document).on('click', '.image-container .delete-image', (e) => {
        e.stopPropagation();
        const { id } = $(e.currentTarget).data();
        deleteClubImage(id);
    });

    // Star image
    $(document).on('click', '.star-image', (e) => {
        e.stopPropagation();
        const { id } = $(e.currentTarget).data();
        const image = clubData.photos.find((p) => p.id === id);
        updateClubImage(id, { starred: !image?.starred });

        $(e.currentTarget)
            .closest('.image-container')
            .attr('starred', image?.starred ? 'unstarred' : 'starred');

        Swal.close();
    });

    // Toggle upload image modal when add button is clicker
    $(document).on('click', '#images .image-container.empty', (e) => {
        imageUploadModal();
    });

    // Toggle upload image modal when update button is clicked
    $(document).on('click', '#images .image-container .update-image', (e) => {
        e.stopPropagation();
        const { id } = $(e.currentTarget).data();
        const image = clubData.photos.find((p) => p.id === id);
        if (!image) return;

        image.type === 'shop-front'
            ? renderPanoramaSelectorDialog(image)
            : imageUploadModal($(e.currentTarget).data('id'));
    });

    $(document).on('click', '#images .image-container:not(.empty)', (e) => {
        const element = $(e.currentTarget);
        const { id } = element.data();
        const photo = clubData.photos?.find((photo) => photo.id === id);

        if (!photo) return;
        if (photo.type === 'shop-front') return renderPanoramaSelectorDialog(photo);
        previewClubImageDialog(photo);
    });

    function previewClubImageDialog(photo) {
        const div = document.createElement('div');
        $(div).addClass('preview-image-dialog ph-dialog-v2');
        $(div).html(`
            <div class="body">
                <h3 style="padding: 40px; text-align: center;">${t('One Moment Please')}...</h3>
            </div>
            <div class="footer"></div>
        `);

        let actions = null;

        if (photo.type === 'additional') {
            actions = `
                <div class="actions d-flex justify-between align-center">
                    <i class="star-image" data-id="${photo.id}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17"><path class="cls-1" d="M781.218,715.793l-4.958-.466a0.856,0.856,0,0,1-.71-0.522l-1.776-4.278a0.852,0.852,0,0,0-1.577,0l-1.761,4.278a0.838,0.838,0,0,1-.711.522l-4.957.466a0.851,0.851,0,0,0-.483,1.483l3.736,3.262a0.842,0.842,0,0,1,.27.833l-1.123,4.575a0.856,0.856,0,0,0,1.265.932l4.134-2.415a0.865,0.865,0,0,1,.866,0l4.134,2.415a0.853,0.853,0,0,0,1.264-.932l-1.108-4.575a0.842,0.842,0,0,1,.27-0.833l3.736-3.262A0.861,0.861,0,0,0,781.218,715.793Z" transform="translate(-764 -710)" /><head xmlns="" /></svg>
                    </i>
                    <div class="d-flex gap-1">
                        <button class="btn btn-danger">${t('Delete')}</button>
                        <button class="btn btn-default close-btn">${t('Close')}</button>
                    </div>
                </div>
            `;
        } else if (photo.type === 'cover') {
            actions = `
                <div class="actions d-flex justify-end align-center gap-1">
                    <button class="btn btn-primary change-btn">${t('Change')}</button>
                    <button class="btn btn-default close-btn">${t('Close')}</button>
                </div>
            `;
        }

        const renderPreviewImg = new Image();
        renderPreviewImg.src = photo.url;
        renderPreviewImg.onload = () => {
            $(div).find('.body').html(`<div class="image-container"></div>`);
            $(div).find('.image-container').html(renderPreviewImg);
            if (actions) $(div).find('.footer').append(actions);

            $(div)
                .find('.change-btn')
                .on('click', () => imageUploadModal(photo.id));

            $(div).find('.close-btn').on('click', Swal.close);

            $(div)
                .find('.reset-btn')
                .on('click', async () => {
                    loadingOverlay(div, true, true);
                    const defaultPhotoUrl = await getDefaultShopFrontPhoto(photo);
                    if (!defaultPhotoUrl) return Swal.close();
                    renderPanoramaSelectorDialog({ ...photo, url: defaultPhotoUrl });
                });
        };

        swalWithBootstrapButtons.fire({
            title: t('Preview Image'),
            html: div,
            width: 800,
            showCloseButton: true,
            showCancelButton: false,
            showConfirmButton: false,
            animation: false,
        });
    }

    async function getDefaultShopFrontPhoto(photo) {
        const defaultPhoto = await getDefaultClubPhotoAPI(selectedID, photo.type);

        if (!defaultPhoto[0]) {
            instantNotification(
                t('Unable to retrieve image. Please contact HQ to further investigate this issue.'),
                'error'
            );

            return false;
        }

        return defaultPhoto[1].url;
    }

    function renderPanoramaSelectorDialog(photo) {
        let isStreetViewImage = photo.url.includes('maps.googleapis.com');
        const div = document.createElement('div');
        $(div).addClass('panorama-selector-dialog ph-dialog-v2');
        $(div).html(`
            <div class="body">
                <p style="font-size: 16px; color: #30313D;">
                    ${t(`Customize the image that represents your facilities entrance.`)}
                </p>
                <p style="margin-bottom: 24px;">
                    <em style="font-size: 12px;">
                        * ${t(
                            'Please note that shopfront images are displayed on landing pages on the main UBX website, and are not published to Google Maps and other third party directories'
                        )}
                    </em>
                </p>

                <ul class="nav nav-tabs tab-nav-right club-hours-tab" role="tablist">
                    <li role="presentation" class="${isStreetViewImage ? 'active' : ''}">
                        <a href="#street-view-tab" data-toggle="tab">${t('Google Street View')}</a>
                    </li>
                    <li role="presentation" class="${!isStreetViewImage ? 'active' : ''}">
                        <a href="#upload-picture-tab" data-toggle="tab">${t('Upload Your Own Picture')}</a>
                    </li>
                </ul>

                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane in ${isStreetViewImage ? 'active' : ''}" id="street-view-tab">
                        <p style="font-weight: 400; color: #A3ACBA; margin-bottom: 14px;">
                            ${t(`Pan and select the perfect view that reflects your club's entrance.`)}
                        </p>
                        <div id="pano-map"></div>
                    </div>
                    <div role="tabpanel" class="tab-pane in ${
                        !isStreetViewImage ? 'active' : ''
                    }" id="upload-picture-tab">
                        <p style="font-weight: 400; color: #A3ACBA; margin-bottom: 14px;">
                            ${t(`Upload an image of your facilities entrance.`)}
                        </p>
                        <img class="preview d-none"></img>
                        <div class="dz-container dropzone ${
                            !isStreetViewImage || clubData.previouslyUsedPhotos ? 'min' : ''
                        }"></div>

                        <div class="used-images d-none">
                            <p style="text-transform: uppercase; font-size: 11px; color: #6A7383; font-weight: 600; margin-bottom: 1.5rem;">
                                ${t(`Previously Used Images`)}
                            </p>
                            <div class="image-thumbnails"></div>
                        </div>
                    </div>
                </div>

                <div class="display-type-container">
                    <p class="heading">
                        ${t('Please select what to display on your facility website page')}
                    </p>
                    <div class="d-flex gap-3 justify-center">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="url-type" value="street-view" ${
                                isStreetViewImage ? 'checked' : ''
                            }>
                            <label class="form-check-label" for="exampleRadios1">
                                ${t('Use Google Street View')}
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="url-type" value="upload" ${
                                !isStreetViewImage ? 'checked' : ''
                            }>
                            <label class="form-check-label" for="exampleRadios2">
                                ${t('Use Own Picture')}
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer d-flex justify-between gap-1">
                <button class="btn btn-default reset-btn">${t('Reset')}</button>
                <div class="d-flex gap-1">
                    <button class="btn btn-primary save-btn">${t('Save')}</button>
                    <button class="btn btn-default close-btn">${t('Close')}</button>
                </div>
            </div>
        `);

        let uploadUrl = photo.url;
        let panoData = getPanoDataFromUrl(photo.url);

        swalWithBootstrapButtons.fire({
            title: t('Shop-Front Image'),
            html: div,
            width: 632,
            showCloseButton: true,
            showConfirmButton: false,
            allowEscapeKey: false,
            allowOutsideClick: false,
            onOpen: async () => {

                const swalElement = Swal.getPopup();
                $(swalElement).draggable({
                    handle: '.swal2-header',  // Makes the title bar the drag handle
                });
                // Only change the cursor to move for the title bar
                $(swalElement).find('.swal2-header').css('cursor', 'move');

                renderPanoSection();
                rerenderDropZone();
                renderUsedImages();
            },
        });

        $(div).on('click', '.form-check', (e) => {
            $(e.currentTarget).find('.form-check-input').prop('checked', false);
            $(e.currentTarget).find('.form-check-input').prop('checked', true);
            isStreetViewImage = $(e.currentTarget).find('.form-check-input').val() === 'street-view';
        });

        $(div)
            .find('.save-btn')
            .on('click', () => {
                const url = isStreetViewImage ? panoData.url : uploadUrl;
                updateClubImage(photo.id, { url }, true);
                Swal.close();
            });

        $(div)
            .find('.reset-btn')
            .on('click', async () => {
                loadingOverlay(div, true, true);
                const defaultPhotoUrl = await getDefaultShopFrontPhoto(photo);
                if (!defaultPhotoUrl) return Swal.close();

                pushPreviousUsedPhotos(uploadUrl);
                renderPanoramaSelectorDialog({ ...photo, url: defaultPhotoUrl });
            });

        $(div).find('.close-btn').on('click', Swal.close);

        function renderUsedImages() {
            $(div).find('.used-images > .image-thumbnails').replaceWith(`
                <div class="image-thumbnails">
                    <button class="add-photo-btn ${isStreetViewImage ? 'selected' : ''}">
                        <i class="fa fa-plus"></i>
                        <span>${t('ADD PHOTO')}</span>
                    </button>
                </div>
            `);

            let usedPhotos = clubData.previouslyUsedPhotos.filter((url) => url !== uploadUrl).slice(0, 4);

            if (!isStreetViewImage) {
                usedPhotos = usedPhotos.slice(0, 3);
            }

            usedPhotos.reverse().forEach((url) => {
                $(div).find('.used-images > .image-thumbnails').prepend(`
                    <button class="select-photo-btn">
                        <img src="${url}"></img>
                    </button>
                `);
            });

            if (!isStreetViewImage) {
                loadingOverlay($(div).find('.preview'), true, true);
                $(div).find('.preview').removeClass('d-none').attr('src', uploadUrl);
                $(div).find('.dropzone').addClass('d-none');

                $(div).find('.used-images > .image-thumbnails').prepend(`
                <button class="select-photo-btn selected">
                    <img src="${uploadUrl}"></img>
                </button>
            `);

                $(div)
                    .find('.preview')
                    .on('load', () => {
                        loadingOverlay($(div).find('.preview'), false);
                    });
            }

            if (!isStreetViewImage || clubData.previouslyUsedPhotos.length > 0) {
                $(div).find('.used-images').removeClass('d-none');
            }

            $(div)
                .find('.add-photo-btn')
                .on('click', (e) => {
                    $(div).find('.dropzone').removeClass('d-none').addClass('min');
                    $(div).find('.preview').addClass('d-none');
                    $(div).find('.used-images button').removeClass('selected');
                    $(e.currentTarget).addClass('selected');
                });

            $(div)
                .find('.select-photo-btn')
                .on('click', (e) => {
                    loadingOverlay($(div).find('.preview'), true, true);
                    uploadUrl = $(e.currentTarget).find('img').attr('src');
                    $(div).find('.dropzone').addClass('d-none');
                    $(div).find('.preview').removeClass('d-none');
                    $(div).find('.used-images button').removeClass('selected');
                    $(e.currentTarget).addClass('selected');
                    $(div).find('.preview').attr('src', uploadUrl);
                    $(div)
                        .find('.preview')
                        .on('load', () => {
                            loadingOverlay($(div).find('.preview'), false);
                        });
                });
        }

        function rerenderDropZone() {
            $(div).find('.dropzone').replaceWith('<div class="dz-container dropzone min"></div>');
            renderDropZone($(div).find('.dropzone'), {
                maxFiles: 1,
                acceptedFiles: '.jpeg,.jpg,.png',
                success: (url) => {
                    rerenderDropZone();
                    pushPreviousUsedPhotos(uploadUrl);
                    uploadUrl = url;
                    isStreetViewImage = false;
                    $(div).find('.preview').removeClass('d-none').attr('src', uploadUrl);
                    $(div).find('.dropzone').addClass('d-none');
                    renderUsedImages();
                },
                addedfiles: () => {
                    $(div).find('.footer button').prop('disabled', true);
                },
                queuecomplete: () => {
                    $(div).find('.footer button').prop('disabled', false);
                },
            });
        }

        async function renderPanoSection() {
            const panoSection = $(div).find('#street-view-tab');

            loadingOverlay(panoSection, true, true);

            if (!isStreetViewImage) {
                const defaultPhotoUrl = await getDefaultShopFrontPhoto(photo);
                if (!defaultPhotoUrl) return Swal.close();
                panoData = getPanoDataFromUrl(defaultPhotoUrl);
            }

            if (!panoData.heading && panoData.lat && panoData.lng) {
                const heading = await getDefaultStreetViewHeading(panoData.lat, panoData.lng);
                panoData.heading = heading;
            }

            loadingOverlay(panoSection, false);

            const config = panoData.pano
                ? { pano: panoData.pano }
                : {
                      position: { lat: panoData.lat, lng: panoData.lng },
                      pov: { heading: panoData.heading, pitch: panoData.pitch, zoom: panoData.zoom },
                  };

            const panorama = new google.maps.StreetViewPanorama(document.getElementById('pano-map'), config);

            google.maps.event.addListener(panorama, 'pov_changed', () => {
                if (!panorama.position) return;
                const queries = {
                    location: [panorama.position?.lat(), panorama.position?.lng()].join(','),
                    heading: panorama.getPov().heading,
                    pitch: panorama.getPov().pitch,
                    fov: zoomToFOV(panorama.getPov().zoom),
                    size: '600x300',
                    key: GOOGLE_STREETVIEW_KEY,
                };

                const queryString = Object.entries(queries).reduce((acc, [key, value]) => {
                    return `${acc}&${key}=${value}`;
                }, '');

                panoData.url = `https://maps.googleapis.com/maps/api/streetview?${queryString}`;
            });
        }

        function getPanoDataFromUrl(url) {
            const queryStrings = Object.fromEntries(new URL(url).searchParams.entries());

            return {
                lat: +(queryStrings?.location?.split(',')[0] || 0),
                lng: +(queryStrings?.location?.split(',')[1] || 0),
                heading: +(queryStrings?.heading || 0),
                pitch: +(queryStrings?.pitch || 0),
                zoom: fovToZoom(+(queryStrings?.fov ?? 90)),
                pano: queryStrings?.pano,
                url,
            };
        }
    }

    function pushPreviousUsedPhotos(url) {
        clubData.previouslyUsedPhotos = Array.from(new Set([url, ...clubData.previouslyUsedPhotos]))
            .filter((url) => !url.includes('maps.googleapis.com'))
            .slice(0, 4);
    }

    function getDefaultStreetViewHeading(lat, lng) {
        let point = new google.maps.LatLng(lat, lng);
        const streetViewService = new google.maps.StreetViewService();

        return new Promise((resolve) => {
            streetViewService.getPanoramaByLocation(point, 100, function (streetViewPanoramaData, status) {
                if (status === google.maps.StreetViewStatus.OK) {
                    var oldPoint = point;
                    point = streetViewPanoramaData.location.latLng;
                    const heading = google.maps.geometry.spherical.computeHeading(point, oldPoint);
                    resolve(heading);
                } else {
                    console.error('Sorry! Street View is not available.');
                    resolve(0);
                }
            });
        });
    }

    function zoomToFOV(zoom = 1) {
        return 180 / Math.pow(2, zoom);
    }

    function fovToZoom(fov = 90) {
        return Math.log2(180 / fov);
    }

    function getImageContainer({ url, type, starred, id }) {
        const hasActionButtons = type !== 'logo' && url;

        const actionButtons = {
            additional: `
                <i class="star-image" data-id="${id}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17"><path class="cls-1" d="M781.218,715.793l-4.958-.466a0.856,0.856,0,0,1-.71-0.522l-1.776-4.278a0.852,0.852,0,0,0-1.577,0l-1.761,4.278a0.838,0.838,0,0,1-.711.522l-4.957.466a0.851,0.851,0,0,0-.483,1.483l3.736,3.262a0.842,0.842,0,0,1,.27.833l-1.123,4.575a0.856,0.856,0,0,0,1.265.932l4.134-2.415a0.865,0.865,0,0,1,.866,0l4.134,2.415a0.853,0.853,0,0,0,1.264-.932l-1.108-4.575a0.842,0.842,0,0,1,.27-0.833l3.736-3.262A0.861,0.861,0,0,0,781.218,715.793Z" transform="translate(-764 -710)" /><head xmlns="" /></svg>
                </i>
                <i class="material-icons delete-image" data-id="${id}">delete</i>
                <input type="file" data-type="${type.toUpperCase()}" style="display: none;">
            `,
            cover: `<i class="material-icons update-image" data-id="${id}">camera_alt</i>`,
            'shop-front': `<i class="material-icons update-image" data-id="${id}">camera_alt</i>`,
        };

        const backgroundCSS = id ? `background-color: #000;` : '';
        const emptyClx = !id ? `empty` : '';
        const typeClx = `${type.toLowerCase()}-image-container`;
        const starredProp = starred ? 'starred' : 'unstarred';
        const dataId = id ? `data-id="${id}"` : '';

        return `
            <div
                class="image-container ${typeClx} ${emptyClx}"
                ${dataId}
                starred="${starredProp}"
            >
                <div class="card image" style="${backgroundCSS}">
                    ${!url ? '<i class="material-icons">add_circle</i>' : `<img src="${url}" />`}
                </div>
                ${hasActionButtons ? actionButtons[type.toLowerCase()] || '' : ''}
            </div>
        `;
    }

    function imageUploadModal(imageId) {
        const image = clubData.photos?.find((p) => p.id === imageId);
        const type = image?.type ?? 'additional';

        const imageUploadDialog = `
            <div class="ph-dialog-v2 image-upload-dialog">
                <div class="body">
                    <p>
                        <small>
                            ${t(
                                'Please only upload image files (jpg, png etc) - other formats will not be converted or distributed to your external integrations.'
                            )}
                        </small>
                    </p>
                    <div id="imageDZUploads" class="dropzone"></div>
                </div>
            </div>
        `;

        const attachments = [];

        swalWithBootstrapButtons
            .fire({
                title: t('Upload Images'),
                html: imageUploadDialog,
                width: '800px',
                showCloseButton: false,
                showConfirmButton: true,
                showCancelButton: true,
                allowEscapeKey: false,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                allowOutsideClick: false,

                onBeforeOpen: () => {
                    swalWithBootstrapButtons.getConfirmButton().setAttribute('disabled', '');

                    // Setup Dropzone within the new agreement dialog...
                    $('div#imageDZUploads').dropzone({
                        maxFiles: !imageId ? null : 1,
                        url: `/api/asset-upload`,
                        parallelUploads: 5,
                        acceptedFiles: '.jpeg,.jpg,.png',
                        addRemoveLinks: true,
                        timeout: 3600000,
                        dictDefaultMessage: t('Drop files here to upload'),
                        dictFallbackText: t(
                            'Please use the fallback form below to upload your files like in the olden days.'
                        ),
                        dictFileTooBig:
                            t('File is too big') + '({{filesize}}MiB).' + t('Max filesize') + ': {{maxFilesize}}MiB.',
                        dictInvalidFileType: t("You can't upload files of this type."),
                        dictResponseError: t('Server responded with') + '{{statusCode}}' + t('code.'),
                        dictCancelUpload: t('Cancel upload'),
                        dictUploadCanceled: t('Upload canceled.'),
                        dictCancelUploadConfirmation: t('Are you sure you want to cancel this upload?'),
                        dictRemoveFile: t('Remove file'),
                        dictMaxFilesExceeded: t('You can not upload any more files.'),
                        addedfiles: () => {
                            swalWithBootstrapButtons.getConfirmButton().setAttribute('disabled', '');
                        },
                        queuecomplete: () => {
                            swalWithBootstrapButtons.getConfirmButton().removeAttribute('disabled');
                        },
                        success: function (_, response) {
                            if (
                                response !== undefined &&
                                response.location !== undefined &&
                                response.originalname !== undefined
                            ) {
                                attachments.push({ type, url: response.location });
                            }
                        },
                        error: (file) => {
                            file.previewElement.classList.add('dz-error');
                        },
                        sending: (_, xhr) => {
                            xhr.ontimeout = (e) => {
                                console.info('Server Timeout');
                            };
                        },
                    });
                },
            })
            .then((response) => {
                if (response.value) {
                    attachments.forEach((attachment) => {
                        if (!imageId) {
                            addClubImage(attachment);
                            renderClubImages(clubData);
                        } else {
                            updateClubImage(imageId, attachment, true);
                        }
                    });

                    $('#save-club-footer').addClass('active');
                }
            });
    }

    function updateClubImage(imageId, attrs = {}, render = false) {
        let photo = clubData.photos.find((p) => p.id === imageId);
        if (!photo) return;

        pushPreviousUsedPhotos(photo.url);

        clubData.photos = (clubData.photos || []).map((p) => {
            if (p.id !== imageId) return p;
            return { ...p, ...attrs };
        });

        $('#save-club-footer').addClass('active');
        renderShopFrontNotification(clubData);
        render && $(`.image-container[data-id=${imageId}]`).replaceWith(getImageContainer({ ...photo, ...attrs }));
    }

    function addClubImage(attrs = {}, render = false) {
        const newImage = { id: uuidv4(), type: 'additional', ...attrs };
        clubData.photos = [...(clubData.photos || []), newImage];
        render && renderClubImages(clubData);
        $('#save-club-footer').addClass('active');
    }

    function deleteClubImage(imageId) {
        confirmDeleteDialog(null, null, () => {
            clubData.photos = clubData.photos.filter((photo) => photo.id !== imageId);
            $('#save-club-footer').addClass('active');
            $(`.image-container[data-id=${imageId}]`).remove();
        });
    }
}
