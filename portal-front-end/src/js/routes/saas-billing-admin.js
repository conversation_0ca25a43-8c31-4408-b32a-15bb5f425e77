function sSaasBillingAdmin() {
    const basePath = '/saas-billing-admin';
    const tabs = ['charges-config', 'stripe-customer-mapping', 'invoice-history', 'billing-imports'];

    function init() {
        initUrl();
        initTabs();
        handleTabChange();
        renderPageNotifications();
    }

    function initUrl() {
        const tab = location.pathname.split('/').pop();
        if (tabs.includes(tab)) return;
        history.pushState({ tabChange: true }, null, `${basePath}/${tabs[0]}`);
    }

    function initTabs() {
        const tab = location.pathname.split('/').pop();

        $(`.nav-tabs a[href="#${tab}"]`).tab('show');
        $('.nav-tabs a').on('click', function (e) {
            e.preventDefault();
            const href = $(this).attr('href');
            const formattedHref = `${basePath}/${href.replace('#', '')}`;
            history.pushState({ tabChange: true }, null, formattedHref);
            handleTabChange();
        });
    }

    function handleTabChange() {
        const tab = location.pathname.split('/').pop();
        if (tab === 'charges-config') return sSaasBillingChargesConfig();
        if (tab === 'stripe-customer-mapping') return sStripeCustomerMapping();
        if (tab === 'invoice-history') return sSaaSBillingInvoiceHistory();
        if (tab === 'billing-imports') return sSaaSBillingImports();
    }

    async function renderPageNotifications() {
        $('.page-notifications').html('');
        const response = await paginateMonthlyBillingRecordsAPI({ month: 'all', year: 'all', status: 'failed' });
        if (!response[0] || response[1].data.length < 1) return;

        $('.page-notifications').html(`
            <div class="custom-alert custom-alert--danger" style="" role="alert">
                <div class="custom-alert__content" style="width: 100%;">
                    <div class="custom-alert__icon">
                        <i class="warning-icon custom-icon"></i>
                    </div>
                    <div class="custom-alert__body">
                        <h6>${t('Failed Billing Records')}</h6>
                        <p>
                            ${t(
                                `Some records failed to be billed. You can manually retry these in the billing invoices management page.`
                            )}
                        </p>
                    </div>
                </div>
                <a href="/saas-billing-admin/invoice-history?month=all&year=all&status=failed" class="btn btn-danger">${t(
                    'View Records'
                )}</a>
            </div>
        `);
    }

    init();
}
