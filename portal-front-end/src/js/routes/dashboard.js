var widgetTimeRange =
    localStorage.getItem('dashboardTimerange') === null ? 14 : localStorage.getItem('dashboardTimerange');

const chartData = {
    revenue: []
}

let dashboardAlertSession = null;

const chartColors = [
    { border: 'rgb(188, 131, 238)', background: 'rgba(188, 131, 238, 0.2)' },
    { border: 'rgb(255, 106, 183)', background: 'rgba(255, 106, 183, 0.2)' },
    { border: 'rgb(255, 118, 104)', background: 'rgba(255, 118, 104, 0.2)' },
    { border: 'rgb(33, 150, 243)', background: 'rgba(33, 150, 243, 0.2)' },
];

const tooltipUI = {
    backgroundColor: '#1e293b',
    titleColor: '#f1f5f9',
    bodyColor: '#cbd5e1',
    padding: 8,
    titleFont: {
        size: 12,
        weight: 'bold',
    },
    bodyFont: {
        size: 10,
    },
}

function weekBookings (data, date) {
    let result = 0
    let weekData = []
    let weekLabels = []

    for (const i of Array(7).keys()) {
        const date = moment().add(i, 'days').format('YYYY-MM-DD');
        const bookings = data.filter((booking) => moment(booking.date * 1000).format('YYYY-MM-DD') === date);
        const currentDay = moment(date, 'YYYY-MM-DD').format('dddd');
        const isToday = moment().format('dddd') === currentDay
        const currentDate = new Date(date);

        const day = {
            x: currentDay,
            y: bookings.length,
            day: isToday ? 'Today' : currentDay,
            date: currentDate,
            dayIndex: i,
            bookings
        };

        weekData.push(day);
        weekLabels.push(currentDay);

        if(bookings.length > 0) result++;
    }

    const todaysData = weekData.find((data) => moment(data.date).format('MMM/DD/YYYY') === moment(date).format('MMM/DD/YYYY'))

    return {
        weekData,
        weekLabels,
        todaysBooking: todaysData ? todaysData.bookings : [],
        hasBookings: result > 0
    }
}

function renderTodaysBooking (todaysBooking, bookingList, day = "Today") {

    bookingList.html('')

    if(todaysBooking.length < 1) {
        bookingList.html(
            `<li class="list-group-item row-span-3">${t(`There are no scheduled bookings for ${day}`)}</li>`
        );
        return
    }

    bookingList.append(
        `<li class="list-group-item list-group-item--day">${t(`${day}'s Schedule`)}</li>`
    );

    todaysBooking.forEach((booking) => {
        const time = moment(booking.date * 1000).format('hh:mma');
        bookingList.append(
            `<li class="list-group-item">
                <span>${booking.name}</span>
                <span> ${time}</span>
            </li>`
        );
    });
}

function displayCurrentBookings(data, date = new Date(), day = "Today", dayIndex = 0) {
    // list bookings for today | the selected date
    // check if week has booking and weekData
    const {todaysBooking, hasBookings, weekData} = weekBookings(data, date)

    // el selectors
    const bookingList = $('.bookings__wrapper .body ul')
    const navBtn = $('.bookings-nav');

    // empty the list
    bookingList.html('');

    // if no booking for the whole week 
    if(!hasBookings) {
        bookingList.html(
            `<li class="list-group-item row-span-3">${t('There are no bookings scheduled in the next 7 days')}</li>`
        );
        navBtn.hide()
        return;
    }

    // if we got bookings for the week ---
    navBtn.show()
    // unbind btn events
    navBtn.unbind()

    // set the initial prev and next index
    let currentIndex = dayIndex
    let prevIndex = currentIndex - 1;
    let nextIndex = currentIndex + 1;

    // disable buttons if it exceeds range
    function disableButtons(prevIndex,nextIndex) {
        $('#bookings-prev').prop('disabled', prevIndex < 0)
        $('#bookings-next').prop('disabled', nextIndex > 6)
    }
    disableButtons(prevIndex, nextIndex)
    
    // add navigate booking events
    navBtn.on('click', function() {
        const direction = $(this).data('direction');
        
        currentIndex = direction === 'prev' ? prevIndex : nextIndex
        prevIndex = currentIndex - 1
        nextIndex = currentIndex + 1
        const activeData = weekData.find(data => data.dayIndex === currentIndex)

        // update disabled buttons 
        disableButtons(prevIndex, nextIndex)

        // don't do anything 
        if(currentIndex < 0 || currentIndex > 6) return

        // update booking list display
        const {day, bookings} = activeData
        renderTodaysBooking(bookings, bookingList, day)

    })
    // ------

    // render the bookings details
    if (todaysBooking.length > 0) {
        renderTodaysBooking(todaysBooking, bookingList, day)
        return;
    }

    // show if there's no bookings for the current day
    bookingList.html(
        `<li class="list-group-item row-span-3">${t(`There are no scheduled bookings for ${day}`)}</li>`
    );
}

function drawWorkoutBookingsChart(data) {
    $('#upcoming_bookings_chart').addClass('d-none');

    const datasets = [
        {
            data: [],
            labels: [],
            backgroundColor: 'rgba(33, 150, 243, .2)',
            borderColor: 'rgb(33, 150, 243)',
            borderWidth: 1,
        },
    ];

    if (data.length > 0) {
        $('.bookings__wrapper-chart').removeClass('d-none');
        $('#upcoming_bookings_chart').removeClass('d-none'); 
        const {weekData, weekLabels} = weekBookings(data)

        datasets[0].data = weekData;
        datasets[0].labels = weekLabels; 
    } else {
        $('.bookings__wrapper-chart').addClass('d-none');
    }


    const chartEl = $('#upcoming_bookings_chart');
    const checkActive = Chart.getChart('upcoming_bookings_chart');
    if (checkActive !== undefined) {
        chartEl.unbind();
        checkActive.destroy();
    }

    const hoverBackground = {
        id: 'hoverBackground',
        beforeDatasetsDraw(chart){
            const {
                ctx, tooltip, chartArea: {
                    top,
                    height
                }
            } = chart;

            // active intersection
            const activeData = tooltip._active[0]

            // check if the section intersection is hover
            if(activeData) {

                // the index of the currenthover intersection
                const index = activeData.index

                // get the bar element
                const el = activeData.element

                // get the width of the bar
                const width = el.width

                // set the x when drawing the background. value is current x value subtracted by the width divided by 2
                const x = el.x - (width / 2)

                // draw the background
                ctx.fillStyle = 'rgba(229,229,229,.5)',
                ctx.fillRect(x, top, width, height)
            }
        }
    }
    
    const bookingChart = new Chart(chartEl, {
        type: 'bar',
        data: {
            datasets,
        },
        options: {
            interaction: {
                intersect: false,
                axis: 'x',
                mode: 'index',
            },
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    enabled: false
                },
                legend: {
                    display: false,
                },
                datalabels: {
                    anchor: 'top',
                    align: 'bottom',
                    textAlign: 'center',
                    labels: {
                        value: {
                            color: 'rgb(33, 150, 243)',
                        },
                        title: {
                            font: {
                                weight: 'bold',
                            },
                        },
                    },
                    formatter: (val) => val.y || '',
                },
            },
            scales: {
                y: {
                    display: false,
                },
                x: {
                    ticks: {
                        font: {
                            size: 10,
                        },
                        maxRotation: 0,
                        color: '#6A7383'
                    },
                    grid: {
                        color: '#EBEEF1',
                        borderColor: '#EBEEF1'
                    }
                },
            },
        },
        plugins: [ChartDataLabels, hoverBackground],
    });
    chartEl.on('click', function(e) {
        const activePoint = bookingChart.getElementsAtEventForMode(e, 'index', { intersect: false }, false)

        if(activePoint.length > 0) {
            const pointIndex = activePoint[0].index
            const {date, day, dayIndex} = bookingChart.data.datasets[0].data[pointIndex]
            displayCurrentBookings(data, date, day, dayIndex)
        }
    })
}

function revenueResize() {
    drawRevenue(chartData.revenue);
}

function drawRevenue(data) {

    if(!data) return;

    var grandTotal = 0;

    const dataSetConfig = {
        radius: 0,
        hoverRadius: 0,
        fill: true,
        borderWidth: 0,
    };

    let datasets = Array.from({ length: 4 }, () => ({ ...dataSetConfig, data: [] }));

    // Draw chart data in reverse (so the chart starts with the 1st week being 12 weeks ago, and the last week being the current period)...
    reverseForIn(data, function (key) {
        var obj = this[key];
        grandTotal = grandTotal + Number(obj.totalPayments);
        const period = obj.startDate;

        var dataSetValues = [
            {
                label: t('Other'),
                data: Number(obj.otherPayments).toFixed(2),
            },
            {
                label: t('One-time eftpos / credit card'),
                data: Number(obj.eftposPayments).toFixed(2),
            },
            {
                label: t('Cash'),
                data: Number(obj.cashPayments).toFixed(2),
            },
            {
                label: t('Recurring Payments'),
                data: Number(obj.billingPayments).toFixed(2),
            },
        ];

        datasets = datasets.map(({ data, label, ...prop }, index) => {
            data.push({ x: period, y: dataSetValues[index].data, grandTotal: Number(obj.totalPayments) });
            const borderColor = chartColors[index].border;

            return {
                ...prop,
                data,
                label: dataSetValues[index].label,
                borderColor,
                backgroundColor: createChartGradient,
                tension: 0.3,
                borderWidth: 1,
            };
        });
    });

    grandTotal = grandTotal.toFixed(2);

    // charts js replacement
    const chartEl = $('#revenue_chart');
    const chartElLabels = $('#revenue_chart_labels');
    const checkActive = Chart.getChart('revenue_chart');
    const checkActiveLabels = Chart.getChart('revenue_chart_labels');
    if (checkActive !== undefined) {
        checkActive.destroy();
        checkActiveLabels.destroy();
    }

    const yLine = {
        id: 'yLine',
        afterDraw: chart => {
            const {
                tooltip
            } = chart;

            if(tooltip._active && tooltip._active.length) {
                const {
                    ctx,
                    chartArea: { left, right, top, bottom },
                    scales: { y },
                } = chart;
                ctx.save();
                const activePoint = tooltip._active[0];
                // draw the vertical line chartjs
                ctx.beginPath();
                ctx.setLineDash([5, 5]);
                ctx.moveTo(activePoint.element.x, top);
                ctx.lineTo(activePoint.element.x, bottom);
                ctx.lineWidth = 1;
                ctx.strokeStyle = 'rgba(179,9,60,.7)';
                ctx.stroke();
                ctx.restore();
            }
        }
    }

    new Chart(chartEl, {
        type: 'line',
        data: {
            datasets,
        },
        plugins: [yLine],
        options: {
            layout: {
                padding: {
                    top: 10,
                }
            },
            interaction: {
                intersect: false,
                axis: 'x',
                mode: 'index',
            },
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false,
                },
                tooltip: {
                    callbacks: {
                        footer: (tooltipItems) =>
                            `${t('Grand Total')}: ${new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: clubCurrency,
                                currencyDisplay: 'narrowSymbol',
                            }).format(tooltipItems[0].raw.grandTotal)}`,
                        title: (context) => context[0].label + ' '+t('period'),
                        label: function (context) {
                            let label = context.dataset.label || '';

                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += new Intl.NumberFormat('en-US', { style: 'currency', currency: clubCurrency }).format(
                                    context.parsed.y
                                );
                            }
                            return label;
                        },
                    },
                    // place tooltip on top of the chart
                    position: 'nearest',
                    ...tooltipUI,
                },
            },
            scales: {
                y: {
                    min: 0,
                    ticks: {
                        callback: function (value, index, ticks) {
                            return index % 2
                                ? ''
                                : new Intl.NumberFormat('en-US', {
                                      style: 'currency',
                                      currency: clubCurrency,
                                      currencyDisplay: 'narrowSymbol',
                                  }).format(value);
                        },
                        font: {
                            size: 10,
                        },
                        color: '#6A7383',
                        display: window.innerWidth < 600 ? false : true,
                    },
                    stacked: true,
                    grid: {
                        color: 'rgba(228, 239, 245, 0.35)',
                        borderColor: 'rgba(228, 239, 245, 35)',
                        drawBorder: window.innerWidth < 600 ? false : true,
                    },
                    afterFit: () => {
                        setTimeout(() => {
                            $('.the-info-box__chart-section').scrollLeft(1600);
                        }, 500)
                    }
                },
                x: {
                    ticks: {
                        callback: function (value, index, ticks) {
                            const labelValue = this.getLabelForValue(value);
                            const result = index % 2 ? labelValue : '';
                            return result;
                        },
                        font: {
                            size: 10,
                        },
                        maxRotation: 0,
                        color: '#6A7383'
                    },
                    grid: {
                        color: 'rgba(228, 239, 245, 0.35)',
                        borderColor: 'rgba(228, 239, 245, 35)',
                    }
                },
            },
        },
    });

    new Chart(chartElLabels, {
        type: 'line',
        data: {
            datasets,
        },
        options: {
            interaction: {
                intersect: false,
                axis: 'x',
                mode: 'index',
            },
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false,
                },
                tooltip: {
                    callbacks: {
                        footer: (tooltipItems) =>
                            `${t('Grand Total')}: ${new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: clubCurrency,
                                currencyDisplay: 'narrowSymbol',
                            }).format(tooltipItems[0].raw.grandTotal)}`,
                        title: (context) => context[0].label + ' '+t('period'),
                        label: function (context) {
                            let label = context.dataset.label || '';

                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += new Intl.NumberFormat('en-US', { style: 'currency', currency: clubCurrency }).format(
                                    context.parsed.y
                                );
                            }
                            return label;
                        },
                    },
                },
            },
            scales: {
                y: {
                    min: 0,
                    ticks: {
                        callback: function (value, index, ticks) {
                            return index % 2
                                ? ''
                                : new Intl.NumberFormat('en-US', {
                                      style: 'currency',
                                      currency: clubCurrency,
                                      currencyDisplay: 'narrowSymbol',
                                  }).format(value);
                        },
                        font: {
                            size: 10,
                        },
                        color: '#6A7383'
                    },
                    stacked: true,
                    grid: {
                        color: '#EBEEF1',
                        borderColor: '#EBEEF1'
                    },
                    afterFit: ctx => {
                        $('.the-info-box__chart-wrapper--labels').css('width', ctx.width + 10 + 'px');
                    }
                },
                x: {
                    ticks: {
                        callback: function (value, index, ticks) {
                            const labelValue = this.getLabelForValue(value);
                            const result = index % 2 ? labelValue : '';
                            return result;
                        },
                        font: {
                            size: 10,
                        },
                        maxRotation: 0,
                        color: '#6A7383'
                    },
                    grid: {
                        color: '#EBEEF1',
                        borderColor: '#EBEEF1'
                    }
                },
            },
        },
    });

    // $(window).off('resize', revenueResize);
    // $(window).on('resize', revenueResize);
}
function drawMemberSplit(males, females, avgAge) {
    toggleLoading($('.members-split__wrapper'), false);
    // Convert male/female to percentage (one decimal)...
    var malePercent = Math.round((males / (males + females)) * 100 * 10) / 10;
    var femalePercent = Math.round((females / (females + males)) * 100 * 10) / 10;

    var memberSplitText = '';
    if (malePercent > femalePercent) {
        memberSplitText = `${malePercent}% ${t('Male')}`;
    } else {
        memberSplitText = `${femalePercent}% ${t('Female')}`;
    }

    $('#member_split_legend').html(`
        <p class="members-split-legend-values"><span class="members-split-legend__name use-skeleton">${t('Gender Split')}:</span> <span class="members-split-legend__value use-skeleton">${memberSplitText}</span></p>
        <p class="members-split-legend-values"><span class="members-split-legend__name use-skeleton">${t('Avg Age')}:</span> <span class="members-split-legend__value use-skeleton">${avgAge} ${t('y/old')}</span></p>
        <p class="members-split-legend-wrap">
            <span class="members-split-legend members-split-legend--male use-skeleton">${t('Male')}</span>
            <span class="members-split-legend members-split-legend--female use-skeleton">${t('Female')}</span>
        </p>
    `);

    const chartEl = $('#member_split_chart');
    const hoverLabel = {
        id: 'hoverLabel',
        afterDraw(chart, args, options) {
            const {
                ctx,
                chartArea: { width, height },
            } = chart;
            ctx.save();

            const isActive = chart._active.length > 0;
            const textLabel = isActive ? chart.config.data.labels[chart._active[0].index] : t('Female');
            const memberValue = isActive
                ? `${chart.config.data.datasets[0].data[chart._active[0].index]} ${t('members')}`
                : `${females} ${t('members')}`;
            const fillColor = isActive ? chart.config.data.datasets[0].borderColor[chart._active[0].index] : '#ff6c90';

            (ctx.font = '16px Arial'), (ctx.fillStyle = fillColor);
            ctx.textAlign = 'center';
            ctx.fillText(memberValue, width / 2, height / 1.8);

            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'black';
            ctx.fillText(textLabel, width / 2, height / 2.3);
        },
    };
    const checkActive = Chart.getChart('member_split_chart');
    if (checkActive !== undefined) {
        checkActive.destroy();
    }
    new Chart(chartEl, {
        type: 'doughnut',
        data: {
            labels: [t('Male'), t('Female')],
            datasets: [
                {
                    data: [males, females],
                    backgroundColor: ['rgba(33, 150, 243, .2)', 'rgba(255, 106, 183, .2)'],
                    borderColor: ['rgba(33, 150, 243, 1)', 'rgba(255, 106, 183, 1)'],
                    borderWidth: 1,
                    cutout: '70%',
                },
            ],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    onClick: () => null,
                    display: false
                },
                title: {
                    display: false,
                },
                tooltip: {
                    enabled: false,
                },
            },
        },
        plugins: [hoverLabel],
    });
}

function drawAppEngagementCharts(tcUsersByDate, trainUsersByDate) {
    var chartData = [];

    function formatDate(date) {
        return formatDateYMD(new Date(parseInt(date + '000')));
    }

    // Draw chart data in reverse (so the chart starts with the 1st week being 12 weeks ago, and the last week being the current period)...
    for (var key in tcUsersByDate) {
        if (tcUsersByDate[key].date) {
            chartData.push({
                date: formatDate(tcUsersByDate[key].date),
                total: Number(tcUsersByDate[key].count),
                tcUsers: Number(tcUsersByDate[key].count),
                trainUsers: Number(0),
            });
        }
    }

    for (var key in trainUsersByDate) {
        if (trainUsersByDate[key].date) {
            if (chartData.some((e) => e.date === formatDate(trainUsersByDate[key].date))) {
                // This date already exists, merge with other object...
                let obj = chartData.find((o) => o.date === formatDate(trainUsersByDate[key].date));
                obj.trainUsers = Number(trainUsersByDate[key].count);
                obj.total = Number(obj.total + trainUsersByDate[key].count);
            } else {
                chartData.push({
                    date: formatDate(trainUsersByDate[key].date),
                    total: Number(trainUsersByDate[key].count),
                    tcUsers: Number(0),
                    trainUsers: Number(trainUsersByDate[key].count),
                });
            }
        }
    }

    // Sort dates (they may not be in order, since we're merging two arrays...)
    chartData.sort(function (a, b) {
        return new Date(b.date) - new Date(a.date);
    });

    // Reverse dates...
    chartData = chartData.reverse();

    // Only put a maximum of 30 items on the bargraph...
    if (chartData.length > 30) chartData.length = 30;
}
function drawWidgetLoaders(loading = true) {
    $('.container-gm-api').show();
    $('.container-no-gm-api').hide();
    toggleLoading($('.w'), loading);
}

function hideRevenueGraph() {
    $('.revenue__wrapper').hide();
    $('.revenue__section').addClass('no-revenue-graph');
}

function loadDashboardRevenue(clubID) {

    let clubHasStripe = false;

    $('.revenue__wrapper').show();
    toggleLoading($('.revenue__wrapper'), true);

    let gmReq = new Promise((resolve, reject) => {
        $.ajax({
            url: `/api/reporting/gymmaster/revenuegraph/${clubID}`,
            method: 'GET',
            data: {
                range: widgetTimeRange,
            },
            success: function (data) {
                if (data && data !== false) {
                    // Draw charts...
                    $('#revenue_graph').empty();
                    chartData.revenue = data.revenue;
                    drawRevenue(data.revenue);
                }
                resolve()
            },
            error: function (xhr, textStatus, errorThrown) {
                console.error(xhr, textStatus, errorThrown);
                hideRevenueGraph();
                resolve();
            },
        });
    })

    let listIntegrations = new Promise((resolve, reject) => {
        $.ajax({
            url: `/api/clubs/integrations/list/${clubID}`,
            method: 'GET',
            success: function(data) {
                if(data) {
                    if(
                        data.stripeConnectedAccountID !== undefined &&
                        data.stripeConnectedAccountID !== null &&
                        data.stripeConnectedAccountID !== ""
                    ) clubHasStripe = true;
                }
                resolve();
            },
            error: function(xhr, textStatus, errorThrown){
                hideRevenueGraph();
                resolve();
            }
        });
    })

    Promise.all([listIntegrations, gmReq, getClubDetailsAPI(clubID)]).then(([inRes, gmRes, clubRes]) => {
        if (clubRes[0]) {
            const count = clubRes[1].failedPayments?.count ?? 0;
            $('.btn-failed-payments').html(`${count || 'View'} ${t('failed payments in the last 14 days')}`);
        }

        if(clubHasStripe) {
            $(".btn-revenue-history").removeClass("hidden");
            $(".btn-failed-payments").removeClass("hidden");
        } else {
            $(".btn-revenue-history:not([class*='hidden'])").addClass("hidden");
            $(".btn-failed-payments:not([class*='hidden'])").addClass("hidden");
        }
        toggleLoading($('.revenue__wrapper'), false);
    })

}

async function loadMyzoneAlert(clubID) {
    try {
        const data = await $.ajax({
            url: '/api/reporting/myzone/widgets/' + clubID,
            method: 'GET',
        });

        if (!data) return [false];
        const mzLevel = data.myzone?.level || null;
        if (!mzLevel || mzLevel >= 4) return [false];
        const alertContent = `
            <div class="custom-alert custom-alert--danger" style="" role="alert">
                <div class="custom-alert__content">
                    <div class="custom-alert__icon">
                        <i class="warning-icon custom-icon"></i>
                    </div>
                    <div class="custom-alert__body">
                        <h6>${t('Myzone Data Level Inadequate')}!</h6>
                        <p>
                            ${t(
                                'Your Myzone GDPR Agreement MUST be level 4 or higher in-order to integrate with the Training Camp & other UBX Member Services. Please update your Myzone Facility Configuration ASAP to avoid member issues with our digital products and apps.'
                            )}
                        </p>
                    </div>
                </div>
                <a target="_blank" href="https://l.myzone.org/b2b/how-to-comply-with-gdpr-using-myzone-a-guide-for-club-operators" class="btn btn-danger btn-outline custom-alert__btn waves-effect normal-case">${t('Change your facility to Level 4')}!</a>
            </div>
        `;
        return [true, alertContent];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false]
    }
}

async function loadLowDuressAlert() {
    const data = await listClubLowBatteryDuressAPI(selectedID);
    console.log('data', data)
    if (!data[0] || data[1].total < 1) return [false];

    const alertContent = `
        <div class="custom-alert custom-alert--warning" style="" role="alert">
            <div class="custom-alert__content">
                <div class="custom-alert__icon">
                    <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M56.1338 49.885L34.83 10.3225C33.32 7.5175 29.2975 7.5175 27.7863 10.3225L6.48377 49.885C6.15592 50.4939 5.99154 51.1775 6.0067 51.8689C6.02185 52.5603 6.21602 53.236 6.57025 53.83C6.92448 54.424 7.42666 54.916 8.02779 55.258C8.62891 55.6 9.30843 55.7803 10 55.7812H52.6113C53.3034 55.7813 53.9837 55.6018 54.5857 55.2603C55.1877 54.9188 55.6907 54.4269 56.0457 53.8327C56.4007 53.2385 56.5954 52.5624 56.6108 51.8705C56.6263 51.1785 56.4619 50.4944 56.1338 49.885ZM31.3088 49.6562C30.8143 49.6562 30.331 49.5096 29.9198 49.2349C29.5087 48.9602 29.1883 48.5698 28.9991 48.113C28.8099 47.6561 28.7603 47.1535 28.8568 46.6685C28.9533 46.1836 29.1914 45.7381 29.541 45.3885C29.8906 45.0388 30.3361 44.8008 30.821 44.7043C31.306 44.6078 31.8087 44.6573 32.2655 44.8465C32.7223 45.0358 33.1127 45.3562 33.3874 45.7673C33.6622 46.1784 33.8088 46.6618 33.8088 47.1562C33.8088 47.8193 33.5454 48.4552 33.0765 48.924C32.6077 49.3929 31.9718 49.6562 31.3088 49.6562ZM34.0238 24.5125L33.3063 39.7625C33.3063 40.2929 33.0956 40.8016 32.7205 41.1767C32.3454 41.5518 31.8367 41.7625 31.3063 41.7625C30.7758 41.7625 30.2671 41.5518 29.8921 41.1767C29.517 40.8016 29.3063 40.2929 29.3063 39.7625L28.5888 24.5187C28.5727 24.1545 28.63 23.7907 28.7575 23.4491C28.885 23.1075 29.0799 22.795 29.3307 22.5303C29.5815 22.2657 29.883 22.0542 30.2172 21.9085C30.5515 21.7629 30.9117 21.686 31.2763 21.6825H31.3025C31.6696 21.6823 32.0329 21.7565 32.3706 21.9006C32.7082 22.0447 33.0131 22.2557 33.267 22.5208C33.5208 22.786 33.7183 23.0999 33.8475 23.4435C33.9767 23.7871 34.035 24.1533 34.0188 24.52L34.0238 24.5125Z" fill="#ffc433"/>
                    </svg>
                </div>
                <div class="custom-alert__body">
                    <h6>${t('Low Battery Duress Buttons')}!</h6>
                    <p>
                        ${t(
                            `Your club's emergency duress buttons are running low on battery. To ensure the safety and effectiveness of these devices, please replace their batteries as soon as possible. Timely action is crucial to keep your facility safe and operational for both members and staff.`
                        )}
                    </p>
                </div>
            </div>
            <a href="/device-management?deviceType=duress" class="btn btn-warning btn-outline custom-alert__btn waves-effect normal-case">${t('Check devices')}!</a>
        </div>
    `;
    
    return [true, alertContent];
}

async function loadClubHolidayAnnouncement(sessionId) {
    const response = await listClubUnReviewedHolidaysAPI(selectedID);
    if (sessionId !== dashboardAlertSession) return [false, 'Session ID mismatch'];

    if (!response[0]) return response;

    const holidays = response[1].filter((holiday) =>
        moment().isSameOrBefore(moment(holiday.date * 1000))
    );

    if (holidays.length <= 0) return response;

    let holidaysText = `\"${holidays[0].name}\" on \"${moment(holidays[0].date * 1000).format(
        'MMMM DD, YYYY'
    )}\"`;

    if (holidays.length > 1) {
        holidaysText = `${holidaysText} and ${holidays.length - 1} other${
            holidays.length - 1 > 1 ? 's' : ''
        }`;
    }

    $('.dashboard-alerts-banner').append(`
        <div class="custom-alert" style="" role="alert">
            <div class="custom-alert__content">
                <div class="custom-alert__icon">
                    <i class="bell-icon custom-icon"></i>
                </div>
                <div class="custom-alert__body">
                    <h6>${t('Automatic Holiday Hours added to your Club')}</h6>
                    <p>${t(`Your Club's hours have been automatically updated to include the upcoming holidays`)}:</p>
                    <ul class="holidays">
                        ${holidays.map((holiday) => (`
                            <li>${holiday.name} (${moment(holiday.date * 1000).format('MMMM DD, YYYY')})</li>
                        `)).join('')}
                    </ul>
                    <p>${t(`Please review these hours in your Facility Details to ensure these updates are correct.`)}</p>
                </div>
            </div>
            <div class="d-flex gap-1">
                <a href="#" class="btn btn-primary custom-alert__btn waves-effect normal-case animate-pulse accept-auto-hours">${t('Accept Changes')}</a>
                <a href="/club-details?action=review-holidays" class="btn btn-outline btn-warning waves-effect normal-case">${t('Review/Edit')}</a>
            </div>
        </div>
    `);

    $('.accept-auto-hours').on('click', async (e) => {
        e.preventDefault();
        loadingOverlay('.dashboard-alerts-banner');
        $('.accept-auto-hours').removeClass('animate-pulse');
        const response = await reviewClubHolidaysAPI(selectedID);
        loadingOverlay('.dashboard-alerts-banner', false);

        if (!response) {
            return instantNotification('Failed to accept holiday hours. Please contact HQ for assistance.');
        }

        $('.dashboard-alerts-banner').html('');
    });

    return response;
}

/**
 * Renders an info box element for the dashboard
 * @param {Object} options - Configuration options for the info box
 * @param {string} options.id - The ID of the info box element
 * @param {string} options.iconColor - Color class for the icon wrapper (e.g. 'blue', 'green', 'pink', 'yellow', 'red')
 * @param {string} options.iconType - Type of icon to display (e.g. 'current-members', 'new-members', 'ubx-gym', 'on-hold', 'non-visiting')
 * @param {string} options.title - The title of the info box
 * @param {string} options.tooltip - Tooltip text to display on hover
 * @param {string} options.value - Main value to display in the info box
 * @param {string} options.subLabel - Label for the sub-content area
 * @param {string} options.subValueId - ID for the span element displaying sub-value
 * 
 * @returns {string} HTML string for the info box
 */
function renderInfoBox(options) {
    const {
        id,
        iconColor = 'blue',
        iconType,
        title,
        tooltip,
        value = '0',
        subLabel = '',
        subValueId = ''
    } = options

    return `
    <div class="w gm-api the-info-box isLoading ph-the-info-box" id="${id}">
        <div class="the-info-box__data">
            <div class="the-info-box__icon__wrapper the-info-box__icon__wrapper--stroked the-info-box__icon__wrapper--stroked--${iconColor} use-skeleton">
                <i class="the-info-box__icon the-info-box__icon--${iconType}">
                    ${getDashboardIcons(iconType)}
                </i> 
            </div>
            <div class="the-info-box__name__wrapper">
                <div class="the-info-box__name">
                    <h6>
                        <span class="use-skeleton isInlineBlock rounded">${title}</span>
                    </h6>
                    <i class="the-info-box__info" data-toggle="tooltip" data-placement="bottom"
                        title="${tooltip}">
                        <span class="use-skeleton use-skeleton--absolute rounded--full"></span>
                    </i>
                </div>
                <div class="the-info-box__content">
                    <p class="number use-skeleton isInlineBlock rounded">
                        ${value}
                    </p>
                    ${subLabel ? `
                    <span class="use-skeleton rounded isInlineBlock sub-content">
                        ${subLabel} <span id="${subValueId}"></span>
                    </span>
                    ` : ''}
                </div>
            </div>
        </div>
        <div class="icon percentage-stat__wrapper" data-toggle="tooltip" data-placement="bottom"
            title="${t('Current trend, based on the previous \'period\' compared to the current selected Reporting Range.')}">
            <span class="percentage-stat use-skeleton isInlineBlock rounded">
                <span>0(0%)</span>
            </span>
        </div>
    </div>`
}

function renderDashboardInfoBoxes() {
    const infoBoxesContainer = $('.the-info-box__row')
    if (!infoBoxesContainer.length) return
    
    infoBoxesContainer.empty()
    
    // Current members info box
    infoBoxesContainer.append(renderInfoBox({
        id: 'currentMembers',
        iconColor: 'blue',
        iconType: 'current-members',
        title: t('CURRENT MEMBERS'),
        tooltip: t('Total number of current active paying members'),
        value: '00',
        subLabel: t('Retention'),
        subValueId: 'memberRetention'
    }))
    
    // New members info box
    infoBoxesContainer.append(renderInfoBox({
        id: 'newMembers',
        iconColor: 'green',
        iconType: 'new-members',
        title: t('NEW MEMBERS'),
        tooltip: t('Number of members that have signed up to your club (For the period selected)'),
        value: '000',
        subLabel: t('Cancellations'),
        subValueId: 'cancellations'
    }))
    
    // Visits info box
    infoBoxesContainer.append(renderInfoBox({
        id: 'visits',
        iconColor: 'pink',
        iconType: 'ubx-gym',
        title: t('VISITS'),
        tooltip: t('Number of members that have visits your club (For the period selected)'),
        value: '0'
    }))
    
    // Members on hold info box
    infoBoxesContainer.append(renderInfoBox({
        id: 'membersOnHold',
        iconColor: 'yellow',
        iconType: 'on-hold',
        title: t('ON HOLD'),
        tooltip: t('Total number of memberships currently on hold'),
        value: '0'
    }))
    
    // Non visiting members info box
    infoBoxesContainer.append(renderInfoBox({
        id: 'notVisitingMembers',
        iconColor: 'red',
        iconType: 'non-visiting',
        title: t('NON VISITING'),
        tooltip: t('Number of members that have not visited your club (during the selected period).'),
        value: '0'
    }))
}

async function loadDashboardAlerts() {
    $('.dashboard-alerts-banner').html('');
    const sessionId = uuidv4();
    dashboardAlertSession = sessionId;

    const connectedAccountRes = await getStripeConnectedAccountDetailsAPI(selectedID);
    if (sessionId !== dashboardAlertSession) return;

    if (connectedAccountRes[0]) {
        const hasErrors = connectedAccountRequirementErrorsAlert(connectedAccountRes[1], '.dashboard-alerts-banner');
        if (hasErrors) return;
    }

    const [myzoneRes, lowDuress] = await Promise.all([
        loadMyzoneAlert(selectedID),
        loadLowDuressAlert(),
    ]);

    if (sessionId !== dashboardAlertSession) return;

    if (myzoneRes[0] || lowDuress[0]) {
        return $('.dashboard-alerts-banner').html([myzoneRes[1] ?? '', lowDuress[1] ?? ''].join(''))
    }

    const kycConfigRes = await showMissingKYCConfigAlert(dashboardAlertSession);
    if (sessionId !== dashboardAlertSession) return;
    if (kycConfigRes[0]) return $('.dashboard-alerts-banner').html(kycConfigRes[1]);

    loadClubHolidayAnnouncement(dashboardAlertSession);
}

async function loadDashboardWidgets(clubID) {
    // Reset any warnings...
    $('.members-split__wrapper').show();

    renderDashboardInfoBoxes()

    // Get any Myzone data...
    drawWidgetLoaders();
    getLeadEvents(clubID);
    
    toggleLoading($('.bookings__wrapper'), true);
    toggleLoading($('.review-management__wrapper'), true);
    toggleLoading($('.members-split__wrapper'), true);
    toggleLoading($('.revenue-outlook'), true);

    getSmartClubDataAPI(clubID).then((smartClubRes) => {
        if (!smartClubRes[0]) return;
        globalStore.smartClubData = smartClubRes[1];
    });

    const failedPaymentsRange = {
        from: moment().subtract(13, 'days').startOf('day').unix(),
        to: moment().unix(),
    }
    $('.btn-failed-payments').attr(
        'href',
        `/member-payments/payments?fromDate=${failedPaymentsRange.from}&toDate=${failedPaymentsRange.to}&filter=failed`
    )

    // social media link is treated as quiet route
    $('.social-media-calendar-btn').on('click', function (e) {
        e.preventDefault();
        const url = $(this).attr('href');
        sammy.quiet = false;
        sammy.setLocation(url);
    })

    var gmReq = $.ajax({
        url: '/api/reporting/gymmaster/widgets/' + clubID,
        method: 'GET',
        data: {
            range: widgetTimeRange,
        },
        success: function (data) {
            if (data && data !== false) {
                // Populate 'single widget' data (Top row)
                $('#currentMembers .number').html(formatNumberWithCommas(data.widgets.currentMembers));
                $('#currentMembers .percentage-stat').html(
                    returnPercentageStatHTML(data.widgets.currentMembers, data.widgets_previous.currentMembers)
                );

                $('#newMembers .number').html(formatNumberWithCommas(data.widgets.newMembers));
                $('#newMembers .percentage-stat').html(
                    returnPercentageStatHTML(data.widgets.newMembers, data.widgets_previous.newMembers)
                );

                $('#membersOnHold .number').html(formatNumberWithCommas(data.widgets.membersOnHold));
                $('#membersOnHold .percentage-stat').html(
                    returnPercentageStatHTML(data.widgets.membersOnHold, data.widgets_previous.membersOnHold, true)
                );

                $('#notVisitingMembers .number').html(formatNumberWithCommas(data.widgets.notVisitingMembers));
                $('#notVisitingMembers .percentage-stat').html(
                    returnPercentageStatHTML(
                        data.widgets.notVisitingMembers,
                        data.widgets_previous.notVisitingMembers,
                        true
                    )
                );

                $('#visits .number').html(formatNumberWithCommas(data.widgets.totalGrantedVisitsDuringPeriod));
                $('#visits .percentage-stat').html(
                    returnPercentageStatHTML(
                        data.widgets.totalGrantedVisitsDuringPeriod,
                        data.widgets_previous.totalGrantedVisitsDuringPeriod,
                    )
                );

                $('#member_split').empty();
                drawMemberSplit(data.widgets.maleMembers, data.widgets.femaleMembers, data.widgets.memberAvgAge);

                // Summary to right of chart
                const displayPeriod = humaniseDays(widgetTimeRange).trim()
                $('.display-period').html(displayPeriod);

                // $("#avgMemberLength").html(humaniseDays(data.widgets.avgMemberLength));
                $('#cancellations').html(data.widgets.cancellations + ' '+t('Members'));
                $('#memberRetention').html(`${data.widgets.memberRetention}%`);

                // Payments
                $('#totalPayments').html(clubCurrencySymbol + data.totalPaymentsV2.totalPayments.formatCurrency());
                $('#totalPaymentsAvg').html(clubCurrencySymbol + (data.totalPaymentsV2.weeklyAvg || 0).formatCurrency());

                if (data.avgYield == 0 || data.avgYield == null || data.avgYield === undefined) {
                    $('#avgYield').hide();
                } else {
                    $('#avgYield').show();
                    $('#avgYield').html(clubCurrencySymbol + data.avgYield.formatCurrency(2));
                }

                if (widgetTimeRange <= 7) {
                    $('.avg-weekly-payouts-container').hide();
                } else {
                    $('.avg-weekly-payouts-container').show();
                }
            }
            drawWidgetLoaders(false);
            toggleLoading($('.revenue-outlook'), false);
            $('.w.gm-api').LoadingOverlay('hide');
        },
        error: function (xhr, textStatus, errorThrown) {
            console.error(xhr, textStatus, errorThrown);
            // window.location.replace("/401.html");
            $('.w.gm-api').LoadingOverlay('hide');

            // Hide all GYM Master related Widgets...
            drawWidgetLoaders(false);
            toggleLoading($('.revenue-outlook'), false);
            $('.members-split__wrapper').hide();
            $('.container-gm-api').hide();
            $('.container-no-gm-api').show();
        },
    });

    function returnPercentageStatHTML(cur, prev, rev = false) {
        let currentChange = getPercentageChange(cur, prev);
        if (currentChange == 0 || Number.isNaN(currentChange)) {
            return `
                <span style="">
                    <!-- &#9632; -->
                    <span class="percentage-stat__val">${Math.abs(getDifferenceChange(cur, prev))}</span><span class="percentage-stat__percent">${Number.isNaN(currentChange) ? 0 : Math.abs(currentChange)}%</span>
                </span>`;
        } 
        else if (currentChange <= 0.01) {
            return `
                <span class="${rev == false ? 'is-down' : 'is-up'}">
                    <!-- &#x25BC; -->
                    <span class="percentage-stat__val">${Math.abs(getDifferenceChange(cur, prev))}</span><span class="percentage-stat__percent has-icon ${rev == false ? 'is-down-icon' : 'is-down-icon-alt'}">${Math.abs(currentChange)}%</span>
                </span>`;
        } 
        else {
            return `
                <span 
                class="${rev == false ? 'is-up' : 'is-down'}"
                >
                    <!-- &#x25B2; -->
                    <span class="percentage-stat__val">${getDifferenceChange(cur, prev)}</span><span class="percentage-stat__percent has-icon ${rev == false ? 'is-up-icon' : 'is-up-icon-alt'}">${Math.abs(currentChange)}%</span>
                </span>`;
        }
    }

    const loadSocialWidget = async () => {
        try {
            const [{ reviews, tags }] = await Promise.all([fetchClubReviews(clubID), fetchClubReviewsSettings(clubID)]);

            renderReviewsSummary(reviews);
            renderReviewsRatings(reviews);
            renderReviewsWithoutResponses(reviews);
            renderReviewsTags(tags);
            toggleLoading($('.review-management__wrapper'), false);
        } catch (error) {
            console.error(error);
            $('.dashboard-stat-list').html(t("<li>Club's digitial profile not set.</li>"));
        }

        $('.w.review-management__wrapper').LoadingOverlay('hide');
    };

    const renderReviewsRatings = (reviews) => {
        // from 1 year/365 days
        let from = moment().subtract(365, 'days');
        let to = from.clone().add(1, 'week');

        let datasets = [
            {
                radius: 0,
                hoverRadius: 0,
                fill: true,
                // borderColor: 'rgb(188, 131, 238)',
                // backgroundColor: 'rgba(188, 131, 238, 0.2)',
                borderColor: 'rgb(33, 150, 243)',
                backgroundColor: 'rgba(33, 150, 243, 0.2)',
                tension: 0,
                borderWidth: 1,
                data: [],
            },
        ];
        let counter = 0;
        while (to.clone().isSameOrBefore(moment())) {
            const totalReviews = reviews.filter((review) => {
                const reviewDate = moment(new Date(review.date));
                return from.clone().isSameOrBefore(reviewDate) && to.clone().isAfter(reviewDate);
            });

            const reviewsCount = totalReviews.length
            datasets[0].data.push({
                toolipContent: reviewsCount > 0
                ? `${moment(totalReviews[0].date).format('YYYY-MM-DD')}: ${reviewsCount} ${t(`Review${reviewsCount > 1 ? 's' : ''}`)}`
                : t(`No Reviews For Period`),
                x: counter.toString(),
                y: reviewsCount,
            });
            counter++

            from = to.clone();
            to = from.clone().add(1, 'week');
        }

        const chartEl = $('#reviews-ratings-graph');
        const checkActive = Chart.getChart('reviews-ratings-graph');
        if (checkActive !== undefined) {
            checkActive.destroy();
        }
       
        new Chart(chartEl, {
            type: 'line',
            data: {
                datasets,
            },
            options: {
                interaction: {
                    intersect: false,
                    axis: 'x',
                    mode: 'index',
                },
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: (context) => {
                                const { toolipContent } = context[0].raw
                                return toolipContent;
                            },
                            label: function () {
                                return '';
                            },
                        },
                        ...tooltipUI,
                    },
                },
                scales: {
                    y: {
                        min: 0,
                        ticks: {
                            display: false,
                            font: {
                                size: 10,
                            },
                        },
                        stepSize: 1,
                        grid: {
                            display: false,
                        }
                    },
                    x: {
                        ticks: {
                            display: false,
                        },
                        grid: {
                            display: false,
                        },
                    },
                },
            },
        });
    };

    const renderReviewsWithoutResponses = (reviews) => {
        const noResponseEl = $('.reviews-without-responses')

        // empty the container
        noResponseEl.html('');

        // get reviews without response
        const reviewsWithoutResponses = reviews.filter((review) => review.responses.length <= 0);

        // clear noResponse filter value in initialization
        localStorage.removeItem('noResponse')

        if (reviewsWithoutResponses.length > 0) {
            noResponseEl.html(`
                <label class="clickable">
                    <span>${t('Reviews Requiring Response')}</span> 
                    <div class="badge ${
                        reviewsWithoutResponses.length >= 10
                            ? 'badge-red'
                            : reviewsWithoutResponses.length >= 2
                            ? 'badge-green'
                            : ''
                    }">
                    <span>${reviewsWithoutResponses.length}</span>
                    <svg class="badge__icon" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M55.3419 44.0478L55.342 44.048C55.6852 44.6364 55.8672 45.3049 55.8696 45.9861C55.8721 46.6673 55.6949 47.3371 55.356 47.928C55.0171 48.5188 54.5284 49.01 53.9392 49.3518C53.35 49.6936 52.681 49.8741 51.9999 49.875H41.7975H41.6955L41.6751 49.975C41.2194 52.2046 40.0076 54.2085 38.2447 55.6476C36.4817 57.0867 34.2758 57.8727 32 57.8727C29.7243 57.8727 27.5184 57.0867 25.7554 55.6476C23.9925 54.2085 22.7807 52.2046 22.325 49.975L22.3046 49.875H22.2025H12.0001C11.3187 49.8745 10.6495 49.6943 10.06 49.3527C9.47042 49.011 8.98139 48.52 8.64219 47.929C8.30299 47.338 8.12561 46.6681 8.12795 45.9867C8.13029 45.3053 8.31226 44.6366 8.65551 44.048L8.65564 44.0478C10.0577 41.6327 12.125 34.8489 12.125 26C12.125 20.7288 14.219 15.6735 17.9463 11.9463C21.6736 8.21897 26.7289 6.125 32 6.125C37.2712 6.125 42.3265 8.21897 46.0538 11.9463C49.7811 15.6735 51.875 20.7288 51.875 26C51.875 34.8464 53.9399 41.6327 55.3419 44.0478ZM26.345 49.875H26.1683L26.2272 50.0417C26.6493 51.2355 27.431 52.2692 28.4648 53.0005C29.4987 53.7317 30.7337 54.1246 32 54.125H32.0001C33.2664 54.1246 34.5014 53.7317 35.5352 53.0005C36.569 52.2692 37.3508 51.2355 37.7729 50.0417L37.8318 49.875H37.655H26.345ZM11.892 45.9372L11.7827 46.125H12H52H52.2174L52.1081 45.9371C50.1929 42.6452 48.125 34.9993 48.125 26C48.125 21.7234 46.4262 17.6219 43.4021 14.5979C40.3781 11.5739 36.2766 9.875 32 9.875C27.7234 9.875 23.622 11.5739 20.5979 14.5979C17.5739 17.6219 15.875 21.7234 15.875 26C15.875 35.0068 13.8021 42.6527 11.892 45.9372Z" fill="currentColor" stroke="currentColor" stroke-width="0.25"/></svg>
                    </div>
                </label>
            `);

            // add a click event 
            noResponseEl.find('label').on('click', function() {
                // set localStorage value for review management initial filter data
                localStorage.setItem('noResponse', 'true');
                // redirect to reviews page
                sammy.setLocation('/review-management');
            })
        }
    };

    const renderReviewsTags = (tags) => {
        $('.reviews-tag-cloud > span').unbind();
        $('.reviews-tag-cloud').html('');

        tags.map((tag) => {
            $('.reviews-tag-cloud').append(`
                <span class="badge badge-green-outline">
                    <span>${tag.tag}</span>
                    <span class="count">(${tag.count})</span>
                </span>
            `);
        });

        $('.reviews-tag-cloud > span').click(function () {
            defaultSelectedReviewTag = $(this).find('span').html();

            firstLoad = true;
            sammy.quiet = false;
            sammy.setLocation('/review-management');
            firstLoad = false;
        });
    };

    const renderReviewsSummary = (reviews) => {
        const sources = {};
        const keys = {
            'maps.google.com': 'GOOGLE MY BUSINESS',
            'facebook.com': 'CLUB FACEBOOK PAGE',
        };

        reviews.forEach(({ source, rating, content, ...item }) => {
            const key = keys[source] || source;

            if (!sources[key]) {
                sources[key] = { ratings: [], reviews: [], source };
            }

            sources[key].reviews.push(content);
            sources[key].ratings.push(rating);
        });

        $('.dashboard-stat-list.reviews-summary').html('');

        if (reviews.length > 0) {
            
            Object.keys(sources).sort().forEach((name) => {
                const { reviews, ratings } = sources[name];
                const isFB = name === 'FACEBOOK'
                const aveRating = !isFB ? 
                    ratings.reduce((sum, rating) => {
                        const rateVal = typeof rating === 'undefined' ? 0 : rating;
                        return sum + rateVal;
                    }, 0) / ratings.length : reviews.length;
                const reviewLabels = isFB ? t('reviews') : t('reviewers')

                $('.dashboard-stat-list.reviews-summary').append(
                    `<li>
                        <span class="dashboard-stat-list__title">${name}</span>
                        <div class="dashboard-stat-list__values">
                            ${!isFB ? `<span><b>${aveRating.toFixed(1)}</b> <span>${t('rating')}</span></span>` : ''}
                            <span><b>${reviews.length}</b> <span>${reviewLabels}</span></span>
                        </div>
                    </li>`
                );
            });
        } else {
            $('.review-management__wrapper .dashboard-stat-list').html(`<li>${t('No reviews yet.')}</li>`);
        }
    };

    loadSocialWidget();

    /*
    var appEngagement = $.ajax({
        url: '/api/reporting/apps/widgets/' + clubID,
        method: 'GET',
        success: function(data){
            if(data && data !== false) {

                // Key 'singlestat' trainingcamp metrics...
                $("#tc-user-count").html(data.trainingcamp.totalUsers.toLocaleString());

                // Engagement very slow - for now removing till we build better reporting...
                //$("#tc-engaged-count").html(data.app.engagedMembers.toLocaleString());
                $("#train-user-count").html(data.trainathome.totalUsers.toLocaleString()); 

                // Sorts response signup metrics dates into ascending order (oldest dates first, newest dates last)
                var tcUsersByDate = data.trainingcamp.usersByDate.sort(function(a, b) {
                    return parseFloat(a.date) - parseFloat(b.date);
                });
                var trainUsersByDate = data.trainathome.usersByDate.sort(function(a, b) {
                    return parseFloat(a.date) - parseFloat(b.date);
                });

                drawAppEngagementCharts(tcUsersByDate, trainUsersByDate);

            }

            $('.w.trainingcamp').LoadingOverlay("hide");

        },
        error: function(xhr, textStatus, errorThrown){
            console.error(xhr, textStatus, errorThrown);
            $('.w.trainingcamp').LoadingOverlay("hide");
        }
    });
    */

    // get workout bookings
    $.ajax({
        url: `/api/reporting/clubs/${clubID}/workout-bookings`,
        method: 'GET',
        success: (data) => {
            drawWorkoutBookingsChart(data);

            displayCurrentBookings(data)

            // $('.w.bookings__wrapper').LoadingOverlay('hide');
            toggleLoading($('.bookings__wrapper'), false);
        },
        error: function (xhr, textStatus, errorThrown) {
            console.error(xhr, textStatus, errorThrown);
            // $('.w.bookings__wrapper').LoadingOverlay('hide');
            toggleLoading($('.bookings__wrapper'), false);
            $('.peak-time__wrapper').hide();
            console.log({ peakTimeError: errorThrown })
        },
    });

    fetchClubBusyHours((hours) => initHoursChart(hours, renderBusyHoursChart))

}

/** fetch busy hours **/
const fetchClubBusyHours = async (renderChart) => {
    $('.peak-time__wrapper').show();
    toggleLoading($('.peak-time__wrapper'), true);

    /** make sure the current day is active tab **/
    let dayText = moment().format("dddd");
    $('.popular-times-tab li').removeClass('active');
    $('.popular-times-tab .tab-pane').removeClass('active');
    $(`#popular-times-tab-${dayText.toLowerCase()}`).addClass('active');
    $(`#${dayText.toLowerCase()}-tab-container`).addClass('active');

    const result = await uniApiConCall(`/api/club-busy-hours/${selectedID}`);
    $('.w.club-popular-times').LoadingOverlay('hide');
    if (result && 'status' in result && result.status == 'success') {
        if (
            'data' in result
            && result.data
            && 'busyHours' in result.data
            && Object.keys(result.data.busyHours).length
        ) {
            $('#popular-times-widget').removeClass('hidden');
            $('#busy-hours-chart').removeClass('hidden');

            /**
             * @TODO
             * remove this we won't be needing this anymore
             */
            const busyHours = result.data.busyHours
            // for (const key in busyHours) {
            //     const theDay = key;
            //     const hoursAndPercent = busyHours[key];
            //     chartFunc(hoursAndPercent, `popular_times_chart_${theDay}`, theDay);
            //     hoursData.push({
            //         day: theDay,
            //         percentages: hoursAndPercent
            //     })
            // }
            /**
             * call the chart function with all the hours and percentage
             */
            renderChart(busyHours)
        } else {
            $('#popular-times-widget').addClass('hidden');
            $('#busy-hours-chart').addClass('hidden');
            $('.peak-time__wrapper').hide();
        }
    }
}

function setDashboardNoClubs() {
    $('.member-container').hide();
    $('.no-clubs-associated-container').show();

    $(".no-clubs-nav-options .s-store").on('click', function () {
        sammy.quiet = false;
        sammy.setLocation('/store-ui');
    })

    $(".no-clubs-nav-options .s-wiki").on('click', function () {
        sammy.quiet = false;
        sammy.setLocation('/wiki');
    })

    $(".no-clubs-nav-options .s-support").on('click', function () {
        sammy.quiet = false;
        sammy.setLocation('/support');
    })
    
    
    // Bind to the 'linking' form
    $('form[name=linking-request] button').on('click', function () {
        $('.w.linking-request').LoadingOverlay('show', {
            image: '/assets/images/widget-loader-solid.svg',
        });
        var $inputs = $('form[name=linking-request] :input');

        // not sure if you wanted this, but I thought I'd add it.
        // get an associative array of just the values.
        var formValues = {};
        $inputs.each(function () {
            formValues[this.name] = $(this).val();
        });

        var linnkingRequest = $.ajax({
            url: '/api/user/linkclub',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formValues),
            success: function (data) {
                $('.w.linking-request').LoadingOverlay('hide');
                $('form[name=linking-request]').html(
                    "<center style='padding:50px;'><h4>"+t('Thank you, an Account Manager will review your request and be in touch.')+"</h4></center>"
                );
            },
            error: function (xhr, textStatus, errorThrown) {
                console.error(xhr, textStatus, errorThrown);
                $('.w.linking-request').LoadingOverlay('hide');
                $('form[name=linking-request]').html(
                    "<center style='padding:50px;'><h4 style='color: red'>"+t('An error occurred while processing your request. Please contact your account manager to have your account linked')+"</h4></center>"
                );
            },
        });
    });
}

/** draw chart busy hours **/
const initHoursChart = (hoursData = {}, renderCallback) => {
    //local moment instance to avoid changing global moment locale
    const localMoment = moment()
    // get default day
    let currentDay = localMoment.locale('en').format('dddd').toLowerCase()

    // get days as keys
    const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    // render the the days options
    const dayNav = $('.peak-time-nav')
    dayNav.show()
    let dayNavButtons = ""
    days.map(day => {
        dayNavButtons += `<li><button data-day="${day}" class="${day === currentDay ? 'active' : ''}" type="button">${t(day.slice(0,3))}</button></li>`
        return day
    })
    dayNav.html(dayNavButtons)

    const navBtns = dayNav.find('button')
    navBtns.each(function() {
        $(this).click(function() {
            // toggle active class
            navBtns.removeClass('active')
            $(this).addClass('active')

            // render the chart
            const day = $(this).data('day')
            const {data, labels} = getDataHours(hoursData, day)
            renderCallback(data, labels)
        })
    })

    // get data and labels
    const getDataHours = (data = {}, day = day) => {
        const format12Hour = (hr) => {
            if(typeof hr === 'undefined') return
            return moment().hour(hr).format('ha')
        }
    
        
        const defaultLabels = ["9am", "10am", "11am", "12pm", "1pm", "2pm", "3pm", "4pm", "5pm"];
        const defaultData = Array.from({length: defaultLabels.length}, (_, i) => ({
            y: 0,
            x: defaultLabels[i]
        }));
        const dataVal = data[day].map(item => ({x: format12Hour(item.hour), y: item.percentBusy}))
        const labelsVal = data[day].map(item => format12Hour(item.hour))

        return {
            data: dataVal.length ? dataVal : defaultData,
            labels: labelsVal.length ? labelsVal : defaultLabels,
        }
    }

    // render the chart
    const {data, labels} = getDataHours(hoursData, currentDay)
    renderCallback(data, labels)
}

const renderBusyHoursChart = (data, labels) => {

    toggleLoading($('.peak-time__wrapper'), false);

    // default dataset config
    const defaultDataConfig = {
        backgroundColor: 'rgba(33, 150, 243, .2)',
        borderColor: 'rgb(33, 150, 243)',
        borderWidth: 1,
    }

    /**
     * fill bar for 0 percentage
     */
    const zeroPercentageIndexes = data.map((item, index) => {
        if(item && item.y === 0) return {annotIndex: index}
        return undefined
    }).filter((item) => typeof item !== 'undefined');
    const annotationRange = [];
    let annotations = {}

    if (zeroPercentageIndexes.length) {
        let groupings = [];
        zeroPercentageIndexes.forEach((zeroIndex, startIndex) => {
            let start = zeroIndex.annotIndex;
            let end = zeroIndex.annotIndex + 1;

            /** filter the start or end if already in groupings **/
            if (groupings.includes(end) || groupings.includes(start)) {
                return;
            }

            /** the counter as index checker **/
            let counter = zeroIndex.annotIndex + 1
            zeroPercentageIndexes.forEach((z, endIndex) => {
                if (startIndex === endIndex) return;

                /**
                 * If counter is equal to the index, assign it to "end" variable
                 * then save it to "groupings" variable
                 **/
                if (counter === z.annotIndex) {
                    end = z.annotIndex;
                    groupings.push(end);
                    counter++;
                }
            })
            annotationRange.push({start, end});
        });
    }

    const defaultBoxAnnotation = {
        adjustScaleRange: true,
        drawTime: 'afterDatasetsDraw',
        type: 'box',
        backgroundColor: 'rgba(255, 106, 183, .03)',
        borderColor: 'rgba(0, 0, 0, 0)',
    }

    const defaultLabelUnstaffedAnnotation = {
        type: 'label',
        content: [t('CLUB'), t('CLOSED')],
        font: {
            size: 0,
        },
        color: 'rgba(255, 99, 132, 0.8)',
    }

    /** populate the range in xMin and xMax **/
    if (annotationRange.length) {
        let boxAnnotations = {}
        annotationRange.forEach((range, index) => {
            let startingRangeIndex = range.start -.5;
            let endingRangeIndex = range.end +.5;

            /** This is to make sure that no overlapping of annotation box **/
            if (data[range.end] && data[range.end].y > 0) {
                endingRangeIndex = range.end -.5;
            }

            boxAnnotations[`boxUnstaffed${(index+1)}`] = {
                ...defaultBoxAnnotation,
                xMin: startingRangeIndex,
                xMax: endingRangeIndex,
            }

            /** the label annotation, for wide area only **/
            if ((endingRangeIndex - startingRangeIndex) > 3) {
                const diffIndex = endingRangeIndex - startingRangeIndex;
                const offsetIndex = diffIndex / 2
                const xPosition = startingRangeIndex + offsetIndex;
                boxAnnotations[`labelUnstaffed${(index+1)}`] = {
                    type: 'label',
                    xValue: xPosition,
                    content: [t('CLUB'), t('CLOSED')],
                    font: {
                        size: 10,
                        weight: 'bold'
                    },
                    color: 'rgba(255, 99, 132, 0.8)',
                }
            }

            boxAnnotations = {...boxAnnotations};
        });

        annotations = {...annotations, ...boxAnnotations};
    } else {
        
        annotations['boxUnstaffed'] = {
            ...defaultBoxAnnotation,
            xMin: -0.5,
            xMax: 9,
        }
        annotations['labelUnstaffed'] = {
            ...defaultLabelUnstaffedAnnotation,
            type: 'label',
            content: ['CLUB', 'CLOSED'],
            font: {
                size: 9,
            },
            color: 'rgba(255, 99, 132, 0.8)',
            xValue: 4,
        }
    }

    // initial dataset display
    const datasets = [
        {
            ...defaultDataConfig,
            data, 
            labels
        },
    ];

    // reset chart
    const chartEl = $('#peak-time-chart');
    const checkActive = Chart.getChart('peak-time-chart');
    if (checkActive !== undefined) {
        chartEl.unbind();
        checkActive.destroy();
    }
    new Chart(chartEl, {
        type: 'bar',
        data: {
            datasets,
        },
        options: {
            interaction: {
                intersect: false,
                axis: 'x',
                mode: 'index',
            },
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `${context.raw.y}%`
                        },
                    },
                    position: 'nearest',
                    ...tooltipUI,
                },
                annotation: {
                    annotations,
                },
                legend: {
                    display: false,
                },
                datalabels: {
                    anchor: 'top',
                    align: 'bottom',
                    textAlign: 'center',
                    labels: {
                        value: {
                            color: 'rgb(33, 150, 243)',
                        },
                        title: {
                            font: {
                                weight: 'bold',
                            },
                        },
                    },
                    formatter: (val) => val.y || '',
                },
            },
            scales: {
                y: {
                    display: false,
                    beginAtZero: true,
                    min: 0,
                    max: 100,
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 10,
                        },
                        maxRotation: 0,
                    },
                },
            },
        },
    });
}

$.sDashboard = {}
$.sDashboard.base = {
    deviceFilters: {
        reportingRange: {   
            text: 'Fortnight',
            value: 14
        },
    },
    deviceOptions: {
        reportingRange: [
            {
                text: 'Day',
                value: 1,
            },
            {   
                text: 'Week',
                value: 7
            },
            {   
                text: 'Fortnight',
                value: 14
            },
            {   
                text: '28 Days',
                value: 28
            },
            {   
                text: 'Last 2 months',
                value: 60
            },
            {   
                text: 'Last 6 months',
                value: 180
            },
            {   
                text: 'Last 12 months',
                value: 365
            },
        ],
    },
    selectedFilter: function (event) {
        const target = $(event.target);
        
        $.sDevices.base.filterUpdate(target, function() {
            const timeRange = $.sDashboard.base.deviceFilters.reportingRange.value;
            widgetTimeRange = timeRange;
            localStorage.setItem('dashboardTimerange', timeRange);
            loadDashboardWidgets(selectedID);
            loadDashboardRevenue(selectedID);
        }, $.sDashboard.base.deviceOptions, $.sDashboard.base.deviceFilters);
    },
    init: function () {

        const enableFilter = () => {
            if($.sDevices && $('#filter-dashboard-range .filter__options').length > 0) {
                // filter updates
                $(window).on('click', $.sDashboard.base.selectedFilter);
    
                const activeFilter = $.sDashboard.base.deviceOptions.reportingRange.find((item) => item.value == widgetTimeRange);
                
                $.sDashboard.base.deviceFilters.reportingRange = activeFilter;
    
                $.sDevices.base.renderOptions('reportingRange', $('#filter-dashboard-range .filter__options'), $.sDashboard.base.deviceOptions, $.sDashboard.base.deviceFilters);
    
                return;
            }
    
            setTimeout(() => {
                enableFilter();
    
            }, 1000);
        }

        enableFilter();

        const getMemberRecords = () => {
            // trigger init memberPayments to get the value of the failed payments
            if(typeof initMemberPayments === 'function') {
                initMemberPayments();
                return;
            }

            setTimeout(() => {
                getMemberRecords();
            }, 1000);
        }

        // Set the selectedClubObj to globalStore
        globalStore.selectedClubObj = selectedClubObj;

        
        
    }
}

function displaySmartClub(smartClubData = globalStore.smartClubData) {
    const wrapper = $('.smart-club__wrapper');
    wrapper.show();
    toggleLoading(wrapper, true);

    if(!smartClubData || smartClubData.length === 0) {
        wrapper.hide();
        return;
    }

    // update the content of the smart facility
    const smartClubContent = $('.smart-club__values');
    smartClubContent.empty();

    smartClubData.map(item => {
        const { name, type, value, valueLabel, needRecharge, onlineQty, slug, noValueMessage, size, isOffline } = item;

        smartClubContent.append(`
        <li>
            <${slug === '#' ? 'div' : `a href="${slug}"`} class="smart-club__value__details">
                <span class="smart-club__values-icon__wrapper use-skeleton rounded">
                    <i class="smart-club__values-icon custom-icon ${type}-icon"></i>
                </span>
                <div>
                    <p class="smart-club__label">
                        <span class="use-skeleton rounded">${t(name)}</span>
                    </p>
                    <p class="smart-club__value ${(!value || value === 'Offline') ? 'smart-club__value--fatal' : ''}">
                        <span class="use-skeleton rounded">
                            ${value
                                ? `${type === 'temp'
                                    ? `${formatTemperature(value, globalStore.activeUnitOfMeasurement)}`
                                    : type === 'audio'
                                        ? t(value)
                                        : `${value} ${t(valueLabel)}`}`
                                : t(noValueMessage)}
                        </span>

                        ${type === 'audio' ? `
                        <span class="custom-badge custom-badge--success normal-case use-skeleton" data-toggle="tooltip" data-placement="bottom" title="${t('Your Sonos device integration to the Performance Hub is currently controlled via')} ${valueLabel}. ${t('This has been automatically selected based on your network configuration.')}">
                            ${valueLabel}
                        </span>` : ''}

                        ${onlineQty > 0 ? `
                            <span class="custom-badge custom-badge--success normal-case use-skeleton">
                                ${onlineQty} ${t('Online')}
                            </span>
                        ` : ``}

                        ${needRecharge ? `
                        <span class="custom-badge custom-badge--fatal normal-case use-skeleton">
                        ${needRecharge} ${t('Device(s) need recharging')}
                        </span>` : ''}

                        ${type === 'screen' && isOffline && isOffline > 0 && value < 12 ? `
                        <span class="custom-badge custom-badge--fatal normal-case use-skeleton">
                            ${isOffline} ${t("devices offline")}
                        </span>` : ''}

                        ${type === 'screen' && !isOffline ?
                            `<span class="custom-badge custom-badge--success normal-case use-skeleton">
                                ${t("all devices ok")}
                            </span>` : ''
                        }


                    </p>
                    ${
                        size ? 
                            `
                            <p class="smart-club__size" style="font-size: 12px;">
                                <span class="smart-club__size-value use-skeleton rounded">
                                    ${size} ${t('Footage stored')}
                                </span>
                            </p>
                            ` : ''
                    }
                </div>
            </${slug === '#' ? 'div' : 'a'}>
        </li>
        `);
    })

    // trigger the tooltup
    smartClubContent.find('[data-toggle="tooltip"]').tooltip(); // Restore simple initialization

    // remove loading
    setTimeout(() => {
        toggleLoading(wrapper, false);
    }, 500);
}

const leadEventState = useProxyState({}, (target, key, value) => {
    target[key] = value;
    displayLeadEvents(leadEventState.data);
})

function displayLeadEvents(leadEventsData) {

    const chartEl = $('#lead-events-data-chart');
    const checkActive = Chart.getChart('lead-events-data-chart');
    if (checkActive !== undefined) {
        chartEl.unbind();
        checkActive.destroy();
    }

    const chartData = Object.entries(leadEventsData).sort((a, b) => {
        return b[1] - a[1]
    }).map( arr => ({
        label: arr[0],
        value: arr[1]
    })).splice(0,10)
    
    const colorsRgb = [
        '190, 59, 27',
        '130, 0, 149',
        '231, 152, 32',
        '72, 102, 199',
        '85, 149, 45',
        '86, 152, 195',
        '191, 70, 117',
        '191, 70, 85',
        '191, 70, 70',
        '70, 191, 97',
    ]

    const totalEvents = chartData.reduce( (prev, curr) => {
        if(!prev) return curr.value

        return prev + curr.value
    },0);
    const labels = chartData.map( obj => obj.label );
    const dataValues = chartData.map( obj => obj.value );

    const data = {
        labels,
        datasets: [{
          data: dataValues,
          backgroundColor: chartData.map((_, i) => `rgba(${colorsRgb[i]}, .3)`),
          hoverBorderColor: chartData.map((_, i) => `rgb(${colorsRgb[i]})`),
          hoverOffset: 10,
          borderWidth: 1
        }],
      };

    new Chart(chartEl, {
        type: 'pie',
        data: data,
        // disable legends
        options: {
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    position: 'nearest',
                    ...tooltipUI,
                }
            },
            layout: {
                padding: 10
            },
        }

    });

    // create custom legends
    const legendEl = $('.lead-events-data__values');
    legendEl.empty();
    
    chartData.slice(0, 3).map( (obj, i) => {
        legendEl.append(`
        <li class="lead-events-data__values-grp">
            <span class="lead-events-data__values-grp__pill use-skeleton rounded--full">${((obj.value / totalEvents) * 100).toFixed(2)}%</span>
            <span class="lead-events-data__values-grp__label use-skeleton rounded">${t(`${obj.label}`)}</span>
            <strong class="use-skeleton rounded">${obj.value}</strong>
        </li>
        `)
    })

    if(chartData.length > 3) {
        const totalOthers = chartData.reduce( (prev, curr, i) => {
            if(i < 3) return prev
            return prev + curr.value
        },0);

        legendEl.append(`
        <li class="lead-events-data__values-grp">
            <span class="lead-events-data__values-grp__pill use-skeleton rounded--full">${((totalOthers / totalEvents) * 100).toFixed(2)}%</span>
            <span class="lead-events-data__values-grp__label use-skeleton rounded">${t('Others')}</span>
            <strong class="use-skeleton rounded">${totalOthers}</strong>
        </li>
        `) 
    }


    // update the total events display
    $('.lead-events-data__count').text(totalEvents)
}

// lead events
async function getLeadEvents(selectedID) {

    const leadEventsWrapperEl = $('.lead-events__wrapper');

    leadEventsWrapperEl.removeClass('is-hidden');
    
    const { value } = $.sDashboard.base.deviceFilters.reportingRange;
    
    const toDate = moment().format('YYYY-MM-DD');
    const fromDate = moment(toDate).subtract(value, 'days').format('YYYY-MM-DD');

    try {
        const data = await fetch(
                `/api/clubs/${selectedID}/lead-events/?fromDate=${fromDate}&toDate=${toDate}`
            ).then(
            res => res.json()
        );

        if(!data || Object.keys(data).length === 0) {
            leadEventsWrapperEl.addClass('is-hidden');
            return data
        } 

        leadEventState.data = data;
        
        return data

    } catch (error) {
        console.log({ error })
        leadEventsWrapperEl.addClass('is-hidden');
    }
}

