function sGlobalSchedule() {

    function getEncodedTimezone() {
        try {
            // Attempt to get the user's timezone identifier from the Intl API
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Replace slashes with %2F to match the desired format
            const encodedTimezone = timezone.replace(/\//g, '%2F');

            return encodedTimezone;
        } catch (error) {
            // Log the error for debugging purposes (optional)
            console.error("Error retrieving the timezone: ", error);

            // Return an empty string to indicate failure
            return "";
        }
    }

    function loadCalendar() {
        try {
            // Default calendar URL
            var calendarURL = 'https://calendar.google.com/calendar/embed?src=c_3165f206925e9980d085ea08d431a6329457468dedbafa365732d8d3cbd53748%40group.calendar.google.com';
            
            // Attempt to get the encoded timezone
            var encodedTimezone = getEncodedTimezone();

            // If a valid encoded timezone is retrieved, append it to the URL
            if (encodedTimezone) {
                calendarURL += `&ctz=${encodedTimezone}`;
            }

            // Set the iframe source
            $("#calendarEmbed").attr('src', calendarURL);

            // Show loading overlay
            loadingOverlay('.iframe-placeholder');

            // Hide the loading overlay and show the iframe after it is ready
            $('#calendarEmbed').on('load', function() {
                $("#calendarEmbed").show();
                $('.iframe-placeholder').LoadingOverlay("hide");
            });

        } catch (error) {
            // Log the error for debugging purposes (optional)
            console.error("Error loading the calendar: ", error);

            // Fallback to default calendar URL without timezone if an error occurs
            $("#calendarEmbed").attr('src', 'https://calendar.google.com/calendar/embed?src=c_3165f206925e9980d085ea08d431a6329457468dedbafa365732d8d3cbd53748%40group.calendar.google.com');
            
            // Show loading overlay
            loadingOverlay('.iframe-placeholder');

            // Hide the loading overlay and show the iframe after it is ready
            $('#calendarEmbed').on('load', function() {
                $("#calendarEmbed").show();
                $('.iframe-placeholder').LoadingOverlay("hide");
            });
        }

        setTimeout(function(){
            $("#calendarEmbed").show();
            $('.iframe-placeholder').LoadingOverlay("hide");
        }, 8000);

    }
    loadCalendar();

}
