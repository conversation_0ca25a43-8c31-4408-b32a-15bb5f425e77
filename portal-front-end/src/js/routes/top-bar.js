let hasChangedClub = false;

$(() => {
    $('.change-org-btn').on('click', changeOrgDialog);

    $(document).on('keydown', (event) => {
        // Check if 'O' is pressed along with the Meta key (CMD on Mac) or Ctrl key
        if (event.keyCode === 79 && (event.metaKey || event.ctrlKey)) {
            // Check if currently focused element is an input or a textarea (if it is we don't want t fire shortcuts)
            const activeElement = document.activeElement.tagName.toLowerCase();

            if (
                (activeElement !== 'input' && activeElement !== 'textarea') ||
                event.target.classList.contains('aa-Input')
            ) {
                event.preventDefault();
                changeOrgDialog();
            }
        }
    });
});

function changeOrgDialog() {
    const html = document.createElement('div');
    $(html).addClass('ph-dialog-v2 body');

    html.innerHTML = selectpicker({
        name: 'organisation',
        options:
            signedInUserData?.organisations.map((org) => ({
                label: org.name,
                value: org.organisationId,
            })) || [],
        value: selectedOrgID,
    });

    $(html).find('select').selectpicker();

    async function preConfirm() {
        const newOrgID = $(html).find('select[name="organisation"]').val();
        createCookie('ActiveOrg', newOrgID, 1080);
        getAuthedClubs(newOrgID);
    }

    swalWithBootstrapButtons.fire({
        html,
        title: t('Change Organisation'),
        showConfirmButton: true,
        showCancelButton: true,
        showCloseButton: true,
        confirmButtonText: t('Proceed'),
        cancelButtonText: t('Cancel'),
        onOpen: () => {
            makeSwalDraggable();
            $(html).find('select[name="organisation"]').trigger('focus');
        },
        preConfirm,
    });
}

function renderProfileDropdown() {
    $('.profile-dropdown-container').html(`
    <button type="button" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      <i class="material-icons">account_circle</i>
    </button>
    ${profileDropdownContent()}
  `);

    $('.profile-dropdown-container-mobile').html(`
    <button type="button" style="font-size: 24px;">
      <i class="fa fa-right-left" aria-hidden="true" style="transform: translateY(-4px);"></i>
    </button>
  `);

    $('.profile-dropdown-container-mobile button').click(() => {
        toggleNotificationBar(false);
        openProfileDialog();
    });
}

function openProfileDialog() {
    const html = document.createElement('div');
    $(html).addClass('profile-dialog');

    let selectedOrgItem = '';
    if (!$('.change-org-btn').parent().hasClass('hidden')) {
        const orgName = signedInUserData.organisations.find((org) => org.organisationId === selectedOrgID)?.name;
        selectedOrgItem = `
            <a class="gap-1 justify-between align-center mobile-org-spoof-btn">
                <div class="d-flex align-center" style="gap: 0.5rem;">
                    <i class="material-icons">domain</i>
                    <span>${t('Organisation')}</span>
                </div>
                <div class="d-flex gap-1 align-end">
                    <span>${orgName}</span>
                    <i class="material-icons" style="font-size: 12px;">arrow_forward_ios</i>
                </div>

            </a>
        `;
    }

    let selectClubItem = '';
    if (ClubsObj.length > 1) {
        const name = _get(selectedClubObj, 'name', selectedID);
        const country = _get(selectedClubObj, 'localisation.country.iso_code', 'global').toUpperCase();
        const flag = `<img style='height: 20px; width: 20px;' src='/assets/images/flags/${country}.svg'>`;
        selectClubItem = `
            <a class="gap-1 justify-between align-center mobile-club-spoof-btn">
                <div class="d-flex align-center" style="gap: 0.5rem;">
                <i class="material-icons">work</i>
                <span>${t('Facility')}</span>
                </div>

                <div class="d-flex gap-1">
                <span>${name}</span>
                <span>${flag}</span>
                </div>
            </a>
        `;
    }

    const roleBadge = signedInUserData?.isGlobalAdmin ? `<span class="text-danger">${t('Super Admin')}</span>` : '';

    $(html).html(`
        <div class="profile-dropdown">
        <div class="d-flex justify-between align-center gap-1">
            <div class="d-flex gap-1">
            <i class="material-icons" style="font-size: 40px;">account_circle</i>
            <div style="transform: translateY(5px); text-align: left;">
                <div class="signed-in-user">${signedinUser}</div>
                ${roleBadge}
            </div>
            </div>

            <button class="btn btn-square btn-icon close-btn">
            <i id="notif-bar-close" class="fa fa-close" aria-hidden="true"></i>
            </button>
        </div>

        <hr class="my-1">
        <div class="profile-dropdown-actions">
            ${selectedOrgItem}
            ${selectClubItem}
            <a onclick="openUserPreferencesDialog()">
                <i class="material-icons">settings<span class="hidden"></span></i>
                <span>${t('Settings')}</span>
            </a>
            <a title="Sign-out of the Performance Hub." href="/oauth2/sign_out">
                <i class="material-icons">exit_to_app</i>
                <span>${t('Logout')}</span>
            </a>
        </div>
        </div>
    `);

    $(html).find('.close-btn').click(Swal.close);
    $(html).find('.mobile-club-spoof-btn').on('click', selectClubDialog);
    $(html).find('.mobile-org-spoof-btn').on('click', selectOrgDialog);
    swalWithBootstrapButtons.fire({ html, showConfirmButton: false });
}

function profileDropdownContent() {
    return `
        <div class="profile-dropdown dropdown-menu pull-right">
        <div class="d-flex gap-1" style="padding-right: 3rem;">
            <i class="material-icons" style="font-size: 40px;">account_circle</i>
            <div style="transform: translateY(5px);">
                <div class="signed-in-user"></div>
                <div class="text-danger signed-in-user-role" style="line-height: 14px;"></div>
            </div>
        </div>
        <hr class="my-1">
        <div class="profile-dropdown-actions">
            <a onclick="openUserPreferencesDialog()">
                <i class="material-icons">settings<span class="hidden"></span></i>
                <span>${t('Settings')}</span>
            </a>
            <a title="Sign-out of the Performance Hub." href="/oauth2/sign_out">
                <i class="material-icons">exit_to_app</i>
                <span>${t('Logout')}</span>
            </a>
        </div>
        </div>
    `;
}

function renderNotificationBtn() {
    $('.navbar-notifications-container').html(`
        <a href="javascript:void(0);" onclick="toggleNotificationBar()" role="button" aria-expanded="true">
        <i class="notif-bell material-icons">notifications<span></span></i>
        <span class="label-count"></span>
        <i class="notif-bell-spinner material-icons">sync</i>
        </a>
    `);
}

function selectClubDialog() {
    let filter = '';

    const html = document.createElement('div');
    $(html).addClass('ph-dialog spoof-dialog');
    $(html).html(`
        <button class="btn btn-square btn-icon close-btn"><i class="fa fa-close"></i></button>
        <fieldset>
        <legend>${t('Select Facility')}</legend>
        ${textField({ name: 'club', required: true, type: 'search' })}
        <div class="options-container">
            <ul class="options"></ul>
        </div>
        </fieldset>
    `);

    function getAddress(club) {
        return [_get(club, 'localisation.province.full_name'), _get(club, 'localisation.country.full_name')]
            .filter(Boolean)
            .join(', ');
    }

    function renderClubOptions() {
        const filteredClubs = ClubsObj.filter((club) => {
            const address = getAddress(club);
            const matchesName = new RegExp(filter, 'i').test(club.name);
            const matchesAddress = new RegExp(filter, 'i').test(address);
            return matchesName || matchesAddress;
        });
        // .filter((club, index) => index < 10);

        $(html).find('.options').html('');
        filteredClubs.forEach((club) => {
            const country = _get(club, 'localisation.country.iso_code', 'global').toUpperCase();
            const flag = `<img style='height: 20px; width: 20px;' src='/assets/images/flags/${country}.svg'>`;
            const address = getAddress(club);
            const selected = club.id === selectedID ? `<i class="material-icons">check</i>` : '';

            $(html).find('.options').append(`
                <li class="club-item" data-club-id="${club.id}">
                    <div class="d-flex gap-2 justify-between align-center">
                        <div class="d-flex gap-2">
                            <span>${flag}</span>
                            <span>
                                <h5 class="m-0">${club.name}</h5>
                                <small>${address}</small>
                            </span>
                        </div>
                        ${selected}
                    </div>
                </li>
            `);
        });

        if (filteredClubs.length === 0) {
            $(html).find('.options').append('<li class="text-center">No clubs found.</li>');
        }

        $(html)
            .find('.options .club-item')
            .on('click', (e) => {
                const { clubId } = $(e.currentTarget).data();
                handleChangeClub(clubId);
                closeDialog();
            });
    }

    $(html)
        .find('input[name="club"]')
        .on('input', (e) => {
            filter = e.target.value;
            renderClubOptions();
        });

    $(html).find('.close-btn').on('click', closeDialog);

    function closeDialog() {
        $(html).closest('.swal2-container').addClass('closing');
        setTimeout(Swal.close, 301);
    }

    renderClubOptions();
    swalWithBootstrapButtons.fire({
        html,
        showConfirmButton: false,
        onOpen: () => {
            $(html).find('input[name="club"]').trigger('focus');
        },
    });
}

function selectOrgDialog() {
    let filter = '';

    const html = document.createElement('div');
    $(html).addClass('ph-dialog spoof-dialog');
    $(html).html(`
        <button class="btn btn-square btn-icon close-btn">
            <i class="fa fa-close"></i>
        </button>
        <fieldset>
            <legend>${t('Select Organisation')}</legend>
            ${textField({ name: 'org', required: true, type: 'search' })}
            <div class="options-container">
                <ul class="options"></ul>
            </div>
        </fieldset>
    `);

    function renderOrgOptions() {
        const filteredOrgs = signedInUserData?.organisations.filter((org) => {
            return new RegExp(filter, 'i').test(org.name);
        });

        $(html).find('.options').html('');
        filteredOrgs.forEach((org) => {
            const selected = org.organisationId === selectedOrgID ? `<i class="material-icons">check</i>` : '';

            $(html).find('.options').append(`
                <li class="org-item" data-org-id="${org.organisationId}">
                    <div class="d-flex gap-3 justify-between align-center">
                        <h5 class="m-0">${org.name}</h5>
                        ${selected}
                    </div>
                </li>
            `);
        });

        if (filteredOrgs.length === 0) {
            $(html).find('.options').append('<li class="text-center">No organisations found.</li>');
        }

        $(html)
            .find('.options .org-item')
            .on('click', (e) => {
                const { orgId } = $(e.currentTarget).data();
                createCookie('ActiveOrg', orgId, 1080);
                getAuthedClubs(orgId);
                closeDialog();
            });
    }

    $(html)
        .find('input[name="org"]')
        .on('input', (e) => {
            filter = e.target.value;
            renderOrgOptions();
        });

    $(html).find('.close-btn').on('click', closeDialog);

    function closeDialog() {
        $(html).closest('.swal2-container').addClass('closing');
        setTimeout(Swal.close, 301);
    }

    renderOrgOptions();
    swalWithBootstrapButtons.fire({
        html,
        showConfirmButton: false,
        onOpen: () => {
            $(html).find('input[name="org"]').trigger('focus');
        },
    });
}

function handleChangeClub(clubID, fireEvents = true) {
    const foundClub = signedInUserData?.clubs.find((club) => club.id === clubID);
    if (!foundClub) return;

    prevSelectedID = selectedID;
    selectedID = clubID;
    selectedOrgID = foundClub.organisationId ?? '';
    selectedClubObj = foundClub;
    globalStore.selectedClubObj = selectedClubObj;
    clubCurrency = selectedClubObj ? selectedClubObj.localisation.currency.currency : 'USD';
    clubCurrencySymbol = selectedClubObj ? selectedClubObj.localisation.currency.symbol : '$';
    selectedBrand = _get(foundClub, 'brand', '').toLowerCase();
    selectedPhone = _get(foundClub, 'phone', '');
    gymMasterDomain = _get(foundClub, 'gymMasterDomain', '');

    // refresh ui
    $(`select.club-spoof-select`).val(clubID);
    $('select.club-spoof-select').selectpicker('refresh');

    if (!selectedClubObj.redirectDashboard || selectedClubObj.redirectDashboard === '/dashboard') {
        globallyAllowedRoutes.push('/dashboard');
    } else {
        globallyAllowedRoutes = globallyAllowedRoutes.filter((route) => route !== '/dashboard');

        if (window.location.pathname === '/dashboard') {
            sammy.quiet = false;
            sammy.setLocation(selectedClubObj.redirectDashboard);
        }
    }

    if (fireEvents) {
        hasChangedClub = location.pathname.includes('device-management');
        hasChangedClub && clearDeviceFilters();
    }

    // cookies
    createCookie('ActiveClub', selectedID, 1080);
    createCookie('ActiveOrg', selectedOrgID, 1080);

    fireEvents && fireClubSelectEvents(selectedID, gymMasterDomain);
    fetchSideBarNotifications(selectedID);
    changeClubRoom();

    // overdue agreements don't need to be checked on every club change
    // because it's already checked on the first load for the user
    isClubAccessRestrictedByBilling();
    loadAlgolia();
}

function clearDeviceFilters() {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete('deviceType');

    const newUrl = `${location.pathname}${searchParams.size ? `?${searchParams.toString()}` : ''}`;

    firstLoad = true;
    sammy.quiet = false;
    sammy.setLocation(newUrl);
    firstLoad = false;
}
