let secondsAfterLoad = 0;
let doneLoading = false;
let createdBoards = false;

const WEKAN_URL = '/kanban/api/wekanstatus';
function setDefaultRoute() {
    $('#kanbanContainer').attr('src', `/kanban/entrance?club=${selectedID}`);
}
function setInternalRoute(internalRoute) {
    let newRoute = `/kanban/${internalRoute}`;
    $('#kanbanContainer').attr('src', newRoute);
}
function skanban() {
    loadingOverlay('.iframe-placeholder');
    $('#kanbanWaitMessage').hide();
    let kanbanFrame = $('#kanbanContainer').get(0);
    
    //this will trigger an onmessage event inside kanban iframe
    $('#kanbanOverlay').on('click', function () {
        kanbanFrame.contentWindow.postMessage('closeCard', '*');
    });
    window.onmessage = function (e) {
        if (e.data == 'doneLoading') {
            doneLoading = true;
            $('#kanbanWaitMessage').hide();

            $('.iframe-placeholder iframe').show();
            $('.iframe-placeholder').LoadingOverlay('hide');
        } else if (e.data == 'modalShow') {
            $('#kanbanOverlay').show();
            $('#kanbanContent .iframe-placeholder').parent().addClass('iframe-placeholder-zindexOn');
        } else if (e.data == 'modalHide') {
            $('#kanbanOverlay').hide();
            $('#kanbanContent .iframe-placeholder').parent().removeClass('iframe-placeholder-zindexOn');
        }
    };
    function incrementSeconds() {
        secondsAfterLoad += 1; //check every 5 seconds
        if (secondsAfterLoad % 5 == 0) {
            $.ajax({
                url: WEKAN_URL,
                method: 'GET',
                success: function (res) {
                    // console.log('Done getting wekan status ', res);
                    if (res) {
                        if (res.wekanStatus == 'done') {
                            //stop the timer
                            clearInterval(callInterval);
                            $('#kanbanWaitMessage').hide();
                            $('.iframe-placeholder iframe').show();
                            $('.iframe-placeholder').LoadingOverlay('hide');
                            //if it was previously created boards
    
                          if (createdBoards) {
                              console.log('Reloading iframe..')
                              createdBoards = false;
                              let kanbanFrame = $('#kanbanContainer').get(0);

                              //reload the iframe, not the page
                              kanbanFrame.contentWindow.location.reload(true);
                            }
                        } else if (res.wekanStatus == 'creating boards') {
                            //set the flag to true
                            createdBoards = true;
                            $('.iframe-placeholder').LoadingOverlay('hide');
                            $('#kanbanWaitMessage').show();
                            $('#kanbanWaitMessageText').html('Setting you up for the first time...');
                        } else if (res.wekanStatus == 'error') {
                            $('#kanbanWaitMessage').show();
                            $('#kanbanWaitMessageText').html('Sorry, something went wrong.');
                            //hide the spinner
                            $('.iframe-placeholder').LoadingOverlay('hide');
                        }
                    }
                },
                error: function (xhr, textStatus, errorThrown) {
                    //console.error(xhr, textStatus, errorThrown);
                    console.log('Error while getting wekan status', textStatus);
                    $('#kanbanWaitMessage').show();
                    $('#kanbanWaitMessageText').html('Sorry, something went wrong.');
                    //hide the spinner
                    $('.iframe-placeholder').LoadingOverlay('hide');
                },
            });
        }
    }
    let callInterval = setInterval(incrementSeconds, 1000);
}
