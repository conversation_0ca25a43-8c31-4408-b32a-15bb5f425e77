function initClubKYC() {
    let clubKYCData = null;
    let connectedAccount = null;
    let addressAutoInputLsr = null;
    let addressAutoInput = null;

    async function init() {
        $('.stripe-details-card').html('');
        toggleLoading($('.kyc-stripe-details-card'), true);
        toggleLoading($('.kyc-business-details-card, .kyc-contact-details-card'), true);

        const [stripeRes, kycRes] = await Promise.all([
            getStripeConnectedAccountDetailsAPI(selectedID),
            getClubKycAPI(selectedID),
        ]);

        toggleLoading($('.kyc-stripe-details-card'), false);
        toggleLoading($('.kyc-business-details-card, .kyc-contact-details-card'), false);

        if (stripeRes[0]) {
            connectedAccount = stripeRes[1];
            renderStripeKYCSection();
        } else {
            handleNoStripe();
        }

        if (kycRes[0]) {
            clubKYCData = kycRes[1];
            renderBusinessDetailsCard();
            renderContactDetailsCard();
        } else {
            $('.business-details-card').html(
                `<em class="ph-text text-danger">Failed to fetch data. Please try again later.</em>`
            );
        }
    }

    function renderProvidedBadge(isProvided) {
        const badgeClass = isProvided ? 'badge-green' : 'badge-red';
        const icon = isProvided ? 'done_all' : 'block';
        const providedText = isProvided ? 'Provided' : 'Not Provided';

        // TODO: Add translation on csv
        return `<div class="ph-badge ${badgeClass} ph-badge--borderless d-flex flex-gap-1"><i class="material-icons">${icon}</i>${t(
            t(providedText)
        )}</div>`;
    }

    function handleNoStripe() {
        toggleLoading($('.kyc-stripe-details-card'), false);
        return $('.stripe-details-card').html(`
            <div class="d-flex flex-column align-center justify-center p-4" style="height: 250px;">
                <h3>${t('Your Club does not appear to have integration to the UBX Payment Processor at this time')}</h3>
                <p>
                    ${t(
                        'If you are not yet setup for payment processing with UBX Global, please complete the on-boarding process.'
                    )}
                </p>
                <p>
                    ${t(
                        'If you are setup with UBX Payment Processing, and no longer have access, the UBX Payment Processor may currently be offline - Please try again later.'
                    )}
                </p>
                <p>
                    <a target="_blank" href="https://forms.monday.com/forms/b97c7544ed249a7e63fae27403edf426?r=use1">${t(
                        'Begin on-boarding for UBX Global Payment Processing'
                    )}</a>
                </p>
            </div>
        `);
    }

    function renderContactDetailsCard() {
        const contacts = _get(clubKYCData, 'contactDetails', []);
        const emptyContactsText = `
                <div class="ph-alert_danger m-0 ph-alert">
                    <div class="ph-alert__title">
                        <i class="material-icons" style="font-size: 16px;">warning</i>
                        ${t('Your club does not have any Named Contacts (Owners or Staff) listed.')}
                    </div>
                    <div class="ph-alert__description">
                        ${t(
                            "We're not able to provide support or disclose any information to Owners or Staff that are not listed"
                        )}.
                    </div>
                </div>
            `;

        $('.contact-details-card').html(`
            ${cardTitle(t('Owners and Staff'), ['add'])}
            ${
                contacts.length > 0
                    ? `<div class="ph-card_body d-flex flex-wrap flex-row gap-2 ph-card_contacts">${contacts
                          .map(renderContact)
                          .join('')}</div>`
                    : `<div class="d-flex justify-center align-center p-8">${emptyContactsText}</div>`
            }
            </div>
        `);

        $('.contact-details-card .add-button').on('click', () => {
            sammy.setLocation(`/club-kyc/contact-details/new`);
        });

        $('.contact-details-card .edit-contact-btn').on('click', (e) => {
            const id = $(e.currentTarget).closest('.contact-card').data('id');
            sammy.setLocation(`/club-kyc/contact-details/${id}`);
        });
    }

    function renderContact(contact) {
        const birthday = moment(contact.birthday, 'YYYY-MM-DD').format('MMMM DD, YYYY');
        const birthdayText = contact.birthday ? `Born on ${birthday}` : '';
        const other = _get(contact, 'other', '');
        const ids = _get(contact, 'ids', []);
        const hasId = ids.length > 0;

        return `
            <div class="contact-card ph-contact-card" data-id="${contact.id}">
                <div class="ph-contact-card_header">
                    <div class="ph-contact-card_name_wrapper">
                        <h5 class="__name">${contact.name}</h5>
                        <div class="clickable edit-contact-btn __icon_edit">
                            <i class="material-icons" style="font-size: 14px;">edit</i>
                        </div>
                    </div>
                    <div class="d-flex flex-row flex-gap-2">
                        <div class="__role ph-badge ph-badge--xsmall ph-badge--borderless">${contact.role || ''}</div>
                        ${
                            contact.useAsSaasBillingContact == true
                                ? `<div class="ph-badge badge-blue ph-badge--xsmall ph-badge--borderless"><i class="fas fa-user-shield" aria-hidden="true"></i> &nbsp; ${t(
                                      'Nominated Saas Billing Contact'
                                  )}</div>`
                                : ''
                        }
                    </div>
                </div>
                <div class="ph-contact-card_info">
                    <div class="d-flex flex-column flex-gap-1">
                        ${
                            contact.email
                                ? `
                            <div class="ph-contact-card_info_item">
                                <i class="material-icons">email</i>
                                <span>${contact.email || ''}</span>
                            </div>
                        `
                                : ''
                        }
                        ${
                            contact.phone
                                ? `
                            <div class="ph-contact-card_info_item">
                                <i class="material-icons">phone</i>
                                <span>${
                                    contact.phone ? `<a href="tel:${contact.phone}">${contact.phone}</a>` : ''
                                }</span>
                            </div>
                        `
                                : ''
                        }
                        ${
                            birthdayText
                                ? `
                            <div class="ph-contact-card_info_item">
                                <i class="material-icons">cake</i>
                                <span>${birthdayText}</span>
                            </div>
                        `
                                : ''
                        }
                        ${
                            contact.address
                                ? `
                            <div class="ph-contact-card_info_item">
                                <i class="material-icons">location_on</i>
                                <span>${contact.address || ''}</span>
                            </div>
                        `
                                : ''
                        }
                    </div>
                    <div class="d-flex flex-column __card_info-other flex-gap-2">
                        <div class="d-flex flex-column align-items-start">
                            <strong class="mb-1">${t('Identity Document')}</strong>
                            ${renderProvidedBadge(hasId)}
                        </div>
                        ${
                            other
                                ? `
                                    <div>
                                        <strong class="mb-1">${t('Other information provided')}</strong>
                                        <div class="ph-quote-block">${other}</div>
                                    </div>
                                `
                                : ''
                        }
                    </div>
                </div>
            </div>
        `;
    }

    function getDefaultKYCAddress() {
        const address = clubKYCData?.businessDetails?.address || connectedAccount?.company?.address || {};
        return [address.line1, address.line2, address.city, address.state, address.postalCode, address.country]
            .filter((a) => a)
            .join(', ');
    }

    function renderBusinessDetailsCard(edit = false) {
        let country = clubKYCData.businessDetails.country;
        country = country.iso_code ? country : { iso_code: 'GLO', full_name: 'GLOBAL' };

        let countryIcon = '';

        if (country.iso_code !== 'GLO') {
            countryIcon = `
                <span class='country-flag'>
                    <img style='height: 20px; width: 20px; transform: translateY(-2.5px);' src='/assets/images/flags/${country.iso_code.toUpperCase()}.svg'>
                </span>
            `;
        }

        let regCert = _get(clubKYCData, 'businessDetails.regCert', null);

        const companyNumberLabel =
            country.iso_code === 'AU'
                ? t('Australian Business Number')
                : country.iso_code === 'GB'
                ? t('Companies House Registration Number')
                : country.iso_code === 'SG'
                ? t('Unique Entity Number')
                : country.iso_code === 'NZ'
                ? t('New Zealand Business Number')
                : t('Company Number');

        const companyNumberHelperText =
            country.iso_code === 'AU'
                ? t(
                      '11-digit <a target="_blank" href="https://asic.gov.au/for-business/registering-a-company/steps-to-register-a-company/australian-business-number">Australian Business Number</a>.<br>Don\'t have one yet? <a target="_blank" href="https://register.business.gov.au">Apply online</a>.'
                  )
                : country.iso_code === 'GB'
                ? t(
                      'We only need the connected account\'s 7 or 8-digit <a target="_blank" href="https://online.hmrc.gov.uk/information/help?helpcategory=help&affinitygroup=unknown&helpid=crn">Company Number</a>.<br>Don\'t have one yet? <a target="_blank" href="https://ewf.companieshouse.gov.uk/runpage?page=webfilingRegister">Apply online</a>.'
                  )
                : country.iso_code === 'IE'
                ? t(
                      '6-digit company number. Don\'t have one yet? <a target="_blank" href="https://www.cro.ie/Publications/Business-Names-Forms">Apply online</a>.'
                  )
                : country.iso_code === 'NZ'
                ? t(
                      'We only need the connected account\'s <a target="_blank" href="https://www.nzbn.govt.nz/about-nzbn/what-are-nzbns">New Zealand Business Number</a>.<br>Don\'t have one yet?<a target="_blank" href="https://www.nzbn.govt.nz/get-an-nzbn/get-your-nzbn">Learn more</a>'
                  )
                : '';

        const rows1 = [
            {
                label: t('Legal Business Name'),
                key: 'legalName',
                info: t(
                    'The name you provide must exactly match the name associated with your tax ID/Business Registration Certificate.'
                ),
                value: _get(clubKYCData, 'businessDetails.legalName', ''),
                input: 'text',
                placeholder: `<em class="ph-text text-danger">${t('REQUIRED')}</em>`,
            },
            {
                label: t('Trading/Operation Name'),
                key: 'tradingName',
                value: _get(clubKYCData, 'businessDetails.tradingName', ''),
                input: 'text',
                placeholder: `<em class="ph-text text-danger">${t('REQUIRED')}</em>`,
            },
            {
                label: t('Business Registration Certificate'),
                key: 'reqCert',
                value: _get(regCert, 'originalname', ''),
                input: 'upload',
                placeholder: `<em class="ph-text text-danger">${t('REQUIRED')}</em>`,
                valueClass: 'view-asset clickable text-primary',
            },
        ];

        const rows2 = [
            {
                label: companyNumberLabel,
                key: 'companyNumber',
                value: _get(clubKYCData, 'businessDetails.companyNumber', ''),
                placeholder: `<em class="ph-text text-danger">${t('REQUIRED')}</em>`,
                input: 'text',
                helperText: companyNumberHelperText,
            },
            {
                label:
                    country.iso_code === 'US' || isEuropeanCountry(country.iso_code)
                        ? t('VAT Number')
                        : t('Tax Number'),
                key: 'taxNumber',
                value: _get(clubKYCData, 'businessDetails.taxNumber', 'N/A'),
                input: 'text',
                show: !['AU', 'NZ'].includes(country.iso_code),
            },
            {
                label: t('Country'),
                value: `${country.full_name} ${countryIcon}`,
            },
        ];

        const addressInput = {
            label: t('Business Address'),
            key: 'address',
            value: getDefaultKYCAddress(),
            input: 'textarea',
            placeholder: `<em class="ph-text text-danger">${t('REQUIRED')}</em>`,
        };

        $('.business-details-card').html(`
            ${cardTitle(t('Business Details'), edit ? ['cancel', 'save'] : ['edit'])}
            <form class="ph-card_body">
                <div class="">
                    <div class="details-body ph-form">
                        <div class="details-col_wrapper">
                            <div class="details-col">
                                ${rows1.map(renderBusinessDetailsCardItem(edit)).join('')}
                                ${renderBusinessDetailsCardItem(edit)(addressInput)}
                            </div>
                        </div>
                        <div class="details-col_wrapper">
                            <div class="details-col">
                                ${rows2
                                    .filter((r) => r.show !== false)
                                    .map(renderBusinessDetailsCardItem(edit))
                                    .join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        `);

        $('.business-details-card textarea[name="address"]').css('width', '300px');
        renderAddressAutoComplete($('.business-details-card textarea[name="address"]'));

        $('.business-details-card .edit-button').on('click', () => {
            renderBusinessDetailsCard(true);
            $('input[name="legalName"]').trigger('focus');
        });

        $('.business-details-card .upload-file').on('click', (e) => {
            const container = $(e.currentTarget).closest('div');
            openUploadFileDialog(
                t('Upload certificate'),
                (response) => {
                    regCert = response[0];
                    container.html(`
                        <a class="upload-file clickable">
                            ${response[0].originalname}&nbsp;
                            <i class="material-icons" style="line-height: 0px; transform: translateY(10px);">edit</i>
                        </a>
                    `);
                },
                { url: `/api/clubs/${selectedID}/kyc-assets`, maxFiles: 1 }
            );
        });

        $('.business-details-card .cancel-button').on('click', () => renderBusinessDetailsCard(false));

        $('.business-details-card .save-button').on('click', async () => {
            const doc = { ...getSerializedObjectFromForm($('.business-details-card form')), regCert };
            doc.address = clubKYCData.businessDetails.address;

            toggleLoading($('.kyc-business-details-card'), true);
            const response = await updateClubKycAPI(selectedID, { ...clubKYCData, businessDetails: doc });
            toggleLoading($('.kyc-business-details-card'), false);

            if (!response[0]) {
                return instantNotification(
                    t(
                        '<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to save the kyc data...<br>Please try again later.'
                    ),
                    'error'
                );
            }

            clubKYCData = {
                ...clubKYCData,
                businessDetails: { ...clubKYCData.businessDetails, ...response[1].businessDetails },
            };
            renderBusinessDetailsCard();
            instantNotification(t('Successfully Updated Business Details!'));
        });

        $('.business-details-card .view-asset').on('click', () => viewClubKYCAsset(regCert));
    }

    function renderAddressAutoComplete(element) {
        function cb() {
            const components = addressAutoInput?.getPlace()?.address_components;
            const address = { line1: '', line2: '', city: '', state: '', postalCode: '', country: '' };

            for (const component of components) {
                const type = component.types[0];
                switch (type) {
                    case 'street_number':
                        address.line1 = `${component.long_name} ${address.line1}`;
                        break;
                    case 'route':
                        address.line1 += component.long_name;
                        break;
                    case 'subpremise':
                        address.line2 = component.long_name;
                        break;
                    case 'locality':
                        address.city = component.long_name;
                        break;
                    case 'administrative_area_level_1':
                        address.state = component.short_name;
                        break;
                    case 'postal_code':
                        address.postal_code = component.long_name;
                        break;
                    case 'country':
                        address.country = component.long_name;
                        break;
                }
            }

            clubKYCData.businessDetails.address = address;
        }

        addressAutoInputLsr && google.maps.event.removeListener(addressAutoInputLsr);
        addressAutoInput && google.maps.event.clearInstanceListeners(addressAutoInput);

        addressAutoInput = new google.maps.places.Autocomplete(element[0]);
        addressAutoInputLsr = google.maps.event.addListener(addressAutoInput, 'place_changed', cb);
    }

    function renderBusinessDetailsCardItem(useInput = false) {
        return ({ label, key, value, input, info, helperText, placeholder, valueClass }) => {
            let inputComponent = '';
            if (input === 'upload') {
                inputComponent = !value
                    ? `<a class="upload-file clickable"><i class="material-icons" style="line-height: 0px; transform: translateY(5px);">upload</i> ${t(
                          'Upload File'
                      )}</a>`
                    : `<a class="upload-file clickable">${value}&nbsp;<i class="material-icons" style="line-height: 0px; transform: translateY(10px);">edit</i></a>`;
            } else if (input === 'textarea') {
                inputComponent = textArea({ name: key, value });
            } else if (input) {
                inputComponent = textField({ name: key, value });
            }

            const infoComponent = info ? infoPopup(info, { style: '', class: 'details-row-item--icon' }) : '';

            let valueContent = '';
            if (useInput && input) {
                valueContent = `
                    ${inputComponent}
                    <div class="" style="font-size: 12px; line-height: 1.25; margin-top: 4px;">
                        ${helperText || ''}
                    </div>
                `;
            } else if (value) {
                valueContent = `<span class="${valueClass || ''}">${value}</span>`;
            } else if (placeholder) {
                valueContent = placeholder;
            }

            return `
                <div class="details-row">
                    <div class="details-row_item--label-wrapper">
                        <div class="details-row_item details-row_item--label">
                            ${label} ${infoComponent}
                        </div>
                    </div>
                    <div class="details-row_item details-row_item--value">
                        ${valueContent}
                    </div>
                </div>
            `;
        };
    }

    function cardTitle(title, actions = [], customButton) {
        const buttons = [
            {
                label: t('Edit'),
                icon: 'edit',
                className: `btn-primary edit-button`,
                show: actions.includes('edit'),
            },
            {
                label: t('Cancel'),
                icon: 'close',
                className: `btn-default cancel-button`,
                show: actions.includes('cancel'),
            },
            {
                label: t('Save'),
                icon: 'save',
                className: `btn-primary btn-danger save-button`,
                show: actions.includes('save'),
            },
            {
                label: t('Add'),
                icon: 'add',
                className: `btn-primary add-button`,
                show: actions.includes('add'),
            },
        ];

        if (customButton) {
            buttons.push(customButton);
        }

        const renderedButtons = buttons
            .filter((button) => button.show)
            .map(
                ({ label, icon, className, customIcon }) => `
                    <button class="btn ${className}">
                        ${
                            customIcon
                                ? `${customIcon}`
                                : `</i><i class="material-icons" style="font-size: 14px;">${icon}</i>`
                        }
                        ${label}
                    </button>
                `
            )
            .join('');

        return `
            <div class="ph-card_header --header-surface">
                <h3>${title}</h3>
                <div>${renderedButtons}</div>
            </div>
        `;
    }

    async function renderStripeKYCSection() {
        const companyPhone = !_get(connectedAccount, 'company.phone')
            ? false
            : await phoneParser(connectedAccount.company.phone, connectedAccount.country);

        const formatBankAccountsList = (data) => {
            if (!data) return '';
            let formattedBankItem = '';

            for (const acc of data) {
                formattedBankItem += `
                    <div class="contact-card ph-contact-card ph-bank-card">
                        <div class="ph-contact-card_header">
                            <div class="ph-contact-card_name_wrapper">
                                <h5 class="__name">${acc.bank_name}</h5>
                            </div>
                            <div class="d-flex flex-row flex-gap-2">
                                <div class="ph-badge--borderless ph-badge badge-grey">
                                    ${acc.currency.toUpperCase()}
                                </div>
                                ${
                                    acc.default_for_currency == true
                                        ? `<div class="ph-badge badge-blue ph-badge--borderless">${t('Default')}</div>`
                                        : ''
                                }
                            </div>
                        </div>
                        <div class="ph-contact-card_info">
                            <div class="details-col">
                                <div class="details-row">
                                    <div class="details-row_item details-row_item--label">
                                        ${t('Routing Number')}
                                    </div>
                                    <div class="details-row_item details-row_item--value">
                                        ${acc.routing_number}
                                    </div>
                                </div>
                                <div class="details-row">
                                    <div class="details-row_item details-row_item--label">
                                        ${t('Bank Country')}
                                    </div>
                                    <div class="details-row_item details-row_item--value flex-row">
                                        <div class="d-flex flex-gap-2">
                                            ${acc.country}
                                            <img style="height: 20px; width: 20px;" src="/assets/images/flags/${acc.country.toUpperCase()}.svg">
                                        </div>
                                    </div>
                                </div>
                                <div class="details-row">
                                    <div class="details-row_item details-row_item--label">
                                        ${t('Fingerprint')}
                                        <i
                                            class="material-icons d-flex info-icon"
                                            data-toggle="tooltip"
                                            data-placement="right"
                                            title="${t(
                                                'If you operate multiple clubs that use the same bank account(s), fingerprints will match between clubs. Use this information to verify if each club you operate uses the same or a different configured bank account'
                                            )}"
                                            style="font-size: 15px;"
                                        >
                                            info_outline
                                        </i>
                                    </div>
                                    <div class="details-row_item details-row_item--value">
                                        ${acc.fingerprint}
                                    </div>
                                </div>
                                <div class="details-row">
                                    <div class="details-row_item details-row_item--label">
                                        ${t('Account Number')}
                                    </div>
                                    <div class="details-row_item details-row_item--value">
                                        •••• ${acc.last4}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            return formattedBankItem;
        };

        $('.stripe-details-card').html(`
            ${cardTitle(t('Stripe Business Profile'), [], {
                label: t('Update Business Profile'),
                customIcon: '<i class="fa-brands fa-cc-stripe"></i>',
                className: `btn waves-effect btn-primary open-stripe-kyc`,
                show: true,
            })}
            <div class="px-8 py-4">
                <div class="alerts-list mb-2 d-flex flex-column gap-1">
                    <div class="ph-alert ph-alert_info">
                        <div class="ph-alert__description --no_title">
                            ${t(
                                'UBX uses Stripe for secure financial services. The following information has been provided to Stripe as part of the KYC (Know Your Customer) verification process of your account. Should any of the details on record change, please amend them immediately to avoid service suspension or interruption to your payouts.'
                            )}
                        </div>
                    </div>
                </div>

                <div class="details-body">
                    <div class="details-col_wrapper">
                        <div class="details-col">
                            <h4>${t('Identity')}</h4>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t('Name')}</div>
                                <div class="details-row_item details-row_item--value">${_get(
                                    connectedAccount,
                                    'company.name',
                                    'Unknown'
                                )}</div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t(
                                    'Internal Account ID #'
                                )}</div>
                                <div class="details-row_item details-row_item--value">
                                    <span class="copy-clipboard">
                                        <code>${_get(connectedAccount, 'id', '')}</code>
                                    </span>
                                </div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t('Account Country')}</div>
                                <div class="details-row_item details-row_item--value">
                                    <div class="d-flex flex-row gap-1">
                                    ${
                                        !_get(connectedAccount, 'country')
                                            ? 'Unknown'
                                            : `
                                        ${connectedAccount.country}
                                        <img style="height: 20px; width: 20px;" src="/assets/images/flags/${connectedAccount.country.toUpperCase()}.svg">
                                    `
                                    }
                                    </div>
                                </div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t(
                                    'Company Ownership Details Provided'
                                )}</div>
                                <div class="details-row_item details-row_item--value">
                                  ${
                                      _get(connectedAccount, 'company.owners_provided', null) === null
                                          ? renderProvidedBadge(false)
                                          : renderProvidedBadge(connectedAccount.company.owners_provided)
                                  }
                                </div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t(
                                    'Phone Number on Record'
                                )}</div>
                                <div class="details-row_item details-row_item--value">${
                                    companyPhone === false ? 'Unknown' : companyPhone.phone
                                }</div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t(
                                    'Company Directors Provided'
                                )}</div>
                                <div class="details-row_item details-row_item--value">${
                                    _get(connectedAccount, 'company.directors_provided', null) === null
                                        ? renderProvidedBadge(false)
                                        : renderProvidedBadge(connectedAccount.company.directors_provided)
                                }</div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t('Address')}</div>
                                <div class="details-row_item details-row_item--value">
                                    ${_get(connectedAccount, 'company.address.line1', '')}
                                    ${_get(connectedAccount, 'company.address.line2', '')}
                                    ${_get(connectedAccount, 'company.address.city', '')}
                                    ${_get(connectedAccount, 'company.address.state', '')}
                                    ${_get(connectedAccount, 'company.address.postal_code', '')}
                                    ${_get(connectedAccount, 'company.address.country', '')}
                                </div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t('Tax Details Provided')}</div>
                                <div class="details-row_item details-row_item--value">${
                                    _get(connectedAccount, 'company.tax_id_provided', null) === null
                                        ? renderProvidedBadge(false)
                                        : renderProvidedBadge(connectedAccount.company.tax_id_provided)
                                }</div>
                            </div>
                        </div>
                    </div>
                    <div class="details-col-payout-wrapper">
                        <div class="d-flex flex-column">
                            <h4>${t('Payout information')}</h4>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t('Payout schedule')}</div>
                                <div class="details-row_item details-row_item--value">
                                    ${_get(connectedAccount, 'settings.payouts.schedule.interval', '').capitalize()}
                                    ${_get(
                                        connectedAccount,
                                        'settings.payouts.schedule.weekly_anchor',
                                        ''
                                    ).capitalize()}
                                </div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--label">${t('External Accounts')}</div>
                            </div>
                            <div class="details-row">
                                <div class="details-row_item details-row_item--value details-row_item--accounts details-row_item--accounts-list flex-row flex-gap-4">
                                    ${formatBankAccountsList(_get(connectedAccount, 'external_accounts.data'))}
                                </div>
                            </div>
                            <div class="details-row py-6">
                                <div class="ph-alert">
                                    <div class="ph-alert__description">
                                        ${t(
                                            '* To update your payout schedule and/or payout bank account, please contact your account manager.'
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `);

        $('.stripe-details-card .open-stripe-kyc').on('click', openStripeKYC);

        connectedAccountRequirementErrorsAlert(connectedAccount, '.stripe-details-card .alerts-list');
        addClipboardCopyButtons($('.stripe-details-card').find('.copy-clipboard'));

        if (!_get(connectedAccount, 'tos_acceptance.date')) {
            $('.alerts-list').append(`
                <div class="ph-alert ph-alert_danger stripe-tos-error-notif">
                    <div class="ph-alert__description --no_title">
                        <strong>
                            <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                            ${t(
                                'Your account may have payouts and other functionality disabled until you read and accept the <a href="https://stripe.com/connect-account/legal/full" target="_blank">Stripe Connected Account Agreement</a>, which includes the <a href="https://stripe.com/legal" target="_blank">Stripe Terms of Service</a> (collectively, the "Stripe Services Agreement").'
                            )}
                        </strong>
                    </div>
                    <div class="ph-alert__title">
                        <button
                            id="stripe-accept-tos"
                            class="btn btn-danger waves-effect ml-auto"
                        style="min-width: fit-content;"
                    >
                            ${t('Accept Stripe Services Agreement')}
                        </button>
                    </div>
                </div>
            `);
        }

        $('#stripe-accept-tos').on('click', async () => {
            $('#stripe-accept-tos').attr('disabled', 'disabled');
            const response = await acceptClubStripeAccountTOSAPI(selectedID);

            if (!response[0]) {
                return instantNotification(t('Failed to update account. Please contact HQ for assistance.'), 'error');
            }

            $('.stripe-tos-error-notif').addClass('d-none');
        });
    }

    init();
}

function viewClubKYCAsset(asset) {
    const html = document.createElement('div');
    const url = `/api/clubs/${selectedID}/kyc-assets/${asset.key}`;

    $(html).html(`
        <div class="loading-container">
            <h3>${t('Decrypting File')}</h3>
            <div class="preloader pl-size-xl" style="padding: 10px">
                <img src="/assets/images/tail-spin.svg" />
            </div>
        </div>
        <div class="asset-container d-none">
            ${
                asset.mimetype.includes('image')
                    ? `<div class="d-flex align-center justify-center" style="width: 100%; min-height: 800px;"><img src="${url}" style="max-width: 100%;" /></div>`
                    : `<iframe data-hj-allow-iframe="" src="${url}" style="width: 100%; height: 800px;"></iframe>`
            }
        </div>
        <div class="action-container d-none" style="margin-top: 2rem;">
            <a class="btn btn-primary waves-effect" href="${url}" download="${asset.originalname}">
                <i class="material-icons">download</i> <span>${t('Download')}</span>
            </a>
        </div>
    `);

    $(html)
        .find('.asset-container iframe, .asset-container img')
        .on('load', () => {
            $('.loading-container').addClass('d-none');
            $('.asset-container').removeClass('d-none');
            $('.action-container').removeClass('d-none');
        });

    $(html)
        .find('.asset-container iframe, .asset-container img')
        .on('error', () => {
            $('.loading-container').addClass('d-none');
            $('.asset-container').removeClass('d-none');
            $('.asset-container').html(`
                <div class="text-center">
                    <h3>${t('Failed to load asset. Please try again later.')}</h3>
                </div>
            `);
        });

    swalWithBootstrapButtons.fire({ width: 800, html, showCloseButton: true, showConfirmButton: false });
}

async function showMissingKYCConfigAlert() {
    $('.missing-club-kyc-config-alert-container').html('');
    const response = await getClubKycAPI(selectedID);
    const contactDetails = _get(response, '1.contactDetails', []);

    const hasOwner = contactDetails.some((contact) => contact.role === 'owner');
    const hasStaff = contactDetails.some((contact) => contact.role === 'staff');

    if (!response[1] || hasOwner || hasStaff) return [false];
    const alert = `
        <div class="custom-alert" style="" role="alert">
            <div class="custom-alert__content">
                <div class="custom-alert__icon">
                    <i class="bell-icon custom-icon"></i>
                </div>
                <div class="custom-alert__body">
                    <h6>${t("Don't forget to add your staff, managers, and owners")}</h6>
                    <p>
                        ${t(
                            'Please provide your business details in the KYC (Know Your Customer) section of our software, as information like phone numbers will be used for critical business notifications, including alerts from Duress Buttons and other important events.'
                        )}
                    </p>
                </div>
            </div>
            <a href="/club-kyc" class="btn btn-primary custom-alert__btn waves-effect normal-case">${t(
                "Business Profile (KYC)"
            )}</a>
        </div>
    `;

    $('.missing-club-kyc-config-alert-container').html(alert);
    return [true, alert];
}

function connectedAccountRequirementErrorsAlert(connectedAccount, container) {
    const currentDue = (connectedAccount.requirements?.currently_due || []).map((cd) => ({
        code: cd,
        reason: 'This field is required',
    }));
    const pastDue = (connectedAccount.requirements?.past_due || []).map((pd) => ({
        code: pd,
        reason: 'This field is past due',
    }));
    const currentErrors = connectedAccount.requirements?.errors ?? [];

    const futureRequirements = connectedAccount.future_requirements ?? {};
    const uniqueFutureRequirements = Array.from(
        new Set([...(futureRequirements?.currently_due ?? []), ...(futureRequirements?.eventually_due ?? [])])
    );

    const allErrors = [...currentDue, ...pastDue, ...currentErrors, ...uniqueFutureRequirements];
    if (allErrors.length < 1) return false;

    const message = `To ensure uninterrupted service, Stripe may request additional information to comply with KYC (Know Your Customer) requirements; failure to provide this could result in restricted account access and disabled payouts. Please confirm and update any necessary details through the Stripe KYC process. For assistance, contact HQ directly.`;

    $(container).append(`
        <div class="custom-alert custom-alert--danger" role="alert">
            <div class="custom-alert__content">
                <div class="custom-alert__icon">
                    <i class="warning-icon custom-icon"></i>
                </div>
                <div class="custom-alert__body">
                    <h6 style="font-size: 18px;">${t('Attention Required!')}</h6>
                    <p>${t(message)}</p>
                </div>
            </div>
                <a class="open-stripe-kyc btn btn-danger btn-outline custom-alert__btn waves-effect normal-case">
                    <i class="fa-brands fa-cc-stripe"></i>
                    ${t('Update KYC Details in Stripe')}
                </a>
            </div>
        </div>
    `);

    if (openStripeButton) {
        $(container).find('.open-stripe-kyc').on('click', openStripeKYC);
    }

    return true;
}

async function openStripeKYC() {
    let self = this;

    $(self).attr('disabled', true);
    const response = await generateStripeKYCTokenAPI(selectedID);
    $(self).attr('disabled', false);

    if (!_get(response[1], 'url')) {
        return instantNotification('Failed to get account. Please contact Support.', 'error');
    }

    window.open(response[1].url, '_blank')?.focus();
}
