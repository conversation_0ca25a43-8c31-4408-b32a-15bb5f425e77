function sApplicationFees() {
    const basePath = '/club-charges-and-fees';
    const tabs = ['royalties', 'stripe-connected-accounts'];

    function init() {
        initUrl();
        initTabs();
        handleTabChange();
    }

    function initUrl() {
        const tab = location.pathname.split('/').pop();
        if (tabs.includes(tab)) return;
        history.pushState({ tabChange: true }, null, `${basePath}/${tabs[0]}`);
    }

    function initTabs() {
        const tab = location.pathname.split('/').pop();

        $(`.nav-tabs a[href="#${tab}"]`).tab('show');
        $('.nav-tabs a').on('click', function (e) {
            e.preventDefault();
            const href = $(this).attr('href');
            const formattedHref = `${basePath}/${href.replace('#', '')}`;
            history.pushState({ tabChange: true }, null, formattedHref);
            handleTabChange();
        });
    }

    function handleTabChange() {
        const tab = location.pathname.split('/').pop();
        if (tab === 'royalties') return sRoyaltyChargesConfig();
        if (tab === 'stripe-connected-accounts') return sStripeConnectedAccounts();
    }

    init();
}
