function initSecureUploader() {
    let files = [];
    let table = null;
    const searchButtons = [{ label: 'Upload File', icon: 'fa-upload', onClick: openFileTypeDialog }];

    async function init() {
        $('#files-table tbody').empty();
        searchbarHeader('.uploads-nav', searchButtons);

        loadingOverlay('.uploads-container');
        const [filesRes, legacyRes] = await Promise.all([listSecureUploadsAPI(selectedOrgID), listLegacyUploadsAPI()]);
        loadingOverlay('.uploads-container', false);

        if (!filesRes[0]) {
            return $('#files-table tbody').html(
                '<tr><td colspan="5"><em class="text-danger">Failed to load files</em></td></tr>'
            );
        }

        const legacyFiles = legacyRes[0] ? legacyRes[1] : [];
        files = [...filesRes[1], ...legacyFiles];
        $('.search-bar').on('input', (e) => {
            const text = e.target.value;
            table && table.search(text).draw();
        });

        renderFileList();
    }

    function renderFileList() {
        if (table) {
            table.destroy();
            $('#files-table tbody').empty();
        }

        files.forEach((file) => {
            const icon = file.fileType.includes('image')
                ? 'fa fa-image'
                : file.fileType.includes('video')
                ? 'fa fa-video'
                : 'fa fa-file';

            const viewableClass = file.fileType === 'file' ? 'file-download' : 'view-s3-object';
            const permission = file.isPublic ? 'public' : 'private';
            const hideCopyBtn = ['uploaded', 'processed'].includes(file.status) && !file.deleting ? '' : 'hidden';

            const statusBadges = {
                'to process': `<div class="badge badge-yellow">${t('TO PROCESS')}</div>`,
                processing: `<div class="badge badge-yellow">
                            ${t('PROCESSING')}
                            ${_get(file, 'progress', 0)}%
                        </div>`,
                error: `<div class="badge badge-red" title="${file.message}">${t('ERROR')}</div>`,
            };

            const status = file.deleting
                ? `<div class="badge badge-red">DELETING</div>`
                : statusBadges[file.status] || '';

            const orgUrlBucket = file.isPublic ? '' : `.s3.${file.region}.amazonaws.com`;
            const originalUrl = `https://${file.bucket}${orgUrlBucket}/${file.s3Key}`;
            const proxyUrl = `${location.protocol}//${location.host}/api/admin/secure-upload/${permission}/${file.s3Key}`;
            const nameHtml = !hideCopyBtn
                ? `<a href="${file.isPublic ? originalUrl : proxyUrl}" class="${viewableClass}" target="_blank">
                    ${file.name}
                  </a>`
                : `<span>${file.name}</span>`;

            const sourceBadge =
                file.source?.module === 'designer'
                    ? `<div class="badge badge-blue" data-toggle="tooltip">${t('Marketing & Print')}</div>`
                    : '';

            const date = file.isLegacy
                ? `<div class="badge badge-green">${t('LEGACY UPLOAD')}</div>`
                : moment(file.created * 1000).format('YYYY-MM-DD HH:mm');
            const dateSort = typeof file.created === 'number' ? file.created : 0;

            const wikiEmbedUrl = file.isPublic ? originalUrl : proxyUrl;
            const wikiEmbed = ['image', 'video', 'audio'].includes(file.fileType)
                ? `![media](${wikiEmbedUrl})`
                : `[file](${wikiEmbedUrl})`;

            $('#files-table tbody').append(`
                <tr data-key="${file.s3Key}">
                    <td><i class="${icon}"></i></td>
                    <td>
                        <div class="d-flex flex-column align-start" style="gap: 2px;">
                            ${nameHtml}${sourceBadge}
                        </div>
                    </td>
                    <td class="text-center" data-order="${file.size}">${humanFileSize(file.size)}</td>
                    <td class="text-center" data-order="${dateSort}">${date}</td>
                    <td class="text-center">
                        ${
                            file.isPublic
                                ? '<div class="badge badge-red">Public</div>'
                                : '<div class="badge badge-blue">Private</div>'
                        }
                    </td>
                    <td>
                        <span class="d-flex gap-1 align-center justify-end">
                            ${status}
                            <div class="d-flex gap-1 actions">
                                <div class="dropdown container-body ${hideCopyBtn}">
                                    <a class="btn btn-link ph-btn ph-btn-md btn-outline ph-btn-accent-hover dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                        <i class="fa fa-copy"></i>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenu1">
                                        ${
                                            wikiEmbed
                                                ? `<li><a href="#" class="copy-url" data-url="${wikiEmbed}">Copy Knowledge Base Embed URL</a></li>`
                                                : ''
                                        }
                                        ${
                                            file.isPublic
                                                ? ''
                                                : `<li><a href="#" class="copy-url" data-url="${proxyUrl}">Copy Proxy URL</a></li>`
                                        }
                                        <li><a href="#" class="copy-url" data-url="${originalUrl}">Copy Original URL</a></li>
                                        <li><a href="#" class="copy-url" data-url="${file.s3Key}">Copy Key</a></li>
                                    </ul>
                                </div>
                                <a class="btn btn-link ph-btn ph-btn-md btn-outline btn-danger delete-file"><i class="fa fa-trash"></i></a>
                            </div>
                        </span>
                    </td>
                </tr>       
            `);
        });

        table = $('#files-table').DataTable({
            responsive: true,
            dom: 'tip',
            order: [
                [3, 'desc'],
                [1, 'asc'],
            ],
            iDisplayLength: 50,
            columnDefs: [{ targets: [0, 5], orderable: false }],
        });
        wrapTableWithScrollableDiv('#files-table');

        $('.copy-url').on('click', (e) => {
            e.preventDefault();
            const { url } = $(e.currentTarget).data();
            navigator.clipboard.writeText(url);
            instantNotification('Copied to clipboard', 'success');
        });

        $('.delete-file').on('click', (e) => {
            e.preventDefault();
            const { key } = $(e.currentTarget).closest('tr').data();
            const file = files.find((f) => f.s3Key === key);
            if (!file) return null;

            let deletePermanently = false;
            const html = document.createElement('div');
            const deletePermanentlyLever = file.isLegacy
                ? ''
                : switchLever('Delete Permanently', null, 'delete-permanently', false);
            $(html).append(`
                <p>Are you sure you want to delete this file? Please note that the deleted file will not be fully removed and can be recovered if needed.</p>
                ${deletePermanentlyLever}
            `);

            $(html).on('change', 'input', (e) => {
                deletePermanently = $(e.currentTarget).is(':checked');
            });

            confirmDeleteDialog(null, html, async () => {
                files = files.map((f) => {
                    if (f.s3Key === key) f.deleting = true;
                    return f;
                });
                renderFileList();

                const [ok] = await deleteSecureUploadAPI(key, deletePermanently, selectedOrgID);
                if (!ok) {
                    files = files.map((f) => {
                        if (f.s3Key === key) f.deleting = false;
                        return f;
                    });
                    renderFileList();
                    return instantNotification('Failed to delete file', 'error');
                } else {
                    instantNotification('File deleted', 'success');
                    files = files.filter((f) => f.s3Key !== key);
                    renderFileList();
                }
            });
        });

        $(document).on('click', `#files-table .view-s3-object`, (e) => {
            e.preventDefault();
            const { key } = $(e.currentTarget).closest('tr').data();
            viewFileDialog(key);
        });

        $(document).on('click', `#files-table .file-download`, () => {
            instantNotification('Downloading file', 'success');
        });
    }

    function viewFileDialog(key) {
        const uuid = key.split('/')[1].split('.')[0];
        const file = files.find((f) => f.s3Key === key);
        if (!file) return null;

        const { fileType } = file;
        const permission = file.isPublic ? 'public' : 'private';
        const posterUrl = `/api/admin/secure-upload/${permission}/poster/${uuid}`;

        const html = document.createElement('div');
        $(html).addClass('ph-dialog');
        $(html).html(`
            <fieldset>
                <legend>${file.name}</legend>
                <div class="media-wrapper d-flex"></div>
            </fieldset>
        `);

        const fileOutputComponent =
            fileType === 'image'
                ? `<img src="" style="width: 100%; min-height: 280px; object-fit: contain;">`
                : fileType === 'video'
                ? `<video controls crossorigin playsinline style="width: 100%;" data-poster="${posterUrl}" />`
                : fileType === 'audio'
                ? `<audio controls crossorigin playsinline />`
                : '<div class="d-flex align-center justify-center" style="height: 280px;"><h3>File type not supported</h3></div>';

        if (fileType === 'audio') {
            $(html).find('.media-wrapper').addClass('d-flex align-center justify-center');
            $(html).find('.media-wrapper').css('min-height', '100px');
        } else {
            $(html).find('.media-wrapper').addClass('d-flex align-stretch justify-stretch');
        }

        $(html).find('.media-wrapper').append(fileOutputComponent);
        swalWithBootstrapButtons.fire({
            width: 800,
            html,
            showCloseButton: true,
            showConfirmButton: false,
            onOpen: async () => {
                if (['video', 'audio'].includes(fileType)) {
                    const src = `/api/admin/secure-upload/${permission}/${key}`;
                    const video = $(html).find(fileType);
                    return streamPlayer(video, src);
                }

                if (fileType === 'image') {
                    const img = $(html).find('img');
                    loadingOverlay(img, true, true);
                    $(html).find('img').attr('src', `/api/admin/secure-upload/${permission}/${key}`);
                    $(html)
                        .find('img')
                        .on('load', () => loadingOverlay(img, false));
                }
            },
        });
    }

    function openFileTypeDialog() {
        const html = document.createElement('div');
        $(html).addClass('file-type-dialog ph-dialog-v2 ph-dialog-v2-body');
        $(html).append(`
            <div class="options py-2">
                <div class="d-flex flex-column flex-gap-4">
                    <button class="btn ph-btn btn-default internal-upload-btn upload-btn d-flex items-center justify-center">
                        <i class="material-icons">lock</i> <h4>${t('Internal File')}</h4>
                    </button>
                    <small class="internal-upload-desc upload-desc text-start px-4">
                        <p>${t('Files will only be available to users who are signed into the Performance Hub')}</p>
                        <p>
                            <b>${t('Examples')}:</b>
                            <ul>
                                <li>${t('Knowledge Base')}</li>
                                <li>${t('Internal Announcements')}</li>
                                <li>${t('Restricted Attachments for Clubs')}</li>
                            </ul>
                        </p>
                    </small>
                </div>
                <hr class="ph-hr-vertical mx-3" />
                <div class="d-flex flex-column flex-gap-4">
                    <button class="btn ph-btn ph-btn-warning public-upload-btn upload-btn d-flex items-center justify-center">
                        <i class="material-icons">public</i> <h4>${t('Public File')}</h4>
                    </button>
                    <small class="public-upload-desc upload-desc text-start px-4">
                        <p>${t('Files will be published on the internet and accessible by ANYONE')}</p>
                        <p>
                            <b>${t('Examples')}:</b>
                            <ul>
                                <li>${t('Assets for Websites')}</li>
                                <li>${t('Publicly Downloadable Files for Members')}</li>
                            </ul>
                        </p>
                    </small>
                </div>
            </div>
            <div class="ph-alert ph-alert_slim --size-sm mt-8">
                <div class="ph-alert__description --no_title">
                    ${t('If you are unsure of which type of file to upload')},
                    ${t('please check with a member of the Digital or Technology teams via Slack')}
                </div>
            </div>
        `);

        $(html).on('click', '.options .internal-upload-btn', uploadDialog('internal'));
        $(html).on('click', '.options .public-upload-btn', uploadDialog('public'));

        swalWithBootstrapButtons.fire({
            html,
            title: t('What would you like to upload?'),
            width: 800,
            showConfirmButton: false,
            showCloseButton: true,
        });
    }

    const uploadDialog =
        (type = 'internal') =>
        () => {
            let filesToUpload = [];
            const title = type === 'internal' ? t('Internal File Upload') : t('Public File Upload');
            const html = document.createElement('div');
            $(html).addClass('ph-dialog upload-dialog ph-dialog-v2 ph-dialog-v2-body');
            $(html).append(`
                <p class="mb-0"><b>Supported file types:</b> avi, bmp, docx, gif, jpg, mkv, mp3, mp4, pdf, png, txt, webm, webp</p>
                <em><small>All other file types will be uploaded without preview functionality.</small></em>
                <div class="custom-dz mt-1">
                    <p>${t('Drop files here to upload')}</p>
                </div>
                <form id="upload-form" enctype="multipart/form-data" class="hidden">
                    <input type="file" name="file" accept=".avi,.bmp,.docx,.gif,.jpg,.mkv,.mp3,.mp4,.pdf,.png,.txt,.webm,.webp" multiple />
                    <button type="submit">Upload</button>
                </form>
                <div class="upload-status-list d-flex flex-column gap-1"></div>
            `);

            $(html).on('click', '.custom-dz', () => {
                $('#upload-form input[name=file]').trigger('click');
            });

            $(html).on('dragenter', '.custom-dz', (e) => e.preventDefault());
            $('.custom-dz').on('dragover', (e) => e.preventDefault());

            $(html).on('drop', '.custom-dz', (e) => {
                e.preventDefault();
                const files = e.originalEvent.dataTransfer.files;
                handleFileDrop(files);
            });

            $(html).on('change', '#upload-form input[name=file]', (e) => {
                handleFileDrop(e.target.files);
            });

            function handleFileDrop(filelist) {
                filelist.forEach(renderFile);
                filelist.forEach(uploadFile);
                $('.swal2-confirm').prop('disabled', 'disabled');
            }

            function renderFile(file) {
                const reader = new FileReader();

                reader.onload = function () {
                    const icon = file.type.includes('image')
                        ? 'fa fa-image'
                        : file.type.includes('video')
                        ? 'fa fa-video'
                        : 'fa fa-file';

                    $(html).find('.upload-status-list').append(`
                        <div class="upload-status-item" data-name="${file.name}">
                            <span class="d-flex gap-1 align-center">
                                <i class="${icon}"></i>
                                <span>${file.name}</span>
                                <small>${humanFileSize(file.size)}</small>
                            </span>
                            <span class="d-flex gap-1 align-center">
                                <span class="status" style="padding-top: 6px; padding-bottom: 6px;">Uploading...</span>
                            </span>
                        </div>
                    `);
                };

                reader.readAsDataURL(file);
            }

            const updateUploadStatus = (name) => () => {
                const xhr = new window.XMLHttpRequest();

                xhr.upload.onprogress = function (evt) {
                    if (!evt.lengthComputable) return;
                    const percentComplete = Math.floor((evt.loaded / evt.total) * 100);
                    $(html)
                        .find(`.upload-status-item[data-name="${name}"]`)
                        .find('.status')
                        .text(`${percentComplete}%`);
                };

                return xhr;
            };

            async function uploadFile(file) {
                const tokenRes = await getSecureUploadTokenAPI(file.name, type === 'public', selectedOrgID);
                if (!tokenRes[0]) {
                    if (filesToUpload.length === 0) $('.swal2-confirm').prop('disabled', false);
                    return setStatusToFailed(file);
                }

                const { url, fields, key } = tokenRes[1];
                filesToUpload.push({ file, key, progress: 0 });

                const formData = new FormData();
                Object.entries(fields).forEach(([k, value]) => {
                    formData.append(k, value);
                });
                formData.append('file', file);

                const s3Resp = await uploadToS3API(url, formData, updateUploadStatus(file.name));
                if (!s3Resp[0]) {
                    filesToUpload = filesToUpload.filter((f) => f.key !== key);
                    if (filesToUpload.length === 0) $('.swal2-confirm').prop('disabled', false);
                    return setStatusToFailed(file);
                }

                const fileType = key.split('/')[0];
                const postRes = await saveSecureUploadAPI({
                    key,
                    name: file.name,
                    size: file.size,
                    type: fileType,
                    public: type === 'public',
                    organisationId: selectedOrgID,
                });

                filesToUpload = filesToUpload.filter((f) => f.key !== key);
                if (filesToUpload.length === 0) $('.swal2-confirm').prop('disabled', false);
                if (!postRes[0]) return setStatusToFailed(file);
            }

            function setStatusToFailed(file) {
                $(html)
                    .find(`.upload-status-item[data-name="${file.name}"]`)
                    .find('.status')
                    .html(`<em class="text-danger">${t('FAILED')}</em>`);
            }

            swalWithBootstrapButtons
                .fire({
                    html,
                    title,
                    width: 800,
                    confirmButtonText: t('Done'),
                    showCancelButton: true,
                    showCloseButton: true,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                })
                .then(init);
        };

    init();
}
