let clubVariableData = null;

function initClubVariables() {
    let clubVariableData = null;
    const multiLineVariables = ['ClubHours', 'ExtendedHours', 'UpcomingHolidays'];

    async function init() {
        $('.variables-card-active').hide()
        $('.variables-card-skeleton').show();

        renderVariablesCard('user', true);
        renderVariablesCard('global', true);

        await fetchData();

        if (!clubVariableData) {
            $('.global-variables-card').html(`<em class="text-danger">${t('Failed to fetch data')}</em>`);
        }

        renderVariablesCard('user');
        renderVariablesCard('global');
        renderPresetVariables();

        addClipboardCopyButtons('.variable-key');
        $('#save-variables-footer').on('click', '.btn-save', handleSubmit);
        $('#save-variables-footer').on('click', '.btn-cancel', handleCancel);
        $('.variable-item').on('change', 'input[type=checkbox]', handleCheckboxToggle);
        $('.variable-item').on('input', 'textarea', handleTextAreaChange);
        $('.variable-item').on('click', '.delete-btn', handleDeleteVariable);
        $('.user-variables-card').on('click', '.add-btn', handleAddVariable);
    }

    async function fetchData() {
        const response = await listClubVariablesAPI(selectedID);

        if (!response[0]) {
            return instantNotification(
                t('<b>FAILED TO FETCH VARIABLES DATA!</b><br><br>Could not fetch data at this time. Please contact HQ for assistance.'),
                'error',
                6000
            );
        }

        clubVariableData = Object.keys(response[1].Values)
            .map((key) => response[1].Values[key])
            .sort((a, b) => a.variable.localeCompare(b.variable));
        
        $('.variables-card-skeleton').hide();
        $('.variables-card-active').show();
    }

    function renderVariablesCard(type = 'global', isSkeleton = false) {
        let colHtml = '';

        if (isSkeleton) {
            const skeletonDataItem = {
                type: 'globalAutomatic',
                value: '2 Baroona Rd, Milton QLD 4064, Australia',
                variable: 'ClubAddress',
                isSkeleton: true,
            };
            const skeletonDataItems = Array.from({ length: 5 }, () => ({ ...skeletonDataItem }));
            const skeletonDataColItems = [skeletonDataItems, skeletonDataItems];
            colHtml = skeletonDataColItems
                .map((col) => {
                    return `
                        <div class="variables-list">
                            ${col.map(renderCardItem).join('')}
                        </div>
                    `;
                })
                .join('');    
        } else {
            const variables = clubVariableData.filter((variable) => {
                const fixedVariables = ['LegalBusinessName', 'CompanyNumber'];
                if (fixedVariables.includes(variable.variable)) return false;
                if (type === 'global' && variable.type === 'user') return false;
                if (type === 'user' && variable.type !== 'user') return false;
                return true;
            });
            const maxColSize = Math.ceil(variables.length / 2);
            const cols = variables.reduce(
                (acc, variable) => {
                    const lastIndex = acc.length - 1;
                    if (acc[lastIndex].length >= maxColSize) {
                        acc.push([variable]);
                    } else {
                        acc[lastIndex].push(variable);
                    }
                    return acc;
                },
                [[]]
            );

            if (variables.length == 0) {
                colHtml = `<div class="variables-empty">${t('Looks like it\'s empty...')}</div>`
            } else {
                colHtml = cols
                .map((col) => {
                    return `
                        <div class="variables-list">
                            ${col.map(renderCardItem).join('')}
                        </div>
                    `;
                })
                .join('');
            }
        }

        $(`.${type}-variables-card${isSkeleton ? '.variables-card-skeleton' : '.variables-card-active'}`).html(`
            ${cardTitle(
                type === 'global' ? t('Global Variables') : t('User Variables'),
                type === 'global'
                    ? t('Global Variables are variables set in our designs that are the same across all facilities and brands. Data is pulled from your Facility listing (where possible) to ensure that designs match the same information available on-line. Variables are created by HQ, and may not be deleted, however they can be changed, if the information available is inconsistent or incorrect for your facility.')
                    : t('These variables are specific only to your facility. They can be referenced cross-design, however, however they share no commonality with Global Variables, or other facilities.'),
                type !== 'global'
            )}
            <div class="p-5"><div class="variables-list-wrapper">${colHtml}</div></div>
        `);
    }

    function renderPresetVariables() {
        const exclusiveVariables = [
            {
                key: 'FirstName',
                label: t("Refers to the author's first name"),
                tags: [t('Review Management Only')],
            },
            {
                key: 'LastName',
                label: t("Refers to the author's last name"),
                tags: [t('Review Management Only')],
            },
            {
                key: 'FullName',
                label: t("Refers to the author's full name"),
                tags: [t('Review Management Only')],
            },
            {
                key: 'LegalBusinessName',
                label:
                    clubVariableData.find((v) => v.variable === 'LegalBusinessName')?.value ||
                    `<em class="text-danger">${t('Not Set')}</em>`,
                tags: [],
            },
            {
                key: 'CompanyNumber',
                label:
                    clubVariableData.find((v) => v.variable === 'CompanyNumber')?.value ||
                    `<em class="text-danger">${t('Not Set')}</em>`,
                tags: [],
            },
        ];
        $('.preset-variables-card').html(`
            ${cardTitle(t('Pre-set Variables'), t('These variables are pre-defined and cannot be changed.'))}
            <div class="preset-variables-list">
                ${exclusiveVariables
                    .map((variable) =>
                        renderCardItem({
                            variable: variable.key,
                            valueEl: `<div class="__static-value">${variable.label}</div>`,
                            type: 'preset',
                            actionsMenu: `${variable.tags
                                .map((tag) => `<div class="badge ph-badge bg-primary">${tag}</div>`)
                                .join(' ')}`,
                        })
                    )
                    .join('')}
            </div>
        `);
    }

    function renderCardItem({ variable, value, type, automatic, actionsMenu, valueEl, isSkeleton = false }) {
        const lever =
            type == 'globalAutomatic' || type == 'globalCustom'
                ? switchLever(
                      '<small>' + t('Default') + '</small>',
                      `<small>${t('Manual')}</small>`,
                      'custom',
                      !automatic,
                      'ph-switch'
                  )
                : '';
        const deleteButton =
            type === 'user'
                ? `<button class="btn btn-danger delete-btn"><i class="material-icons">delete</i></button>`
                : '';
        const disabled = type !== 'user' && automatic ? 'disabled' : '';
        let actionsMenuEl = actionsMenu ?? '';

        if (!actionsMenuEl || actionsMenuEl === '') {
            if (lever && lever != '') {
                actionsMenuEl = lever;
            } else if (deleteButton && deleteButton != '') {
                actionsMenuEl = deleteButton;
            }
        }

        return `
            <div class="variable-item" data-variable="${variable}">
                <div class="variable-item_header">
                    <div class="variable-key ${isSkeleton ? 'use-skeleton' : ''}"><code>{{${variable}}}</code></div>
                    <div class="__header-action ${isSkeleton ? 'use-skeleton' : ''}">${actionsMenuEl}</div>
                </div>
                <div class="variable-item_body ${isSkeleton ? 'use-skeleton' : ''}">
                    ${
                        valueEl
                            ? valueEl
                            : type == 'preset'
                            ? value
                            : textArea({
                                  name: variable,
                                  value,
                                  props: disabled,
                                  rows: multiLineVariables.includes(variable) && value !== '' ? 3 : 1,
                              })
                    }
                </div>
            </div>
        `;
    }

    function cardTitle(title, label, showAddBtn = false) {
        const buttons = [{ label: `<i class="material-icons">add</i> ${t('Add Variable')}`, className: `btn-primary btn-danger add-btn`, show: showAddBtn }];

        const renderedButtons = buttons
            .filter((button) => button.show)
            .map(({ label, className }) => `<button class="btn ${className}">${label}</button>`)
            .join('');

        const labelHtml = label ? `
            <div class="ph-alert ph-alert_info --size-sm">
                <div class="ph-alert__description --no_title">${label}</div>
            </div>
        ` : '';

        const renderedButtonsEl = renderedButtons ? `<div class="d-flex gap-1">${renderedButtons}</div>` : '';

        return `
            <div class="ph-card_header --header-surface">
                <div class="d-flex flex-column flex-gap-2">
                    <h3 class="m-0" style="font-size: bold;">${title}</h3>
                    ${labelHtml}
                </div>
                ${renderedButtonsEl}
            </div>
        `;
    }

    const handleSubmit = async () => {
        const data = clubVariableData.map((variable) => ({
            type: variable.type,
            value: variable.value,
            variable: variable.variable,
            automatic: variable.automatic,
        }));

        const response = await saveClubVariablesAPI(selectedID, data);

        if (!response[0]) {
            return instantNotification(
                t('<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to save Facility Variables...<br>Please check that you completed ALL fields, and try again.'),
                'error'
            );
        }

        instantNotification(t('Successfully Saved Facility Variables'));
        toggleFooter(false);
    };

    const handleCheckboxToggle = (e) => {
        const isChecked = $(e.currentTarget).is(':checked');
        const textarea = $(e.currentTarget).closest('.variable-item').find('textarea');
        const variable = $(e.currentTarget).closest('.variable-item').data('variable');
        clubVariableData = clubVariableData.map((variableData) => {
            if (variableData.variable === variable) variableData.automatic = !isChecked;
            return variableData;
        });

        if (textarea.prop('name') === 'CurrencySymbol') {
            textarea.val('');
        }

        textarea.prop('disabled', !isChecked);
        textarea.focus();
        toggleFooter();
    };

    const handleTextAreaChange = (e) => {
        const variable = $(e.currentTarget).closest('.variable-item').data('variable');
        const value = $(e.currentTarget).closest('.variable-item').find('textarea').val();
        clubVariableData = clubVariableData.map((variableData) => {
            if (variableData.variable === variable) variableData.value = value;
            return variableData;
        });
        toggleFooter();
    };

    const handleCancel = () => {
        toggleFooter(false);
        initClubVariables();
    };

    const handleDeleteVariable = (e) => {
        const variableValue = $(e.currentTarget).closest('.variable-item').data('variable');

        confirmDeleteDialog(null, null, async () => {
            const response = await deleteClubVariableAPI(selectedID, variableValue);

            if (!response[0]) {
                return instantNotification(
                    t('<b>FAILED TO DELETE!</b><br><br>An error occurred while trying to delete club variable...<br>Please try again.'),
                    'error'
                );
            }

            initClubVariables();
        });
    };

    const handleAddVariable = () => {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog');
        $(html).html(`
            <fieldset>
                <legend>${t('New Variable')}</legend>
                <form>
                    <div class="d-flex flex-column gap-2">
                        <div>
                            <label>${t('Name')}</label>
                            ${textField({ name: 'variable' })}
                        </div>
                        <div>
                            <label>${t('Value')}</label>
                            ${textArea({ name: 'value' })}
                        </div>
                    </div>
                </form>
            </fieldset>
        `);

        $(html).append(swalActionButtons(handleSubmit));

        async function handleSubmit() {
            const data = getSerializedObjectFromForm($(html).find('form'));
            const response = await createClubVariableAPI(selectedID, data);
            swalWithBootstrapButtons.close();

            if (!response[0]) {
                return instantNotification(
                    t('<b>FAILED ADD VARIABLE! </b><br><br>An error occurred while trying process this request. <br>Please contact HQ for further details.'),
                    'error'
                );
            }

            initClubVariables();
        }

        swalWithBootstrapButtons.fire({
            html: html,
            showConfirmButton: false,
        });
    };

    function toggleFooter(show = true) {
        const footer = $('.pinned-footer');
        const container = $('#facility-variables-page > .container-fluid');

        if (!show) {
            footer.removeClass('active');
            container.css('padding-bottom', '0px');
        } else {
            footer.addClass('active');
            container.css('padding-bottom', '100px');
        }
    }

    init();
}
