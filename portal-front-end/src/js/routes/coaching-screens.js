function coachinScreensIframe() {
    addContainerClasses('#coaching-screens');
    $('.cctv-skeleton-loader').show();

    window.addEventListener('message', function (event) {
        if (typeof event.data == 'object') {
            return;
        }

        if (event.data) {
            const dataObj = JSON.parse(event.data);

            if (dataObj.type !== 'coachingScreens') {
                return;
            }

            if(dataObj.stopCoachingScreensLoaded) {
              $('.cs-skeleton-skeleton-loader').fadeOut();
            }

            console.log({ dataObj });

            if (dataObj.route) {
                firstLoad = true;
                sammy.quiet = false;
                sammy.setLocation(dataObj.route);
                firstLoad = false;
            }

            if (dataObj.deviceData) {
                // Copy the data from the array of items stored in sDevices into a new currentDevicesObj - filtered by the ID of the screen.
                let currentDevicesObj = $.sDevices.base.deviceData.items
                  .filter(item => item.deviceType === "screen")
                  .reduce((acc, curr) => ({ ...acc, [curr.balenaUUID]: curr }), {});
                
                $.sDevices.base.deviceDetailsModel(dataObj.deviceData.balenaUUID, currentDevicesObj);
            }

        }
    });
}

function scoachingScreens() {
    // loadingOverlay('.iframe-placeholder');

    // Update the device stats object if it's not yet set.
    $.sDevices.base.fetchDeviceStats()

    $('.iframe-placeholder iframe').ready(function () {
        setTimeout(function () {
            // $('.iframe-placeholder').LoadingOverlay('hide');
            $('main-container').addClass('coaching-screens-ui');
            if (enableModalBackdrop) enableModalBackdrop($('#coaching-screens'));
        }, 800);
    });

    coachinScreensIframe();
}

function scoachingScreensURI() {
    sammy.quietRoute(
        '/coaching-screens-ui/#' +
            encodeURIComponent(
                $('#coachingScreensContainer').contents().get(0).location.href.replace(document.location.origin, '')
            )
    );
}

function reloadcoachingScreensiframe() {
    $('#coachingScreensContainer').attr('src', '/coaching-screens/');
}
