let searchDebounce = null

function loadTheAcademy() {
    $(document).ready( function () {
        showUsersWithAcademyAccess();
    });
}

function showUsersWithAcademyAccess() {
    loadingOverlay("#members-table-container");

    $('#members-table').DataTable().clear();
    $('#members-table').DataTable().destroy();

    $("#members-table-container").empty();
    $("#members-table-container").html(
        '<table width="100%" id="members-table" class="table table-hover dataTable">' +
            '<thead>' +
                '<tr>' +
                    '<th>Name</th>' +
                    '<th>Email</th>' +
                    '<th>Phone</th>' +
                    '<th>Club</th>' +
                    '<th width="5"></th>' +
                '</tr>' +
            '</thead>' +
            '<tbody></tbody>' +
        '</table>'
    );

    // This table data is loaded server-side so the client does not hang with thousands of members...
    var gmMembersExclusiveStartKey = false, gmStatusFilter = "NONE";
    var gmMembers = $('#members-table').DataTable({
        "responsive": true,
        "initComplete": function(settings, json) {
            $("#members-table .remove").on('click', function () {
                var id = $(this).data("index");
                console.log(id)
                updateAcademyAccess({ id, has_access: false })
            })
        },
        "dom": '<"toolbar">frtip',
        //"order": [[ 3, "desc" ]],
        "ordering": false,
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "/api/admin/users/listacademyusers",
            "data": function (d) {
                if(gmMembersExclusiveStartKey) {
                    d.ExclusiveStartKey = gmMembersExclusiveStartKey;
                }

                // Show members based on selected filter...
                if(gmStatusFilter != "NONE") {
                    d.status = gmStatusFilter
                }

            }
        },
        "columns": [
            { "data": "name" },
            { "data": "email" },
            { "data": "phone" },
            { "data": "club" },
            { "data": "id",
                render: function (data, type, row) {
                    const id = data
                    return `
                        <center>
                            <a class="remove" data-index="${id}" href="#">
                                <i class="material-icons">delete</i>
                            </a>
                        </center>
                    `;
                }
            },
        ]
    });

    // DynamoDB Uses a "start key" which we need to store for pagination of these results...
    gmMembers.on( 'xhr', function () {
        var json = gmMembers.ajax.json();
        gmMembersExclusiveStartKey = (json.ExclusiveStartKey === undefined) ? false : json.ExclusiveStartKey;

        if(json.queryType == "search") {
            $("#members-table-container .paging_simple_numbers").hide();
            $(".filter-by-options select").prop( "disabled", true);
        } else {
            $("#members-table-container .paging_simple_numbers").show();
            $(".filter-by-options select").prop( "disabled", false);
        }

        $('#members-table-container').LoadingOverlay("hide");
    });

    // Respond to search
    $('#mms-user-search-input').change(function() {
      clearTimeout(searchDebounce)
      searchDebounce = null
      searchDebounce = setTimeout(() => {
        console.log("It works!");
        searchClubpassUsers($('#mms-user-search-input').val())
      }, 250)
    })
}

function showSearchResults(data) {
  if (!data || data.length === 0) {
    return $('#academy-search-results').html(`No results found.`)
  }

  const bodyItems = data.map(item => {
    // Get necessary attributes only
    const {
      id,
      first_name,
      last_name,
      email,
      mobile_number,
      gender,
      club_id,
    } = item

    return `
    <tr>
      <th>${item.first_name || ''} ${item.last_name || ''}</th>
      <th>${item.email || ''}</th>
      <th>${item.mobile_number || ''}</th>
      <th>${item.club_id || ''}</th>
      <th>
        <center>
          <a class="add" data-item='${JSON.stringify({
            id,
            first_name,
            last_name,
            email,
            mobile_number,
            gender,
            club_id,
          })}' href="#">
            <i class="material-icons">add</i>
          </a>
        </center>
      </th>
    </tr>
  `}).join('')

  $('#academy-search-results').html(`
    <table width="100%" id="search-result-table" class="table table-hover dataTable">
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Phone</th>
          <th>Club</th>
          <th width="5"></th>
        </tr>
      </thead>
      <tbody>${bodyItems}</tbody>
    </table>'
  `)

  $("#search-result-table .add").on('click', function () {
      const item = $(this).data("item");
      updateAcademyAccess({ ...item, has_access: true })
  })
}

function updateAcademyAccess(data, callback) {
    var req = $.ajax({
        url: '/api/admin/academy/updateaccess',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(data){
            if((data === undefined) ? false : data !== false) {
                showUsersWithAcademyAccess();
                callback(data);
            } else {
                callback(false);
            }
        },
        error: function(xhr, textStatus, errorThrown){
            console.error(xhr, textStatus, errorThrown);
            callback(false);
        }
    });
}

function searchClubpassUsers(keyword, callback) {
    var req = $.ajax({
        url: '/api/admin/users/searchuser/' + keyword,
        method: 'GET',
        contentType: 'application/json',
        success: function(data){
            if((data === undefined) ? false : data !== false) {
                showSearchResults(data)
                callback(data);
            } else {
                callback(false);
            }
        },
        error: function(xhr, textStatus, errorThrown){
            console.error(xhr, textStatus, errorThrown);
            callback(false);
        }
    });
}
