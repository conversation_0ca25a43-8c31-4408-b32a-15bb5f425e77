const initHolidayManagement = () => {
    // Search function
    $(document).on('input', '#holidays-nav input.search-bar', (e) => {
        const value = $(e.currentTarget).val();
        const exp = new RegExp(value, 'igm');

        $('#holidays-table tbody tr').map((_, item) => {
            const name = $(item).find('.name-col .name').html();
            const date = $(item).find('.date-col').html();
            const locations = [];

            $(item).find('.locations-col > div > span').map((_, loc) => {
                locations.push($(loc).html());
            });

            const words = [...locations, name, date];

            if (words.some(w => exp.test(w))) {
                $(item).show();
            } else {
                $(item).hide();
            }
        });
    });

    // Fetch country holidays when a country tab is clicked
    $(document).on('click', '.countries-tabs li a', (e) => {
        e.preventDefault();

        // Change active tab
        $('.countries-tabs li').removeClass('active');
        $(e.currentTarget).closest('li').addClass('active');

        // Save selected country to session
        sessionStorage.setItem('selectedCountry', $(e.currentTarget).data('iso'));

        getCountryHolidays($(e.currentTarget).data('iso'));
    });

    // Activate footer when a switch is toggled
    // Also add an `updated` class to the table row
    $(document).on('change', '#holidays-table tbody tr td input', (e) => {
        $('.pinned-footer').addClass('active');
        $(e.currentTarget).closest('tr').addClass('updated');
    });

    // Save country holidays
    $(document).on('click', '.pinned-footer button', () => {
        onSubmit();
    });

    $(document).on('click', '#holidays-table tbody tr td.name-col .name', (e) => {
        const element = $(e.currentTarget);
        
        updateNameDialog(element, (data) => {
            if (data) {
                if (data.name.replace(/\s/, '') !== '') {
                    element.html(data.name);
                } else {
                    const { originalName } = element.closest('tr').data();
                    element.html(originalName);
                }

                $('.pinned-footer').addClass('active');
                element.closest('tr').addClass('updated');
            }
        });
    });
}

const updateNameDialog = (element, cb) => {
    const div = document.createElement("div");
    $(div).html(`
        <div id="holiday-dialog">
            <fieldset>
                <legend>Edit Holiday</legend>
                    <div class="form-group form-group-no-margin">
                        <div class="form-line">
                            <input class="form-control input-holiday-name" value="${element.html()}"></input>
                        </div>
                    </div>
            </fieldset>
        </div>
    `);

    let updateHolidayConfig
    swalWithBootstrapButtons.fire({
        html: div,
        width: 500,
        showCancelButton: true,
        reverseButtons: true,
        confirmButtonText: 'Save/Update',
        cancelButtonText: 'Cancel',
        onClose: () => {
            updateHolidayConfig = { name: $('.input-holiday-name').val() }
        }
    }).then(response => {
        if (response.value) {
            cb(updateHolidayConfig);
        } else {
            cb(false);
        }
    });
}

const loadCountryHolidays = () => {
    const countries = ClubsObj.reduce((acc, club) => {
        const countryIso = club.localisation?.country?.iso_code;

        if (!!countryIso && !acc.some(a => a.iso_code === countryIso)) {
            return [...acc, club.localisation.country];
        }

        return acc;
    }, []).sort((a, b) => a.iso_code.localeCompare(b.iso_code));

    // Save selected country in session
    sessionStorage.setItem('selectedCountry', countries[0].iso_code);

    // Set up tabs
    $('.countries-tabs').html('');
    countries.forEach((country, index) => {
        $('.countries-tabs').append(`
            <li role="presentation" class="${index === 0 ? 'active' : ''}">
                <a href="#" data-iso=${country.iso_code}>${country.full_name}</a>
            </li>
        `);
    });

    // Fetch table data
    getCountryHolidays(countries[0].iso_code);
}

const getCountryHolidays = (country) => {
    $("#holidays-table tbody").empty();
    $('.table-loading-overlay').show();

    $.ajax({
        url: `/api/admin/country/${country}/holidays`,
        method: 'GET',
        success: (data) => {
            $('.table-loading-overlay').hide();
            data.holidays.forEach((holiday) => {
                $('#holidays-table tbody').append(getHolidayListItem(holiday));
            });
        },
        error: (xhr, textStatus, errorThrown) => {
            $('.table-loading-overlay').hide();
            console.error(xhr, textStatus, errorThrown);
            window.location.replace("/401.html");
        }
    });
}

const getHolidayListItem = (holiday) => {
    const dateObj = moment(holiday.date, 'YYYY-MM-DD');
    const name = holiday.newName || holiday.originalName;
    const isWeekend = dateObj.isoWeekday() === 6 || dateObj.isoWeekday() === 7;
    return `
        <tr data-id="${holiday.id}" data-original-name="${holiday.originalName}">
            <td class="date-col" data-sort="${ dateObj.unix() }">${ dateObj.format("YYYY-MM-DD") }</td>
            <td class="name-col">
                <span style="margin-right: 1rem;" class="name">${name}</span>
                ${isWeekend ? '<div class="badge badge-orange">Weekend</div>' : ''}
            </td>
            <td class="locations-col">
                <div>
                    ${holiday.type === 'National' || (holiday.locations || []).length <= 0
                        ? `<span class="badge badge-blue">National</span>`
                        : holiday.locations.map(location => `<span class="badge badge-grey">${location}</span>`).join('')}
                </div>
            </td>
            <td class="text-center">
                <div class="switch primary">
                    <label>
                        <input type="checkbox" ${holiday.blackListed ? '' : 'checked' }>
                        <span class="lever"></span>
                    </label>
                </div>
            </td>
        </tr>
    `;
}

const onSubmit = () => {
    const selectedCountry = sessionStorage.getItem('selectedCountry');
    const holidays = [];

    $('#holidays-table tbody tr.updated').map((_, item) => {
        const doc = {
            id: $(item).data('id'),
            name: $(item).find('.name-col .name').html(),
            blackListed: !$(item).find('input[type=checkbox]').is(':checked'), 
        };

        holidays.push(doc);
    });

    $('.save-overlay').show();

    $.ajax({
        url: `/api/admin/country/${selectedCountry}/holidays`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({ holidays }),
        success: () => {
            $('.save-overlay').hide();
            $('.pinned-footer').removeClass('active');
            instantNotification(
                `Holidays for the Country [${selectedCountry}] successfully saved.`,
                'success',
                10000
            );
        },
        error: () => {
            $('.save-overlay').hide();
            $('.pinned-footer').removeClass('active');
            instantNotification(
                `An error occured while trying to save the holidays for the Country [${selectedCountry}]. Please contact HQ to further investigate this issue.`,
                'error',
                10000
            );
        }
    });
}