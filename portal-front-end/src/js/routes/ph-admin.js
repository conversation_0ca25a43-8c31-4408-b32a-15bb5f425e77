// db-table-list
// <select id="some-name">


$.phAdmin = {};
$.phAdmin.base = {
    isInitialised: false,
    init: async function() {
        let SocketID = uuidv4();
        let syncLog = [];
        let syncStatus = [false]

        socket.off(SocketID);
        !socket.hasListeners(SocketID) &&
            socket.on(SocketID, (data) => {
                if(data.tableSync !== undefined && data.tableSync.log !== undefined) {
                    syncLog.push(data.tableSync.log.join('\r\n'))
                    syncLog.push('\r\n')
                }
                if(data.tableSync !== undefined && data.tableSync.completed !== undefined && data.tableSync.completed == true) {
                    // Completed table sync command...
                    syncLog.push(`<span style="color: green; font-family: monospace; font-size: 18px; font-weight: bold;">Sync Completed!</span>`)
                    syncStatus.push(true)
                }
            });

        // On init...
        await renderDBTablesList();

        async function renderDBTablesList() {

            let tableNames = []

            await $.ajax({
                type: "GET",
                url: '/api/admin/db-sync/list-tables',
                success: function(res) {
                    for (let variable in res.prod) {
                        tableNames.push(`<option value="${variable}">${res.prod[variable]}</option>`)
                    }
                }
            });

            $(".db-table-list").html(`

                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert alert-red" style="margin-top: 10px;">
                            <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                            <b>Important - Read First!</b><br>
                            <p style="font-weight: 300;">
                                Only sync tables you require - Note any customer data synced from Prod to Stage should have Emails, Phone Numbers &amp; other sensitive information removed to ensure no customer's receive notifications during testing.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12">
                        <select multiple="multiple" id="sync-tables-multi-select">
                            ${ tableNames.join('') }
                        </select>
                    </div>
                </div>

            `);

            $("#sync-tables-multi-select").DualListBox({
                title: 'Database Tables',
                json: false,
                timeout: 10,
                iconSet: 'fontawesome',
                selectedText: 'Will Sync',
                showingText: "items: ",
                changeCallback: function() {
                    let selectedTables=[]
                    $("#sync-tables-multi-select option").each(function() {
                        selectedTables.push($(this).val())
                    });
                    if(selectedTables.length >= 1) {
                        $("a[name='start-sync']").removeClass('disabled');
                    } else {
                        $("a[name='start-sync']").addClass('disabled');
                    }

                }
            });
            return true;
        }

        $(".db-table-list").append(`
            <div class="row">
                <div class="col-sm-12 text-right">
                    <a name="show-log" class="btn btn-lg btn-secondary"><i class="fa fa-terminal" aria-hidden="true"></i> Show Log</a>
                    <a name="start-sync" class="btn btn-lg btn-primary disabled"><i class="fa fa-refresh" aria-hidden="true"></i> Start Sync</a>
                </div>
            </div>`);
        
        
        $("a[name='show-log']").on('click', function () {
            showLogDialog();
        })

        $("a[name='start-sync']").on('click', function () {

            let tablesToSync = [];

            $("#sync-tables-multi-select option").each(function() {
                tablesToSync.push($(this).val())
            });

            let dryRun = true;
            swalWithBootstrapButtons.fire({
                title: 'Confirm Start',
                html: `
                    <p>The following tables will be copied from production to stage:</p>
                    <pre style="max-height: 200px;">${ tablesToSync.join(',\r\n') }</pre>
                    <p>Are you sure you want to proceed? Please ensure if you are copying customer data you remove any personal details after performing a sync.</p>
                    <div class="styled-checkbox">
                        <input id="dry-run" type="checkbox" class="filled-in chk-col-blue" checked="checked">
                        <label for="dry-run">Perform Dry Run (Data will only  be logged and not manipulated)</label>
                    </div>
                    `,
                width: 500,
                showCancelButton: true,
                confirmButtonText: 'Yes, Start Sync!',
                onClose: () => {
                    if (! $('#dry-run').is(':checked')) dryRun = false;
                }

            }).then((result) => {
                if (result.value) {
                    startSync(tablesToSync, dryRun)
                } else {
                    return false;
                }
            })

        })

        async function startSync(tables, dryRun) {

            // Reset log...
            syncLog = []
            syncStatus = []

            // Fire off request to start syncing requested tables...
            await $.ajax({
                type: 'POST',
                url: `/api/admin/db-sync/sync-from-prod?socketID=${SocketID}&dryRun=${dryRun}`,
                contentType: 'application/json',
                data: JSON.stringify(tables),
                success: function(data){
                    console.log(data)
                    showLogDialog();
                },
                error: function(xhr, textStatus, errorThrown){
                    console.log(xhr, textStatus, errorThrown);
                }
            });

        }

        function showLogDialog() {
            swalWithBootstrapButtons.fire({
                title: 'Sync Log',
                html: `<pre style="height: 450px; overflow-y: scroll;" class="text-left realtime-sync-log">${ syncLog.join('\r\n') }</pre><div class="sync-status" style="float: left;"></div>`,
                width: 800,
                showConfirmButton: false,
                showCancelButton: false,
                showCloseButton: true,
                onBeforeOpen: () => {
                    $(function() {

                        let spinner = `<svg style="float: left; height: 20px;" width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" stroke="#000"><g fill="none" fill-rule="evenodd"><g transform="translate(1 1)" stroke-width="2"><circle stroke-opacity=".5" cx="18" cy="18" r="18"/><path d="M36 18c0-9.94-8.06-18-18-18"><animateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/></path></g></g></svg>`

                        // Observe the array - if any more logs are "pushed" in, scroll to the bottom of the log...
                        _.observe(syncLog, function() {
                            $(".realtime-sync-log").html(syncLog.join('\r\n'))
                            $(".realtime-sync-log").scrollTop($(".realtime-sync-log")[0].scrollHeight)
                        });

                        _.observe(syncStatus, function() {
                            if(syncStatus.includes(true) || syncStatus.includes(false)) { 
                                $(".sync-status").hide();
                            } else {
                                $(".sync-status").html(`${spinner} <b>Sync in progress...</b>`)
                            }
                        });

                        if(syncLog.length == 0) {
                            $(".realtime-sync-log").html(`<span>${spinner}<span style="float: left; font-family: monospace; margin-left: 5px;">Logs will appear shortly after sync start...</span></span>`)
                        } else {
                            $(".realtime-sync-log").scrollTop($(".realtime-sync-log")[0].scrollHeight)
                        }

                    });
                },
            })

        }

        $('a.monthly-biller-trigger').on('click', async function(e) {
            e.preventDefault();
            const sessionKey = 'monthly-biller-trigger';
            const html = document.createElement('div');
            $(html).addClass('ph-dialog-v2');
            $(html).html(`
                <div class="body">
                    <div class="form-group">
                        <label>Choose date to simulate</label>
                        <input type="text" class="form-control" id="cron-date" placeholder="YYYY-MM" required>
                        <em class="text-muted"><small>Choose a date in UTC timezone. This will simulate the cron job for the selected date.</small></em>
                    </div>
                    <div class="form-group">
                        <label>Alternatively, you can input a unix timestamp here</label>
                        <input type="text" class="form-control" id="cron-timestamp" placeholder="Unix timestamp">
                        <em class="text-muted"><small>This will override the date picker above.</small></em>
                    </div>
                </div>
            `);
    
            let savedDate = await getSessionStorage(sessionKey);
            if (savedDate) {
                savedDate = moment.unix(savedDate);
            } else {
                savedDate = moment();
            }
    
            datePicker($(html).find('#cron-date'), {
                timePicker: true,
                singleDatePicker: true,
                format: 'YYYY-MM-DD HH:mm',
                startDate: savedDate,
            });
    
            swalWithBootstrapButtons
                .fire({
                    title: 'Run Monthly Biller',
                    html,
                    showCancelButton: true,
                    showCloseButton: true,
                    confirmButtonText: 'Run',
                    preConfirm: async () => {
                        let unix = $(html).find('#cron-timestamp').val();
    
                        if (!unix) {
                            const pickerDate = $(html).find('#cron-date').val();
                            unix = moment(pickerDate, 'YYYY-MM-DD HH:mm').unix();
                        }
    
                        await setSessionStorage(sessionKey, unix);
                        const response = await manuallyRunBillingCronAPI(unix);
                        if (!response[0]) return Swal.showValidationMessage(response[1]);
                    },
                })
                .then((response) => {
                    if (!response.value) return;
                    instantNotification('Cron initiated', 'success');
                });
        });

        $('a.marketing-print-index-trigger').on('click', async function(e) {
          e.preventDefault();
          
          $(this).attr('disabled', 'disabled').addClass('disabled');
          instantNotification('Index update initiated', 'success');
          await updateMPIndex();
          instantNotification('Index update completed', 'success');
          $(this).removeAttr('disabled').removeClass('disabled');
      });

        this.isInitialised = true;
    }
}