function sLegacySaaSBilling() {
    let pdfExportDefinitions = [];
    let csvExportDefinitions = [];

    const rootElement = $('.saas-billin-container');

    renderSearchNav();
    loadSaasBilling();

    function loadSaasBilling() {
        rootElement.empty(); // Clear previous content
        showLoadingIndicator(rootElement);
        pdfExportDefinitions = [];
        csvExportDefinitions = [];

        $.ajax({
            type: 'GET',
            url: `/api/saas-billing/extended-access/usage-report/${selectedID}`,
            success: function (history) {
                hideLoadingIndicator();
                generateSaaSReportUI(history);
            },
        });
    }

    function renderSearchNav() {
        $('.search-nav').html(`
            <button class="export-csv btn btn-default">CSV <i class="fa fa-file-csv pl-4 mr-0"></i></button>
            <button class="export-pdf btn btn-default">PDF <i class="fa fa-file-pdf pl-4 mr-0"></i></button>
        `);

        $('.export-pdf').on('click', exportPdf);
        $('.export-csv').on('click', exportCSV);
    }

    function showLoadingIndicator(element) {
        $('<div>').addClass('loading-indicator').text(`Generating report for ${selectedID}...`).appendTo(element);
    }

    function hideLoadingIndicator() {
        $('.loading-indicator').remove();
    }

    function rateMapping(key) {
        const map = {
            storage: 'pricePerGBStored',
            performanceHubSoftware: 'performanceHubEASoftware',
            cameraSoftware: 'pricePerCamera',
            DuressButtons: 'pricePerDuressButton',
            CoachingScreens: 'pricePerCoachingScreen',
        };
        return map[key] || key;
    }

    function friendlyNameMapping(key) {
        const map = {
            storage: 'CCTV Storage Consumed',
            performanceHubSoftware: 'Extended Access Software',
            cameraSoftware: 'Camera Software',
            DuressButtons: 'Duress Buttons',
            CoachingScreens: 'Coaching Screen Device & Software Charge',
        };
        return map[key] || capitalize(key);
    }

    // "CCTV Storage": "pricePerGBStored",
    // "Extended Access Software": "performanceHubEASoftware",
    // "Camera Software": "pricePerCamera",
    // "Duress Buttons": "pricePerDuressButton",
    // "Coaching Screen Device & Software Charge": "pricePerCoachingScreen"

    function generateSaaSReportUI(data) {
        const container = $('<div>').addClass('card content col-sm-12 saas-billing-report').appendTo(rootElement);

        if (pdfExportDefinitions.length > 0) {
            pdfExportDefinitions.push({ text: '', margin: 30 });
            csvExportDefinitions.push(['']);
            csvExportDefinitions.push(['']);
        }

        const docTitle = `SaaS Billing For ${selectedID}`;
        $('<h3>').text(docTitle).appendTo(container);

        if (data.periodStart && data.periodEnd) {
            const periodStart = moment.unix(data.periodStart).format('MMMM DD, YYYY');
            const periodEnd = moment.unix(data.periodEnd).format('MMMM DD, YYYY');
            const docPeriod = `Period: ${periodStart} - ${periodEnd}`;
            $('<p>').text(docPeriod).appendTo(container);
            pdfExportDefinitions.push({ text: docPeriod, style: 'subheader' });
            csvExportDefinitions.push([docPeriod]);
        } else {
            const docGenerated = `Generated: ${new Date().toLocaleString()}`;
            const docNote = `Please note that device usage is based on devices that have been online in the last 30 days`;
            $('<p>').text(docGenerated).appendTo(container);
            $('<p>').text(docNote).appendTo(container);
            pdfExportDefinitions.push({ text: docGenerated, style: 'subheader' });
            pdfExportDefinitions.push({ text: docNote, style: 'node' });
            csvExportDefinitions.push([docGenerated]);
            csvExportDefinitions.push([docNote]);
        }

        const table = $('<table>').addClass('table table-striped').appendTo(container);
        const thead = $('<thead>').appendTo(table);
        const tbody = $('<tbody>').appendTo(table);
        const pdfTableDefinition = {
            margin: [0, 5, 0, 5],
            layout: 'lightHorizontalLines',
            table: {
                headerRows: 1,
                widths: ['*', 'auto', 'auto', 'auto'],
                body: [],
            },
        };

        // Table headers
        $('<tr>').append('<th>Item</th><th>Count</th><th>Rate</th><th>Total</th>').appendTo(thead);
        pdfTableDefinition.table.body.push(['Item', 'Count', 'Rate', 'Total']);
        csvExportDefinitions.push(['']);
        csvExportDefinitions.push(['Item', 'Count', 'Rate', 'Total']);

        let grandTotal = 0;

        for (let section in data) {
            if (section === 'chargeBreakdown') continue;

            const sectionData = data[section];
            const charge = sectionData.charges;

            for (let type in charge) {
                if (type === 'total') continue;

                if (type === 'performanceHubSoftware' && data.CCTVDevices.totalDevices === 0) {
                    continue; // Skip this item if CCTVDevices.totalDevices is 0
                }

                let rateKey = rateMapping(type);
                let rate = data.chargeBreakdown[rateKey] || '-';
                let count = type === 'storage' ? sectionData.totalStorage : sectionData.totalDevices;

                // If count is zero, then set rate to "-" and total to empty string
                let total = count == 0 ? '' : `$${charge[type]}`;
                if (count == 0) {
                    rate = '-';
                } else {
                    grandTotal += parseFloat(charge[type] || 0);
                }

                $('<tr>')
                    .append(
                        `<td>${friendlyNameMapping(type)}</td>
                     <td>${count}</td>
                     <td>${rate}</td>
                     <td>${total}</td>`
                    )
                    .appendTo(tbody);

                pdfTableDefinition.table.body.push([friendlyNameMapping(type), count, rate, total]);
                csvExportDefinitions.push([friendlyNameMapping(type), count, rate, total]);
            }
        }

        // For DuressButtons and CoachingScreens total charges:
        for (let device of ['DuressButtons', 'CoachingScreens']) {
            let rate = data.chargeBreakdown[rateMapping(device)];
            let charge = data[device]?.charges.total ?? 0;
            let count = data[device]?.totalDevices ?? 0;

            grandTotal += parseFloat(charge);

            $('<tr>')
                .append(
                    `<td>${friendlyNameMapping(device)}</td>
                 <td>${count}</td>
                 <td>$${rate}</td>
                 <td>$${charge}</td>`
                )
                .appendTo(tbody);

            pdfTableDefinition.table.body.push([friendlyNameMapping(device), count, rate, charge]);
            csvExportDefinitions.push([friendlyNameMapping(device), count, rate, charge]);
        }

        $('<tr>')
            .append(
                `<td colspan="3" class="text-right"><strong>SaaS Usage Total:</strong></td>
             <td><strong>$${grandTotal.toFixed(2)}</strong></td>`
            )
            .appendTo(tbody);

        pdfTableDefinition.table.body.push(['', '', '', `SaaS Usage Total: $${grandTotal.toFixed(2)}`]);
        pdfExportDefinitions.push(pdfTableDefinition);
        csvExportDefinitions.push(['', '', '', `SaaS Usage Total: $${grandTotal.toFixed(2)}`]);

        // Charge Breakdown
        const breakdownContainer = $('<div>').addClass('charge-breakdown').appendTo(container);
        const breakdownTitle = 'Breakdown of Charges (Configured for this club)';
        $('<h4>').text(breakdownTitle).appendTo(breakdownContainer);
        pdfExportDefinitions.push({ text: breakdownTitle, style: 'subheader' });
        csvExportDefinitions.push([breakdownTitle]);

        for (let item in data.chargeBreakdown) {
            if (item === 'id') continue;
            const chargeText = `${capitalize(item)}: $${data.chargeBreakdown[item]}`;
            $('<p>').text(chargeText).appendTo(breakdownContainer);
            pdfExportDefinitions.push({ text: chargeText });
            csvExportDefinitions.push([chargeText]);
        }
    }

    function capitalize(string) {
        return (
            string
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .charAt(0)
                .toUpperCase() + string.slice(1)
        );
    }

    function exportPdf() {
        const docWithTitle = [{ text: `SaaS Billing For ${selectedID}`, style: 'header' }, ...pdfExportDefinitions];

        pdfMake
            .createPdf({
                info: { title: `${selectedID}.pdf` },
                content: docWithTitle,
                styles: {
                    header: {
                        fontSize: 24,
                        bold: true,
                        margin: [0, 0, 0, 10],
                    },
                    subheader: {
                        fontSize: 14,
                        bold: true,
                    },
                    node: {
                        fontSize: 12,
                    },
                    defaultStyle: {
                        fontSize: 14,
                    },
                },
            })
            .download(`${selectedID}.pdf`);
    }

    function exportCSV() {
        const rows = [[`SaaS Billing For ${selectedID}`], ...csvExportDefinitions];
        const csvContent = 'data:text/csv;charset=utf-8,' + rows.map((e) => e.join(',')).join('\n');
        window.open(encodeURI(csvContent));
    }
}
