function loadMembershipManagement() {
    $(document).ready( function () {
        loadPrompts();
    });
}

function loadPrompts() {
  $('#gm-membership-access-types-table-container').LoadingOverlay("show");

  $('#prompts-table').DataTable().clear();
  $('#prompts-table').DataTable().destroy();

  $("#gm-membership-access-types-table-container").empty();
  $("#gm-membership-access-types-table-container").html(
    `<div class="table-responsive">
        <div class="mb-3">
            <button id="create-prompt" class="btn btn-primary">${t('Create New')}</button>
        </div>
        <table width="100%" id="prompts-table" class="table table-hover dataTable">
            <thead>
                <tr>
                    <th class="table-header-id">${t('ID')}</th>
                    <th>${t('Name')}</th>
                    <th>${t('Description')}</th>
                    <th>${t('Default')}</th>
                    <th class="table-header-actions">${t('Actions')}</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>`
  );

  var promptsTable = $('#prompts-table').DataTable({
    "responsive": true,
    "dom": '<"toolbar">frtip',
    "ordering": false,
    "processing": true,
    "serverSide": false,
    "ajax": {
      "url": "/api/admin/clubs/membershipaccesstypes?organisationId=" + selectedOrgID,
      "dataSrc": function(json) {
        $('#gm-membership-access-types-table-container').LoadingOverlay("hide");
        return json;
      }
    },
    "columns": [
      { "data": "id" },
      { "data": "name" },
      { "data": "description" },
      { "data": "isDefault" },
      {
        "data": null,
        "render": function (data, type, row) {
          return `<div class="prompt-actions">
                  <a class="btn btn-link action-btn ph-btn ph-btn-md btn-outline ph-btn-accent-hover edit-prompt" data-id="${row.id}"><i class="fa fa-edit mr-0"></i></a>
                  <a class="btn btn-link action-btn ph-btn ph-btn-md btn-outline btn-danger delete-prompt" data-type="archive" data-id="${row.id}" ${row.isDefault ? 'disabled data-toggle="tooltip" title="There should be one default item. Set another item as default to enable delete."' : ''}><i class="fa fa-trash mr-0"></i></a>
                 </div>`;
        },
        "className": "text-right table-data-actions"
      }
    ]
  });

  // Create new prompt
  $('#create-prompt').on('click', function () {
    createPromptDialog();
  });

  // Edit prompt
  $('#prompts-table').on('click', 'tr', function (event) {
    if (!$(event.target).hasClass('delete-prompt')) {
        var accessType = promptsTable.row($(this)).data(); // Get the row data
        editPromptDialog(accessType); // Pass the accessType to the dialog
    }
  });

  // Delete prompt
  $('#prompts-table').on('click', '.delete-prompt', function () {
    var id = $(this).data('id');
    deletePromptConfirmation(id);
  });
}

// Trigger loadPrompts when the page loads
$(document).ready(function() {
    loadPrompts();
});

function createPromptDialog() {
  swalWithBootstrapButtons.fire({
    title: `${t('Create New Membership Access Type')}`,
    html:
        `<div class="ph-dialog-v2"><div class="ph-dialog-v2__content">
            <input id="swal-input-name" class="swal2-input" name="membership-accesstype-name" placeholder="${t('Name')}">
            <textarea id="swal-input-description" class="swal2-textarea" name="membership-accesstype-description" placeholder="${t('Description')}"></textarea>
            <div class="form-check styled-checkbox" style="margin-top:5px;">
              <input id="membership-accesstype-default" type="checkbox" name="membership-accesstype-default" class="filled-in chk-col-blue">
              <label for="membership-accesstype-default">${t('Set this as default')}</label>
            </div>
        </div></div>`,
    focusConfirm: false,
    showCancelButton: true,
    confirmButtonText: `${t('Create')}`,
    cancelButtonText: `${t('Cancel')}`,
    preConfirm: () => {
      return {
        name: document.getElementById('swal-input-name').value,
        description: document.getElementById('swal-input-description').value,
        isDefault: document.getElementById('membership-accesstype-default').checked
      }
    }
  }).then((result) => {
    console.log("result", result);
    if (result.value) {
      $.ajax({
        url: '/api/admin/clubs/membershipaccesstypes?organisationId=' + selectedOrgID,
        method: 'POST',
        data: JSON.stringify(result.value),
        contentType: 'application/json',
        success: function() {
          swalWithBootstrapButtons.fire(`${t('Created!')}`, `${t('The new membership access type has been created.')}`, 'success');
          loadPrompts();
        },
        error: function(e) {
          console.log("Error creating membership access type", e);
          swalWithBootstrapButtons.fire(`${t('Error!')}`, `${t('Failed to create the membership access type.')}`, 'error');
        }
      });
    }
  });
}

function editPromptDialog(accessType) {
    swalWithBootstrapButtons.fire({
        title: `${t('Edit Membership Access Type')}`,
        html:
            `<div class="ph-dialog-v2"><div class="ph-dialog-v2__content">
                <input id="swal-input-name" class="swal2-input" name="membership-accesstype-label" placeholder="${t('Label')}" value="${accessType.name}">
                <textarea id="swal-input-description" class="swal2-textarea" name="membership-accesstype-description" placeholder="${t('Prompt')}">${accessType.description}</textarea>
                <div class="form-check styled-checkbox" style="margin-top:5px;" ${accessType.isDefault ? 'data-toggle="tooltip" title="There should be one default item. Set another item as default to unset this one."' : ''}>
                  <input id="membership-accesstype-default" type="checkbox" name="membership-accesstype-default" class="filled-in chk-col-blue" ${accessType.isDefault ? 'checked' : ''} ${accessType.isDefault ? 'disabled' : ''} >
                  <label for="membership-accesstype-default">${t('Set this as default')}</label>
                </div>
            </div></div>`,
        focusConfirm: false,
        showCancelButton: true,
        confirmButtonText: `${t('Save')}`,
        cancelButtonText: `${t('Cancel')}`,
        preConfirm: () => {
            return {
                name: document.getElementById('swal-input-name').value,
                description: document.getElementById('swal-input-description').value,
                isDefault: document.getElementById('membership-accesstype-default').checked
            }
        }
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: '/api/admin/clubs/membershipaccesstypes/' + accessType.id + '?organisationId=' + selectedOrgID,
                method: 'PUT',
                data: JSON.stringify(result.value),
                contentType: 'application/json',
                success: function() {
                    swalWithBootstrapButtons.fire(`${t('Updated!')}`, `${t('The membership access type has been updated.')}`, 'success');
                    loadPrompts();
                },
                error: function() {
                    swalWithBootstrapButtons.fire(`${t('Error!')}`, `${t('Failed to update the membership access type.')}`, 'error');
                }
            });
        }
    });
}

function deletePromptConfirmation(id) {
  swalWithBootstrapButtons.fire({
      title: `${t('Are you sure?')}`,
      html: `<div class="ph-dialog-v2"><div class="ph-dialog-v2__content ph-dialog-v2__content--prompt-confirmation">
              <h6 class='text-danger'>${t("You won't be able to revert this!")}</h6>
          </div></div>`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: `${t('Yes, delete it!')}`,
      cancelButtonText: `${t('No, cancel!')}`,
      reverseButtons: true
  }).then((result) => {
      if (result.value) {
          $.ajax({
              url: '/api/admin/clubs/membershipaccesstypes/' + id + '?organisationId=' + selectedOrgID,
              method: 'DELETE',
              success: function() {
                  swalWithBootstrapButtons.fire(`${t('Deleted!')}`, `${t('The membership access type has been deleted.')}`, 'success');
                  loadPrompts();
              },
              error: function() {
                  swalWithBootstrapButtons.fire(`${t('Error!')}`, `${t('Failed to delete the membership access type.')}`, 'error');
              }
          });
      }
  });
}
