let supportTicketFilters = {
    status: 'all',
    replyStatus: 'all',
    topic: 'all',
    department: 'all',
    author: 'all',
    sortDate: 'newest',
    sorting: 'lastreply'
};

function generateTicketFilters(topics = [], departments = [], authors = [], filterTickets = () => {}) {
    if ($('#tickets-filter').length === 0) {
        setTimeout(() => {
            generateTicketFilters(topics, departments, authors, filterTickets);
        }, 1000);
        return;
    }
    const topicsOptions = [],
        departmentsOptions = [],
        authorsOptions = [];

    addFilterOptions(topicsOptions, topics, 'topic', supportTicketFilters);
    addFilterOptions(departmentsOptions, departments, 'department', supportTicketFilters);
    addFilterOptions(authorsOptions, authors, 'author', supportTicketFilters);

    new PageFilters(
        '#tickets-filter',
        [
            {
                id: 'status',
                label: t('Status'),
                options: [
                    {
                        label: t('All'),
                        value: 'all',
                        selected: supportTicketFilters['status'] === 'all',
                    },
                    {
                        label: t('Open'),
                        value: 'open',
                        selected: supportTicketFilters['status'] === 'open',
                    },
                    {
                        label: t('Closed'),
                        value: 'closed',
                        selected: supportTicketFilters['status'] === 'closed',
                    },
                ],
                optional: false,
                visible: true,
                closedValue: 'active',
            },
            {
                id: 'replyneeded',
                label: t('Reply Status'),
                options: [
                    {
                        label: t('All'),
                        value: 'all',
                        selected: supportTicketFilters['replyStatus'] === 'all',
                    },
                    {
                        label: t('Reply Needed'),
                        value: 'yes',
                        selected: supportTicketFilters['replyStatus'] === 'yes',
                    },
                    {
                        label: t('Replied'),
                        value: 'no',
                        selected: supportTicketFilters['replyStatus'] === 'no',
                    },
                ],
                optional: false,
                visible: true,
                closedValue: 'active',
            },
            {
                id: 'topic',
                label: t('Topic'),
                options: [
                    {
                        label: t('All'),
                        value: 'all',
                        selected: supportTicketFilters['topic'] === 'all',
                    },
                    ...topicsOptions,
                ],
                optional: false,
                visible: true,
                closedValue: 'active',
                searchBox: true
            },
            {
                id: 'department',
                label: t('Department'),
                options: [
                    {
                        label: t('All'),
                        value: 'all',
                        selected: supportTicketFilters['department'] === 'all',
                    },
                    ...departmentsOptions,
                ],
                optional: false,
                visible: true,
                closedValue: 'active',
                searchBox: departmentsOptions.length > 6
            },
            {
                id: 'author',
                label: t('Author'),
                options: [
                    {
                        label: t('All'),
                        value: 'all',
                        selected: supportTicketFilters['author'] === 'all',
                    },
                    ...authorsOptions,
                ],
                optional: false,
                visible: true,
                closedValue: 'active',
                searchBox: authorsOptions.length > 6
            },
        ],
        (filters) => {
            supportTicketFilters = { ...supportTicketFilters, ...filters };
            // getAllAgreements();
            ticketFilters = supportTicketFilters;
            filterTickets();
        },
        true
    );
}

jQuery(document).ready(function () {
  $(document).on('click','.status-pages a', function() {
    const target = $(this).data('target');
    const tab = $(`#support-page-tabs a[href="${target}"]`);
    $(tab).tab('show');
  })

  $(document).on('click', '#support-page-tabs a[data-toggle="tab"]', function (e) {
    const target = $(this).attr('href')
    const route = target.slice(1);

    sammy.quiet = true;

    if(target.includes('support-tickets')) {
      sammy.setLocation(`/support`);
      return
    }
    
    sammy.setLocation(`/support/${route}`)
  })
});
