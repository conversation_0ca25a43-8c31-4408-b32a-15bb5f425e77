let clubStripeSettingsTable;

function sStripeCustomerMapping() {
    const exportFilename = 'Stripe Customer ID_' + new Date().toISOString();
    let tableData = [];

    const qsObject = getQueryStringsObject();
    const filters = {
        search: qsObject.search || '',
        status: qsObject.status || 'active',
    };

    async function init() {
        renderSearchNav();
        loadSettingsTable();
    }

    function loadSettingsTable() {
        $('#stripe-club-settings-loader').html('');
        [1, 2, 3, 4, 5].forEach(() => {
            $('#stripe-club-settings-loader').append(`
                <tr>
                    <td><span class="use-skeleton rounded">Xxxxxxxxx</span></td>
                    <td><span class="use-skeleton rounded">cus_xxxxxxxxxxxxxxxx</span></td>
                    <td><span class="use-skeleton rounded">xxxxxxxxxxxxxxx</span></td>
                    <td><span class="use-skeleton rounded"></span></td>
                    <td><span class="use-skeleton rounded"></span></td>
                </tr>
            `);
        });

        if (clubStripeSettingsTable) {
            clubStripeSettingsTable.clear();
            clubStripeSettingsTable.destroy();
        }

        function createDataTable() {
            clubStripeSettingsTable = $('#stripe-club-settings-table').DataTable({
                responsive: true,
                paging: false,
                dom: 'rtip',
                buttons: [
                    { extend: 'copy', title: exportFilename },
                    { extend: 'print', title: exportFilename },
                    { extend: 'csv', title: exportFilename },
                    { extend: 'pdf', title: exportFilename },
                ],
                language: {
                    info: 'Showing _TOTAL_ entries',
                    infoEmpty: '',
                    infoFiltered: '',
                    lengthMenu: '',
                },
                pagingType: 'simple',
                serverSide: false,
                order: [[1, 'desc']],
                autoWidth: false,
                columns: [
                    { data: null, render: (_, __, row) => row.name ?? row.id },
                    {
                        data: 'customerID',
                        render: (customerId, _, row) => {
                            if (!customerId) return '';
                            const div = document.createElement('div');
                            $(div).addClass('d-flex gap-1');
                            $(div).append(`<div class="copy-id-container"><code>${customerId}</code></div>`);

                            if (row.customerCreatedByPH) {
                                $(div).append(`
                                    <span class="clickable" data-toggle="tooltip" title="${t('Auto generated by Performance Hub')}">
                                        <i class="fas fa-laptop-house"></i>
                                    </span>
                                `);
                            }

                            return div.outerHTML;
                        },
                    },
                    {
                        data: 'primaryPaymentMethod',
                        render: (primaryPaymentMethod) => {
                            if (!primaryPaymentMethod?.card) return `<span class="badge badge-red">${t('No Stored Cards')}</span>`;
                            const { brand, last4 } = primaryPaymentMethod.card || {};
                            return `
                                <div class="d-flex gap-1">
                                    <img src="/assets/images/payment_methods/${brand}.png" width="20px">
                                    <span style="letter-spacing: 0.5rem;">••••</span>
                                    <span style="letter-spacing: 0.5rem;">${last4}</span>
                                </div>
                            `;
                        },
                    },
                    {
                        data: null,
                        render: (_, __, row) => {
                            if (row.pauseBilling) return `<span class="badge badge-grey">${t('Billing Paused')}</span>`;
                            return '';
                        },
                    },
                    {
                        data: null,
                        render: (_, __, row) => {
                            if (row.lockedOut) return `<span class="badge badge-red">${t('Locked Out')}</span>`;
                            return '';
                        }
                    }
                ],
                fnDrawCallback: function () {
                    $('[data-toggle="tooltip"]').tooltip();
                },
            });

            wrapTableWithScrollableDiv('#stripe-club-settings-table_wrapper');

            clubStripeSettingsTable.on('click', 'tr', function (e) {
                const row = clubStripeSettingsTable.row(this).data();
                if (!row || !row.id) return;
                return editCustomerIDDialog(row.id);
            });

            $('.search-bar').on('input change', (e) => {
                const search = e.target.value;
                updateHrefQuery({ search });

                clubStripeSettingsTable?.search(search)?.draw();
            });
        }

        createDataTable();

        $('#stripe-club-settings-table > tbody').hide();
        $('#stripe-club-settings-loader').show();

        // Fetch the data
        $.ajax({
            url: '/api/saas-billing/payment-settings/customer-ids',
            type: 'GET',
            success: function(response) {
                const data = response.data;
                tableData = data;

                $('#stripe-club-settings-table > tbody').show();
                $('#stripe-club-settings-loader').hide();

                clubStripeSettingsTable.rows.add(data);
                clubStripeSettingsTable.draw();
            },
            error: function(xhr, status, error) {
                console.error('Failed to fetch data:', error);
            }
        });
    }

    const exportButtons = [
        { label: 'COPY', icon: 'fa-copy', onClick: () => clubStripeSettingsTable && clubStripeSettingsTable.button(0).trigger() },
        { label: 'PRINT', icon: 'fa-print', onClick: () => clubStripeSettingsTable && clubStripeSettingsTable.button(1).trigger() },
        { label: 'CSV', icon: 'fa-file-csv', onClick: () => clubStripeSettingsTable && clubStripeSettingsTable.button(2).trigger() },
        { label: 'PDF', icon: 'fa-file-pdf', onClick: () => clubStripeSettingsTable && clubStripeSettingsTable.button(3).trigger() },
    ];

    function renderSearchNav() {
        searchbarHeader('.search-nav-container', [
            ...exportButtons,
            //{ label: 'Show Unpublished', icon: 'fa-file-pdf', onClick: () => clubStripeSettingsTable && clubStripeSettingsTable.button(3).trigger() },
        ], {
            usePhTheme: true,
            expandableButtons: {
                startIndex: 0,
                count: exportButtons.length,
                label: t('Export'),
                icon: 'fa-ellipsis-v',
                id: 'facilities-export-buttons',
            },
        });
        if (filters.search) $('.search-bar').val(filters.search);
    }

    function editCustomerIDDialog(settingID) {
        const setting = tableData.find((s) => s.id === settingID);
        if (!setting) return;

        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');

        const pauseBillingChecked = setting.pauseBilling ? '' : 'checked';
        const lockedOutChecked = setting.lockedOut ? 'checked' : '';

        const currentIDHtml = setting.customerID
            ? `
                <div>
                    <label>${t('Current Customer ID')}</label><br>
                    <div class="id-container">
                        <code>${setting.customerID}</code>
                    </div>
                </div>
                <br>
            ` : '';

        $(html).html(`
            <div class="body">
                <div class="d-flex flex-column gap-1">
                    <div class="demo-switch pause-billing-container">
                        <div class="switch">
                            <label style="min-width: max-content;">
                                <input data-hj-allow type="checkbox" name="pauseBilling" ${pauseBillingChecked}>
                                <span class="lever lever switch-col-blue" style="margin-left: 0px;"></span>
                                ${t('Turn on/off automatic billing')}
                            </label>
                        </div>
                    </div>
                    <div class="demo-switch locked-out-container">
                        <div class="switch">
                            <label style="min-width: max-content;">
                                <input data-hj-allow type="checkbox" name="lockedOut" ${lockedOutChecked}>
                                <span class="lever lever switch-col-red" style="margin-left: 0px;"></span>
                                ${t('Lock/Unlock access to Performance Hub')}
                            </label>
                        </div>
                    </div>
                </div>
                <hr>
                ${currentIDHtml}
                ${textField({
                    label: t('New Customer ID'),
                    placeholder: 'cus_xxxxxxxxxxxxxx',
                    name: 'customerID',
                })}
            </div>
        `);

        if (setting.customerID) {
            const checked = setting.customerCreatedByPH && !setting.primaryPaymentMethod ? 'checked' : '';
            const warningDisplay = checked === 'checked' ? 'block' : 'none';

            $(html).find('.body').append(`
                <br>
                <div class="form-check styled-checkbox">
                    <input type="checkbox" class="filled-in chk-col-blue" id="deleteSourceID" ${checked}>
                    <label class="form-check-label" for="deleteSourceID">${t('Delete Stripe Customer ID')}</label>
                </div>
                <div class="delete-source-id-warning" style="display: ${warningDisplay};">
                    <br>
                    <div>
                        <em>${t('Warning! This will delete the current customer from stripe and all associated payment methods of the new customer will be transferred to this club.')}</em>
                    </div>
                    <br>
                    <div>${t("Would you like to proceed?")}</div>
                </div>
            `);
        }

        showDeleteSourceIDWarning();
        addClipboardCopyButtons($(html).find('.id-container'));

        $(html)
            .find('#deleteSourceID')
            .on('change', () => showDeleteSourceIDWarning());

        $(html).find('.body').append(`
            <div  style="margin-top: 4px;">
                <a class="view-club-invoices" href="#">
                    ${t('Show invoices for this club')} <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        `);

        $(html)
            .find('.view-club-invoices')
            .on('click', (e) => {
                e.preventDefault();
                const url = `/saas-billing-admin/invoice-history?club=${setting.id}&month=all`;
                
                sammy.quiet = false;
                sammy.setLocation(url);
                Swal.close();
            });

        async function showDeleteSourceIDWarning() {
            if (!setting.customerID) return;
            const isChecked = $(html).find('#deleteSourceID').is(':checked');
            if (isChecked) return $('.delete-source-id-warning').show();
            $('.delete-source-id-warning').hide();
        }

        async function handleSubmit() {
            const data = {
                lockedOut: $(html).find('input[name=lockedOut]').is(':checked'),
                pauseBilling: !$(html).find('input[name=pauseBilling]').is(':checked'),
            }

            const newCustomerID = $(html).find('input[name=customerID]').val();
            if (newCustomerID.replace(/\s/g, '') !== '') {
                data.customerID = newCustomerID;
                data.deleteSourceID = $(html).find('#deleteSourceID').is(':checked');
            }

            const res = await updateClubSaasBillingPaymentSettings(setting.id, data);
            if (!res[0]) return Swal.showValidationMessage(res[1]);
            return true;
        }

        swalWithBootstrapButtons.fire({
            title: t('Update Stripe Settings'),
            html,
            showCancelButton: true,
            showCloseButton: true,
            confirmButtonText: t('Save'),
            cancelButtonText: t('Cancel'),
            preConfirm: handleSubmit,
        }).then(({ value }) => {
            if (!value) return;
            instantNotification(t('Settings updated'), 'success');
            init();
        });
    }

    init();
}
