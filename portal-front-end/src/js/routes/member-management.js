function sMMS() {

    // Make the side nav small on this page...
    // if(smallSidebar == false) toggleSmallNav();

    loadingOverlay('.iframe-placeholder')

    $('.iframe-placeholder iframe').ready(function() {
        setTimeout(function(){
            $(".iframe-placeholder iframe").show();
            $('.iframe-placeholder').LoadingOverlay("hide");
        }, 800);
    });

    $('.mms-popout button').on('click', function () {
        window.open($('#mmsContainer').contents().get(0).location.href,'Member Management',`height=${screen.availHeight},width=${screen.availWidth},resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=yes,directories=no,status=no`);
    });
}

function sMMSURI() {
    sammy.quietRoute("/mms-ui/#" + encodeURIComponent($('#mmsContainer').contents().get(0).location.href.replace(document.location.origin, '')));
}


function reloadMMSiframe() {
    $('#mmsContainer').attr('src', `/mms/?club=${selectedID}`);
}