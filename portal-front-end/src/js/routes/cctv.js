function sCCTV() {
    // get path value from url from the hash. hash will look like this #%2Fcctv%2Fpeople. get only the path after cctv%2F
    const newPath = decodeURIComponent(window.location.hash.split('cctv%2F').slice(-1));

    if(newPath) {
      reloadCCTViframe(newPath);
    }

    addContainerClasses('#cctv');
    $('.cctv-skeleton-loader').show();
    if (enableModalBackdrop) enableModalBackdrop($('#cctv'));

    $('.iframe-placeholder iframe').ready(function () {
        setTimeout(function () {
            // $('.iframe-placeholder').LoadingOverlay('hide');
            $('main-container').addClass('cctv-ui');
            console.log('cctv ui loaded');
        }, 800);
    });
}

function sCCTVURI() {
    sammy.quietRoute(
        '/cctv-ui/#' +
            encodeURIComponent(
                $('#cctvContainer').contents().get(0).location.href.replace(document.location.origin, '')
            )
    );
}

function reloadCCTViframe(path) {
    if(path) {
      $('#cctvContainer').attr('src', '/cctv/' + path);
    } else {
        $('#cctvContainer').attr('src', $('#cctvContainer').attr('src')); // Refresh the iframe by resetting its src
    }
}


// function reloadCCTViframe(path) {

//     let container = $('#cctvContainer');
//     let currentSrc = container.attr('src');
//     let newSrc = path ? ('/cctv/' + path) : currentSrc;

//     // Recreate iframe to prevent memory leaks when changing between clubs
//     if (currentSrc === newSrc) {
//         let newIframe = $('<iframe>', {
//             id: 'cctvContainer',
//             src: newSrc,
//             frameborder: 0,
//             scrolling: 'no',
//             width: container.width(),
//             height: container.height()
//         });

//         container.replaceWith(newIframe);
//     } else {
//         container.attr('src', newSrc);
//     }
// }