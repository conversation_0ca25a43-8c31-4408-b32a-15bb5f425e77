function healthCheckIframe() {
  window.addEventListener('message', function (event) {
      if (typeof event.data == 'object') {
          return;
      }

      if (event.data) {
          const dataObj = JSON.parse(event.data);

          if (dataObj.type !== 'healthCheck') {
              return;
          }

          if (dataObj.route) {
              firstLoad = true;
              sammy.quiet = false;
              sammy.setLocation(dataObj.route);
              firstLoad = false;
          }

      }
  });
}

function shealthCheck() {
  loadingOverlay('.iframe-placeholder');

  // Update the device stats object if it's not yet set.
  $.sDevices.base.fetchDeviceStats()

  $('.iframe-placeholder iframe').ready(function () {
      setTimeout(function () {
          $('.iframe-placeholder').LoadingOverlay('hide');
          $('main-container').addClass('health-check-ui');
          if (enableModalBackdrop) enableModalBackdrop($('#health-check'));
      }, 800);
  });

  healthCheckIframe();
}

function shealthCheckURI() {
  sammy.quietRoute(
      '/health-check-ui/#' +
          encodeURIComponent(
              $('#healthCheckContainer').contents().get(0).location.href.replace(document.location.origin, '')
          )
  );
}

function reloadhealthCheckiframe() {
  $('#healthCheckContainer').attr('src', '/health-check/');
}
