
let ticketInfo = {};
let debounceTime = 500;
let ticketDepartments = [];
let ticketTopics = [];
let replyAttachments = [];
let ticketFilters = {
  sortDate: 'newest',
  sorting: 'lastreply',
};
let createTicketUploadTotalSize = 0;

const UPLOAD_MAX_TOTAL_SIZE = 99000000;

let hiddenTopics = [
    'members - general',
    'orders - clubs / automated order forms',
    'catchall messages',
]
let hiddenDepartments = [
    'maintenance'
]

$.sTicketing = {};

$.sTicketing.base = {
    isInitialised: false,
    init: function(ticketNumber) {

        // On init...
        getSearchTopicsPinned();
        getTicketDepartments();
        getTicketTopics();
        fetchFilterOptions();
        refreshTickets();

        let departmentsCache;

        $.sTicketing.base.isInitialised = true;

        // --------------------------- EVENTS ---------------------------------------------------------------------------------------
        let optionsFilter = [];
        let optionsTopic = [];
        let optionsDepartment = [];
        let optionsAuthor = [];

        let statusPageOutages = {}

        searchbarHeader(
            '.searchbar-container',
            [{ label: t('New Support Ticket'), icon: 'add', onClick: createTicketSelectEmail }]
        );
        
        // open modal
        $('.add-new-ticket').click(createTicketSelectEmail)

        $( '#dropdownStatus a' ).on( 'click', function( event ) {
            var $target = $( event.currentTarget ),
            val = $target.attr( 'data-value' );
            if(val == "remove") {
                clearStatusFilter();
            } else if(val =="open"){
                $('#btnToggleStatusFilter').html(`<i class="material-icons">lock_open</i> ${t('Open')} <span class="caret"></span>`);
                ticketFilters.status = val;
            } else if(val == "closed") {
                $('#btnToggleStatusFilter').html(`<i class="material-icons">lock</i> ${t('Closed')} <span class="caret"></span>`);
                ticketFilters.status = val;
            }

            filterTickets();

        });

        $( '#dropdownReplyNeeded a' ).on( 'click', function( event ) {
            var $target = $( event.currentTarget ),
            val = $target.attr( 'data-value' );
            if(val == "remove") {
                clearReplyStatusFilter();
            } else if(val == "reply_needed") {
                $('#btnToggleReplyNeededFilter').html(`<i class="material-icons">reply_all</i> ${t('Reply Needed')} <span class="caret"></span>`);
                ticketFilters.replyneeded = "yes";
            } else if(val == "replied") {
                $('#btnToggleReplyNeededFilter').html(`<i class="material-icons">message</i> ${t('Replied')} <span class="caret"></span>`);
                ticketFilters.replyneeded = "no";
            }

            filterTickets();

        });

        $( '#dropdownAddFilter li' ).on( 'click', function( event ) {

            var $target = $( event.currentTarget ),
                $inp = $target.find( 'input' ),
                idx;

            let val = $inp.data('value');

            if(val) {

                if ( ( idx = optionsFilter.indexOf( val ) ) > -1 ) {
                    optionsFilter.splice( idx, 1 );
                    setTimeout( function() { $inp.prop( 'checked', false ) }, 0);
                    switch (val) {
                        case "status":
                            clearStatusFilter();
                            break;
                        case "reply_status":
                            clearReplyStatusFilter();
                            break;
                        case "topic":
                            clearTopicFilter();
                            break;
                        case "department":
                            clearDepartmentFilter();
                            break;
                        case "author":
                           clearAuthorFilter();
                            break;
                    }

                    //reload tickets after hiding filters
                    filterTickets();

                } else {
                    optionsFilter.push( val );
                    setTimeout( function() { $inp.prop( 'checked', true ) }, 0);
                    switch (val) {
                        case "status":
                            $('#filterStatusContainer').show(100);
                            break;
                        case "reply_status":
                            $('#filterReplyStatusContainer').show(100);
                            break;
                        case "topic":
                            $('#filterTopicContainer').show(100);
                            break;
                        case "department":
                            $('#filterDepartmentContainer').show(100);
                            break;
                        case "author":
                            $('#filterAuthorContainer').show(100);
                            break;
                    }
                }


            } else {
                //remove all filters
                optionsFilter = [];
                $(this).parent().find('input[type=checkbox]').prop('checked',false);

                clearStatusFilter();
                clearReplyStatusFilter();
                clearTopicFilter();
                clearDepartmentFilter();
                clearAuthorFilter();

                $( event.target ).blur();
                return true;
            }

            $( event.target ).blur();
            return false;

        });

        $('#btnSortByDate').click(function() {
            let sortby = $(this).data('sortby');
            if(sortby == "down") {
                $('#iconSort').html('<i class="material-icons">arrow_upward</i>');
                $(this).data('sortby', 'up');
                ticketFilters.sortDate = "newest";
            } else {
                $('#iconSort').html('<i class="material-icons">arrow_downward</i>');
                $(this).data('sortby', 'down');
                ticketFilters.sortDate = "oldest";
            }
            filterTickets();
        });

        //user types on the search box
        $(document).on('keyup change', '#inputSearchWiki', function(e) {

            //just making sure the pressed keys are alphanumeric so it won't fire the search on other keys
            var keyCode = e.keyCode == 0 ? e.charCode : e.keyCode;
            if( ((keyCode >= 48 && keyCode <= 57) || (keyCode >= 65 && keyCode <= 90) || (keyCode >= 97 && keyCode <= 122) || (keyCode == 32) || (keyCode == 8) )) {

                clearTimeout(searchDebounce)
                searchDebounce = null
                searchDebounce = setTimeout(() => {

                    let searchTerm = $(this).val();

                    getSearchTopics(searchTerm);

                }, debounceTime)

            }
        });

        //when user clears the text in search wiki
        $('#inputSearchWiki').on('search', function () {
            getSearchTopics('');
        });

        function searchTickets(keyword){

            let searchTicketResults = [];

            if(keyword.length > 0) {
                if(selectedClubObj.tickets && selectedClubObj.tickets.length > 0) {
                    //filter the selectedClub.tickets and create a new copy to display it temporarily
                    searchTicketResults = selectedClubObj.tickets.filter(x => x.subject.toLowerCase().includes(keyword) );

                    displayTickets(searchTicketResults, 'search', keyword);

                }

            } else {
                displayTickets(selectedClubObj.tickets);
            }
        }

        //user presses backspace in search tickets
        $(document).on('input', '.searchbar-container .search-bar, .support-page__search-field', function(e) {
            const keyword = $(this).val();

            searchTickets(keyword);

            if(keyword) {
              $(this).addClass('filled');
              return
            } 

            $(this).removeClass('filled');
        });

        $('.support-page__search-field__clear').on('click', function() {
          $('.support-page__search-field').val('').removeClass('filled').focus();
          searchTickets('');
        })

        //user clicks the Ticket Subject link
        $(document).off('click', '.divTicketHeader').on('click', '.divTicketHeader', function() {
            //page re=routing
            sammy.quiet = false;
            sammy.setLocation(`/support/${ $(this).data('ticketnumber') }`);
        });

        $(document).off('click', '#btnBackToAllTickets').on('click', '#btnBackToAllTickets', function() {
            $(this).css('visibility', 'hidden');
            $('#divTicketDetailsSection').empty();

            $('#divTicketDetailsSection').css('visibility', 'hidden');

            //show the tickets again
            $('#divTicketsSection').show(100);
            $('#divSearchWiki').show(100);

            sammy.quietRoute(`/support`);
        })
        
        // User submits reply to a ticket
        $(document).off('click', '#btnSubmitReply').on('click', '#btnSubmitReply', function() {
            $(this).attr('disabled', 'disabled');

            loadingOverlay('#replyContainer');

            let ticketNumber = $('.support-page__single-ticket__content').data('ticketnumber');
            //reply data here......
            let replyObject = {

                _method: "POST",
                ticketNumber : ticketNumber,
                msgId : "",
                a : "reply",
                emailreply : "1",
                emailcollab : "1",
                cannedResp : "0",
                draft_id : "",
                response : $('#replyMessageBody').trumbowyg('html'),
                signature : "none",
                reply_status_id : "1",
                staffUserName : signedinUser,
                ip_address : "::1",
                attachments : replyAttachments

            }


            var req = $.ajax({
                url: `/api/ticketing/user-reply/${signedinUser}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(replyObject),
                success: async function(res){
                    $('#replyContainer').LoadingOverlay("hide");


                    if((res === undefined) ? false : res !== false) {
                        if(res.response.length > 0){
                            instantNotification(t('Reply Posted!'));

                            // Add elements to indicate the reply is refreshing/loading...
                            $('#divTicketDetailsBody')
                            .append(`
                                <div class="card">
                                    <div class="header ticketThreadItem thread-reply-updating">
                                        <center><b>Updating Ticket...</b></center>
                                    </div>
                                </div>
                            `)
                            loadingOverlay('.thread-reply-updating');

                            // Refresh tickets without updating the UI (UI will be updated when we try run the .load(<TICKET-ID>) function below)
                            await getClientTickets2(false);

                            // Clear any attachments
                            replyAttachments = [];

                            // Must reload ticket threads here (note we need to await the getClientTickets2 above for this to work)
                            $.sTicketing.getTicketInfo.load(ticketNumber);
                        }
                    } else {
                        instantNotification(
                            t('<b>FAILED TO POST REPLY!</b><br><br>An error occurred while trying to add/save your reply.<br>Please contact HQ for further details.'),
                            'error',
                            60000
                        );
                    }
                    $(this).attr('disabled', false);
                },
                error: function(xhr, textStatus, errorThrown){
                    $('#replyContainer').LoadingOverlay("hide");
                    console.error(xhr, textStatus, errorThrown);
                    instantNotification(
                        // Message...
                        t('<b>FAILED TO POST REPLY!</b><br><br>An error occurred while trying to add/save your reply.<br>Please contact HQ for further details.'),
                        // Type...
                        'error',
                        // Timeout (1m)...
                        60000
                    );
                    $(this).attr('disabled', false);
                },
                // complete: function(xhr, textStatus){
                //     console.log(xhr);
                //     $(this).attr('disabled', false);
                // }
            });


        });

        $(document).off('change', '#filterSortDate').on('change', '#filterSortDate', function() {
            let val = $(this).val();

            if(selectedClubObj.tickets && selectedClubObj.tickets.length > 0 && val != ""){
                ticketFilters.sortDate = $(this).val();

                filterTickets();
            }
        });

        $(document).off('click', '#expandSubtractTickets').on('click', '#expandSubtractTickets', function() {
            toggleExpandSubtractAllTickets();
        })

        // change the items sorting
        $(document).on('click', '.support-page .sort-items', function() {
          const currentId = $(this).attr('id');
          const sortingVal = currentId.split('-')[1];
          const sortItems = $('.support-page .sort-items');

          sortItems.removeClass('active asc');
          
          $(this).addClass('active');

          const sorting = ticketFilters.sortDate === 'newest' ? 'oldest' : 'newest';
          ticketFilters.sortDate = sorting;
          ticketFilters.sorting = sortingVal;
          supportTicketFilters = { ...supportTicketFilters, ...ticketFilters }

          

          if(sorting === 'oldest') {
            $(this).addClass('asc');
          }
          filterTickets();
        });

        if(ticketNumber) {
          $(`#support-page-tabs a[href="#${ticketNumber}"]`).tab('show');
        }

    // ---------------------------- FUNCTIONS ------------------------------------------------------------------------------------
        function clearStatusFilter(){
            $('#filterStatusContainer').hide(100);
            $('#btnToggleStatusFilter').html(`<i class="material-icons">check</i> ${t('Status')} <span class="caret"></span>`);
            $('#showHideStatusFilter').prop('checked',false);
            ticketFilters.status = "";
        }

        function clearReplyStatusFilter(){
            $('#filterReplyStatusContainer').hide(100);
            $('#btnToggleReplyNeededFilter').html(`<i class="material-icons">reply</i> ${t('Reply Status')} <span class="caret"></span>`);
            $('#showHideReplyStatusFilter').prop('checked',false);
            ticketFilters.replyneeded = "";
        }

        function clearTopicFilter(){
            optionsTopic = [];
            $('#filterTopicContainer').hide(100);
            $('#dropdownTopic').find('input[type=checkbox]').prop('checked',false);
            $('#showHideTopicFilter').prop('checked',false);
        }

        function clearDepartmentFilter(){
            optionsDepartment = [];
            $('#filterDepartmentContainer').hide(100);
            $('#dropdownDepartment').find('input[type=checkbox]').prop('checked',false);
            $('#showHideDepartmentFilter').prop('checked',false);
        }

        function clearAuthorFilter(){
            optionsAuthor = [];
            $('#filterAuthorContainer').hide(100);
            $('#dropdownAuthor').find('input[type=checkbox]').prop('checked',false);
            $('#showHideAuthorFilter').prop('checked',false);
        }

        function toggleExpandSubtractAllTickets() {

            if($('#expandSubtractTickets').hasClass('toggled')) {

                $('#expandSubtractTickets').html(`
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M11.6464 3.39645C11.8417 3.20118 12.1583 3.20118 12.3536 3.39645L17.6036 8.64645C17.7988 8.84171 17.7988 9.15829 17.6036 9.35355C17.4083 9.54882 17.0917 9.54882 16.8964 9.35355L12 4.45711L7.10355 9.35355C6.90829 9.54882 6.59171 9.54882 6.39645 9.35355C6.20118 9.15829 6.20118 8.84171 6.39645 8.64645L11.6464 3.39645ZM6.39645 14.6464C6.59171 14.4512 6.90829 14.4512 7.10355 14.6464L12 19.5429L16.8964 14.6464C17.0917 14.4512 17.4083 14.4512 17.6036 14.6464C17.7988 14.8417 17.7988 15.1583 17.6036 15.3536L12.3536 20.6036C12.1583 20.7988 11.8417 20.7988 11.6464 20.6036L6.39645 15.3536C6.20118 15.1583 6.20118 14.8417 6.39645 14.6464Z" fill="currentColor"/>
                  </svg> 
                `);

                $(".ticket-header:not(:last), .ticketThreadItem:not(:last)").each(function() {
                    toggleExpandSubtractTickets(this, 'subtract');
                });

                $('#expandSubtractTickets').removeClass("toggled")

            } else {

                $('#divTicketDetailsBody .support-page__ticket-msg-item.hidden').removeClass('hidden');
                $('.support-page__ticket-count').remove();
                $('#divTicketDetailsBody').removeClass('has-hidden-threads');

                $('#expandSubtractTickets').html(` 
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M6.39645 4.14645C6.59171 3.95118 6.90829 3.95118 7.10355 4.14645L12 9.04289L16.8964 4.14645C17.0917 3.95118 17.4083 3.95118 17.6036 4.14645C17.7988 4.34171 17.7988 4.65829 17.6036 4.85355L12.3536 10.1036C12.1583 10.2988 11.8417 10.2988 11.6464 10.1036L6.39645 4.85355C6.20118 4.65829 6.20118 4.34171 6.39645 4.14645ZM6.39645 19.1464L11.6464 13.8964C11.8417 13.7012 12.1583 13.7012 12.3536 13.8964L17.6036 19.1464C17.7988 19.3417 17.7988 19.6583 17.6036 19.8536C17.4083 20.0488 17.0917 20.0488 16.8964 19.8536L12 14.9571L7.10355 19.8536C6.90829 20.0488 6.59171 20.0488 6.39645 19.8536C6.20118 19.6583 6.20118 19.3417 6.39645 19.1464Z" fill="currentColor"/>
                  </svg>
                `);
                // Expand all tickets...
                $(".ticket-header, .ticketThreadItem").each(function() {
                    toggleExpandSubtractTickets(this, 'expand');
                });
                $('#expandSubtractTickets').addClass("toggled")
            }

        }


        function toggleExpandSubtractTickets(el,arg=false) {
            let currentHeight = $(el).parent().height();

            function expandItem() {
              $(el).addClass('expanded');
                const $iframe = $(el).find('iframe');
                const iframe =  $iframe.get(0);
                iframe.height = iframe.contentWindow.document.body.scrollHeight + "px";
            }
            
            // thread item clicked
            if($(el).hasClass('ticketThreadItem')) {
              if(arg === 'expand') {
                expandItem();
                return;
              }

              if($(el).hasClass('expanded')) {
                $(el).removeClass('expanded');
              } else {
                expandItem();
              }
              return;
            }

            if(!arg) {
                if(currentHeight >= 100) {
                    hide();
                } else {
                    show();
                }
            } else {
                switch(arg) {
                  case 'expand':
                    show();
                    break;
                  case 'subtract':
                    hide();
                    break;
                }
            }

            function hide() {
                $(el).parent().css({"height": "65px", "overflow": "hidden"});
                $(el).find('.ticket-small-summary-contents').show();
            }
            function show() {
                $(el).parent().css({"height": "auto", "overflow": "auto"});
                $(el).find('.ticket-small-summary-contents').hide();
            }
        }

        function displaySearchResults(resultsArray) {

            $('#divSearchResults').LoadingOverlay("hide");

            console.log('resultsArray', resultsArray)
            //populate wiki search
            if(resultsArray) {
                resultsArray.map(res => {
                    let mainUrl = `/wiki/#%2Fopenkb%2Fkb%2F/${res._id}`;
                    let wikiSummary = $($.parseHTML(res._snippetResult.kb_body.value)).text();

                    $('#divSearchResults').append(`
                        <div class="header resultItem" data-url="${mainUrl}" style="margin-bottom: 10px">
                            <h2> ${ res.kb_title }</h2>
                            <small class="wiki-summary"> ${ wikiSummary } </small>
                        </div>
                    `);
                });
            }

            $("#divSearchResults .resultItem").click(function() {
                firstLoad = true; sammy.quiet = false;
                sammy.setLocation($(this).data('url'));
                firstLoad = false;
            });
        }

        function getTicketTopics() {
            var req = $.ajax({
                url: `/api/ticketing/topics`,
                method: 'GET',
                success: function(data){
                    //the returned data is not an array, but objet, so, iterate thru each property
                    if(data){
                        ticketTopics = [];
                        let topic = data.data;

                        for (var d in topic) {
                            if (Object.prototype.hasOwnProperty.call(topic, d)) {
                                ticketTopics.push({
                                    topicId: d,
                                    topicName: topic[`${d}`]
                                })

                                // $('#filterTopics').append(
                                //     $('<option>', {
                                //         value: topic[`${d}`],
                                //         text: topic[`${d}`]
                                //     })
                                // );
                                $('#dropdownTopic').append(
                                    $(`<li class="filterLi">
                                        <div class="form-check styled-checkbox">
                                            <input type="checkbox" class="form-check-input filled-in chk-col-blue" data-value="${topic[`${d}`]}" id="topiccheckbox-${topic[`${d}`]}">
                                            <label class="form-check-label" for="deptcheckbox-${topic[`${d}`]}">${topic[`${d}`]}</label>
                                        </div>
                                    </li>
                                    `)
                                );
                            }
                        }
                        //remove filter link
                        $('#dropdownTopic').append($(`<li role="separator" class="divider"></li>`));

                        $('#dropdownTopic').append(
                            $(`<li>
                                <a href="#" class="filterItem" data-value="remove" tabIndex="-1"><i class="material-icons">close</i> ${t('Remove Filter')}</a>`
                            ));

                        $( '#dropdownTopic li' ).on( 'click', function( event ) {

                            var $target = $( event.currentTarget ),
                                //val = $target.attr( 'data-value' ),
                                $inp = $target.find( 'input' ),
                                idx;

                            let val = $inp.data('value');

                            if(val){
                                if ( ( idx = optionsTopic.indexOf( val ) ) > -1 ) {
                                    optionsTopic.splice( idx, 1 );
                                    setTimeout( function() { $inp.prop( 'checked', false ) }, 0);
                                } else {
                                    optionsTopic.push( val );
                                    setTimeout( function() { $inp.prop( 'checked', true ) }, 0);
                                }

                                filterTickets();
                            }
                            else {
                                //remove selected
                                clearTopicFilter();

                                filterTickets();

                                $( event.target ).blur();
                                return true;
                            }

                            $( event.target ).blur();

                            return false;
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                }
            });
        }

        function getSearchTopicsPinned() {

            //clear the div first
            $('#divSearchResults').empty();


            loadingOverlay('#divSearchResults');

             var req = $.ajax({
                url: `/api/algolia/search-wiki`,
                method: 'POST',
                data: JSON.stringify({
                    searchTerms: "",
                    club: selectedID,
                }),
                success: function(res){
                   if(res.suggestedArticles){
                       displaySearchResults(res.suggestedArticles);
                   } else {
                    $('#divSearchResults').LoadingOverlay("hide");
                   }
                },
                error: function(xhr, textStatus, errorThrown){
                   console.error(xhr, textStatus, errorThrown);
                }
             });
        }

        function getTicketDepartments() {
            var req = $.ajax({
                url: `/api/ticketing/departments`,
                method: 'GET',
                //data: {ticketNumber},
                success: function(data){
                    if(data){
                        departmentsCache = data;
                        ticketDepartments = structureDepartments(data.data);
                        ticketDepartments.forEach(department => {
                            const departmentName = department.department_name;
                            $('#dropdownDepartment').append(
                                $(`<li class="filterLi">
                                    <div class="form-check styled-checkbox">
                                        <input type="checkbox" class="form-check-input filled-in chk-col-blue" data-value="${departmentName}" id="deptcheckbox-${departmentName}">
                                        <label class="form-check-label" for="deptcheckbox-${departmentName}">${departmentName}</label>
                                    </div>
                                </li>
                                `)
                            );
                        });

                        // let dept = data.data;
                        // //the returned data is not an array, but objet, so, iterate thru each property
                        // for (var d in dept) {
                        //     if (Object.prototype.hasOwnProperty.call(dept, d)) {
                        //         ticketDepartments.push({
                        //             deptId: d,
                        //             deptName: dept[`${d}`]
                        //         })

                        //         // $('#filterDepartments').append(
                        //         //     $('<option>', {
                        //         //         value: dept[`${d}`],
                        //         //         text: dept[`${d}`]
                        //         //     })
                        //         // );
                        //         $('#dropdownDepartment').append(
                        //             $(`<li class="filterLi">
                        //                 <div class="form-check">
                        //                     <input type="checkbox" class="form-check-input filled-in chk-col-blue" data-value="${dept[`${d}`]}" id="deptcheckbox-${dept[`${d}`]}">
                        //                     <label class="form-check-label" for="deptcheckbox-${dept[`${d}`]}">${dept[`${d}`]}</label>
                        //                 </div>
                        //             </li>
                        //             `)
                        //         );
                        //     }
                        // }

                        //remove filter link
                        $('#dropdownDepartment').append($(`<li role="separator" class="divider"></li>`));

                        $('#dropdownDepartment').append(
                            $(`<li>
                                <a href="#" class="filterItem" data-value="remove" tabIndex="-1"><i class="material-icons">close</i> ${t('Remove Filter')}</a>`
                            ));

                        $( '#dropdownDepartment li' ).on( 'click', function( event ) {

                            var $target = $( event.currentTarget ),
                                $inp = $target.find( 'input' ),
                                idx;

                            let val = $inp.data('value');

                            if(val) {
                                if ( ( idx = optionsDepartment.indexOf( val ) ) > -1 ) {
                                    optionsDepartment.splice( idx, 1 );
                                    setTimeout( function() { $inp.prop( 'checked', false ) }, 0);
                                } else {
                                    optionsDepartment.push( val );
                                    setTimeout( function() { $inp.prop( 'checked', true ) }, 0);
                                }
                                filterTickets();

                            } else {
                                //remove selected
                                clearDepartmentFilter();

                                filterTickets();

                                $( event.target ).blur();
                                return true;
                            }

                            $( event.target ).blur();
                            return false;
                        });

                    }
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                }
            });
        }

        function ticketItemsLoader(removeLoader = false) {
          const ticketWrapper = $('.support-page__items');
          
          if(removeLoader) {
            ticketWrapper.removeClass('isLoading');
            return;
          }

          const loader = `
          <a class="support-page__item support-page__item--Closed" data-ticketnumber="344-651-01">
            <div class="support-page__item__status-wrapper">
              <div class="support-page__item__ticket-id use-skeleton">Ticket #344-651-01</div>
              <div class="support-page__item__ticket-status use-skeleton">Status: Open</div>
            </div>
            <div class="support-page__item__title-wrapper">
              <h6 class="support-page__item__title use-skeleton">Test Support Ticket</h6>
              <div class="support-page__item__meta use-skeleton"><span class="support-page__item__meta-author"><svg width="11" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M8.043 5.25c.944.275 2.243 1.205 2.243 4.252 0 1.38-1.025 2.498-2.284 2.498H2.283C1.024 12 0 10.882 0 9.502 0 6.456 1.299 5.525 2.243 5.25a3.392 3.392 0 0 1-.529-1.821A3.435 3.435 0 0 1 5.143 0 3.435 3.435 0 0 1 8.57 3.429a3.39 3.39 0 0 1-.528 1.821ZM5.143.857a2.572 2.572 0 1 0 .001 5.144A2.572 2.572 0 0 0 5.143.857Zm2.86 10.286c.783 0 1.426-.73 1.426-1.64 0-2.11-.71-3.43-2.036-3.496a3.394 3.394 0 0 1-2.25.85 3.394 3.394 0 0 1-2.25-.85C1.567 6.074.857 7.393.857 9.502c0 .911.643 1.64 1.426 1.64h5.72Z" fill="currentColor"></path></g></svg> Test Club</span> <span class="support-page__item__meta-item"><svg width="12" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M1.143 11.143h9.428V4.286H1.143v6.857ZM3.714 3V1.071A.212.212 0 0 0 3.5.857h-.429a.212.212 0 0 0-.214.214V3c0 .12.094.214.214.214H3.5c.12 0 .214-.094.214-.214Zm5.143 0V1.071a.212.212 0 0 0-.214-.214h-.429A.212.212 0 0 0 8 1.071V3c0 .12.094.214.214.214h.429c.12 0 .214-.094.214-.214Zm2.572-.429v8.572a.863.863 0 0 1-.858.857H1.143a.863.863 0 0 1-.857-.857V2.57c0-.468.388-.857.857-.857H2v-.643C2 .482 2.482 0 3.071 0H3.5c.59 0 1.071.482 1.071 1.071v.643h2.572v-.643C7.143.482 7.625 0 8.214 0h.429c.59 0 1.071.482 1.071 1.071v.643h.857c.47 0 .858.389.858.857Z" fill="currentColor"></path></g></svg> Mar 01, 2024 3:41AM</span></div>
            </div>
            <div class="support-page__item__excerpt use-skeleton">Test ParagraphTest Paragraph 2Test Paragraph 3 ...
            </div>
            <div class="support-page__item-details">
              <div class="support-page__item__ticket-participants-wrapper">
                <div class="support-page__item__ticket-message-count use-skeleton">1 <svg width="19" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.866 10.417h2.656c.628 0 1.139-.477 1.139-1.063V2.98c0-.586-.511-1.062-1.139-1.062H2.656c-.627 0-1.138.476-1.138 1.062V13.25l4.047-2.833h4.301Zm-6.83-.071.117-.011-.117.082v-.071Z" fill="currentColor"></path><path d="M15.558 6.167h-.38v4.25c0 .78-.677 1.412-1.51 1.416H6.071v.354c0 .586.511 1.063 1.139 1.063h5.44l4.046 2.833V7.23c0-.586-.51-1.062-1.138-1.062Z" fill="currentColor"></path></svg></div>
                <div class="support-page__item__ticket-participants use-skeleton">Test Club</div>
              </div>
              <div class="support-page__item__ticket-departments">
                <span class="use-skeleton"><svg width="19" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><path d="M5.036 4.75A1.21 1.21 0 0 0 3.82 3.536 1.21 1.21 0 0 0 2.607 4.75a1.21 1.21 0 0 0 1.214 1.214A1.21 1.21 0 0 0 5.036 4.75Zm10.122 5.464c0 .323-.133.636-.35.854l-4.659 4.668c-.228.218-.54.35-.863.35-.323 0-.636-.132-.854-.35L1.65 8.943C1.165 8.469.786 7.548.786 6.875V2.929c0-.665.55-1.215 1.214-1.215h3.946c.674 0 1.594.38 2.078.864l6.783 6.773c.218.228.351.54.351.863Zm3.643 0c0 .323-.133.636-.351.854l-4.658 4.668c-.228.218-.541.35-.863.35-.493 0-.74-.227-1.063-.56l4.459-4.458a1.222 1.222 0 0 0 0-1.717L9.542 2.578c-.484-.484-1.404-.864-2.078-.864H9.59c.674 0 1.594.38 2.078.864L18.45 9.35c.*************.35.863Z" fill="#D5DBE1"></path></g></svg></span>
                <div class="support-page__item__ticket-badge use-skeleton">Accounts</div>
                <div class="support-page__item__ticket-badge use-skeleton">Accounting Software / Fathom</div>
              </div>
            </div>
          </a>
          `;

          ticketWrapper.addClass('isLoading');
          ticketWrapper.html(loader);
        }

        function refreshTickets() {
            fetchFilterOptions();
            ticketItemsLoader();
            getClientTickets2();
            getStatusPageUpdates();
            

        }

        async function fetchFilterOptions() {
          const ticketsFilterLoader = Array.from({ length: 5 }, () => `<div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>`).join('');
          $('#tickets-filter').addClass('isLoading');
          $("#tickets-filter").empty();
          $('#tickets-filter').html(ticketsFilterLoader);

          $('#tickets-sorting').addClass('isLoading');

          const topics = new Promise((resolve) => {
              $.ajax({
                url: `/api/ticketing/topics`,
                method: 'GET',
                success: function(data){
                  const topics = data?.data || {}
                  resolve(Object.values(topics))
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                    resolve([]);
                }
              });
          });
          
          const departments = new Promise((resolve) => {
            $.ajax({
                url: `/api/ticketing/departments`,
                method: 'GET',
                //data: {ticketNumber},
                success: function(data){
                  
                    if(data){
                        departmentsCache = data;
                        const departmentNames = structureDepartments(data.data).map(department => department.department_name);

                        ticketDepartments.forEach(department => {
                            const departmentName = department.department_name;
                            $('#dropdownDepartment').append(
                                $(`<li class="filterLi">
                                    <div class="form-check styled-checkbox">
                                        <input type="checkbox" class="form-check-input filled-in chk-col-blue" data-value="${departmentName}" id="deptcheckbox-${departmentName}">
                                        <label class="form-check-label" for="deptcheckbox-${departmentName}">${departmentName}</label>
                                    </div>
                                </li>
                                `)
                            );
                        });

                        resolve(departmentNames);
                    } else {
                      resolve([]);
                    }
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                    resolve([]);
                }
            });
          });

          const userTickets = new Promise((resolve) => {
            $.ajax({
                url: `/api/ticketing/usertickets`,
                method: 'GET',
                success: function(res){
                    resolve(res.data.tickets ?? []);
                },
                error: function(err) {
                    console.error(err.responseJSON?.error?.message || err);
                    resolve([]);
                }
            })
          });

          Promise.all([topics, departments, userTickets]).then(([topics, departments, userTickets]) => {
            const authors = [...new Set(userTickets.map(ticket => ticket.user_name))];
            $('#tickets-sorting, #tickets-filter').removeClass('isLoading');
            generateTicketFilters(topics, departments, authors, filterTickets);
          });
        }

        // Bind local function to sTickets Object
        $.sTicketing.refreshTickets = function() {
            refreshTickets();
        }

        // add toggle for online status box
        $(document).off('click','.supportSystemsBox.online').on('click', '.supportSystemsBox.online', function() {
          if($(this).hasClass('isCollapsed')) {
            return $(this).removeClass('isCollapsed');
          }
          $(this).addClass('isCollapsed');
        });

        function getStatusPageUpdates() {
          $(".supportSystemsBox, .support-page__sysmessage-wrapper").removeClass("isLoading online offline");
          $(".supportSystemsBox, .support-page__sysmessage-wrapper").addClass("isLoading isCollapsed");

            $.ajax({
                url: `/api/reporting/statuspage/events`,
                method: 'GET',
                success: function(res){
                    $(".supportSystemsBox, .support-page__sysmessage-wrapper").removeClass("isLoading");

                    if(res){
                        statusPageOutages = res;

                        // if there's location search ?popup=newticket
                        if(window.location.search.includes('popup=newticket')) {
                          // toggle new support modal
                          createTicketSelectEmail();
                        }

                        // Update status page outages box style ...
                        if(Object.keys(statusPageOutages).length !== 0) {
                            $(".supportSystemsBox, .support-page__sysmessage-wrapper").removeClass("online isCollapsed")
                            $(".supportSystemsBox:not([class*='offline']), .support-page__sysmessage-wrapper:not([class*='offline'])").addClass("offline")
                            $(".saas-issue-icon").html(`<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.011 20.918H19.99a1.5 1.5 0 0 0 1.32-2.211L13.322 3.87c-.567-1.052-2.075-1.052-2.642 0L2.691 18.707a1.5 1.5 0 0 0 1.32 2.211Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="m11.73 9.159.27 5.719.269-5.717a.27.27 0 0 0-.272-.281.27.27 0 0 0-.266.279Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 18.621a.937.937 0 1 1 0-1.875.937.937 0 0 1 0 1.875Z" fill="currentColor"/></svg>`)
                        } else {
                            // No reported issues from API...
                            $(".supportSystemsBox, .support-page__sysmessage-wrapper").removeClass("offline")
                            $(".supportSystemsBox:not([class*='online']), .support-page__sysmessage-wrapper:not([class*='online'])").addClass("online isCollapsed")
                            $(".saas-issue-icon").html(`<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M22 5.18 10.59 16.6l-4.24-4.24 1.41-1.41 2.83 2.83 10-10L22 5.18Zm-2.21 5.04c.13.57.21 1.17.21 1.78 0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8c1.58 0 3.04.46 4.28 1.25l1.44-1.44A9.9 9.9 0 0 0 12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-1.19-.22-2.33-.6-3.39l-1.61 1.61Z" fill="currentColor"/></g></svg>`)
                            $(".saas-issue-text").html(t('There are no reported SaaS issues at this time'))
                        }

                    }
                },
                error: function(err) {}
            })
        }

        async function getClientTickets2(renderHTML=true) {

            const userTickets = new Promise((resolve) => {
                $.ajax({
                    url: `/api/ticketing/usertickets`,
                    method: 'GET',
                    success: function(res){
                        resolve(res.data.tickets ?? []);
                    },
                    error: function(err) {
                        console.error(err.responseJSON?.error?.message || err);
                        resolve([]);
                    }
                })
            });

            const clubTickers = new Promise((resolve) => {
                $.ajax({
                    url: `/api/ticketing/clubtickets/${selectedID}`,
                    method: 'GET',
                    success: function(res){
                        resolve(res.data ?? []);
                    },
                    error: function(err) {
                        console.error(err.responseJSON?.error?.message || err);
                        resolve([]);
                    }
                })
            });

            $('.support-page__items').addClass('isLoading');

            return Promise.all([userTickets, clubTickers]).then(v => {

                // Clear any and all tickets from selected club...
                // And remove any duplicate tickets...
                selectedClubObj.tickets = [...new Map(v[0].concat(v[1]).map( o => [JSON.stringify(o), o])).values()];

                // sort the tickets based on last reply date
                selectedClubObj.tickets.sort((a, b) => {
                    // if there's no last_response_timestamp, then it's a new ticket and should be at the top
                    if (!a.last_response_timestamp) return 1;

                    return +new Date(b.last_response_timestamp) - +new Date(a.last_response_timestamp);
                });
                
                // Only perform UI changes if renderHTML is set... (we may just want to update the selectedClubObj without changing the HTML elements)
                if(renderHTML) {
                    displayTickets(selectedClubObj.tickets);

                    // Hide after rendering...
                    $('#divAllTickets').LoadingOverlay("hide");
                    ticketItemsLoader(true);

                    //construct ticketAuthors from grouped tickets user_name
                    function groupByUsername(objectArray, property) {
                        return objectArray.reduce((acc, obj) => {
                           const key = obj[property];

                           if (!acc[key]) {

                            //    $('#filterAuthors').append(
                            //         $('<option>', {
                            //             value: key,
                            //             text: key
                            //         })
                            //     );
                            $('#dropdownAuthor').append(
                                $(`<li class="filterLi">
                                        <div class="form-check styled-checkbox">
                                            <input type="checkbox" class="form-check-input filled-in chk-col-blue" data-value="${key}" id="authorcheckbox-${key}">
                                            <label class="form-check-label" for="authorcheckbox-${key}">${key}</label>
                                        </div>
                                    </li>
                                `)
                            );

                              acc[key] = [];
                           }
                           // Add object to list for given key's value
                           //acc[key].push(obj);

                           return acc;
                        }, []);
                    }

                    let groupedTickets = groupByUsername(selectedClubObj.tickets.filter(ticket => {
                        return ticket !== undefined;
                    }), 'user_name');

                    //remove filter link
                    $('#dropdownAuthor').append($(`<li role="separator" class="divider"></li>`));

                    $('#dropdownAuthor').append(
                        $(`<li>
                            <a href="#" class="filterItem" data-value="remove" tabIndex="-1"><i class="material-icons">close</i> ${t('Remove Filter')}</a>`
                        ));

                    $( '#dropdownAuthor li' ).on( 'click', function( event ) {

                        var $target = $( event.currentTarget ),
                            $inp = $target.find( 'input' ),
                            idx;

                        let val = $inp.data('value');


                        if(val) {
                            if ( ( idx = optionsAuthor.indexOf( val ) ) > -1 ) {
                                optionsAuthor.splice( idx, 1 );
                                setTimeout( function() { $inp.prop( 'checked', false ) }, 0);
                            } else {
                                optionsAuthor.push( val );
                                setTimeout( function() { $inp.prop( 'checked', true ) }, 0);
                            }
                            filterTickets();

                        } else {
                            //remove selected

                            //$(this).parent().find('input[type=checkbox]').prop('checked',false);
                            clearAuthorFilter();

                            filterTickets();

                            $( event.target ).blur();
                            return true;
                        }
                        $( event.target ).blur();
                        console.log( optionsAuthor );
                        return false;
                    });
                }
            });

        }

        // Function to "Render" content in a newly created window, so it can be printed...
        function printTicketThread(ticketSubject, ticketId) {

            let pageContents = [], threadIDs = [], css = [], threadContents = {}, ticket = ticketInfo;
            let participants = [], participantBadges = '';

            ticket.thread_entries.forEach(thread => {

                let badge = `
                    <span class="badge badge-outline-dark">
                        ${thread.poster}${ (thread.staff_id === null) ? '' : ` <i>(${t('Staff Member')})</i>` }
                    </span>
                `
                participants.indexOf(badge) === -1 ? participants.push(badge) : '';
            });

            participants.forEach(userBadgeHTML => {
                participantBadges += userBadgeHTML;
            });

            // ------------------------------------------------

            pageContents.push(`
                <div class="header no-padding">
                    <div class="ticket-details-header">

                        <div class="row">
                            <div class="col-md-7">
                                <code>${t('Ticket #')}<span class="spanTicketNo" style="font-weight: bold" id="ticketDetailsNumber">${ticket.ticket_number}</span></code>

                                <div class="rowTicketSubject card-inside-title">
                                    <h2>${ ticket.subject }</h2>
                                </div>
                            </div>
                            <div class="col-md-5 pull-right text-right">
                                <div class="ticket-summary">
                                    ${t('Created')}: ${ moment(ticket.create_timestamp).format('llll') } <br>
                                    ${t('Last Reply')}: ${ moment(ticket.thread_entries[ticket.thread_entries.length - 1].created).format('llll') } <br>
                                    ${t('Ticket Status')}: ${ ticket.ticket_status }
                                </div>
                        </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-5 vertical-align-row">
                                <div class="row ticket-badges">
                                    <div class="col-sm-12">
                                        <small>${t('Participants')}:</small><br>
                                    </div>
                                    <div class="col-sm-12 ticket-participant-badges">
                                        ${participantBadges}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            `);

            ticketInfo.thread_entries && ticketInfo.thread_entries.map(thread => {

                // Don't process internal 'notes' when we print...
                if(thread.type != 'N') {

                    threadContents[thread.id] = thread.message.body;
                    pageContents.push(`
                        <div class="card">
                            <div class="header ticketThreadItem">

                                <div class="ticket-thread-container">
                                    <span ${ (thread.staff_id === null) ? '' : ` data-toggle="tooltip" data-placement="right" title="${t('Verified Support/Staff Member')}" ` } class="badge badge-outline-dark ${ (thread.staff_id === null) ? '' : ' badge-staff-member' }">
                                        ${thread.poster}${ (thread.staff_id === null) ? '' : ` <i>(${t('Staff Member')})</i>` }
                                    </span>

                                    <small class="pull-right thread-post-item">
                                        ${ moment(thread.created).format('llll') }
                                        (${ timeDifference(new Date(), new Date(thread.created)) })
                                    </small>
                                </div>

                                <iframe width=100% height="100" scrolling="no" frameBorder="0"  id="t-${thread.id}"
                                    onload="
                                        this.contentDocument.body.style.fontFamily='Arial';
                                        this.contentDocument.body.style.fontSize='16px';
                                        this.contentDocument.body.style.margin='0';
                                    "
                                ></iframe>
                            </div>
                        </div>
                    `)

                    threadIDs.push(thread.id);
                    // pageJS.push(`document.getElementById('t-${thread.id}').height = document.getElementById('t-${thread.id}').contentWindow.document.body.scrollHeight + "px";`)
                }

            });


            // ticketInfo
            // var frame_content = document.getElementById('ticket-history-container');
            // pageContents.push(frame_content.innerHTML);

            var additionalCss = `

                @import url("https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css");

                body {
                    padding: 20px;
                }
                .card {
                    margin-bottom: 10px;
                }
                .pull-right {
                    float: right;
                }
                .ticket-thread-container .badge {
                    text-transform: capitalize;
                }
                .ticket-thread-container {
                    background-color: #dfdfdf;
                    padding: 10px;
                    border-bottom: 1px solid #dfdfdf;
                }
                .side-article-bar, .searchBar {
                    display: none !important;
                }
                .kb-content {
                    overflow-y: unset;
                    height: auto;
                    width: 100%;
                }
                .ticket-participant-badges .badge {
                    border: 1px solid;
                }
                .ticket-details-header {
                    margin-bottom: 20px;
                }
            `;

            if (pageContents && pageContents.length) {

                let w = window.open('', 'width=400, height=400');
                w.document.head.innerHTML = `
                    <style>${ css.join('\n') + additionalCss }</style>
                    <title>#${ticketId} - ${ticketSubject}</title>
                `;
                w.document.body.innerHTML = pageContents.join("");

                threadIDs.forEach(id => {
                    var $iframe = $(w.document).find(`#t-${id}`);
                    $iframe.ready(function() {

                        let body = $iframe.contents().find("body");
                        body.append(threadContents[id]);

                        var iframe =  $iframe.get(0);
                        iframe.height = iframe.contentWindow.document.body.scrollHeight + "px";
                    });

                });

                setTimeout(function() {
                    w.print();
                }, 500);
                // w.close();
            }

        }

        function filterTickets() {
            //destructuring to be able to sort again, seems like the tickets array became immutable after sorting on
            let { tickets } = JSON.parse(JSON.stringify(selectedClubObj));

            if(ticketFilters.status && ticketFilters.status != "" && ticketFilters.status !== 'all') {
                //the close_timestamp is null when the ticket is open
                if(ticketFilters.status == "open") {
                    tickets = tickets.filter(ticket => ticket.close_timestamp == null );

                } else if (ticketFilters.status == "closed") {
                    tickets = tickets.filter(ticket => ticket.close_timestamp != null );
                }
            }

            if(ticketFilters.replyneeded && ticketFilters.replyneeded != "" && ticketFilters.replyneeded !== 'all') {
                //this should check if someone already replied to the ticket
                //but what if the author himself replied first to his own ticket?
                if(ticketFilters.replyneeded == "yes") {
                    tickets = tickets.filter(ticket => ticket.thread_entries && ticket.thread_entries.length == 1  );
                } else if (ticketFilters.replyneeded == "no") {
                    tickets = tickets.filter(ticket => ticket.thread_entries && ticket.thread_entries.length > 1 );
                }
            }

            // if(ticketFilters.author && ticketFilters.author != "") {
            //     tickets = tickets.filter(t => t.user_name == ticketFilters.author  );
            // }

            // if(ticketFilters.department && ticketFilters.department != "") {
            //     tickets = tickets.filter(t => t.department == ticketFilters.department  );
            // }

            // if(ticketFilters.help_topic && ticketFilters.help_topic != "") {
            //     tickets = tickets.filter(t => t.help_topic.includes(ticketFilters.help_topic) );
            // }


            //new filter implementation using multi-select
            if(optionsTopic.length > 0) {
                tickets = tickets.filter(item => optionsTopic.includes(item.help_topic));
            }

            if(ticketFilters.topic && ticketFilters.topic !== 'all') {
                tickets = tickets.filter(item => item.help_topic == ticketFilters.topic);
            }

            if(optionsDepartment.length > 0) {
                tickets = tickets.filter(item => optionsDepartment.includes(item.department));
            }

            if(ticketFilters.department && ticketFilters.department !== 'all') {
                tickets = tickets.filter(item => item.department == ticketFilters.department);
            }
            
            if(ticketFilters.author && ticketFilters.author !== 'all') {
                tickets = tickets.filter(item => item.user_name == ticketFilters.author);
            }

            if(optionsAuthor.length > 0) {
                tickets = tickets.filter(item => optionsAuthor.includes(item.user_name));
            }

            //do the sorting last
            if(ticketFilters.sortDate && ticketFilters.sortDate != "" && ticketFilters.sorting) {


                if(ticketFilters.sorting === 'lastreply' && ticketFilters.sortDate === 'newest') {
                    tickets.sort( (a,b) => {
                      if (!a.last_response_timestamp) return 1;

                      return +new Date(b.last_response_timestamp) - +new Date(a.last_response_timestamp);
                    })
                }

                if(ticketFilters.sorting === 'lastreply' && ticketFilters.sortDate === 'oldest') {
                    tickets.sort( (a,b) => {
                      if (!a.last_response_timestamp) return -1;
                      
                      return +new Date(a.last_response_timestamp) - +new Date(b.last_response_timestamp);
                    })
                }

                if(ticketFilters.sorting === 'created' && ticketFilters.sortDate === 'newest') {
                    tickets.sort( (a,b) => +new Date(a.create_timestamp) - +new Date(b.create_timestamp))
                    console.log(tickets)
                }

                if(ticketFilters.sorting === 'created' && ticketFilters.sortDate === 'oldest') {
                    tickets.sort( (a,b) => +new Date(b.create_timestamp) - +new Date(a.create_timestamp))
                }

            }

            displayTickets(tickets, 'filter');

        }


        function getSearchTopics(searchTerm) {

            //clear the div first
            $('#divSearchResults').empty();

            loadingOverlay('#divSearchResults');

            if(searchTerm.length > 0 ){
                var req = $.ajax({
                    url: `/api/algolia/search-wiki`,
                    method: 'POST',
                    data: JSON.stringify({
                        searchTerms: searchTerm,
                        club: selectedID,
                    }),
                    contentType: 'application/json',
                    success: function(res){
                        if(res.suggestedArticles.length){
                            displaySearchResults(res.suggestedArticles);
                        } else {
                            $('#divSearchResults').LoadingOverlay('hide')
                            $('#divSearchResults').append(`<center><h4>${t('There were no results for the specified search. Please try a different term.')}</h4></center>`)
                        }
                    },
                    error: function(xhr, textStatus, errorThrown){
                        $('#divSearchResults').LoadingOverlay('hide');
                        $('#divSearchResults').append(`<small>${t('Something went wrong. Please try again.')}</small>`)
                       console.error(xhr, textStatus, errorThrown);
                    }
                });
            } else {
                getSearchTopicsPinned();
                $('#divSearchResults').LoadingOverlay('hide')
            }
        }


        function createTicketSelectEmail() {

            let offlineNotificationBanner = '';
            let newTicketDialog = '';

            // console.log('statusPageOutages', statusPageOutages)
            if(Object.keys(statusPageOutages).length !== 0) {

                let offlineServicesHTML = '';
                Object.keys(statusPageOutages).forEach(service => {
                    // console.log(service, statusPageOutages[service]);
                    offlineServicesHTML += `<li><a target="_blank" href="${statusPageOutages[service].url}">${service}</a></li>`
                })

                offlineNotificationBanner = `
                    <div class="alert alert-red text-left">
                        <p>
                            <i style="color: #eb5b74; margin-right: 5px;" class="fa fa-exclamation-triangle" aria-hidden="true"></i> <strong>${t('SERVICE OUTAGE OR IMPACTED PERFORMANCE DETECTED')}</strong>
                        </p>
                        <p class="font-weight-light">
                            ${t('Please note that if you are experiencing an issue with one of the the following SaaS/services, we\'re looking into the issue(s). Please check the status monitoring page for real-time updates rather than posting a support ticket if possible:')}
                        </p>
                        <ul>${offlineServicesHTML}</ul>
                    </div>
                `;
            }

            let formsAndCRs = `
                <div>
                    <ul style="text-align: left;">
                        <li><a target="_blank" href="https://ubx.wufoo.com/forms/performance-hub-bug-report-submission/?Field6=${ encodeURIComponent(signedinUser) }&Field10=${ encodeURIComponent( (selectedID === undefined || selectedID === null || selectedID === '') ? 'No Club Associated' : selectedID ) }&Field16=${ encodeURIComponent(window.browserInfo.os + ', ' + window.browserInfo.osVersion ) }&Field15=${ encodeURIComponent(window.browserInfo.browser + ' ' + window.browserInfo.browserVersion) }&Field22=${ encodeURIComponent(JSON.stringify({ ...window.browserInfo, ...{ userAgent: navigator.userAgent }} )) }">${t('Performance Hub Bug Report')}</a></li>
                        <li><a target="_blank" href="https://ubx.wufoo.com/forms/warranty-claim-form/?Field3=${ encodeURIComponent(signedinUser) }&Field19=${ encodeURIComponent( (selectedID === undefined || selectedID === null || selectedID === '') ? 'No Club Associated' : selectedID ) }">${t('PH Store: Warranty Claim Form')}</a></li>
                    </ul>
                </div>

                ${signedInUserData?.isGlobalAdmin ? `

                    <hr>
                    <h4>${t('Admin Forms & Change Requests')}</h4>
                    <div>
                        <ul style="text-align: left;">
                        
                            <li><a target="_blank" href="https://ubx.wufoo.com/forms/p1-p2-incident-report/?Field216=${ encodeURIComponent(signedinUser) }">${t('P1 & P2 Incident Report')}</a> <b>(${t('Report an Outage or Critical Problem to IT')})</b></li>

                            <li><a target="_blank" href="https://forms.monday.com/forms/b97c7544ed249a7e63fae27403edf426?r=use1">${t('Stripe Onboarding (Existing Locations)')}</a></li>
                            <li><a target="_blank" href="https://forms.monday.com/forms/7edbf2f673a1ba8f09c6412500c9f8e2?r=use1">${t('New Google Account / Other IT Service Request')}</a></li>
                            <li><a target="_blank" href="https://forms.monday.com/forms/f722d0448b1c52244b1478df710dbd3c?r=use1">${t('Marketing & Print Design Request & Store Change Requests')}</a></li>
                        </ul>
                    </div>

                ` : '' }
            `;

            //if the user belongs to club/s
            if(ClubsObj.length > 0) {

                //if user email is not the same as club email, ask to select which email to use
                if(signedinUser != selectedClubObj.email && selectedClubObj.email) {
                    newTicketDialog = newTicketDialogSelectUserType()
                } else {
                    //if the club does not have an email, the system will not prompt the user to select As Me or As Club
                    console.log(`There is no email in this club`)
                    newTicketDialog = newTicketDialogSingleUser();
                }
            }
            else {
                newTicketDialog = newTicketDialogSingleUser();
            }


            function newTicketDialogSingleUser() {
                return `
                    <div class="create-new-ticket-option">

                        ${offlineNotificationBanner}

                        <div class="container-fluid">
                            <div class="row">
                                <div class="col-sm-12 custom-list">
                                    <h4>${t('Bug Reports & other Forms')}</h4>
                                    <p class="content">${t('If you have a Bug Report or query that follows one of our standard processes please use one of the relevant forms below to initiate your request - Not a support ticket.')}</p>
                                    ${ formsAndCRs }
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <p class="content">${t('Please proceed to create a support ticket if you don\'t match any of the above.')}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            function newTicketDialogSelectUserType() {
                return `
                    <div class="create-new-ticket-option ph-dialog-v2 support-page__dialog__content">

                        ${offlineNotificationBanner}

                        <div class="container-fluid">
                            <div class="d-flex gap-2 flex-wrap justify-start new-ticket-option">
                                <button class="btn btn-default new-ticket-selection">
                                    <label>
                                        <input type="radio" name="select_radio" id="radiobtnUserEmail" checked> ${t('My Email/Account')}
                                        <small>${signedinUser}</small>
                                    </label>
                                </button>
                                <div class="divider"></div>
                                <button class="btn btn-default new-ticket-selection">
                                    <label class="">
                                        <input type="radio"  name="select_radio" id="radiobtnClubEmail"> ${t('The Club')}: (${selectedID})
                                        <small>${selectedClubObj.email}</small>
                                    </label>
                                </button>
                            </div>
                            <p class="new-ticket-info-text content">
                            ${t('When creating a ticket as/under a individual Facility, other user\'s who also have permission to access your facility will be able to collaborate on the ticket.')}
                            </p>
                            <hr>
                            <div class="row">
                                <div class="col-sm-12 custom-list">
                                    <h4>${t('Bug Reports & other Forms')}</h4>
                                    <p>${t('If you have a Bug Report or query that follows one of our standard processes please use one of the relevant forms below to initiate your request - Not a support ticket.')}</p>
                                    ${ formsAndCRs }
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            let newTicketType
            swalWithBootstrapButtons.fire({
                title:`${t('Create a New Support Ticket using the account:')}`,
                width: 640,
                showConfirmButton: true,
                customClass: {
                  container: 'support-page__dialog device-details-dialog__container',
                  confirmButton: 'btn btn-primary waves-effect',
                  cancelButton: 'btn btn-default waves-effect'
                },
                confirmButtonText: `<i class="fa-solid fa-headset" style="font-size: 14px;"></i><span>${t('Create New Support Ticket')}</span>`,
                showCloseButton: true,
                showCancelButton: true,
                cancelButtonText: t('Cancel'),
                allowOutsideClick: false,
                html: newTicketDialog,
                onOpen: function() {
                    $('body').removeClass('materialize')
                    $('.new-ticket-selection').on('click', (e) => {
                        $(e.currentTarget).find('input').prop('checked', true)
                    });
                    const swalElement = Swal.getPopup();
                    $(swalElement).draggable({
                        handle: '.swal2-header',  // Makes the title bar the drag handle
                    });
                    // Only change the cursor to move for the title bar
                    $(swalElement).find('.swal2-header').css('cursor', 'move');
                },
                onClose: function() {
                    $('body').addClass('materialize')
                },
                preConfirm: () => {
                    $('input:radio[name=select_radio]:checked').each(function () {
                        if($(this).attr('id') === 'radiobtnUserEmail') {
                            newTicketType = 'user'
                        } else {
                            newTicketType = 'club'
                        }
                    });
                }
            }).then(result => {
                if(result.value) {
                    switch(newTicketType) {
                        case 'club':
                            showCreateNewTicketSwal("club");
                            break;
                        case 'user':
                            showCreateNewTicketSwal("user");
                            break;
                        default:
                            showCreateNewTicketSwal("user");
                    }
                }
            })
        }

        function showCreateNewTicketSwal(createTicketVia) {
            var createTicketDialog = `<div class="new-support-ticket-dialog ph-dialog-v2">
                <div id="divNewTicketModal">

                    <div class="row topSpacer">
                        <div class="col-sm-6">
                            <label for="createTicketTitle" class="field-label">${t('Subject')}</label>
                            <div class="form-group">
                                <div class="form-line">
                                    <input data-hj-allow="" type="text" id="createTicketTitle" class="form-control" placeholder="${t('Subject')}" required="">
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-3">
                        <label for="selectTicketDepartments" class="field-label">${t('Department')}</label>
                            <div class="input-field s12">
                                <select data-live-search="true" class="form-control" id="selectTicketDepartments" title="${t('Please Select')}">
                                </select>
                            </div>
                            <div class="ai-badge-suggestions"></div>
                        </div>
                        <div class="col-sm-3">
                        <label for="selectTicketTopics" class="field-label">${t('Category')}</label>
                            <div class="input-field s12">
                                <select data-live-search="true" class="form-control" id="selectTicketTopics" title="${t('Please Select')}">
                                </select>
                            </div>
                            <div class="ai-topic-suggestions"></div>
                        </div>
                    </div>

                    <div class="row topSpacer">
                        <div class="col-sm-12">
                            <p class="field-label">${t('Message')}</p>
                            <div id="createTicketBody"></div>
                        </div>
                    </div>

                    <div class="row topSpacer">
                        <div class="col-sm-12 hidden">
                            <button type="button" class="btn btn-default waves-effect btn-xs btn-toggle-attachments visible-xs-block visible-sm-block visible-md-block visible-lg-block visible-xl-block"><i class="fa-solid fa-paperclip"></i> ${t('Attach Files')}</button>
                        </div>
                        <div id="attachmentsContainer">
                            <div class="col-sm-12">
                                <div class="list-attachments"></div>
                            </div>
                            <div class="col-sm-12">
                                <div id="ticketUploads" class="dropzone">
                                  <div class="dz-message dropzone-content">
                                    <p><strong>${t('Drag and Drop to Upload Attachments')}</strong> ${t('OR')}</p>
                                    <button>${t('Browse File')}</button>
                                  </div>
                                </div>
                                <div id="upload-size-left-alert">${t('You may upload a maximum of 99 MB (combined files) to a support ticket.')} ${humanFileSize(getUploadSizeLeft(createTicketUploadTotalSize), true)} ${t('remaining')}</div>
                                <div id="upload-max-limit-alert" class="text-danger hidden">${t('You may only upload a maximum of 99 MB (combined files) per support ticket - Current attachments exceed 99 MB.')}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row topSpacer suggested-wiki-articles-container" style="display: none;">
                        <div class="col-sm-12">
                            <div class="suggested-articles">
                                <h4>
                                    <i class="fa-solid fa-book-atlas"></i>
                                    Suggestions Based on Your Message
                                    <span class="ai-icon-container">
                                        <img src="/assets/images/openai.svg" alt="AI Icon" class="ai-icon" data-toggle="tooltip" data-placement="right" title="${t("We're using AI to enhance your experience. This is a feature we're trialing and continuously improving.")}">
                                    </span>
                                </h4>
                                <ul></ul>
                            </div>
                        </div>
                    </div>

                    <div class="row hidden-xs hidden-sm">
                        <div class="col-sm-12">
                            ${t('Your support ticket will be created under the account')}: <strong class="highlighte-text">${createTicketVia === "club" ? selectedClubObj.email : signedinUser}</strong>
                        </div>
                    </div>

                    </fieldset>
                </div>

            </div>`;

            function getPlainTextFromTrumbowyg(selector) {
                // Fetch the HTML content from Trumbowyg
                let htmlContent = $(selector).trumbowyg('html');
                
                // Create a temporary DOM element to convert HTML to text
                let tempDiv = document.createElement("div");
                tempDiv.innerHTML = htmlContent;
                
                // Get the plain text content
                return tempDiv.textContent || tempDiv.innerText || "";
            }

            // debounce function to slowly increase its delay each time it's called, regardless of whether it's on keyup or some other action, over a period of 15 seconds. 
            function debounce(func) {
                let lastCalled = null;
                let debounceTimeout = null;
                let debounceTime = 2500;
                const maxDebounceTime = 6000; // Adjust this as required

                return function(...args) {
                    const now = Date.now();

                    if (lastCalled && (now - lastCalled) < 15000) {
                        debounceTime = Math.min(debounceTime + 1000, maxDebounceTime);
                    } else {
                        debounceTime = 2500;
                    }

                    if (debounceTimeout) {
                        clearTimeout(debounceTimeout);
                    }

                    debounceTimeout = setTimeout(() => {
                        func.apply(this, args);
                    }, debounceTime);

                    lastCalled = now;
                };
            }



            function callOpenAI() {
                let messageBody = getPlainTextFromTrumbowyg('#createTicketBody');
                if(messageBody.length >= 50) {
                    console.log("Checking OpenAI + Algolia for topic suggestions and search result suggestions...");

                    let req = $.ajax({
                        url: `/api/ai/helpers/analyse-support-ticket?facility=${selectedID}`,
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            messageBody: messageBody,
                            topicData: departmentsCache.data
                        }),
                        success: function(res){
                            
                            // console.log(res)
                            if(res.suggestedArticles.length) {
                                $(".suggested-articles").find("ul").empty();
                                res.suggestedArticles.forEach((element) => {
                                    // console.log(element)
                                    $(".suggested-articles").find("ul").append(`<li><a target="_blank" href="/wiki/#%2Fopenkb%2Fkb%2F${element.id}">
                                        <i class="fa-solid fa-chevron-right" style="margin-right: 5px;"></i>
                                        ${element.title}
                                    </a></li>`);
                                });

                                if ($(".suggested-wiki-articles-container").css("display") === "none") {
                                    $(".suggested-wiki-articles-container").fadeIn(500);
                                }
                            }

                            // Some logic to set the topic if it's not yet been set...
                            if(res?.department_name_id && res?.help_topic_id) {
                                if($('#selectTicketTopics').selectpicker('val') == '' || $('#selectTicketDepartments').selectpicker('val') == '') {
                                    $('#selectTicketDepartments').selectpicker('val', res.department_name_id);
                                    $('#selectTicketDepartments').change();
                                    
                                    $('#selectTicketTopics').selectpicker('val', res.help_topic_id);
                                    $('#selectTicketTopics').change();

                                } else {

                                    // If topic selected by user is not what the AI thinks - suggest the correct topic...
                                    if($('#selectTicketDepartments').selectpicker('val') != res.department_name_id) {
                                        $(".ai-badge-suggestions").html(`
                                            <a href='#' style="font-size: 11px;">${t('Use')} '${res.department_name}' ${t('Department?')}</a>
                                        `)
                                        $(".ai-badge-suggestions").click(function() {

                                            $('#selectTicketDepartments').selectpicker('val', res.department_name_id);
                                            $('#selectTicketDepartments').change();

                                            $('#selectTicketTopics').selectpicker('val', res.help_topic_id);
                                            $('#selectTicketTopics').change();

                                            $('.ai-badge-suggestions').html('')
                                        });
                                    
                                    } else {

                                        // Only show suggestion to change the help topic if the department is already correct.
                                        if($('#selectTicketTopics').selectpicker('val') != res.help_topic_id) {
                                            $(".ai-topic-suggestions").html(`
                                                <a href='#' style="font-size: 11px;">${t('Use')} '${res.help_topic}' ${t('Category?')}</a>
                                            `)
                                            $(".ai-topic-suggestions").click(function() {

                                                $('#selectTicketTopics').selectpicker('val', res.help_topic_id);
                                                $('#selectTicketTopics').change();

                                                $('.ai-topic-suggestions').html('')
                                            });
                                        }
                                    }
                                    // $('#selectTicketTopics').selectpicker('val') != '') {
                                }
                            }
                            
                        },
                        error: function(xhr, textStatus, errorThrown){
                            console.log('Error Calling AI API:', textStatus)
                        }
                    });


                }
            }

            const openAISuggestions = debounce(callOpenAI);

            function validateInternal() {
                var isValid = true;

                $('#createTicketBody').trumbowyg('html')
                let message = $('#createTicketBody').trumbowyg('html');

                if($("#createTicketTitle").val().length == 0) isValid = false;
                if(message.length == 0) isValid = false;

                if(!$("#selectTicketDepartments").val()) isValid = false;
                if(!$("#selectTicketTopics").val()) isValid = false;

                if(isValid) {
                    $('.swal2-actions .swal2-confirm').prop('disabled', false);
                } else {
                    $('.swal2-actions .swal2-confirm').prop('disabled', true);
                }

                if(!$("#selectTicketDepartments").val()) {
                    $("#selectTicketTopics").prop("disabled", true);
                    $("#selectTicketTopics").selectpicker('refresh');
                } else {
                    $("#selectTicketTopics").prop("disabled", false);
                    $("#selectTicketTopics").selectpicker('refresh');
                }
            }

            function validate() {
                validateInternal();
                openAISuggestions(); // Call the debounced API function after validation logic
            }


            let ticketAttachments = [], ticketObj

            swalWithBootstrapButtons.fire({
                title:`${t('New Support Ticket')}`,
                html: createTicketDialog,
                customClass: {
                  container: 'support-page__dialog support-page__dialog--wide device-details-dialog__container',
                  confirmButton: 'btn btn-primary waves-effect',
                  cancelButton: 'btn btn-default waves-effect'
                },
                width: 800,
                showCloseButton: true,
                allowOutsideClick: false,
                allowEscapeKey : false,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonText: t("Create!"),
                cancelButtonText: t("Cancel"),
                onBeforeOpen: () => {

                    // Create tooltips...
                    $('[data-toggle="tooltip"]').tooltip({ container: 'body' });

                    // ticketUploads
                    $('.btn-toggle-attachments').click(function() {
                        $("#attachmentsContainer").removeClass('hidden');
                        $(this).hide();
                    });

                    ticketDepartments.forEach((department) => {
                        $('#selectTicketDepartments').append(
                            $('<option>', {
                                value: department.department_id,
                                text: department.department_name
                            })
                        );
                    });

                    // Add select picker ui styles...
                    $("#selectTicketDepartments").selectpicker();
                    $('#selectTicketDepartments').on('change', () => {
                        validate();
                        const selectedDeptId = $('#selectTicketDepartments').val();
                        const selectedDept = ticketDepartments.find(dept => dept.department_id == selectedDeptId);
                        if (
                            'help_topic' in selectedDept
                            && Array.isArray(selectedDept.help_topic)
                            && selectedDept.help_topic.length
                        ) {
                            const topics = selectedDept.help_topic.map(ht => {
                                const topic = ht.split('|');
                                return {
                                    id: topic[0],
                                    text: topic[1]
                                };
                            }).sort(function (a, b) {
                                return a.text.localeCompare(b.text);
                            })

                            const topicOptions = topics.map(topic => {
                                return `<option value="${topic.id}">${topic.text}</option>`;
                            });

                            $('#selectTicketTopics').html(topicOptions);
                            $('#selectTicketTopics').selectpicker('refresh');
                        }
                    });
                    
                    ticketTopics.forEach((topic) => {
                        $('#selectTicketTopics').append(
                            $('<option>', {
                                value: topic.topicId,
                                text: topic.topicName
                            })
                        );
                    });

                    $("#selectTicketTopics").selectpicker();
                    $("#selectTicketTopics").change(function() {
                        validate();
                    });

                    $('.swal2-actions .swal2-confirm').prop('disabled', true);

                    $("#divNewTicketModal #createTicketTitle").focus();

                    let btns = [
                        ['fontfamily'],
                        ['fontsize'],
                        ['strong', 'em', 'del']
                    ]

                    // On mobile devices - make the editor more compact...
                    switch(getOS()) {
                      case 'ios':
                        btns.push(['foreColor'])
                        break;
                      case 'android':
                        btns.push(['foreColor'])
                        break;
                      default:
                        // Undo/Redo added to start of array...
                        btns.unshift(['historyUndo','historyRedo']);
                        btns.push(['foreColor', 'backColor'])
                        btns.push(['link']);
                    }

                    $('#createTicketBody').trumbowyg({
                        svgPath: '/assets/images/trumbowyg.svg',
                        autogrow: true,
                        defaultLinkTarget: '_blank',
                        btns: btns
                    });

                    $('#selectTicketDepartments').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
                        validate()
                    });

                    $("#createTicketTitle").change(function() {
                        validate()
                    });

                    $("#createTicketBody").on('keyup', function() {
                        validate()
                    });

                    // Setup Dropzone
                    $("div#ticketUploads").dropzone({
                        action: 'none',
                        url: '/api/non-existing-endpoint', //dummy endpoint
                        autoProcessQueue: false, //don't auto upload files
                        maxFilesize: 99, // MB
                        parallelUploads: 1,
                        maxFiles: 25,
                        uploadMultiple: true,
                        addRemoveLinks: true,
                        acceptedFiles: "image/*,application/pdf,audio/*,text/*,video/*,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pages,.odt,.rtf,.zip,.7z,.tar",
                        dictDefaultMessage: t("Drop files here to upload"),
                        dictFallbackText: t("Please use the fallback form below to upload your files like in the olden days."),
                        dictFileTooBig: t("File is too big") + "({{filesize}}MiB)." + t("Max filesize") + ": {{maxFilesize}}MiB.",
                        dictInvalidFileType: t("You can't upload files of this type."),
                        dictResponseError: t("Server responded with") + "{{statusCode}}" + t("code."),
                        dictCancelUpload: t("Cancel upload"),
                        dictUploadCanceled: t("Upload canceled."),
                        dictCancelUploadConfirmation: t("Are you sure you want to cancel this upload?"),
                        dictRemoveFile: t("Remove file"),
                        dictMaxFilesExceeded: t("You can not upload any more files."),
                        accept: function(file, done) {
                            /** limit the maximum size to upload **/
                            if (createTicketUploadTotalSize >= UPLOAD_MAX_TOTAL_SIZE) {
                                file.status = Dropzone.CANCELED;
                                this.removeFile(file);
                                $('#upload-max-limit-alert').removeClass('hidden');
                            } else {
                                reader = new FileReader();
                                reader.onload = handleReaderLoad;
                                reader.readAsDataURL(file);
                                function handleReaderLoad(evt) {
                                    //note: the attachment data format specified in the documentation was wrong
                                    //follow the JSON attachment example, not the documentation.
                                    //sample:
                                    // attachments: [{'actualFileName': 'base64data'}]
                                    let fileName = file.name
                                        .replace(/\.[^/.]+$/, "")
                                        .replace(/[^a-zA-Z0-9]/g,'_');

                                    ticketAttachments.push({
                                        [fileName]: evt.target.result
                                    });
                                }
                                done();
                                $('#upload-max-limit-alert').addClass('hidden');
                            }
                        },
                        init: function() {
                            this.on("addedfile", function(file) {
                                createTicketUploadTotalSize += file.size
                                let sizeLeft = getUploadSizeLeft(createTicketUploadTotalSize);
                                $('#upload-size-left-alert').text(`${t('You may upload a maximum of 99 MB (combined files) to a support ticket.')} ${humanFileSize(sizeLeft, true)} ${t('remaining')}.`);
                                 //remove the progress loading
                                $('.dz-progress').hide();
                            });
                            this.on("removedfile", function(file) {
                                createTicketUploadTotalSize -= file.size
                                let sizeLeft = getUploadSizeLeft(createTicketUploadTotalSize);
                                $('#upload-size-left-alert').text(`${t('You may upload a maximum of 99 MB (combined files) to a support ticket.')} ${humanFileSize(sizeLeft, true)} ${t('remaining')}.`);
                                $('#upload-max-limit-alert').addClass('hidden');
                            });
                        }
                    });

                    validate();
                },
                onClose: () => {
                    /** reset this back to zero **/
                    createTicketUploadTotalSize = 0;

                    let title = $("#createTicketTitle").val();
                    let message = $('#createTicketBody').trumbowyg('html');
                    let department = $("#selectTicketDepartments").selectpicker('val');
                    let topic = $("#selectTicketTopics").selectpicker('val');

                    let ticketName = signedinUser;

                    //ticket is created for a club
                    if(selectedClubObj.name && createTicketVia == "club") {
                        ticketName = selectedClubObj.name;
                    }

                    ticketObj = {
                        alert: true,
                        autorespond: true,
                        source: "API",
                        name: ticketName,
                        phone: "",
                        subject: title,
                        message: 'data:text/html,' + message,
                        deptId: department,
                        topicId: topic,
                        attachments: ticketAttachments
                    }

                }
            })
            .then(response => {
                if (response.value) {

                    $('.swal2-actions .swal2-confirm').prop('disabled', true);
                    loadingOverlay('#divAllTickets')
                    ticketItemsLoader();

                    let saveUrl = `/api/ticketing/userticket`;

                    //createTicketVia changes when selecting As Me or As Club prompt
                    if(createTicketVia == "club"){
                        saveUrl = `/api/ticketing/clubticket/${selectedClubObj.id}`;
                    }

                    var req = $.ajax({
                        url: saveUrl,
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(ticketObj),
                        success: function(res){
                            createTicketUploadTotalSize = 0;
                            $('#divAllTickets').LoadingOverlay("hide");
                            ticketItemsLoader(true);

                            if((res === undefined) ? false : res !== false) {
                                if(res.response.length > 0){

                                    instantNotification(`${t('New ticket created with Ticket')} #${res.response}<br>${t('We will respond to your query soon.')}`);
                                    //also clear the div
                                    $('#divAllTickets, .support-page__items').empty();
                                    //reload tickets
                                    refreshTickets();
                                }
                            } else {
                                instantNotification(
                                    t('<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to add/save your Ticket.<br>Please contact HQ for further details.'),
                                    'error',  60000
                                );
                            }
                        },
                        error: function(xhr, textStatus, errorThrown){
                            createTicketUploadTotalSize = 0;
                            $('#divAllTickets').LoadingOverlay("hide");
                            ticketItemsLoader(true);

                            instantNotification(
                                t('<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to add/save your Ticket.<br>Please contact HQ for further details.'),
                                'error',  60000
                            );
                        }
                    });
                }
            });
        }

        function displayTickets(tickets, method=false, searchTerm=false) {

            $('#divAllTickets, .support-page__items').empty();

            $('.support-page__items').removeClass('isLoading');

            $('#resultsCount').text(tickets.length);

            // Default messages if no matches...
            if(tickets && tickets.length == 0) {
                switch(method) {
                    case 'search':
                        $('#divAllTickets, .support-page__items').append(`
                            <center>
                                <h2>${t('No matches for the search term')} &quot;${searchTerm}&quot;</h2>
                                <h4>${t('Try searching/filtering by ticket ID, Names, Emails, or Subjects')}</h4>
                            </center>
                        `);
                        break;
                    case 'filter':
                        $('#divAllTickets, .support-page__items').append(`
                            <center class="support-page__items__msg">
                                <h2>${t('No results for that filter')}</h2>
                                <h4>${t('Try resetting or removing filters to broaden your filter scope')}</h4>
                            </center>
                        `);
                        //also don't show filters anymore coz there's nothing to filter
                        $('#ticketFilterContainer').addClass('hidden-xs');
                        break;
                    default:
                        $('#divAllTickets, .support-page__items').append(`
                            <center class="support-page__items__msg">
                                <h2>${t('There is currently no support history for this account')}</h2>
                                <h4>${t('Create a new ticket if you require support, but be sure to refer to the Knowledge Base for common processes & FAQ\'s first.')}</h4>
                            </center>
                        `);
                }
            }

            const filteredTickets = tickets.filter(ticket => {
                return ticket !== undefined;
            });

            filteredTickets && filteredTickets.map(ticket => {

                // Check if thread_entries is undefined or empty, and skip if true - this happens when you 'merge' tickets and the old tickets remain with no threads..
                if (!ticket.thread_entries || ticket.thread_entries.length === 0) {
                    return; // Skip to the next ticket
                }


                let strippedTicketSummary = $($.parseHTML(ticket.thread_entries[0].message.body)).text();
                let participants = [], participantBadges = '';

                ticket.thread_entries.forEach(thread => {

                    let badge = `
                        <span ${ (thread.staff_id === null) ? '' : ` data-toggle="tooltip" data-placement="right" title="${t('Verified Support/Staff Member')}" ` } class="badge badge-outline-dark ${ (thread.staff_id === null) ? '' : ' badge-staff-member' }">
                            ${thread.poster}${ (thread.staff_id === null) ? '' : '<i class="material-icons staff-badge-icon">verified_user</i>' }
                        </span>
                    `
                    participants.indexOf(badge) === -1 ? participants.push(badge) : '';
                });

                participants.forEach(userBadgeHTML => {
                    participantBadges += userBadgeHTML;
                });

                const participantItems = (entries) => {
                  const entryPosters = [...new Set(entries.map(entry => entry.poster))];
                  const posterList = entryPosters.join(', ');
                  const len = entryPosters.length

                  if(len > 3) {
                    return `${entryPosters.slice(0, 3).join(', ')} +${len - 3} others`
                  }

                  return posterList
                }

                $('#divAllTickets, .support-page__items')
                    .append(`
                        <a class="support-page__item support-page__item--${ticket.ticket_status}" data-ticketnumber="${ticket.ticket_number}">
                          <div class="support-page__item__status-wrapper">
                            <div class="support-page__item__ticket-id">Ticket #${ticket.ticket_number}</div>
                            <div class="support-page__item__ticket-status">Status: ${ticket.ticket_status}</div>
                          </div>
                          <div class="support-page__item__title-wrapper">
                            <h6 class="support-page__item__title">${ ticket.subject }</h6>
                            <div class="support-page__item__meta"><span class="support-page__item__meta-author"><svg width="11" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M8.043 5.25c.944.275 2.243 1.205 2.243 4.252 0 1.38-1.025 2.498-2.284 2.498H2.283C1.024 12 0 10.882 0 9.502 0 6.456 1.299 5.525 2.243 5.25a3.392 3.392 0 0 1-.529-1.821A3.435 3.435 0 0 1 5.143 0 3.435 3.435 0 0 1 8.57 3.429a3.39 3.39 0 0 1-.528 1.821ZM5.143.857a2.572 2.572 0 1 0 .001 5.144A2.572 2.572 0 0 0 5.143.857Zm2.86 10.286c.783 0 1.426-.73 1.426-1.64 0-2.11-.71-3.43-2.036-3.496a3.394 3.394 0 0 1-2.25.85 3.394 3.394 0 0 1-2.25-.85C1.567 6.074.857 7.393.857 9.502c0 .911.643 1.64 1.426 1.64h5.72Z" fill="currentColor"/></g></svg> ${ticket.user_name}</span> <span
                                class="support-page__item__meta-item"><svg width="12" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M1.143 11.143h9.428V4.286H1.143v6.857ZM3.714 3V1.071A.212.212 0 0 0 3.5.857h-.429a.212.212 0 0 0-.214.214V3c0 .12.094.214.214.214H3.5c.12 0 .214-.094.214-.214Zm5.143 0V1.071a.212.212 0 0 0-.214-.214h-.429A.212.212 0 0 0 8 1.071V3c0 .12.094.214.214.214h.429c.12 0 .214-.094.214-.214Zm2.572-.429v8.572a.863.863 0 0 1-.858.857H1.143a.863.863 0 0 1-.857-.857V2.57c0-.468.388-.857.857-.857H2v-.643C2 .482 2.482 0 3.071 0H3.5c.59 0 1.071.482 1.071 1.071v.643h2.572v-.643C7.143.482 7.625 0 8.214 0h.429c.59 0 1.071.482 1.071 1.071v.643h.857c.47 0 .858.389.858.857Z" fill="currentColor"/></g></svg> ${ moment(ticket.create_timestamp).format('MMM DD, YYYY h:mmA') }</span></div>
                          </div>
                          <div class="support-page__item__excerpt">${ strippedTicketSummary } ...
                          </div>
                          <div class="support-page__item-details">
                            <div class="support-page__item__ticket-participants-wrapper">
                              <div class="support-page__item__ticket-message-count">${ticket.thread_entries.filter(key => {return !key.type.includes("N")}).length} <svg width="19" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.866 10.417h2.656c.628 0 1.139-.477 1.139-1.063V2.98c0-.586-.511-1.062-1.139-1.062H2.656c-.627 0-1.138.476-1.138 1.062V13.25l4.047-2.833h4.301Zm-6.83-.071.117-.011-.117.082v-.071Z" fill="currentColor"/><path d="M15.558 6.167h-.38v4.25c0 .78-.677 1.412-1.51 1.416H6.071v.354c0 .586.511 1.063 1.139 1.063h5.44l4.046 2.833V7.23c0-.586-.51-1.062-1.138-1.062Z" fill="currentColor"/></svg></div>
                              <div class="support-page__item__ticket-participants">${participantItems(ticket.thread_entries)}</div>
                            </div>
                            <div class="support-page__item__ticket-departments">
                            <svg width="19" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><path d="M5.036 4.75A1.21 1.21 0 0 0 3.82 3.536 1.21 1.21 0 0 0 2.607 4.75a1.21 1.21 0 0 0 1.214 1.214A1.21 1.21 0 0 0 5.036 4.75Zm10.122 5.464c0 .323-.133.636-.35.854l-4.659 4.668c-.228.218-.54.35-.863.35-.323 0-.636-.132-.854-.35L1.65 8.943C1.165 8.469.786 7.548.786 6.875V2.929c0-.665.55-1.215 1.214-1.215h3.946c.674 0 1.594.38 2.078.864l6.783 6.773c.218.228.351.54.351.863Zm3.643 0c0 .323-.133.636-.351.854l-4.658 4.668c-.228.218-.541.35-.863.35-.493 0-.74-.227-1.063-.56l4.459-4.458a1.222 1.222 0 0 0 0-1.717L9.542 2.578c-.484-.484-1.404-.864-2.078-.864H9.59c.674 0 1.594.38 2.078.864L18.45 9.35c.*************.35.863Z" fill="#D5DBE1"/></svg>
                              <div class="support-page__item__ticket-badge">${ ticket.department }</div>
                              <div class="support-page__item__ticket-badge">${ ticket.help_topic }</div>
                            </div>
                          </div>
                        </a>`);

            });

            $('.support-page__items .support-page__item').on('click', function() {
              sammy.quiet = false;
              sammy.setLocation(`/support/${ $(this).data('ticketnumber') }`)
            });

        }

        $.sTicketing.getTicketInfo = {

            // This is using the object found from selectedClubObj.tickets using id - so that there's no more ajax call
            load: function(ticketNumber, useApi = true) {
                
                //hide tickets list
                $('#divTicketsSection').hide(100);
                $('#divSearchWiki').hide(100);
                $('#btnBackToAllTickets').css('visibility', 'visible');
                $('.support-page__single-ticket').addClass('support-page__single-ticket--visible isLoading');

                // loadingOverlay('#divTicketDetailsSection');

                //add height to the div so it won't overlap with the nav
                // $('#divTicketDetailsSection').attr('min-height', '200px');

                console.log({ ticketNumber, useApi })

                if(ticketNumber.includes('status-')) {
                  $('.support-page__single-ticket').removeClass('support-page__single-ticket--visible isLoading');
                  return;
                }

                //if the tickets are already loaded - normal usage
                if(selectedClubObj.tickets !== undefined && selectedClubObj.tickets.length !== undefined && selectedClubObj.tickets.length > 0){
                    
                    let selectedTicket = selectedClubObj.tickets.find(ticket => ticket.ticket_number == ticketNumber);

                    if(selectedTicket) {
                      // Update current 'ticketInfo' object...
                      ticketInfo = selectedTicket;
                      $('.support-page__single-ticket').empty();
                      displayTicketDetails(selectedTicket);
                    } else {
                      $('.support-page__single-ticket').empty();
                          $('.support-page__single-ticket').removeClass('isLoading');
                          $('.support-page__single-ticket').addClass('support-page__single-ticket--visible support-page__single-ticket--error');
                          $('#divTicketDetailsSection').append(`
                              <div class="row">
                                  <div class="col text-center topSpacer">
                                      <p>${t('You do not have permission to view this ticket.')}</p>
                                      <a href="/support"> ${t('Go Back')} </a>
                                  </div>
                              </div>
                          `)
                    }
                    
                } else {
                    //if the user refreshes the page - requery using id
                    if(!useApi && !selectedClubObj?.tickets) {
                      setTimeout(() => {
                        this.load(ticketNumber, false);
                      }, 1000);
                      return;
                    }

                    var req = $.ajax({
                        url: `/api/ticketing/ticket-details/${ticketNumber}/${selectedClubObj.id}`,
                        method: 'GET',
                        success: function(data){
                            ticketInfo = {};
                            $('.support-page__single-ticket').empty();
                            if(data.data){
                                ticketInfo = data.data.ticket;
                                displayTicketDetails(ticketInfo);
                            }
                        },
                        error: (err) => {
                            console.error(err.responseJSON?.error?.message || err);
                            // $('#divTicketDetailsSection').LoadingOverlay("hide");
                        },
                        complete: (jqXHR, textStatus) => {
                            if (textStatus == 'error') {
                                // $('#divTicketDetailsSection').LoadingOverlay("hide");
                                // trigger the function again but do not call the api and use the existing tickets data
                                this.load(ticketNumber, false);
                            }
                         }
                    });
                }

            }
        }


        function setTicketBadgeClass(status) {
            switch(status.toLowerCase()) {
              case 'open':
                return 'badge-blue'
                break;
              case 'resolved':
                return 'badge-green'
                break;
              case 'closed':
                return 'badge-grey'
                break;
              default:
                return 'badge-grey'
            }
        }

        function setTicketBadgeIcon(status) {
            switch(status.toLowerCase()) {
              case 'open':
                return '<i class="fa-solid fa-rotate"></i>'
                break;
              case 'resolved':
                return '<i class="fa-solid fa-check"></i>'
                break;
              case 'closed':
                return ''
                break;
              default:
                return ''
            }
        }



        function displayTicketDetails(ticket) {

            // $('#divTicketDetailsSection').css('visibility', 'visible');
            $('.support-page__single-ticket').removeClass('isLoading');
            $('.support-page__single-ticket').addClass('support-page__single-ticket--visible');
            // $('#divTicketDetailsSection').LoadingOverlay("hide");

            // $('#divTicketDetailsSection').empty();
            // @TODO hide skeleton loader

            const getLatestReplyDate = (entries) => {
              const latestEntry = entries.sort((a, b) => {
                return new Date(b.created) - new Date(a.created)
              })[0]

              return moment(latestEntry.created).format('ddd, MMM DD, YYYY h:mmA')
            }

            let participants = [], participantBadges = '';

            ticket.thread_entries.forEach(thread => {

                let badge = `
                    <span ${ (thread.staff_id === null) ? '' : ` data-toggle="tooltip" data-placement="right" title="${t('Verified Support/Staff Member')}" ` } class="support-page__item__ticket-badge ${ (thread.staff_id === null) ? '' : ' support-page__item__ticket-badge--badge-staff-member' }">
                        ${thread.poster}${ (thread.staff_id === null) ? '' : '<i class="material-icons staff-badge-icon">verified_user</i>' }
                    </span>
                `
                participants.indexOf(badge) === -1 ? participants.push(badge) : '';
            });

            participants.forEach(userBadgeHTML => {
                participantBadges += userBadgeHTML;
            });

            $('#divTicketDetailsSection').append(`
                <div class="support-page__single-ticket__content support-page__item--full support-page__item support-page__item--${ticket.ticket_status}" data-ticketnumber="${ticket.ticket_number}">
                    <div class="support-page__sticky-data">
                      <div class="support-page__sticky-data__actions">
                        <button class="support-page__item-btn" type="button" id="btnBackToAllTickets">
                          <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><path d="M17.5 9.167H5.692l2.983-2.992L7.5 5l-5 5 5 5 1.175-1.175-2.983-2.992H17.5V9.167Z" fill="currentColor"/></g></svg> ${t('Go Back')}
                        </button>
                      </div>
                      <div class="support-page__item__status-section">
                        <div class="support-page__item__status-section-col">
                          <div class="support-page__item__status-wrapper">
                            <div class="support-page__item__ticket-id">
                              ${t('Ticket #')}${ticket.ticket_number}
                            </div>
                            <div class="support-page__item__ticket-status">${t('Status')}: ${ticket.ticket_status}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="support-page__item__status-section">
                      <div class="support-page__item__status-section-col">
                        <div class="support-page__item__title-wrapper">
                          <h6 class="support-page__item__title">${ ticket.subject }</h6>
                          <div class="support-page__item__meta">
                            <span class="support-page__item__meta-item">
                              <svg width="11" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M8.043 5.25c.944.275 2.243 1.205 2.243 4.252 0 1.38-1.025 2.498-2.284 2.498H2.283C1.024 12 0 10.882 0 9.502 0 6.456 1.299 5.525 2.243 5.25a3.392 3.392 0 0 1-.529-1.821A3.435 3.435 0 0 1 5.143 0 3.435 3.435 0 0 1 8.57 3.429a3.39 3.39 0 0 1-.528 1.821ZM5.143.857a2.572 2.572 0 1 0 .001 5.144A2.572 2.572 0 0 0 5.143.857Zm2.86 10.286c.783 0 1.426-.73 1.426-1.64 0-2.11-.71-3.43-2.036-3.496a3.394 3.394 0 0 1-2.25.85 3.394 3.394 0 0 1-2.25-.85C1.567 6.074.857 7.393.857 9.502c0 .911.643 1.64 1.426 1.64h5.72Z" fill="currentColor"/></g></svg> ${ticket.user_name}
                            </span>
                            <span
                              class="support-page__item__meta-item">
                              <svg width="12" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M1.143 11.143h9.428V4.286H1.143v6.857ZM3.714 3V1.071A.212.212 0 0 0 3.5.857h-.429a.212.212 0 0 0-.214.214V3c0 .12.094.214.214.214H3.5c.12 0 .214-.094.214-.214Zm5.143 0V1.071a.212.212 0 0 0-.214-.214h-.429A.212.212 0 0 0 8 1.071V3c0 .12.094.214.214.214h.429c.12 0 .214-.094.214-.214Zm2.572-.429v8.572a.863.863 0 0 1-.858.857H1.143a.863.863 0 0 1-.857-.857V2.57c0-.468.388-.857.857-.857H2v-.643C2 .482 2.482 0 3.071 0H3.5c.59 0 1.071.482 1.071 1.071v.643h2.572v-.643C7.143.482 7.625 0 8.214 0h.429c.59 0 1.071.482 1.071 1.071v.643h.857c.47 0 .858.389.858.857Z" fill="currentColor"/></g></svg>${t('Created')}: ${ moment(ticket.create_timestamp).format('ddd, MMM DD, YYYY h:mmA') }
                            </span>
                            <span
                              class="support-page__item__meta-item">
                              <svg width="12" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.464 7H8.09c.384 0 .697-.337.697-.75v-4.5c0-.413-.313-.75-.697-.75H2.053c-.384 0-.696.337-.696.75V9l2.476-2h2.631Zm-4.178-.05.071-.008L2.286 7v-.05Z" fill="currentColor"/><path d="M9.946 4h-.232v3c0 .55-.414.997-.924 1H4.143v.25c0 .414.312.75.696.75h3.328l2.476 2V4.75c0-.413-.313-.75-.697-.75Z" fill="currentColor"/></svg>${t('Last Reply')}: ${getLatestReplyDate(ticket.thread_entries)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="support-page__item__ticket-participants-section-col">
                        <div class="support-page__item__ticket-participants-section__label">
                          <span>
                          ${t('Department')}: <strong>${ ticket.department }</strong>
                          </span>
                          <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11 6v.333h1.667a1 1 0 0 1 1 1V13H14c.089 0 .173.035.236.098l.236-.236-.236.236a.333.333 0 0 1-.236.569H2a.333.333 0 0 1-.236-.57l-.235-.235.235.236A.333.333 0 0 1 2 13h.333V6a1 1 0 0 1 1-1H5V3.333a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1V6ZM5 6v-.333H3V13h2V6Zm8 1.333V7h-2v6h2V7.333Zm-2.666-4V3H5.667v10h4.667V3.333Zm-2 7V11h-.667v-.667h.667Zm0-2.666v.666h-.667v-.666h.667Zm0-2.667v.667h-.667V5h.667Z" fill="currentColor" stroke="currentColor" stroke-width=".667"/></svg>
                        </div>
                        <div class="support-page__item__ticket-participants-section__label">
                          <span>
                          ${t('Category')}: <strong>${ ticket.help_topic ? ticket.help_topic : '' }</strong>
                          </span>
                          <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.667 12V4c0-.278.095-.507.294-.706.2-.2.429-.294.705-.294H6.53l1.235 1.236.098.097H13.334c.277 0 .506.095.706.295.2.199.294.428.294.705V12a.952.952 0 0 1-.295.706c-.2.2-.428.294-.705.294H2.667a.952.952 0 0 1-.706-.294.949.949 0 0 1-.294-.706Zm.666 0v.333h11.334V5H7.588L6.353 3.764l-.098-.097H2.334V12Z" fill="currentColor" stroke="currentColor" stroke-width=".667"/></svg>
                        </div>
                      </div>
                    </div>

                    <div class="support-page__item__ticket-participants-section">
                      <div class="support-page__item__ticket-participants-section-row">
                        <div class="support-page__item__ticket-participants-section__label">
                          <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.563 5.25C12.47 6.52 11.527 7.5 10.5 7.5c-1.028 0-1.973-.979-2.063-2.25C8.345 3.928 9.262 3 10.5 3c1.238 0 2.156.952 2.063 2.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/><path d="M10.5 9.5c-2.036 0-3.995 1.012-4.486 2.982-.065.26.099.518.367.518h8.239c.268 0 .43-.258.366-.518C14.496 10.48 12.537 9.5 10.5 9.5Z" stroke="currentColor" stroke-miterlimit="10"/><path d="M6.25 5.81c-.073 1.016-.835 1.815-1.656 1.815s-1.585-.8-1.656-1.814C2.863 4.755 3.604 4 4.594 4c.99 0 1.73.774 1.656 1.81Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/><path d="M6.438 9.563c-.564-.259-1.186-.358-1.844-.358-1.625 0-3.19.807-3.583 2.381-.052.208.08.414.293.414h3.509" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round"/></svg>
                          Participants
                        </div>
                        <div class="support-page__item__ticket-participants-wrapper">${participantBadges}</div>
                      </div>
                      <div class="support-page__item-actions">
                        <button data-toggle="tooltip" data-placement="right" title="${t('Expand/Subtract Tickets')}" id="expandSubtractTickets" class="waves-effect button">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.6464 3.39645C11.8417 3.20118 12.1583 3.20118 12.3536 3.39645L17.6036 8.64645C17.7988 8.84171 17.7988 9.15829 17.6036 9.35355C17.4083 9.54882 17.0917 9.54882 16.8964 9.35355L12 4.45711L7.10355 9.35355C6.90829 9.54882 6.59171 9.54882 6.39645 9.35355C6.20118 9.15829 6.20118 8.84171 6.39645 8.64645L11.6464 3.39645ZM6.39645 14.6464C6.59171 14.4512 6.90829 14.4512 7.10355 14.6464L12 19.5429L16.8964 14.6464C17.0917 14.4512 17.4083 14.4512 17.6036 14.6464C17.7988 14.8417 17.7988 15.1583 17.6036 15.3536L12.3536 20.6036C12.1583 20.7988 11.8417 20.7988 11.6464 20.6036L6.39645 15.3536C6.20118 15.1583 6.20118 14.8417 6.39645 14.6464Z" fill="currentColor"/>
                          </svg>                                               
                        </button>
                        <button class="waves-effect button use-skeleton" type="button" id="printTicketThread" data-toggle="tooltip" data-placement="right" title="Entire Print Ticket Thread / History">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M20.1253 6.9375H18.5625V3.75C18.5625 3.60082 18.5032 3.45774 18.3977 3.35225C18.2923 3.24676 18.1492 3.1875 18 3.1875H6C5.85082 3.1875 5.70774 3.24676 5.60225 3.35225C5.49676 3.45774 5.4375 3.60082 5.4375 3.75V6.9375H3.87469C2.66906 6.9375 1.6875 7.86281 1.6875 9V16.5C1.6875 16.6492 1.74676 16.7923 1.85225 16.8977C1.95774 17.0032 2.10082 17.0625 2.25 17.0625H5.4375V20.25C5.4375 20.3992 5.49676 20.5423 5.60225 20.6477C5.70774 20.7532 5.85082 20.8125 6 20.8125H18C18.1492 20.8125 18.2923 20.7532 18.3977 20.6477C18.5032 20.5423 18.5625 20.3992 18.5625 20.25V17.0625H21.75C21.8992 17.0625 22.0423 17.0032 22.1477 16.8977C22.2532 16.7923 22.3125 16.6492 22.3125 16.5V9C22.3125 7.86281 21.3309 6.9375 20.1253 6.9375ZM6.5625 4.3125H17.4375V6.9375H6.5625V4.3125ZM17.4375 19.6875H6.5625V14.8125H17.4375V19.6875ZM21.1875 15.9375H18.5625V14.25C18.5625 14.1008 18.5032 13.9577 18.3977 13.8523C18.2923 13.7468 18.1492 13.6875 18 13.6875H6C5.85082 13.6875 5.70774 13.7468 5.60225 13.8523C5.49676 13.9577 5.4375 14.1008 5.4375 14.25V15.9375H2.8125V9C2.8125 8.48344 3.28875 8.0625 3.87469 8.0625H20.1253C20.7112 8.0625 21.1875 8.48344 21.1875 9V15.9375ZM18.5625 10.875C18.5625 11.0604 18.5075 11.2417 18.4045 11.3958C18.3015 11.55 18.1551 11.6702 17.9838 11.7411C17.8125 11.8121 17.624 11.8307 17.4421 11.7945C17.2602 11.7583 17.0932 11.669 16.9621 11.5379C16.831 11.4068 16.7417 11.2398 16.7055 11.0579C16.6693 10.876 16.6879 10.6875 16.7589 10.5162C16.8298 10.3449 16.95 10.1985 17.1042 10.0955C17.2583 9.99248 17.4396 9.9375 17.625 9.9375C17.8736 9.9375 18.1121 10.0363 18.2879 10.2121C18.4637 10.3879 18.5625 10.6264 18.5625 10.875Z" fill="currentColor"/>
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div class="support-page__ticket-divider">
                    </div>

                    <div class="support-page__single-ticket__threads"><div id="divTicketDetailsBody"></div></div>
                
                    <div class="new-support-ticket-dialog ${(ticket.ticket_status !== undefined && (ticket.ticket_status.toLowerCase() == 'closed' || ticket.ticket_status.toLowerCase() == 'resolved' )) ? 'new-support-ticket-dialog--closed' : ''}">
                        <div class="card replyContainer">
                            <div class="body">

                                ${

                                    (ticket.ticket_status !== undefined && (ticket.ticket_status.toLowerCase() == 'closed' || ticket.ticket_status.toLowerCase() == 'resolved' )) ? `

                                        <center class="re-open-ticket-container">
                                            <p>
                                                <b>${t('This ticket has been closed.')}</b><br><small>
                                                ${t('Please create a new ticket if you require support to an unrelated issue')}</small>
                                            </p>
                                            <p>
                                                <button type="button" class="button waves-effect" id="reopenTicketBtn">
                                                <svg width="21" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.317 6.667a7.918 7.918 0 0 1 14.862 1.401.625.625 0 1 0 1.213-.303C18.394 3.783 14.792.833 10.5.833a9.163 9.163 0 0 0-7.917 4.544V3.958a.625.625 0 0 0-1.25 0v3.125a.833.833 0 0 0 .834.834h3.125a.625.625 0 1 0 0-1.25H3.317Zm-.496 5.265a.625.625 0 0 0-1.213.303c.998 3.983 4.6 6.932 8.892 6.932a9.163 9.163 0 0 0 7.917-4.543v1.418a.625.625 0 1 0 1.25 0v-3.125a.833.833 0 0 0-.834-.834h-3.125a.625.625 0 1 0 0 1.25h1.975a7.92 7.92 0 0 1-14.862-1.401Z" fill="currentColor"/><path d="M11.678 11.178a1.666 1.666 0 1 1-2.397-2.315 1.666 1.666 0 0 1 2.397 2.315Z" fill="currentColor"/></svg> ${t('Re-open ticket & post reply')}
                                                </button>
                                            </p>
                                        </center>

                                    ` : ``
                                }

                                <fieldset class="${ (ticket.ticket_status !== undefined && (ticket.ticket_status.toLowerCase() == 'closed' || ticket.ticket_status.toLowerCase() == 'resolved' )) ? 'ticket-reply-closed' : '' }"><legend>${t('Post Reply/Comments')}</legend>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div id="replyMessageBody"></div>
                                        </div>
                                    </div>

                                    <div class="row topSpacer">
                                        <div class="col-sm-12">
                                            <div id="replyAttachmentsContainer" class="dropzone">
                                            <div class="dz-message dropzone-content">
                                              <p><strong>${t('Drag and Drop to Upload Attachments')}</strong> ${t('OR')}</p>
                                              <button>${t('Browse File')}</button>
                                            </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <button type="button" class="btn btn-primary waves-effect pull-right ticketBtn" id="btnSubmitReply">
                                                <i class="material-icons">send</i> ${t('Post Reply')}
                                            </button>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            let btns = [
                ['fontfamily'],
                ['fontsize'],
                ['strong', 'em', 'del']
            ]

            // On mobile devices - make the editor more compact...
            switch(getOS()) {
              case 'ios':
                btns.push(['foreColor'])
                break;
              case 'android':
                btns.push(['foreColor'])
                break;
              default:
                // Undo/Redo added to start of array...
                btns.unshift(['historyUndo','historyRedo']);
                btns.push(['foreColor', 'backColor'])
                btns.push(['link']);
            }

            $('div#replyMessageBody').trumbowyg({
                svgPath: '/assets/images/trumbowyg.svg',
                autogrow: true,
                defaultLinkTarget: '_blank',
                btns: btns
            })
            .on('tbwchange', function() {
                let content = $('#replyMessageBody').trumbowyg('html');
                if(content.trim() === '') {
                    $('#btnSubmitReply').attr('disabled', 'disabled');
                } else {
                    $('#btnSubmitReply').removeAttr('disabled');
                }
            })
            // Initially set the 'Reply' button as disabled right after initializing the editor... (as no content will be provided yet)
            $('#btnSubmitReply').attr('disabled', 'disabled');

            $("#reopenTicketBtn").click(function() {
                $(".re-open-ticket-container").hide();
                $('.new-support-ticket-dialog').removeClass('new-support-ticket-dialog--closed');
                $(".ticket-reply-closed").removeClass('ticket-reply-closed');
                $('#divTicketDetailsBody').addClass('sm-padding')
                var replyMessageBody= $('#replyMessageBody');
                setTimeout(function() {
                  replyMessageBody.focus();
                }, 100);
            });

            $("#printTicketThread").click(function() {
                printTicketThread(ticket.subject, ticket.ticket_number);
            });

            $('div#replyAttachmentsContainer').dropzone({
                action: 'none',
                url: '/api/non-existing-endpoint', //dummy endpoint
                autoProcessQueue: false, //don't auto upload files
                maxFilesize: 4, // MB
                parallelUploads: 1,
                maxFiles: 10,
                uploadMultiple: true,
                addRemoveLinks: true,

                // All files should be accepted for now...
                // acceptedFiles: "image/*,application/pdf,.doc,.docx,.xls,.xlsx,.csv,.tsv,.ppt,.pptx,.pages,.odt,.rtf",

                accept: function(file, done){
                    reader = new FileReader();
                    reader.onload = handleReaderLoad;
                    reader.readAsDataURL(file);
                    function handleReaderLoad(evt) {
                        replyAttachments.push({
                            [file.name]: evt.target.result
                        });
                    }

                    done();
                },
                init: function() {
                    this.on("addedfile", function(file) {
                         //remove the progress loading
                        $('.dz-progress').hide();
                    });
                }
            });

            displayTicketThreads(ticket.thread_entries, ticket.user_name);

            // If we're on a desktop - automatically subtract previous replies..
            if ($(window).width() >= 991) {
                $('#expandSubtractTickets').addClass("toggled");
                $(".ticketThreadItem").each(function() {
                  toggleExpandSubtractTickets(this);
                });
                toggleExpandSubtractAllTickets();
            }


        }

        

        function displayTicketThreads(thread_entries, poster = '') {
            //populate threads

            if(!thread_entries || thread_entries.length === 0) {
              return;
            }

            const threads = thread_entries.sort((thread1, thread2) => {
              return  +new Date(thread1.created) - +new Date(thread2.created)
            })
            .filter(thread => thread.type != 'N');

            const hasHiddenThreads = threads.length > 3;

            threads.map((thread, index) => {

              //there are 3 types of threads, M, R and N
              //and it looks like N is internal, so don't display it

              const isTicketPoster = thread.poster === poster;

              let htmlContents = thread.message.body;
              

              // if there are more thread items insert the hidden count
              if(index === 1 && threads.length > 3) {
                $('#divTicketDetailsBody').append(`<div class="support-page__ticket-count"><span>${threads.length - 3}</span></div>`);
                $('#divTicketDetailsBody').addClass('has-hidden-threads');
              }

              $('#divTicketDetailsBody')
              .append(`<div class="support-page__ticket-msg-item ${index !== 0 && index <= threads.length - 3 && hasHiddenThreads ? 'hidden' : ''} ${isTicketPoster ? 'support-page__ticket-msg-item--ticketPoster' : ''}">
                          <div class="support-page__ticket-msg-item__meta">
                            <span data-toggle="tooltip" data-placement="right" title="${t('Verified Support/Staff Member')}">${thread.poster}${ (thread.staff_id === null) ? '' : ' <i class="material-icons staff-badge-icon">verified_user</i>' }</span>: ${ moment(thread.created).format('llll') } (${ timeDifference(new Date(), new Date(thread.created)) })
                          </div>
                          <div class="header ticketThreadItem">

                              <div class="ticket-header">
                                  <div class="hidden-xs hidden-sm ticket-small-summary-contents" id="threadSummary-${thread.id}"></div>
                              </div>

                              <iframe width=100% data-hj-allow-iframe="" scrolling="no" frameBorder="0" height=1  id="ticketingThread-${thread.id}"
                              onload="
                                  this.contentDocument.body.style.fontFamily='Arial';
                                  this.contentDocument.body.style.fontSize='16px';
                                  this.contentDocument.body.style.margin='0px';
                              " ></iframe>

                              <div class="row">
                                  <div class="col-sm-12">
                                      <div class="thread-attachments" id="threadAttachments-${thread.id}"></div>
                                  </div>
                              </div>

                          </div>
                      </div>`);

              var $iframe = $(`#ticketingThread-${thread.id}`);

              $iframe.ready(function() {

                  let body = $iframe.contents().find("body");
                  body.append(htmlContents);

                  var iframe =  $iframe.get(0);

                  // Strip out any "CID:" images (as we can't view them anyway and they are shown as attachments below...
                  $(iframe.contentWindow.document.body).find("img[src^='cid:']").hide();

                  // Adjusting the iframe height
                  iframe.height = iframe.contentWindow.document.body.scrollHeight + "px";
                  
                  // Create/copy short summary text...
                  $(`#threadSummary-${thread.id}`).text(`
                      ${
                          iframe.contentWindow.document.body.innerText.substring(0,300) ||
                          iframe.contentWindow.document.body.textContent.substring(0,300)
                      }
                  `);

              });

              //check for files
              if(thread.attachments) {

                  for (var key in thread.attachments) {
                      if (thread.attachments.hasOwnProperty(key)) {

                          let url = thread.attachments[key].download_url;
                          let filename = thread.attachments[key].filename;
                          //let filetype = thread.attachments[key].filetype;

                          if(url) {
                              let splitted = filename.split(".");
                              let filetype =  splitted[splitted.length - 1];

                              //make unknown.svg the file icon if the file type has no corresponding icon
                              let fileicon = 'unknown'
                              let availIcons = ['jpeg', 'jpg', '7z', 'aac', 'ai', 'archive', 'arj', 'audio', 'avi', 'css', 'csv', 'dbf',
                                  'doc', 'dwg', 'exe', 'fla', 'flac', 'gif', 'html', 'iso', 'js', 'json', 'mdf', 'mp2', 'mp3', 'mp4',
                                  'mxf', 'nrg', 'pdf', 'png', 'ppt', 'psd', 'rar', 'rtf', 'svg', 'text', 'tiff', 'txt', 'unknown', 'video',
                                  'wav', 'wma', 'xls', 'xml', 'zip'
                              ];

                              if(availIcons.includes(filetype)){
                                  fileicon = filetype;
                              }

                              $(`#threadAttachments-${thread.id}`).append(`
                                  <div class="attachment" onclick="window.open('${url}')">
                                      <img target="_blank" class="file-img" src='/assets/images/pretty_file_icons/${fileicon}.svg' alt="${fileicon}.svg" />
                                      <a href="#" id="attachment-${key + thread.id}" >${filename}</a>
                                  </div>
                              `)
                          }

                      }
                  }

              }

              // view hidden threads
              $(document).off('click', '.support-page__ticket-count').on('click', '.support-page__ticket-count', function() {
                $(this).remove();
                $('#divTicketDetailsBody').removeClass('has-hidden-threads');
                $('#divTicketDetailsBody .support-page__ticket-msg-item.hidden').removeClass('hidden');
              })

          });
            

            // removed ticket header. please add this back if it is needed in other sections in app
            // .ticket-header
            $(".ticketThreadItem").click(function() {
                toggleExpandSubtractTickets(this);
            });

        }
    }
}

const getUploadSizeLeft = (size) => {
    const sizeLeft = UPLOAD_MAX_TOTAL_SIZE - size
    return sizeLeft < 0 ? 0 : sizeLeft
}

const structureDepartments = (departments) => {
    return departments.map(oDept => {
        return {
            department_id: oDept.department_name_id ? oDept.department_name_id : "",
            department_name: oDept.department_name ? oDept.department_name : "",
            department_signature: oDept.department_signature ? oDept.department_signature : "",
            topic_id: oDept.topic_id ? oDept.topic_id : "",
            help_topic: oDept.help_topic ? oDept.help_topic : [],
        }
    }).sort(function (a, b) {
        return a.department_name.localeCompare( b.department_name );
    })
}