const loadTrainingCampMembersPage = (clubId) => {
    const tcMembersTableId = '#tc-members-table';
    const seasonFilterId = 'tc-members-season';
    const onboardingFilterId = 'tc-members-onboarding';
    const filterIdLabelMap = {
        [seasonFilterId]: 'Camp',
        [onboardingFilterId]: 'Status',
    };
    const filterIdIndexMap = {
        [seasonFilterId]: 4,
        [onboardingFilterId]: 5,
    };
    const columnDef = [
        { title: '' },
        { title: t('Name'), className: 'col-name' },
        { title: t('Email Address'), orderable: false },
        { title: t('Gender') },
        {
            title: t('Current Camp'),
            className: 'col-current-camp',
            render: function (data, type) {
                if (type === 'sort') {
                    return data.sort;
                }
                if (type === 'filter') {
                    return data.allSeasons;
                }
                if (type === 'display') {
                    let displayEl = data.display;

                    if (data.tooltip) {
                        const tooltipEl = (tooltip, html) =>
                            `<span title="${tooltip}" data-toggle="tooltip">${html}</span>`;

                        if (data.concatBadgeText) {
                            const badgeEl = `<span class="ph-badge ph-badge--xsmall ml-2">${data.concatBadgeText}</span>`;
                            displayEl = tooltipEl(data.tooltip, `${displayEl} ${badgeEl} `);
                        } else {
                            displayEl = tooltipEl(data.tooltip, displayEl);
                        }
                    }

                    return displayEl;
                }
                return data.display;
            },
        },
        { title: t('Date Joined') },
    ];
    const maxRowLength = 20;
    const cacheRowsKey = 'tcMemberCacheRowLength';
    let cacheRowsLength = maxRowLength;
    let membersTable;
    let pageFilters;
    let urlParams;
    let selectedSeasonFilter = null;

    $(document).ready(function () {
        loadPage(clubId);
    });

    const initTooltips = () => {
        $('[data-toggle="tooltip"]').tooltip({
            container: 'body',
            open: handleArrowTooltipOpen
        });
    }

    const loadPage = async (clubId) => {
        // The usage of localStorage, cacheRowsLength, and cachedRows is to display the number of
        // skeleton rows correctly based on the number of rows loaded previously.
        const cachedRows = localStorage.getItem('tcMemberCacheRowLength');
        if (cachedRows !== null) {
            cacheRowsLength = Math.min(parseInt(cachedRows, 10), maxRowLength);
        }

        if (cacheRowsLength == 0) {
            cacheRowsLength = maxRowLength;
        }

        const $tcMembersTableContainer = $('#tc-members-table-container');
        const initTableContainer = () => {
            const skeletonRowValues = [
                '',
                '5b98d234-1a2b-3c4d-5e6f-789012345678',
                'Male',
                '2222',
                'Complete a 5k run under 25 minutes<br>Master double-unders',
                '2024-01-01',
            ];
            $tcMembersTableContainer.empty();
            $tcMembersTableContainer.prepend(
                `
                <table id="tc-members-table" class="table table-hover dataTable ph-table isLoading">
                    <thead>
                        <tr>
                            ${renderSkeletonColumn(columnDef)}
                        </tr>
                    </thead>
                    <tbody>
                        ${[...Array(cacheRowsLength)]
                    .map(() => `<tr>${renderSkeletonRow(skeletonRowValues)}</tr>`)
                    .join('')}
                    </tbody>
                </table>
            `
            );
        };

        $('#training-camp-members-page').addClass('isLoading');
        $('#training-camp-members-page .ph-filters-container').addClass('isLoading');

        wrapTableWithScrollableDiv(tcMembersTableId);

        initTableContainer();

        await Promise.all([getTCSeasonMembers({ clubId })])
            .then(([{ tcData, missingMMSUsers }]) => {
                let maxMembersInitPageLength = maxRowLength;

                renderTCMembersTable(tcData);

                if (membersTable) {
                    membersTable.on('draw.dt', function () {
                        initTooltips();
                    });
                }

                loadingOverlay('#tc-members-table-container', false);

                if (tcData.length <= maxRowLength) {
                    maxMembersInitPageLength = tcData.length;
                }

                localStorage.setItem(cacheRowsKey, maxMembersInitPageLength);

                $('#training-camp-members-page').removeClass('isLoading');

                return Promise.resolve(missingMMSUsers);
            })
            .then(async (missingMMSUsers) => {
                if (missingMMSUsers && missingMMSUsers.length > 0) {
                    const { tcData } = await getTCSeasonMembers({
                        clubId,
                        userIds: missingMMSUsers,
                        useCache: false,
                    });

                    updateTCMembersTable(tcData);
                }

                $('#training-camp-members-page .ph-filters-container').removeClass('isLoading');

            })
            .catch((error) => {
                console.error('Error fetching training camp members', error);
            });
    };

    const renderSkeletonColumn = (cols) => {
        const colStr = (name) => ` <th
                                class="sorting"
                                tabindex="0"
                            >
                                <span class="use-skeleton rounded">${name}</span>
                            </th>`;
        return cols.map((col) => colStr(col.title)).join('');
    };

    const renderSkeletonRow = (values) => {
        const rowStr = (value) => `<td><span class="use-skeleton rounded">${value}</span></td>`;
        return values.map((value) => rowStr(value)).join('');
    };

    const createFilterOptions = (data, convertToString = false, firstAsDefault = false) => {
        const allOption = { label: t('All'), value: 'all', selected: !firstAsDefault };
        const options = data.map((item, index) => ({
            label: convertToString ? item.toString() : item,
            value: convertToString ? item.toString() : item,
            selected: index === 0 && firstAsDefault,
        }));
        let defaultOption = { ...allOption };

        options.push(allOption);

        if (firstAsDefault) {
            defaultOption.value = options[0].value;
            defaultOption.label = options[0].label;
            defaultOption.selected = true;
        }

        return [options, defaultOption];
    };

    const renderPageFilters = (seasonData, onboardingData) => {
        const seasonOptions = createFilterOptions(seasonData, true, true);
        const seasonOptionsValues = seasonOptions[0];
        const defaultSeasonOption = seasonOptions[1];
        const onboardingOptionsValues = createFilterOptions(onboardingData)[0];

        pageFilters = new PageFilters(
            '#training-camp-members-page .ph-filters-container',
            [
                {
                    id: seasonFilterId,
                    label: filterIdLabelMap[seasonFilterId],
                    options: seasonOptionsValues,
                },
                {
                    id: onboardingFilterId,
                    label: filterIdLabelMap[onboardingFilterId],
                    options: onboardingOptionsValues,
                },
            ],
            filterOnChange,
            false
        );

        return { defaultFilter: { [seasonFilterId]: defaultSeasonOption.value } };
    };

    const filterOnChange = (filterObject) => {
        const filterId = Object.keys(filterObject)[0];
        const filterValue = filterObject[filterId];
        const filterIndex = filterIdIndexMap[filterId];

        if (filterValue && filterValue !== 'all') {
            if (filterId === seasonFilterId) {
                // Use regex with word boundaries to match the exact number in a comma-separated list
                const exactMatchRegex = `(^|,)\\s*${filterValue}\\s*(,|$)`;
                membersTable.column(filterIndex).search(exactMatchRegex, true, false).draw();
            } else {
                membersTable.column(filterIndex).search(filterValue, true, false).draw();
            }
        } else {
            membersTable.column(filterIndex).search('').draw();
        }

        if (filterId === seasonFilterId) {
            selectedSeasonFilter = filterValue;
            membersTable.rows().invalidate().draw();
        }
    };

    const getTCSeasonMembers = async ({ clubId, season, userIds, useCache = true, isUserMemberInfo = false }) => {
        const queryParams = {
            season,
            userIds,
            useCache,
            isUserMemberInfo,
        };
        const queryString = buildQueryParams(queryParams);
        const results = await apiCall(
            `/api/members-leads/trainingcamp/club/season-members/${clubId}${queryString}`,
            'GET',
            null,
            '5m'
        );
        if (results && 'status' in results && results.status === 'success') {
            const { tcData, missingMMSUsers } = results.data;

            return Promise.resolve({ tcData, missingMMSUsers });
        }

        return Promise.reject(results);
    };

    const renderTCMembersSearchbarHeader = () => {
        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => membersTable && membersTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => membersTable && membersTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => membersTable && membersTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => membersTable && membersTable.button(3).trigger() },
        ];
        searchbarHeader(
            '#training-camp-members-page .searchbar-header-container',
            exportButtons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 0,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'facilities-export-buttons',
                },
            }
        );
    };

    const parseFilterEmptyData = () => {
        if (!urlParams) {
            return t('No data available in table');
        }

        const paramsObj = Object.keys(urlParams).map((key) => {
            return {
                [filterIdLabelMap[key]]: urlParams[key],
            };
        });
        const defaultStr = t('No data available in table');
        let str = `No data available in table`;

        if (paramsObj.length > 0) {
            str = 'No data available in table';
        }

        return str;
    };

    const renderTCMembersTable = (members) => {
        const $tcMembersTable = $(tcMembersTableId);
        const $tcMembersTableBody = $tcMembersTable.find('tbody');
        const JOINED_STATUS = t('Joined');
        const INCOMPLETE_STATUS = t('Boarding Incomplete');
        let defaultFilter = null;
        let uniqueSeasons = new Set();
        let uniqueStatuses = new Set();

        $tcMembersTableBody.empty();

        const data = members.map((member) => {
            const { email = 'N/A', trainingCamp, fullName = 'N/A', gender = 'N/A', userId } = member;
            const seasonCount = trainingCamp.seasons ? Object.keys(trainingCamp.seasons).length : 0;
            const recentSeason = trainingCamp.recentSeason ?? 'N/A';
            const recentCamp = recentSeason ? trainingCamp.seasons[recentSeason] : null;
            const seasons = Object.keys(trainingCamp.seasons) ?? [];
            const recentCampDateJoined = recentCamp ? recentCamp.createdAt : null;
            const genderText = t(gender);
            let displayText = recentSeason;
            let tooltipText = '';
            const seasonData = {
                display: displayText,
                sort: recentSeason.toString(),
                allSeasons: seasons.join(','),
                tooltip: null,
                concatBadgeText: null,
            };
            const dateJoinedData = {
                display: '',
                sort: 0,
                filter: INCOMPLETE_STATUS,
                concatBadgeText: null,
            };

            if (seasonCount > 1) {
                const additionalCount = seasonCount - 1;
                displayText = `${recentSeason}, +${additionalCount}`;
                tooltipText = seasons.sort((a, b) => b - a).join(', ');
                seasonData.concatBadgeText = `+${additionalCount} ${t('more')}`;
                seasonData.tooltip = tooltipText;
            }

            const onboardingStatus = 'onboarding' in recentCamp ? recentCamp.onboarding?.complete : false;
            let onboardingStatusLabel = INCOMPLETE_STATUS;

            if (recentCampDateJoined) {
                dateJoinedData.display = unixTimeFormat(recentCampDateJoined);
                dateJoinedData.sort = recentCampDateJoined;

                if (!onboardingStatus) {
                    dateJoinedData.concatBadgeText = INCOMPLETE_STATUS;
                }
            } else if (onboardingStatus) {
                dateJoinedData.concatBadgeText = JOINED_STATUS;
                dateJoinedData.display = '';
            }

            if (!onboardingStatus) {
                onboardingStatusLabel = INCOMPLETE_STATUS;
                dateJoinedData.filter = INCOMPLETE_STATUS;
                dateJoinedData.concatBadgeText = INCOMPLETE_STATUS;
            }

            if (!recentCampDateJoined) {
                dateJoinedData.sort = 0;
            }

            if (recentCampDateJoined || onboardingStatus) {
                onboardingStatusLabel = JOINED_STATUS;
                dateJoinedData.filter = JOINED_STATUS;
            }

            uniqueStatuses.add(onboardingStatusLabel);
            seasons.forEach((season) => uniqueSeasons.add(season));

            const providedData = {
                seasonAdequateDataMap: trainingCamp.seasonAdequateDataMap || {},
                recentSeason: recentSeason,
            };

            return [providedData, fullName, email, genderText, seasonData, dateJoinedData];
        });

        if (members.length > 0) {
            uniqueStatuses = Array.from(uniqueStatuses).sort();
            uniqueSeasons = Array.from(uniqueSeasons).sort((a, b) => b - a);

            const pageFiltersData = renderPageFilters(uniqueSeasons, uniqueStatuses);
            defaultFilter = 'defaultFilter' in pageFiltersData ? pageFiltersData.defaultFilter : null;

            urlParams = getQueryStringsObject(window.location.search);
            selectedSeasonFilter = urlParams[seasonFilterId];

            columnDef[0] = {
                title: '',
                className: 'col-data-status',
                render: function (data, type) {
                    const adequacyDataMap = data.seasonAdequateDataMap;

                    if (type !== 'sort' && type !== 'display') {
                        return data;
                    }

                    let currentSeasonFilter = selectedSeasonFilter || pageFilters?.getValue(seasonFilterId);
                    let seasonFilter = currentSeasonFilter;

                    // If "all" is selected OR the current filter season isn't in the user's data, use recent season
                    if (currentSeasonFilter === 'all' || !(currentSeasonFilter in adequacyDataMap)) {
                        seasonFilter = data.recentSeason;
                    }

                    const adequacyData =
                        seasonFilter in adequacyDataMap ? adequacyDataMap[seasonFilter] : { level: 0, score: 0 };

                    const adequacyLevel = Number(adequacyData.level) || 0;
                    const adequacyScore = Number(adequacyData.score) || 0;

                    if (type === 'sort') {
                        const sortValue = adequacyLevel + adequacyScore;

                        if (sortValue === 0) {
                            return -999999;
                        }

                        return sortValue;
                    }

                    let dotClass = '--inactive';

                    if (adequacyLevel > 0) {
                        if (adequacyLevel <= 25) {
                            dotClass = '--level-25';
                            tooltipText = t('Initial data available');
                        } else if (adequacyLevel <= 50) {
                            dotClass = '--level-50';
                            tooltipText = t('Has goals/journals and metrics with start values');
                        } else if (adequacyLevel <= 75) {
                            dotClass = '--level-75';
                            tooltipText = t('All metrics complete with start, goal and result values');
                        } else {
                            dotClass = '--level-100';
                            tooltipText = t('Full training data with complete metrics and goals/journals');
                        }
                    } else {
                        tooltipText = t('No data available');
                    }

                    return `<div class="has-data-dot-container" data-toggle="tooltip" title="${tooltipText}"><span class="has-data-dot --bg"></span><span class="has-data-dot ${dotClass}"></span></div>`;
                },
            };

            columnDef[5] = {
                title: t('Date Joined'),
                render: function (data, type) {
                    if (type === 'sort') {
                        return data.sort;
                    }
                    if (type === 'filter') {
                        return data.filter;
                    }
                    if (type === 'display' && data.concatBadgeText) {
                        return `${data.display}<span class="ph-badge ph-badge--xsmall ${data.display ? 'ml-2' : ''}">${data.concatBadgeText
                            }</span>`;
                    }
                    return data.display;
                },
            };

            renderTCMembersSearchbarHeader();
        }

        const export_filename = 'Training-Camp-Members__' + new Date().toISOString();

        membersTable = $tcMembersTable.DataTable({
            responsive: true,
            iDisplayLength: maxRowLength,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [
                [5, 'desc'],
                [0, 'desc'],
            ],
            bAutoWidth: false,
            data: data ?? [],
            columns: columnDef,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default',
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Training Camp Members',
                    className: 'btn btn-sm btn-default',
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv',
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf',
                },
            ],
            createdRow: function (row, data, dataIndex) {
                const member = members[dataIndex];
                if (member && member.userId) {
                    $(row).attr('data-user-id', member.userId);
                }
            },
            search: {
                regex: true,
                smart: false,
            },
            language: {
                paginate: {
                    previous: t('Previous'),
                    next: t('Next'),
                },
                infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
                info: t('Showing') + ' _START_ ' + t('to') + ' _END_ ' + t('of') + ' _TOTAL_ ' + t('entries'),
                emptyTable: t('No data available in table'),
                zeroRecords: t('No matching records found'),
                infoFiltered: `(${t('filtered from')} _MAX_ ${t('total entries')})`,
            },
            columnDefs: [
                {
                    targets: 0,
                    type: 'num',
                    orderSequence: ['desc', 'asc'],
                },
            ],
        });

        $('#training-camp-members-page .search-bar').on('keyup search', (e) => {
            const value = $(e.currentTarget).val();
            if (membersTable) {
                membersTable.search(value).draw();

                updateHrefQuery({
                    search: value || undefined,
                });
            }
        });

        // Remove any existing search extensions to avoid duplicates
        $.fn.dataTable.ext.search.pop();

        $(`${tcMembersTableId} tbody`).on('click', 'tr', function () {
            const userId = $(this).data('user-id');
            if (userId) {
                // Get current query parameters
                const currentQueryString = window.location.search;
                // Redirect to user detail page with preserved query parameters
                sammy.setLocation(`/training-camp/${userId}${currentQueryString}`);
            }
        });

        applyUrlParameters(defaultFilter);

        // initTooltips();
    };

    const applyUrlParameters = (defaultFilter) => {
        urlParams = getQueryStringsObject(window.location.search);

        if (urlParams.search) {
            $('#training-camp-members-page .search-bar').val(urlParams.search);
            membersTable.search(urlParams.search).draw();
        }

        const initialFilters = {
            ...defaultFilter,
            ...urlParams,
        };

        if (pageFilters) {
            Object.entries(filterIdIndexMap).forEach(([filterId]) => {
                if (initialFilters[filterId]) {
                    pageFilters.selectOption(filterId, initialFilters[filterId], false);
                }
            });
        }
    };

    const updateTCMembersTable = (members) => {
        if (membersTable) {
            membersTable.destroy();
        }

        renderTCMembersTable(members);
    };
};
