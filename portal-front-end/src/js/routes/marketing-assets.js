let storedAsset = {},
    storedAssets = [],
    assetObject = {};

function resetAssetObject() {
    assetObject = {
        id: null,
        name: null,
        tags: [],
        brand: 'ALL',
        url: '',
        thumbnail: '',
        deleted: 0,
    };
}

function sAssetsBrand() {}

/**
 * Save asset to databse
 *
 * @param payload
 * @returns callback response
 * @method PUT
 */
const saveAsset = (payload, callback) => {
    $.ajax({
        url: '/api/marketing/assets/save',
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(payload),
        success: function (response) {
            callback(response);
        },
        error: function (error) {
            instantNotification(
                // Message...
                t('<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to save your asset changes...<br>Please contact HQ for further details.'),
                // Type...
                'error',
                // Timeout (1m)...
                60000
            );
        },
    });
};

/**
 * Get asset by id from database
 *
 * @param id
 * @returns assets
 * @method GET
 */
const getAssetById = (id, callback) => {
    $.ajax({
        url: '/api/marketing/assets/get/' + id,
        method: 'GET',
        success: function (response) {
            callback(response);
        },
        error: function (error) {
            console.log('Unable to get assets');
        },
    });
};

/**
 * Validate asset name, asset tags & file uploaded
 *
 * @returns boolean to check if it is valid
 */
const validate = () => {
    let nameIsValid = false,
        tagsAreValid = false,
        nameEl = $('#asset_name'),
        tagsEl = $('#asset_tags');

    $('#asset_name, #asset_tags').parents('.form-group').find('label.error').remove();

    if (nameEl.val() === '') {
        nameIsValid = false;
        nameEl.parents('.form-group').append('<label class="error">'+t('This field is required')+'</label>');
        nameEl.parent().addClass('focused error');
    } else {
        nameEl.removeClass('error');
        nameIsValid = true;
    }

    if (!tagsEl.val().length) {
        tagsAreValid = false;
        tagsEl
            .parent()
            .find('.select2')
            .addClass('error')
            .parent()
            .append('<label class="error">'+t('This field is required')+'</label>');
    } else {
        tagsEl.removeClass('error');
        tagsAreValid = true;
    }

    return nameIsValid && tagsAreValid;
};

/**
 * Delete asset
 *
 * @params id of the asset that will be deleted
 */
function deleteAsset(id) {
    getAssetById(id, function (response) {
        storedAsset = {};
        storedAsset = response.Item;
        storedAsset.tags = JSON.parse(storedAsset.tags);

        swalWithBootstrapButtons
            .fire({
                title: t('Delete Asset?'),
                text: `${t('Are you sure you wish to delete the asset')} '${storedAsset.name}'? ${t('THIS CAN NOT BE UNDONE!')}`,
                type: 'warning',
                width: 500,
                showCancelButton: true,
                confirmButtonText: t('Yes, proceed'),
            })
            .then((result) => {
                if (result.value) {
                    storedAsset = { ...storedAsset, deleted: 1 };

                    saveAsset(storedAsset, function (response) {
                        if (response) {
                            // Delete asset in DOM element
                            $('#asset_manager-list .row').children(`[data-id='${response.id}']`).remove();

                            // Delete specific asset inside stored assets state
                            storedAssets = storedAssets.filter((asset) => asset.id !== response.id);

                            instantNotification(
                                // Message...
                                t('Delete asset successfully!!'),
                                // Type...
                                'success',
                                // Timeout (1m)...
                                60000
                            );
                        }
                    });
                }
            });
    });
}

/**
 * Edit asset
 *
 * @params id of the asset that will be updated
 */
function editAsset(id) {
    getAssetById(id, function (response) {
        storedAsset = {};
        storedAsset = response.Item;
        storedAsset.tags = JSON.parse(storedAsset.tags);

        const setupHTML = `
            <div class="upload-asset-dialog ph-dialog-v2" style="padding: 20px;">
                <fieldset>
                    <label for="asset_name">${t('Name')}</label>
                    <div class="form-group">
                        <div class="form-line">
                            <input type="text" id="asset_name" class="form-control asset-name" placeholder="${t('Enter asset name')}">
                        </div>
                    </div>
                    <label for="asset_tags">${t('Tags')}</label>
                    <div class="form-group asset-tags__container">
                        <select class="select-tags" id="asset_tags"></select>
                    </div>
                    <label for="asset_brand">${t('Brand')}</label>
                    <div class="form-group">
                        <select id="asset_brand" class="form-control">
                            <option value="ALL">${t('All - (Design works with any Brand)')}</option>
                            <option value="12RND">12RND</option>
                            <option value="UBX">UBX</option>
                        </select>
                    </div>
                </fieldset>
            </div>
        `;

        swalWithBootstrapButtons
            .fire({
                width: 600,
                html: setupHTML,
                title: t('Edit Asset'),
                showCloseButton: true,
                showCancelButton: true,
                confirmButtonText: t('Update'),
                cancelButtonText: t('Cancel'),
                onBeforeOpen: () => {
                    // Custom toggle focus
                    $('#asset_name')
                        .focus(function () {
                            $(this).parent().addClass('focused');
                        })
                        .blur(function () {
                            $(this).parent().removeClass('focused');
                        });

                    $('#asset_name').val(storedAsset.name);
                    $('#asset_brand').val(storedAsset.brand);

                    $('#asset_brand').on('change', function () {
                        const value = $(this).val();
                        storedAsset.brand = value;
                    });

                    // Grouping Tags Select Multiple Autocomplete
                    setGroupingTags(storedAsset.tags, 'assets');
                },
                preConfirm: () => {
                    //Render validations
                    const isValid = validate();
                    return isValid;
                },
            })
            .then((result) => {
                if (result.value) {
                    storedAsset.name = $('#asset_name').val();
                    storedAsset.tags = $('#asset_tags').val();

                    saveAsset(storedAsset, function (response) {
                        if (response) {
                            let { name, tags, brand } = response;
                            tags = JSON.parse(tags);

                            // Update asset name and tags in DOM
                            const targetHTML = $('#asset_manager-list .row').children(`[data-id='${response.id}']`);
                            targetHTML.find('.caption h4').html(name);
                            targetHTML.find('.asset-tags').html('');
                            tags.map((tag) => {
                                targetHTML
                                    .find('.asset-tags')
                                    .prepend(`<span class="badge"><small>${tag}</small></span>`);
                            });

                            targetHTML.find('#asset_brand').val(brand);

                            instantNotification(
                                // Message...
                                t('Update asset successfully!!'),
                                // Type...
                                'success',
                                // Timeout (1m)...
                                60000
                            );
                        }
                    });
                }
            });
    });
}

/**
 * Binding & Rerender functions
 *
 */
function bindAssetActions() {
    $('.delete-asset').on('click', function () {
        const assetId = $(this).data('id');
        deleteAsset(assetId);
    });

    if (storedAssets.length > 0) {
        $('#asset_manager-upload').html(`
            <button type="button" class="btn btn-primary waves-effect upload-asset">
                <i class="material-icons m-r-10">file_upload</i>${t('Upload New Asset')}
            </button>
        `);
    } else {
        $('#asset_manager-upload').html(`
            <div class="card upload-asset">
                <i class="material-icons">add_circle</i>
                <small>${t('Upload File Here')}</small>
            </div>
        `);
    }

    $('.edit-asset').on('click', function () {
        const assetId = $(this).data('id');
        editAsset(assetId);
    });

    $('.upload-asset').on('click', function () {
        uploadAsset();
    });
}

function assetActionsHTML(assetId) {
    return `<div class="asset-actions">
        <button type="button" class="btn btn-xs btn-primary waves-effect btn-circle waves-effect waves-circle waves-float edit-asset m-r-5" data-id="${assetId}"><i class="material-icons">edit</i></button>
        <button type="button" class="btn btn-xs btn-danger waves-effect btn-circle waves-effect waves-circle waves-float delete-asset" data-id="${assetId}"><i class="material-icons">delete</i></button>
    </div>`;
}

/**
 * Load assets list to asset manager
 *
 * @method GET
 * @returns assets from database
 */
function loadAssetsList() {
    $('#asset_manager-list').html('');

    $.ajax({
        url: '/api/marketing/assets/get',
        method: 'GET',
        contentType: 'application/json',
        success: function (assets) {
            // This will update stored assets current state
            storedAssets = assets;

            let assetListHTML = '<div class="row">';

            assets.forEach((asset) => {
                asset.tags = JSON.parse(asset.tags);

                assetListHTML += `<div class="col-sm-6 col-md-4 col-lg-3 col-xl-2 asset-holder" data-id="${asset.id}">
                        <div class="thumbnail">
                            <div class="thumbnail-img">
                                <a href="${asset.url}" target="_blank">
                                    <img src="${asset.thumbnail}">
                                </a>`;

                assetListHTML += assetActionsHTML(asset.id);

                assetListHTML += `</div>
                                <div class="caption">
                                <h4>${asset.name}</h4>
                                <div>
                                    <b>${t('ID')}: </b>
                                    <code style="font-size: 10px;">${asset.id}</code>
                                </div>
                                <div class="asset-tags">
                `;
                asset.tags.map((tag) => {
                    assetListHTML += `<span class="badge"><small>${tag}</small></span>`;
                });

                assetListHTML += `</div></div></div></div>`;
            });

            assetListHTML += '</div>';

            $('#asset_manager-list').append(assetListHTML);

            bindAssetActions();
        },
        error: function (error) {
            $('#asset_manager-list').append(`
                <div class="row"><p>${error.message}</p></div>
            `);

            console.log(error);
        },
    });
}

/**
 * Dropzone file uploader
 *
 * @param acceptedFiles, swal, assetUpload
 * @returns callback uploaded asset object
 */
function dzFileUpload(acceptedFiles, swal, assetUpload, setNameFromDZ, callback) {
    let assetCompleted = assetUpload;

    const dz = $('#imageDZUploads').dropzone({
        url: '/api/asset-upload',
        acceptedFiles,
        addRemoveLinks: true,
        parallelUploads: 1,
        maxFiles: 1,
        maxFilesize: 50,
        timeout: 3600000, // Uploading large files
        dictMaxFilesExceeded: t('You can only upload one file.'),
        dictDefaultMessage: t('Drop file or click here to upload'),
        sending: (file, xhr, formData) => {
            setNameFromDZ(file);
            //Execute on case of timeout only
            xhr.ontimeout = function (e) {
                //Output timeout error message here
                console.log('Server Timeout');
            };
        },
        addedfiles: (file) => {
            file.forEach((f) => {
                if (f.accepted) {
                    $('#imageDZUploads').removeClass('error');
                    $('[class^="upload-status"]').remove();
                    $('.dropzone-holder').prepend(`
                        <small class="upload-status-added">${t('Uploading file - please wait (saving will be available after upload complete)')}....</small>
                    `);

                    swal.getConfirmButton().setAttribute('disabled', '');
                } else {
                    if (file.length > 1) {
                        $('[class^="upload-status"]').remove();
                        $('.dropzone-holder').prepend(`
                            <small class="upload-status-error">${t('You can only upload one file, the first one will get uploaded.')}</small>
                        `);
                    }
                }
            });
        },
        queuecomplete: () => {
            swal.getConfirmButton().removeAttribute('disabled');
        },
        maxfilesexceeded: (file) => {
            // Need to disabled click upload after adding file
            dz[0].dropzone.options.removedfile(file);
        },
        removedfile: (file) => {
            $('.upload-status-completed').remove();
            $(file.previewElement).remove();

            if (assetCompleted.length > 0) {
                callback([]);
            }
        },
        success: function (file, response) {
            $('[class^="upload-status"]').remove();

            if (file.status === 'success') {
                $('.dropzone-holder').prepend(`
                    <small class="upload-status-completed">File succcesfully uploaded!</small>
                `);
            }

            assetCompleted = [...assetCompleted, response];
            callback(assetCompleted);
        },
        error: (error, message) => {
            //Error Messages
            $('[class^="upload-status"]').remove();
            $('.dropzone-holder').prepend(`
                <small class="upload-status-error">${message}</small>
            `);
        },
    });
}

/**
 * Upload assets to asset manager
 *
 */
function uploadAsset() {
    resetAssetObject();

    const setupHTML = `
        <div class="upload-asset-dialog ph-dialog-v2" style="padding: 20px;">
            <fieldset>
                <label for="asset_name">${t('Name')}</label>
                <div class="form-group">
                    <div class="form-line">
                        <input type="text" id="asset_name" class="form-control" placeholder="${t('Enter asset name')}">
                    </div>
                </div>
                <label for="asset_tags">${t('Tags')}</label>
                <div class="form-group asset-tags__container">
                    <select class="select-tags" id="asset_tags"></select>
                </div>
                <label for="asset_brand">${t('Brand')}</label>
                <div class="form-group">
                    <select id="asset_brand" class="form-control">
                        <option value="ALL">${t('All - (Design works with any Brand)')}</option>
                        <option value="12RND">12RND</option>
                        <option value="UBX">UBX</option>
                    </select>
                </div>
                <div class="m-t-20">
                    <div class="dropzone-holder">
                        <div id="imageDZUploads" class="dropzone"></div>
                    </div>
                </div>
            </fieldset>
        </div>
    `;

    let assetUpload = [];
    swalWithBootstrapButtons
        .fire({
            width: 600,
            html: setupHTML,
            title: t('Upload Asset'),
            showCancelButton: true,
            confirmButtonText: t('Upload'),
            cancelButtonText: t('Cancel'),
            allowEscapeKey: false,
            showCloseButton: true,
            allowOutsideClick: false,
            onBeforeOpen: () => {
                // Grouping Tags Select Multiple Autocomplete
                setGroupingTags(assetObject.tags, 'assets');

                // Custom toggle focus
                $('#asset_name')
                    .focus(function () {
                        $(this).parent().addClass('focused');
                    })
                    .blur(function () {
                        $(this).parent().removeClass('focused');
                    })
                    .on('input', (e) => {
                        assetObject.name = $(e.currentTarget).val();
                    });

                $('#asset_tags').on('change', (e) => {
                    assetObject.tags = $(e.currentTarget).val();
                })

                $('#asset_brand').on('change', function () {
                    const value = $(this).val();
                    assetObject.brand = value;
                });

                dzFileUpload(
                    '.jpeg,.jpg,.png',
                    swalWithBootstrapButtons,
                    assetUpload,
                    (response) => response,
                    function (response) {
                        if (response) {
                            assetUpload = response;
                        }
                    }
                );
            },
            preConfirm: () => {
                //Render validations
                const isValid = validate();
                const isAssetUploaded = assetUpload.length !== 0;
                $('[class^="upload-status"]').remove();

                if (!isAssetUploaded) {
                    $('#imageDZUploads').addClass('error');
                    $('.dropzone-holder').prepend(`
                    <small class="upload-status-error">${t('Please upload one file.')}</small>
                `);
                } else {
                    $('#imageDZUploads').removeClass('error');
                }

                return isAssetUploaded && isValid;
            },
        })
        .then((result) => {
            if (result.value) {
                const { s3Id, variations } = assetUpload[0];

                assetObject.id = s3Id;
                assetObject.url = variations.image[0];
                assetObject.thumbnail = variations.thumbnails[0];

                saveAsset(assetObject, function (asset) {
                    if (asset) {
                        asset.tags = JSON.parse(asset.tags);

                        let addAssetHTML = `
                        <div class="col-sm-6 col-md-4 col-lg-3 col-xl-2 asset-holder" data-id="${asset.id}">
                            <div class="thumbnail">
                                <div class="thumbnail-img">
                                    <img src="${asset.url}">`;

                        addAssetHTML += assetActionsHTML(asset.id);

                        addAssetHTML += `</div>
                                    <div class="caption">
                                    <h4>${asset.name}</h4>
                                    <div><b>ID: </b> <code style="font-size: 10px;">${asset.id}</code></div>
                                    <div class="asset-tags">
                    `;
                        asset.tags.map((tag) => {
                            addAssetHTML += `<span class="badge"><small>${tag}</small></span>`;
                        });

                        addAssetHTML += `</div></div></div></div>`;

                        $('#asset_manager-list .row').prepend(addAssetHTML);

                        // This will update the stored assets current state
                        storedAssets.push(asset);

                        bindAssetActions();
                        resetAssetObject();

                        instantNotification(
                            // Message...
                            t('Upload asset successfully!!'),
                            // Type...
                            'success',
                            // Timeout (1m)...
                            60000
                        );
                    }
                });
            }
        });
}

/**
 * Load asset list from database
 *
 * @returns assets
 */
function sAssetManager() {
    loadAssetsList();
}
