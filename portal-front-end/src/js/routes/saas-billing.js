function sSaaSBilling() {
    const basePath = '/saas-billing';
    const tabs = ['overview', 'cost-breakdown', 'service-pricing', 'payment-settings'];

    function init() {
        initUrl();
        initTabs();
        handleTabChange();
    }

    function initUrl() {
        const tab = location.pathname.split('/').pop();
        if (tabs.includes(tab)) return;
        history.pushState({ tabChange: true }, null, `${basePath}/${tabs[0]}`);
    }

    function initTabs() {
        const tab = location.pathname.split('/').pop();

        $(`.nav-tabs a[href="#${tab}"]`).tab('show');
        $('.nav-tabs a').on('click', function (e) {
            e.preventDefault();
            const href = $(this).attr('href');
            const formattedHref = `${basePath}/${href.replace('#', '')}`;
            history.pushState({ tabChange: true }, null, formattedHref);
            handleTabChange();
        });
    }

    function handleTabChange() {
        const tab = location.pathname.split('/').pop();
        if (tab === 'overview') return sSaaSBillingOverview();
        if (tab === 'cost-breakdown') return sSaaSBillingCostBreakdown();
        if (tab === 'service-pricing') return sSaaSBillingServicePricing();
        if (tab === 'payment-settings') return sSaaSBillingPaymentSettings();
    }

    function createSidebarLinks() {
      const servicePricingContent = $('.service-pricing__content');
      const allHeadings = servicePricingContent.find('h1, h2, h3, h4, h5, h6');
      const servicePricingSidebarList = $('.service-pricing__sidebar ul');
      const containerFluid = $('#SaaSBilling .container-fluid');
      servicePricingSidebarList.html('');

      allHeadings.each(function() {
        const currentText = $(this).text();
        const sectionId = currentText.toLowerCase().replace(/[^a-z0-9]/g, '-');
        servicePricingSidebarList.append(`<li><a href="#${sectionId}">${currentText}</a></li>`);
        $(this).attr('id', sectionId);
      });
      
      const isInViewport = function (elem) {
        const bounding = elem.getBoundingClientRect();
        return (
          bounding.top >= 0 &&
          bounding.left >= 0 &&
          bounding.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          bounding.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
      };

      containerFluid.on('scroll', function() {

        // exist each when first item is found
        allHeadings.each(function() {
          if (isInViewport(this)) {
            servicePricingSidebarList.find('a').removeClass('active');
            servicePricingSidebarList.find(`a[href="#${$(this).attr('id')}"]`).addClass('active');
            return false;
          }
        });
      });


      servicePricingSidebarList.find('a').on('click', function(e) {
        e.preventDefault();
        const href = $(this).attr('href');
        const target = $(href);
        const offsetTop = target.position().top + containerFluid.scrollTop();
        containerFluid.animate({ scrollTop: offsetTop }, 500);
        servicePricingSidebarList.find('a').removeClass('active');
        $(this).addClass('active');
      });

      containerFluid.scrollspy({ target: '.service-pricing__sidebar ul' })
    }

    async function loadServicePricingTable() {
        $('.base-charges-container .service-pricing__table tbody').html(`
            <tr>
                <td><span class="use-skeleton">Commodo commodo officia consectetur</span></td>
                <td><span class="use-skeleton">$20.00</span></td>
            </tr>
            <tr>
                <td><span class="use-skeleton">Commodo commodo officia consectetur</span></td>
                <td><span class="use-skeleton">$20.00</span></td>
            </tr>
        `)


        const table = $('.service-pricing__table');
        table.addClass('isLoading');
        $('.base-charges-container').show();

        const [breakdownRes, additionalRes] = await Promise.all([
            getClubBillingChargeBreakdownAPI(selectedID),
            listClubUpcomingAdditionalItemsAPI(selectedID),
        ]);
        if (!breakdownRes[0]) return instantNotification(breakdownRes[1], 'error');
        if (!additionalRes[0]) return instantNotification(additionalRes[1], 'error');
        table.removeClass('isLoading');

        const breakdown = breakdownRes[1].charges;
        table.find('.price-per-coaching-screen').text(formatAmount(breakdown.pricePerCoachingScreen));
        table.find('.price-per-duress-button').text(formatAmount(breakdown.pricePerDuressButton));
        table.find('.price-per-cctv-device').text(formatAmount(breakdown.pricePerCamera));
        table.find('.price-per-cctv-storage').text(formatAmount(breakdown.pricePerGBStored));
        table.find('.extended-access-software-charges').text(formatAmount(breakdown.performanceHubEASoftware));

        const additionalItems = additionalRes[1];
        if (!additionalItems.length) return $('.base-charges-container').hide();
        $('.base-charges-container .service-pricing__table tbody').empty();
        additionalItems.forEach((item) => {
            const row = `
                <tr>
                    <td>${item.name}</td>
                    <td>${formatAmount(item.amount)}</td>
                </tr>
            `;
            $('.base-charges-container .service-pricing__table tbody').append(row);
        });
    }

    function formatAmount(amount) {
        const abs = Math.abs(amount);
        const isNegative = amount < 0;
        return `${isNegative ? '-' : ''}$${abs.toFixed(2)}`;
    }

    function sSaaSBillingServicePricing() {
        createSidebarLinks();
        loadServicePricingTable();
    }

    init();
}
