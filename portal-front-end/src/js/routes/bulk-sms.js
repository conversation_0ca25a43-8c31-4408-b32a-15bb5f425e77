let smsCompleteLastKey = null;
let smsPendingLastKey = null;
let smsFailedLastKey = null;
let smsLimit = 10;
let smsMasterList = [];

let startOfMonth = moment().startOf('month');
let endOfMonth = moment().endOf('month');
let filterSmsStartDateUnix = null;
let filterSmsEndDateUnix = null;
let filterSearch = null;
let filterSmsStatus = [];
let defaultSmsStatus = ['complete', 'pending', 'failed'];

let smsSeachDebounce = null;
let smsDebounceQuery = null;

let lastSmsTableRowObserver = new IntersectionObserver(entries => {
    const lastRow = entries[0];
    if (!lastRow.isIntersecting) return
    loadMoreSmsHistory();
    lastSmsTableRowObserver.unobserve(lastRow.target);
});

function sBulkSMS() {
    // scrolls down 20px from the top of the document, show the button
    window.onscroll = function () {
        scrollFunction();
    };

    function scrollFunction() {
        if (
            document.body.scrollTop > 150 ||
            document.documentElement.scrollTop > 150
        ) {
            $('#btn-back-to-top').css({display: 'block'});
        } else {
            $('#btn-back-to-top').css({display: 'none'});
        }
    }

    $('#btn-back-to-top').on('click', function() {
        window.scrollTo({top: 0, behavior: 'smooth'});
    })

  loadingOverlay("#bulk-sms-history-container");

  const unsubString = ` Opt out: https://ubx.fit/u`;

    searchbarHeader('.messages-search-nav', [{ label: 'New SMS', icon: 'add', onClick: newBulkSMS }]);

    $('#smsStatusSelect').selectpicker();
    $('#smsStatusSelect').val(defaultSmsStatus);
    $('#smsStatusSelect').selectpicker('refresh');
    $('#smsStatusSelect').on('change', function() {
        callSmsDebounceQuery();
    })

    $("#bulk-sms-history-container").empty();
    $("#bulk-sms-history-container").html(`
        <table width="100%" id="bulk-sms-history-table" class="table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Performance Hub Sender</th>
                    <th>Message From</th>
                    <th>Recipients</th>
                    <th>Preview</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    `);

    $('.bulk-sms .search-bar').on('keyup', function () {
        clearTimeout(searchDebounce)
        searchDebounce = null
        searchDebounce = setTimeout(() => {
            filterSearch = this.value ? this.value : null;
            callSmsDebounceQuery();
        }, 450)
    });

    /** filter date range **/
    const filterSmsDaterangeChange = (start, end) => {
        const startDate = start.format('MMMM D, YYYY');
        const endDate = end.format('MMMM D, YYYY');
        $('#sms-history-daterange span').html(`${startDate} - ${endDate}`);

        filterSmsStartDateUnix = start.unix();
        filterSmsEndDateUnix = end.unix();
        callSmsDebounceQuery();
    }

    filterSmsStartDateUnix = startOfMonth.unix();
    filterSmsEndDateUnix = endOfMonth.unix();

    if (filterSmsStartDateUnix && filterSmsEndDateUnix) {
        $('#sms-history-daterange span').html(`${startOfMonth.format('MMMM D, YYYY')} - ${endOfMonth.format('MMMM D, YYYY')}`);
    }

    const initSmsDaterangePicker = () => {
        let start = moment().startOf('month');
        let end = moment().endOf('month');

        $('#sms-history-daterange').daterangepicker({
            startDate: start,
            opens: 'left',
            endDate: end,
            maxDate: end,
            ranges: {
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().startOf('month'), moment().endOf('month')],
                'Last 2 Months': [moment().subtract(2, 'month').startOf('month'), moment().endOf('month')],
                'Last 4 Months': [moment().subtract(4, 'month').startOf('month'), moment().endOf('month')],
                'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment().endOf('month')],
                'Last 12 Months': [moment().subtract(12, 'month').startOf('month'), moment().endOf('month')],
                'Last 2 Years': [moment().subtract(2, 'years').startOf('month'), moment().endOf('month')],
            }
        }, filterSmsDaterangeChange);
    }

    initSmsDaterangePicker();
    fetchInitialSmsHistory(renderSmsHistoryMessages);

    /** if enter key is pressed perform search **/
    $(document).keyup(function(event) {
        if (event.key === 'Enter') {
            callSmsDebounceQuery();
        }
    });

  function newBulkSMS() {
      var smsDialog = `
          <div class="bulk-sms-dialog ph-dialog-v2" style="padding: 20px;">
              <fieldset>
                  <div class="row">
                      <div class="col-sm-2">
                          To Group:
                      </div>
                      <div class="col-sm-4">
                          <div class="form-group form-group-no-margin">
                              <div class="form-line">
                                  <select title="Select group to bulk SMS..." class="select-recipient-type form-control show-tick selectpicker" required>
                                      <optgroup label="Clubs & Franchisees">
                                          <option value="clubnumbers">Club Mobiles</option>
                                      </optgroup>
                                      <optgroup label="Member Database">
                                          <option value="memberscurrent">Club Members - Current</option>
                                          <option value="membersexpired">Club Members - Expired & Recently Expired</option>
                                          <option value="membershold">Club Members - Hold</option>
                                      </optgroup>
                                      <optgroup label="Other">
                                          <option value="customlist">Custom List (Manual Input)</option>
                                      </optgroup>
                                  </select>
                              </div>
                          </div>
                      </div>
                      <div class="col-sm-1">
                          From:
                      </div>
                      <div class="col-sm-5">
                          <div class="form-group form-group-no-margin">
                              <div class="form-line">
                                  <input class="form-control" id="fromText" type="text" value="UBX HQ" placeholder="Name Text, or Twilio Mobile Number">
                              </div>
                          </div>
                      </div>
                  </div>
                  <div class="row toNumbers-row">
                      <div class="col-sm-2">
                          Numbers:
                      </div>
                      <div class="col-sm-10">
                          <div class="form-group form-group-no-margin">
                              <div class="form-line tags-overflow">
                                  <input id="toNumbers" type="text" value="" data-role="tagsinput" placeholder="Separate multiple numbers with comma, provide in international format if sending outside of AU.">
                              </div>
                          </div>
                      </div>
                  </div>
                  <div class="row club-members-opts-row">
                      <div class="col-sm-2">
                          Options:
                      </div>
                      <div class="col-sm-10">
                          <div class="form-group form-group-no-margin">

                              <select title="Select Club(s) members to message..." data-live-search="true" multiple class="select-group-clubs form-control show-tick selectpicker show-tick">
                              </select>

                              <div class="form-check styled-checkbox" style="margin-top:5px; margin-left: 7px;">
                                  <input type="checkbox" class="form-check-input filled-in chk-col-blue" id="allClubs">
                                  <label class="form-check-label" for="allClubs">Message all Clubs</label>
                              </div>

                          </div>
                      </div>
                  </div>
              </fieldset>
              <fieldset><legend>Message Body</legend>
                  <div class="row">
                      <div class="col-sm-12">

                          <div class="form-group">
                              <div class="form-line">
                                  <textarea rows="4" class="form-control no-resize" id="messageBody" placeholder="Type Message..."></textarea>
                              </div>
                          </div>
                          <div class="form-check styled-checkbox" style="margin-top:-20px;">
                              <input type="checkbox" class="form-check-input filled-in chk-col-blue" id="optOutText">
                              <label class="form-check-label" for="optOutText">Include Opt Out Links</label>
                          </div>
                          <div style="width:60%">
                              <small>Note numbers that have previously opted out will automatically be excluded from SMS messaging when using the <u>Member Database</u> options</small>
                          </div>

                          <div class="text-length-counter"></div>
                      </div>
                  </div>
              </fieldset>
          </div>
      `;

      let lJQ = $, smsObj;

      swalWithBootstrapButtons.fire({
          title: 'New Bulk SMS',
          html: smsDialog,
          width: 900,
          showCloseButton: true,
          showConfirmButton: true,
          showCancelButton: true,
          allowOutsideClick: false,
          confirmButtonText: `Send!`,
          cancelButtonText: "Cancel",
          heightMax: 300,
          onBeforeOpen: () => {

              // Populate clubs in club select dropdown...
              for (let club in ClubsObj) {
                  $(".select-group-clubs").append(new Option(ClubsObj[club].name, ClubsObj[club].id));
              }
              $('.select-group-clubs').selectpicker('refresh');

              // Bind 'material design' to elements...
              $(".select-recipient-type").focus();
              lJQ.AdminBSB.input.activate();
              lJQ('.select-recipient-type').selectpicker();
              $("#optOutText").change(function() {
                  if(this.checked) {
                      $("#messageBody")[0].value += `${unsubString}`
                  } else {
                      $("#messageBody")[0].value = $("#messageBody").val().replace(`${unsubString}`, '');
                  }
              });

              $("#allClubs").change(function() {
                  if(this.checked) {
                      $(".select-group-clubs").prop('disabled', true);
                  } else {
                      $(".select-group-clubs").prop('disabled', false);
                  }
                  $('.select-group-clubs').selectpicker('refresh');
                  validate();
              });

              smsLength();
              validate();

              $("#toNumbers").tagsinput();

              $("#messageBody").on("input",function(e){ smsLength(); });
              $("#messageBody").keyup(function() { smsLength(); });

              function smsLength() {
                  $(".text-length-counter").html(`<b>${ $("#messageBody").val().length } Characters</b><br>*Messages over 160 characters will be charged as 2 or more SMS's`)
                  validate();
              }

              $("#fromText").change(function() {
                  validate();
              });
              $('.select-recipient-type').change(function() {
                  validate();
              });
              $('.select-group-clubs').change(function() {
                  validate();
              });

              $('#toNumbers').on('itemAdded', function(event) {
                  validate();
              });

              $('#toNumbers').on('itemRemoved', function(event) {
                  validate();
              });


              function validate() {
                  var isValid = true;

                  if(! $("#messageBody").val()) isValid = false;
                  if(! $("#fromText").val()) isValid = false;

                  if($('.select-recipient-type')[1].value == 'customlist') {
                      $(".toNumbers-row").show();
                      if($("#toNumbers").tagsinput('items').length == 0) isValid = false;
                  } else {
                      $(".toNumbers-row").hide();
                  }

                  if(
                      $('.select-recipient-type')[1].value == 'memberscurrent' ||
                      $('.select-recipient-type')[1].value == 'membersexpired' ||
                      $('.select-recipient-type')[1].value == 'membershold'
                  ) {
                      $(".club-members-opts-row").show();
                      if(!$("#allClubs").is(':checked') && $(".select-group-clubs").selectpicker('val').length < 1) isValid = false;
                  } else {
                      $(".club-members-opts-row").hide();
                  }

                  if(isValid) {
                      $('.swal2-actions .swal2-confirm').prop('disabled', false);
                  } else {
                      $('.swal2-actions .swal2-confirm').prop('disabled', true);
                  }
              }
          },
          onClose: () => {
              smsObj = {
                  to: $('.select-recipient-type')[1].value,
                  allClubs: $("#allClubs").is(':checked'),
                  clubList: $(".select-group-clubs").selectpicker('val'),
                  toList: $("#toNumbers").tagsinput('items'),
                  fromNameNumber: $("#fromText").val(),
                  messageBody: $("#messageBody").val()
              }
          }
      }).then(response => {
          if (response.value) {
            // loadingOverlay("#bulk-sms-history-container");
              let req = $.ajax({
                  url: '/api/admin/sms/new-message',
                  method: 'POST',
                  contentType: 'application/json',
                  data: JSON.stringify(smsObj),
                  success: function(data){
                        resetSmsLastKeys();
                        fetchInitialSmsHistory(renderSmsHistoryMessages);
                      if((data === undefined) ? false : data !== false) {
                        instantNotification(
                            // Message...
                            'Messages queued for sending!'
                        );
                      } else {
                          instantNotification(
                              // Message...
                              '<b>FAILED SEND MESSAGES!</b><br><br>An error occurred while trying to batch send messages...<br>Please refer to server logs for details.',
                              // Type...
                              'error',
                              // Timeout (1m)...
                              60000
                          );
                          console.dir(response);
                      }
                      $('#bulk-sms-history-container').LoadingOverlay('hide');
                  },
                  error: function(xhr, textStatus, errorThrown){
                    $('#bulk-sms-history-container').LoadingOverlay('hide');
                      console.error(xhr, textStatus, errorThrown);
                      instantNotification(
                          '<b>FAILED SEND MESSAGES!</b><br><br>An error occurred while trying to batch send messages...<br>Please refer to server logs for details.',
                          'error',
                          60000
                      );
                  }
              });
          }
      });

  }
}

const resetSmsLastKeys = () => {
    smsCompleteLastKey = null;
    smsPendingLastKey = null;
    smsFailedLastKey = null;
}

/** debounce query **/
const callSmsDebounceQuery = () => {
    filterSmsStatus = $('#smsStatusSelect').val();
    clearTimeout(smsDebounceQuery)
    smsDebounceQuery = null
    smsDebounceQuery = setTimeout( async () => {
        loadingOverlay("#bulk-sms-history-container");
        resetSmsLastKeys();
        smsMasterList = [];
        await filterSearchSmsHistory(renderSmsHistoryMessages);
    }, 650);
  }

/** initial fetch sms history **/
const filterSearchSmsHistory = async (renderCallback) => {
    let smsStatusCompleteItems = [];
    let smsStatusPendingItems = [];
    let smsStatusFailedItems = [];

    let smsStatusComplete = Promise.resolve([]);
    let smsStatusPending = Promise.resolve([]);
    let smsStatusFailed = Promise.resolve([]);

    /** api recursive **/
    const recursiveSmsStatusCompleteApi = async (lastProcessedKey=null) => {
        return await new Promise( async (resolve, reject) => {
            try {
                const queryString = getSmsFilterQueryString(lastProcessedKey, "complete");
                let recurseResult = await uniApiConCall(`/api/admin/sms/message-history${queryString}`);
                if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
                    const smsStatusCompleteData = recurseResult.data
                    const dataItems = 'Items' in smsStatusCompleteData ? smsStatusCompleteData.Items : []
                    /** save the last key for next query **/
                    if (smsStatusCompleteData && 'LastEvaluatedKey' in smsStatusCompleteData) {
                        smsCompleteLastKey = smsStatusCompleteData.LastEvaluatedKey;
                    } else {
                        smsCompleteLastKey = null;
                    }

                    if (dataItems.length) {
                        smsStatusCompleteItems = [...smsStatusCompleteItems, ...dataItems];
                    }

                    /** keep on query **/
                    if (smsCompleteLastKey !== null && smsStatusCompleteItems.length < 1) {
                        await recursiveSmsStatusCompleteApi(smsCompleteLastKey);
                    }
                    resolve(smsStatusCompleteItems);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /** api recursive pending **/
    const recursiveSmsStatusPendingApi = async (lastProcessedKey=null) => {
        return await new Promise( async (resolve, reject) => {
            try {
                const queryString = getSmsFilterQueryString(lastProcessedKey, "pending");
                let recurseResult = await uniApiConCall(`/api/admin/sms/message-history${queryString}`);
                if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
                    const smsStatusPendingData = recurseResult.data
                    const dataItems = 'Items' in smsStatusPendingData ? smsStatusPendingData.Items : []
                    /** save the last key for next query **/
                    if (smsStatusPendingData && 'LastEvaluatedKey' in smsStatusPendingData) {
                        smsPendingLastKey = smsStatusPendingData.LastEvaluatedKey;
                    } else {
                        smsPendingLastKey = null;
                    }

                    if (dataItems.length) {
                        smsStatusPendingItems = [...smsStatusPendingItems, ...dataItems];
                    }

                    /** keep on query **/
                    if (smsPendingLastKey !== null && smsStatusPendingItems.length < 1) {
                        await recursiveSmsStatusPendingApi(smsPendingLastKey);
                    }
                    resolve(smsStatusPendingItems);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /** api recursive failed **/
    const recursiveSmsStatusFailedApi = async (lastProcessedKey=null) => {
        return await new Promise( async (resolve, reject) => {
            try {
                const queryString = getSmsFilterQueryString(lastProcessedKey, "failed");
                let recurseResult = await uniApiConCall(`/api/admin/sms/message-history${queryString}`);
                if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
                    const smsStatusFailedData = recurseResult.data
                    const dataItems = 'Items' in smsStatusFailedData ? smsStatusFailedData.Items : []
                    /** save the last key for next query **/
                    if (smsStatusFailedData && 'LastEvaluatedKey' in smsStatusFailedData) {
                        smsFailedLastKey = smsStatusFailedData.LastEvaluatedKey;
                    } else {
                        smsFailedLastKey = null;
                    }

                    if (dataItems.length) {
                        smsStatusFailedItems = [...smsStatusFailedItems, ...dataItems];
                    }

                    /** keep on query **/
                    if (smsFailedLastKey !== null && smsStatusFailedItems.length < 1) {
                        await recursiveSmsStatusFailedApi(smsFailedLastKey);
                    }
                    resolve(smsStatusFailedItems);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /** status complete **/
    if (filterSmsStatus.includes('complete')) {
        smsStatusComplete = recursiveSmsStatusCompleteApi()
        .then(result => {
            if (result && result.length) {
                return result;
            }
            return [];
        })
        .catch(err => {
            console.log(err);
            return [];
        });
    }

    /** staus pending **/
    if (filterSmsStatus.includes('pending')) {
        smsStatusPending = recursiveSmsStatusPendingApi()
        .then(result => {
            if (result && result.length) {
                return result;
            }
            return [];
        })
        .catch(err => {
            console.log(err);
            return [];
        });
    }

    /** status failed **/
    if (filterSmsStatus.includes('failed')) {
        smsStatusFailed = recursiveSmsStatusFailedApi()
        .then(result => {
            if (result && result.length) { return result }
            return [];
        })
        .catch(err => {
            console.log(err);
            return [];
        });
    }

    let smsPromises = [
        smsStatusComplete,
        smsStatusPending,
        smsStatusFailed
    ];

    $('#empty-sms-history').addClass('hidden');
    $('#searching-sms-history').removeClass('hidden');

    /** this part is important if in fist load due to await **/
    const [
        smsStatusCompleteResponse,
        smsStatusPendingResponse,
        smsStatusFailedResponse,
    ] = await Promise.all(smsPromises);

    const combinedData = [
        ...smsStatusCompleteResponse,
        ...smsStatusPendingResponse,
        ...smsStatusFailedResponse
    ].sort((a, b) => {
        return new Date(b.sendDate) - new Date(a.sendDate);
    });

    smsMasterList = combinedData; // renew the master list
    renderCallback(combinedData); // render the result

    $('#bulk-sms-history-container').LoadingOverlay('hide');
    $('#searching-sms-history').addClass('hidden');
}

/** initial fetch sms history **/
const fetchInitialSmsHistory = async (renderCallback) => {
    let combinedInitSmsList = [];
    const statusComplete = getSmsList(getSmsFilterQueryString(null, "complete"));
    const statusPending = getSmsList(getSmsFilterQueryString(null, "pending"));
    const statusFailed = getSmsList(getSmsFilterQueryString(null, "failed"));

    const [
        smsStatusCompleteInitResponse,
        smsStatusPendingInitResponse,
        smsStatusFailedInitResponse
    ] = await Promise.all([statusComplete, statusPending, statusFailed]);

    $('#bulk-sms-history-container').LoadingOverlay('hide');

    /** complete **/
    if (smsStatusCompleteInitResponse) {
        const smsData1 = smsStatusCompleteInitResponse
        const dataItems1 = 'Items' in smsData1 ? smsData1.Items : []
        /** save the last key for next query **/
        smsCompleteLastKey = (smsData1 && 'LastEvaluatedKey' in smsData1) ? smsData1.LastEvaluatedKey : null;
        combinedInitSmsList = [...combinedInitSmsList, ...dataItems1];
    }

    /** pending **/
    if (smsStatusPendingInitResponse) {
        const smsData2 = smsStatusPendingInitResponse
        const dataItems2 = 'Items' in smsData2 ? smsData2.Items : []
        /** save the last key for next query **/
        smsPendingLastKey = (smsData2 && 'LastEvaluatedKey' in smsData2) ? smsData2.LastEvaluatedKey : null;
        combinedInitSmsList = [...combinedInitSmsList, ...dataItems2];
    }

    /** failed **/
    if (smsStatusFailedInitResponse) {
        const smsData3 = smsStatusFailedInitResponse
        const dataItems3 = 'Items' in smsData3 ? smsData3.Items : []
        /** save the last key for next query **/
        smsFailedLastKey = (smsData3 && 'LastEvaluatedKey' in smsData3) ? smsData3.LastEvaluatedKey : null;
        combinedInitSmsList = [...combinedInitSmsList, ...dataItems3];
    }

    // clear the filter status
    filterSmsStatus = [];
    // clear the master list
    smsMasterList = [];
    const list = updateSmsMasterList(combinedInitSmsList);

    renderCallback(list);
}

/** get sms list **/
const getSmsList = async (queryString='') => {
    const result = await uniApiConCall(`/api/admin/sms/message-history${queryString}`);
    if (result && 'status' in result && result.status == 'success') {
        return result.data;
    }
    return null;
}

/** just to update the master list **/
const updateSmsMasterList = (list) => {
    smsMasterList = [...smsMasterList, ...list].sort((a, b) => {
        return new Date(b.sendDate) - new Date(a.sendDate);
    });

    return smsMasterList;
}

/** load more **/
const loadMoreSmsHistory = async () => {
    loadingOverlay("#bulk-sms-history-container");
    let combinedLoadMoreData = [];
    if (smsCompleteLastKey) {
        const completeResponse = await getSmsList(getSmsFilterQueryString(smsCompleteLastKey, 'complete')); // a status complete response
        if (completeResponse) {
            const smsDataMore1 = completeResponse
            const dataMoreItems1 = 'Items' in smsDataMore1 ? smsDataMore1.Items : [];

            /** save the last key for next query **/
            smsCompleteLastKey = smsDataMore1 && 'LastEvaluatedKey' in smsDataMore1 ? smsDataMore1.LastEvaluatedKey : null;
            combinedLoadMoreData = [...combinedLoadMoreData, ...dataMoreItems1];
        }
    }

    if (smsPendingLastKey) {
        const pendingResponse = await getSmsList(getSmsFilterQueryString(smsPendingLastKey, 'pending')); // a status pending response
        if (pendingResponse) {
            const smsDataMore2 = pendingResponse
            const dataMoreItems2 = 'Items' in smsDataMore2 ? smsDataMore2.Items : [];

            /** save the last key for next query **/
            smsPendingLastKey = smsDataMore2 && 'LastEvaluatedKey' in smsDataMore2 ? smsDataMore2.LastEvaluatedKey : null;
            combinedLoadMoreData = [...combinedLoadMoreData, ...dataMoreItems2];
        }
    }

    if (smsFailedLastKey) {
        const failedResponse = await getSmsList(getSmsFilterQueryString(smsFailedLastKey, 'failed')); // a status failed response
        if (failedResponse) {
            const smsDataMore3 = failedResponse
            const dataMoreItems3 = 'Items' in smsDataMore3 ? smsDataMore3.Items : [];

            /** save the last key for next query **/
            smsFailedLastKey = smsDataMore3 && 'LastEvaluatedKey' in smsDataMore3 ? smsDataMore3.LastEvaluatedKey : null;
            combinedLoadMoreData = [...combinedLoadMoreData, ...dataMoreItems3];
        }
    }

    if (combinedLoadMoreData.length) {
        const loadedList = updateSmsMasterList(combinedLoadMoreData);
        $('#bulk-sms-history-table tbody').empty();
        loadedList.forEach((sms) => {
            $('#bulk-sms-history-table tbody').append(getSmsHistoryTableRow(sms));
        });
        let lastRow = document.querySelector('#bulk-sms-history-table tbody .message:last-child');
        if (lastRow !== null) {
            lastSmsTableRowObserver.observe(lastRow);
        }
        bindMessageClickEvents();
    }
    $('#bulk-sms-history-container').LoadingOverlay('hide');
}

/** Use the status as hash in dynamodb **/
const getSmsFilterQueryString = (lastKey, status="complete") => {
    let queryString = {
        status,
        search: filterSearch,
        limit: smsLimit,
        startDate: filterSmsStartDateUnix,
        endDate: filterSmsEndDateUnix,
    };

    if (lastKey) {
        queryString.LastEvaluatedKey = JSON.stringify(lastKey);
    }

    let stringUrlQuery = stringifyQueryStringsObject(queryString);

    return stringUrlQuery;
}

/** render sms table **/
const renderSmsHistoryMessages = (data) => {
    if (data && data.length) {
        $('#empty-sms-history').addClass('hidden');
        $('#bulk-sms-history-table tbody').empty();
        data.forEach((sms) => {
            $('#bulk-sms-history-table tbody').append(getSmsHistoryTableRow(sms));
        });
        let lastRow = document.querySelector('#bulk-sms-history-table tbody .message:last-child');
        if (lastRow !== null) {
            lastSmsTableRowObserver.observe(lastRow);
        }
        bindMessageClickEvents();
    } else {
        $('#bulk-sms-history-table tbody').empty();
        $('#empty-sms-history').removeClass('hidden');
    }
}

/** render row of table **/
const getSmsHistoryTableRow = (message) => {
    return `
        <tr class="message" data-id="${ message.id }">
            <td data-sort="${ message.sendDate }">${ moment.unix(message.sendDate).format("YYYY-MM-DD HH:mm A") }</td>
            <td>${ message.phSender }</td>
            <td>${ message.fromNameNumber }</td>
            <td data-sort="${ Object.keys(message.toReceipients).length }" >${ Object.keys(message.toReceipients).length } Numbers</td>
            <td>${ message.messageBody.trunc(60) }</td>
            <td>${ formatStatus(message.status) }</td>
        </tr>
    `;
}

const formatStatus = (status) => {
    let statusHtml = '';
    switch(status.toLowerCase()) {
        case 'complete':
            statusHtml = '<div class="badge badge-green">Sending Complete <i class="fa fa-check" aria-hidden="true"></i></div>'
            break;
        case 'pending':
            statusHtml = '<div class="badge badge-blue">Sending in Progress <i class="fa fa-refresh" aria-hidden="true"></i></div>'
            break;
        case 'failed':
            statusHtml = '<div class="badge badge-red">Sending Failed <i class="fa fa-exclamation-triangle" aria-hidden="true"></i></div>'
            break;
        default:
            break;
    }

    return statusHtml;
}

function bindMessageClickEvents() {
    // Unbind any old events first
    $("#bulk-sms-history-table tbody .message").off('click');

    // Bind on message click to open message details...
    $("#bulk-sms-history-table tbody .message").click(function () {
        messageDetailsModel($(this).data("id"));
    });
}

function messageDetailsModel(id) {
    let message = smsMasterList.find(message => message.id == id)
    console.log('message:', message, 'id:', id)
    let messageDetails = `
        <div class="message-details-dialog ph-dialog-v2">
            <div class="body">
                <div class="row mb-1">
                    <div class="col-sm-2">
                        Sent On:
                    </div>
                    <div class="col-sm-4">
                        <div>${ moment.unix(message.sendDate).format("YYYY/MM/DD HH:mm A") }</div>
                    </div>
                    <div class="col-sm-2">
                        Sender:
                    </div>
                    <div class="col-sm-4">
                        <div>${ message.phSender }</div>
                    </div>
                </div>
                <div class="row mb-1">
                    <div class="col-sm-2">
                        Status:
                    </div>
                    <div class="col-sm-4">
                        <div>${ formatStatus(message.status) }</div>
                    </div>
                    <div class="col-sm-2">
                        Summary:
                    </div>
                    <div class="col-sm-4">
                        <div>${ Object.keys(message.toReceipients).length } Recipients</div>
                    </div>
                </div>
                <div class="row mb-1">
                    <div class="col-sm-2">
                        Recipients:
                    </div>
                    <div class="col-sm-10">
                        <div style="max-height: 100px; overflow-y: scroll;">${ formatRecipients(message.toReceipients) }</div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-2">
                        Message Body:
                    </div>
                    <div class="col-sm-10">
                        <div><pre>${ message.messageBody }</pre></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    function formatRecipients(toReceipients) {
        const recipients = typeof toReceipients === 'string'
            ? { [toReceipients]: toReceipients }
            : toReceipients;

        return Object.keys(recipients)
            .map((number) => {
                return `<a href="https://www.twilio.com/console/sms/logs/${toReceipients[number]}" target="_blank">${number}</a>`;
            })
            .join('<br/>');
    }

    swalWithBootstrapButtons.fire({
        title: 'Message Details',
        html: messageDetails,
        width: 820,
        showCloseButton: true,
        showConfirmButton: false,
        showCancelButton: false,
        cancelButtonText: "Close"
    });
}