
function sSaaSBillingPaymentSettings() {
    let settingsData = {};
    let stripePublishableKey = '';
    let platformConnectedAccountId = '';
    let recordsThatNeedAuth = [];
    let failedPaymentRecords = [];

    const root = $('#payment-settings');

    async function init() {
        root.addClass('isLoading');
        renderCardsGrid();
        renderInvoiceDetails();

        await syncClubPaymentMethodsAPI(selectedID);

        const [settingsRes, stripeKeyRes, platformKeyRes, flaggedRecordsRes] = await Promise.all([
            getClubSaasBillingPaymentSettingsAPI(selectedID),
            getStripePublishableKeyAPI(),
            getPlatformConnectedAccountIdAPI(),
            listClubFlaggedRecordsAPI(selectedID),
        ]);

        root.removeClass('isLoading');

        if (!settingsRes[0] || !stripeKeyRes[0] || !platformKeyRes[0] || !flaggedRecordsRes[0]) return renderPageError();

        settingsData = settingsRes[1];
        stripePublishableKey = stripeKeyRes[1];
        platformConnectedAccountId = platformKeyRes[1];

        recordsThatNeedAuth = flaggedRecordsRes[1].filter((record) => record.status === '3d-secure-required');
        failedPaymentRecords = flaggedRecordsRes[1].filter((record) => record.status === 'failed');

        settingsData.paymentMethods =
            settingsData.paymentMethods?.sort((a, b) => {
                if (a.id === settingsData.primaryPaymentMethodID) return -1;
                if (b.id === settingsData.primaryPaymentMethodID) return 1;
                return 0;
            }) ?? [];

        renderCardsGrid();
        renderInvoiceDetails();
    }

    async function reCheckOutstandingPayments() {
        const flaggedRecordsRes = await listClubFlaggedRecordsAPI(selectedID);

        // check if there are any new records that need auth
        const newRecordsThatNeedAuth = flaggedRecordsRes[1].some(
            (record) => ![...recordsThatNeedAuth, ...failedPaymentRecords].map((r) => r.id).includes(record.id)
        );

        if (!newRecordsThatNeedAuth) return;

        recordsThatNeedAuth = flaggedRecordsRes[1].filter(record => record.status === '3d-secure-required');
        failedPaymentRecords = flaggedRecordsRes[1].filter(record => record.status === 'failed');

        renderCardsGrid();
    }


    function renderCardsGrid() {
        const container = root.find('.cards-grid');
        container.empty();
        settingsData.paymentMethods?.forEach((element) => {
            renderPMCardThumbnail(container, element);
        });
        renderAddCardThumbnail(container);

        if (recordsThatNeedAuth.length) {
            requiresActionAlert();
            const { authPayment } = getQueryStringsObject();

            if(authPayment && authPayment == 'true') {
                $(".payment-settings-wrapper").find('.authorize-payment').trigger( "click" );
            }

        } else if (failedPaymentRecords.length) {
            failedPaymentAlert();
        } else if (settingsData.paymentMethods?.length === 1) {
            noBackupPaymentMethodWarning();
        } else if (settingsData.paymentMethods?.length) {
            $('.cards-alerts').html('').addClass('hidden');
        } else if (settingsData.paymentMethods) {
            noPaymentMethodWarning();
        }
    }

    function renderPMCardThumbnail(container, pm) {
        const isPrimary = pm.id === settingsData.primaryPaymentMethodID;
        const { brand, last4, expMonth, expYear } = pm.card;
        const labelEl = pm.label ? `<h3 class="card-label">${pm.label}</h3>` : '';
        const isExpired = moment().isAfter(moment({ year: expYear, month: expMonth - 1 }));
        const expiredClx = isExpired ? 'expired' : '';

        const expirationDetails = isExpired
            ? `<p class="expiration expired">${t('EXPIRED')}</p>`
            : `<p class="expiration">${t('EXPIRES')} ${t(expMonth.toString())}/${t(expYear.toString())}</p>`;

        const div = $(`
            <div class="card-thumbnail ${brand} ${expiredClx}">
                <div class="d-flex gap-1 justify-between">
                    <div class="card-details">
                        ${labelEl}
                        <h3 class="card-brand">${brand} •••• ${last4}</h3>
                        ${expirationDetails}
                    </div>
                    <img class="card-logo" src="/assets/images/pm_logos/${brand.toLowerCase()}-logo.svg" alt="${brand}" />
                </div>
                <div class="d-flex gap-1 justify-between">
                    <button class="btn btn-link set-primary ${isPrimary ? 'is-primary' : ''}">
                        ${isPrimary ? t('Primary') : t('Backup')}
                    </button>
                    <div class="card-actions">
                        <button class="btn btn-link delete-button"><i class="fa fa-trash"></i></button>
                        <button class="btn btn-link edit-button"><i class="fa fa-edit"></i></button>
                    </div>
                </div>
            </div>
        `);

        if (isPrimary) {
            div.find('.card-actions .delete-button').attr('title', 'Primary payment method cannot be deleted');
            div.find('.card-actions .delete-button').attr('data-toggle', 'tooltip');
            div.find('.card-actions .delete-button').tooltip();
        } else {
            div.find('.delete-button').on('click', () => deletePaymentMethod(pm.id));
            div.find('.set-primary').on('click', () => setCardAsPrimaryConfirm(pm.id));
        }

        div.find('.edit-button').on('click', () => paymentMethodDialog(pm));
        container.append(div);
    }

    function deletePaymentMethod(id) {
        confirmDeleteDialog(null, null, () => {
            deleteClubSaasBillingPaymentMethodAPI(selectedID, id).then((res) => {
                if (!res[0]) return instantNotification(res[1], 'error');
                instantNotification('Payment method deleted successfully', 'success');
                init();
            });
        });
    }

    function renderAddCardThumbnail(container) {
        const div = $(`
            <div class="card-thumbnail add-card use-skeleton">
                <span class="add-icon">
                    <i class="fas fa-plus"></i>
                </span>
                <h3>${t('Add Payment Method')}</h3>
                <p>${t('Click here to add a new payment method')}</p>
            </div>
        `);

        div.on('click', () => paymentMethodDialog());
        container.append(div);
    }

    function renderInvoiceDetails() {
        const container = root.find('.details-grid');
        const { businessDetails, contactDetails, ccEmails } = settingsData;

        const address = [
            businessDetails?.address?.line1,
            businessDetails?.address?.line2,
            businessDetails?.address?.city,
            businessDetails?.address?.state,
            businessDetails?.address?.postalCode,
            businessDetails?.address?.country,
        ]
            .filter((x) => x && x.trim() !== '')
            .join(', ');

        container.html(`
            <div class="details-col">
                <h3 class="use-skeleton">${t('Invoice Address')}</h3>
                <p class="use-skeleton">${address}</p>
            </div>
            <div class="separator"></div>
            <div class="details-col">
                <h3 class="use-skeleton">${t('Invoice Entity')}</h3>
                <p class="use-skeleton">${businessDetails?.legalName ?? ''}</p>
                <p class="use-skeleton">
                    ${businessDetails?.companyNumber.label ?? 'Company Number'}:
                    ${businessDetails?.companyNumber.value ?? 'N/A'}
                </p>
            </div>
            <div class="separator"></div>
            <div class="details-col">
                <h3 class="use-skeleton">${t('Nominated SaaS Billing Contacts')}</h3>
                ${contactDetails?.length > 0 ? contactDetails.map(renderContact).join('') : `<div class="badge badge-red use-skeleton" style="padding: 3px;"><i class="fa fa-exclamation-triangle" aria-hidden="true"></i> &nbsp; ${t('No Billing Contacts Listed')}</div>`}
            </div>
            <div class="separator"></div>
            <div class="details-col">
                <h3 class="use-skeleton">${t('Invoice CC Emails')}</h3>
                ${ccEmails?.length > 0 ? ccEmails.map(email => `<p class="use-skeleton"><a href="mailto:${email}">${email}</a></p>`).join('') : `<div class="badge badge-grey use-skeleton" style="padding: 3px;">${t('None')}</div>`}
            </div>

            <button class="btn btn-link edit-button"><i class="fa fa-edit"></i></button>
        `);

        container.find('.edit-button').on('click', () => {

            const billingProfileBody = document.createElement("div");

            $(billingProfileBody).addClass('ph-dialog-v2');
            $(billingProfileBody).html(`

                  <form class="form-horizontal editBillingProfile isLoading">
                    <!-- Invoice Address -->

                    <div class="form-group">
                      <label class="control-label col-sm-5" for="invoice-address">${t('Invoice Address')}:</label>
                      <div class="col-sm-6 use-skeleton">
                        <input type="text" class="form-control" id="invoice-address" value="Loading..." disabled>
                      </div>
                      <div class="col-sm-1">
                        <a onclick="Swal.close();" href="/club-kyc" class="btn btn-link edit-button" style="width: 50px; margin-left: -20px;"><i class="fa fa-edit"></i></a>
                      </div>
                    </div>

                    <!-- Invoice Entity -->
                    <div class="form-group">
                      <label class="control-label col-sm-5" for="invoice-entity">${t('Invoice Entity')}:</label>
                      <div class="col-sm-7 no-left-margin use-skeleton">
                        <select class="form-control" id="invoice-entity">
                          <option>Loading...</option>
                        </select>
                      </div>
                    </div>

                    <!-- Nominated SaaS Billing Contacts -->
                    <div class="form-group billing-contacts-group">
                      <label class="control-label col-sm-5" for="billing-contacts">${t('Nominated SaaS Billing Contacts')}:</label>
                      <div class="col-sm-7 use-skeleton">
                        ${selectpicker({ label: '', name: 'billing-contacts', options: [], multiple: true, selectClx: 'mui' })}
                      </div>
                    </div>

                    <!-- Invoice CC Emails -->
                    <div class="form-group">
                      <label class="control-label col-sm-5" for="cc-emails">
                        ${t('Invoice CC Emails')}:
                    <br><small>${t('Use a comma')} <code>,</code> ${t('to separate emails')}</small>
                      </label>
                      <div class="col-sm-7 use-skeleton">
                        <input type="text" class="form-control" id="cc-emails" placeholder="Enter emails, separated by commas">
                      </div>
                    </div>

                    <hr>

                    <div class="form-group">
                      <div class="col-sm-12">
                        <p>
                          <small>
                            ${t(`Nominated SaaS Billing Contacts will automatically receive essential notifications and reminders related to the account's usage and billing details for the Performance Hub. They will also have the authority to modify payment settings and access the account's financial history.`)}
                          </small>
                        </p>
                        <p>
                          <small>
                            ${t(`To add & modify Billing Contacts deails (such as Phone & Email etc), please do so via the`)} <a onclick="Swal.close();" href="/club-kyc">'${t('Owners and Staff')}'</a> ${t('part of your Business Profile in the Performance Hub.')}
                          </small>
                        </p>
                        <p>
                          <small>
                            ${t(`Your Invoice Address is also automatically configured from the Business Address also set in the`)} <a onclick="Swal.close();" href="/club-kyc">'${t('Business Details')}'</a> ${t('part of your Business Profile.')}
                          </small>
                        </p>
                      </div>
                    </div>

                  </form>
            `);

            swalWithBootstrapButtons.fire({
                title: t('Billing Profile'),
                html: billingProfileBody,
                width: 650,
                showCancelButton: true,
                showConfirmButton: true,
                showCloseButton: true,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                onOpen: async () => {

                    const swalElement = Swal.getPopup();
                    $(swalElement).draggable({
                        handle: '.swal2-header',  // Makes the title bar the drag handle
                    });
                    // Only change the cursor to move for the title bar
                    $(swalElement).find('.swal2-header').css('cursor', 'move');

                    async function fetchData() {
                        try {
                            const [kyc, billingProfile] = await Promise.all([
                                $.ajax({ url: `/api/clubs/${selectedID}/kyc`, type: 'GET' }),
                                $.ajax({ url: `/api/clubs/${selectedID}/saas-billing/payment-settings`, type: 'GET' })
                            ]);

                            return { kyc, billingProfile };
                        } catch (error) {
                            console.error('Error fetching data:', error);
                            throw error;
                        }
                    }

                    fetchData().then(data => {
                        if (data) {
                            populateDialog(data);
                        } else {
                            swal.showValidationError('No data available. Please complete your KYC information.');
                        }
                    }).catch(error => {
                        swal.showValidationError('Failed to fetch data. Please try again.');
                        console.error('Error occurred:', error);
                    });

                },
                preConfirm: async () => {
                    const attrs = {
                        invoiceEntity: $('#invoice-entity').val(),
                        billingContacts: $(billingProfileBody).find('select[name="billing-contacts"]').val(),
                        ccEmails: JSON.parse($('#cc-emails').val() || '[]'),
                    }

                    const response = await updateBillingProfileAPI(selectedID, attrs);
                    if (!response[0]) return Swal.showValidationMessage(response[1]);
                },
            }).then((result) => {
                if (!result.value) return;
                init();
                instantNotification('Billing profile updated successfully', 'success');
            });

            async function populateDialog(data) {
                $('#invoice-address').val(getDefaultBillingAddress(data.billingProfile));

                $('#invoice-entity').empty();
                const { legalName, tradingName } = data.kyc.businessDetails;
                const { invoiceEntity } = data.billingProfile;
                ['legalName', 'tradingName'].forEach((name) => {
                    const label = name === 'legalName' ? 'Legal Business Name' : 'Trading Name';
                    const subtext = name === 'legalName' ? (legalName || label) : (tradingName || label);
                    const selected = invoiceEntity === name ? 'selected' : '';
                    $('#invoice-entity').append(`<option value="${name}" data-subtext="${t(subtext)}" ${selected}>${t(label)}</option>`);
                });

                $(billingProfileBody).find('select[name="billing-contacts"]').empty();

                data.kyc.contactDetails.forEach(contact => {
                    const useAsBillingContact = contact.useAsSaasBillingContact ? 'selected' : '';
                    $(billingProfileBody).find('select[name="billing-contacts"]').append(`<option value="${contact.id}" ${useAsBillingContact}>${contact.name}</option>`);
                });

                $(billingProfileBody).find('#invoice-entity').selectpicker();
                $(billingProfileBody).find('select.mui').select2({ theme: "material" });


                $('#cc-emails').val(JSON.stringify(data.billingProfile.ccEmails || []));
                $("#cc-emails").multiple_emails({
                   position: 'top', // Display the added emails above the input
                   theme: 'bootstrap', // Bootstrap is the default theme
                   checkDupEmail: true, // Should check for duplicate emails added
                });

                // Remove use-skeleton classes after populating
                $('.editBillingProfile').removeClass('isLoading');
            }

            function getDefaultBillingAddress(billingProfile) {
                // Check if we have a valid businessDetails and address object
                const address = billingProfile?.businessDetails?.address || billingProfile?.company?.address || {};

                // Prepare an array with the address components
                const formattedAddress = [
                    address.line1,
                    address.line2,
                    address.city,
                    address.state,
                    address.postalCode,
                    address.country
                ].filter(part => part).join(', '); // Filter out any undefined or empty parts and join them

                // Return the formatted address if it's not empty, otherwise return false
                return formattedAddress.length > 0 ? formattedAddress : t('Not configured - Please set in KYC Preferences');
            }

        });
    }

    function renderContact(contact) {
        return `
            <p class="use-skeleton">${contact.name}</p>
            <div style="padding-left: 1rem; margin-bottom: 1rem;">
                <p class="use-skeleton">
                    <a href="mailto: ${contact.email}">
                        ${contact.email}
                    </a>
                </p>
                <p class="use-skeleton">${contact.phone}</p>
            </div>
        `;
    }

    function paymentMethodDialog(card) {
        let cardLabel = card?.label ?? '';
        let setAsPrimary = card?.id === settingsData.primaryPaymentMethodID || !settingsData.paymentMethods.length;
        const disableSetAsPrimary = settingsData.paymentMethods.length < 2 && card && !card.id === settingsData.primaryPaymentMethodID;

        const div = document.createElement('div');
        $(div).addClass('payment-method-dialog ph-dialog-v2');

        $(div).html(`
            <div class="body d-flex flex-column gap-1">
                ${textField({
                    placeholder: t('Card Label (So you can identify the card)'),
                    name: 'label',
                    value: cardLabel,
                })}
                <div class="card-number stripe-card-input"></div>
                <div class="d-flex gap-1">
                    <div class="grow-1 card-expiry stripe-card-input"></div>
                    <div class="grow-1 card-cvc stripe-card-input"></div>
                </div>
                <div class="form-check styled-checkbox" style="margin-top:5px;">
                    <input id="set-as-primary" type="checkbox" class="filled-in chk-col-blue">
                    <label for="set-as-primary">${t('Set as primary payment method')}</label>
                </div>
                <em class="card-errors text-danger" role="alert"></em>
            </div>
        `);

        $(div)
            .find('input[name=label]')
            .on('input', (e) => {
                cardLabel = e.target.value;
            });

        $(div).find('#set-as-primary').prop('checked', setAsPrimary).prop('disabled', disableSetAsPrimary);
        $(div).find('#set-as-primary').on('change', (e) => {
            setAsPrimary = e.target.checked;
        });

        let cardNumber;
        let stripe = Stripe(stripePublishableKey, { stripeAccount: platformConnectedAccountId });

        function handleOnChange(event) {
            const displayError = $(div).find('.card-errors');
            if (event.error) return displayError.text(event.error.message);
            displayError.text('');
        }

        async function handleCreateCard() {
            let newCard = null;

            try {
                const { paymentMethod } = await stripe.createPaymentMethod({
                    type: 'card',
                    card: cardNumber,
                });

                newCard = {
                    id: paymentMethod.id,
                    label: cardLabel,
                    setAsPrimary,
                    card: {
                        brand: paymentMethod.card.brand,
                        last4: paymentMethod.card.last4,
                        expMonth: paymentMethod.card.exp_month,
                        expYear: paymentMethod.card.exp_year,
                    },
                };
            } catch (error) {
                console.error(error);
                return Swal.showValidationMessage('Something went wrong. Please try again.');
            }

            const newPmRes = await addClubSaasBillingPaymentMethodAPI(selectedID, newCard);
            if (!newPmRes[0]) return Swal.showValidationMessage(newPmRes[1]);
            init();
            return true;
        }

        async function handleEditCard() {
            const updateRes = await updateClubSaasBillingPaymentMethodAPI(selectedID, card.id, { label: cardLabel, setAsPrimary });
            if (!updateRes[0]) return Swal.showValidationMessage(updateRes[1]);
            settingsData.paymentMethods = settingsData.paymentMethods.map((pm) => {
                if (pm.id === card.id) return { ...pm, label: cardLabel };
                return pm;
            });
            if (setAsPrimary) settingsData.primaryPaymentMethodID = card.id;
            renderCardsGrid();
            return true;
        }

        async function renderStripeElements() {
            const elements = stripe.elements();

            const style = {
                base: {
                    color: '#32325d',
                    fontFamily: '"Source Sans Pro", "Helvetica Neue", sans-serif',
                    fontSmoothing: 'antialiased',
                    fontSize: '16px',
                    '::placeholder': { color: '#aab7c4' },
                },
                invalid: { color: '#fa755a', iconColor: '#fa755a' },
            };

            cardNumber = elements.create('cardNumber', {
                style,
                showIcon: true,
                placeholder: 'Card Number',
            });
            const cardExpiry = elements.create('cardExpiry', { style });
            const cardCvc = elements.create('cardCvc', { style });

            cardNumber.mount($(div).find('.card-number').get(0));
            cardExpiry.mount($(div).find('.card-expiry').get(0));
            cardCvc.mount($(div).find('.card-cvc').get(0));

            cardNumber.on('change', handleOnChange);
            cardExpiry.on('change', handleOnChange);
            cardCvc.on('change', handleOnChange);
        }

        function renderCardDetails() {
            const brand = card.card.brand.toLowerCase();
            $(div).find('.card-number').html(`
                <img class="card-logo" src="/assets/images/payment_methods/${brand}.png" alt="${brand}" height="19px" width="24px" style="margin-right: 1rem;">
                <span style="letter-spacing: 5px;">•••• •••• ••••</span> ${card.card.last4}
            `);
            $(div)
                .find('.card-expiry')
                .text(`${t(card.card.expMonth.toString())}/${t(card.card.expYear.toString())}`);
            $(div).find('.card-cvc').text('•••');
            $(div).find('.card-number, .card-expiry, .card-cvc').addClass('disabled');
        }

        swalWithBootstrapButtons.fire({
            title: card ? t('Edit Payment Method') : t('Add Payment Method'),
            html: div,
            showCancelButton: true,
            showConfirmButton: true,
            showCloseButton: true,
            confirmButtonText: t('Save'),
            cancelButtonText: t('Cancel'),
            onOpen: async () => {

                const swalElement = Swal.getPopup();
                $(swalElement).draggable({
                    handle: '.swal2-header',  // Makes the title bar the drag handle
                });
                // Only change the cursor to move for the title bar
                $(swalElement).find('.swal2-header').css('cursor', 'move');

                if (!card) return renderStripeElements();
                return renderCardDetails();
            },
            preConfirm: async () => {
                if (!card) return await handleCreateCard();
                return await handleEditCard();
            },
        }).then((result) => {
            if (!result.value) return;
            instantNotification('Payment method saved', 'success');
        });
    }

    function setCardAsPrimaryConfirm(id) {
        confirmDialog(
            t('Set as Primary Payment Method'),
            t('Are you sure you want to set this as your primary payment method?'),
            () =>
                setPMAsPrimaryAPI(selectedID, id).then((res) => {
                    if (!res[0]) return instantNotification(res[1], 'error');
                    instantNotification('Primary payment method updated successfully', 'success');
                    settingsData.primaryPaymentMethodID = id;
                    renderCardsGrid();
                })
        );
    }

    function requiresActionAlert() {
        const alertEl = $(`
            <div class="alert alert-yellow" style="margin-top: 10px; width: 100%;">
                <div class="d-flex align-center gap-1">
                    <div class="failed-payment-icon-banner" style="display: inline;">
                        <i class="fa fa-exclamation-triangle" style="color: #ff9600 !important; font-size: 22px;" aria-hidden="true"></i>
                    </div>
                    <b>${t('Action Required')}</b>
                </div>    
                <p style="font-weight: 300;" class="failed-payment-alert-body">
                    ${t(
                        'To complete your payment, we need you to verify your card through 3D Secure validation. Please complete this verification to avoid any disruption to your service.'
                    )}
                </p>
                <hr style="border-color: #856404;">
                <div class="d-flex justify-between gap-1 align-start">
                    <div class="outstanding-invoices d-flex flex-column gap-1"></div>
                    <button class="btn btn-warning authorize-payment">
                        ${t('Complete Verification & Make Payment')}
                        <div class="dots-loader"><div class="dots"></div></div>
                    </button>
                </div>
            </div>
        `);

        recordsThatNeedAuth.forEach((invoice) => {
            const { invoiceID, report } = invoice;
            alertEl.find('.outstanding-invoices').append(`
                <div class="d-flex gap-1 outstanding-invoice" data-id="${invoice.id}">
                    <div class="icon-wrapper">
                        <i class="fa fa-arrow-right" style="color: #ff9600 !important; font-size: 22px;" aria-hidden="true"></i>
                    </div>
                    <div>
                        <div>${t('Outstanding Invoice')}: ${invoiceID}</div>
                        <div>${t('Charge Amount')}: $${report.grandTotal} USD</div>
                    </div>
                </div>
            `);
        });

        alertEl.find('.authorize-payment').on('click', () => handleAuthorizePayments(alertEl));
        root.find('.cards-alerts').removeClass('hidden').html(alertEl);
    }


    function failedPaymentAlert() {
        const alertEl = $(`
            <div class="alert alert-red" style="margin-top: 10px; width: 100%;">
                <div class="d-flex align-center gap-1">
                    <div class="failed-payment-icon-banner" style="display: inline;">
                        <i class="fa fa-exclamation-triangle" style="color: #721c24; font-size: 22px;" aria-hidden="true"></i>
                    </div>
                    <b>${t('Payment Failed')}</b>
                </div>
                <p style="font-weight: 300;" class="failed-payment-alert-body">
                    ${t(
                        'Unfortunately, the recent attempt to charge your account for the Performance Hub and services you have consumed was declined. To avoid service disruption, please update your payment information, ensure there are adequate funds on your card, and then retry the outstanding payment.'
                    )}
                </p>
                <hr style="border-color: #dc35454f;">
                <div class="d-flex justify-between gap-1 align-start">
                    <div class="outstanding-invoices d-flex flex-column gap-1"></div>
                    <button class="btn btn-danger retry-payment">
                        ${t('Retry Payment')}
                        <div class="dots-loader"><div class="dots"></div></div>
                    </button>
                </div>

            </div>

        `);

        failedPaymentRecords.forEach((invoice) => {
            const { invoiceID, report, id: reportId } = invoice;
            alertEl.find('.outstanding-invoices').append(`
                <div class="d-flex gap-1 outstanding-invoice" data-id="${reportId}">
                    <div class="icon-wrapper">
                        <i class="fa-solid fa-arrow-right" style="color: #721c24; font-size: 22px;" aria-hidden="true"></i>
                    </div>
                    <div>
                        <div>${t('Outstanding Invoice')}: ${invoiceID}</div>
                        <div>${t('Charge Amount')}: $${report.grandTotal} USD</div>
                        <div class="message"></div>
                    </div>
                </div>
            `);
        });

        alertEl.find('.retry-payment').on('click', async () => {
            if (recordsThatNeedAuth.length) {
                handleAuthorizePayments(alertEl, '#721c24');
            } else if (failedPaymentRecords.length) {
                handleRetryAllFailed(alertEl);
            }
        });

        root.find('.cards-alerts').removeClass('hidden').html(alertEl);
    }

    async function handleRetryAllFailed(alertEl) {
        alertEl.find('.retry-payment').prop('disabled', true);
        alertEl.find('.retry-payment .dots-loader').addClass('on');
        
        await retryClubFailedPaymentsAPI(selectedID);
        await Promise.all(failedPaymentRecords.map((record) => billingStatusChecker(record, moment().unix(), alertEl)));

        if (recordsThatNeedAuth.length) {
            alertEl.find('.retry-payment').prop('disabled', false);
            alertEl.find('.retry-payment').html(t('Authorize Payment'));
        }

        // recheck for any new records that need auth after delay so check status is seen
        await new Promise((resolve) => setTimeout(resolve, 3000));
        reCheckOutstandingPayments();
    }

    async function handleAuthorizePayments(alertEl, color = '#ff9600') {
        alertEl.find('.authorize-payment,.retry-payment').prop('disabled', true);
        alertEl.find('.authorize-payment,.retry-payment').find('.dots-loader').addClass('on');

        for (let record of recordsThatNeedAuth) {
            const element = alertEl.find(`.outstanding-invoice[data-id="${record.id}"]`);
            const iconEl = element.find(`.icon-wrapper`);

            iconEl.html(`<i class="fa fa-circle-notch spin" style="font-size: 22px; color: ${color} !important"></i>`);

            await confirmStripePaymentIntent(stripePublishableKey, platformConnectedAccountId, record);
            const response = await getClubMonthlyRecordAPI(record.clubID, record.billingMonth);

            if (response[0] && response[1].status === 'success') {
                recordsThatNeedAuth = recordsThatNeedAuth.filter((r) => r.id !== record.id);
                iconEl.html('<i class="fa fa-check" style="color: #28a745 !important; font-size: 22px;"></i>');
                continue;
            }
            
            iconEl.html('<i class="fa fa-exclamation-triangle" style="color: #dc3545 !important; font-size: 22px;"></i>');
        }

        // recheck for any new records that need auth after delay so check status is seen
        await new Promise((resolve) => setTimeout(resolve, 3000));
        reCheckOutstandingPayments();
    }

    async function billingStatusChecker(record, triggerTime, html) {
        const element = html.find(`.outstanding-invoice[data-id="${record.id}"]`);
        const iconEl = element.find(`.icon-wrapper`);
        const messageEl = element.find(`.message`);

        iconEl.html('<i class="fa fa-circle-notch spin" style="font-size: 22px;"></i>');
        let response = [false, {}];
        let isAfterTrigger = false;
        let isProcessing = false;

        while (!response[0] || !isAfterTrigger || isProcessing) {
            await new Promise((resolve) => setTimeout(resolve, 5000));
            response = await getClubMonthlyRecordAPI(record.clubID, record.billingMonth);
            isAfterTrigger = response[0] && response[1].modifiedAt >= triggerTime;
            isProcessing = response[0] && response[1].status === 'processing';
        }
        
        if (response[1].status === 'success') {
            failedPaymentRecords = failedPaymentRecords.filter((r) => r.id !== record.id);
            iconEl.html('<i class="fa fa-check" style="color: #28a745; font-size: 22px;"></i>');
        } else if (response[1].status === 'failed') {
            iconEl.html('<i class="fa fa-exclamation-triangle" style="color: #dc3545; font-size: 22px;"></i>');
        } else if (response[1].status === '3d-secure-required') {
            recordsThatNeedAuth.push(response[1]);
            iconEl.html('<i class="fa fa-lock" style="color: #721c24; font-size: 22px;"></i>');
            messageEl.html(`<p>${t('Additional authentication is required to process this payment.')}</p>`);
        }
    }

    function noPaymentMethodWarning() {
        root.find('.cards-alerts').removeClass('hidden').html(`
            <div class="alert-yellow custom-alert custom-alert--warning" role="alert">
                <div class="custom-alert__content">
                    <div class="custom-alert__icon">
                        <i class="warning-icon-yellow custom-icon"></i>
                    </div>
                    <div class="custom-alert__body">
                        <p>${t('Please add a payment method to your account to ensure uninterrupted service of the Performance Hub.')}</p>
                    </div>
                </div>
            </div>
        `);
    }

    function noBackupPaymentMethodWarning() {
        root.find('.cards-alerts').removeClass('hidden').html(`
            <div class="alert-yellow custom-alert custom-alert--warning" role="alert">
                <div class="custom-alert__content">
                    <div class="custom-alert__icon">
                        <i class="warning-icon-yellow custom-icon"></i>
                    </div>
                    <div class="custom-alert__body">
                        <p>${t(
                            'Please add a backup payment method to your facility to ensure uninterrupted service.'
                        )}</p>
                    </div>
                </div>
            </div>
        `);
    }

    function renderPageError() {
        $('.page-error-alerts').html(`
            <div class="alert-red custom-alert custom-alert--danger" role="alert">
                <div class="custom-alert__content">
                    <div class="custom-alert__icon">
                        <i class="warning-icon custom-icon"></i>
                    </div>
                    <div class="custom-alert__body">
                        <p>${t(
                            'An error occurred while loading the page. Please try again later or contact support.'
                        )}</p>
                    </div>
                </div>
            </div>
        `);

        root.find('.payment-settings-wrapper').addClass('page-error');
    }

    init();
}

function isClubAccessRestrictedByBilling(callback) {
    getClubSaasBillingPaymentSettingsAPI(selectedID).then((res) => {
        saasBillingLockOut = res[0] && res[1].lockedOut && !signedInUserData.isGlobalAdmin;
        lockOutSidebarAndBanner();
        return callback?.();
    });
}
