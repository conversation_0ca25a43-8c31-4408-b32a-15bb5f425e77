$.websiteManagement = {};
$.websiteManagement.base = {
    isInitialised: false,
    init: function () {
        // the current open section
        let openSection = '#faqs';

        // current data holder
        const currentData = {
            '#faqs': [],
            '#mappings': [],
            '#press': [],
            '#club-lp': [],
        };
        let statusFilter = 'all';
        let searchTextFilter = '';

        // filter item display
        const filterItems = (
            text = searchTextFilter,
            status = statusFilter,
            dataArr = [],
        ) => {
            return dataArr.filter((data) => {
                const matchStatus =
                    status === 'all' || openSection === '#mappings'
                        ? true
                        : data.status === status;

                if (text) {
                    return (
                        JSON.stringify(data).toLowerCase().includes(text) &&
                        matchStatus
                    );
                }
                return matchStatus;
            });
        };

        const publishedFilter = $('#show-published-items');
        const searchInput = $('#filter-items');

        // add filter events
        publishedFilter.on('change', function () {
            statusFilter = $(this).is(':checked') ? 'all' : 'published';
            filterCallback();
        });
        searchInput.on('input', function () {
            searchTextFilter = $(this).val();
            filterCallback();
        });

        // filter callback
        const filterCallback = () => {
            let filteredData =
                openSection !== '#club-lp'
                    ? filterItems(
                          searchTextFilter,
                          statusFilter,
                          currentData[openSection],
                      )
                    : 'club-lp';

            $('.faqs-display, .press-display, .mappings-display').hide();

            if (!filteredData.length) {
                $('.no-search-results').fadeIn();
                return;
            }

            $('.no-search-results').hide();
            $('.faqs-display, .press-display, .mappings-display').show();

            // switch display call based on the curren open section
            switch (openSection) {
                case '#faqs':
                    fillTable(filteredData);
                    break;

                case '#press':
                    fillPressDisplay(filteredData);
                    break;

                case '#mappings':
                    fillMappingsDisplay(filteredData);
                    break;

                case '#club-lp':
                    fillClubLPDisplay(currentData[openSection]);
                    break;

                default:
                    break;
            }
        };

        // reset filter data
        const resetFilterData = () => {
            statusFilter = 'all';
            searchTextFilter = '';
            publishedFilter.prop('checked', true);
            searchInput.val('');
            $('.faqs-display, .press-display, .mappings-display').show();
            $('.no-search-results').hide();
        };

        // check if element is for delete
        const isDelete = (target) =>
            Array.from(target.classList).some(
                (val) => val.includes('-delete') || val === 'fa-trash-o',
            );

        const initWebsiteManagement = () => {
            // Enable Tabs
            $('.website-management-tabs a').click(function (e) {
                e.preventDefault();
                $(this).tab('show');

                resetFilterData();

                // set open section
                openSection = e.target.hash;

                // hide forms
                $('#faqs-form, #press-form, #mappings-form').hide();

                const useData = currentData[openSection];
                switch (openSection) {
                    case '#faqs':
                        fillTable(useData);
                        break;

                    case '#press':
                        fillPressDisplay(useData);
                        break;

                    case '#mappings':
                        fillMappingsDisplay(useData);
                        break;

                    default:
                        break;
                }

                $('.website-management .files-toolbar').show();
            });

            // Get faq data on initialization
            fetchData('', fillTable, true);

            // Show add button
            $('.add-item').on('click', function (e) {
                e.preventDefault();

                switch (openSection) {
                    case '#faqs':
                        initFaqForm();
                        break;

                    case '#press':
                        toggleForm(false);
                        break;

                    case '#mappings':
                        toggleForm(
                            false,
                            false,
                            {},
                            mappingForm,
                            mappingDisplay,
                            initMappingForm,
                        );
                        break;

                    case '#club-lp': {
                        const defaultLP = {
                            title: '',
                            slug: '',
                            availabilityDates: null,
                            isAlwaysAvailable: true,
                        };

                        initClubLPForm(defaultLP);
                        break;
                    }

                    default:
                        break;
                }
            });

            // Cancel add form
            $('.faq-add-cancel').on('click', (e) => {
                e.preventDefault();
                resetFaqDisplay();
                fetchData('', fillTable);
                scrollTop();
                $('.website-management .files-toolbar').show();
            });
        };

        const resetFaqDisplay = () => {
            // Hide form
            $('#faqs-form').hide();
            // Show add button
            $('.add-faq').show();
        };

        const getActive = (data) => {
            if (!Array.isArray(data)) return data;

            const result = [...data].filter(
                (item) => !item.status || item.status !== 'deleted',
            );
            currentData[openSection] = result;
            return result;
        };

        const fetchData = (
            id = '',
            callback,
            newLoad,
            url = '/api/admin/faqs/',
        ) => {
            if (newLoad) {
                // trigger call
                $.ajax({
                    url: url + id,
                    method: 'GET',
                    success: (data) => {
                        const activeData = getActive(data);
                        if (callback && typeof callback == 'function') {
                            callback(activeData);
                        }

                        $('.save-overlay').fadeOut('slow');
                    },
                    error: (xhr, textStatus, errorThrown) => {
                        $('.table-loading-overlay').hide();
                        console.error(xhr, textStatus, errorThrown);
                        window.location.replace('/401.html');
                    },
                });
                return;
            }

            if (callback && typeof callback == 'function') {
                callback();
            }
        };

        const itemLocations = [
            {
                label: 'APP: Train On Demand',
                value: 'app_tod',
            },
            {
                label: 'APP: Training Camp',
                value: 'app_tc',
            },
            {
                label: 'SITE: ubxtraining.com',
                value: 'website_main',
            },
            {
                label: 'SITE: ownaubx.com',
                value: 'website_franchise',
            },
        ];

        const editorConfig = {
            svgPath: '/assets/images/trumbowyg.svg',
            // The text editing zone will extend itself when writing a long text
            autogrow: true,
            // Links should open in new tabs by default...
            defaultLinkTarget: '_blank',
            btns: [
                ['historyUndo', 'historyRedo'],

                ['strong', 'link'],
            ],
        };

        const fillTable = async (data) => {
            const faqsBody = $('#faqs-items');

            if (!data || !data.length) {
                faqsBody.fadeIn();
                return;
            }

            const [userPrefRes, langRes] = await Promise.all([
                getUserPreferencesAPI(),
                getLanguagesAPI(),
            ]);
            const selectedLanguageCode =
                userPrefRes[1].preferredLanguage || 'en';

            // fill the table with data
            faqsBody.html('');
            for (const item of data) {
                const {
                    faqId,
                    categories,
                    question,
                    answer,
                    status,
                    location,
                    language,
                } = item;
                const faqItem = `
            <div class="faq-item faq-edit" data-faqid="${faqId}">
                <div class="faq-question">
                    ${question}
                </div>
                <div class="faq-answer">
                    ${answer}
                </div>
                ${
                    location
                        ? `
                    <div class="faq-location-val">
                        <strong>Location(s) Published: </strong>
                        ${location
                            .map(
                                (item) =>
                                    `<span>${itemLocations.find((option) => option.value === item).label}</span>`,
                            )
                            .join(', ')}
                    </div>
                `
                        : ''
                }
                <div class="faq-categories">
                    ${categories.map((item) => `<span>${item}</span>`).join('')}
                </div>
                <div class="faq-status">
                    <strong>Status:</strong> <span>${status || 'unpublished'}</span>
                </div>
                <div class="faq-language">
                    <strong>Language:</strong> <span>${language ? `${getLanguageNameFromCode(language, selectedLanguageCode)} (${getLanguageNameFromCode(language, language)})` : 'Global'}</span>
                </div>
                <div class="faq-actions">
                    <button class="faq-delete delete-faq" data-faqid="${faqId}"><i class="fa fa-trash-o" aria-hidden="true"></i></button>
                    
                </div>
            </div>
            `;
                faqsBody.append(faqItem);
            }
            // add delete function
            faqsBody.find('.delete-faq').click(function (e) {
                e.preventDefault();
                const faqId = $(this).data('faqid');
                toggleConfirmDelete(faqId);
            });
            faqsBody.fadeIn();

            // add edit function
            faqsBody.find('.faq-edit').click(function (e) {
                e.preventDefault();

                if (isDelete(e.target)) return;

                const faqId = $(this).data('faqid');
                resetFaqDisplay();
                fetchData(
                    faqId,
                    (data) => {
                        initFaqForm(data, true, 'Edit Faq Item');
                    },
                    true,
                );
            });
        };

        const initFaqForm = async (
            data = {},
            update = false,
            headline = 'Add New Faq Item',
        ) => {
            $('#faqs-items, .website-management .files-toolbar').hide();
            window.scrollTo({
                top: top - 150,
                left: 0,
                behavior: 'smooth',
            });

            // update headline
            $('.faq-form-headline').text(headline);

            // default field data
            let fieldData = {
                faqId: uuidv4(),
                location: [],
                categories: [],
                language: '',
                question: '',
                answer: '',
                status: 'unpublished', // default status
                ...data,
            };

            /**
             * submit the form
             */
            const faqsForm = $('#faqs-form');

            faqsForm.submit((e) => {
                e.preventDefault();

                // basic validation
                const { faqId, ...fields } = fieldData;

                const valid = basicValidation(fields);
                if (valid) submitForm(fieldData, update);
            });

            faqsForm.show();
            $('.fld-label').removeAttr('style');

            /**
             * faqs content editor
             */
            const faqQuestion = $('#faq-question');
            const faqAnswer = $('#faq-answer');

            faqQuestion.trumbowyg(editorConfig).on('tbwchange', () => {
                //  set the field data value for the question
                fieldData.question = faqQuestion.trumbowyg('html');
            });

            faqAnswer.trumbowyg(editorConfig).on('tbwchange', () => {
                //  set the field data value for the answer
                fieldData.answer = faqAnswer.trumbowyg('html');
            });

            // set the default value of the editor fields
            faqQuestion.trumbowyg('html', fieldData.question);
            faqAnswer.trumbowyg('html', fieldData.answer);

            /**
             * handle categories
             */
            const faqEl = '.faq-category-input';
            const faqCategories = $(faqEl);
            // clear all checked inputs
            $(faqEl).each(function () {
                $(this).prop('checked', false);
            });

            // set checked categories
            if (fieldData.categories.length > 0) {
                for (let val of fieldData.categories) {
                    $(`${faqEl}[value=${val}]`).prop('checked', true);
                }
            }
            // faqCategories.val(fieldData.categories);
            // category on change
            faqCategories.change(function () {
                const val = $(this).val();
                const catIndex = fieldData.categories.indexOf(val);
                if (catIndex === -1) fieldData.categories.push(val);
                else fieldData.categories.splice(catIndex, 1);
            });

            /**
             * handle faq location
             */

            // add faq location options
            const faqLocation = $('#faq-location');
            faqLocation.html(
                itemLocations
                    .map(({ label, value }) => {
                        const option = `<option value="${value}">${label}</option>`;
                        return option;
                    })
                    .join(''),
            );

            // initialize picker styling
            faqLocation.selectpicker('refresh');

            // add default selected option
            faqLocation.selectpicker('val', fieldData.location);

            // add location in fieldData
            faqLocation.change(function () {
                const val = $(this).val();
                fieldData.location = val;
            });

            const faqLanguage = $('#faq-language');
            const [userPrefRes, langRes] = await Promise.all([
                getUserPreferencesAPI(),
                getLanguagesAPI(),
            ]);
            const selectedLanguageCode =
                userPrefRes[1].preferredLanguage || 'en';
            const langOptions = [
                { label: `Global`, value: '' },
                {
                    label: `${getLanguageNameFromCode('en', 'en')} (English)`,
                    value: 'en',
                },
                {
                    label: `${getLanguageNameFromCode('ja', selectedLanguageCode)} (${getLanguageNameFromCode('ja', 'ja')})`,
                    value: 'ja',
                },
            ];

            faqLanguage.html(
                langOptions
                    .map(({ label, value }) => {
                        const option = `<option value="${value}">${label}</option>`;
                        return option;
                    })
                    .join(''),
            );

            // initialize picker styling
            faqLanguage.selectpicker('refresh');

            // add default selected option
            faqLanguage.selectpicker('val', fieldData.language);

            // add location in fieldData
            faqLanguage.change(function () {
                const val = $(this).val();
                fieldData.language = val;
            });

            /**
             * handle faq status
             */
            const faqStatus = $('input[name="faq-status"]');
            // set selected status

            faqStatus.prop('checked', fieldData.status === 'published');
            faqStatus.each(function () {
                const val = $(this).val();
                if (val === fieldData.status) {
                    $(this).prop('checked', true);
                    return;
                }
            });
            faqStatus.change(function () {
                fieldData.status = $(this).is(':checked')
                    ? 'published'
                    : 'unpublished';
            });
        };

        const basicValidation = (fields, parent) => {
            $('.fld-label').removeAttr('style');

            let errorCount = 0;
            let elFocus = null;

            for (const field in fields) {
                const idSelector = $(`#fld-label-${field}`);
                const dataIdSelector = $(
                    `${parent} label[data-id="fld-label-${field}"]`,
                );
                const el =
                    parent && dataIdSelector.length > 0
                        ? dataIdSelector
                        : idSelector;
                const notRequired = el.data('skip');

                if (fields[field].length === 0 && !notRequired) {
                    el.css({ color: 'red' });

                    if (errorCount === 0) elFocus = el;

                    errorCount++;
                }
            }

            if (elFocus && elFocus.length > 0) {
                // scroll to the first element with error
                const { top } = elFocus.offset();

                window.scrollTo({
                    top: top - 150,
                    left: 0,
                    behavior: 'smooth',
                });

                return false;
            }

            return true;
        };

        const submitForm = (
            data,
            update = false,
            callback,
            formEl = '#faqs-form',
            useUrl,
        ) => {
            // show loader
            $('.save-overlay').show();

            const { faqId: id } = data;
            const url =
                useUrl ||
                (update
                    ? `/api/admin/faqs/update/${id}`
                    : '/api/admin/faqs/add');

            // start api call
            $.ajax({
                url,
                method: 'POST',
                data,
                success: (data) => {
                    // update loader text
                    const overlayEl = $('.overlay-text');
                    const overlayText = `${update ? 'Item Updated' : 'Item Added'}.`;
                    overlayEl.text(overlayText);

                    // unbind form event
                    $(formEl).unbind();

                    // redirect to website-management
                    setTimeout(() => {
                        $('.save-overlay').fadeOut('slow', () =>
                            resetOverlay(),
                        );
                        if (callback) {
                            callback();
                            return;
                        }
                        resetFaqDisplay();
                        fetchData('', fillTable, true);
                    }, 1500);
                },
                error: (err) => {
                    console.log({ err });

                    // hide loader
                    $('.save-overlay').hide();
                },
            });
        };

        const toggleConfirmDelete = (id, callback = deleteItem) => {
            $('#confirm-delete').modal('show');

            const btn = $('#proceed-delete');
            btn.click(() => {
                $('#confirm-delete').modal('hide');
                callback(id);
                btn.unbind();
            });
        };

        const deleteItem = (
            id,
            urlVal = '/api/admin/faqs/update/',
            callback,
        ) => {
            $('.save-overlay').show();
            const url = `${urlVal + id}`;
            $.ajax({
                url,
                method: 'POST',
                data: { status: 'deleted' },
                success: (data) => {
                    scrollTop();

                    // update message
                    $('.overlay-text').text('Item Deleted');

                    // update faqs
                    setTimeout(() => {
                        if (!callback) {
                            resetFaqDisplay();
                            fetchData('', fillTable, true);
                        }

                        if (callback && typeof callback == 'function')
                            callback();

                        $('.save-overlay').fadeOut('slow', () =>
                            resetOverlay(),
                        );
                    }, 1500);
                },
                error: (err) => {
                    console.log({ err });

                    // hide loader
                    $('.save-overlay').hide();
                },
            });
        };

        // update item to published/unpublished directly
        const updateItem = (
            id,
            urlVal = '/api/admin/faqs/update/',
            callback,
            status = 'unpublished',
        ) => {
            $('.save-overlay').show();
            const url = `${urlVal + id}`;
            const overLayText =
                status === 'published' ? 'Item Published' : 'Item Published';
            $.ajax({
                url,
                method: 'POST',
                data: { status },
                success: (data) => {
                    scrollTop();

                    // update message
                    $('.overlay-text').text(overLayText);

                    // update faqs
                    setTimeout(() => {
                        if (!callback) {
                            resetFaqDisplay();
                            fetchData('', fillTable, true);
                        }

                        if (callback && typeof callback == 'function')
                            callback();

                        $('.save-overlay').fadeOut('slow', () =>
                            resetOverlay(),
                        );
                    }, 1500);
                },
                error: (err) => {
                    console.log({ err });

                    // hide loader
                    $('.save-overlay').hide();
                },
            });
        };

        const resetOverlay = () => {
            // reset overlay text
            $('.overlay-text').text('One moment please...');
        };

        /*
         * press functions
         */

        let loadedPressItems = false;

        // load press items
        const initPress = () => {
            $('.website-management-tabs a[href="#press"]').on(
                'show.bs.tab',
                () => {
                    // load press items
                    if (!loadedPressItems)
                        return fetchData(
                            '',
                            fillPressDisplay,
                            true,
                            '/api/admin/press/',
                        );
                },
            );
            $('#press-add').on('click', (e) => {
                e.preventDefault();
                toggleForm();
            });
            $('#press-cancel').on('click', (e) => {
                e.preventDefault();
                toggleForm(true);
            });
        };

        const fillPressDisplay = (data) => {
            loadedPressItems = true;
            const pressWrap = $('.press-items');
            pressWrap.html('');
            for (let item of data) {
                pressWrap.append(generatePressItem(item));
            }
            // add delete function
            pressWrap.find('.press-delete').click(function (e) {
                e.preventDefault();
                const pressId = $(this).data('pressid');
                // show delete confirmation
                toggleConfirmDelete(pressId, () =>
                    // proceed delete
                    deleteItem(pressId, '/api/admin/press/update/', () =>
                        fetchData(
                            '',
                            fillPressDisplay,
                            true,
                            '/api/admin/press/',
                        ),
                    ),
                );
            });

            // add edit function
            pressWrap.find('.press-edit').on('click', function (e) {
                e.preventDefault();

                if (isDelete(e.target)) return;

                const pressId = $(this).data('pressid');
                fetchData(
                    pressId,
                    (data) => toggleForm(false, true, data),
                    true,
                    '/api/admin/press/',
                );
            });
        };

        const pressForm = '#press-form';
        const pressDisplay = '#press-display';

        const toggleForm = (
            hide,
            edit = false,
            data,
            form = pressForm,
            display = pressDisplay,
            initForm = initPressForm,
        ) => {
            $(form).unbind();

            $('.website-management .files-toolbar').hide();

            if (hide) $('.website-management .files-toolbar').fadeIn();

            if (edit) {
                initForm(data, true, 'Update Item');
            } else {
                initForm();
            }

            scrollTop();

            if (hide) {
                $(display).fadeIn();
                $(form).hide();
                return;
            }

            // show form
            $(display).hide();
            $(form).fadeIn();
        };

        const generatePressItem = (item) => {
            let {
                date,
                quote,
                publisher,
                imageSrc,
                title,
                url,
                pressId,
                status,
                location,
            } = item;

            if (!imageSrc.includes('http')) {
                imageSrc = `https://ubxtraining.com${imageSrc}`;
            }

            return `
        <div class="press-item press-edit" data-pressid="${pressId}">
            <a href="${url}" target="_blank" class="press-details">
            <div class="press-thumb">
                <img src="${imageSrc}" alt="${publisher}">
            </div>
            <h6 class="press-title">${title}</h6>
            <p class="press-publisher">${publisher}</p>
            <p class="press-date">${date}</p>
            <p class="press-quote">${quote}</p>
            </a>
            <div class="status-location">
                ${
                    location
                        ? `
                    <div class="press-location-val">
                        <strong>Location(s) Published: </strong>
                        ${location
                            .map(
                                (item) =>
                                    `<span>${itemLocations.find((option) => option.value === item).label}</span>`,
                            )
                            .join(', ')}
                    </div>
                `
                        : ''
                }
                <div class="press-status">
                    <strong>Status:</strong> <span>${status || 'unpublished'}</span>
                </div>
            </div>

            <div class="press-item-actions">
                <button class="press-delete"
                    data-pressid="${pressId}"><i class="fa fa-trash-o"
                    aria-hidden="true"></i></button>
            </div>

        </div>
    `;
        };

        const initPressForm = (
            data = {},
            update = false,
            headline = 'New Landing Page',
        ) => {
            // update headline
            $('.press-form-headline').text(headline);

            // default field data
            let fieldData = {
                location: [],
                pressId: uuidv4(),
                date: '',
                quote: '',
                publisher: '',
                imageSrc: '',
                title: '',
                url: '',
                status: 'unpublished',
                ...data,
            };

            $(pressForm).submit((e) => {
                e.preventDefault();

                // basic validation
                const { pressId, ...fields } = fieldData;

                const valid = basicValidation(fields, pressForm);

                const url = update
                    ? `/api/admin/press/update/${pressId}`
                    : '/api/admin/press/add/';

                if (valid)
                    submitForm(
                        fieldData,
                        update,
                        pressFormCallback,
                        pressForm,
                        url,
                    );
            });
            // clear validation
            $('.fld-label').removeAttr('style');

            // enable dropzone
            const dropArea = $('.fld-drop');
            dropArea.html('');
            dropArea.append(
                `<div id="press-imageSrc" class="dropzone drop-area"></div><small>${t('Please only upload image files (jpg, png etc) - other formats will not be converted.')}</small>`,
            );

            dropArea.find('#press-imageSrc').dropzone({
                maxFiles: 1,
                url: `/api/asset-upload`,
                acceptedFiles: '.jpeg,.jpg,.png',
                timeout: 3600000,
                dictDefaultMessage: t('Drop files here to upload'),
                addedfiles: () => {
                    $('.press-action').attr('disabled', true);
                },
                queuecomplete: () => {
                    $('.press-action').attr('disabled', false);
                },
                success: function (_, response) {
                    if (
                        response !== undefined &&
                        response.location !== undefined &&
                        response.originalname !== undefined
                    ) {
                        fieldData.imageSrc = response.location;
                    }
                },
                error: (_, response) => {
                    console.error({ response });
                },
                sending: (_, xhr) => {
                    xhr.ontimeout = (e) => {
                        console.info('Server Timeout');
                    };
                },
            });

            /**
             * handle press location
             */

            // add press location options
            const pressLocation = $('#press-location');
            pressLocation.html(
                itemLocations
                    .map(({ label, value }) => {
                        const option = `<option value="${value}">${label}</option>`;
                        return option;
                    })
                    .join(''),
            );

            // initialize picker styling
            pressLocation.selectpicker('refresh');

            // add default selected option
            pressLocation.selectpicker('val', fieldData.location);

            // add location in fieldData
            pressLocation.change(function () {
                const val = $(this).val();
                fieldData.location = val;
            });

            // fill fields
            for (const field in fieldData) {
                const el = $(`#press-${field}`);
                if (el.length !== 0) {
                    let val = fieldData[field];

                    if (field === 'date' && val !== '') {
                        val = moment(val).format('YYYY-MM-DD');
                    }

                    if (field === 'status') {
                        el.prop('checked', val === 'published');
                    }

                    setTimeout(() => {
                        el.val(val);
                    }, 700);
                }
            }

            // add field events
            const fields = $(pressForm).find(
                'input[id^="press-"], textarea[id^="press-"]',
            );
            fields.each(function () {
                const el = $(this);
                const field = el.attr('id').replace('press-', '');

                // clear values
                el.val('');

                // style date field
                if (field === 'date') {
                    const initDateVal = fieldData.date
                        ? new Date(fieldData.date)
                        : new Date();
                    setTimeout(() => {
                        el.daterangepicker({
                            singleDatePicker: true,
                            showDropdowns: true,
                            startDate: moment(initDateVal),
                            ranges: {
                                Today: [moment(new Date()), moment()],
                                Tomorrow: [
                                    moment().add(1, 'days'),
                                    moment().add(1, 'days'),
                                ],
                                '+7 Days': [
                                    moment().add(7, 'days'),
                                    moment().add(7, 'days'),
                                ],
                            },
                            locale: {
                                format: 'YYYY-MM-DD',
                            },
                        });
                    }, 500);
                }

                // unbind and add event
                el.unbind();
                el.on('input change', function () {
                    let val = el.val();

                    if (field === 'date') {
                        val = moment(new Date(val)).format('D MMMM YYYY');
                    }

                    if (field === 'status') {
                        val = el.is(':checked') ? 'published' : 'unpublished';
                    }

                    fieldData[field] = val;
                });
            });
        };

        const pressFormCallback = () => {
            toggleForm(true);
            fetchData('', fillPressDisplay, true, '/api/admin/press/');
        };

        const scrollTop = () => {
            window.scrollTo({
                top: 0,
                left: 0,
                behavior: 'smooth',
            });
        };

        // email mapping
        const generateMapping = (data) => {
            let details = '';
            const { id } = data;

            if (!data.language || data.language === undefined) {
                data.language = 'global';
            }

            for (let item in data) {
                details += generateMappingDetail(item, data[item]);
            }

            const res = `
                <div class="mappings-item mapping-edit" data-id="${id}">

                    ${details}

                    <div class="mappings-actions">
                        <button class="mapping-delete" data-id="${id}"><i class="fa fa-trash-o"
                            aria-hidden="true"></i></button>
                    </div>

                </div>
            `;

            return res;
        };

        const generateMappingDetail = (item, value) => {
            const res = `
                <div class="mappings-detail">
                    <strong>${item}:</strong>
                    <p>${value}</p>
                </div>
            `;

            return res;
        };

        const fillMappingsDisplay = (data) => {
            loadedMappings = true;
            const container = $('#mappings-container');
            container.html('');
            for (let item of data) {
                container.append(generateMapping(item));
            }
            // add delete function
            container.find('.mapping-delete').click(function (e) {
                e.preventDefault();
                const id = $(this).data('id');
                // show delete confirmation
                toggleConfirmDelete(id, () =>
                    // proceed delete
                    deleteItem(id, '/api/admin/email-mapping/update/', () =>
                        fetchData(
                            '',
                            fillMappingsDisplay,
                            true,
                            '/api/admin/email-mapping/',
                        ),
                    ),
                );
            });

            // add edit function
            container.find('.mapping-edit').on('click', function (e) {
                e.preventDefault();

                if (isDelete(e.target)) return;

                const id = $(this).data('id');
                fetchData(
                    id,
                    (data) =>
                        toggleForm(
                            false,
                            true,
                            { ...data, ...{ oldId: id } },
                            mappingForm,
                            mappingDisplay,
                            initMappingForm,
                        ),
                    true,
                    '/api/admin/email-mapping/',
                );
            });
        };

        let loadedMappings = false;
        const mappingForm = '#mappings-form';
        const mappingDisplay = '#mappings-display';

        const initMappings = () => {
            $('.website-management-tabs a[href="#mappings"]').on(
                'show.bs.tab',
                () => {
                    if (!loadedMappings)
                        return fetchData(
                            '',
                            fillMappingsDisplay,
                            true,
                            '/api/admin/email-mapping/',
                        );
                },
            );
            $('#mapping-add').on('click', (e) => {
                e.preventDefault();
                toggleForm(
                    false,
                    false,
                    {},
                    mappingForm,
                    mappingDisplay,
                    initMappingForm,
                );
            });
            $('#mapping-cancel').on('click', (e) => {
                e.preventDefault();
                toggleForm(
                    true,
                    false,
                    {},
                    mappingForm,
                    mappingDisplay,
                    initMappingForm,
                );
            });
        };

        const initMappingForm = async (
            data = {},
            update = false,
            headline = 'Add New Item',
        ) => {
            // update headline
            $('.mappings-form-headline').text(headline);

            // default field data
            let fieldData = {
                id: '',
                label: '',
                email: '',
                notice: '',
                language: '',
                ...data,
            };

            $(mappingForm).submit((e) => {
                e.preventDefault();

                // basic validation
                const { id, oldId, ...fields } = fieldData;

                delete fieldData.oldId;

                const valid = basicValidation(fields);

                const url = update
                    ? `/api/admin/email-mapping/update/${oldId}`
                    : '/api/admin/email-mapping/add/';

                if (valid)
                    submitForm(
                        fieldData,
                        update,
                        mappingsFormCallback,
                        mappingForm,
                        url,
                    );
            });

            // clear validation
            $('.fld-label').removeAttr('style');

            const mappingsNotice = $('#mappings-notice');
            mappingsNotice.trumbowyg(editorConfig).on('tbwchange', () => {
                //  set the field data value for notice
                fieldData.notice = mappingsNotice.trumbowyg('html');
            });
            mappingsNotice.trumbowyg('html', fieldData.notice);

            // add field events
            const fields = $(mappingForm).find('input.fld-input');

            fields.each(function () {
                const el = $(this);
                const field = el.attr('id').replace('mappings-', '');

                // clear values
                el.val('');

                // unbind and add event
                el.unbind();
                el.on('input change', function () {
                    let val = el.val();

                    fieldData[field] = val;
                });
            });

            // fill fields
            for (const field in fieldData) {
                const el = $(`#mappings-${field}`);
                if (el.length !== 0) {
                    let val = fieldData[field];

                    el.val(val);
                }
            }

            const fldLanguage = $('#mappings-language');
            const [userPrefRes, langRes] = await Promise.all([
                getUserPreferencesAPI(),
            ]);
            const selectedLanguageCode =
                userPrefRes[1].preferredLanguage || 'en';
            const langOptions = [
                { label: `Global`, value: '' },
                {
                    label: `${getLanguageNameFromCode('en', 'en')} (English)`,
                    value: 'en',
                },
                {
                    label: `${getLanguageNameFromCode('ja', selectedLanguageCode)} (${getLanguageNameFromCode('ja', 'ja')})`,
                    value: 'ja',
                },
            ];

            fldLanguage.html(
                langOptions
                    .map(({ label, value }) => {
                        const option = `<option value="${value}">${label}</option>`;
                        return option;
                    })
                    .join(''),
            );

            // initialize picker styling
            fldLanguage.selectpicker('refresh');

            // add default selected option
            fldLanguage.selectpicker('val', fieldData.language);

            // add location in fieldData
            fldLanguage.change(function () {
                const val = $(this).val();
                fieldData.language = val;
            });
        };

        const mappingsFormCallback = () => {
            toggleForm(
                true,
                false,
                {},
                mappingForm,
                mappingDisplay,
                initMappingForm,
            );
            fetchData(
                '',
                fillMappingsDisplay,
                true,
                '/api/admin/email-mapping/',
            );
        };

        let lpSelected = null;

        let isCloning = false;

        let loadedClubData = null;

        let countryList = [];

        const getCountryList = async () => {
            countryList = await getCountriesOfClubsAvailableToUser();
            countryList = countryList.sort((a, b) =>
                a.name.localeCompare(b.name),
            );
        };

        const submitAddLP = async (data) => {
            $('#club-lp').addClass('isLoading');

            // trigger submission here
            const [isOK] = await addLandingPage(data);

            if (isOK) {
                // refetch data
                const [, fetchData] = await listLandingPages();

                fillClubLPDisplay(fetchData);
                return true;
            }

            $('#club-lp').removeClass('isLoading');

            return false;
        };

        const deleteLP = async (data) => {
            $('#club-lp').addClass('isLoading');

            // trigger submission here
            const [isOK] = await deleteLandingPage(data);

            if (isOK) {
                // refetch data
                const [, fetchData] = await listLandingPages();

                fillClubLPDisplay(fetchData);
                return true;
            }

            $('#club-lp').removeClass('isLoading');

            return false;
        };

        const updateLP = async (data) => {
            $('#club-lp').addClass('isLoading');

            // trigger submission here
            const [isOK] = await updateLandingPage(data);

            if (isOK) {
                // refetch data
                const [, fetchData] = await listLandingPages();

                fillClubLPDisplay(fetchData);
                return true;
            }

            $('#club-lp').removeClass('isLoading');

            return false;
        };

        const initClubLPForm = (lpData) => {
            const selectedVisibility = lpData?.clubs?.length
                ? 'club'
                : lpData?.regions?.length
                  ? 'region'
                  : 'global';

            const newLpData = lpData ? { ...lpData } : {};

            // initial dropdown  visibility
            const lpDropdownVisible =
                selectedVisibility === 'club' ||
                selectedVisibility === 'region';
            const lpVisibilityLabel =
                selectedVisibility === 'club' ? t('Clubs') : t('Regions');

            // check video url when it's available
            async function checkVideoUrl(uuid) {
                if (!uuid) return false;

                try {
                    const data = await fetch(
                        `/api/asset-upload/${uuid}/?poll=1`,
                    );
                    const res = await data.json();

                    return res;
                } catch (error) {
                    console.error('Error:', error);
                    return false;
                }
            }

            async function updateCustomVideo(uuid) {
                const pollRes = await checkVideoUrl(uuid);

                if (pollRes && pollRes?.completed?.variations?.video?.length) {
                    const url = pollRes.completed.variations.video[0];

                    newLpData.customVideo.url = url;
                    $('#lp-video-filename').text(url);
                }
            }

            // update newLpData with the new data
            if (lpData?.customVideo?.uuid) {
                updateCustomVideo(lpData?.customVideo?.uuid);
            }

            const modalHtml = `
              <div id="club-lp-form" class="device-details-dialog--v2 ph-dialog-v2 device-details-dialog">
                <fieldset>
                  <div class="row vertical-align-row">
                      <div class="col-xs-5">
                          <strong>${t('Always Available')}:</strong>
                      </div>
                      <div class="col-xs-7">
                        <div class="switch">
                            <label>${t('NO')} <input type="checkbox" id="lp-always-active" ${lpData.isAlwaysAvailable ? 'checked' : ''}><span class="lever lever switch-col-blue"></span> ${t('YES')}</label>
                        </div>
                      </div>
                  </div>
                  <div class="row vertical-align-row ${lpData.isAlwaysAvailable ? 'hidden' : ''}" id="lp-dates-active-container">
                      <div class="col-xs-5 disabled">
                          <strong>${t('Select Availability Dates')}:</strong>
                      </div>
                      <div class="col-xs-7">
                        <input
                            type="text"
                            id="lp-dates-active"
                            name="lp-dates-active"
                        />
                      </div>
                  </div>
                  <div class="row vertical-align-row">
                      <div class="col-xs-5">
                          <strong>${t('Page Name')}:</strong>
                      </div>
                      <div class="col-xs-7">
                          <input id="club-lp-title" type="text" value="${lpData ? lpData.title : ''}" placeholder="${t('Enter page name')}">
                      </div>
                  </div>
                  <div class="row vertical-align-row">
                      <div class="col-xs-5">
                          <strong>${t('Page slug')}:</strong>
                      </div>
                      <div class="col-xs-7">
                          <input id="club-lp-slug" type="text" value="${lpData ? lpData.slug : ''}" placeholder="${t('Enter page slug. ie: eofy-sale-2024')}">
                      </div>
                  </div>
                  <div class="row vertical-align-row">
                      <div class="col-xs-5">
                          <strong>${t('Banner Text')}:</strong>
                      </div>
                      <div class="col-xs-7">
                          <input id="club-lp-banner-text" type="text" value="${lpData.bannerText || ''}" placeholder="${t('Enter banner text')}">
                      </div>
                  </div>
                  <div class="row vertical-align-row">
                      <div class="col-xs-5">
                          <strong>${t('Visibility')}:</strong>
                      </div>
                      <div class="col-xs-7">
                        <div class="form-group form-group-no-margin">
                          <select title="Please Select..." id="lp-visibility" class="show-tick selectpicker form-control" required>
                              <option ${
                                  selectedVisibility === 'global'
                                      ? 'selected'
                                      : ''
                              } value="global">Global (Applies to all Clubs)</option>
                              <option ${
                                  selectedVisibility === 'club'
                                      ? 'selected'
                                      : ''
                              } value="club">Individual Club(s)</option>
                              <option ${
                                  selectedVisibility === 'region'
                                      ? 'selected'
                                      : ''
                              } value="region">Region(s)</option>
                          </select>
                        </div>
                      </div>
                  </div>
                  <div class="row vertical-align-row ${!lpDropdownVisible ? 'hidden' : ''}" id="lp-visibility-dropdowns">
                      <div class="col-xs-5">
                          <strong id="lp-visibility-label">${lpVisibilityLabel}:</strong>
                      </div>
                      <div class="col-xs-7">
                        <div class="lp-club-container ${selectedVisibility === 'club' ? '' : 'hidden'}">
                            ${selectpicker({
                                name: 'lp-clubs',
                                options: ClubsObj
                                    .filter((c) => c.clubPublished)
                                    .map((c) => ({
                                        label: c.name,
                                        value: c.id,
                                    })),
                                multiple: true,
                            })}
                        </div>
                        <div class="lp-region-container  ${selectedVisibility === 'region' ? '' : 'hidden'}">
                            ${selectpicker({
                                name: 'lp-regions',
                                options: countryList.map((c) => ({
                                    value: c.code,
                                    label: c.name,
                                })),
                                multiple: true,
                            })}
                        </div>
                      </div>
                  </div>
                  <div class="row">
                      <div class="col-xs-12 mb-1">
                          <strong>${t('Banner background image')}:</strong>
                      </div>
                      <div class="col-xs-12 pb-4">

                        <div class="club-lp-droparea">
                          <div id="club-lp-bg" class="dropzone drop-area" style="background-image: url(${lpData?.bannerBG})">
                          </div>
                          <small>${t('Please only upload image files (jpg, png etc) - other formats will not be converted.')}</small>
                        </div>
                      </div>
                  </div>
                  <div class="row vertical-align-row mb-2">
                      <div class="col-xs-5">
                          <strong>${t('Show offers')}:</strong>
                      </div>
                      <div class="col-xs-7">
                        <div class="switch">
                            <label>${t('OFF')} <input type="checkbox" id="club-lp-show-offers" ${lpData?.showOffers ? 'checked' : ''}><span class="lever lever switch-col-blue"></span> ${t('ON')}</label>
                        </div>
                      </div>
                  </div>
                  <div class="row">
                      <div class="col-xs-12 mb-1">
                          <strong>${t('Custom Video')}:</strong>
                          <span id="lp-video-filename" class="flex">
                            <span>
                              ${
                                  lpData?.customVideo?.url
                                      ? lpData?.customVideo.url
                                      : lpData?.customVideo?.uuid
                                        ? `${lpData.customVideo.uuid} <strong>(pending)</strong>`
                                        : ''
                              }
                            </span>
                            ${lpData?.customVideo?.uuid ? '<i class="fa fa-trash-o lp-video-delete" aria-hidden="true"></i>' : ''}
                          </span>
                      </div>
                      <div class="col-xs-12 pb-4">
                        <div class="club-lp-droparea">
                          <div id="club-lp-video" class="dropzone drop-area" style="background-image: url(${lpData?.customVideo?.thumbnail})"></div>
                          <small>${t('Please only upload video files (mp4, mov, webm, etc) - other formats will not be converted or distributed to your external integrations.')}</small>
                        </div>
                      </div>
                  </div>
                  <div class="row vertical-align-row mb-2">
                      <div class="col-xs-5">
                        <strong>${t('Status')}:</strong>
                      </div>
                      <div class="col-xs-7">
                      <div class="col-xs-12">
                          <div class="switch">
                              <label>${t('DRAFT')}<input type="checkbox" id="club-lp-status" ${lpData.published ? 'checked' : ''}><span class="lever lever switch-col-blue"></span>${t('PUBLISHED')}</label>
                          </div>
                      </div>
                  </div>
                </fieldset>
              </div>
            
            `;

            swalWithBootstrapButtons
                .fire({
                    title:
                        lpSelected && !isCloning
                            ? t('Edit Landing Page')
                            : t('Add New Landing Page'),
                    html: modalHtml,
                    width: 640,
                    showCloseButton: true,
                    showConfirmButton: true,
                    showCancelButton: true,
                    confirmButtonText: lpSelected
                        ? t('Save Changes')
                        : t('Save Page'),
                    cancelButtonText: t('Close'),
                    onBeforeOpen: () => {
                        $('#club-lp-bg').dropzone({
                            maxFiles: 1,
                            url: `/api/asset-upload`,
                            acceptedFiles: '.jpeg,.jpg,.png',
                            dictDefaultMessage: t('Drop files here to upload'),
                            timeout: 3600000,
                            addedfiles: () => {
                                $('.lp-modal__confirm').attr('disabled', true);
                            },
                            queuecomplete: () => {
                                $('.lp-modal__confirm').attr('disabled', false);
                            },
                            success: function (_, response) {
                                if (
                                    response !== undefined &&
                                    response.location !== undefined &&
                                    response.originalname !== undefined
                                ) {
                                    newLpData.bannerBG = response.location;
                                }
                            },
                            error: (_, response) => {
                                console.error({ response });
                            },
                            sending: (_, xhr) => {
                                xhr.ontimeout = (e) => {
                                    console.info('Server Timeout');
                                };
                            },
                        });

                        $('#club-lp-video').dropzone({
                            maxFiles: 1,
                            url: `/api/asset-upload?poll=0`,
                            acceptedFiles: '.mp4,.webm,.ogg',
                            dictDefaultMessage: lpData?.customVideo?.thumbnail
                                ? t('Replace Video')
                                : t('Drop files here to upload'),
                            timeout: 3600000,
                            addedfiles: () => {
                                $('#club-lp-video').css('background-image', '');
                                $('.lp-modal__confirm').attr('disabled', true);
                                newLpData.customVideo = {
                                    uuid: '',
                                    thumbnail: '',
                                };
                            },
                            queuecomplete: () => {
                                $('.lp-modal__confirm').attr('disabled', false);
                            },
                            success: async function (_, response) {
                                if (response !== undefined && response?.token) {
                                    const uuid = response?.token?.uuid;
                                    const thumbnail = `${response?.token?.postParams?.url}/processed/thumbnails_large/${uuid}.jpg`;

                                    // update video filename
                                    $('#lp-video-filename').text(
                                        `${uuid} (pending)`,
                                    );

                                    // apply video thumbnail bg
                                    $('#club-lp-video').css(
                                        'background-image',
                                        `url('${response?.token?.postParams?.url}/processed/thumbnails_large/${response?.token?.uuid}.jpg')`,
                                    );

                                    newLpData.customVideo = {
                                        uuid,
                                        thumbnail,
                                    };

                                    updateCustomVideo(uuid);
                                }
                            },
                            error: (_, response) => {
                                console.error({ response });
                            },
                            sending: (_, xhr) => {
                                xhr.ontimeout = (e) => {
                                    console.info('Server Timeout');
                                };
                            },
                        });

                        $('#lp-dates-active').daterangepicker({
                            startDate: moment(
                                lpData?.availabilityDates?.start || new Date(),
                            ).startOf('day'),
                            endDate: moment(
                                lpData?.availabilityDates?.end || new Date(),
                            )
                                .endOf('day')
                                .add(7, 'day'),
                            drops: 'auto',
                            locale: {
                                format: 'Do MMM YYYY',
                            },
                        });

                        // initialize picker
                        $('[name=lp-clubs]').selectpicker();
                        $('[name=lp-regions]').selectpicker();
                        $('#lp-visibility').selectpicker();

                        // change club and regions dropdown on visibility change
                        $('#lp-visibility').on('change', function () {
                            const val = $(this).val();
                            const clubContainer = $('.lp-club-container');
                            const regionContainer = $('.lp-region-container');
                            const visibilityLabel = $('#lp-visibility-label');
                            const visibilityDropdowns = $(
                                '#lp-visibility-dropdowns',
                            );

                            if (val === 'club' || val === 'region') {
                                visibilityDropdowns.removeClass('hidden');
                            } else {
                                clubContainer.addClass('hidden');
                                regionContainer.addClass('hidden');
                                visibilityDropdowns.addClass('hidden');
                            }

                            if (val === 'club') {
                                clubContainer.removeClass('hidden');
                                regionContainer.addClass('hidden');
                                visibilityLabel.text(t('Clubs'));
                            } else if (val === 'region') {
                                clubContainer.addClass('hidden');
                                regionContainer.removeClass('hidden');
                                visibilityLabel.text(t('Regions'));
                            }
                        });

                        // fill dropdown values
                        if (selectedVisibility === 'club') {
                            $('[name=lp-clubs]').selectpicker(
                                'val',
                                lpData.clubs,
                            );
                        }

                        if (selectedVisibility === 'region') {
                            $('[name=lp-regions]').selectpicker(
                                'val',
                                lpData.regions,
                            );
                        }

                        // save picker data
                        $('[name=lp-clubs]').on('change', function () {
                            const val = $(this).val();
                            newLpData.clubs = val;
                        });

                        $('[name=lp-regions]').on('change', function () {
                            const val = $(this).val();
                            newLpData.regions = val;
                        });

                        // show/hide dates active field based on isAlwaysAvailable
                        $('#lp-always-active').on('change', function () {
                            const isChecked = $(this).is(':checked');

                            if (isChecked) {
                                $('#lp-dates-active-container').addClass(
                                    'hidden',
                                );
                                newLpData.isAlwaysAvailable = true;
                                newLpData.availabilityDates = null;
                                return;
                            }

                            newLpData.isAlwaysAvailable = false;

                            // if lp data exists use the existing dates active
                            if (lpData?.availabilityDates) {
                                newLpData.availabilityDates = {
                                    start: lpData.availabilityDates.start,
                                    end: lpData.availabilityDates.end,
                                };
                            }

                            // if there's no existing dates active, set the start date to today and end date to 7 days from now
                            if (!lpData.availabilityDates && !newLpData.availabilityDates) {
                                newLpData.availabilityDates = {
                                    start: moment(new Date())
                                        .startOf('day')
                                        .format('MM-DD-YYYY'),
                                    end: moment(new Date())
                                        .endOf('day')
                                        .add(7, 'day')
                                        .format('MM-DD-YYYY'),
                                };
                            }

                            $('#lp-dates-active-container').removeClass(
                                'hidden',
                            );
                        });

                        // save dates active when changed
                        $('#lp-dates-active').on(
                            'apply.daterangepicker',
                            function (ev, picker) {
                                const startDate =
                                    picker.startDate.format('MM-DD-YYYY');
                                const endDate =
                                    picker.endDate.format('MM-DD-YYYY');

                                if (newLpData.availabilityDates) {
                                    newLpData.availabilityDates.start = startDate;
                                    newLpData.availabilityDates.end = endDate;
                                    return;
                                }

                                newLpData.availabilityDates = {
                                    start: startDate,
                                    end: endDate,
                                };
                            },
                        );

                        // delete video
                        $('#lp-video-filename').on(
                            'click',
                            '.lp-video-delete',
                            function () {
                                newLpData.customVideo = {
                                    uuid: '',
                                    thumbnail: '',
                                };
                                $('#club-lp-video').css('background-image', '');
                                $('#lp-video-filename').text('');
                                $('#club-lp-video .dz-message .dz-button').text(
                                    t('Drop files here to upload'),
                                );
                            },
                        );

                        // save page title
                        let slugChanged = false;
                        if ($('#club-lp-slug').val() !== '') {
                            slugChanged = true;
                        }
                        $('#club-lp-title').on('input', function () {
                            $(this).removeClass('error');

                            newLpData.title = $(this).val();
                            const slug = slugify($(this).val());

                            // if the slug field is empty fill the slug based on the page name converted to slug
                            if (
                                $('#club-lp-slug').val() === '' ||
                                !slugChanged
                            ) {
                                newLpData.slug = slug;
                                $('#club-lp-slug').val(slug);
                            }
                        });

                        // save page slug
                        $('#club-lp-slug').on('input', function () {
                            $(this).removeClass('error');
                            newLpData.slug = $(this).val();
                            slugChanged = true;
                        });

                        // save banner text
                        $('#club-lp-banner-text').on('input', function () {
                            newLpData.bannerText = $(this).val();
                        });

                        // save show offers
                        $('#club-lp-show-offers').on('change', function () {
                            newLpData.showOffers = $(this).is(':checked');
                        });

                        // save status
                        $('#club-lp-status').on('change', function () {
                            newLpData.published = $(this).is(':checked')
                        });
                    },
                    customClass: {
                        container:
                            'club-lp__modal device-details-dialog__container',
                        confirmButton:
                            'lp-modal__confirm btn btn-primary waves-effect',
                        cancelButton:
                            'lp-modal__cancel btn btn-default waves-effect',
                    },
                    preConfirm: async () => {
                        // if the submission is clone, check the existing landing pages, if the slug already exist then add '-1' in the end
                        if (isCloning) {
                            const slug = newLpData.slug;
                            const existingSlugs = clubLPs.map(
                                (item) => item.slug,
                            );

                            if (existingSlugs.includes(slug)) {
                                newLpData.slug = `${slug}-1`;
                            }
                        }

                        // select all existing form elements and remove error class
                        let hasError = false;
                        $('#club-lp-form')
                            .find('input, select')
                            .removeClass('error');

                        // add error class in all empty fields
                        if (!newLpData.title) {
                            $('#club-lp-title').addClass('error');
                            hasError = true;
                        }

                        if (
                            !newLpData.isAlwaysAvailable &&
                            !newLpData.availabilityDates
                        ) {
                            $('#lp-dates-active').addClass('error');
                            hasError = true;
                        }

                        if (!newLpData.slug) {
                            $('#club-lp-slug').addClass('error');
                            hasError = true;
                        }

                        if (hasError) return false;

                        const { isAlwaysAvailable, availabilityDates, ...lp } = newLpData;
                        const availability = isAlwaysAvailable
                            ? { isAlwaysAvailable: true, availabilityDates: null }
                            : { isAlwaysAvailable: false, availabilityDates };

                        // If data has id, it means it is in edit mode
                        let res = false;
                        if (lp.id) {
                            res = await updateLP({
                                ...lp,
                                ...availability,
                            });
                        } else {
                            // trigger submission here
                            res = await submitAddLP({
                                ...lp,
                                ...availability,
                            });
                        }

                        if (res) {
                            console.log(res);
                            return;
                        }

                        return Swal.showValidationMessage(
                            t('An error has occured. Please try again.'),
                        );
                    },
                    onClose: () => {
                        isCloning = false;
                    },
                })
                .then((response) => {
                    console.log({ response });
                });
        };

        const fillClubLPDisplay = (data) => {
            $('#club-lp').off();

            $('#club-lp').removeClass('isLoading');
            currentData['#club-lp'] = data;

            const showAll = $('#show-published-items').is(':checked');

            // save loaded data
            loadedClubData = data;

            // clear the table
            const lpTableEl = $('#club-lp-table');
            const lpTableBody = lpTableEl.find('tbody');
            lpTableBody.html('');

            // add show all class to the table
            if (showAll) {
                lpTableEl.removeClass('lp-hide-inactive');
            } else {
                lpTableEl.addClass('lp-hide-inactive');
            }

            const formatDate = (date) => {
                return moment(new Date(date)).format('Do MMM YYYY');
            };

            const landingPages = [...data.items]
                .filter((item) => {
                    if (showAll) return true;

                    return item.published;
                })
                // don't show deleted landing pages
                .filter((item) => !item.deleted);

            const lpDtConfig = {
                responsive: true,
                dom: '<"toolbar">rtip',
                order: [[0, 'asc']],
                stateSave: true,
                // Show all results in table...
                iDisplayLength: -1,
                columnDefs: [
                    {
                        targets: 'no-sort',
                        orderable: false,
                    },
                    { width: '110px', targets: 0 },
                ],
                paging: false,
                info: false,
                language: {
                    infoEmpty:
                        t('Showing') +
                        ' 0 ' +
                        t('to') +
                        ' 0 ' +
                        t('of') +
                        ' 0 ' +
                        t('entries'),
                    emptyTable: t('No data available in table'),
                    zeroRecords: t('No matching records found'),
                    infoFiltered:
                        '(' +
                        t('filtered from') +
                        ' _MAX_ ' +
                        t('total entries') +
                        ')',
                    aria: {
                        sortAscending: `: ${t('activate to sort column ascending')}`,
                        sortDescending: `: ${t('activate to sort column descending')}`,
                    },
                },
            };

            if (getOS() == 'ios' || getOS() == 'android')
                lpDtConfig.scrollX = true;
            let lpTable = null;

            if (!$.fn.dataTable.isDataTable('#club-lp-table')) {
                lpTable = $('#club-lp-table').DataTable(lpDtConfig);
            } else {
                // assign the existing table to the variable
                lpTable = $('#club-lp-table').DataTable();
            }

            lpTable.clear();

            // fill the table body with data
            for (const item of landingPages) {
                item.isActive = (item.isAlwaysAvailable && item.published) || false;
                if (!item.isAlwaysAvailable) {
                    const currentDate = moment();
                    const startDate = moment(item.availabilityDates.start);
                    const endDate = moment(item.availabilityDates.end);

                    item.isExpired = endDate.isBefore(currentDate);
                    item.isActive = currentDate.isBetween(startDate, endDate);
                }

                // TODO: Visit by them club included in the clubs array or by the region, pick 1 club
                let exampleClub = null;
                if (item?.regions?.length) {
                    exampleClub = ClubsObj?.find((c) => c?.localisation?.country?.iso_code === item.regions[0] && c.clubPublished)?.id;
                } else if (item?.clubs?.length) {
                    exampleClub = item.clubs[0];
                } else {
                    exampleClub = ClubsObj?.find((c) => c.clubPublished)?.id;
                }

                const domainPrefix = devStage ? 'stage.' : '';
                const checkURL = `http://${domainPrefix}ubxtraining.com/gym/${exampleClub}/${item.slug}`;

                const rowItem = `
                <tr class="lp-row ${item.isActive ? 'lp-tr-active' : 'lp-tr-inactive'}" data-lp-id="${item.id}">
                    <td>
                        <span class="use-skeleton">${item.title}</span>
                    </td>
                    <td class="hidden-xs">
                        <a class="lp-link use-skeleton" href="${checkURL}" target="_blank">/${item.slug}</a>
                    </td>
                    <td class="visible-xl">
                        <div class="use-skeleton d-flex gap-1">
                            ${!item?.regions?.length && !item?.clubs?.length ? '<span class="capitalize lp-item__visibility badge badge-blue">GLOBAL</span>' : ''}
                            ${item?.regions?.map((item) => `
                                <div class="capitalize lp-item__visibility badge badge-blue gap-1">
                                    <div style="height: 16px; width: 20px;">
                                        <img style="height: 20px; width: 20px; transform: translateY(-2.5px);" src="/assets/images/flags/${item}.svg">
                                    </div>
                                    <span>${item}</span>
                                </div>`).join('') || ''}
                        </div>
                    </td>
                    <td class="visible-xl">
                        <div class="use-skeleton d-flex gap-1">
                            ${item?.clubs?.map((item) => `<span class="capitalize lp-item__visibility badge">${item}</span>`).join('') || ''}
                        </div>
                    </td>
                    <td class="hidden-sm hidden-xs">
                        <span class="use-skeleton" ${item.isExpired ? 'style="color: #c4b405;"' : ''}>
                            ${item?.availabilityDates ? `${formatDate(item.availabilityDates.start)} - ${formatDate(item.availabilityDates.end)}` : 'Always Available'}
                        </span>
                    </td>
                    <td class="visible-xl">
                        <div class="use-skeleton d-flex gap-1">
                            ${item.isExpired
                                ? `<span class="badge badge-yellow">${t('EXPIRED')}</span>`
                                : item.isActive
                                    ? `<span class="badge badge-green">${t('ACTIVE')}</span>`
                                    : `<span class="badge badge-grey">${t('INACTIVE')}</span>`
                            }
                            ${item.published
                                ? `<span class="badge badge-green">${t('PUBLISHED')}</span>`
                                : `<span class="badge badge-grey">${t('DRAFT')}</span>`}
                        </div>
                    </td>
                    <td class="text-right">
                        <div class="flex gap-1 justify-end items-start">
                            ${item.id !== 'free-trial'
                                ? `
                                    <button class="lp-item__action btn btn-xs btn-primary btn-outline waves-effect lp-item__edit use-skeleton" data-lp-id="${item.id}">
                                        ${t('Edit')}
                                    </button>
                                    `
                                : ''}
                            <button
                                class="lp-item__action btn btn-xs btn-primary btn-outline waves-effect lp-item__clone use-skeleton"
                                data-lp-id="${item.id}"
                            >
                                ${t('Clone')}
                            </button>
                            ${item.id !== 'free-trial'
                                ? `${item.published
                                    ? `<button class="lp-item__action btn btn-xs btn-primary btn-outline waves-effect lp-item__draft use-skeleton" data-lp-id="${item.id}">
                                            ${t('Move to draft')}
                                        </button>`
                                    : `<button class="lp-item__action btn btn-xs btn-primary btn-outline waves-effect lp-item__publish use-skeleton" data-lp-id="${item.id}">
                                            ${t('Publish')}
                                        </button>`}
                                    <button class="lp-item__action btn btn-xs btn-primary btn-outline waves-effect lp-item__delete use-skeleton" data-lp-id="${item.id}">
                                        <i class="fa fa-trash-o" aria-hidden="true"></i>
                                    </button>`
                                : ''}
                        </div>
                    </td>
                </tr>`;

                lpTable.row.add($(rowItem));
            }

            $('.website-management').on(
                'click',
                '.lp-link',
                function (event) {
                    event.stopPropagation();
                },
            );

            $('.website-management').on(
                'click',
                '.lp-row',
                function () {
                    const id = $(this).data('lp-id');

                    const foundLp = currentData['#club-lp'].items.find(
                        (item) => item.id === id,
                    );
                    lpSelected = foundLp;

                    initClubLPForm(foundLp);
                },
            );

            $('.website-management').on(
                'click',
                '.lp-item__edit, .lp-item__clone',
                function (event) {
                    event.stopPropagation();

                    const id = $(this).data('lp-id');
                    const isClone = $(this).hasClass('lp-item__clone');
                    isCloning = isClone;

                    const foundLp = currentData['#club-lp'].items.find(
                        (item) => item.id === id,
                    );
                    lpSelected = foundLp;

                    if (isClone) {
                        const clonedLp = { ...foundLp };
                        clonedLp.id = undefined;
                        clonedLp.slug += '-clone';
                        clonedLp.title += ' (Clone)';
                        lpSelected = null;
                        initClubLPForm(clonedLp);
                        return;
                    }

                    initClubLPForm(foundLp);
                },
            );

            $('.website-management').on(
                'click',
                '.lp-item__delete',
                function (event) {
                    event.stopPropagation();

                    const id = $(this).data('lp-id');
                    toggleConfirmDelete(id, () => {
                        deleteLP({ id });
                    });
                },
            );

            $('.website-management').on(
                'click',
                '.lp-item__draft',
                function (event) {
                    event.stopPropagation();

                    const id = $(this).data('lp-id');
                    updateLP({ id, published: false });
                },
            );

            $('.website-management').on(
                'click',
                '.lp-item__publish',
                function (event) {
                    event.stopPropagation();

                    const id = $(this).data('lp-id');
                    updateLP({ id, published: true });
                },
            );

            if (searchTextFilter) {
                lpTable?.search(searchTextFilter)?.draw();
            } else {
                lpTable?.search('')?.draw();
            }
        };

        const initClubLP = () => {
            $('.website-management-tabs a[href="#club-lp"]').on(
                'show.bs.tab',
                () => {
                    if (!selectedID) return;
                    $('#club-lp').addClass('isLoading');

                    fetchData(
                        '',
                        fillClubLPDisplay,
                        true,
                        '/api/admin/landing-pages',
                    );
                    searchTextFilter = '';
                },
            );
            $('#club-lp-add').on('click', (e) => {
                e.preventDefault();
                toggleForm(
                    false,
                    false,
                    {},
                    clubLPForm,
                    clubLPDisplay,
                    initClubLPForm,
                );
            });
            $('#club-lp-cancel').on('click', (e) => {
                e.preventDefault();
                toggleForm(
                    true,
                    false,
                    {},
                    clubLPForm,
                    clubLPDisplay,
                    initClubLPForm,
                );
            });
        };

        const clubLPForm = '#club-lp-form';
        const clubLPDisplay = '#club-lp-display';

        // .... trigger init
        initWebsiteManagement();
        initPress();
        initMappings();
        initClubLP();
        getCountryList();

        this.isInitialised = true;
    },
};
