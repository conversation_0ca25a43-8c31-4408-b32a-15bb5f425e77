let saasBillingInvoicesTable;

function sSaaSBillingInvoiceHistory() {
    const rootElement = $('#invoice-history');
    const filtersLeftElement = rootElement.find('.invoice-filters-left');
    const filtersRightElement = rootElement.find('.invoice-filters-right-container');
    const tableLoader = rootElement.find('.table-loader');
    const tableBody = rootElement.find('tbody:not(.table-loader)');

    let tableRecords = [];
    let platformConnectedAccountId = '';
    let stripePublishableKey = '';
    let billingAccessibleRegions = [];

    const qsObject = getQueryStringsObject();
    let earliestBillingCycle = moment();
    let yearFilterOptions = [];

    const filters = {
        search: qsObject.search || '',
        status: qsObject.status || 'all',
        month: qsObject.month || moment().format('MM'),
        year: qsObject.year || moment().format('YYYY'),
        country: qsObject.country || 'all',
        club: qsObject.club || 'all',
    };

    async function init() {
        // populate table with skeleton loaders
        rootElement.find('.table-loader').html(
            Array(5)
                .fill(0)
                .map(
                    () => `
                        <tr>
                            <td><span class="use-skeleton rounded">XXXX</span></td>
                            <td><span class="use-skeleton rounded">XXXXX XXXX</span></td>
                            <td><span class="use-skeleton rounded">XXXXXXXX</span></td>
                            <td><span class="use-skeleton rounded">XX XXXXXXX</span></td>
                            <td><span class="use-skeleton rounded">XXXX-XX-XX XX:XX</span></td>
                            <td><span class="use-skeleton rounded">XX</span></td>
                            <td><span class="use-skeleton rounded">XXXXXXXXX</span></td>
                            <td><span class="use-skeleton rounded">XXXX</span></td>
                            <td><span class="use-skeleton rounded">XXXXXXXXXX</span></td>
                            <td><span class="use-skeleton rounded">XXXXXXXXXXXXX</span></td>
                        </tr>
                    `
                )
        );

        tableBody.addClass('hidden');
        tableLoader.removeClass('hidden');

        renderSearchNav();

        const [stripeKeyRes, platformKeyRes, access] = await Promise.all([
            getStripePublishableKeyAPI(),
            getPlatformConnectedAccountIdAPI(),
            getMyOrgAccessForModuleAPI('billing-admin'),
        ]);

        if (stripeKeyRes[0]) stripePublishableKey = stripeKeyRes[1];
        if (platformKeyRes[0]) platformConnectedAccountId = platformKeyRes[1];
        billingAccessibleRegions = access[0] ? access[1].allRegions : [];

        loadPageFilters(), loadRecordsTable();
    }

    async function loadPageFilters() {
        yearFilterOptions = [];
        monthFilterOptions = [];

        new PageFilters(filtersLeftElement, [
            { id: 'year', label: 'Year', options: [{ label: 'skeleton', value: 'skeleton', selected: true }] },
            { id: 'month', label: 'Month', options: [{ label: 'skeleton', value: 'skeleton', selected: true }] },
        ]);

        let earliestYear = moment().startOf('year');
        filtersLeftElement.addClass('isLoading');
        const response = await getEarliestBilledMonthAPI();
        filtersLeftElement.removeClass('isLoading');

        if (response[0]) {
            earliestBillingCycle = moment(response[1], 'YYYY-MM').subtract(1, 'month');
            earliestYear = earliestBillingCycle.clone();
        }

        while (earliestYear.isSameOrBefore(moment(), 'year')) {
            yearFilterOptions = [earliestYear.format('YYYY'), ...yearFilterOptions];
            earliestYear.add(1, 'year');
        }

        // validate selected month and year
        if (filters.month !== 'all' && filters.year !== 'all') {
            const monthMoment = moment(`${filters.year}-${filters.month}`, 'YYYY-MM');
            const isFutureMonth = monthMoment.isAfter(moment().subtract(1, 'month'), 'month');
            const isEarlierMonth = monthMoment.isBefore(earliestBillingCycle, 'month');
            if (isFutureMonth || isEarlierMonth) {
                filters.month = moment().subtract(1, 'month').format('MM');
                filters.year = moment().subtract(1, 'month').format('YYYY');
                updateHrefQuery({ month: filters.month, year: filters.year });
            }
        }

        renderPageFilters();
        $('.retry-all-failed').on('click', handleRetryAllFailed);
    }

    function handleRetryAllFailed() {
        const failedRecords = tableRecords.filter((record) => record.status === 'failed');
        failedRecords.forEach((record) => manuallyBillClub(record, null));
    }

    function renderPageFilters() {
        let monthOptions = [];
        for (let i = 0; i < 12; i++) {
            const label = moment().month(i).format('MMMM');
            const value = moment().month(i).format('MM');
            const selected = value === filters.month;

            // month should be disabled if it's in the future
            // same with earlier months of the earliest year
            const year = filters.year === 'all' ? earliestBillingCycle.format('YYYY') : filters.year;
            const monthMoment = moment(`${year}-${value}`, 'YYYY-MM');
            const isFutureMonth = monthMoment.isAfter(moment().subtract(1, 'month'), 'month');
            const isEarlierMonth = monthMoment.isBefore(earliestBillingCycle, 'month');

            if (isFutureMonth || isEarlierMonth) continue;
            monthOptions = [{ label, value, selected }, ...monthOptions];
        }

        const formattedYearOptions = yearFilterOptions.map((year) => {
            return { label: year, value: year, selected: year === filters.year };
        });

        new PageFilters(
            filtersLeftElement,
            [
                {
                    id: 'year',
                    label: 'Year',
                    options: [
                        { label: 'All', value: 'all', selected: filters.year === 'all' },
                        ...formattedYearOptions,
                    ],
                },
                {
                    id: 'month',
                    label: 'Month',
                    options: [{ label: 'All', value: 'all', selected: filters.month === 'all' }, ...monthOptions],
                },
                {
                    id: 'status',
                    label: 'Status',
                    options: [
                        { label: 'All', value: 'all', selected: filters.status === 'all' },
                        { label: 'Success', value: 'success', selected: filters.status === 'success' },
                        { label: 'Failed', value: 'failed', selected: filters.status === 'failed' },
                        { label: 'Processing', value: 'processing', selected: filters.status === 'processing' },
                        { label: 'Pending', value: 'pending', selected: filters.status === 'pending' },
                        { label: 'Skipped', value: 'skipped', selected: filters.status === 'skipped' },
                        {
                            label: '3D Secure Required',
                            value: '3d-secure-required',
                            selected: filters.status === '3d-secure-required',
                        },
                    ],
                },
                {
                    id: 'country',
                    label: 'Country',
                    options: [
                        { label: 'All', value: 'all', selected: filters.country === 'all' },
                        ...billingAccessibleRegions.map((region) => {
                            return {
                                label: region.full_name,
                                value: region.iso_code,
                                selected: region.iso_code === filters.country,
                            };
                        }),
                    ],
                },
                {
                    id: 'club',
                    label: 'Club',
                    options: [
                        { label: 'All', value: 'all', selected: filters.club === 'all' },
                        ...ClubsObj.sort((a, b) => a.id.localeCompare(b.id)).map((club) => {
                            return {
                                label: club.name || club.id,
                                value: club.id,
                                selected: club.id === filters.club,
                            };
                        }),
                    ],
                    searchBox: true,
                },
            ],
            (newFilters) => {
                Object.keys(newFilters).forEach((key) => {
                    filters[key] = newFilters[key];
                });

                renderPageFilters();
                loadRecordsTable();
            }
        );

        new PageFilters(
            filtersRightElement,
            [
                {
                    id: 'results-shown',
                    label: 'Results Per Page',
                    options: [
                        { label: '10', value: '10', selected: false },
                        { label: '25', value: '25', selected: false },
                        { label: '50', value: '50', selected: false },
                        { label: '100', value: '100', selected: true },
                        { label: '500', value: '500', selected: false },
                        { label: '1000', value: '1000', selected: false },
                        { label: 'All', value: '-1', selected: false },
                    ],
                },
            ],
            (newFilters) => {
                saasBillingInvoicesTable.page.len(parseInt(newFilters['results-shown'])).draw();
            }
        );
    }

    function billingStatusBadge(status) {
        const badgeClasses = {
            skipped: 'badge-grey',
            failed: 'badge-red',
            success: 'badge-green',
            processing: 'badge-grey',
            pending: 'badge-grey',
            '3d-secure-required': 'badge-yellow',

            // stripe intent statuses
            succeeded: 'badge-green',
            canceled: 'badge-red',
            requires_capture: 'badge-yellow',
            processing: 'badge-yellow',
            requires_action: 'badge-yellow',
            requires_confirmation: 'badge-yellow',
            requires_payment_method: 'badge-yellow',

            // custom payment status
            refunded: 'badge-grey',
        };

        const badge = badgeClasses[status.toLowerCase()] || 'badge-blue';
        return `<span class="badge ${badge}">${status.toUpperCase()}</span>`;
    }

    function loadRecordsTable() {
        tableRecords = [];

        if (saasBillingInvoicesTable) {
            saasBillingInvoicesTable.clear();
            saasBillingInvoicesTable.destroy();
        }

        const exportOptions = {
            format: {
                body: function (data, row, column, node) {
                    return $(node).attr('data-export') ?? data;
                },
            },
            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8],
        };

        let exportFilename = 'SaaS_Billing_Invoices' + new Date().toISOString();
        const rowsPerPage = 100;
        saasBillingInvoicesTable = $('#saas-billing-invoices-table').DataTable({
            responsive: true,
            dom: 'rtip',
            iDisplayLength: rowsPerPage, // Initial number of entries to show
            lengthMenu: [
                [10, 25, 50, 100, 500, 1000, -1],
                [10, 25, 50, 100, 500, 1000, 'All'],
            ], // Dropdown options for number of entries to show
            pagingType: 'simple',
            ajax: {
                url: `/api/saas-billing/monthly-records`,
                type: 'GET',
                data: function (d) {
                    tableBody.addClass('hidden');
                    tableLoader.removeClass('hidden');

                    d.filters = filters;
                    d.filters.ignoreNotStarted = true;
                },
                dataSrc: function (json) {
                    tableBody.removeClass('hidden');
                    tableLoader.addClass('hidden');
                    tableRecords = json.data;
                    return json.data;
                },
            },
            processing: true,
            serverSide: true,
            order: [[1, 'desc']],
            autoWidth: false,
            createdRow: (row, data) => {
                $(row).attr('data-record-id', data.id);
                $(row).addClass('clickable');
                $(row).on('click', () => openRecordRowDialog(data.id));
            },
            columnDefs: [
                { targets: [0], width: '30px' },
                { targets: [6, 9], orderable: false },
            ],
            columns: [
                { data: null, render: (_, __, row) => row.invoiceID },
                {
                    data: null,
                    render: (_, __, row) => moment(row.billingMonth, 'YYYY-MM').subtract(1, 'month').format('MMM YYYY'),
                },
                { data: null, render: (_, __, row) => row.clubID },
                {
                    data: null,
                    render: (_, __, row) => {
                        const country = row.country;
                        if (!country) return 'N/A';
                        const img = `<img src="/assets/images/flags/${country?.iso_code?.toUpperCase()}.svg" style="width: 20px;" />`;
                        return country ? `${img} ${country.full_name}` : 'N/A';
                    },
                    createdCell: (cell, _, record) => $(cell).attr('data-export', record.country?.full_name ?? 'N/A'),
                },
                {
                    data: null,
                    className: 'last-attempt-date',
                    render: (_, __, row) =>
                        row.lastAttemptDate ? moment.unix(row.lastAttemptDate).format('YYYY-MM-DD HH:mm') : '',
                },
                { data: null, className: 'attempt-count text-center', render: (_, __, row) => row.attemptCount ?? 0 },
                {
                    data: null,
                    className: 'payment-ids',
                    render: (_, __, row) => renderPaymentIds(row.paymentIds),
                    createdCell: (cell, _, record) => {
                        if (!record.paymentIds?.length) return $(cell).attr('data-export', '');
                        $(cell).attr('data-export', record.paymentIds?.join(', ') ?? '');
                    },
                },
                {
                    data: null,
                    className: 'text-center',
                    render: (_, __, row) => {
                        const report = row.report;
                        const stripeFormatAmount = Number(report?.grandTotal ?? 0) * 100;
                        return stripeFormatAmount.formatStripeCurrency('USD');
                    },
                },
                {
                    data: null,
                    className: 'status',
                    render: (_, __, row) => billingStatusBadge(row.status),
                    createdCell: (cell, _, record) => $(cell).attr('data-export', record.status),
                },
                {
                    data: null,
                    render: (_, __, row) => {
                        const monthMoment = moment(row.billingMonth, 'YYYY-MM').subtract(1, 'month');
                        const query = `?month=${monthMoment.format('MM')}&year=${monthMoment.format('YYYY')}`;
                        const href = `/api/saas-billing/extended-access/monthly-usage-report/${row.clubID}/invoice${query}`;
                        return `<a href="${href}" target="_blank" class="link-to-invoice">
                            <i class="fas fa-file-invoice"></i> ${t('Download Invoice')}
                        </a>`;
                    },
                },
            ],
            buttons: [
                {
                    extend: 'copy',
                    className: 'btn btn-sm btn-default',
                    exportOptions,
                },
                {
                    extend: 'print',
                    title: exportFilename,
                    className: 'btn btn-sm btn-default',
                    orientation: 'landscape',
                    exportOptions,
                },
                {
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.csv',
                    exportOptions,
                },
                {
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.pdf',
                    orientation: 'landscape',
                    exportOptions,
                },
            ],
            bAutoWidth: false,
        });

        wrapTableWithScrollableDiv('#saas-billing-invoices-table');
        saasBillingInvoicesTable.draw();
    }

    function openRecordRowDialog(recordId) {
        const record = tableRecords.find((r) => r.id === recordId);
        if (!record) return;

        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2 saas-billing-record-dialog');
        const amountDue = Number(record.report?.grandTotal ?? 0) * 100;
        const formattedAmount = amountDue.formatStripeCurrency('USD');
        const billingMonth = moment(record.billingMonth, 'YYYY-MM').subtract(1, 'month');
        const invoiceQuery = `?month=${billingMonth.format('MM')}&year=${billingMonth.format('YYYY')}`;
        const invoiceLink = `/api/saas-billing/extended-access/monthly-usage-report/${record.clubID}/invoice${invoiceQuery}`;
        const actionsDisabled = !['failed', '3d-secure-required'].includes(record.status) ? 'disabled' : '';

        const statusOptions = [
            { label: 'Success', value: 'success' },
            { label: 'Failed', value: 'failed' },
            { label: 'Skipped', value: 'skipped' },
        ];

        if (record.status === 'processing') {
            statusOptions.push({ label: 'Processing', value: 'processing' });
        }

        if (record.status === 'pending') {
            statusOptions.push({ label: 'Pending', value: 'pending' });
        }

        if (record.status === '3d-secure-required') {
            statusOptions.push({ label: '3D Secure Required', value: '3d-secure-required' });
        }

        $(html).html(`
            <div class="d-flex flex-column gap-1" style="padding: 10px 15px;">
                <div class="d-flex justify-between align-center">
                    <h1 style="font-size: 20px; font-weight: bold; margin: 0px;">
                        ${formattedAmount}
                    </h1>
                    <div class="d-flex gap-1 align-center">
                        <span class="save-status"></span>
                        ${selectpicker({
                            name: 'status',
                            value: record.status,
                            liveSearch: false,
                            options: statusOptions,
                        })}
                        <div class="btn-group">
                            <button type="button" class="btn btn-icon dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="width: 32px;">
                                <i class="fas fa-ellipsis-v" style="margin: 0px;"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <li class="${actionsDisabled}">
                                    <a href="#" class="manually-bill-club" ${actionsDisabled}>
                                        <i class="fas fa-redo" style="margin-right: 6px;"></i>Retry Payment
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <hr class="m-0" />

                <div class="d-flex gap-3 py-1">
                    ${[
                        { label: t('ID'), value: record.invoiceID ?? 'N/A' },
                        { label: t('Billing Cycle'), value: billingMonth.format('MMMM YYYY') },
                        { label: t('Club ID'), value: record.clubID },
                        { label: t('Charge Attempts'), value: record.attemptCount },
                        {
                            label: t('Invoice'),
                            value: `<a href="${invoiceLink}" target="_blank">
                                ${record.clubID}-${billingMonth.format('YYYY-MM')}.pdf
                            </a>`,
                        },
                    ]
                        .map(
                            ({ label, value }) => `
                                <div>
                                    <label class="m-0" style="font-size: 14px; color: #6A7383; font-weight: bold;">${label}</label>
                                    <p class="m-0" style="font-size: 14px;">${value}</p>
                                </div>
                            `
                        )
                        .join('')}
                </div>

                <h1 style="font-size: 20px; font-weight: bold;">
                    ${t('Payment Details')}
                </h1>
                <hr class="m-0"/>
                <div style="overflow-x: auto;">
                    <table class="table" style="margin: 0px;">
                        <thead>
                            <th>${t('ID')}</th>
                            <th>${t('Date')}</th>
                            <th>${t('Status')}</th>
                            <th>${t('Message')}</th>
                        </thead>
                        <tbody>
                            ${
                                record.paymentIds
                                    ?.map((paymentId) => {
                                        const link = paymentId.includes('no_usable_card')
                                            ? 'No Usable Card'
                                            : paymentId.includes('no_payment_required')
                                            ? 'No Payment Required'
                                            : `<a href="https://dashboard.stripe.com/connect/accounts/${platformConnectedAccountId}/payments/${paymentId}" target="_blank">${paymentId}</a>`;
                                        return `
                                            <tr class="payment-row" data-id="${paymentId}">
                                                <td>${link}</td>
                                                <td class="date" style="width: 85px">
                                                    Fetching <img src="/assets/images/tail-spin.svg" width="14px"></img>
                                                </td>
                                                <td class="status">
                                                    Fetching <img src="/assets/images/tail-spin.svg" width="14px"></img>
                                                </td>
                                                <td class="message"></td>
                                            </tr>
                                        `;
                                    })
                                    .join('') ??
                                `
                                    <tr>
                                        <td colspan="100%" class="text-muted">
                                            <em>No payment details available</em>
                                        </td>
                                    </tr>
                                `
                            }
                        </tbody>
                    </table>
                </div>

                <div class="d-flex flex-wrap gap-3">
                    <div style="flex: 1;">
                        <h1 style="font-size: 20px; font-weight: bold;">
                            ${t('Communication Logs')}
                        </h1>
                        <hr class="m-0" style="margin-bottom: 1rem;"/>
                        <div class="d-flex flex-column gap-1" style="overflow-y: auto; max-height: 370px;">
                            ${
                                record.communicationLogs
                                    ?.sort((a, b) => b.createdAt - a.createdAt)
                                    .map((log) => {
                                        const logDate = moment.unix(log.createdAt).format('YYYY-MM-DD HH:mm');
                                        const logType = log.type === 'email' ? 'Email' : 'SMS';
                                        const logStatus =
                                            log.status !== 200
                                                ? 'failed'
                                                : log.type === 'email'
                                                ? 'success'
                                                : 'scheduled';
                                        const logMessage =
                                            log.status === 200 ? log.details.subject ?? '' : log.error ?? '';

                                        const statuses = {
                                            failed: `<div class="badge badge-red">Failed</div>`,
                                            success: `<div class="badge badge-green">Success</div>`,
                                            scheduled: `<div class="badge badge-green">Scheduled</div>`,
                                        };

                                        let sentTo = log.details?.to?.[0] ?? '';
                                        if (logType.details?.to?.length > 1) {
                                            sentTo += ` +${log.details.to.length - 1} more`;
                                        }

                                        return `
                                            <div>
                                                <div class="d-flex justify-between">
                                                    <div class="d-flex gap-1">
                                                        <h5 class="m-0">${t(logType)}</h5>
                                                        <div class="text-muted small">${sentTo}</div>
                                                    </div>
                                                    <small>${logDate}</small>
                                                </div>
                                                <div class="d-flex justify-between align-start gap-2">
                                                    <p class="mb-1">${logMessage}</p>
                                                    ${statuses[logStatus]}
                                                </div>
                                            </div>
                                        `;
                                    })
                                    .join('') ??
                                `
                                    <div class="text-muted">
                                        <em>${t('No communication logs available')}</em>
                                    </div>
                                `
                            }
                        </div>
                    </div>
                    <div style="flex: 1;">
                        <h1 style="font-size: 20px; font-weight: bold;">
                            ${t('Admin Notes')}
                        </h1>
                        <hr class="m-0" style="margin-bottom: 1rem;" />
                        <div style="overflow-y: auto; max-height: 200px;">
                            ${
                                record.notes
                                    ?.sort((a, b) => b.createdAt - a.createdAt)
                                    ?.map((note) => {
                                        const noteDate = moment.unix(note.createdAt).format('YYYY-MM-DD HH:mm');
                                        return `
                                            <div class="admin-note mb-1">
                                                <div class="d-flex justify-between">
                                                    <h5 class="m-0">${note.createdBy}</h5>
                                                    <small>${noteDate}</small>
                                                </div>
                                                <pre class="content">${note.content}</pre>
                                            </div>
                                        `;
                                    })
                                    .join('') ?? ''
                            }
                        </div>

                        <div class="d-flex flex-column gap-half add-note-container">
                            ${textArea({ name: 'add-note', placeholder: 'Add a note here...', rows: 3 })}
                            <div class="d-flex justify-end">
                                <em class="text-danger small"></em>
                                <button class="btn btn-primary add-note-btn" style="margin-top: 10px;">Add Note</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>    
        `);

        if (['processing', 'success'].includes(record.status)) {
            $(html).find('.selectpicker[name="status"]').attr('disabled', true);
        }

        $(html)
            .find('.payment-row')
            .each(async (i, cell) => {
                const paymentId = $(cell).attr('data-id');

                if (paymentId.includes('no_usable_card')) {
                    const unixDate = paymentId.split('_')[3];
                    $(cell).find('.date').html(moment.unix(unixDate).format('YYYY-MM-DD HH:mm'));
                    $(cell).find('.message').html('No usable card. Please update your club payment methods');
                    return $(cell).find('.status').html(billingStatusBadge('failed'));
                }

                if (paymentId.includes('no_payment_required')) {
                    const unixDate = paymentId.split('_')[3];
                    $(cell).find('.date').html(moment.unix(unixDate).format('YYYY-MM-DD HH:mm'));
                    $(cell).find('.message').html('No payment required. Skipping payment');
                    return $(cell).find('.status').html(billingStatusBadge('skipped'));
                }

                const response = await getStripePaymentIntentAPI(paymentId);

                if (!response[0]) {
                    $(cell).find('.date').html('');
                    return $(cell).find('.status').html('<em class="text-danger">Failed to fetch payment</em>');
                }

                const refundedCharge = response[1].charges?.data?.find((charge) => charge.refunded);
                const status = refundedCharge ? 'refunded' : response[1].status;
                $(cell).find('.status').html(billingStatusBadge(status));
                $(cell).find('.date').html(moment.unix(response[1].created).format('YYYY-MM-DD HH:mm'));

                let errorMessage = response[1].last_payment_error?.message ?? response[1].cancellation_reason;

                if (
                    !errorMessage &&
                    response[1].status === 'requires_action' &&
                    response[1].next_action.type === 'use_stripe_sdk'
                ) {
                    errorMessage = `3D Secure action required`;
                }

                $(cell)
                    .find('.message')
                    .html(errorMessage ?? '');
            });

        $(html).find('.selectpicker').selectpicker();
        $(html)
            .find('.manually-bill-club')
            .on('click', async () => {
                if (actionsDisabled) return;

                if (record.status === '3d-secure-required') {
                    loadingOverlay(html, true, true);
                    await confirmStripePaymentIntent(stripePublishableKey, platformConnectedAccountId, record);

                    const response = await getClubMonthlyRecordAPI(record.clubID, record.billingMonth);
                    if (!response[0]) return Swal.close();

                    tableRecords = tableRecords.map((r) => (r.id === record.id ? response[1] : r));
                    updateRecordRenderItem(response[1]);

                    return openRecordRowDialog(record.id);
                }

                manuallyBillClub(record, html);
            });

        let savingStatusTimeout = null;
        $(html)
            .find('.selectpicker[name="status"]')
            .on('changed.bs.select', async (e) => {
                const value = $(e.currentTarget).val();

                $(html).find('.save-status').html(`<div class="badge badge-grey">Saving...</div>`);
                const response = await updateClubMonthlyRecordAPI(record.id, { status: value });

                if (!response[0]) {
                    $(html).find('.save-status').html(`<div class="badge badge-red">Saving Failed</div>`);
                } else {
                    $(html).find('.save-status').html(`<div class="badge badge-green">Saved</div>`);
                    tableRecords = tableRecords.map((r) => (r.id === record.id ? { ...r, status: value } : r));
                    updateRecordRenderItem({ ...record, status: value });
                }

                savingStatusTimeout && clearTimeout(savingStatusTimeout);
                savingStatusTimeout = setTimeout(async () => $(html).find('.save-status').html(''), 1500);
            });

        $(html)
            .find('.add-note-btn')
            .on('click', async () => {
                const note = $(html).find('textarea[name="add-note"]').val();
                if (!note) return;

                const response = await addNoteToClubMonthlyRecordAPI(record.id, note);
                if (!response[0]) return $(html).find('.add-note-container em').html(response[1]);

                tableRecords = tableRecords.map((r) =>
                    r.id === record.id ? { ...r, notes: [...(r.notes ?? []), response[1]] } : r
                );

                openRecordRowDialog(record.id);
            });

        swalWithBootstrapButtons.fire({
            title: 'Billing Record Details',
            width: '800px',
            html,
            showCloseButton: true,
            showConfirmButton: false,
            confirmButtonText: 'Save',
        });
    }

    function renderPaymentIds(paymentIds) {
        const paymentIdsList = paymentIds
            ?.filter((id) => !id.includes('no_usable_card') && !id.includes('no_payment_required'))
            .map((id) => `<a href="https://dashboard.stripe.com/payments/${id}" target="_blank">${id}</a>`);

        if (!paymentIdsList?.length) return `<div class="badge badge-grey">No Payment</div>`;
        return paymentIdsList?.join('<br>');
    }

    function updateRecordRenderItem(record) {
        const row = $(`#saas-billing-invoices-table tr[data-record-id="${record.clubID}-${record.billingMonth}"]`);
        if (record.lastAttemptDate) {
            row.find('.last-attempt-date').html(moment.unix(record.lastAttemptDate).format('YYYY-MM-DD HH:mm'));
        }

        row.find('.attempt-count').html(record.attemptCount);
        row.find('.status').html(billingStatusBadge(record.status));
        row.find('.payment-ids').html(renderPaymentIds(record.paymentIds));
    }

    async function manuallyBillClub(record, html) {
        html && loadingOverlay(html, true, true);
        updateRecordRenderItem({ ...record, status: 'processing' });
        const response = await manuallyBillClubAPI(record.clubID, record.billingMonth);

        if (!response[0]) {
            html && loadingOverlay(html, false);
            updateRecordRenderItem(record);
            return instantNotification(response[1], 'error');
        }

        billingStatusChecker(record, moment().unix(), html);
    }

    async function billingStatusChecker(record, triggerTime, html) {
        const response = await getClubMonthlyRecordAPI(record.clubID, record.billingMonth);

        const isAfterTrigger = response[0] && response[1].modifiedAt >= triggerTime;
        const isProcessing = response[0] && response[1].status === 'processing';

        if (!response[0] || !isAfterTrigger || isProcessing) {
            return setTimeout(() => billingStatusChecker(record, triggerTime, html), 5000);
        }

        tableRecords = tableRecords.map((r) => (r.id === record.id ? response[1] : r));
        updateRecordRenderItem(response[1]);

        if (!html) return;

        await confirmStripePaymentIntent(stripePublishableKey, platformConnectedAccountId, response[1]);

        // after confirming payment intent, status should change to success or failed
        const confirmUpdateRes = await getClubMonthlyRecordAPI(record.clubID, record.billingMonth);
        if (confirmUpdateRes[0]) {
            tableRecords = tableRecords.map((r) => (r.id === record.id ? confirmUpdateRes[1] : r));
            updateRecordRenderItem(confirmUpdateRes[1]);
        }

        openRecordRowDialog(record.id);
    }

    function renderSearchNav() {
        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => saasBillingInvoicesTable?.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => saasBillingInvoicesTable?.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => saasBillingInvoicesTable?.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => saasBillingInvoicesTable?.button(3).trigger() },
        ];
        searchbarHeader('.search-nav-container', exportButtons, {
            usePhTheme: true,
            expandableButtons: {
                startIndex: 0,
                count: exportButtons.length,
                label: t('Export'),
                icon: 'fa-ellipsis-v',
                id: 'saas-billing-export-buttons',
            },
        });

        if (filters.search) $('.search-bar').val(filters.search);

        let searchTimeout = null;
        $('.search-bar').on('input', (e) => {
            filters.search = e.target.value;
            updateHrefQuery({ search: filters.search });

            searchTimeout && clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                loadRecordsTable();
            }, 500);
        });
    }

    init();
}

async function confirmStripePaymentIntent(stripePublishableKey, platformConnectedAccountId, record) {
    const { id: recordId, paymentIds, clubID } = record;

    // these ids are not actual payment intents so we don't fetch them from stripe
    const idsToIgnore = ['no_usable_card', 'no_payment_required'];
    const paymentIdsToConfirm = paymentIds?.filter((id) => !idsToIgnore.some((ignore) => id.includes(ignore)));
    const paymentIntentIdDetails = await Promise.all(paymentIdsToConfirm?.map(getStripePaymentIntentAPI) ?? []);

    const fetchedIntents = paymentIntentIdDetails.filter(([ok]) => ok).map(([, intent]) => intent);
    if (!fetchedIntents.length) return [false, 'No payment intents fetched'];

    const paymentIntentToConfirm = fetchedIntents.find(({ status }) => status === 'requires_action');
    if (!paymentIntentToConfirm) {
        // some intents may be stuck in requires_payment_method so we need to validate them and
        // change the status to failed so that the user can retry the payment
        await validateMonthlyRecordStatusAPI(clubID, recordId);
        return [false, 'No payment intent requires 3D Secure'];
    }

    const stripe = Stripe(stripePublishableKey, { stripeAccount: platformConnectedAccountId });
    const { payment_method, client_secret, id: intentId } = paymentIntentToConfirm;

    const response = await stripe.confirmCardPayment(client_secret, { payment_method });

    // this could happen if the user removes the card from their account and adds a new one
    if (response.error?.shouldRetry === false) {
        await cancelStripePaymentIntentAPI(selectedID, intentId);
    }

    return validateMonthlyRecordStatusAPI(clubID, recordId);
}
