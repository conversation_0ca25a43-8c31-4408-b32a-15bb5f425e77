var smallSidebar = false;

function toggleSmallNav() {

    smallSidebar = (smallSidebar === true) ? false : true;

    // Trigger resize event to redraw any graphs etc that may have changed...
    setTimeout(function(){
        window.dispatchEvent(new Event('resize'))
    }, 500);

    if(smallSidebar) {
        setSmallNavState(true);
    } else {
        setSmallNavState(false);
    }
    // Save state in local storage...
    localStorage.setItem("smallNav", smallSidebar);

}


function setSmallNavState(state) {
    // Remove badges so they won't show up in small menu pop-up's
    // Remove them first so we don't get duplicates...
    $('#leftsidebar .is-new .badge').remove();
    $('#leftsidebar .is-updated .badge').remove();

    if(state) {
        // Main window & bar css classes
        $("#leftsidebar").addClass('smallSidebar');
        $("section.content").addClass('smallSidebarContent'); // TODO: Remove binding to section.content if all pages uses .ph-page
        $('.ph-page').addClass('smallSidebarContent');

        $("#leftsidebar li a span").hide(200);
        $("#leftsidebar li.header").hide(200);
        $("#leftsidebar li.header").animate({width:'0px'}, 200);
        if (signedInUserData?.isGlobalAdmin) $('#leftsidebar .admin-mode').addClass('hidden');

        $(".legal .version, .legal .systemStatusLink, .legal hr").hide();

        // Toggle chevron icon..
        $("#toggleNavSmallBtn i").html("chevron_right");

        if(getOS() != 'ios' || getOS() != 'android') {
          $('.smallSidebar [data-smallNavItem="tooltip"]').tooltip({ container: 'body', open: handleArrowTooltipOpen });
        }

        // Bind menu pop-up's for small menu..
        bindSmallClickEvents(true);
    } else {
        // Main window & bar css classes
        $("#leftsidebar").removeClass('smallSidebar');
        $("section.content").removeClass('smallSidebarContent');
        $('.ph-page').removeClass('smallSidebarContent');
        $("#leftsidebar li a span").show(200);
        $("#leftsidebar li.header").show(200);
        $("#leftsidebar li.header").animate({width:'300px'}, 200);
        if (signedInUserData?.isGlobalAdmin) $('#leftsidebar .admin-mode').removeClass('hidden');

        $(".legal .version, .legal .systemStatusLink, .legal hr").show();

        $("#toggleNavSmallBtn i").html("chevron_left");

        // Add badges back in...
        $('#leftsidebar .is-new').append('<span class="badge badge-updated">New</span>');
        $('#leftsidebar .is-updated').append('<span class="badge badge-updated">Updated</span>');

        // Unbind small context menu
        bindSmallClickEvents(false);

        // Redraw selected menus etc...
        reDrawNavSelections();

        // $('.sidebar [data-smallNavItem="tooltip"]').tooltip('destroy');
    }

    $(document).ready(function() {
        $('[data-smallNavItem="tooltip"]').each(function() {
            let dest = 'data-original-title'
            let origin = 'title'

            if (state) {
                dest = 'title'
                origin = 'data-original-title'
            }

            $(this).attr(dest, $(this).attr(origin));
            $(this).removeAttr(origin);
        });
    });
}


function bindSmallClickEvents(state) {

    if(state) {
        // Bind events requested...

        $("#leftsidebar ul > li a, #leftsidebar ul > div > li a").each(function() {

            var menu = [];

            $(this).parent().find("ul > li > a").each(function() {

                var menuLink = $(this).attr('href');
                const isHidden = $(this).css('display') === 'none';
                if(!isHidden) {
                    menu.push(
                        {
                            name: $(this).text(),
                            fun: function () {
                                // Some URL's go off to third party systems - not just our app...
                                if(menuLink.startsWith("http")) {
                                    window.open(menuLink, '_blank');
                                } else {
                                    sammy.setLocation(menuLink)
                                }
                            }
                        }
                    );
                }

            });

            if (menu.length) {
                $(this).contextMenu(menu, {
                    displayAround:'trigger',
                    position: 'right'
                });
            }
        });
    } else {
        // Unbind menu events requested (large sidebar is visible so no need for context menu...
        $("#leftsidebar ul > li a, #leftsidebar ul > div > li a").each(function() {
            // console.dir(
            //     $._data(this, "events" )
            // );

            $(this).off(".contextMenu");
        });
    }

}

function firstLoadSetSmallNavState() {
    if (localStorage.getItem("smallNav") !== null) {
        if(localStorage.getItem("smallNav") == 'true') {
            smallSidebar = true;
        }
    }

    setSmallNavState(smallSidebar);
}
