let saasBillingImportsTable = null;
function sSaaSBillingImports() {
    let importedReports = [];

    const qsObj = getQueryStringsObject();
    let lastClubSelectedInDialog = qsObj.clubFilter ?? selectedID;
    const filters = {
        clubFilter: qsObj.clubFilter ?? 'all',
        feature: qsObj.feature ?? 'all',
        search: qsObj.search ?? '',
    };

    const reportTemplate = dedent(`
        [
            {
                "period": "December-2023",
                "chargeBreakdown": {
                    "Price per GB Stored": 0.03,
                    "Price per Coaching Screen": 3.5,
                    "Price per Duress Button": 2,
                    "Performance Hub EA Software": 10,
                    "Price per Camera": 4
                },
                "charges": {
                    "Camera Software": {
                        "total": 24,
                        "rate": 4,
                        "count": 6
                    },
                    "CCTV Storage Consumed": {
                        "total": 1.09,
                        "rate": 0.03,
                        "count": {
                            "gbDays": "18825.99",
                            "count": "1.67 TB"
                        }
                    },
                    "Coaching Screen Device & Software Charge": {
                        "total": 45.5,
                        "rate": 3.5,
                        "count": 13
                    },
                    "Duress Buttons": {
                        "total": 6,
                        "rate": 2,
                        "count": 3
                    },
                    "Extended Access Software": {
                        "total": 10,
                        "rate": 10
                    }
                },
                "grandTotal": "164.39"
            }
        ]
    `);

    async function init() {
        renderSearchNav();
        renderPageFilters();
        renderSkeletonReports();
        const reportsRes = await listImportedReportsAPI();
        if (!reportsRes[0]) {
            return $('#billing-imports table tbody').html(
                `<tr>
                    <td><em class="text-danger">Failed to retrieve reports</em></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>`
            );
        }
        importedReports = reportsRes[1];
        renderImportedReports();
    }

    function renderSearchNav() {
        searchbarHeader($('#billing-imports .search-nav-container'), [
            { label: 'Import', icon: 'fa-file-arrow-up', onClick: openImportDialog },
        ]);

        filters.search && $('#billing-imports .search-nav-container input').val(filters.search);

        $('#billing-imports .search-nav-container input').on('input', (e) => {
            filters.search = e.target.value;
            updateHrefQuery({ search: filters.search });
            handleTableFilters();
        });
    }

    function renderPageFilters() {
        new PageFilters(
            '.imports-filters',
            [
                {
                    id: 'clubFilter',
                    label: 'Club',
                    options: [
                        { label: 'All', value: 'all', selected: filters.clubFilter === 'all' },
                        ...ClubsObj.map((c) => ({ label: c.name, value: c.id, selected: c.id === filters.clubFilter })),
                    ],
                },
                {
                    id: 'feature',
                    label: 'Feature',
                    options: [
                        { label: 'All', value: 'all', selected: filters.feature === 'all' },
                        { label: 'CCTV Devices', value: 'CCTVDevices', selected: filters.feature === 'CCTVDevices' },
                        {
                            label: 'Coaching Screens',
                            value: 'CoachingScreens',
                            selected: filters.feature === 'CoachingScreens',
                        },
                        {
                            label: 'Duress Buttons',
                            value: 'DuressButtons',
                            selected: filters.feature === 'DuressButtons',
                        },
                    ],
                },
            ],
            (newFilters) => {
                Object.keys(newFilters).forEach((key) => {
                    filters[key] = newFilters[key];
                    updateHrefQuery({ [key]: filters[key] });
                    if (key === 'clubFilter') lastClubSelectedInDialog = filters.clubFilter;
                });
                handleTableFilters();
            }
        );
    }

    function renderImportedReports() {
        $('#billing-imports table tbody').html('');
        saasBillingImportsTable?.clear();
        saasBillingImportsTable?.destroy();

        importedReports.forEach(renderImportedReportsRow);

        saasBillingImportsTable = $('#billing-imports table').DataTable({
            responsive: true,
            dom: 'rtip',
            iDisplayLength: 50,
            columnDefs: [{ targets: [3], orderable: false }],
        });

        handleTableFilters();
    }

    function handleTableFilters() {
        if (!saasBillingImportsTable) return;

        const clubFilter =
            filters.clubFilter === 'all'
                ? ''
                : ClubsObj.find((c) => c.id === filters.clubFilter)?.name ?? filters.clubFilter;
        const featureFilter = filters.feature === 'all' ? '' : filters.feature;

        saasBillingImportsTable.column(1).search(clubFilter).draw();
        saasBillingImportsTable.column(2).search(featureFilter).draw();
        saasBillingImportsTable.search(filters.search).draw();
    }

    function renderSkeletonReports() {
        $('#billing-imports table tbody').html('');
        Array(4)
            .fill(0)
            .forEach(() =>
                renderImportedReportsRow({
                    date: moment().unix(),
                    feature: 'CCTVDevices',
                    grandTotal: 0,
                    isSkeleton: true,
                })
            );
        saasBillingImportsTable?.draw();
    }

    function renderImportedReportsRow(report) {
        const isLoading = report.isSkeleton ? 'isLoading' : '';

        const dateMoment = moment.unix(report.date);
        const period = report.daily ? dateMoment.format('DD MMMM YYYY') : dateMoment.format('MMMM YYYY');

        const superseded =
            report.daily && importedReports.some((r) => !r.daily && dateMoment.isSame(moment.unix(r.date), 'month'));

        const supersededBadge = superseded
            ? `<div class="badge badge-orange" data-toggle="tooltip" title="This report has been superseded by a monthly report for the same month. Either delete this report or delete the monthly report so that this report will be used.">Obsolete</div>`
            : '';

        const clubName = ClubsObj.find((c) => c.id === report.clubID)?.name ?? report.clubID;

        const div = $(`
            <tr class="${isLoading}">
                <td>
                    <div class="use-skeleton"><a href="#" class="open-dialog">${period}</a></div>
                </td>
                <td>
                    <div class="use-skeleton">${clubName}</div>
                </td>
                <td>
                    <div class="use-skeleton">${report.feature ?? 'ALL'}</div>
                </td>
                <td>
                    <div class="d-flex justify-end gap-1">
                        ${supersededBadge}
                        <button class="btn btn-danger delete-btn use-skeleton">
                            <i class="fa fa-trash mr-0"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);

        $(div).find('[data-toggle="tooltip"]').tooltip();

        div.find('.delete-btn').on('click', async () => {
            confirmDeleteDialog(
                'Delete Report?',
                '<p>Deleting this report is irreversible and will affect billing.<br>Are you sure you want to proceed?</p>',
                async () => {
                    const response = await deleteImportedReportAPI(report.id);
                    if (!response[0]) return instantNotification(response[1], 'danger', 3000);
                    instantNotification('Report deleted successfully', 'success', 3000);
                    init();
                }
            );
        });

        div.find('.open-dialog').on('click', (e) => {
            e.preventDefault();
            openReportDialog(report);
        });

        $('#billing-imports table tbody').append(div);
    }

    function openImportDialog() {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');
        $(html).html(`
            <div class="body d-flex gap-1 flex-column">
                <div style="max-width: 300px;">
                    ${selectpicker({
                        label: 'Club',
                        name: 'club',
                        options: ClubsObj.map((c) => ({ label: c.name, value: c.id })),
                        value: lastClubSelectedInDialog === 'all' ? selectedID : lastClubSelectedInDialog,
                    })}
                </div>
                ${textArea({
                    label: 'List of Reports',
                    name: 'reports',
                    placeholder: 'Paste your json here...',
                    rows: 20,
                    props: 'style="resize: vertical"',
                    value: reportTemplate,
                })}

                <em class="text-muted">Periods can refer to a month or a day. If a day is specified, the report will be used for that day. If a month is specified, the report will be used for the entire month and will supersede any daily reports for that month.</em>
            </div>
        `);

        $(html).find('.selectpicker').selectpicker();

        $(html)
            .find('select[name="club"]')
            .on('change', (e) => {
                lastClubSelectedInDialog = e.target.value;
            });

        swalWithBootstrapButtons
            .fire({
                title: 'Import Reports',
                width: 800,
                html,
                showCancelButton: true,
                confirmButtonText: 'Import',
                showCloseButton: true,
                preConfirm,
            })
            .then((result) => {
                if (!result.value) return;
                instantNotification('Monthly reports imported successfully', 'success', 3000);
                init();
            });

        async function preConfirm() {
            const selectedClub = $(html).find('select[name="club"]').val();
            const json = $(html).find('textarea').val();
            let reports = null;

            if (!json) {
                return Swal.showValidationMessage('Please paste your json');
            }

            try {
                reports = JSON.parse(json);
            } catch (error) {
                return Swal.showValidationMessage('Invalid json. Please try again.');
            }

            const response = await importClubSaasBillingMonthlyReportsAPI(selectedClub, reports);
            if (!response[0]) return Swal.showValidationMessage(response[1]);
        }
    }

    function openReportDialog(report) {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');
        $(html).html(`
            <div class="body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Count</th>
                            <th>Rate</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        `);

        const charges = report.daily ? report.formatted.charges : report.charges;
        charges?.forEach((charge) => {
            const count = charge.count.count ? `${charge.count.count} (${charge.count.gbDays})` : charge.count;
            $(html).find('tbody').append(`
                <tr>
                    <td>${charge.label}</td>
                    <td>${count}</td>
                    <td>$${charge.rate}</td>
                    <td>$${charge.total}</td>
                </tr>
           `);
        });

        swalWithBootstrapButtons.fire({
            title: 'Report Details',
            width: 800,
            html,
            showCloseButton: true,
            showConfirmButton: false,
        });
    }

    init();
}
