function initiateWelcomeTourQuestion() {

    const setupHTML = `
        <div class="performance-hub-welcome">
            <h3>${t("Welcome to the Performance Hub")}</h3>
            <hr>
            <p>${t("Get started with a short self-guided tour of the Performance Hub, key features & various configuration components for your Club.")}</p>
            <img src="/assets/images/ph-onboarding.jpg" style="width: 100%;">
        </div>
    `;

    swalWithBootstrapButtons.fire({
        width: 600,
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: `${t('Start Tour')}`,
        cancelButtonText: `${t("Skip, I know what I'm doing")}`,
        html: setupHTML,
    }).then(result => {
        if(result.value) {
            startOnboardingTour();
        } else {
            // User clicked skip...
            saveOnboardingTourComplete();
        }
    })

}

function saveOnboardingTourComplete() {
    $.ajax({
        url: '/api/user-preferences',
        method: 'PUT',
        data: JSON.stringify({ tutorialWizardExited: true }),
        contentType: 'application/json',
        success: () => console.info('user preferenses saved'),
        error: (xhr, textStatus, errorThrown) => {
            console.error(xhr, textStatus, errorThrown);
        }
    });
}

function startOnboardingTour() {

    var tour = new Tour({
        debug: true,
        backdrop: true,
        //orphan: true,
        delay: 500,
        name: "7bd57ddc-6ac9-4827-9491-34b4797c618d",
        onEnd: () => {
            saveOnboardingTourComplete();
            tourEndConfetti();
        },
        template: `<div class='popover tour'>
                <div class='arrow'></div>
                <h3 class='popover-title'></h3>
                <div class='popover-content'></div>
                <div class='popover-navigation'>
                    <button class='btn btn-default' data-role='prev'>« ${t("Prev")}</button>
                    <span data-role='separator'>|</span>
                    <button class='btn btn-default' data-role='next'>${t("Next")} »</button>
                </div>
                <button class='btn btn-default' data-role='end'>${t("End tour")}</button>
            </div>`,
        steps: [
            {
                element: "#leftsidebar",
                title: `${t("Welcome to the Performance Hub")}!`,
                content: t("To start, use the side menu, to navigate the various modules & features of the Performance Hub.")
            },
            {
                element: "#toggleNavSmallBtn",
                title: `${t("Hide")}/${t("Show Navigation")}`,
                placement: 'right',
                backdropPadding: 5,
                content: `
                    ${t("If you're on a mobile device or a device with a small screen, you can use this button to make the main navigation Smaller and Larger. This is useful when using parts of the Performance Hub that have a complex workflow.")}
                `,
                onShow: function (tour) {
                    sammy.setLocation('/');
                }
            },
            {
                element: "#navbar-collapse .club-spoof-select",
                title: t("Multiple Club Selection"),
                placement: 'bottom',
                content: t("If you have multiple clubs, you can use this Dropdown to select which club you would like to administer.")
            },
            {
                element: ".dashboard",
                title: t("Your Dashboard"),
                content: `
                    ${t("When you log into the Performance Hub each day, you'll be presented with a dashboard of the selected club")}.
                    <br><br>
                    ${t("Key metrics such as your club's Revenue history & Review Management Bookings etc are loaded in real-time from your various systems we integrate with.")}
                `,
                placement: 'left top',
            },
            {
                element: "#filter-dashboard-range",
                title: t("Changing the report period"),
                backdropPadding: 10,
                placement: 'bottom',
                content: t("You may change the 'Time Period' of data that is displayed on your Dashboard by using the preset periods."),
            },
            // {
            //     element: "#dashboardSelectMoreWizard",
            //     title: "More Periods",
            //     backdropPadding: 10,
            //     placement: 'left',
            //     content: "If you would like to view a greater date range, you will find additional periods by clicking the More dropdown.",
            //     onShow: function (tour) {
            //         setTimeout(function(){
            //             // Click the 'more' dropdown for the next tour item...
            //             $('.btn-group-dropdown').addClass('open'); // Opens the dropdown
            //         }, 500);
            //     }
            // },
            {
                onShow: function (tour) {
                    sammy.setLocation('/wiki');
                },
                element: "body > application > main-container > section > div",
                title: t("Club Operations Made Simple"),
                placement: 'left',
                backdropPadding: 10,
                content: `
                    ${t("Your club operational guides are all located in our 'Wiki', and it provides one simple way to access all operational content for your club.")}
                    <br><br>
                    ${t("Support guides, processes, technology setup guides and more are all located in the Wiki.")}
                `,
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/club-details');
                },
                element: "body > application > main-container .club-hours-block",
                title: t("Manage your club"),
                placement: 'left',
                content: `
                    ${t("You can manage all key aspects of your club's public website listing (such as your Opening Hours, Address, Social Media Page Names, etc) in the Manage your Club part of the system.")}
                `,
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/review-management');
                },
                element: ".review-management .container-fluid",
                title: t("Review Management"),
                placement: 'left',
                content: `
                    ${t("View, Manage & Respond to your reviews from Social Media, Google, and many other sites around the Internet from the Review Management screen.")}
                    <br><br>
                    ${t("You can also customise your settings here so 5 Star reviews automatically publish to your Club page.")}
                `,
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/designer');
                },
                element: ".designer .tab-content",
                title: t("Marketing & Print Material"),
                placement: 'left',
                content: `
                    ${t("Download your Digital & Social print material from our 'Marketing & Print' tool in common formats such as JPG, PNG, PDF, and PSD.")}
                    <br><br> 
                    ${t("Designs are rendered/customised automatically by the Performance Hub to automatically include your relevant Facility Details such as")}:
                    <ul>
                        <li>${t("Club Hours")}</li>
                        <li>${t("Contact Details")}</li>
                        <li>${t("Club Address")}</li>
                        <li>${t("and more")}</li>
                    </ul>
                `
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/club-analytics');
                },
                element: "#pageview-graph-container",
                title: t("View your Analytics"),
                placement: 'bottom',
                backdropPadding: 10,
                content: `
                    ${t("You can track your marketing traffic using Google Analytics for your Club, which is embedded into the Performance Hub.")}
                    <br><br>
                    ${t("Traffic from sources such as:")}
                    <ul>
                        <li>${t("Print QR Codes")}</li>
                        <li>${t("Website Club Page Visitors")}</li>
                        <li>${t("Social Media")}</li>
                        <li>${t("And more")}...</li>
                    </ul>
                    ${t("are tracked here")}.
                `,
            },
            {
                element: "#analyticsSelectDateRangeWizard",
                title: t("Select a custom date range"),
                placement: 'left',
                backdropPadding: 10,
                content: t("Like the Dashboard, you can also select custom date/time ranges to view your analytics from a certain period."),
                onShow: function(tour) {
                    sammy.setLocation('/club-analytics');
                    setTimeout(function(){
                        $("#reportrange").click();
                    }, 500);
                },
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/coaching-screens');
                },
                element: ".screens-main-container",
                title: t("Smart Facility"),
                placement: 'left',
                content: `
                    ${t("If your club is equipped with 'Smart Facility' technology, you may manage it from the Performance Hub here.")}
                    <br><br>
                    ${t("Updating the Workouts on your TV's, configuring automatic power on/off and other core setup are also made here.")}
                `
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/store-ui');
                },
                element: "#storeV2",
                title: t("Performance Hub Store"),
                placement: 'left',
                backdropPadding: 10,
                content: `
                    ${t("Manage your inventory, orders, and tracking for essential club  merchandise such as technology items, gloves/wraps, and more directly from the Performance Hub Store.")}
                `,
            },
            {
                onShow: function (tour) {
                    sammy.setLocation('/support');
                },
                element: "#divTicketsSection",
                title: t("Support & Ticketing"),
                placement: 'left',
                backdropPadding: 5,
                content: `
                    ${t("Lastly, if you have any issues you can contact us via the Ticketing functionality of the Performance Hub.")}
                    <br><br>
                    ${t("All communications (both from Email, and directly submitted via Performance Hub) are logged here, and history can be viewed by the rest of your Club (if creating Club tickets)")}.
                `,
            }
        ]
    });

    function tourEndConfetti() {
        setTimeout(function(){
            sammy.setLocation('/');
        }, 500);
        setTimeout(function(){
            $("main-container").append(`<div class="ph-welcome-text">${t("Welcome")}!</div>`);
            confetti.start();
        }, 700);
        setTimeout(function(){
            $(".ph-welcome-text").fadeOut(2000, function() {});
        }, 7000);
        setTimeout(function(){
            confetti.stop();
        }, 8000);
    }

    // Initialize the tour
    tour.init();

    // Start the tour
    tour.start(true);
    //tour.restart();

}
