let displayUIOptionsTable = null;

async function sDisplayUIOptions() {
    const root = $('#display-admin #display-ui-options');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');

    const query = getQueryStringsObject();

    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('DISPLAY_ADMIN_FILTERS_DISPLAY_UI_OPTIONS');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }

    // Use query params first, then localStorage, then defaults
    let search = query.search || savedFilters.search || '';
    let displayUIOptions = [];

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const [querySuccess, allDisplayUIOptions] = await listAllDisplayUIOptionsAPI();

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!querySuccess) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        displayUIOptions = allDisplayUIOptions;

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => displayUIOptionsTable && displayUIOptionsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => displayUIOptionsTable && displayUIOptionsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => displayUIOptionsTable && displayUIOptionsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => displayUIOptionsTable && displayUIOptionsTable.button(3).trigger() },
        ];

        const buttons = [
            { label: t('Add UI Option'), icon: 'fa-add', onClick: () => displayUIOptionDialog() },
            ...exportButtons,
        ];

        searchbarHeader(
            root.find('.search-bar-container'),
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'display-ui-options-export-buttons',
                },
            }
        );

        root.find('.search-bar').val(search);

        root.find('.search-bar').on('input', function () {
            search = $(this).val();
            updateHrefQuery({ search });

            // Save search filter to localStorage
            try {
                localStorage.setItem('DISPLAY_ADMIN_FILTERS_DISPLAY_UI_OPTIONS', JSON.stringify({ search }));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }

            renderDisplayUIOptionsTable();
        });

        renderDisplayUIOptionsTable();
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>

            <div class="d-flex align-center gap-3" style="width: 100%; height: 44.8px;">
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 20%;"></div>
                    <div class="d-flex" style="height: 16px; width: 50%; gap: 2px;">
                        <div class="use-skeleton" style="width: 30%;"></div>
                        <div class="use-skeleton" style="width: 30%;"></div>
                        <div class="use-skeleton" style="width: 10%;"></div>
                    </div>
                </div>
            `);
        });
    }

    async function renderDisplayUIOptionsTable() {
        const exportFilename = 'Display_UI_Options__' + (new Date().toISOString());

        displayUIOptionsTable?.clear();
        displayUIOptionsTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('ID')}</th>
                        <th>${t('UI Name')}</th>
                        <th>${t('Description')}</th>
                        <th>${t('URL')}</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        displayUIOptions
            .filter((option) => {
                const searchRegex = new RegExp(search, 'igm');
                return (
                    option.id.match(searchRegex) ||
                    option.name.match(searchRegex) ||
                    option.description.match(searchRegex)
                );
            })
            .forEach((option) => {
                tableBodyEl.append(`
                    <tr data-id="${option.id}">
                        <td><code>${option.id.split('-').shift()}</code></td>
                        <td>${option.name}</td>
                        <td>${option.description}</td>
                        <td>${option.url}</td>
                    </tr>
                `);
            });

        displayUIOptionsTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [[0, 'asc']],
            autoWidth: false,
            iDisplayLength: 50,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Display UI Options',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.pdf'
                },
            ],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const selectedOptionID = $(this).data('id');
                    const option = displayUIOptions.find((opt) => opt.id === selectedOptionID);
                    displayUIOptionDialog(option);
                });
            },
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));

        search && displayUIOptionsTable.search(search).draw();
    }

    function displayUIOptionDialog(option) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');

        $(div).html(`
            <div class="body">
                <div class="d-flex flex-column gap-3">
                    ${textField({ label: t('Name'), name: 'name', value: option?.name })}
                    ${textField({ label: t('Description'), name: 'description', value: option?.description })}
                    ${textField({ label: t('URL'), name: 'url', value: option?.url })}
                </div>
            </div>
        `);

        async function preConfirm() {
            const doc = {
                id: option?.id,
                name: $(div).find('input[name=name]').val(),
                description: $(div).find('input[name=description]').val(),
                url: $(div).find('input[name=url]').val(),
            };

            const response = !option?.id ? await createDisplayUIOptionAPI(doc) : await updateDisplayUIOptionAPI(doc);

            if (!response[0]) Swal.showValidationMessage(response[1]);
            return response[1];
        }

        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 600,
                title: !option ? t('Add UI Option') : t('Edit UI Option'),
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false,
                showCloseButton: true,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                preConfirm,
            })
            .then((result) => {
                if (result.value) return init();
            });
    }

    init();
}
