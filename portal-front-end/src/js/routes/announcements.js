async function sAnnouncements(id=false) {

    let announcementTags=[];
    let adminAccess = null;

    loadingOverlay(".announcements");
    const accessRes = await getMyOrgAccessForModuleAPI('announcements-admin');
    adminAccess = accessRes[0] ? accessRes[1] : null;

    getAnnouncements(id);

    function userHasAccessToAllNotifRegions(notifRegions = []) {
        return notifRegions.every((code) => adminAccess?.orgs.some((org) => org.regions.some((region) => region.iso_code === code)));
    }

    function newEditAnnouncement(id=null, announcementsObj=null) {
        let aTitle = "", aSummary = "", aHTML = "", aDate = null, aTags = [], aRegions = [];
        let aOrgIds = [];

        if(id && announcementsObj) {
            aOrgIds = announcementsObj[id].organisationIds;
            aTitle = announcementsObj[id].title
            aSummary = announcementsObj[id].summary
            aHTML = announcementsObj[id].html
            aDate = announcementsObj[id].date
            aTags = announcementsObj[id].tags
            aRegions = announcementsObj[id].regions || [];
        }

        const orgOptions = adminAccess?.orgs.map((org) => ({
            label: org.name,
            value: org.organisationId,
        })) ?? [];

        const html = document.createElement('div');
        $(html).addClass('new-announcement-dialog ph-dialog-v2');
        $(html).html(`
            <div class="body">
                <div class="d-flex flex-column gap-1">
                    <div class="row">
                        <div class="col-sm-6">
                            ${textField({ label: t('Title'), name: 'title', value: aTitle })}
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group form-group-no-margin">
                                <label>${t('Date')}</label>
                                <div id="announcementDate" class="selectbox selectbox-form-control pull-right">
                                    <i class="material-icons">calendar_today</i>
                                    <span></span> <b class="caret"></b>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group form-group-no-margin">
                        <label>${t('Tags')}</label>
                        <div class="form-line">
                            <input class="form-control" name="tags" type="text" value="${aTags.join(",")}" data-role="tagsinput" placeholder="">
                        </div>
                    </div>

                    ${selectpicker({
                        label: t('Organisations'),
                        name: 'organisations',
                        options: orgOptions,
                        value: aOrgIds,
                        multiple: true,
                    })}

                    ${selectpicker({ label: t('Regions'), name: 'regions', options: [], multiple: true })}
                    ${textArea({ label: 'Summary', name: 'summary', value: aSummary })}

                    <div>
                        <label>${t('Announcement Body')}</label>
                        <div name="body" style="max-height:200px;"></div>
                    </div>
                </div>
            </div>
        `);

        $(html).find('.selectpicker').selectpicker();
        renderRegionOptions();

        $(html).find('[name=organisations]').on('changed.bs.select', function() {
            renderRegionOptions();
        });

        function renderRegionOptions() {
            const orgIds = $(html).find('[name=organisations]').val();
            
            const regions = adminAccess?.orgs.reduce((acc, org) => {
                if (!orgIds.includes(org.organisationId)) return acc;
                
                org.regions.forEach((region) => {
                    if (!acc.some(r => r.iso_code === region.iso_code)) {
                        acc.push(region);
                    }
                });

                return acc;
            }, []).sort((a, b) => a.full_name.localeCompare(b.full_name)) ?? [];

            const regionSelect = $(html).find('[name=regions]');
            regionSelect.empty();
            
            regions.forEach((region) => {
                const selected = aRegions.includes(region.iso_code) ? 'selected' : '';
                
                regionSelect.append(`
                    <option value="${region.iso_code}" ${selected}>
                        ${region.full_name}
                    </option>
                `);
            });

            regionSelect.selectpicker('refresh');
        }

        let announcementObj;
        swalWithBootstrapButtons.fire({
            title: id ? t("Edit Announcement") : t("New Announcement"),
            html,
            width: 900,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: true,
            allowEscapeKey: false,
            allowOutsideClick: false,
            confirmButtonText: `${(id == null) ? t("Create!") : t("Update")}`,
            cancelButtonText: t("Cancel"),
            onBeforeOpen: () => {

                // Bind WYSIWYG editor...
                $('[name=body]').trumbowyg({
                    svgPath: '/assets/images/trumbowyg.svg',
                    // The text editing zone will extend itself when writing a long text
                    autogrow: true,
                    // Links should open in new tabs by default...
                    defaultLinkTarget: '_blank',
                    // Allow image upload in the image dropdown...
                    btnsDef: {
                        // Create a new dropdown
                        image: {
                            dropdown: ['insertImage', 'upload'],
                            ico: 'insertImage'
                        }
                    },
                    btns: [
                        ['viewHTML'],

                        // https://alex-d.github.io/Trumbowyg/demos/#plugins-history
                        ['historyUndo','historyRedo'],

                        // https://alex-d.github.io/Trumbowyg/demos/#plugins-fontfamily
                        ['fontfamily'],
                        // https://alex-d.github.io/Trumbowyg/demos/#plugins-fontsize
                        ['fontsize'],

                        // https://alex-d.github.io/Trumbowyg/demos/#plugins-lineheight
                        ['lineheight'],

                        ['formatting'],
                        ['removeformat'],

                        ['strong', 'em', 'del'],
                        ['superscript', 'subscript'],

                        // https://alex-d.github.io/Trumbowyg/demos/#plugins-colors
                        ['foreColor', 'backColor'],

                        ['link', 'image'],
                        ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                        ['unorderedList', 'orderedList'],
                        ['horizontalRule'],
                        'table',
                        ['fullscreen'],

                    ],
                    plugins: {
                        resizimg: {
                            minSize: 64,
                            step: 16,
                        },
                    }
                });

                if(aHTML) $('[name=body]').trumbowyg('html', aHTML)

                // Bind daterangepicker...
                function displayDateRange(date) {
                    $('#announcementDate span').html(date.format('MMMM D, YYYY'));
                }

                const rangeObject = {};

                rangeObject[t('Tomorrow')] = [moment().add(1, 'days'), moment().add(1, 'days')];
                rangeObject[t('Today')] = [moment(), moment()];
                rangeObject[t('Yesterday')] = [moment().subtract(1, 'days'), moment().subtract(1, 'days')];
                rangeObject[t('Last Week')] = [moment().subtract(7, 'days'), moment().subtract(7, 'days')];

                $('#announcementDate').daterangepicker({
                    singleDatePicker: true,
                    showDropdowns: true,
                    startDate: (id == null) ? moment() : moment.unix(aDate),
                    parentEl: $('#announcementDate').parent().parent(),
                    ranges: rangeObject,
                    locale: {
                      
                        applyLabel: t('Apply'),
                        cancelLabel: t('Cancel'),
                        customRangeLabel: t('Custom Range'),
                        monthNames: [
                            t('Jan'),
                            t('Feb'),
                            t('Mar'),
                            t('Apr'),
                            t('May'),
                            t('Jun'),
                            t('Jul'),
                            t('Aug'),
                            t('Sep'),
                            t('Oct'),
                            t('Nov'),
                            t('Dec'),
                        ],
                        daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
                    }
                }, displayDateRange);

                $('#announcementDate span').html(moment().format('MMMM D, YYYY'));

                $('#announcementDate').on('apply.daterangepicker', function(ev, picker) {
                    $("#announcementDate").data("date", moment(picker.endDate).unix())

                    validate();
                });

                // Check if we've got a name
                $("[name=title]").change(function() {
                    validate()
                });
                $("[name=summary]").change(function() {
                    validate()
                });
                $("[name=regions]").change(function() {
                    validate()
                });

                var tagSearch = new Bloodhound({
                    datumTokenizer: Bloodhound.tokenizers.obj.whitespace('name'),
                    queryTokenizer: Bloodhound.tokenizers.whitespace,
                    local: function() {
                        return $.map(announcementTags, function(tagName) {
                        return { name: tagName }; });
                    }
                });
                tagSearch.initialize();

                $('[name=tags]').tagsinput({
                    typeaheadjs: {
                        name: 'tagSearch',
                        displayKey: 'name',
                        valueKey: 'name',
                        source: tagSearch.ttAdapter()
                    }
                });

                // Load in date if it's not a new announcement.
                if(id) {
                    $('#announcementDate').data('daterangepicker').setStartDate(moment.unix(aDate));
                    $('#announcementDate').data('daterangepicker').setEndDate(moment.unix(aDate));
                    displayDateRange(moment.unix(aDate));
                }

                validate();
                function validate() {
                    var isValid = true;

                    if (!$("[name=title]").val()) isValid = false;
                    if (!$("[name=summary]").val()) isValid = false;
                    if (!$("[name=regions]").val().length > 0) isValid = false;
                    if (!$("[name=organisations]").val().length > 0) isValid = false;

                    if(isValid) {
                        $('.swal2-actions .swal2-confirm').prop('disabled', false);
                    } else {
                        $('.swal2-actions .swal2-confirm').prop('disabled', true);
                    }
                }
            },
            preConfirm: async () => {
                const tagInput = $('[name=tags]').tagsinput('items');
                const tagValues = tagInput[tagInput.length - 1].map((x) => x.toUpperCase());

                announcementObj = {
                    title: $("[name=title]").val(),
                    date: ($("#announcementDate").data("date") === undefined || $("#announcementDate").data("date") == "" || $("#announcementDate").data("date") == null) ? moment().unix() : Number($("#announcementDate").data("date")),
                    summary: $("[name=summary]").val(),
                    tags: tagValues,
                    html: ($('[name=body]').trumbowyg('html') === undefined || $('[name=body]').trumbowyg('html') == false || $('[name=body]').trumbowyg('html') == "") ? t("No detailed information is included with this announcement.") : $('[name=body]').trumbowyg('html'),
                    regions: $('[name=regions]').val() || [],
                    organisationIds: $('[name=organisations]').val() || [],
                }

                // Add ID if we're updating an existing announcement...
                if(id) announcementObj.id = id;

                const [ok, message] = await upsertAnnouncementAPI(announcementObj);
                if (!ok) return Swal.showValidationMessage(message);

                return true;
            }
        }).then((response) => {
            if (!response.value) return;
            instantNotification('Changes Saved');
            getAnnouncements();
        });
    }

    function getAnnouncements(id=false) {

        var announcementsObj = {};
        var fTags = [], fMonth = [], filterTags = "all", filterMonth = "all";

        var req = $.ajax({
            url: '/api/announcements/list',
            method: 'GET',
            contentType: 'application/json',
            success: function(data){
                if((data === undefined) ? false : data) {

                    // Sort (newest first)
                    data.sort(function(a, b) {
                        return parseFloat(b.date) - parseFloat(a.date);
                    });

                    data.forEach(function(announcement) {
                        announcementsObj[announcement.id] = announcement;
                        announcementsObj[announcement.id].filterMonth = moment.unix(announcement.date).format("MMMM YYYY");

                        // Push tags for filtering...
                        announcement.tags.forEach(function(tag) {
                            fTags.pushUnique(tag);
                        });

                        // Push month for filtering...
                        fMonth.pushUnique(moment.unix(announcement.date).format("MMMM YYYY"));

                    });


                    // --- Tags Filter ---
                    $(".tags-filter-list").html(`
                        <li>
                            <input data-tag="all" name="tags" type="radio" id="tag_all" class="with-gap radio-col-blue" checked />
                            <label for="tag_all" class="mt-2">${t('ALL')}</label>
                        </li>
                    `);

                    fTags.forEach(function(tag) {
                        var filterItem =  `
                            <li>
                                <input data-tag="${tag}" name="tags" type="radio" id="tag_${tag.replace(/\W/g, '')}" class="with-gap radio-col-blue" />
                                <label for="tag_${tag.replace(/\W/g, '')}">${tag}</label>
                            </li>
                        `;
                        $(".tags-filter-list").append(filterItem);
                    });
                    announcementTags = fTags;

                    // Bind to filter change...
                    $('input[type=radio][name=tags]').change(function() {
                        filterTags = $(this).data("tag");
                        filterUI();
                    });

                    // --- Date/Month Filter ---
                    $(".month-filter-list").html(`
                        <li>
                            <input data-month="all" data-year="all" name="month" type="radio" id="month_all" class="with-gap radio-col-blue" checked />
                            <label for="month_all">${t('ALL')}</label>
                        </li>
                    `);

                    fMonth.forEach(function(month) {
                        var filterItem =  `
                            <li>
                                <input data-month="${month}" name="month" type="radio" id="tag_${month.replace(/\W/g, '')}" class="with-gap radio-col-blue" />
                                <label for="tag_${month.replace(/\W/g, '')}">${month}</label>
                            </li>
                        `;
                        $(".month-filter-list").append(filterItem);
                    });

                    // Bind to filter change...
                    $('input[type=radio][name=month]').change(function() {
                        filterMonth = $(this).data("month");
                        filterUI();
                    });


                    if(id) {
                        loadAnnouncement(id);
                    } else {
                        // Only create list if function is called without an id...
                        // (otherwise we're loading an individual announcement)
                        // NB List is loaded via the filterUI method which will filter the results based on any options on the side nav.
                        filterUI();
                    }

                    $('.announcements').LoadingOverlay("hide");


                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
            }
        });

        function filterUI(searchText=false) {

            var filteredObj = {};

            // Perform tags filters first....
            if(filterTags == 'all') {
                filteredObj = JSON.parse(JSON.stringify(announcementsObj));
            } else {

                Object.keys(announcementsObj).forEach(function(key) {
                    if(announcementsObj[key].tags.includes(filterTags)) {
                        filteredObj[key] = announcementsObj[key];
                    }
                })
            }

            // Date/Month filters after.
            if(filterMonth != 'all') {

                Object.keys(filteredObj).forEach(function(key) {
                    if(filteredObj[key].filterMonth == filterMonth) {
                        filteredObj[key] = announcementsObj[key];
                    } else {
                        delete filteredObj[key];
                    }
                })
            }

            renderAnnouncements(filteredObj, searchText);

        }

        function renderAnnouncements(announcementsObj, searchText=false) {

            $(".notices-containers").html('');
            $(".announcement-admin-controls").html('');
            $(".view-all-announcements").hide();

            Object.keys(announcementsObj).forEach(function(key) {
                var announcement = announcementsObj[key], adminControls = "";
                const notifRegions = _get(announcement, 'regions', []);
                
                if(adminAccess && userHasAccessToAllNotifRegions(notifRegions)) {
                    adminControls += `
                        <div class="admin-notice-controls">
                            <button type="button" data-id="${announcement.id}" class="btn btn-xs btn-square bg-blue btn-edit-notice waves-effect">
                                <i class="material-icons">edit</i>
                            </button>
                            <button type="button" data-id="${announcement.id}" class="btn btn-xs btn-square bg-red btn-delete-notice waves-effect">
                                <i class="material-icons">delete</i>
                            </button>
                        </div>
                    `;
                }

                var announcementTags = '';
                announcement.tags.forEach(function (item) {
                    announcementTags += `<span class="badge badge-grey">${item}</span>`
                });

                $(".notices-containers").append(`
                    <div class="row">
                        <div class="col-xs-12 notice waves-effect waves-block" data-search="${announcement.title + ' ' + announcement.summary}" data-id="${announcement.id}">
                            <h3>
                                ${announcement.title}
                            </h3>
                            <div class="tags-container">
                                ${announcementTags}
                                <small class="date">${moment.unix(announcement.date).format("Do MMMM YYYY")}</small>
                            </div>
                            <p class="${ (adminControls == "") ? "" : "has-admin-controls" } announcement-summary" >
                                ${announcement.summary}
                            </p>
                            ${adminControls}
                        </div>
                    </div>
                `);

            });

            if(Object.keys(announcementsObj).length === 0 && announcementsObj.constructor === Object) {
                $(".notices-containers").append(`
                    <div class="row">
                        <div class="col-sm-12">
                            <center>
                                <h4>${t('There are no results that match your specified filters.')}</h4>
                                <a class="btn btn-default btn-reset-filters waves-effect">${t('Reset Filters')}</a>
                            </center>
                        </div>
                    </div>
                `);

                $(".btn-reset-filters").on('click', function () {
                    getAnnouncements();
                });
            }

            const searchButtons = [];

            if (adminAccess) {
                searchButtons.push({
                    label: t('New Announcement'),
                    icon: 'add',
                    onClick: newEditAnnouncement
                });
            }

            searchbarHeader('.notices-header', searchButtons);

            if(searchText) {
                $(".search-bar").focus();
                $(".search-bar").putCursorAtEnd();
            }

            bindEditDeleteButtons();

            $(".announcements .notices-header .search-bar").on("keyup", function() {
                searchAnnouncements($(this).val().toLowerCase());
            });

            $(".notices-containers .notice").on('click', function () {
                loadAnnouncement($(this).data('id'))
                sammy.quietRoute(`/announcements/${$(this).data('id')}`);
            });

            function searchAnnouncements(text) {

                if(filterTags == "all" && filterMonth == "all") {

                    $(".notices-containers .notice").filter(function() {

                        $(this).parent().toggle(
                            $(this).data('search').toLowerCase().indexOf(text.toLowerCase()) > -1
                        )

                    });

                } else {

                    $("#month_all").click();
                    $("#tag_all").click();

                    filterUI(text);
                }



            }

        }

        function bindEditDeleteButtons() {
            $(".admin-notice-controls .btn-edit-notice").unbind("click");
            $(".admin-notice-controls .btn-delete-notice").unbind("click");

            $(".admin-notice-controls .btn-edit-notice").on('click', function () {
                newEditAnnouncement($(this).data('id'), announcementsObj)
                event.stopPropagation();
            });
            $(".admin-notice-controls .btn-delete-notice").on('click', function () {
                //console.log("delete", $(this).data('id'));

                var deleteID = $(this).data('id');
                event.stopPropagation();

                swalWithBootstrapButtons.fire({
                    title: t('Delete Announcement?'),
                    text: t('Are you sure you wish to delete the announcement') +"'" + announcementsObj[deleteID].title + "'? "+t("THIS CAN NOT BE UNDONE!"),
                    width: 500,
                    showCancelButton: true,
                    confirmButtonText: t('Yes, delete'),
                    cancelButtonText: t('Cancel')
                }).then((result) => {
                    if (result.value) {
                        deleteAnnouncement(deleteID, function() {
                            getAnnouncements();
                        });
                    }
                })

                return true;

            });
        }

        function deleteAnnouncement(id) {

            var req = $.ajax({
                url: `/api/announcements/delete?organisationId=${selectedOrgID}`,
                method: 'DELETE',
                contentType: 'application/json',
                data: JSON.stringify({
                    id: id
                }),
                success: function(data){
                    getAnnouncements();
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                    instantNotification(
                        // Message...
                        t('<b>FAILED TO DELETE ANNOUNCEMENT!</b><br><br>An error occurred while trying to remove this announcement ID. Please contact HQ for further details.'),
                        // Type...
                        'error',
                        // Timeout (1m)...
                        60000
                    );
                    getAnnouncements();
                }
            });


        }

        function loadAnnouncement(id) {
            if (!announcementsObj[id]) {
                sammy.quietRoute(`/announcements`);
                return instantNotification('Announcement not found', 'error');
            }

            // Hide elements not used if not admin..
            $(".filters-col").hide();
            $(".view-all-announcements").show();

            $(".announcements-col").removeClass("col-lg-9 col-md-9 col-md-push-3 col-lg-push-3");
            $(".announcements-col").addClass("col-lg-12 col-md-12");

            var announcementTags = '<div class="announcement-tags">';
            announcementsObj[id].tags.forEach(function (item) {
                announcementTags += `<span class="badge badge-grey">${item}</span>`
            });
            announcementTags += '</div>';

            $(".announcements .notices-header").html(`
                <h2>
                    ${announcementsObj[id].title}
                </h2>
                <div class="posted-on">Posted on: ${moment.unix(announcementsObj[id].date).format("Do MMMM YYYY")}</div>
                ${announcementTags}
            `);

            const notifRegions = _get(announcementsObj[id], 'regions', []);

            if(adminAccess && userHasAccessToAllNotifRegions(notifRegions)) {
                $(".announcement-admin-controls").html(`
                     <div class="admin-notice-controls">
                        <button type="button" data-id="${announcementsObj[id].id}" class="btn btn-xs btn-square bg-blue btn-edit-notice waves-effect">
                            <i class="material-icons">edit</i>
                        </button>
                        <button type="button" data-id="${announcementsObj[id].id}" class="btn btn-xs btn-square bg-red btn-delete-notice waves-effect">
                            <i class="material-icons">delete</i>
                        </button>
                    </div>
                `);
                bindEditDeleteButtons();
            }

            $(".notices-containers").html(`
                <iframe data-hj-allow-iframe="" id="announcementBodyContent"></iframe>
            `);

            let htmlContents = announcementsObj[id].html;
            try {
                htmlContents = atob(announcementsObj[id].html);
            } catch(_) {}

            const $iframe = $('#announcementBodyContent');
            $iframe.ready(function() {
                $iframe.contents().find("body").append(`${htmlContents}`);
                $iframe.contents().find("[data-f-id]").remove();

                // Remove the mailchimp footer (if it's an auto imported announcement)
                $iframe.contents().find("#templateFooter > table").remove();
            });


            $(".notices-containers").scrollTop(0);
            $(".view-all-announcements").on('click', function() {

                $(".announcements-col").removeClass("col-lg-12 col-md-12");
                $(".announcements-col").addClass("col-lg-9 col-md-9 col-md-push-3 col-lg-push-3");
                $(".filters-col").show();

                renderAnnouncements(announcementsObj);
                sammy.quietRoute(`/announcements`);
            });

        }
    }
}
