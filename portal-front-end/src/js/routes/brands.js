let adminBrandsTable = null;

async function sBrandsManagement() {
    const root = $('#admin-portal #brands');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');

    const query = getQueryStringsObject();
    let brands = [];
    let organisations = [];
    let orgModules = [];

    // Display Config
    let enableDisplayConfig = false;
    let displayUIOptions = [];
    let displayDataSources = [];

    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('ADMIN_FILTERS_BRANDS');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }

    // Use query params first, then localStorage, then defaults
    let selectedFilters = {
        search: query.search || savedFilters.search || '',
        organisations: query.organisations || savedFilters.organisations || 'all',
    };

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const [
            brandsRes,
            organisationsRes,
            modulesRes,
            displayUIOptionsRes,
            displayDataSourcesRes,
        ] = await Promise.all([
            listBrandsAPI(),
            listOrganisationsAPI(),
            listModulesAPI(),
            listAllDisplayUIOptionsAPI(),
            listAllDisplayDataSourcesAPI(),
        ]);

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!brandsRes[0] || !organisationsRes[0] || !modulesRes[0]) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        brands = brandsRes[1];
        organisations = organisationsRes[1];
        orgModules = modulesRes[1];

        enableDisplayConfig = displayUIOptionsRes[0] && displayDataSourcesRes[0];
        if (enableDisplayConfig) {
            displayUIOptions = displayUIOptionsRes[1];
            displayDataSources = displayDataSourcesRes[1];
        }

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => adminBrandsTable && adminBrandsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => adminBrandsTable && adminBrandsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => adminBrandsTable && adminBrandsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => adminBrandsTable && adminBrandsTable.button(3).trigger() },
        ];
        const buttons = [
            { label: 'Add Brand', icon: 'fa-add', onClick: () => brandDialog() },
            ...exportButtons,
        ];
        searchbarHeader(
            root.find('.search-bar-container'),
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'brands-export-buttons',
                },
            }
        );

        root.find('.search-bar').val(selectedFilters.search || '');

        root.find('.search-bar').on('input', function () {
            selectedFilters.search = $(this).val();
            updateHrefQuery({ search: selectedFilters.search });

            // Save search filter to localStorage
            try {
                localStorage.setItem('ADMIN_FILTERS_BRANDS', JSON.stringify(selectedFilters));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }

            renderBrandsTable();
        });

        renderPageFilters();
        renderBrandsTable();
    }

    function renderPageFilters() {
        filtersContainer.html('');

        return new PageFilters(
            filtersContainer,
            [
                {
                    id: 'organisations',
                    label: t('Organisations'),
                    options: [
                        { label: 'All', value: 'all', selected: selectedFilters['organisations'] === 'all' },
                        ...organisations
                            .map((option) => ({
                                label: option.name,
                                value: option.organisationId,
                                selected: selectedFilters['organisations'] === option.organisationId,
                            }))
                            .sort((a, b) => a.label.localeCompare(b.label)),
                    ],
                },
            ],
            (newFilters) => {
                selectedFilters = { ...selectedFilters, ...newFilters };

                Object.keys(selectedFilters).forEach((key) => {
                    updateHrefQuery({ [key]: selectedFilters[key] === 'all' ? null : selectedFilters[key] });
                });

                // Save filters to localStorage
                try {
                    localStorage.setItem('ADMIN_FILTERS_BRANDS', JSON.stringify(selectedFilters));
                } catch (error) {
                    console.error('Error saving filters to localStorage:', error);
                }

                renderBrandsTable();
            },
            true
        );
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>
            <div class="d-flex gap-1 my-4">
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                </div>
            `);
        });
    }

    async function renderBrandsTable() {
        let export_filename = 'Brands__' + (new Date().toISOString());
        adminBrandsTable?.clear();
        adminBrandsTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('Name')}</th>
                        <th>${t('Organisation')}</th>
                        <th>${t('Redirect Dashboard')}</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        brands
            .filter((brand) => {
                const searchRegex = new RegExp(selectedFilters.search, 'igm');
                const searchMatch =
                    brand.brandId.match(searchRegex) ||
                    brand.name.match(searchRegex) ||
                    brand.organisation?.name.match(searchRegex);

                if (!searchMatch) return false;

                const selectedOrg = organisations.find(
                    (org) => org.organisationId === selectedFilters['organisations']
                );
                const orgMatch = !selectedOrg || brand.organisation?.organisationId === selectedOrg.organisationId;
                return orgMatch;
            })
            .forEach((brand) => {
                tableBodyEl.append(`
                    <tr data-id="${brand.brandId}">
                        <td>${brand.name}</td>
                        <td>
                            ${
                                brand.organisation?.name ||
                                '<div class="text-muted" style="opacity: 0.5;">No organisation</div>'
                            }
                        </td>
                        <td>
                            ${brand.redirectDashboard
                                ? orgModules.find((m) => m.moduleId === brand.redirectDashboard)?.baseUIPath ||
                                '<div class="text-muted" style="opacity: 0.5;">None</div>'
                                : '<div class="text-muted" style="opacity: 0.5;">None</div>'}
                        </td>
                    </tr>
                `);
            });

        adminBrandsTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [[0, 'asc']],
            autoWidth: false,
            iDisplayLength: 50,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Brands',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf'
                },
            ],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const brandId = $(this).data('id');
                    const brand = brands.find((org) => org.brandId === brandId);
                    brandDialog(brand);
                });
            },
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));

        selectedFilters.search && adminBrandsTable.search(selectedFilters.search).draw();
    }

    function brandDialog(brand) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');

        $(div).html(`
            <div class="body">
                <ul class="nav nav-tabs ph-nav-tabs tab-nav-right" role="tablist" style="min-width: max-content">
                    <li role="presentation" class="active">
                        <a href="#brand-info" data-toggle="tab">${t('Brand Info')}</a>
                    </li>
                    <li role="presentation">
                        <a href="#modules-config" data-toggle="tab">${t('Modules Configuration')}</a>
                    </li>
                </ul>
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane in active" id="brand-info">
                        <div class="d-flex flex-column gap-3">
                            ${textField({ label: t('Name'), name: 'name', value: brand?.name })}
                            ${selectpicker({
                                label: t('Organisation'),
                                name: 'organisationId',
                                options: organisations.map((org) => ({
                                    value: org.organisationId,
                                    label: org.name,
                                })),
                                value: brand?.organisationId,
                            })}
                            ${selectpicker({
                                label: t('Redirect Dashboard'),
                                info: t('The page to redirect users to when they log in instead of the default dashboard.'),
                                name: 'redirectDashboard',
                                options: []
                            })}
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane in" id="modules-config">
                        <div class="module-groups d-flex flex-column gap-2 ph-scrollbar"></div>
                    </div>
                </div>
            </div>
        `);

        $(div).find('select').selectpicker();
        $(div).find('[data-toggle="tooltip"]').tooltip();
        updateRedirectDashboardOptions();
        $(div).find('select[name="organisationId"]').on('change', updateRedirectDashboardOptions);

        async function updateRedirectDashboardOptions() {
            const organisationId = $(div).find('select[name="organisationId"]').val();
            const organisation = organisations.find(org => org.organisationId === organisationId);
            const redirectDashboardSelect = $(div).find('select[name="redirectDashboard"]');

            redirectDashboardSelect.html(`<option value="dashboard">Default Dashboard</option>`);
            if (!organisation) return redirectDashboardSelect.selectpicker('refresh');

            organisation.modules.forEach((moduleId) => {
                const moduleData = orgModules.find(m => m.moduleId === moduleId);
                if (!moduleData) return;

                const selected = brand?.redirectDashboard === moduleData.moduleId ? 'selected' : '';
                redirectDashboardSelect.append(
                    `<option value="${moduleData.moduleId}" ${selected}>${moduleData.name}</option>`
                );
            });

            redirectDashboardSelect.selectpicker('refresh');
        }

        const modules = [];

        // Append display config items to modules array
        if (enableDisplayConfig) {
            displayDataSources.forEach((src) => {
                modules.push(({
                    group: 'Coaching Screen Data Sources',
                    moduleId: src.id,
                    name: src.name,
                }))
            })

            displayUIOptions.forEach((opt) => {
                modules.push(({
                    group: 'Coaching Screen UI Options',
                    moduleId: opt.id,
                    name: opt.name,
                }))
            })
        }

        let selectedModules = [];
        if (brand?.displayUIOptions && brand?.displayUIOptions?.length) {
            selectedModules = selectedModules.concat(brand.displayUIOptions);
        }
        if (brand?.displayDataSources && brand?.displayDataSources?.length) {
            selectedModules = selectedModules.concat(brand.displayDataSources);
        }

        // Re-use module picker but with coaching screen options and sources
        renderModuleGroupPickers($(div).find('.module-groups'), modules, selectedModules);

        async function preConfirm() {
            const doc = {
                name: $(div).find('input[name=name]').val(),
                organisationId: $(div).find('select[name="organisationId"]').val(),
                redirectDashboard: $(div).find('select[name="redirectDashboard"]').val(),
            };

            // Add selected config
            const selectedUIOptions = [];
            const selectedDataSources = [];
            $(div).find('input[name="modules"]').each(function() {
                const id = $(this).val();
                const isUIOption = displayUIOptions.some((opt) => opt.id === id);
                if (isUIOption) {
                    selectedUIOptions.push(id);
                    return;
                }
                const isDataSource = displayDataSources.some((src) => src.id === id);
                if (isDataSource) {
                    selectedDataSources.push(id);
                }
            });
            doc.displayUIOptions = selectedUIOptions;
            doc.displayDataSources = selectedDataSources;

            const response = !brand ? await createBrandAPI(doc) : await updateBrandAPI(brand.brandId, doc);

            if (!response[0]) Swal.showValidationMessage(response[1]);
            return response[1];
        }

        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 600,
                title: !brand ? t('Add Brand') : t('Edit Brand'),
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false,
                showCloseButton: true,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                preConfirm,
            })
            .then((result) => {
                if (result.value) return init();
            });
    }

    init();
}
