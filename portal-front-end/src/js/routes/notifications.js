const SwalNotifWithCustomButtons = Swal.mixin({
  customClass: {
      confirmButton: 'btn btn-primary waves-effect',
      cancelButton: 'btn btn-default waves-effect'
  },
  buttonsStyling: false,
})

let filterStartDate = moment().subtract(1, 'year').startOf('month');
let filterEndDate = moment().endOf('month');
let filterStartDateUnix = filterStartDate.unix();
let filterEndDateUnix = filterEndDate.unix();

let notificationMasterList = [];
let notifCategoriesByUser = [];

let minimumTotalList = 30;

const notifQueryLimit = 2000;
const notifRequestLimit = 5;
const notifApiBaseUri = '/api/club/notification/messages';

let categoryDebounce = null;

let lastRowObserver = new IntersectionObserver(entries => {
  const lastRow = entries[0];
  if (!lastRow.isIntersecting) return
  getNotificationsByLastKey();
  lastRowObserver.unobserve(lastRow.target);
});

const loadNotifications = (clubId) => {
  $(document).ready( function () {
    loadNotificationPage(clubId);
  });

  const loadNotificationPage = async (clubId) => {
    $("#notifications-list").empty();
    filterDaterangeChange(filterStartDate, filterEndDate);
  }

  // settings toggle menu
  $('.notif-menu-list-item').on('click', function() {
    if ($(this).hasClass('toggled')) {
      $(this).removeClass('toggled');
      $(this).siblings('.notif-sub-menu').collapse('hide');
    } else {
      $(this).addClass('toggled');
      $(this).siblings('.notif-sub-menu').collapse('show');
    }
  });

  // callback function if daterange change
  const filterDaterangeChange = async (start, end) => {
    const startDate = start.format('MMMM D, YYYY');
    const endDate = end.format('MMMM D, YYYY');

    filterStartDateUnix = start.unix();
    filterEndDateUnix = end.unix();

    $('#notif-filter-daterange span').html(`${startDate} - ${endDate}`);

    // if date range is change call api again.
    await getNotificationViewers(renderViewers);
    callDebounceQuery();
  }

  // daterange filter
  $('#notif-filter-daterange').daterangepicker({
    showDropdowns: true,
    opens: 'center',
    startDate: filterStartDate,
    endDate: filterEndDate,
    maxDate: moment(),
  }, filterDaterangeChange);

  // all, unread, opened filters
  $(document).on('click', '#notif-read-filters > button', (e) => {
    const { value } = $(e.currentTarget).data();
    $('#notif-read-filters > button').removeClass('btn-primary').addClass('btn-default');
    $(`#notif-read-filters > button[data-value=${value}]`).removeClass('btn-default').addClass('btn-primary');

    if (value === 'all') {
      renderNotificationList(notificationMasterList);
    } else if (value === 'unread') {
      const unreadList = notificationMasterList.filter(notif => {
        let viewers = 'viewers' in notif ? notif.viewers : [];
        return !viewers.includes(signedinUser);
      })
      renderNotificationList(unreadList);
    } else if (value === 'opened') {
      const openedList = notificationMasterList.filter(notif => {
        let viewers = 'viewers' in notif ? notif.viewers : [];
        return !!viewers.includes(signedinUser);
      })
      renderNotificationList(openedList);
    }

  });

  assembleCategory();
}

/** assemble category **/
const assembleCategory = async () => {
  let notifSource = notificationCategories;
  let selectedViewers = getFilteredViewers();

  /**
   * If ever viewers are present get the
   * categories by users
   **/
  if (selectedViewers.length) {
    const responseCategories = await fetchNotifCategoriesBySignedInUser(selectedViewers);
    notifSource = notificationCategories.filter(cat => (responseCategories.includes(cat.source)));
  }

  if (notifSource && notifSource.length < 1) {
    $('#notif-category-filter').empty().append(`
      <li style="padding-top: 5px;">
        <div style="padding: 5px 25px; text-align: center">No categories found</div>
      </li>
    `);
    return;
  }

  const checkAllCategory = `
    <li style="padding-top: 5px;">
      <div style="padding: 5px 25px;" class="styled-checkbox">
        <input
          id="cat-select-all"
          type="checkbox"
          class="filled-in chk-col-blue"
          data-category="cat-select-all"
          checked
        />
        <label style="font-size: 12px; display: block" for="cat-select-all">All</label>
      </div>
    </li>
  `;
  let checkboxCategoryHtml = notifSource.map(category => {
    return (`
      <li class="category-checkbox-listItem" style="padding-top: 5px;">
        <div style="padding: 5px 25px;" class="styled-checkbox">
          <input
            id="cat-${category.source}"
            type="checkbox"
            class="cat-checkboxes filled-in chk-col-blue"
            data-category="${category.source}"
            checked
          />
          <label style="font-size: 12px; display: block" for="cat-${category.source}">${category.name}</label>
        </div>
      </li>
    `);
  });

  /** no need to put the all checkbox if equal to one or nothing **/
  if (notifSource.length > 1) {
    /** put the select all checkbox at first **/
    checkboxCategoryHtml.unshift(checkAllCategory);
  }

  $('#notif-category-filter').empty().append(checkboxCategoryHtml.join(''));

  /** toggle select all **/
  $('#cat-select-all').on('click', function() {
    const isAll = $(this).prop('checked');
    $('#notif-category-filter input:checkbox').prop('checked', isAll);
    callDebounceQuery();
  });

  /** begin to request if any category is click **/
  $('.category-checkbox-listItem input:checkbox').on('click', function() {
    let isAllChecked = $('.cat-checkboxes:checked').length === $('.cat-checkboxes:checkbox').length
    $('#cat-select-all').prop('checked', isAllChecked);
    callDebounceQuery();
  });
}

/** debounce query **/
const callDebounceQuery = () => {
  clearTimeout(categoryDebounce)
  categoryDebounce = null
  categoryDebounce = setTimeout(() => {
    resetLastKeys();
    notificationMasterList = [];
    fetchNotificationsByFilter();
  }, 650);
}

const resetLastKeys = () => {
  clubsNotifLastKeyQuery = null;
  usersNotifLastKeyQuery = null;
  regionNotifLastKeyQuery = null;
  globalNotifLastKeyQuery = null;
}

/** This is a handy function just to reset the timestamp **/
const resetDateRangeUnix = async () => {
  filterStartDate = moment().subtract(2, 'months').startOf('month');
  filterEndDate = moment().endOf('month');
  filterStartDateUnix = filterStartDate.unix();
  filterEndDateUnix = filterEndDate.unix();
}

/** main notification list **/ 
const renderNotificationList = async (data=[]) => {
  if (data && Array.isArray(data) && data.length) {
    $('#notifications-list').empty();
    data.forEach((notif, index) => {
      $('#notifications-list').append(getNotificationRow(index, notif));
    });
  } else {
    $('#notifications-list').empty().append(`
      <div style="text-align: center; padding-top: 1.5rem;">
        There are no notifications for now
      </div>
    `);
  }
}

// get the checked category for filter query
const getFilteredCategory = () => {
  let filterdCategory = [];
  let categoryCheck = $('#notif-category-filter').find('input:checkbox');
  categoryCheck.each(function() {
    if ($(this).data('category') !== 'cat-select-all' && $(this).prop('checked')) {
      filterdCategory.push($(this).data('category'));
    }
  });

  return filterdCategory;
}

// convert object to query string
const notifConvertToQueryString = (target, lastKey=null) => {
  let filterCategories = getFilteredCategory();
  let filterViewers = getFilteredViewers();

  let queryString = {
    target,
    limit: notifQueryLimit,
    startDate: filterStartDateUnix,
    endDate: filterEndDateUnix,
  };

  if (filterCategories.length) queryString.category = encodeURIComponent(JSON.stringify(filterCategories));
  if (filterViewers.length) queryString.viewers = encodeURIComponent(JSON.stringify(filterViewers));
  if (lastKey) queryString.LastEvaluatedKey = lastKey;

  let stringUrlQuery = stringifyQueryStringsObject(queryString);

  return stringUrlQuery;
}

// the paginated infinite scrolling of notification
const getNotificationsByLastKey = async () => {
  let entries = [];
  let promises = [];

  // club api query
  if (clubsNotifLastKeyQuery) {
    const clubQueryString = notifConvertToQueryString('clubs', JSON.stringify(clubsNotifLastKeyQuery));
    promises.push({
      target: 'clubs',
      promise: uniApiConCall(`${notifApiBaseUri}/${selectedID}${clubQueryString}`)
    });
  }

  // user api query
  if (usersNotifLastKeyQuery) {
    const userQueryString = notifConvertToQueryString('users', JSON.stringify(usersNotifLastKeyQuery));
    promises.push({
      target: 'users',
      promise: uniApiConCall(`${notifApiBaseUri}/${selectedID}${userQueryString}`)
    });
  }

  // region api query
  if (regionNotifLastKeyQuery) {
    const regionQueryString = notifConvertToQueryString('region', JSON.stringify(regionNotifLastKeyQuery));
    promises.push({
      target: 'region',
      promise: uniApiConCall(`${notifApiBaseUri}/${selectedID}${regionQueryString}`)
    });
  }

  // global api query
  if (globalNotifLastKeyQuery) {
    const globalQueryString = notifConvertToQueryString('global', JSON.stringify(globalNotifLastKeyQuery));
    promises.push({
      target: 'global',
      promise: uniApiConCall(`${notifApiBaseUri}/${selectedID}${globalQueryString}`)
    });
  }

  // get all the notification responses with proper tracker of index
  if (promises.length) {
    loadingOverlay('#notifications-list');
    const responses = await Promise.all(promises.map(apiReq => (apiReq.promise)));
    $('#notifications-list').LoadingOverlay("hide");
    promises.forEach((tracker, index) => {
      const result = responses[index];
      if (result && 'status' in result && result.status == 'success') {
        const { data } = result
        const items = 'Items' in data ? data.Items : [];
        const freshLastKey = 'LastEvaluatedKey' in data ? data.LastEvaluatedKey : null;
        entries = [...entries, ...items];

        switch (tracker.target) {
          case 'clubs':
            clubsNotifLastKeyQuery = freshLastKey
            break;

          case 'users':
            usersNotifLastKeyQuery = freshLastKey
            break;

          case 'region':
            regionNotifLastKeyQuery = freshLastKey
            break;

          case 'global':
            globalNotifLastKeyQuery = freshLastKey
            break;

          default:
            break;
        }
      }
    });

    notificationMasterList = [...notificationMasterList, ...entries];

    if (entries.length) {
      let lastKeyUpdatedList = filterListUnique(notificationMasterList);
      let sortedEntries = [...lastKeyUpdatedList].sort((a, b) => {
        return new Date(b.created) - new Date(a.created);
      });

      $('#notifications-list').empty();
      sortedEntries.forEach((notif, index) => {
        $('#notifications-list').append(getNotificationRow(index, notif))
      });

      lastRowObserver.observe(document.querySelector('.main-notif-listItem:last-child'));
    }
  }
}

const filterListUnique = (updatedList) => {
  const uniqueIds = [];
  return updatedList.filter(element => {
    const isDuplicate = uniqueIds.includes(element.id);

    if (!isDuplicate) {
      uniqueIds.push(element.id);
      return true;
    }
    return false;
  });
}

/** seprated query **/
const fetchNotificationsByFilter = async () => {
  let listClubItems = [];
  let listRegionItems = [];
  let listUsersItems = [];
  let listGlobalItems = [];

  /** club notif api recursive **/
  const recursiveNotifApi = (lastProcessedKey=null, index = 0) => {
    return new Promise( async (resolve, reject) => {
      try {
        clubsNotifLastKeyQuery = lastProcessedKey;
        let recurseResult = await uniApiConCall(`${notifApiBaseUri}/${selectedID}${notifConvertToQueryString('clubs', JSON.stringify(clubsNotifLastKeyQuery))}`);
        if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
          let subData = recurseResult.data;
          let subItems = 'Items' in subData ? subData.Items : [];
          clubsNotifLastKeyQuery = 'LastEvaluatedKey' in subData ? subData.LastEvaluatedKey : null;

          listClubItems = [...listClubItems, ...subItems];
          let totalList = (
            listClubItems.length
            + listRegionItems.length
            + listUsersItems.length
            + listGlobalItems.length
          );

          /** keep on query **/
          if (clubsNotifLastKeyQuery !== null && totalList < minimumTotalList && index < notifRequestLimit) {
            await recursiveNotifApi(clubsNotifLastKeyQuery, index + 1);
          }
          resolve(listClubItems);
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /** region notif api recursive **/
  const recursiveRegionNotifApi = (lastProcessedKey=null, index = 0) => {
    return new Promise(async (resolve, reject) => {
      try {
        /** if viewers filter is selected don't query the region **/
        if (getFilteredViewers().length) {
          resolve([]);
          return;
        }
        regionNotifLastKeyQuery = lastProcessedKey;
        let recurseResult = await uniApiConCall(`${notifApiBaseUri}/${selectedID}${notifConvertToQueryString('region', JSON.stringify(regionNotifLastKeyQuery))}`);
        if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
          let subData = recurseResult.data;
          let subItems = 'Items' in subData ? subData.Items : [];
          regionNotifLastKeyQuery = 'LastEvaluatedKey' in subData ? subData.LastEvaluatedKey : null;

          listRegionItems = [...listRegionItems, ...subItems];
          let totalList = (
            listClubItems.length
            + listRegionItems.length
            + listUsersItems.length
            + listGlobalItems.length
          );

          /** keep on query **/
          if (regionNotifLastKeyQuery !== null && totalList < minimumTotalList && index < notifRequestLimit) {
            await recursiveRegionNotifApi(regionNotifLastKeyQuery, index + 1);
          }
          resolve(listRegionItems);
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /** users notif api recursive **/
  const recursiveUserNotifApi = (lastProcessedKey=null, index = 0) => {
    return new Promise( async (resolve, reject) => {
      try {
        if (getFilteredViewers().length) {
          resolve([]);
          return;
        }
        usersNotifLastKeyQuery = lastProcessedKey;
        let queryUrl = notifConvertToQueryString('users', JSON.stringify(usersNotifLastKeyQuery));
        let apiUrl = `${notifApiBaseUri}/${selectedID}${queryUrl}`;
        let recurseResult = await uniApiConCall(apiUrl);
        if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
          let subData = recurseResult.data;
          let subItems = 'Items' in subData ? subData.Items : [];
          usersNotifLastKeyQuery = 'LastEvaluatedKey' in subData ? subData.LastEvaluatedKey : null;

          listUsersItems = [...listUsersItems, ...subItems];
          let totalList = (
            listClubItems.length
            + listRegionItems.length
            + listUsersItems.length
            + listGlobalItems.length
          );

          /** keep on query **/
          if (usersNotifLastKeyQuery !== null && totalList < minimumTotalList && index < notifRequestLimit) {
            await recursiveUserNotifApi(usersNotifLastKeyQuery, index + 1);
          }
          resolve(listUsersItems);
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /** users notif api recursive **/
  const recursiveGlobalNotifApi = (lastProcessedKey=null, index = 0) => {
    return new Promise(async (resolve, reject) => {
      try {
        /** if viewers filter is selected don't query the global **/
        if (getFilteredViewers().length) {
          resolve([]);
          return;
        }
        globalNotifLastKeyQuery = lastProcessedKey;
        let queryUrl = notifConvertToQueryString('global', JSON.stringify(globalNotifLastKeyQuery));
        let apiUrl = `${notifApiBaseUri}/${selectedID}${queryUrl}`;
        let recurseResult = await uniApiConCall(apiUrl);
        if (recurseResult && 'status' in recurseResult && recurseResult.status == 'success') {
          let subData = recurseResult.data;
          let subItems = 'Items' in subData ? subData.Items : [];
          globalNotifLastKeyQuery = 'LastEvaluatedKey' in subData ? subData.LastEvaluatedKey : null;

          listGlobalItems = [...listGlobalItems, ...subItems];
          let totalList = (
            listClubItems.length
            + listRegionItems.length
            + listUsersItems.length
            + listGlobalItems.length
          );

          /** keep on query **/
          if (globalNotifLastKeyQuery !== null && totalList < minimumTotalList && index < notifRequestLimit) {
            await recursiveGlobalNotifApi(globalNotifLastKeyQuery, index + 1);
          }
          resolve(listGlobalItems);
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  loadingOverlay('#notifications-list');
  $("#notifications-list").empty();
  try {
    const responses = await Promise.all([
      recursiveNotifApi(clubsNotifLastKeyQuery),
      recursiveRegionNotifApi(),
      recursiveUserNotifApi(),
      recursiveGlobalNotifApi()
    ]);
    loadingOverlay('#notifications-list', false);

    const newNotifications = responses.reduce((acc, response) => {
      if (response && response.length) return [...acc, ...response];
      return acc;
    }, []);

    const sortedNotifs = newNotifications
      .sort((a, b) => new Date(b.created) - new Date(a.created))

    notificationMasterList = filterListUnique(sortedNotifs);

    await renderNotificationList(notificationMasterList);
    /** if data is present, provide observer **/
    if (notificationMasterList.length) {
      const lastRow = document.querySelector('.main-notif-listItem:last-child');
      if (lastRow !== null) lastRowObserver.observe(lastRow);
    }
  } catch (err) {
    console.error(err);
    $('#notifications-list').html('Something went wrong. Please try again later.')
    loadingOverlay('#notifications-list', false);
  }
}