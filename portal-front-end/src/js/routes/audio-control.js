function sAudioControl() {
    addContainerClasses('#audio-control');

    if (enableModalBackdrop) enableModalBackdrop($('#audio-control'));
    $('.ac-skeleton-loader').show();

    $('.iframe-placeholder iframe').ready(function () {
        setTimeout(function () {
            // $('.iframe-placeholder').LoadingOverlay("hide");
            $('.ac-skeleton-loader').fadeOut();
            $('main-container').addClass('audio-control-ui');
        }, 800);
    });
}

function sAudioControlURI() {
    sammy.quietRoute(
        '/audio-control-ui/#' +
            encodeURIComponent(
                $('#audioControlContainer').contents().get(0).location.href.replace(document.location.origin, '')
            )
    );
}

function reloadAudioControliframe() {
    $('#audioControlContainer').attr('src', '/audio-control/');
}
