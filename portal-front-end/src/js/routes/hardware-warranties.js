function sHardwareWarranties() {

    getDevices();

    function getDevices() {

        var req = $.ajax({
            url: '/api/admin/hardware/all-devices',
            method: 'GET',
            contentType: 'application/json',
            success: function(data){
                if((data === undefined) ? false : data !== false) {

                    // Add devices to UI...
                    if(data.length > 0) {
                        renderDevices(data);
                    } else {
                        noDevices();
                    }

                } else {
                    noDevices();
                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
                noDevices();
            }
        });

        function noDevices() {
            $("#hardware-tracking-container").empty();
            $("#hardware-tracking-container").html(`<center><h3>No devices found in the system at this time.</h3></center>`);
        }

        function renderDevices(data) {

            devicesObj = {};

            $("#hardware-tracking-container").empty();
            $("#hardware-tracking-container").html(`

                 <table id="hardware-tracking-table" class="table">
                    <thead>
                        <tr>
                            <th>Club ID</th>
                            <th>Balena ID (UUID)</th>
                            <th>Pairing ID</th>
                            <th>Device Serial</th>
                            <th>Device Version</th>
                            <th>Device Make</th>
                            <th>Last Seen</th>
                            <th>IP</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            `);

            data.forEach(function(device) {

                devicesObj[device.uuid] = device;

                var deviceItem = `
                    <tr class="device" data-id="${ device.uuid }">
                        <td>${ device.clubID }</td>
                        <td class="copy-buttons">
                            <code>${ device.uuid }</code>
                            <a data-toggle="tooltip" title="Open In Balena" href="https://dashboard.balena-cloud.com/devices/${ device.uuid }" target="_blank">
                                <i class="fa fa-external-link"></i>
                            </div>
                        </td>
                        <td>${ device.pairingID }</td>
                        <td>${ device.deviceSerial }</td>
                        <td>${ device.deviceVersion }</td>
                        <td>${ device.deviceMake }</td>
                        <td data-sort="${ device.updated }">${ moment.unix(device.updated / 1000).format("YYYY/MM/DD HH:mm A") }</td>
                        <td>${ device.ip }</td>
                    </tr>
                `;
                $("#hardware-tracking-table tbody").append(deviceItem);
            });

            addClipboardCopyButtons('.copy-buttons');

            let export_filename = 'hardware-tracking__' + new Date().toISOString();
            let dtConfig = {
                responsive: true,
                iDisplayLength: 250,
                dom: '<"toolbar">rtip',
                order: [[6, 'asc']],
                language: {
                    paginate: {
                        previous: t('Previous'),
                        next: t('Next'),
                    },
                    infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
                    info: t('Showing') + ' _START_ ' + t('to') + ' _END_ ' + t('of') + ' _TOTAL_ ' + t('entries'),
                    emptyTable: t('No data available in table'),
                    zeroRecords: t('No matching records found'),
                    infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
                },
                buttons: [
                    {
                        text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                        extend: 'copy',
                        className: 'btn btn-sm btn-default',
                    },
                    {
                        text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                        extend: 'print',
                        title: 'Hardware Tracking',
                        className: 'btn btn-sm btn-default',
                    },
                    {
                        text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                        extend: 'csv',
                        className: 'btn btn-sm btn-default',
                        title: export_filename,
                        extension: '.csv'
                    },
                    {
                        text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                        extend: 'pdf',
                        className: 'btn btn-sm btn-default',
                        title: export_filename,
                        extension: '.pdf',
                        orientation: 'landscape',
                        customize: function (doc) {
                            doc.defaultStyle.fontSize = 9;
                        }
                    },
                ],
                bAutoWidth: false,
            };

            let table = $('#hardware-tracking-table').DataTable(dtConfig); 

            const exportButtons = [
                { label: 'COPY', icon: 'fa-copy', onClick: () => table && table.button(0).trigger() },
                { label: 'PRINT', icon: 'fa-print', onClick: () => table && table.button(1).trigger() },
                { label: 'CSV', icon: 'fa-file-csv', onClick: () => table && table.button(2).trigger() },
                { label: 'PDF', icon: 'fa-file-pdf', onClick: () => table && table.button(3).trigger() },
            ];
            searchbarHeader(
                '.search-nav', 
                exportButtons,
                {
                    usePhTheme: true,
                    expandableButtons: {
                        startIndex: 0,
                        count: exportButtons.length,
                        label: t('Export'),
                        icon: 'fa-ellipsis-v',
                        id: 'hardware-export-buttons',
                    },
                }
            )

            $('.search-nav .search-bar').on('keyup', function () {
                table.search(this.value).draw();
            });

            //$("#bulk-sms-history-table tbody .message").click(function () {
            //    messageDetailsModel($(this).data("id"));
            //});

        }

    }
}