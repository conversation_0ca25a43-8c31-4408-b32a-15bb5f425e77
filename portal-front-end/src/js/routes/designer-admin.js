let designerCustomPagesTable = null;

async function sDesignerAdmin() {
    const root = $('#designer-admin #custom-pages');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');
    let customPages = [];

    const filters = {
        types: [
            { value: 'wysiwyg', label: t('WYSIWYG') },
            { value: 'pdf', label: t('PDF') },
            { value: 'iframe', label: t('Iframe') },
        ],
        regions: [],
        facilities: [],
    };

    const query = getQueryStringsObject();

    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('DESIGNER_FILTER_CUSTOM_PAGES');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }

    // Use query params first, then localStorage, then defaults
    let selectedFilters = {
        regions: query.regions || savedFilters.regions || 'all',
        facilities: query.facilities || savedFilters.facilities || 'all',
        search: query.search || savedFilters.search || '',
        types: query.types || savedFilters.types || 'all',
    };

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const customPagesRes = await listDesignerCustomPagesAPI(selectedOrgID);

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!customPagesRes[0]) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        customPages = customPagesRes[1];
        const orgFacilities = ClubsObj.filter((club) => club.organisationId === selectedOrgID);

        filters.facilities = orgFacilities
            ?.map((facility) => ({
                value: facility.id,
                label: facility.name,
            }))
            .sort((a, b) => a.label.localeCompare(b.label));

        filters.regions = orgFacilities
            ?.reduce((acc, facility) => {
                const { country } = facility?.localisation || {};
                if (!country?.full_name || !country?.iso_code || acc.some((r) => r.value === country.iso_code))
                    return acc;
                acc.push({ value: country.iso_code, label: country.full_name });
                return acc;
            }, [])
            .sort((a, b) => a.label.localeCompare(b.label));

        const exportButtons = [
            {
                label: 'COPY',
                icon: 'fa-copy',
                onClick: () => designerCustomPagesTable?.button(0).trigger(),
            },
            {
                label: 'PRINT',
                icon: 'fa-print',
                onClick: () => designerCustomPagesTable?.button(1).trigger(),
            },
            {
                label: 'CSV',
                icon: 'fa-file-csv',
                onClick: () => designerCustomPagesTable?.button(2).trigger(),
            },
            {
                label: 'PDF',
                icon: 'fa-file-pdf',
                onClick: () => designerCustomPagesTable?.button(3).trigger(),
            },
        ];

        const buttons = [
            { label: 'Add Page', icon: 'fa-file-circle-plus', onClick: () => pageDialog() },
            ...exportButtons,
        ];

        searchbarHeader(root.find('.search-bar-container'), buttons, {
            usePhTheme: true,
            expandableButtons: {
                startIndex: 1,
                count: exportButtons.length,
                label: t('Export'),
                icon: 'fa-ellipsis-v',
                id: 'page-export-buttons',
            },
        });

        root.find('.search-bar').val(selectedFilters.search);

        root.find('.search-bar').on('input', function () {
            selectedFilters.search = $(this).val();
            updateHrefQuery({ search: selectedFilters.search });

            // Save search filter to localStorage
            try {
                localStorage.setItem('DESIGNER_FILTER_CUSTOM_PAGES', JSON.stringify(selectedFilters));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }

            renderCustomPagesTable();
        });

        renderPageFilters();
        renderCustomPagesTable();
    }

    function renderPageFilters() {
        filtersContainer.html('');

        new PageFilters(
            filtersContainer,
            Object.keys(filters).map((key) => ({
                id: key,
                label: key.charAt(0).toUpperCase() + key.slice(1),
                options: [
                    { label: 'All', value: 'all', selected: selectedFilters[key] === 'all' },
                    ...filters[key]
                        .map((option) => ({
                            label: option.label,
                            value: option.value,
                            selected: selectedFilters[key] === option.value,
                        }))
                        .sort((a, b) => a.label.localeCompare(b.label)),
                ],
            })),
            (newFilters) => {
                selectedFilters = { ...selectedFilters, ...newFilters };

                Object.keys(selectedFilters).forEach((key) => {
                    updateHrefQuery({ [key]: selectedFilters[key] === 'all' ? null : selectedFilters[key] });
                });

                // Save filters to localStorage
                try {
                    localStorage.setItem('DESIGNER_FILTER_CUSTOM_PAGES', JSON.stringify(selectedFilters));
                } catch (error) {
                    console.error('Error saving filters to localStorage:', error);
                }

                renderPageFilters();
                renderCustomPagesTable();
            },
            true
        );
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>
            <div class="d-flex gap-1 my-4">
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 5%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 5%;"></div>
                </div>
            `);
        });
    }

    async function renderCustomPagesTable() {
        let export_filename = 'Marketing_Pages__' + new Date().toISOString();
        designerCustomPagesTable?.clear();
        designerCustomPagesTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th></th>
                        <th>${t('Name')}</th>
                        <th>${t('Type')}</th>
                        <th>${t('Regions')}</th>
                        <th>${t('Facilities')}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        customPages
            .filter((page) => {
                if (selectedFilters.types !== 'all' && page.type !== selectedFilters.types) return false;
                if (selectedFilters.regions !== 'all' && !page.regions?.includes(selectedFilters.regions)) return false;

                if (selectedFilters.facilities !== 'all' && !page.facilities?.includes(selectedFilters.facilities))
                    return false;

                const searchRegex = new RegExp(selectedFilters.search || '', 'igm');
                return page.name.match(searchRegex);
            })
            .forEach((page) => {
                const regions = page.regions?.map((code) => getCountryNameFromCode(code)) || [];

                const facilities =
                    page.facilities?.map((facilityId) => {
                        const clubData = ClubsObj.find((club) => club.id === facilityId);
                        return clubData?.name || facilityId;
                    }) || [];

                tableBodyEl.append(`
                    <tr data-id="${page.id}">
                        <td>
                            <button class="btn btn-icon drag-handle">
                                <i class="fa fa-grip-lines m-0" style="font-size: 12px; margin: 0px;"></i>
                            </button>
                        </td>
                        <td>${page.name}</td>
                        <td>${page.type}</td>
                        <td>${regions.join(', ')}</td>
                        <td>${facilities.join(', ')}</td>
                        <td>
                            <div class="btn ph-btn ph-btn-sm btn-outline btn-danger d-flex gap-1 btn-delete-page row-actions">
                                <a class="remove-page text-danger" data-id="${page.id}" href="#" title="Remove Page">
                                    <i class="fa fa-times"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `);
            });

        designerCustomPagesTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            autoWidth: false,
            iDisplayLength: 50,
            ordering: false,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default',
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Pages',
                    className: 'btn btn-sm btn-default',
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv',
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf',
                },
            ],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const pageId = $(this).data('id');
                    const page = customPages.find((p) => p.id === pageId);
                    page && pageDialog(page);
                });

                $(row)
                    .find('.btn-delete-page')
                    .on('click', async function (e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const pageId = $(this).find('a').data('id');
                        const page = customPages.find((p) => p.id === pageId);
                        page && deletePageDialog(page);
                    });
            },
            columnDefs: [{ targets: [0, 5], width: '0px' }],
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));
        selectedFilters.search && designerCustomPagesTable.search(selectedFilters.search).draw();

        Sortable.create(tableContainer.find('tbody')[0], {
            handle: '.drag-handle',
            onEnd: function (evt) {
                const { oldIndex, newIndex } = evt;
                const pages = customPages.splice(oldIndex, 1);
                customPages.splice(newIndex, 0, ...pages);
                customPages.forEach((page, index) => upsertDesignerCustomPageAPI({ ...page, order: index }));
            },
        });
    }

    function deletePageDialog(page) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');

        $(div).html(`
            <div class="body">
                ${t('You are about to remove this page from Performance Hub.')}
                <br />
                ${t('This action cannot be undone.')}
            </div>
        `);

        swalWithBootstrapButtons
            .fire({
                title: t('Are you sure you want to remove this page?'),
                html: div,
                showCancelButton: true,
                confirmButtonText: t('Delete'),
                cancelButtonText: t('Cancel'),
                preConfirm: async () => {
                    const response = await upsertDesignerCustomPageAPI({ ...page, deleted: 1 });
                    if (!response[0]) return Swal.showValidationMessage(response[1]);
                    return true;
                },
            })
            .then(async (result) => {
                if (!result.value) return;
                instantNotification(t('Page deleted successfully'), 'success');
                customPages = customPages.filter((p) => p.id !== page.id);
                renderCustomPagesTable();
            });
    }

    function pageDialog(page) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2 designer-custom-page-dialog');
        let attachedFiles = [];

        $(div).html(`
            <div class="body">
                <div class="d-flex flex-column gap-1">
                    ${textField({ label: t('Name'), name: 'name', value: page?.name })}
                    ${textField({ label: t('URL Slug'), name: 'slug', value: page?.slug })}
                    <div class="d-flex gap-3">
                        ${selectpicker({
                            label: t('Type'),
                            name: 'type',
                            options: filters.types,
                            value: page?.type || 'wysiwyg',
                        })}
                        ${selectpicker({
                            label: t('Regions'),
                            name: 'regions',
                            options: filters.regions,
                            value: page?.regions || [],
                            multiple: true,
                        })}
                        ${selectpicker({
                            label: t('Facilities'),
                            name: 'facilities',
                            options: filters.facilities,
                            value: page?.facilities || [],
                            multiple: true,
                        })}
                    </div>
                    <div class="iframe-container d-flex flex-column gap-1">
                        ${textField({ label: t('Iframe URL'), name: 'iframeUrl', value: page?.iframeUrl || '' })}
                        <iframe src="${page?.iframeUrl || ''}"></iframe>
                    </div>
                    <div class="pdf-container">
                        <div class="upload-status-list"></div>
                        <div class="pdf-preview" style="display: none;">
                            <iframe></iframe>
                        </div>
                        <div class="custom-dz" style="height: 467px"></div>
                        <form id="upload-form" enctype="multipart/form-data" class="hidden">
                            <input type="file" name="file" accept=".pdf" />
                            <button type="submit">Upload</button>
                        </form>
                    </div>
                    <div class="wysiwyg-container">
                        <div class="wysiwyg-editor" style="height: 394px"></div>
                    </div>
                </div>
            </div>
        `);

        const textEditor = $(div).find('.wysiwyg-editor');

        textEditor.trumbowyg({
            svgPath: '/assets/images/trumbowyg.svg',
            autogrow: true,
            defaultLinkTarget: '_blank',
            btnsDef: {
                customUpload: {
                    fn: () => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';

                        $(input).on('change', async (e) => {
                            const file = e.target.files[0];
                            if (!file) return;

                            uploadFile(
                                file,
                                () => Swal.showValidationMessage(t('Failed to upload file')),
                                undefined,
                                (data) => {
                                    const uploadUrl = `/api/admin/secure-upload/private/${data.s3Key}`;
                                    const currentHtml = textEditor.trumbowyg('html');
                                    const newHtml = currentHtml + `<img src="${uploadUrl}" alt="uploaded image">`;
                                    textEditor.trumbowyg('html', newHtml);
                                }
                            );
                        });

                        $(input).trigger('click');
                    },
                    title: 'Custom Upload',
                    ico: 'upload',
                },
                image: { dropdown: ['insertImage', 'customUpload'], ico: 'insertImage' },
            },
            btns: [
                ['viewHTML'],
                ['historyUndo', 'historyRedo'],
                ['fontfamily'],
                ['fontsize'],
                ['lineheight'],
                ['formatting'],
                ['removeformat'],
                ['strong', 'em', 'del'],
                ['superscript', 'subscript'],
                ['foreColor', 'backColor'],
                ['link', 'image'],
                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                ['unorderedList', 'orderedList'],
                ['horizontalRule'],
                'table',
                'customUpload',
                ['fullscreen'],
            ],
            plugins: { resizimg: { minSize: 64, step: 16 } },
        });

        $(div).find('.selectpicker[name=type]').selectpicker();
        displayFormByType();
        handleIframeUrlInput();
        if (page?.wysiwygContent) $(textEditor).trumbowyg('html', page.wysiwygContent);

        if (page?.type === 'pdf') {
            displayPdfPreview({ url: page.pdfUrl });
            renderCurrentPDFFile();
            $(div).find('.custom-dz').hide();
            $(div).find('.pdf-preview').show();
            if (page.pdfS3Data) attachedFiles.push(page.pdfS3Data);
        }

        let slugChanged = false;

        $(div)
            .find('input[name=slug]')
            .on('input', () => {
                slugChanged = true;
                const slug = $(div).find('input[name=slug]').val();
                $(div).find('input[name=slug]').val(slugify(slug));
            });

        $(div)
            .find('[name=name]')
            .on('input', () => {
                if (page?.slug || slugChanged) return;
                const slug = slugify($(div).find('[name=name]').val());
                $(div).find('input[name=slug]').val(slug);
            });

        $(div)
            .find('.custom-dz')
            .on('click', function () {
                $(div).find('input[name=file]').trigger('click');
            });

        $(div)
            .find('.custom-dz')
            .on('dragenter', (e) => e.preventDefault());
        $(div)
            .find('.custom-dz')
            .on('dragover', (e) => e.preventDefault());

        $(div)
            .find('.custom-dz')
            .on('drop', (e) => {
                e.preventDefault();
                const files = e.originalEvent.dataTransfer.files;
                handleFileDrop(files);
            });

        $(div)
            .find('input[name=file]')
            .on('change', (e) => {
                handleFileDrop(e.target.files);
            });

        $(div)
            .find('.selectpicker[name=regions]')
            .selectpicker({ noneSelectedText: t('Global') });

        $(div)
            .find('.selectpicker[name=facilities]')
            .selectpicker({ noneSelectedText: t('All Facilities') });

        $(div).find('select[name=type]').on('change', displayFormByType);
        $(div).find('input[name=iframeUrl]').on('input', handleIframeUrlInput);

        function handleIframeUrlInput() {
            const url = $(div).find('input[name=iframeUrl]').val();
            const validUrlPattern = /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(:\d+)?(\/[\w- .\/?%&=]*)?$/;
            if (!validUrlPattern.test(url)) {
                $(div).find('.iframe-container iframe').attr('srcdoc', `
                    <html>
                        <body style='font-family: Roboto, Arial, Tahoma, sans-serif; height: 100vh; display: flex; justify-content: center; align-items: center;'>
                            <h1 style='text-align: center; color: #ddd; opacity: 0.5; width: 380px;'>
                                ${t('Please enter a valid URL to preview the content.')}
                        </body>
                    </html>
                `);
            } else {
                $(div).find('.iframe-container iframe').removeAttr('srcdoc');
                $(div).find('.iframe-container iframe').attr('src', url);
            }
        }

        function handleFileDrop(filelist) {
            filelist.forEach((file) => {
                renderFile(file);
                uploadFile(
                    file,
                    () => {
                        $(div)
                            .find(`.upload-status-item[data-name="${file.name}"]`)
                            .find('.status')
                            .html(`<em class="text-danger">${t('FAILED')}</em>`);
                    },
                    () => {
                        const xhr = new window.XMLHttpRequest();

                        xhr.upload.onprogress = function (evt) {
                            if (!evt.lengthComputable) return;
                            const percentComplete = Math.floor((evt.loaded / evt.total) * 100);

                            $(div)
                                .find(`.upload-status-item[data-name="${file.name}"]`)
                                .find('.status')
                                .text(`${percentComplete}%`);
                        };

                        return xhr;
                    },
                    displayPdfPreview
                );
            });
        }

        function renderCurrentPDFFile() {
            if (!page?.pdfS3Data) return;

            const item = $(`
                <div class="upload-status-item" data-name="${page.pdfS3Data.name}">
                    <span class="d-flex gap-1 align-center">
                        <i class="fa fa-file"></i>
                        <span>${page.pdfS3Data.name}</span>
                        <small>${humanFileSize(page.pdfS3Data.size)}</small>
                    </span>
                    <button class="btn btn-icon remove-file">
                        <i class="fa fa-close text-danger"></i>
                    </button>
                </div>
            `);

            $(item)
                .find('.remove-file')
                .on('click', () => removePdfFile(item));

            $(div).find('.upload-status-list').html(item);
        }

        function renderFile(file) {
            const reader = new FileReader();

            reader.onload = () => {
                const item = $(`
                    <div class="upload-status-item" data-name="${file.name}">
                        <span class="d-flex gap-1 align-center">
                            <i class="fa fa-file"></i>
                            <span>${file.name}</span>
                            <small>${humanFileSize(file.size)}</small>
                        </span>
                        <span class="d-flex gap-1 align-center">
                            <span class="status" style="padding-top: 6px; padding-bottom: 6px;">Uploading...</span>
                            <button class="btn btn-icon remove-file">
                                <i class="fa fa-close text-danger"></i>
                            </button>
                        </span>
                    </div>
                `);

                $(item)
                    .find('.remove-file')
                    .on('click', () => removePdfFile(item));

                $(div).find('.upload-status-list').html(item);
            };

            reader.readAsDataURL(file);
        }

        function removePdfFile(item) {
            item?.remove();
            $(div).find('.custom-dz').show();
            $(div).find('.pdf-preview').hide();
            attachedFiles = [];
        }

        function displayPdfPreview({ s3Key, url }) {
            $(div).find('.pdf-preview').empty();

            const pdfUrl = url || `/api/admin/secure-upload/private/${s3Key}`;
            PDFObject.embed(pdfUrl, $(div).find('.pdf-preview')[0], {
                fallbackLink: `<iframe data-hj-allow-iframe='' class='fallback-agreement-iframe' src='[url]#zoom=50'></iframe>`,
                pdfOpenParams: {
                    pagemode: 'thumbs',
                    navpanes: 0,
                    toolbar: 0,
                    statusbar: 0,
                },
            });
        }

        async function uploadFile(file, handleFail, handleProgress, handleSuccess) {
            $(div).find('.pdf-preview').html(`
                <iframe srcdoc="
                    <html>
                        <body style='font-family: Roboto, Arial, Tahoma, sans-serif; height: 100vh; display: flex; justify-content: center; align-items: center;'>
                            <h1 style='color: #ddd; opacity: 0.5'>${t('Loading')}...</h1>
                        </body>
                    </html>
                "></iframe>    
            `);
            $(div).find('.pdf-preview').show();
            $(div).find('.custom-dz').hide();

            const tokenRes = await getSecureUploadTokenAPI(file.name, false, selectedOrgID);
            if (!tokenRes[0]) return handleFail();

            const { url, fields, key } = tokenRes[1];
            const formData = new FormData();

            Object.entries(fields).forEach(([k, value]) => formData.append(k, value));
            formData.append('file', file);

            const s3Resp = await uploadToS3API(url, formData, handleProgress);
            if (!s3Resp[0]) return handleFail();

            const fileType = key.split('/')[0];
            const postRes = await saveSecureUploadAPI({
                key,
                name: file.name,
                size: file.size,
                type: fileType,
                public: false,
                organisationId: selectedOrgID,
                source: {
                    module: 'designer',
                    type: 'custom-page',
                    id: page?.id || 'new',
                },
            });

            if (!postRes[0]) return handleFail();
            attachedFiles.push(postRes[1]);
            handleSuccess(postRes[1]);
        }

        async function preConfirm() {
            const doc = {
                id: page?.id,
                slug: $(div).find('input[name=slug]').val(),
                name: $(div).find('input[name=name]').val(),
                facilities: $(div).find('select[name="facilities"]').val(),
                regions: $(div).find('select[name="regions"]').val(),
                type: $(div).find('select[name=type]').val(),
                iframeUrl: $(div).find('input[name=iframeUrl]').val(),
                wysiwygContent: $(div).find('.wysiwyg-editor').trumbowyg('html'),
                organisationId: selectedOrgID,
                order: page?.order || customPages.length,
            };

            if (doc.type === 'pdf' && attachedFiles.length) {
                doc.pdfUrl = `/api/admin/secure-upload/private/${attachedFiles[0]?.s3Key || ''}`;
                doc.pdfS3Data = attachedFiles[0];
            } else {
                doc.pdfUrl = page?.pdfUrl || '';
                doc.pdfS3Data = page?.pdfS3Data || null;
            }

            const response = await upsertDesignerCustomPageAPI(doc);

            if (!response[0]) Swal.showValidationMessage(response[1]);
            return response[1];
        }

        async function displayFormByType() {
            const type = $(div).find('select[name=type]').val();

            $(div).find('.iframe-container').hide();
            $(div).find('.pdf-container').hide();
            $(div).find('.wysiwyg-container').hide();

            if (type === 'iframe') {
                $(div).find('.iframe-container').show();
            } else if (type === 'pdf') {
                $(div).find('.pdf-container').show();
            } else if (type === 'wysiwyg') {
                $(div).find('.wysiwyg-container').show();
            }
        }

        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 800,
                title: !page ? t('Add Page') : t('Edit Page'),
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false,
                showCloseButton: true,
                allowEscapeKey: false,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                preConfirm,
            })
            .then((result) => {
                if (result.value) return init();
            });
    }

    init();
}
