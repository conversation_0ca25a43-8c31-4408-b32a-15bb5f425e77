
var mapObj = null;
var markerObj = null;
var daySelectors = new Array(
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday"
);
// There's four 'blocks' of hours we can set...
var hourBlocks = 4;
var placeData = {};
let countryAutocomplete = null;
let countryAutocompleteLsr = null;
let stateAutocomplete = null;
let stateAutocompleteLsr = null;
let cityAutocomplete = null;
let cityAutocompleteLsr = null;
let manuallyTriggeredPublishToDirectories = false;
const unitaryCountries = ['SG', 'MC', 'VA', 'SM', 'LI', 'AD', 'MT', 'NR', 'HK'];

function generateTimezones() {
    var timeZones = moment.tz.names();
    var offsetTmz=[];

    for(var i in timeZones) {
        try {
            offsetTmz.push(
                {
                   label: `${timeZones[i]}    (GMT ${moment.tz(timeZones[i]).format('Z')})`,
                   value: timeZones[i]
                }
            )

        } catch(e) {}
    }

    $("#club_timezone").autocomplete({
        source: offsetTmz,
        delay: 200,
        minLength: 1
    });

    $("#club_timezone").change(function() {
        $("#club_timezone").data('auto', "false");
    });
}

async function sAdministerClub(placeData) {
 
     const fetchUserGeoLocation = async () => {
        try {
            const response = await fetch('/api/geo/ip-current');
            if (!response.ok) throw new Error('Failed to fetch geolocation data');

            const data = await response.json();

            // Extract required fields
            return {
                ip: data.ip || '0.0.0.0',
                country_code: data.country_code || 'US',
                latitude: data.latitude || 40.7128,  // Default: New York, NY
                longitude: data.longitude || -74.0060 // Default: New York, NY
            };
        } catch (error) {
            console.error('Error fetching geolocation:', error.message);

            // Default to US (New York) if the API fails
            return {
                ip: '0.0.0.0',
                country_code: 'US',
                latitude: 40.7128,
                longitude: -74.0060
            };
        }
    };

     // Bind to the toggle of the 'isopen' part of the UI...
    $("#isOpen").click(function() { toggleClubOpen(); });

    $('.btn-cancel').click(function() {
        sammy.refresh();
    });

    generateTimezones();

    // Function to load marker for user to adjust lat/lng and club address...
    initClubMap(fetchUserGeoLocation);

    // Render manual address autocompletes
    renderCountryAutocomplete();
    renderStateAndCityAutocomplete(placeData.country);

    // Toggle switching between auto and manual address
    $('#manual-address-toggle').on('change', (e) => {
        const manual = $(e.currentTarget).is(':checked');
        toggleAddressSwitch(manual ? 'manual' : 'auto');
    });

    $( "form input, form textarea").not('#tracking-name').not('#tracking-code').not('#tracking-status').keyup(function() {
        $("#save-club-footer").addClass('active');
    });
    $( "form input, form textarea, form select").not('.image-container input').not('#tracking-name').not('#tracking-code').not('#tracking-status').not('[name=line-chat-enabled]').change(function() {
        $("#save-club-footer").addClass('active');
    });

    // Add hours to special hours
    $(document).on('click', '.input-special-hours', (e) => {
        const element = $(e.currentTarget);
        addHourBlock(element, '.special-hour-container');
    });

    // Add hours to club opening hours
    $(document).on('click', '.input-club-hours', (e) => {
        const element = $(e.currentTarget);
        addHourBlock(element, '.club-hour');
    });

    // Add hours to club default special hours
    $(document).on('click', '#default-club-blocks .input-default-hours', (e) => {
        const element = $(e.currentTarget);
        addHourBlock(element, '#default-club-blocks');
    });

    // Add special hour
    $(document).on('click', '.add-special-hour', () => {
        specialHourDialog({}, (res) => {
            if (!!res) {
                const { name, date } = res;
                const id = $('#special-club-hours .special-hour').length;
                const defaultBlocks = [];

                $('#default-club-blocks .block-list li').map((_, block) => {
                    const { opens, closes } = $(block).data();
                    defaultBlocks.push({ start: opens, end: closes });
                });

                $('#special-club-hours > div:not(.special-hour-container)')
                    .before(getSpecialClubHour({ id, name, date, blocks: defaultBlocks, tags: [] }));

                // Activate footer
                $("#save-club-footer").addClass('active');
            }
        });
    });

    // Duplicate special hour
    $(document).on('click', '.duplicate-special-hour', (e) => {
        const specialHour = $(e.currentTarget).closest('.special-hour');

        specialHourDialog(specialHour.data(), (res) => {
            if (!!res) {
                const { name, date } = res;
                const id = $('#special-club-hours .special-hour').length;

                // Get special hour data
                const data = { id, name, date, blocks: [], tags: [] };

                specialHour.find('.tags span').map((_, tag) => {
                    const tagsToRemove = ['Added automatically'];
                    const tagValue = $(tag).data('tag');

                    if (!tagsToRemove.includes(tagValue)) {
                        data.tags.push(tagValue)
                    }
                });

                specialHour.find('ul.block-list li').map((_, block) => {
                    data.blocks.push({ start: $(block).data('opens'), end: $(block).data('closes') });
                });

                $('#special-club-hours > div:not(.special-hour-container)').before(getSpecialClubHour(data));

                // Activate footer
                $("#save-club-footer").addClass('active');
            }
        });
    });

    // Update special hour
    $(document).on('click', '.update-special-hour', (e) => {
        const specialHour = $(e.currentTarget).closest('.special-hour');

        specialHourDialog(specialHour.data(), (res) => {
            if (!!res) {
                const { name, date } = res;

                // Get special hour data
                const data = { id: specialHour.data('id'), name, date, blocks: [], tags: [] };

                specialHour.find('.tags span').map((_, tag) => data.tags.push($(tag).data('tag')));
                specialHour.find('ul.block-list li').map((_, block) => {
                    data.blocks.push({ start: $(block).data('opens'), end: $(block).data('closes') });
                });

                if (data.tags.includes('Added automatically')) {
                    data.oldName = specialHour.data('name');
                    data.oldDate = specialHour.data('date');
                }

                specialHour.closest('.special-hour-container').replaceWith(getSpecialClubHour(data));

                // Activate footer
                $("#save-club-footer").addClass('active');
            }
        });
    });

    // Delete special hour
    $(document).on('click', '.delete-special-hour', (e) => {
        const element = $(e.currentTarget);
        const container = element.closest('.special-hour-container');

        specialHourDeleteDialog(`Delete ${element.data('name')}`, (res) => {
            let wasAddedAutomatically = false;

            container.find('.tags span').map((_, tag) => {
                if ($(tag).data('tag').toLowerCase() === 'added automatically') {
                    wasAddedAutomatically = true;
                }
            });

            if (wasAddedAutomatically) {
                container.find('.tags').append(getHourTag('Deleted'));
                container.addClass('deleted');
            } else if (res) {
                container.remove();
            }

            // Activate footer
            $("#save-club-footer").addClass('active');
        });
    });

    // Update hour block
    $(document).on('click', 'ul.block-list li > div > span', (e) => {
        const element = $(e.currentTarget).closest('li');
        const blockList = element.closest('.block-list');
        const { name } = $(element).data();

        // get blocks array
        let blocks = [];
        element.closest('.block-list').find('li').map((index, block) => {
            const { opens, closes } = $(block).data();

            if (index !== element.index()) {
                blocks = [...blocks, { opens, closes }]
            }
        });

        const title = t('Update hours for') + `: ${name}`;
        hoursSelectorDialog(element, title, blocks, (response) => {
            if (response) {
                blockList.html('');
                
                blocks.push({ opens: response.opens, closes: response.closes });
                blocks.sort((a, b) => {
                    return moment(a.opens, 'h:mma').isBefore(moment(b.opens, 'h:mma')) ? -1 : 1;
                });

                blocks.forEach(block => {
                    blockList.append(getHourBlock({ name, start: block.opens, end: block.closes }));                    
                });

                // Activate footer
                $("#save-club-footer").addClass('active');
            }
        });
    });

    // Delete hour block
    $(document).on('click', 'ul.block-list li > div > i', (e) => {
        const element = $(e.currentTarget).closest('li');
        const { opens, closes } = $(element).data();

        const title = `${t('Delete block')} ${opens} - ${closes}?`;
        specialHourDeleteDialog(title, (res) => {
            if (res) {
                // if tags exist, add "closed" tag
                const remainingBlocks = element.siblings('li').length;
                const hasTags = element.closest('ul.block-list').siblings('.tags').length > 0;

                if (remainingBlocks < 1 && hasTags) {
                    element.closest('ul.block-list').siblings('.tags').append(getHourTag('Closed'));
                }
                
                // remove hours block
                element.remove();

                // Activate footer
                $("#save-club-footer").addClass('active');
            }
        });
    });

    // Add payment method
    $(document).on('click', '.select-payment-methods', selectPaymentMethodsDialog);

    // Remove payment method
    $(document).on('click', '.payment-method', () => {
        selectPaymentMethodsDialog();
    });

    $(document).on('input', '#facebook_url', (e) => {
        formatSocialUrls($(e.currentTarget), 'facebook');
    });

    $(document).on('input', '#instagram_url', (e) => {
        formatSocialUrls($(e.currentTarget), 'instagram');
    });

    // Prevent 'enter' from submitting the form (users must click save button)...
    $(document).ready(function() {
      $("form input").keydown(function(event){
        if(event.keyCode == 13) {
          event.preventDefault();
          return false;
        }
      });
    });

    function selectPaymentMethodsDialog() {
        const allPaymentMethods = [
            "AFTERPAY",
            "AMERICANEXPRESS",
            "GOOGLEPAY", // this is actually ANDROIDPAY in yext
            "APPLEPAY",
            "BITCOIN",
            "CASH",
            "CHECK",
            "CONTACTLESSPAYME",
            "DINERSCLUB",
            "DIRECTDEBIT",
            "DISCOVER",
            "FINANCING",
            "INVOICE",
            "JCB",
            "KLARNA",
            "MASTERCARD",
            "PAYPAL",
            "TRAVELERSCHECK",
            "VISA"
        ].filter((pm) => {
            // JCB is only available in Japan
            const isJapanese = placeData.country?.iso_code === 'JP';
            return isJapanese || pm !== 'JCB';
        });

        const currentPaymentMethods = [];
        
        $('.payment-methods .payment-method').map((_, pm) => {
            const { name } = $(pm).data();
            currentPaymentMethods.push(name.toLowerCase());
        });

        // Build html content for popup...
        const div = document.createElement("div");
        $(div).addClass('ph-dialog-v2');
        $(div).html(`<div class="body"><div class="row"></div></div>`);

        allPaymentMethods.forEach(renderPaymentMethodCheckbox);

        function renderPaymentMethodCheckbox(pm, index) {
            if (index % 10 === 0) {
                $(div).find('.row').append('<div class="col-md-6"></div>');
            }

            const container = $(div).find('.row .col-md-6').last();

            const checkedProp = currentPaymentMethods.includes(pm.toLowerCase()) ? 'checked' : '';
            const image = `
                <img
                    src="/assets/images/payment_methods/${pm?.toLowerCase()}.png"
                    style="height: 24px; width: 24px; margin-right: 10px; object-fit: contain;"
                />
            `;

            const label = getPaymentMethodDisplayName(pm);

            container.append(`
                <div class="form-check styled-checkbox" style="margin-top:5px;">
                    <input
                        value="${pm}"
                        ${checkedProp}
                        type="checkbox"
                        class="filled-in chk-col-blue"
                        name="payment-methods"
                    >
                    <label>${image}${label}</label>
                </div>
            `);
        }

        $(div).find('input[type="checkbox"] + label').on('click', (e) => {
            const element = $(e.currentTarget).siblings('input');
            const checked = element.is(':checked');
            const value = element.val().toUpperCase();

            $(div).find(`input[type="checkbox"][value="${value}"]`).prop('checked', !checked);
        });

        let selectedValues = [];
        swalWithBootstrapButtons.fire({
            title: t('Select payment methods'),
            html: div,
            width: 500,
            showCloseButton: true,
            //true means show cancel button, with default values
            showCancelButton: true,
            confirmButtonText: t('Save'),
            cancelButtonText: t('Cancel'),
            preConfirm: () => {
                $(div).find('input[type="checkbox"]:checked').map((_, el) => {
                    selectedValues.push($(el).parent().text().trim());
                });

                if (selectedValues.length < 1) {
                    return Swal.showValidationMessage('Select at least one payment method');
                }

                return true;
            }
        }).then(response => {
            if (!response.value) return;
            $('.payment-methods .payment-method').remove();
            selectedValues.forEach(pm => {
                $('.select-payment-methods').before(getPaymentMethodContainer(pm));
            });

            $("#save-club-footer").addClass('active');
        });
    }
}

function formatSocialUrls(element, type) {
    const expressions = {
        facebook: /facebook\.com\/(.*?)($|\/)/,
        instagram: /instagram\.com\/(.*?)($|\/)/
    }

    const value = element.val();
    const expression = expressions[type];

    if (expression && expression.test(value)) {
        const [_, name] = expression.exec(value);
        if (name !== "") {
            element.val(name);
        }
    }
}

// Function to update the latitude/longitude based on where the user drags the marker on the map...
function updateLatLng(lat,lng) {
    $("#lat_lng").val(lat + ", " + lng);

    // Store data values...
    $("#lat_lng").data('lat', lat);
    $("#lat_lng").data('lng', lng);

}

function panMapMarker(lat,lng) {
    const panToLatLng = new google.maps.LatLng(lat, lng);

    if (mapObj) {
        mapObj.panTo(panToLatLng);
        mapObj.setZoom(15);
    }

    markerObj && markerObj.setPosition(panToLatLng);
    updateLatLng(lat,lng);
}

function toggleClubOpen() {
    if ($('#isOpen').is(':checked')) {
        $('.club-hours-tab').show();
        $('.club-hours-tab-content').show();
        $(".opening-soon-container").hide();

        if (sammy.getParam('action') === 'review-holidays') {
            $('#holiday-hours-tab').tab('show');

            setTimeout(() => {
                $('.club-hours-block')[0].scrollIntoView({ block: 'center', behaviour: 'smooth' });
                window.scrollBy(0, 390);
            }, 500);
        }
    } else {
        $('.club-hours-tab').hide();
        $('.club-hours-tab-content').hide();
        $(".opening-soon-container").show();
    }
}

function hoursSelectorDialog(element, title, blocks, callback) {
    let opens = ($(element).data('opens') === undefined) ? "" : $(element).data('opens');
    let closes = ($(element).data('opens') === undefined) ? "" : $(element).data('closes');

    // Build html content for popup...
    const div = document.createElement("div");
    $(div).addClass('ph-dialog-v2');
    $(div).html(
        '<div id="hours-selector-form" class="d-flex justify-center body">' +
            '<div class="d-flex align-center">' +
                '<b>'+t('Opens')+':</b>' +
                '<div class="form-group">' +
                    '<div class="form-line">' +
                        `<input data-hj-allow class="form-control input-hours-block" id="input_block_opens" value="${opens}"></input>` +
                    '</div>' +
                '</div>' +
            '</div>' +
            '<div class="d-flex align-center">' +
                '<b>'+t('Closes')+':</b>' +
                '<div class="form-group">' +
                    '<div class="form-line">' +
                        `<input data-hj-allow class="form-control input-hours-block" id="input_block_closes" value="${closes}"></input>` +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>'
    );

    // Bind open/close autocomplete...
    $(div).find("#input_block_opens").timepicker();
    $(div).find("#input_block_closes").timepicker();

    swalWithBootstrapButtons.fire({
        title,
        html: div,
        width: 500,
        showCloseButton: true,
        showCancelButton: true,
        confirmButtonText: t('Save/Update'),
        cancelButtonText: t('Cancel'),
        preConfirm: () => {
            let error = null;
            opens = $("#input_block_opens").val();
            closes = $("#input_block_closes").val();
            
            if (opens === '' || closes === '') {
                error = t('All fields are required.');
            } else if (moment(closes, 'h:mma').isSameOrBefore(moment(opens, 'h:mma'))) {
                error = t('Closing hour must be after the opening hour');
            } else {
                // Check for overlapping ranges
                const blockA = { opens: moment(opens, 'h:mma'), closes: moment(closes, 'h:mma') };
                
                for (let i = 0; i < blocks.length; i++) {
                    const blockB = { opens: moment(blocks[i].opens, 'h:mma'), closes: moment(blocks[i].closes, 'h:mma') };
                    if (blockB.opens.isSameOrAfter(blockA.opens) && blockB.opens.isSameOrBefore(blockA.closes)) {
                        // B starts after A starts but before A finishes.
                        error = `${t('Block is overlapping with block')} ${i + 1}`;
                        break;
                    } else if (blockB.opens.isSameOrBefore(blockA.opens) && blockB.closes.isSameOrAfter(blockA.opens)) {
                        // B starts before A starts and finishes after A starts.
                        error = `${t('Block is overlapping with block')} ${i + 1}`;
                        break;
                    }
                }
            }

            if (error) {
                Swal.showValidationMessage(error);
            }
        }

    }).then(response => {
        if (response.value) {
            callback({ opens, closes });
        } else {
            callback(false);
        }
    });
}

function renderStateAndCityAutocomplete(country) {
    const unitaryCountries = ['SG', 'MC', 'VA', 'SM', 'LI', 'AD', 'MT', 'NR', 'HK'];
    const stateEl = $('#manual_address_state');
    const cityEl = $('#manual_address_city');

    const countryIso = country.short_name || country.iso_code;
    const countryName = country.long_name || country.full_name;

    if (unitaryCountries.includes(countryIso)) {
        stateEl.attr('disabled', true).attr('data-unitary', true).val(countryName);
        cityEl.attr('disabled', true).attr('data-unitary', true).val(countryName);
        return;
    }

    if (stateEl.attr('data-unitary')) {
        stateEl.attr('disabled', false).removeAttr('data-unitary').val('');
    }
    
    if (cityEl.attr('data-unitary')) {
        cityEl.attr('disabled', false).removeAttr('data-unitary').val('');
    }

    renderStateAutocomplete(countryIso);
    renderCityAutocomplete(countryIso);
}

function renderCountryAutocomplete() {
    const input = $('#manual_address_country');
    const components = ['country'];
    
    const cb = (component) => {
        if (placeData.country?.iso_code === component.short_name) return;

        placeData.country = {
            iso_code: component.short_name,
            full_name: component.long_name,
        };

        $('#manual_address_state').val('');
        $('#manual_address_city').val('');
        renderStateAndCityAutocomplete(component);
    };

    countryAutocompleteLsr && google.maps.event.removeListener(countryAutocompleteLsr);
    countryAutocomplete && google.maps.event.clearInstanceListeners(countryAutocomplete);

    countryAutocomplete = new google.maps.places.Autocomplete(input[0], { types: ['country'] });
    countryAutocompleteLsr = google.maps.event.addListener(
        countryAutocomplete,
        'place_changed',
        onManualAddressAutoComplete(countryAutocomplete, input, components, cb)
    );
}

function renderStateAutocomplete(countryISO) {
    const components = ['administrative_area_level_1', 'administrative_area_level_2'];
    
    const options = {
        types: ['(regions)'],
    };

    if (countryISO) {
        options.componentRestrictions = { country: countryISO };
    }
    
    const cb = (component) => {
        placeData.province = {
            abbreviation: component.short_name,
            full_name: component.long_name,
        };
    };

    stateAutocompleteLsr && google.maps.event.removeListener(stateAutocompleteLsr);
    stateAutocomplete && google.maps.event.clearInstanceListeners(stateAutocomplete);
    const currentState = $('#manual_address_state').val();

    // reset element cause the autocomplete keeps suggesting states from the last country
    $('#manual_address_state').replaceWith(`
        <input
            data-hj-allow
            type="text"
            id="manual_address_state"
            class="form-control required"
            placeholder="${t('State')}"
            autocomplete="off"
            value="${currentState}"
        />
    `);

    const input = $('#manual_address_state');
    stateAutocomplete = new google.maps.places.Autocomplete(input[0], options);
    stateAutocompleteLsr = google.maps.event.addListener(
        stateAutocomplete,
        'place_changed',
        onManualAddressAutoComplete(stateAutocomplete, input, components, cb)
    );
}

function renderCityAutocomplete(countryISO) {
    const components = ['locality', 'administrative_area_level_3'];
    
    const options = {
        types: ['(cities)'],
    };

    if (countryISO) {
        options.componentRestrictions = { country: countryISO };
    }
    
    const cb = (component) => {
        placeData.city = {
            short_name: component.short_name,
            full_name: component.long_name,
        };
    };

    cityAutocompleteLsr && google.maps.event.removeListener(cityAutocompleteLsr);
    cityAutocomplete && google.maps.event.clearInstanceListeners(cityAutocomplete);
    const currentCity = $('#manual_address_city').val();

    // reset element cause the autocomplete keeps suggesting cities from the last country
    $('#manual_address_city').replaceWith(`
        <input
            data-hj-allow
            type="text"
            id="manual_address_city"
            class="form-control required"
            placeholder="${t('City')}"
            autocomplete="off"
            value="${currentCity}"
        />
    `);

    const input = $('#manual_address_city');
    cityAutocomplete = new google.maps.places.Autocomplete(input[0], options);
    cityAutocompleteLsr = google.maps.event.addListener(
        cityAutocomplete,
        'place_changed',
        onManualAddressAutoComplete(cityAutocomplete, input, components, cb)
    );
}

const onManualAddressAutoComplete = (autocomplete, input, components, cb) => () => {
    const place = autocomplete.getPlace();

    if (!place || !place.address_components) {
        input.closest('.form-line').addClass('error');
        return;
    }

    input.closest('.form-line').removeClass('error');

    let addressComponent = null;
    let count = 0;

    while (!addressComponent && count < components.length) {
        addressComponent = place.address_components.find(c => c.types.includes(components[count]));
        count += 1;
    }

    input.val(addressComponent ? addressComponent.long_name : null);
    cb(addressComponent);
}

function initClubMap(defaultLocationData) {

    // By default, use the user's location from IP-based geolocation
    let clubLatLng = new google.maps.LatLng(defaultLocationData.latitude, defaultLocationData.longitude);
    let mapOptions = {
        zoom: 8,
        center: clubLatLng
    };

    var map = new google.maps.Map(
        document.getElementById('clubLocationMap'), mapOptions
    );

    // Position the marker where the map is centered...
    var marker = new google.maps.Marker({
        position: clubLatLng,
        map: map,
        draggable: true,
        title: t("Club Location")
    });

    // Try HTML5 geolocation (for more accurate searches), with fallback
    let defaultBounds;
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function (position) {
                var pos = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                console.log("Using precise HTML5 geolocation:", pos);

                defaultBounds = new google.maps.LatLngBounds(
                    new google.maps.LatLng(pos.lat, pos.lng)
                );

                // Update the map center and marker position
                map.setCenter(pos);
                marker.setPosition(pos);
            },
            function (error) {
                console.warn("Geolocation access denied or blocked! Using IP-based fallback.", error);

                // Fallback to IP-based geolocation
                defaultBounds = new google.maps.LatLngBounds(
                    new google.maps.LatLng(defaultLocationData.latitude, defaultLocationData.longitude)
                );
            }
        );
    } else {
        console.warn("Browser doesn't support Geolocation. Using IP-based fallback.");

        // Fallback to IP-based geolocation
        defaultBounds = new google.maps.LatLngBounds(
            new google.maps.LatLng(defaultLocationData.latitude, defaultLocationData.longitude)
        );
    }

    var input = document.getElementById('display_address');
    var options = {
        types: ['geocode']
    };

    if (typeof(defaultBounds) == 'undefined') {
        defaultBounds = new google.maps.LatLngBounds(
            new google.maps.LatLng(defaultLocationData.latitude, defaultLocationData.longitude)
        );
        options.bounds = defaultBounds;
    } else {
        options.bounds = defaultBounds;
    }

    let autocomplete = new google.maps.places.Autocomplete(input, options);
    autocomplete.bindTo('bounds', map);

    // Bind to the autocomplete of the address - fire off an event in the UI to update when the address is updated...
    google.maps.event.addListener(autocomplete, 'place_changed', function () {
        panMapMarker(
            autocomplete.getPlace().geometry.location.lat(),
            autocomplete.getPlace().geometry.location.lng()
        );

        // Reset timezone info - We'll pull the timezone from the new location by default unless the user manually overrides it.
        $("#club_timezone").val('');
        $("#club_timezone").data('auto', "false");
    });

    marker.addListener('dragend', function (e) {
        updateLatLng(
            e.latLng.lat(), //.toFixed(3),
            e.latLng.lng() //.toFixed(3)
        );

        // Show save button
        $("#save-club-footer").addClass('active');
    });

    function formatGeoFields() {

        // Response object...
        var geoFields = {
            country: {
                full_name: '',
                iso_code: ''
            },
            province: {
                full_name: '',
                abbreviation: ''
            },
            city: {
                full_name: '',
                short_name: ''
            },
            google_place_id: null,
            formatted_address: null
        };

        // Get the place details from the autocomplete object.
        var place = autocomplete.getPlace();

        geoFields.google_place_id = place.place_id;
        geoFields.formatted_address = place.formatted_address;

        // Get each component of the address from the place details
        // and fill the corresponding field on the form.
        for (var i = 0; i < place.address_components.length; i++) {
            var addressType = place.address_components[i].types[0];

            // This is the State/Province type..
            if (addressType == 'administrative_area_level_1') {
                geoFields.province.full_name = place.address_components[i].long_name;
                geoFields.province.abbreviation = place.address_components[i].short_name;
            }

            if (addressType == 'administrative_area_level_2') {
                geoFields.city.full_name = place.address_components[i].long_name;
                geoFields.city.short_name = place.address_components[i].short_name;
            }

            if (addressType == 'country') {
                geoFields.country.full_name = place.address_components[i].long_name;
                geoFields.country.iso_code = place.address_components[i].short_name;
            }
        }

        return geoFields;
    }

    google.maps.event.addListener(autocomplete, 'place_changed', function () {
        var geoFields = formatGeoFields();
        placeData = geoFields;
    });

    // Lastly share access to both the marker & the map from outside of this function
    mapObj = map;
    markerObj = marker;

}

function renderSecondaryEmails() {
    var tmp = $("#secondaryEmails").val();

    $(".secondaryEmails-placeholder").empty();
    $(".secondaryEmails-placeholder").html('<input data-hj-allow type="text" id="secondaryEmails" class="form-control date" placeholder="">');
    $("#secondaryEmails").val(tmp);

    $("#secondaryEmails").multiple_emails({
       position: 'top', // Display the added emails above the input
       theme: 'bootstrap', // Bootstrap is the default theme
       checkDupEmail: true // Should check for duplicate emails added
    });

    $("#secondaryEmails").change(function() {
        $("#save-club-footer").addClass('active');
    });

    // Prevent 'enter' from submitting the form (users must click save button)...
    $(".secondaryEmails-placeholder input").keydown(function(event){
        if(event.keyCode == 13) {
            event.preventDefault();
            return false;
        }
    });

}

function toggleAddressSwitch(type) {
    const manualInputs = $('.manual-address-inputs');
    const autoInputs = $('.auto-address-inputs');

    if (type === 'auto') {
        $('#manual-address-toggle').prop('checked', false);
        manualInputs.find('input').removeAttr('required');
        autoInputs.find('input.required').attr('required', 'true');
        manualInputs.collapse('hide');
        autoInputs.collapse('show');
    } else {
        $('#manual-address-toggle').prop('checked', true);
        autoInputs.find('input').removeAttr('required');
        manualInputs.find('input.required').attr('required', 'true');
        manualInputs.collapse('show');
        autoInputs.collapse('hide');
    }
}

function renderClubYextNotifications({ listings }) {
    const syncingErrors = listings ? listings.filter(listing => listing.statusDetails) : [];

    if (syncingErrors.length > 0) {
        $('.yext-notifications').html(`
            <div class="alert alert-red toggle-yext-notifs">
                <div class="d-flex justify-between">
                    <span>
                        <i class="material-icons" style="font-size: 18px; transform: translateY(3px)">warning</i>
                        <b>${t('This Club has issues syncing with publishers which require reviewing.')}</b>
                    </span>
                    <span>
                        <i class="material-icons chevron" style="transform: translateY(3px)">expand_more</i>
                    </span>
                </div>

                <div class="collapse" style="padding-top: 10px;">
                    <ul>
                        ${syncingErrors.map(listing => {
                            return (`
                                <li>
                                    <div style="padding-bottom: 10px;">
                                        <b>${listing.publisherId}</b>
                                        ${listing.statusDetails.map(details => {
                                            return `<p style="padding-bottom: 5px;">${details.message}</p>`;
                                        }).join('')}
                                    </div>
                                </li>
                            `)
                        }).join('')}
                    </ul>
                </div>
            </div>
        `);

        $('.yext-notifications .toggle-yext-notifs').on('click', (e) => {
            e.preventDefault();
            const collapsed = !$('.yext-notifications .collapse').hasClass('in');
            $('.yext-notifications .collapse').collapse(collapsed ? 'show' : 'hide');
            $('.yext-notifications .chevron').html(collapsed ? 'expand_less' : 'expand_more');
        });

        return true;
    } else {
        $('.yext-notifications').html('');
        return false;
    }
}

function renderShopFrontNotification(clubData) {
    const shopFrontPhoto = clubData.photos.find(photo => photo.type === 'shop-front');
    const queryStrings = shopFrontPhoto?.url?.split('?')[1] || '';
    const updated = shopFrontPhoto && !queryStrings.includes('pano') && !queryStrings.includes('new');
    
    const hasLandingPageEnabled = clubData.landingPageOptions.some(
        option => !clubData.disabledLandingPages.includes(option.id)
    );

    if (updated || !hasLandingPageEnabled) return $('.shop-front-notifications').html('');

    $('.shop-front-notifications').html(`
        <div class="custom-alert" style="" role="alert">
            <div class="custom-alert__content">
                <div class="custom-alert__icon">
                    <i class="bell-icon custom-icon"></i>
                </div>
                <div class="custom-alert__body">
                    <h6>${t(`Your Club's shop front image is outdated!`)}</h6>
                    <p style="margin-bottom: 0px;">${t('The shop front image will be used in your landing pages to help customers find your club.')}</p>
                    <p>${t('Please update your shop front image to ensure your club is displayed correctly.')}</p>
                </div>
            </div>
            <a href="#" class="btn btn-primary custom-alert__btn waves-effect normal-case update-shop-front-image">${t('Update Image')}</a>
        </div>
    `);

    $('.update-shop-front-image').on('click', (e) => {
        e.preventDefault();
        $('.digital-profile-config-container')[0].scrollIntoView({ block: 'center', behaviour: 'smooth' });
        window.scrollBy(0, -280);
    });
}

async function loadClubData() {
    let clubData = null;
    let listings = null;
    let devices = [];
    let brands = [];
    // $(".preload-text").show();
    // $('.club-details').hide();
    $('.club-details').addClass('isLoading');
    $('#tracking-items').html('');
    $('.yext-notifications').html('');

    // image loading skeletons
    const imageSkeleton = `
        <div class="card image empty">
            <i class="material-icons use-skeleton rounded--full">insert_photo</i>
        </div>
        <i class="material-icons update-image use-skeleton rounded--full">camera_alt</i>
        <input
            data-hj-allow
            type="file"
            data-type="{{ADDITIONAL}}"
            style="display: none"
        />
    `;

    $('#additional-images .image-list').html(`<div class="image-container additional-image-container">${imageSkeleton}</div>`);
    $('.logo-image-container').html(imageSkeleton);
    $('.cover-image-container').html(imageSkeleton);
    $('.shop-front-image-container').html(imageSkeleton);

    // Fetch club kyc alert...
    const [kycIssues] = await showMissingKYCConfigAlert();
    
    if (kycIssues) {
        $('.missing-club-kyc-config-alert-container')
            .on('click', 'a', (e) => {
                e.preventDefault();
                const hasChanges = $('#save-club-footer').hasClass('active');
                hasChanges ? confirmDialog(
                    'You have unsaved changes.',
                    'Are you sure you want to leave this page?',
                    () => sammy.quietRoute('/club-kyc')
                ) : sammy.quietRoute('/club-kyc');
            });
    }

    const [clubDataRes, listingsRes, devicesRes, brandsRes] = await Promise.all([
        getClubDetailsAPI(selectedID),
        listClubYextListingsAPI(selectedID),
        listClubDevicesAPI(selectedID),
        listBrandsAvailableToClubAPI(selectedID)
    ]);

    if (!clubDataRes[0]) {
        window.location.replace("/401.html");
    }

    clubData = clubDataRes[1];
    listings = listingsRes[0] ? listingsRes[1].listings : null;
    devices = devicesRes[1]?.items?.filter(d => d.deviceType === 'camera') || [];
    brands = brandsRes[0] ? brandsRes[1] : [];

    const isOrganisationAdmin = 
        signedInUserData?.organisations?.find((org) => org.organisationId === selectedOrgID)?.isOrganisationAdmin
        || false;

    let yextIssues = false;
    if (isOrganisationAdmin) {
        yextIssues = renderClubYextNotifications({ listings });
    }

    !yextIssues && !kycIssues && renderShopFrontNotification(clubData);

    // Club published/unpublished selector...
    (clubData.published) ? $('#published').prop('checked', true) : $('#published').prop('checked', false);

    $('#published').on('change', () => {
        const published = $('#published').is(':checked');

        if (!manuallyTriggeredPublishToDirectories) {
            $('.digital-profile-settings input').prop('checked', published);
        }
    })

    // Booking widget if enable/disable
    if ('bookingWidget' in clubData) $('#booking-widget').prop('checked', clubData.bookingWidget);

    // Load place data into object - we don't use this in the UI, however it needs to be stored for updates...
    placeData = clubData.placeData;

    // Primary Listing
    $("#email_address").val(clubData.email);
    $("#club_phne").val(clubData.phone);
    $("#club_name").val(clubData.name);

    $('#slug')
        .inputmask({ regex: '^[a-z0-9]+(?:-[a-z0-9]+)*$' })
        .val(clubData.slug);

    $('.club-id-container').html(`<code id="club_id" class="single-line">${clubData.baseURL}</code>`)
    $('.place-id-container').html(`<code id="place_id">${clubData.gmbPlaceId || ''}</code>`)

    // Add copy button to right of CODE block...
    addClipboardCopyButtons('.club-id-container');
    addClipboardCopyButtons('.place-id-container');

    // Secondary Emails...
    $("#secondaryEmails").val(JSON.stringify(clubData.secondaryEmails));
    renderSecondaryEmails();

    // Brand Selection
    $("#club_brand").html('<option value="">Select a brand</option>');

    brands.forEach((brand) => {
        const selected = (brand.brandId === clubData.brandId) ? 'selected' : '';
        const option = `<option value="${brand.brandId}" ${selected}>${brand.name}</option>`;
        $("#club_brand").append(option);
    });

    $('#club_brand').selectpicker('refresh');

    // Social URLs
    $("#facebook_url").val(clubData.url.facebook);
    $("#instagram_url").val(clubData.url.instagram);

    toggleAddressSwitch(clubData.placeData.isManual ? 'manual' : 'auto');

    // Club Location:
    $("#display_address").val(clubData.placeData.formatted_address);
    $('#manual_address1').val(clubData.placeData.address_1 || clubData.placeData.formatted_address);
    $('#manual_address2').val(clubData.placeData.address_2);
    $('#manual_address_city').val(clubData.placeData.city.full_name);
    $('#manual_address_state').val(clubData.placeData.province.full_name);
    $('#manual_address_country').val(clubData.placeData.country.full_name);
    $('#manual_address_postcode').val(clubData.placeData.postcode);

    // enable selectpicker for max booking days
    const maxBookingDaysVal = clubData.maxBookingDays?.toString();
    $('#maxBookingDays').val(maxBookingDaysVal !== '0' ? maxBookingDaysVal : '14');
    $('#maxBookingDays').selectpicker('refresh');

    // Set the ClubPage URL
    switch(selectedBrand.toUpperCase()) {
      case "UBX":
        $("#clubPageURL").html(`ubxtraining.com/gym/${selectedID} <a target="_blank" href="http://ubxtraining.com/gym/${selectedID}"><i class="fa fa-external-link"></i></a>`);
        $("#clubLinksPageURL").html(`ubxtraining.com/links/${selectedID} <a target="_blank" href="http://ubxtraining.com/links/${selectedID}"><i class="fa fa-external-link"></i></a>`);
        
        break;
      default:
        // code block
        $("#clubPageURL").html(``);
    }

    // render toggle for landing pages
    $('.landing-pages-container').html(`<hr class="ph-hr"><div><h2 class="card-inside-title min-w-max mt-0 use-skeleton">${t('Landing Pages')}</h2></div>`);
    clubData.landingPageOptions?.forEach(({ url, id, availabilityDates }) => {
        let availDates = ''
        if (availabilityDates) {
            const startDate = moment(availabilityDates.start);
            const endDate = moment(availabilityDates.end);
            availDates = '<span class="badge" style="margin-right: 5px;" data-toggle="tooltip" title="This page is active within these dates.">' + startDate.format('MMM D') + ' - ' + endDate.format('MMM D') + '</span>';
        }

        const link = `${availDates}<a href="${url}" target="_blank">${url}</a>`;
        const checked = !clubData.disabledLandingPages?.includes(id);
        $('.landing-pages-container')
            .append(switchLever(link, null, id, checked))
    });

    // if there's landingPages setup except for the old ones display the new landing pages
    if(clubData?.landingPages?.length > 0) {
      $('.landing-pages-container').append('<div class="new-club-lps"></div>');

      const filterClubLps = clubData.landingPages.filter(lp => lp.id !== 'free-trial' && lp.status === 'published');

      filterClubLps.forEach(({ url, id }) => {
        const link = `<a href="${url}" target="_blank">${url}</a>`;
        const checked = !clubData.disabledLandingPages?.includes(id);
        $('.new-club-lps')
            .append(switchLever(link, null, id, checked))
      });
    }

    $('.landing-pages-container').on('change', 'input', () => $("#save-club-footer").addClass('active'));

    // Is Open selector...
    (clubData.isOpen) ? $('#isOpen').prop('checked', true) : $('#isOpen').prop('checked', false);
    toggleClubOpen();

    // If the club is not yet open - placeholder text to display
    $("#placeholderText").val(clubData.placeholderText);

    // Club Hours comments
    $("#clubHoursComments").val(clubData.hoursComments);

    // Timezone data?
    $("#club_timezone").val(clubData.timeZone.name);
    $("#club_timezone").data('auto', clubData.timeZone.auto.toString());

    // Club hours
    $('#club-opening-hours').html('');
    Object.keys(clubData.hours).forEach((day) => {
        const card = `
            <div class="club-hour use-skeleton" data-day="${day}">
                <div class="title">${t(day)}</div>
                <div class="tags">${clubData.hours[day].length <= 0 ? getHourTag('Closed') : ''}</div>
                <ul class="block-list">
                    ${clubData.hours[day].map(block => `
                        <li
                            data-name="${day}"
                            data-opens="${block.start}"
                            data-closes="${block.end}"
                        >
                            <div>
                                <span>${block.start} - ${block.end}</span>
                                <i class="material-icons text-danger">close</i>
                            </div>
                        </li>
                    `).join('')}
                </ul>
                <a
                    href="#"
                    class="input-club-hours"
                    data-name="${day}"
                >
                    <span>
                        <i class="material-icons">add</i>
                        <span>${t('Add hours')}</span>
                    </span>
                </a>
            </div>
        `;

        $('#club-opening-hours').append(card);
    });

    if (clubData.defaultBlocks) {
        $('#default-club-blocks > ul > li').remove();
        $('#default-club-blocks > .tags > span').remove()

        if (clubData.defaultBlocks.length <= 0) {
            $('#default-club-blocks > .tags').html(getHourTag('Closed'));
        }

        clubData.defaultBlocks.forEach(defaultBlock => {
            $('#default-club-blocks > ul').append(getHourBlock({ ...defaultBlock, name: 'Default blocks' }));
        });
    }

    if (clubData.specialHours) {
        $('#special-club-hours > .special-hour-container').remove();

        const sortedSpecialHours = clubData.specialHours.sort((a, b) => a.date - b.date);
        sortedSpecialHours.forEach((specialHour) => {
            if (!moment(specialHour.date * 1000).isSameOrAfter(moment())) return;
            $('#special-club-hours > div:not(.special-hour-container)').before(getSpecialClubHour(specialHour));
        });
    }

    if (clubData.description) {
        $('#description').text(clubData.description);
    }

    if (clubData.facebookAbout) {
        $('#facebook-about').text(clubData.facebookAbout);
    }

    renderClubImages(clubData);

    // payment methods
    $('.payment-methods').html(getPaymentMethodContainer());
    clubData.paymentMethods.reverse().forEach(paymentMethod => {
        $('.payment-methods').prepend(getPaymentMethodContainer(paymentMethod));
    });

    // fetch tracking data after Facility Details loaded
    fetchTrackingData(selectedID);
    renderDigitalProfileSettings(clubData);

    // Update the db and remove unreviewed from all holiday hours
    if (sammy.getParam('action') === 'review-holidays' ) {
        reviewClubHolidaysAPI(selectedID);
    }

    renderExtendedAccessHours(clubData, devices);
    renderLineChatIntegration(clubData);

    // $(".preload-text").hide();
    // $(".club-details").show();
    $('.club-details').removeClass('isLoading');

    // Bind to 'save' button in application...
    $('#club-form').off('submit');
    $("#club-form").on('submit', (event) => {
        event.preventDefault();
        $(".save-overlay").show();

        saveClubData(clubData, () => {
            $(".save-overlay").hide();
            $('#save-club-footer').removeClass('active');

            instantNotification(
                t('Content for the Club ID') + ': [' + selectedID + ']' + t('successfully saved.'),
                'success',
                10000
            );
        })
    });

    sAdministerClub(placeData);

    // Loads the Lat & Lng for both the map, marker & details input box...
    panMapMarker(
        clubData.latitude,
        clubData.longitude
    );

}

function renderExtendedAccessHours(club, devices, enable) {
    const extendedHours = _get(club, 'extendedAccessHours', {});
    const enabled = typeof enable === 'boolean'
        ? enable
        : Object.keys(extendedHours).reduce((acc, day) => acc + extendedHours[day].length, 0) > 0;
    const disableSwitch = devices.length < 1 && !enabled;

    $('#extended-access-hours').html('');
    $('#extended-hours .enable-switch').prop('disabled', disableSwitch);
    $('#extended-hours .enable-switch').prop('checked', enabled);

    if (disableSwitch) {
        $('.no-device-alert').removeClass('hidden');
    } else {
        $('.no-device-alert').addClass('hidden');
    }

    $('#extended-hours .enable-switch').on('change', () => {
        if (!enabled) {
            club.extendedAccessHours = {
                Monday: [{ start: "4:00am", end: "10:00pm" }],
                Tuesday: [{ start: "4:00am", end: "10:00pm" }],
                Wednesday: [{ start: "4:00am", end: "10:00pm" }],
                Thursday: [{ start: "4:00am", end: "10:00pm" }],
                Friday: [{ start: "4:00am", end: "10:00pm" }],
                Saturday: [{ start: "4:00am", end: "10:00pm" }],
                Sunday: [{ start: "5:00am", end: "9:00pm" }]
            }
        }
        renderExtendedAccessHours(club, devices, !enabled);
    });

    if (!enabled) return;
    Object.keys(extendedHours).forEach((day) => {
        const card = `
            <div class="extended-hour" data-day="${day}">
                <div class="title">${t(day)}</div>
                <div class="tags">${extendedHours[day].length <= 0 ? getHourTag('Closed') : ''}</div>
                <ul class="block-list">
                    ${extendedHours[day].map(block => `
                        <li
                            data-name="${day}"
                            data-opens="${block.start}"
                            data-closes="${block.end}"
                        >
                            <div>
                                <span>${block.start} - ${block.end}</span>
                                <i class="material-icons text-danger">close</i>
                            </div>
                        </li>
                    `).join('')}
                </ul>
                <a
                    href="#"
                    class="input-extended-hours"
                    data-name="${day}"
                >
                    <span>
                        <i class="material-icons">add</i>
                        <span>${t('Add hours')}</span>
                    </span>
                </a>
            </div>
        `;

        $('#extended-access-hours').append(card);
    });

    // Add hours to club opening hours
    $('.input-extended-hours').on('click', (e) => {
        const element = $(e.currentTarget);
        addHourBlock(element, '.extended-hour');
    });
}

function renderDigitalProfileSettings(club) {
    manuallyTriggeredPublishToDirectories = 'publishToDirectories' in club;

    $('.digital-profile-settings').html(`
        <div class="demo-switch">
            <div class="switch ph-switch">
                <label style="min-width: max-content;" class="use-skeleton">
                    ${t('Auto publish changes to Directories')}
                    <input data-hj-allow type="checkbox">
                    <span class="lever lever switch-col-blue"></span>
                </label>
            </div>
        </div>
        <button type="button" class="btn btn-primary use-skeleton">
            ${t('Push Updates to Directories')}
        </button>
        ${club.yextEntityId ? `<span class="yext-id use-skeleton">Yext ID: <code>#${club.yextEntityId}</code></span>` : ''}
    `);

    if (manuallyTriggeredPublishToDirectories ? club.publishToDirectories : club.published) {
        $('.digital-profile-settings input').attr('checked', true);
    }

    addClipboardCopyButtons('.digital-profile-settings .yext-id');

    $('.digital-profile-settings input').on('change', () => {
        manuallyTriggeredPublishToDirectories = true;
        $("#save-club-footer").addClass('active');
    })
    
    $('.digital-profile-settings button').on('click', async (e) => {
        $(".save-overlay").show();
        await updateClubYextEntity(selectedID);
        $(".save-overlay").hide();
    });
}

function getPaymentMethodDisplayName(paymentMethod) {
    const alternateNames = {
        'cheque': 'check',
        'android': 'android pay',
        'traveler': 'traveler\'s check',
    };

    return alternateNames[paymentMethod.toLowerCase()] || paymentMethod.toLowerCase();
}

function getPaymentMethodContainer(paymentMethod) {
    if (!paymentMethod) {
        return '<div class="select-payment-methods use-skeleton"><div class="card"><i class="material-icons">add_circle</i></div></div>';
    }

    return (`
        <div class="payment-method use-skeleton" data-name="${paymentMethod}">
            <img src="/assets/images/payment_methods/${paymentMethod.toLowerCase()}.png" />
            <small>${getPaymentMethodDisplayName(paymentMethod)}</small>
        </div>
    `);
}

function getSpecialClubHour(specialHour) {
    const tags = specialHour.tags || [];

    if (specialHour.blocks.length < 1 && !tags.includes('Closed')) {
        tags.push('Closed');
    }

    const deletedClx = tags.includes('Deleted') ? 'deleted' : '';
    const formattedDate = moment(specialHour.date * 1000).format('MMM DD, YYYY');

    return (
        `<div class="special-hour-container ${deletedClx}">
            <div
                id="special-hour-${specialHour.id}"
                data-id="${specialHour.id}"
                class="card special-hour ${specialHour.unreviewed ? 'unreviewed' : ''}"
                data-name="${specialHour.name}"
                data-date="${specialHour.date}"
                ${specialHour.oldName ? `data-old-name="${specialHour.oldName}"` : ''}
                ${specialHour.oldDate ? `data-old-date="${specialHour.oldDate}"` : ''}
            >
                <div class="body">
                    <div class="title-container">
                        <div class="title">
                            ${specialHour.name}<br><small>${formattedDate}</small>
                        </div>
                        <div class="dropdown">
                            <i class="material-icons" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">more_vert</i> 
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                <a
                                    class="dropdown-item duplicate-special-hour"
                                    href="#"
                                >
                                    <i
                                        class="material-icons"
                                        style="transform: translateY(2px)"
                                    >
                                        content_copy
                                    </i>
                                    <span>Create copy</span>
                                </a>
                                <a
                                    class="dropdown-item update-special-hour"
                                    href="#"
                                >
                                    <i
                                        class="material-icons"
                                        style="transform: translateY(2px)"
                                    >
                                        edit
                                    </i>
                                    <span>Update</span>
                                </a>
                                <a
                                    class="dropdown-item delete-special-hour"
                                    data-name="${specialHour.name}"
                                    href="#"
                                >
                                    <i
                                        class="material-icons"
                                        style="transform: translateY(2px)"
                                    >
                                        delete
                                    </i>
                                    <span>Delete</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="tags">
                        ${(tags || []).map(getHourTag).join('')}
                    </div>
                    <ul class="block-list">
                        ${
                            specialHour.blocks.map(block => {
                                return getHourBlock({ ...block, name: specialHour.name });
                            }).join('')
                        }
                    </ul>
                    <a 
                        href="#" 
                        class="input-special-hours" 
                        data-id="${specialHour.id}" 
                        data-name="${specialHour.name}" 
                        data-date="${specialHour.date}" 
                    >
                        <span>
                            <i class="material-icons">add</i>
                            <span>add hours</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>`
    );
}

function getHourTag(tag) {
    const types = {
        'closed': 'red',
        'added automatically': 'blue',
        'global': 'lightgrey',
    }

    const type = types[tag.toLowerCase()] || types['global'];
    return `<span class='badge badge-${type}' data-tag="${tag}">${t(tag)}${ (type == 'red') ? ' <i class="fa fa-clock-o" aria-hidden="true"></i>' : '' }</span>`;
}

function getHourBlock(block) {
    return (`
        <li
            data-name="${block.name}"
            data-opens="${block.start}"
            data-closes="${block.end}"
        >
            <div>
                <span>${block.start} - ${block.end}</span>
                <i class="material-icons text-danger">close</i>
            </div>
        </li>
    `);
}

function specialHourDialog(specialHour, callback) {
    // Build html content for popup...
    const div = document.createElement("div");
    $(div).addClass('ph-dialog-v2');
    $(div).html(`
        <div class="body d-flex justify-around">
            <div style='margin: 3px' class="form-group form-group-no-margin">
                <div class="form-line">
                    <label>${t('Holiday Name')}:</label>
                    <input data-hj-allow class='form-control' type='text' name='name' value="${specialHour.name || ''}" />
                </div>
            </div>
            <div style='margin: 3px' class="form-group form-group-no-margin">
                <div class="form-line">
                    <label>${t('Date')}:</label>
                    <input data-hj-allow class='form-control' type='text' name='date' />
                </div>
            </div>
        </div>
    `);
    
    //Datepicker ranges
    const rangeObject = {};
    rangeObject[t('Today')] = [moment(), moment()];
    rangeObject[t('Tomorrow')] = [moment().add(1, 'days'), moment().add(1, 'days')];
    rangeObject[t('+7 Days')] = [moment().add(7, 'days'), moment().add(7, 'days')];

    // Bind datepicker
    $(div).find('input[name=date]').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        startDate: specialHour.date ? moment(specialHour.date * 1000) : moment(),
        minDate: moment(),
        ranges: rangeObject,
        locale: {
            format: 'MMMM DD, YYYY',
            applyLabel: t('Apply'),
            cancelLabel: t('Cancel'),
            customRangeLabel: t('Custom Range'),
            monthNames: [
                t('Jan'),
                t('Feb'),
                t('Mar'),
                t('Apr'),
                t('May'),
                t('Jun'),
                t('Jul'),
                t('Aug'),
                t('Sep'),
                t('Oct'),
                t('Nov'),
                t('Dec'),
            ],
            daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
        },
    });

    let holidayHoursObj
    swalWithBootstrapButtons.fire({
        title: `${t(`${specialHour.id > -1 ? 'Edit' : 'Add'} Holiday Block`)}`,
        html: div,
        width: 500,
        showCloseButton: true,
        //true means show cancel button, with default values
        showCancelButton: true,
        confirmButtonText: t('Save/Update'),
        cancelButtonText: t('Cancel'),
        onBeforeOpen: () => {
            $('.daterangepicker').css('z-index', 3000);
        },
        preConfirm: () => {
            let error = null;
            const name = $(div).find('input[name=name]').val();
            const date = $(div).find('input[name=date]').val();
            
            if (name === '' || date === '') {
                error = t('Name and date is required');
            }

            if (error) {
                Swal.showValidationMessage(error);
            }
        },
        onClose: () => {
            let selectedDate = $(div).find('input[name=date]').val();
            holidayHoursObj = {
                name: $(div).find('input[name=name]').val(),
                date: moment(selectedDate, 'MMMM DD, YYYY').unix()
            }
        }
    }).then(response => {
        if (response.value) {
            callback(holidayHoursObj);
        } else {
            callback(false);
        }
    });
}

function specialHourDeleteDialog(title, callback) {

    swalWithBootstrapButtons.fire({
        title,
        width: 500,

        //true means show cancel button, with default values
        showCancelButton: true,
        confirmButtonText: t('Confirm'),
        cancelButtonText: t('Cancel')

    }).then(response => {
        if (response.value) {
            callback(true);
        } else {
            callback(false);
        }
    });
}

function addHourBlock(element, parentClass) {
    const { name } = element.data();

    // get blocks array
    let blocks = [];
    element.closest(parentClass).find('.block-list li').map((_, block) => {
        const { opens, closes } = $(block).data();
        blocks = [...blocks, { opens, closes }]
    });

    const title = `Add hours for ${name}`;
    hoursSelectorDialog(element, title, blocks, (response) => {
        if(response !== false) {
            if(response.opens !== "" && response.closes !== "") {
                
                // Remove "closed" tag if it exists
                element.closest(parentClass).find('.tags span').map((_, tag) => {
                    if ($(tag).data('tag') === 'Closed') {
                        $(tag).remove();
                    }
                });

                // Add response to blocks and sort
                blocks.push({ opens: response.opens, closes: response.closes });
                blocks = blocks.sort((a, b) => {
                    return moment(a.opens, 'hh:mma').isBefore(moment(b.opens, 'hh:mma')) ? -1 : 1;
                });

                // Replace block list with sorted blocks
                element.closest(parentClass).find('.block-list').html('');
                blocks.forEach(block => {
                    return element.closest(parentClass).find('.block-list').append(getHourBlock({ start: block.opens, end: block.closes, name }));
                });

                // Activate footer
                $("#save-club-footer").addClass('active');
            }
        }
    });
}

async function saveClubData(clubData, callback) {
    //console.log(selectedID);

    if ($('#published').is(':checked')) { var published = true } else { var published = false; }
    if ($('#isOpen').is(':checked')) { var isOpen = true } else { var isOpen = false; }

    var bookingWidget = $('#booking-widget').is(':checked');

    var clubItem = {
        "name": $("#club_name").val(),
        "brandId": $("#club_brand").val(),
        "published": published,
        "latitude": $("#lat_lng").data('lat'),
        "longitude": $("#lat_lng").data('lng'),
        "email": $("#email_address").val(),
        "secondaryEmails": JSON.parse($("#secondaryEmails").val()),
        "phone": $("#club_phne").val(),
        "url": {
          "facebook": $("#facebook_url").val().toLowerCase(),
          "instagram": $("#instagram_url").val().toLowerCase(),
          "twitter": false
        },
        "hours": {},
        "hoursComments": $("#clubHoursComments").val(),
        //"analyticsTracking": false
        "isOpen": isOpen,
        "placeholderText": $("#placeholderText").val(),
        "description": $("#description").val(),
        "facebookAbout": $('#facebook-about').val(),
        "bookingWidget": bookingWidget,
        "publishToDirectories": manuallyTriggeredPublishToDirectories
            ? $('.digital-profile-settings input').is(':checked')
            : undefined,
        "disabledLandingPages": [],
        photos: clubData.photos ?? [],
        previouslyUsedPhotos: clubData.previouslyUsedPhotos ?? [],
        maxBookingDays: bookingWidget ? Number($('#maxBookingDays').val()) : 0,
        slug: $('#slug').val(),
    }

    if ($('[name=line-chat-enabled]').is(':checked')) {
        const qrValue = $('[name=line-chat-qr]').val();
        const btnValue = $('[name=line-chat-btn]').val();

        const qrUrlRgx = /https:\/\/qr-official\.line\.me\/gs\/(.*)\.png/;
        const btnUrlRgx = /"https:\/\/lin\.ee\/(.*?)"/;
        const btnImgRgx = /<img src="(.*?)"/;

        if (qrValue === '' || btnValue === '') {
            $(".save-overlay").hide();
            return instantNotification(t('Please fill in both Line Chat QR and Button Text'), 'error');
        }

        const qrUrl = qrValue.match(qrUrlRgx)?.[0];
        const btnImg = btnValue.match(btnImgRgx)?.[1];
        let btnUrl = btnValue.match(btnUrlRgx)?.[0];
        btnUrl = btnUrl?.substring(1, btnUrl.length - 1);

        if (!qrUrl) {
            $(".save-overlay").hide();
            return instantNotification(t('Invalid value QR code HTML'), 'error');
        }

        if (!btnUrl || !btnImg) {
            $(".save-overlay").hide();
            return instantNotification(t('Invalid value Button code HTML'), 'error');
        }

        clubItem.lineChat = { qrUrl, btnImg, btnUrl };
    }

    if($("#club_timezone").data('auto') != "true") {
        clubItem.timeZone = $("#club_timezone").val()
    }

    // Loop over each opening hour
    $('#club-opening-hours .club-hour').map((_, clubHour) => {
        const { day } = $(clubHour).data();
        clubItem.hours[day] = [];

        $(clubHour).find('.block-list li').map((_, block) => {
            const { opens: start, closes: end } = $(block).data();
            clubItem.hours[day].push({ start, end })
        });
    });

    if ($('#extended-access-hours .extended-hour').length > 0) {
        clubItem.extendedAccessHours = {};
        $('#extended-access-hours .extended-hour').map((_, clubHour) => {
            const { day } = $(clubHour).data();
            clubItem.extendedAccessHours[day] = [];

            $(clubHour).find('.block-list li').map((_, block) => {
                const { opens: start, closes: end } = $(block).data();
                clubItem.extendedAccessHours[day].push({ start, end })
            });
        });
    }

    // Loop over each special dates
    clubItem.specialHours = [];
    
    $('#special-club-hours .special-hour').map((index, specialClubHour) => {
        const { name, date, oldName, oldDate, id } = $(specialClubHour).data();
        const doc = { id, name, date, blocks: [], tags: [] };

        if (oldName && oldDate) {
            doc.oldDate = oldDate;
            doc.oldName = oldName;
        }
        
        $(specialClubHour).find('.block-list li').map((_, block) => {
            const { opens, closes } = $(block).data();
            doc.blocks.push({ start: opens, end: closes });
        });

        $(specialClubHour).find('.tags > span').map((_, tag) => {
            doc.tags.push($(tag).data('tag'));
        });

        clubItem.specialHours.push(doc);
    });

    // Loop over each default block
    clubItem.defaultBlocks = [];

    $('#default-club-blocks .block-list li').map((_, block) => {
        const { opens, closes } = $(block).data();
        clubItem.defaultBlocks.push({ start: opens, end: closes });
    });

    // Loop over each payment method
    clubItem.paymentMethods = [];

    $('.payment-methods .payment-method').map((_, pm) => {
        const { name } = $(pm).data();
        clubItem.paymentMethods.push(name);
    });

    const usingManualAddress = $('#manual-address-toggle').is(':checked');

    // unitary countries don't have provinces or cities but we don't set it to null cause it might break some integrations
    if (unitaryCountries.includes(placeData.country?.iso_code)) {
        placeData.province.iso_code = '';
        placeData.province.full_name = '';
        placeData.province.abbreviation = '';
        placeData.city.iso_code = '';
        placeData.city.full_name = '';
        placeData.city.short_name = '';
    }

    if (usingManualAddress) {
        clubItem.placeData = {
            ...placeData,
            address_1: $('#manual_address1').val(),
            address_2: $('#manual_address2').val(),
            formatted_address: [$('#manual_address1').val(), $('#manual_address2').val()].filter(x => x !== '').join(', '),
            postcode: $('#manual_address_postcode').val(),
            isManual: true,
        }
    } else {
        clubItem.placeData = {
            ...placeData,
            isManual: false,
            address_1: undefined,
            address_2: undefined,
            postcode: undefined,
        }
    }

    // loop through landing pages
    $('.landing-pages-container input').each((_, input) => {
        const name = $(input).attr('name');
        const checked = $(input).is(':checked');
        if (!checked) clubItem.disabledLandingPages.push(name);
    });

    try {
        await $.ajax({
                url: `/api/clubs/clubpage/updateclub/${selectedID}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(clubItem),
        });
    } catch (err) {
        console.error(err.responseJSON?.message ?? 'Something went wrong while trying to save the club data.', err);

        $(".save-overlay").hide();
        $('#save-club-footer').removeClass('active');

        const message = err.status === 400
            ? err.responseJSON.message
            : `${t('An error occurred while trying to save the data for the Club ID:')} [${selectedID}]. ${t('Please contact HQ to further investigate this issue.')}`;

        return instantNotification(message, 'error', 60000);
    }

    if (trackingData.length > 0) {
        const trackRes = await recordTracking(selectedID, trackingData, recordStatus)
        if (!trackRes[0]) return;
    }

    return callback();
}

function fetchTrackingData(clubID) {
    if (!clubID) return;
    $.ajax({
        url: '/api/clubs/customtracking/' + clubID,
        method: 'GET',
        success: function (data) {
            if (data) {
                // initialized custom tracking after details are loaded
                initCustomTracking(data.trackingItems || [], clubID);
            }
        },
        error: function (xhr, textStatus, errorThrown) {
            console.error(xhr, textStatus, errorThrown);
            // set empty array for the list
            initCustomTracking([], clubID);
        },
    });
}

// tracking code variables
let trackingData = [];
let isEditing = false;
let activeId = null;
let recordStatus = 'published'

function resetData() {
    isEditing = false;
    activeId = null;
    recordStatus = 'published'
}

// save/update custom tracking record
async function recordTracking(clubID, data, status = recordStatus) {
    const isDeleted = status === 'deleted'
    const isUpdate = isEditing || isDeleted || data.length > 1;
    const url = `/api/clubs/${clubID}/customtracking`;
    const method = isUpdate ? 'PUT' : 'POST';

    try {
        const saveRes = await $.ajax({
            url,
            method,
            data: { trackingItems: data, status: status || 'published' },
        });

        // get the data from db when deleted
        if (isDeleted) return [true, fetchTrackingData(clubID)];
        resetData()
        return [true, saveRes]
    } catch (err) {
        console.error(err.responseJSON?.message ?? 'Something went wrong while trying to save the club tracking.', err);

        $(".save-overlay").hide();
        $('#save-club-footer').removeClass('active');

        const message = err.status === 400
            ? err.responseJSON?.message
            : `${t('An error occurred while trying to save the tracking for the Club ID:')} [${selectedID}]. ${t('Please contact HQ to further investigate this issue.')}`;
        
        instantNotification(message, 'error', 60000);
        return [false, err]
    }
}

function renderLineChatIntegration(data) {
    const qrImg = data.lineChat?.qrUrl ? `<img src="${data.lineChat.qrUrl}" />` : '';
    const btnImg = data.lineChat?.btnImg ? `<img src="${data.lineChat.btnImg}" />` : 'Link';
    const btnLink = data.lineChat?.btnUrl ? `<a href="${data.lineChat.btnUrl}" target="_blank">${btnImg}</a>` : '';

    $('.line-chat-integration .input-container').html(`
        <div class="card">
            <div>
                ${textArea({ label: 'QR Code HTML', name: 'line-chat-qr', value: qrImg })}
            </div>
            <div class="example">${qrImg}</div>
        </div>
        <div class="card">
            <div>
                ${textArea({ label: 'Button Code HTML', name: 'line-chat-btn', value: btnLink })}
            </div>
            <div class="example">${btnLink}</div>
        </div>
    `);

    toggleLineChat(!!data.lineChat);

    $('[name=line-chat-qr],[name=line-chat-btn]').on('input', (e) => {
        const value = $(e.currentTarget).val();
        const example = $(e.currentTarget).closest('.card').find('.example');
        example.html(value);
    });

    $('input[name=line-chat-enabled]').on('change', (e) => {
        const checked = $(e.currentTarget).is(':checked');
        toggleLineChat(checked);
        if (!checked && data.lineChat) {
            $("#save-club-footer").addClass('active');
        }
    });

    $('[name=line-chat-qr],[name=line-chat-btn]').on('input', (e) => {
        $("#save-club-footer").addClass('active');
    });

    function toggleLineChat(open) {
        const isOpen = $('.line-chat-integration .input-collapse').hasClass('show');
        open = open ?? !isOpen;

        if (open) {
            $('input[name=line-chat-enabled]').prop('checked', true);
            $('.line-chat-integration .input-collapse').collapse('show');
        } else {
            $('input[name=line-chat-enabled]').prop('checked', false);
            $('.line-chat-integration .input-collapse').collapse('hide');
        }
    }
}

// initialize customTracking ui
function initCustomTracking(data) {
    // reset data
    trackingData = data || [];
    resetData()

    // enable sorting
    const el = document.getElementById('tracking-items');
    Sortable.create(el, {
        // Element dragging ended
        onEnd: function (evt) {
            const oldValue = trackingData[evt.oldIndex];
            const newValue = trackingData[evt.newIndex];

            trackingData[evt.oldIndex] = newValue;
            trackingData[evt.newIndex] = oldValue;
        },
    });

    // el selectors
    const trackingItems = $('#tracking-items');
    const trackingContent = $('#tracking-contents');
    const trackingForm = $('#tracking-form');
    const saveTrackingEl = $('#save-tracking');
    const addTrackingEl = $('#add-tracking');
    const fieldError = $('#tracking-form-error');
    const cancelForm = $('#cancel-form');
    const trackingNameEl = $('#tracking-name');
    const trackingCodeEl = $('#tracking-code');
    const trackingStatusEl = $('#tracking-status')

    // generate single item in the list
    function generateItem(id, trackingName, isHidden, status) {
        const badge = status === 'published' ? `<div class="badge badge-green">${status} <i class="fa fa-check" aria-hidden="true"></i></div>` : `<div class="badge badge-red">${status} <i class="fa fa-power-off" aria-hidden="true"></i></div>`

        return `<li class="${isHidden ? 'hidden' : ''}">
                <button type="button" class="tracking-item-edit btn" data-id="${id}">
                    ${trackingName}
                </button>
                <button class="tracking-item-delete" data-name="${trackingName}" data-id="${id}" type="button">
                    <i class="fa fa-trash-o" aria-hidden="true"></i>
                </button>
                ${badge}
            </li>`;
    }

    // get item from data
    function getItem(id = activeId, data = trackingData) {
        const itemIndex = data.findIndex((item) => item.id === id);
        return {
            data: trackingData[itemIndex],
            itemIndex,
        };
    }

    // delete item from data
    function deleteItem(id = activeId, data = trackingData) {
        const { itemIndex, data: item } = getItem(id);
        const newData = {
            ...item,
            status: 'deleted',
        };
        data[itemIndex] = newData;

        const deleteRecord = data.every((item) => item.status === 'deleted');

        isEditing = true;

        // reset the tracking data record
        if (deleteRecord) {
            recordStatus = 'deleted'
        }

        hideForm();
        renderList();
        $("#save-club-footer").addClass('active');

        return newData;
    }

    function renderList() {
        // empty the current list
        trackingItems.html('');

        if (trackingData.length > 0) {
            // generate new list
            trackingData.map(({ id, trackingName, status }) => {
                const item = generateItem(id, trackingName, status === 'deleted', status);
                $(item).appendTo(trackingItems);
            });

            // edit item
            trackingItems.find('.tracking-item-edit').click(function () {
                const id = $(this).data('id');
                activeId = id;
                isEditing = true;
                const {
                    data: { trackingName, trackingCode, status },
                } = getItem();
                trackingNameEl.val(trackingName);
                trackingCodeEl.val(trackingCode);

                trackingStatusEl.prop('checked', status === 'published')
                TLN.append_line_numbers('tracking-code')

                displayForm('Update');

                // Scroll to bottom of page (since this element is the last one)
                window.scrollTo(0, document.body.scrollHeight);
            });

            // delete item
            trackingItems.find('.tracking-item-delete').click(function () {
                const id = $(this).data('id'), tagName = $(this).data('name');

                swalWithBootstrapButtons.fire({
                    title: `Delete Tracking Code: [${tagName}]?`,
                    html: `Note that deleting the tracking code &quot;${tagName}&quot; will instantly un-publish the code from all Club pages associated with this Club<hr><small>UUID: <code>${id}</code></small>`,
                    width: 500,

                    //true means show cancel button, with default values
                    showCancelButton: true,
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel'

                }).then(response => {
                    if (response.value) {
                        deleteItem(id)
                    } else {
                        console.log('canceled')
                    }
                });

            });
        }
    }
    // initial render if trackingData is not empty
    renderList();


    // add new the tracking data or new changes
    function saveTracking(e) {

        $("#save-club-footer").addClass('active');

        e.preventDefault();
        fieldError.hide();

        const id = uuidv4();

        // get the form values
        const trackingName = trackingNameEl.val();
        const trackingCode = trackingCodeEl.val();
        const status = trackingStatusEl.is(':checked') ? 'published' : 'unpublished'

        // error handling
        const defaultErrorMsg = 'All fields are required';
        const analyticsErrorMsg = 'GA and GTM are already added';
        const gtmRegex = /(ga)\b|(gtm)\b/gi;

        // basic validation
        if (!trackingName || !trackingCode) {
            fieldError.text(defaultErrorMsg);
            fieldError.fadeIn();
            return;
        }

        // regex validation
        if (gtmRegex.test(trackingCode.toLowerCase())) {
            fieldError.text(analyticsErrorMsg);
            fieldError.fadeIn();
            return;
        }

        const updateDisplay = () => {
            hideForm();
            renderList();
        };

        // add the content to the array if it's not an update
        if (!isEditing) {
            trackingData.push({
                trackingName,
                trackingCode,
                id,
                status,
            });
            updateDisplay()
            return;
        }

        // edit existing
        const { itemIndex } = getItem();
        trackingData[itemIndex] = {
            id: activeId,
            trackingName,
            trackingCode,
            status
        };

        updateDisplay();
    }

    saveTrackingEl.click(saveTracking);

    // hide form
    function hideForm() {
        TLN.remove_line_numbers('tracking-code');
        trackingContent.removeClass('show-form');
        trackingForm.removeClass('active');
        addTrackingEl.show();
        trackingNameEl.val('');
        trackingCodeEl.val('');
        fieldError.hide();
        activeId = null;
        isEditing = false;
    }
    cancelForm.click(hideForm);

    // display the form
    function displayForm(saveButtonText='Add') {
        if (trackingData.length > 0) {
            trackingContent.addClass('show-form');
        }

        $("#save-tracking").html(t(saveButtonText))
        trackingForm.addClass('active');
        addTrackingEl.hide();
    }
    addTrackingEl.click(() => {
        trackingStatusEl.prop('checked', true)
        TLN.append_line_numbers('tracking-code')
        displayForm()
        // Scroll to bottom of page (since this element is the last one)
        window.scrollTo(0, document.body.scrollHeight);
    });
}
