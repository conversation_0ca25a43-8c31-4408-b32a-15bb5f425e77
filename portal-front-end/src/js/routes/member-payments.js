const memberPaymentsDashboardCharts = {};

const initMemberPayments = () => {
    let records = [];
    let currencies = [];
    let payouts = [];
    let feesConfig = [];
    let uniqueFeeNames = [];
    let unaccountedFeesColVisible = false;
    let payoutTotals = {};
    let feesTotals = {};
    let loading = [];
    let recordSummaryTotals = {};
    let forecastedFutureRecords = [];
    let interval = 'day';
    let paymentsTable = null;
    let recordsSessionID = null;

    const initQueryParams = getQueryStringsObject();
    const defaultSelectedTimezone = initQueryParams.timezone || selectedClubObj.localisation.timezone;
    const config = {
        from: initQueryParams.fromDate ? moment.unix(initQueryParams.fromDate) : moment().subtract(6, 'days').startOf('day'),
        to: initQueryParams.toDate ? moment.unix(initQueryParams.toDate) : moment().endOf('day'),
        search: decodeURIComponent(initQueryParams.filter || ''),
        selectedTimezone: defaultSelectedTimezone,
    };

    const init = async () => {
        updateInitUrl();
        initTabs();
        loadDateRange();
        initialLoading();

        searchbarHeader('.payments-search-container', [
            { label: 'COPY', icon: 'fa-copy', onClick: () => paymentsTable && paymentsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => paymentsTable && paymentsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => paymentsTable && paymentsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => paymentsTable && paymentsTable.button(3).trigger() },
        ], {
            usePhTheme: true,
            expandableButtons: {
                startIndex: 0,
                count: 4,
                label: t('Export'),
                icon: 'fa-ellipsis-v',
                id: 'member-payments-export-buttons',
            },
        });

        $(".search-bar").val(config.search);
        loadMemberPaymentsData();
    };

    const updateInitUrl = () => {
        if (location.pathname !== '/member-payments') return;
        history.pushState({ tabChange: true }, null, '/member-payments/settlement-dashboard');
    }

    const initTabs = () => {
        const tab = location.pathname.split('/').pop();

        // Simple logic (taken from marketing & print) - to select whatever "tab" the user links to from a deeplink...
        $('.payments-container .nav-tabs a[href="#'+ tab +'"]').tab('show');
        $('.payments-container .nav-tabs a').on("click", function (e) {
            e.preventDefault();
            const querystring = location.search;
            const href = $(this).attr("href");
            const formattedHref = '/member-payments/' + href.replace('#', '') + querystring;

            history.pushState({ tabChange: true }, null, formattedHref);
        });
    }

    const initialLoading = () => {
        ['#settlement-summary-container', '#payments-table-container', '#dashboard-container'].forEach((container) => {
            $(container).empty();
            $(`${container}-loader`).show();
            if (!loading.includes(container)) {
                loading.push(container);
            }
        });
    };

    const fetchFeesConfig = async () => {
        const [feesConfigRes, countryHolidaysRes, feesConfigActiveRes] = await Promise.all([
            listClubFeesConfigAPI(selectedID),
            listCountryHolidaysAPI(selectedClubObj.localisation.country.iso_code),
            listClubFeesConfigAPI(selectedID, '?hideInactive=true&showDisabledFees=true'),
        ]);

        if (feesConfigRes[0]) {
            feesConfig = feesConfigRes[1]
                .sort((a, b) => a.dateEffective - b.dateEffective)
                .filter((fc) => {
                    const isEffective = fc.dateEffective <= config.to.unix();
                    const isExpired = fc.dateExpired < config.from.unix();

                   return isEffective && !isExpired;
                });

            uniqueFeeNames = feesConfig.reduce((acc, feeConfig) => {
                feeConfig.fees.forEach((fee) => {
                    const firstTaxType = fee.taxes[0]?.type;
                    const taxType = firstTaxType ? `${fee.name} ${firstTaxType.toUpperCase()}` : null;

                    if (!acc.some(f => f.name === fee.name)) {
                        acc.push({ name: fee.name, tax: taxType });
                    } else if (taxType) {
                        acc = acc.map((f) => {
                            if (f.name === fee.name) return { ...f, tax: taxType };
                            return f;
                        });
                    }
                });
                return acc;
            }, []);
        }

        if (countryHolidaysRes[0]) {
            const dates = countryHolidaysRes[1].reduce((acc, holiday) => {
                if (holiday.blackListed) return acc;
                return [...acc, moment.unix(holiday.date).tz(config.selectedTimezone).format('YYYY-MM-DD')];
            }, []);

            moment.updateLocale(navigator.languages[0], {
                holidays: dates,
                holidayFormat: 'YYYY-MM-DD',
            });
        }

        //update fees table
        if(feesConfigActiveRes[0]) {
          const activeFees = feesConfigActiveRes[1]?.fees || [];
          const processingFees = activeFees.length > 0 ? activeFees.filter(fee => !fee.name.toLowerCase().includes('royalty')) : [];
          const royaltyFees = activeFees.length > 0 ? activeFees.filter(fee => fee.name.toLowerCase().includes('royalty')) : [];

            if (processingFees.length > 0) {
                $('#payment-processing-table tbody').empty();

                processingFees.forEach(fee => {
                const row = `
                    <tr>
                    <td><span class="use-skeleton">${t(fee.name)}</span></td>
                    <td>
                        <span class="use-skeleton">${fee.percentage > 0 ? `${fee.percentage}%` : ''}
                        ${fee.fixed && fee.percentage ? ' + ' : ''}
                        ${fee.fixed ? `${clubCurrencySymbol}${fee.fixed}` : ''}${fee.transactionType !== 'success' ? ` <span class="capitalize">${t(`per ${fee.transactionType === 'chargeback' ? 'dispute' : fee.transactionType} transaction`)}</span>` : ''}</td>
                    </tr>
                `;

                $('#payment-processing-table tbody').append(row);
                });
            }
          
            if (royaltyFees.length > 0) {
                $('#royalty-fee-table tbody').empty();

                royaltyFees.forEach(fee => {
                const row = `
                    <tr>
                    <td><span class="use-skeleton">${t(fee.name)}</span></td>
                    <td>
                        <span class="use-skeleton">${fee.percentage > 0 ? `${fee.percentage}%` : ''}
                        ${fee.name.toLowerCase().includes('royalt') ? ` ${t('of Gross payments collected')}` : ''}</td>
                    </tr>
                `;

                $('#royalty-fee-table tbody').append(row);
                });
            }
        }

        // remove isLoading class after the content is available
        setTimeout(() => {
          $('#payments-royalties').removeClass('isLoading');
          createOverviewSidebarLinks($('#payments-royalties .tab-content-overview__content'), '#payments-royalties .tab-content-overview__sidebar ul', $('#member-payments-page .container-fluid'))
        },500);
    }

    const loadMemberPaymentsData = async () => {
        $('.no-stripe').hide();
        $('.payments-container').show();
        initialLoading();

        // Does the club have stripe configured?
        const integrations = await listClubIntegrationsAPI(selectedID);

        if (!integrations[0] || !integrations[1].stripeConnectedAccountID) {
            $('.no-stripe').show();
            $('.payments-container').hide();
            return;
        }

        await fetchFeesConfig();

        // reset config
        records = [];
        payouts = [];
        feesTotals = {};

        // reset socket listeners in case of reconnection
        socket.off('request-club-stripe-records');
        socket.on('request-club-stripe-records', (data) => {
            if (data.sessionID !== recordsSessionID) return;

            if (data.error) {
                $('.no-stripe').show();
                $('.payments-container').hide();
                return;
            }

            if (data.payouts) {
                payouts = [...payouts, ...data.payouts];
            }

            if (data.records) {
                const recordsToAdd = data.records.reduce((acc, record) => {
                    if (!records.some(({ id }) => id === record.id)) {
                        acc.push(record);
                    }

                    return acc;
                }, []);

                records = records.map((record) => {
                    const found = data.records.find(({ id }) => id === record.id);
                    if (found) return found;
                    return record;
                });

                records = [...records, ...recordsToAdd];

                const newRecords = attachAppFeesToRecords(records);
                records = newRecords;

                unaccountedFeesCol = records.some((record) => record.unaccountedFees > 0);

                currencies = records.reduce((acc, record) => {
                    if (acc.includes(record.currency)) return acc;
                    acc.push(record.currency);
                    return acc;
                }, []);

                renderPaymentsTable();
            }

            if (data.done) {
                // Total collected and total refunded
                recordSummaryTotals = records.reduce(
                    (acc, record) => {
                        const charges = getRecordCharges(record);

                        if (record.status === 'succeeded') {
                            const value = acc.collected.currencies[record.currency] || 0;
                            acc.collected.currencies[record.currency] = value + record.amount;
                        } else if (charges[0]?.refunded > 0) {
                            const value = acc.refunded.currencies[record.currency] || 0;
                            acc.refunded.currencies[record.currency] = value + charges[0].refunded;
                        }

                        return acc;
                    },
                    {
                        collected: { label: t('Total Collected'), currencies: {} },
                        refunded: { label: t('Total Refunded'), currencies: {} },
                    }
                );

                renderGraphsAndSummaryData();
                fetchForecastedFuturePaymentData();
            }
        });

        recordsSessionID = uuidv4(); // prevents old socket responses from being displayed
        const tzFrom = moment.tz(config.from.format('YYYY-MM-DD'), config.selectedTimezone).startOf('day').unix();
        const tzTo = moment.tz(config.to.format('YYYY-MM-DD'), config.selectedTimezone).endOf('day').unix();
        socket.emit('request-club-stripe-records', { clubID: selectedID, fromDate: tzFrom, toDate: tzTo, sessionID: recordsSessionID });
    };

    const fetchForecastedFuturePaymentData = () => {
        const fromDate = moment().tz(config.selectedTimezone).subtract(6, 'days').startOf('day').unix();
        const toDate = moment().tz(config.selectedTimezone).endOf('day').unix();
        forecastedFutureRecords = [];

        if (!loading.includes('forecasted-future-payouts-graph')) {
            toggleLoading($('#forecasted-future-payouts-graph'));
        }

        socket.emit('request-future-payouts', { clubID: selectedID, fromDate, toDate });

        // reset socket listeners in case of reconnection
        socket.off('request-future-payouts');
        socket.on('request-future-payouts', (data) => {
            if (data.error) {
                instantNotification(t('Unable to fetch forecasted future payouts'), 'error');
                return;
            }

            if (data.done) {
                forecastedFutureRecords = attachAppFeesToRecords(data.records);

                renderForecastedFuturePayoutsGraph();
                loadPendingPayoutSingleStatCounter();
            }
        });
    };

    const renderGraphsAndSummaryData = async () => {
        // Calculate payout totals
        payoutTotals = payouts.reduce((acc, payout) => {
            const value = acc[payout.currency] || 0;
            acc[payout.currency] = value + payout.amount;
            return acc;
        }, {});

        // Set payout totals decimal point
        Object.keys(payoutTotals).forEach((key) => {
            payoutTotals[key] = payoutTotals[key] / 100;
        });

        // Get totals per fee
        feesConfig.forEach((fc) => {
            fc.fees.map((fee) => {
                const succeededTotals = records.reduce((acc, record) => {
                    if (fc.dateEffective >= record.created || record.created > fc.dateExpired) return acc;
                    if (record.status !== 'succeeded') return acc;
    
                    let value = acc[record.currency] || 0;
                    const appFee = record.applicationFees.find((f) => f.name === fee.name);
    
                    if (appFee) acc[record.currency] = value + appFee.amount + appFee.tax;
                    return acc;
                }, {});
    
                const refundedTotals = records.reduce((acc, record) => {
                    if (fc.dateEffective >= record.created || record.created > fc.dateExpired) return acc;
                    const refunded = getRecordRefundedAmount(record);
                    if (record.status !== 'failed' || refunded <= 0) return acc;
    
                    let value = acc[record.currency] || 0;
                    const appFee = record.applicationFees.find((f) => f.name === fee.name);
    
                    if (appFee) acc[record.currency] = value + appFee.amount;
                    return acc;
                }, {});

                const foundFeesTotal = feesTotals[fee.name] || {
                    name: fee.name,
                    totals: {},
                }

                Object.keys(succeededTotals).forEach((currency) => {
                    const value = foundFeesTotal.totals[currency] || 0;
                    foundFeesTotal.totals[currency] = value + succeededTotals[currency];
                });
    
                feesTotals[fee.name] = foundFeesTotal;
    
                if (Object.keys(refundedTotals).length > 0) {
                    feesTotals[`${fee.id}:refunded`] = {
                        name: `${fee.name}: Refunded Payments`,
                        totals: refundedTotals,
                    };
                }
            });
        });

        renderSettlementSummaryTables();
        renderDashboardGraphs();
    };

    function payoutStatusLabels(status) {
        // https://stripe.com/docs/api/payouts/object#payout_object-status

        switch (status) {
            case 'paid':
                return `<div class="badge badge-green">${t('Paid out to Bank')}</div>`;
                break;
            case 'pending':
                return `<div class="badge badge-blue">${t('Pending Bank Submission')}</div>`;
                break;
            case 'in_transit':
                return `<div class="badge badge-blue">${t('In Transit to Bank')}</div>`;
                break;
            case 'canceled':
                return `<div class="badge badge-lightgrey">${t('Cancelled')}</div>`;
                break;
            case 'failed':
                return `<div class="badge badge-red">
                        ${t('Failed')}
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                    </div>`;
                break;
            default:
                return `<div class="badge badge-lightgrey">${status}</div>`;
        }
    }

    function getRecordCharges(record) {
        return (record.object === 'charge' ? [record] : record.charges?.data) ?? [];
    }

    function getRecordRefundedAmount(record) {
        const charges = getRecordCharges(record);
        return (charges ?? []).reduce((acc, charge) => acc + charge.amount_refunded, 0);
    }

    function overallStatusLabel(status, payout, record) {
        if (getRecordRefundedAmount(record) > 0) {
            return [t('Refunded'), `<div class="badge badge-orange">
                        ${t('Refunded')}
                        <i class="fa fa-undo" aria-hidden="true"></i>
                    </div>`];
        }

        switch (payout) {
            case undefined:
                if (record.fetchingPayout) {
                    return [t('Fetching'), `<div>
                                ${t('Fetching')}
                                <img src="/assets/images/tail-spin.svg" width="14px"></img>
                            </div>`];
                } else if (status == 'succeeded') {
                    return [t('Funds Clearing'), `<div class="badge badge-blue">
                                ${t('Funds Clearing')}
                                <i class="fa fa-refresh" aria-hidden="true"></i>
                            </div>`];
                } else {
                    return [t('Failed'), `
                        <div
                            class="badge badge-red"
                            title="${record.last_payment_error?.message ?? record.status}"
                        >
                            ${t('Failed')}
                            <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        </div>
                    `];
                }
            default:
                switch (payout.status) {
                    case 'paid':
                        return [t('Paid'), `<div class="badge badge-green">${t('Paid Out')} <i class="fa fa-check" aria-hidden="true"></i></div>`];
                    case 'pending':
                        return [t('Pending'), `<div class="badge badge-blue">${t('(Pending Bank Submission')} <i class="fa fa-university" aria-hidden="true"></i></div>`];
                    case 'in_transit':
                        return [t('In Transit'), `<div class="badge badge-blue">${t('In Transit to Bank')} <i class="fa fa-university" aria-hidden="true"></i></div>`];
                    case 'failed':
                        return [t('Failed'), `<div class="badge badge-red">${t('Failed (Contact HQ)')} <i class="fa fa-exclamation-triangle" aria-hidden="true"></i></div>`];
                    default:
                        return [status, `<div class="badge badge-lightgrey">${status}</div>`];
                }
        }
    }

    const renderPaymentsTable = async () => {
        $('#payments-table-container-loader').hide();
        loading = loading.filter((c) => c !== '#payments-table-container');
        if ($('#payments-table').length > 0) {
            $('#payments-table').DataTable().clear();
            $('#payments-table').DataTable().destroy();
        }

        const feesConfigHeaders = uniqueFeeNames.map(({ name, tax }) => {
            return `
                <th class="hide-lg">${t(name)}</th>
                <th class="hide-lg">${t(tax ?? 'Tax')}</th>
            `;
        }).join('');

        const chargeDateInfo = config.selectedTimezone === 'UTC'
            ? t(`Charge Dates listed are based on UTC timezone`)
            : `${t(`Charge Dates listed are based on your club's timezone`)} (${config.selectedTimezone})`;

        $('#payments-table-container').html(
            `<table id="payments-table" class="table ph-table table-hover dataTable no-footer">
                <thead>
                    <tr>
                        <th>${t('Total Collected')}</th>
                        <th class="hidden">${t('Currency')}</th>
                        <th>${t('Status')}</th>
                        <th class="hide-mobile">${t('Payment Method')}</th>
                        ${feesConfigHeaders}
                        ${unaccountedFeesColVisible ? '<th class="hide-lg">'+t('Unaccounted Fees')+'</th>' : ''}
                        <th>${t('Net Payout')}</th>
                        <th>
                            <span>${t('Charge Date')}</span>
                            <i
                                class="fa-regular fa-info-circle"
                                data-toggle="tooltip"
                                data-placement="right"
                                title="${chargeDateInfo}"
                            ></i>
                        </th>
                        <th class="xl-screen-only no-export">${t('Payout Batch ID')}</th>
                        <th class="hidden">${t('Internal ID')}</th>
                        <th class="hidden">${t('Payment Method ID')}</th>
                        <th class="hide-mobile">${t('Customer')}</th>
                        <th class="hidden">${t('Decline Reason')}</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>`
        );

        if (!records) return null;

        if (records.length <= 0) {
            return emptyDataMessage('#payments-table-container', 'show');
        }

        emptyDataMessage('#payments-table-container', 'hide');

        records.forEach((record) => {
            $('#payments-table tbody').append(paymentRecordRow(record));
        });
        $('#payments-table .btn-show-all-grouped-payouts').on('click', (e) => {
            e.stopPropagation();
            const value = $(e.currentTarget).html();
            $('.search-bar').val(value).trigger('input');
        });

        const disclaimer = {
            title: t('Please note the following:'),
            items: [
                `${t('This report has been exported using the timezone')}: ${config.selectedTimezone}`,
                `${t('Reporting Period')}: ${config.from.clone().format('YYYY/MM/DD')} - ${config.to.clone().format('YYYY/MM/DD')}`,
                t('Net payouts do not include funds currently pending clearance'),
                t('The total collected calculations do not include any failed payments'),
                t('Royalties and Payment Processing fees for failed payments have been included in total calculations shown, however you may be invoiced for these charges separately'),
                t('Please refer to the "Fee Schedule" page for detailed information about charges and FAQs before contacting our accounts department'),
            ],
            footer: t('If you have any concerns or queries relating to this report, please submit a support ticket via the Performance Hub.'),
        }

        const exportFormat = {
            body: function (data, row, column, node) {
                return $(node).attr('data-export') ?? data;
            }
        };

        let export_filename = selectedID + '_Member-Payments-&-Royalties__' + new Date().toISOString();
        let phLogoBase64 = await fetchImageToBase64('/assets/images/ph-logo.png');

        let dtConfig = {
            responsive: true,
            iDisplayLength: 50,
            dom: '<"toolbar">Brtip',
            order: [[9, 'desc']],
            language: {
                paginate: {
                    previous: t('Previous'),
                    next: t('Next'),
                },
                infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
                info: t('Showing') + ' _START_ ' + t('to') + ' _END_ ' + t('of') + ' _TOTAL_ ' + t('entries'),
                emptyTable: t('No data available in table'),
                zeroRecords: t('No matching records found'),
                infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
            },
            buttons: [

                // ---------------------------------------------------------------------------
                // COPY TO CLIPBOARD
                // ---------------------------------------------------------------------------

                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default',
                    exportOptions: { format: exportFormat },
                },

                // ---------------------------------------------------------------------------
                // PRINT EXPORT
                // ---------------------------------------------------------------------------

                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: '',
                    className: 'btn btn-sm btn-default',
                    orientation: 'landscape',
                    // messageTop: `${t('Member Payments & Royalties Export For')}: ${selectedID} \n ${t('Date Range')}: ${config.from.clone().format('YYYY/MM/DD')} - ${config.to.clone().format('YYYY/MM/DD')}`,
                    messageTop: function () {
                        return `
                            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                                <img src="${phLogoBase64}" style="height: 30px; margin-right: 10px;" />
                                <div style="flex-grow: 1; text-align: center;">
                                    <h2 style="margin: 0; font-size: 20px;">${t('Member Payments & Royalties Export For')}: ${selectedID}</h2>
                                    <p style="margin: 0; font-size: 18px;">${t('Date Range')}: ${config.from.clone().format('YYYY/MM/DD')} - ${config.to.clone().format('YYYY/MM/DD')}</p>
                                </div>
                            </div>
                        `;
                    },
                    messageBottom: `<br/>
                        <p>${disclaimer.title}</p>
                        <ul>${disclaimer.items.map((item) => `<li>${item}</li>`).join('')}</ul>
                        <p>${disclaimer.footer}</p>
                    `,
                    exportOptions: { format: exportFormat },
                    customize: function (win) {

                        // Extract column indices based on the header row
                        const headerRow = $(win.document.body).find('thead tr').first();
                        const statusColIndex = headerRow.find('th').filter((_, th) => $(th).text().trim() === t('Status')).index();
                        const internalIdColIndex = headerRow.find('th').filter((_, th) => $(th).text().trim() === t('Internal ID')).index();
                        const paymentMethodIdColIndex = headerRow.find('th').filter((_, th) => $(th).text().trim() === t('Payment Method ID')).index();

                        // Add margin to the whole body
                        $(win.document.body).css({
                            margin: '10px'
                        });

                        // Apply alternating row colors and red background for specific rows
                        $(win.document.body).find('tbody tr').each(function (index, row) {
                            // Apply alternating row colors
                            if (index % 2 === 0) {
                                $(row).css('background-color', 'white');
                            } else {
                                $(row).css('background-color', '#fbfbfb');
                            }

                            // Check if the row should have a red background (e.g., "Failed" status)
                            const statusCell = $(row).find('td').eq(statusColIndex); // Adjust the index based on actual column for status
                            if (statusCell && statusCell.text().includes(t('Failed'))) {
                                $(row).css('background-color', '#fce5ef');
                                $(row).css('color', '#9f2237');
                            }
                        });

                        // Insert an empty row before the TOTALS row
                        $(win.document.body).find('tbody').append('<tr><td colspan="100%"></td></tr>');

                        getExportTotals().forEach((totals) => {
                            // Create 'TOTALS' label row
                            const totalColumns = 10 + totals.appFees.length * 2 + totals.unaccountedFees.length;
                            const rowHeader = `
                                <tr>
                                    <td colspan="${totalColumns}" style="font-weight: bold; background-color: #f3f3f3; color: #333333;">${t('TOTALS')}</td>
                                </tr>
                            `;
                            $(win.document.body).find('tbody').append(rowHeader);

                            // Create actual totals row
                            const rowTotals = `
                                <tr>
                                    <td>${totals.collected}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    ${totals.appFees.map(({ total, tax }) => `<td>${tax}</td><td>${total}</td>`).join('')}
                                    ${totals.unaccountedFees.map((total) => `<td>${total}</td>`).join('')}
                                    <td>${totals.netPayout}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            `;
                            $(win.document.body).find('tbody').append(rowTotals);
                        });
                    },

                },

                // ---------------------------------------------------------------------------
                // CSV EXPORT
                // ---------------------------------------------------------------------------

                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv',
                    exportOptions: { format: exportFormat },
                    customize: function (csv) {
                        // Append totals rows to the CSV
                        getExportTotals().forEach((totals) => {
                            const appFeesCols = totals.appFees.reduce((acc, { total, tax }) => {
                                acc.push(`"${tax}"`);
                                acc.push(`"${total}"`);
                                return acc;
                            }, []);

                            // Calculate total number of columns
                            const numAppFeesCols = appFeesCols.length;
                            const numUnaccountedFeesCols = totals.unaccountedFees.length;
                            const staticCols = 10; // Fixed columns count
                            const totalColumns = staticCols + numAppFeesCols + numUnaccountedFeesCols;

                            // Insert an empty line before the TOTALS row
                            let rowEmptyLine = `"",${new Array(totalColumns).fill('""').join(',')}`;
                            csv += `\n${rowEmptyLine}\n`;

                            // New row for 'TOTALS' label
                            let rowHeader = `"${t('Totals')}",${new Array(totalColumns).fill('""').join(',')}`;
                            csv += `\n${rowHeader}\n`;

                            // New row for actual totals
                            let rowTotals = [
                                `"${totals.collected}"`,   // Total Collected
                                '""',                      // Currency (assuming it's empty)
                                '""',                      // Status (assuming it's empty)
                                '""',                      // Payment Method (assuming it's empty)
                                ...appFeesCols,
                                ...totals.unaccountedFees.map(value => `"${value}"`),
                                `"${totals.netPayout}"`,   // Net Payout
                                '""',                      // Charge Date (assuming it's empty)
                                '""',                      // Internal ID with truncation (assuming it's empty)
                                '""',                      // Payment Method ID with truncation (assuming it's empty)
                                '""'                       // Customer (assuming it's empty)
                                // Add more columns if needed
                            ].join(',');
                            csv += `${rowTotals}\n`;
                        });

                        // Append disclaimer to the CSV
                        csv += `,\n"${disclaimer.title}",\n`;
                        csv += `${disclaimer.items.map((item) => `"• ${item}"`).join(',\n')},\n`;
                        csv += `"${disclaimer.footer}",\n`;

                        return csv;
                    },
                },

                // ---------------------------------------------------------------------------
                // PDF EXPORT
                // ---------------------------------------------------------------------------

                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    filename: export_filename,
                    extension: '.pdf',
                    orientation: 'landscape',
                    pageSize: 'A4',
                    exportOptions: {
                        columns: "thead th:not(.no-export)",
                        format: exportFormat,
                    },
                    title: null,
                    messageBottom: `
                        ${disclaimer.title}\n
                        ${disclaimer.items.map((item) => `• ${item}`).join('\n')}\n
                        ${disclaimer.footer}
                    `,
                    customize: function (doc) {
                        // Ensure doc.content is initialized
                        if (!doc.content || !Array.isArray(doc.content)) {
                            doc.content = [];
                        }

                        // Add the logo and title to the top of the document
                        if (phLogoBase64) {
                            doc.content.unshift({
                                columns: [
                                    {
                                        image: phLogoBase64,
                                        width: 100,
                                        margin: [0, 0, 0, 10], // Adjust margin as needed
                                        relativePosition: { x: 0, y: 10 }, // Move image 20 units down
                                    },
                                    {
                                        text: `${t('Member Payments & Royalties Export For')}: ${selectedID} \n ${t('Date Range')}: ${config.from.clone().format('YYYY/MM/DD')} - ${config.to.clone().format('YYYY/MM/DD')}`,
                                        alignment: 'center',
                                        bold: true,
                                        margin: [10, 0, 0, 0], // Adjust the left margin to align with the logo
                                        style: 'title',
                                        fontSize: 13,
                                    }
                                ]
                            });
                        }

                        // General Styles
                        doc.styles.tableHeader = {
                            fillColor: '#f3f3f3', // Light grey background
                            color: '#333333', // Dark grey text
                            alignment: 'left', // Left-aligned text
                            fontSize: 7, // Reduced font size for header
                            bold: true // Bold headings
                        };
                        doc.defaultStyle = {
                            fontSize: 7, // Reduced font size for body
                        };

                        // Set page margins
                        doc.pageMargins = [20, 20, 20, 20];

                        // Full-width table layout
                        if (doc.content[1] && doc.content[1].layout) {
                            doc.content[1].layout = {
                                hLineWidth: function (i, node) {
                                    return (i === 0 || i === node.table.body.length) ? 1 : 0.5;
                                },
                                vLineWidth: function () { return 0; },
                                hLineColor: function (i, node) {
                                    return (i === 0 || i === node.table.body.length) ? '#333333' : '#cccccc';
                                },
                                paddingLeft: function () { return 3; }, // Reduced padding
                                paddingRight: function () { return 3; }, // Reduced padding
                                paddingTop: function () { return 1; }, // Reduced padding
                                paddingBottom: function () { return 1; } // Reduced padding
                            };
                        }

                        // Make row red if payment failed
                        const [headerRow, ...rows] = doc.content[1].table.body;
                        const statusColIndex = headerRow.findIndex((cell) => cell.text === t('Status'));
                        const internalIdColIndex = headerRow.findIndex((cell) => cell.text === t('Internal ID'));
                        const paymentMethodIdColIndex = headerRow.findIndex((cell) => cell.text === t('Payment Method ID'));

                        rows.forEach((row, i) => {
                            // Alternate row background color
                            const isEvenRow = i % 2 === 0;
                            row.forEach((cell) => {
                                cell.fillColor = isEvenRow ? 'white' : '#fbfbfb';
                            });

                            // Truncate Internal ID and Payment Method ID
                            if (internalIdColIndex !== -1 && row[internalIdColIndex]) {
                                row[internalIdColIndex].text = truncate(row[internalIdColIndex].text);
                            }
                            if (paymentMethodIdColIndex !== -1 && row[paymentMethodIdColIndex]) {
                                row[paymentMethodIdColIndex].text = truncate(row[paymentMethodIdColIndex].text);
                            }

                            // Set cell background if failed
                            if (!doc.content[1].table.body[i + 1][statusColIndex].text.includes(t('Failed'))) return;
                            doc.content[1].table.body[i + 1].forEach((cell) => {
                                cell.fillColor = '#fce5ef';
                                cell.color = '#9f2237';
                            });
                        });

                        getExportTotals().forEach((totals) => {
                            const appFeesCols = totals.appFees.reduce((acc, { total, tax }) => {
                                acc.push({ text: tax });
                                acc.push({ text: total });
                                return acc;
                            }, []);

                            // Calculate total number of columns
                            const numAppFeesCols = appFeesCols.length;
                            const numUnaccountedFeesCols = totals.unaccountedFees.length;
                            const staticCols = 10; // Fixed columns count
                            const totalColumns = staticCols + numAppFeesCols + numUnaccountedFeesCols;

                            // New row for 'TOTALS'
                            const rowHeader = [
                                { text: t('Totals'), colSpan: totalColumns, alignment: 'left', bold: true, fillColor: '#f3f3f3', color: '#333333' }
                            ];
                            for (let i = 1; i < totalColumns; i++) rowHeader.push({ text: '', fillColor: '#f3f3f3', color: '#333333' }); // Fill the remaining columns

                            doc.content[1].table.body.push(rowHeader);

                            const rowTotals = [
                                { text: totals.collected },     // Total Collected
                                { text: '' },                   // Currency
                                { text: '' },                   // Status
                                { text: '' },                   // Payment Method
                                ...appFeesCols,
                                ...totals.unaccountedFees.map((total) => ({ text: total })),
                                { text: totals.netPayout },     // Net Payout
                                { text: '' },                   // Charge Date
                                { text: '' },                   // Internal ID with truncation
                                { text: '' },                   // Payment Method ID with truncation
                                { text: '' },                   // Customer
                                { text: '' },                   // Decline Reason
                            ].map((row) => ({ ...row, fillColor: '#f3f3f3', color: '#333333', bold: true })); // Styling similar to header

                            doc.content[1].table.body.push(rowTotals);
                        });

                        // Footer styling for messageBottom
                        const messageBottom = doc.content.find(item => item.text && item.text.includes('Please note the following:'));
                        if (messageBottom) {
                            messageBottom.margin = [0, 10, 0, 0]; // Adjust margin as needed
                            messageBottom.alignment = 'left'; // Left align footer text
                            messageBottom.style = 'footer'; // Apply footer style
                        }

                        // Add a footer style
                        doc.styles.footer = {
                            fontSize: 6,
                            italics: true
                        };

                        // Truncation function for IDs
                        function truncate(text) {
                            if (!text) return ''; // Return an empty string if text is undefined or null
                            return text.length > 16 ? text.substring(0, 16) + '...' : text;
                        }

                        return doc;
                    },
                }

            ],
            bAutoWidth: false,
            // Draw powered by stripe logo
            fnDrawCallback: function (oSettings) {
                // Check if the custom text already exists, to avoid duplicates
                if ($('#poweredByStripe').length === 0) {
                    // Inject custom text or HTML below the pagination controls
                    $(oSettings.nTableWrapper).find('.dataTables_info').after(
                        '<div id="poweredByStripe"><img style="margin-top: 5px; height: 24px; filter: invert(47%) sepia(14%) saturate(433%) hue-rotate(180deg) brightness(92%) contrast(90%);" src="/assets/images/powered-by-stripe.svg"></div>'
                    );
                }
            }

        };

        paymentsTable = $('#payments-table').DataTable(dtConfig);
        wrapTableWithScrollableDiv('#payments-table');

        $(document).on('click', '.open-record-dialog', (e) => {
            const { id } = $(e.currentTarget).data();
            const record = records.find((r) => r.id === id);
            record && recordInfoDialog(record);
        });

        $('.search-bar').on('input change', (e) => {
            const search = $(e.currentTarget).val();
            config.search = search;
            updateHrefQuery({ filter: search });
            paymentsTable?.search(search).draw();
        });

        paymentsTable?.search(config.search ?? '').draw(); 
    };

    function getTableIndexByHeader(headerText) {
        return paymentsTable?.columns().header().toArray().findIndex((header) => header.innerText === headerText) ?? -1;
    }

    function getExportTotals() {
        if (!paymentsTable) return;
        const appFeesLength = uniqueFeeNames.length;
        const currencyColIndex = getTableIndexByHeader(t('Currency'));
        const unaccountedFeesColIndex = getTableIndexByHeader(t('Unaccounted Fees'));
        const netPayoutColIndex = getTableIndexByHeader(t('Net Payout'));
        const totalCollectedColIndex = getTableIndexByHeader(t('Total Collected'));
        const rows = paymentsTable.rows({ filter: 'applied' }).data().toArray();

        return currencies.map((currency) => {
            const visibleData = [];
            rows.forEach((row) => {
                if (row[currencyColIndex] !== currency) return;
                visibleData.push(row);
            });

            if (visibleData.length <= 0) return;

            const totalCollected = visibleData.reduce((acc, row) => {
                if (rowHasStatus(row, t('Failed'))) return acc;
                return acc + Number(row[totalCollectedColIndex]['@data-sort'].replace(/\.|\,/g, ''));
            }, 0).formatStripeCurrency(currency, false)

            const appFeeTotals = [];
            for (let appFeeIndex = 0; appFeeIndex < appFeesLength; appFeeIndex++) {
                const colIndex = appFeeIndex * 2; // 2 columns per fee (fee and tax)
                visibleData.forEach((row) => {
                    if (rowHasStatus(row, t('Failed'))) return;
                    const total = (row[currencyColIndex + 2 + colIndex]['@data-sort'] ?? '0').replace(',', '');
                    const tax = (row[currencyColIndex + 3 + colIndex]['@data-sort'] ?? '0').replace(',', '');

                    const { total: curTotal, tax: curTax } = appFeeTotals[appFeeIndex] || { total: 0, tax: 0 };
                    const newTotal = curTotal + parseFloat(total);
                    const newTax = curTax + parseFloat(tax);

                    appFeeTotals[appFeeIndex] = { total: newTotal, tax: newTax };
                });
            }

            // format app fees
            appFeeTotals.forEach((fee) => {
                fee.total = fee.total.formatStripeCurrency(currency, false);
                fee.tax = fee.tax.formatStripeCurrency(currency, false);
            });

            const unaccountedFeesTotalCol = unaccountedFeesColVisible
                ? visibleData
                    .reduce((acc, row) => {
                        if (rowHasStatus(row, t('Failed'))) return acc;
                        const unaccountedFees = (row[unaccountedFeesColIndex]['@data-sort'] ?? 0).replace(',', '');
                        const value = parseFloat(acc[0] || 0);
                        acc[0] = (value + parseFloat(unaccountedFees));
                        return acc;
                    }, [0])
                    .map((total) => total.formatCurrency(2))
                : [];


            let netPayoutTotal = visibleData.reduce((acc, row) => {
                const failed = rowHasStatus(row, t('Failed'));
                const pending = rowHasStatus(row, t('Funds Clearing'));
                if (failed || pending) return acc;
                const netPayout = (row[netPayoutColIndex]['@data-sort'] ?? '0').replace(',', '');
                return acc + Number(netPayout);
            }, 0)
            
            netPayoutTotal = (netPayoutTotal * 100).formatStripeCurrency(currency, false);

            const totals = {
                collected: totalCollected,
                currency,
                appFees: appFeeTotals,
                unaccountedFees: unaccountedFeesTotalCol,
                netPayout: netPayoutTotal
            }

            return totals;
        });
    }

    const renderSettlementSummaryTables = () => {
        $('#settlement-summary-container-loader').hide();
        loading = loading.filter((c) => c !== '#settlement-summary-container');

        $('#settlement-summary-container').html(`
            <div>
                <h3>
                    <span>${t('Settlement Summary for payments between')}:</span>
                    <span>${config.from.clone().format('YYYY/MM/DD')} - ${config.to.clone().format('YYYY/MM/DD')}</span>
                </h3>
            </div>
        `);

        if (records.length <= 0 && payouts.length <= 0) {
            return emptyDataMessage('#settlement-summary-container', 'show');
        }

        emptyDataMessage('#settlement-summary-container', 'hide');

        $('#settlement-summary-container div').append(`
          <div class="tab-content-overview__table-wrapper">
            <table class="tab-content-overview__table">
                <thead>
                    <tr>
                        <th>${t('Funds Processed')}</th>
                        <th class="text-right">${t('Amount')}</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys(recordSummaryTotals)
                        .map((totalKey) => {
                            const total = recordSummaryTotals[totalKey];
                            if (['refunded'].includes(totalKey) && Object.keys(total.currencies).length <= 0) return '';

                            return `
                                <tr>
                                    <td>${total.label}</td>
                                    <td>
                                        ${Object.keys(total.currencies)
                                            .map((currency) => {
                                                const currencyTotal = total.currencies[currency]
                                                    .formatStripeCurrency(currency);

                                                return `
                                                    <div class="d-flex justify-end">
                                                        <span>${currencyTotal}</span>
                                                    </div>
                                                `;
                                            }).join('') || `<div class="d-flex justify-end">N/A</div>`}
                                    </td>
                                </tr>
                            `;
                        })
                        .join('')}
                </tbody>
            </table>
          </div>
        `);

        $('#settlement-summary-container').append(`
          <div class="tab-content-overview__table-wrapper">
            <table class="tab-content-overview__table">
                <thead>
                    <tr>
                        <th>${t('Fees & Charges')}</th>
                        <th class="text-right">${t('Amount')}</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys(feesTotals)
                        .map((key) => {
                            const feesTotal = feesTotals[key];
                            return `
                                <tr>
                                    <td>${t(feesTotal.name)}</td>
                                    <td>
                                        ${Object.keys(feesTotal.totals)
                                            .map((currency) => {
                                                const currencyTotal = (feesTotal.totals[currency] * 100)
                                                    .formatStripeCurrency(currency);

                                                return `
                                                    <div class="d-flex justify-end">
                                                        <span>${currencyTotal}</span>
                                                    </div>
                                                `
                                            }).join('') || `<div class="d-flex justify-end">N/A</div>`}
                                    </td>
                                </tr>
                            `;
                        })
                        .join(' ')}
                </tbody>
            </table>
          </div>
        `);

        $('#settlement-summary-container').append(`
          <div class="tab-content-overview__table-wrapper">
            <table class="tab-content-overview__table">
                <thead>
                    <tr>
                        <th>Payouts</th>
                        <th class="text-right">${t('Date of Payout')}</th>
                        <th class="text-right">${t('Amount')}</th>
                    </tr>
                </thead>
                <tbody>
                    ${payouts
                        .sort((a, b) => b.date - a.date)
                        .map((payout) => {
                            const hasRecords = records.some(
                                (record) => record.payout && record.payout.id === payout.id
                            );

                            return `
                                <tr>
                                    <td class="condensed">
                                        ${
                                            !hasRecords
                                                ? `
                                                    <span class="shorten-text payout-id">${payout.id}</span>
                                                    <i
                                                        class="material-icons info-icon"
                                                        data-toggle="tooltip"
                                                        data-placement="right"
                                                        title="${t('This Payout contains payments from before')} ${config.from.format(
                                                            'LL'
                                                        )} ${t('which are not available in this reporting range. Please adjust your dates to see these payments')}"
                                                        style="font-size: 15px;"
                                                    >
                                                        info_outline
                                                    </i>
                                                `
                                                : `
                                                    <div class="payout-id shorten-text">
                                                        <a class="btn-show-all-grouped-payouts clickable">${payout.id}</a>
                                                    </div>
                                                `
                                        }
                                    </td>
                                    <td class="text-right condensed">
                                        ${moment.unix(payout.created)
                                            .tz(config.selectedTimezone)
                                            .format('YYYY/MM/DD')
                                        }
                                    </td>
                                    <td class="condensed">
                                        <span class="d-flex justify-end">
                                            <span>${payout.amount.formatStripeCurrency(payout.currency)}</span>
                                        </span>
                                    </td>
                                </tr>
                            `;
                        })
                        .join('')}
                        <tr>
                            <td style="font-weight: bold;">${t('Total paid out to your nominated account')}</td>
                            <td style="font-weight: bold;" colspan="2">
                                ${Object.keys(payoutTotals)
                                    .map(
                                        (currency) => `
                                            <div class="d-flex justify-end">
                                                <span>${(payoutTotals[currency] * 100).formatStripeCurrency(currency)}</span>
                                            </div>
                                        `
                                    )
                                    .join('')}
                            </td>
                        </tr>
                </tbody>
            </table>
          </div>
        `);

        wrapTableWithScrollableDiv('#settlement-summary-container table');
        $('#settlement-summary-container .btn-show-all-grouped-payouts').on('click', (e) => {
            const value = $(e.currentTarget).html();
            $('.search-bar').val(value).trigger('input');
            $('#payments-panel-tab').trigger('click');
        });

        const failedPayments = records.reduce(
            (acc, record) => {
                if (!record.last_payment_error) return acc;
                const { code } = record.last_payment_error;

                if (['card_declined', 'expired_card'].includes(code)) {
                    acc[code].count += 1;
                } else {
                    acc.other.count += 1;
                }

                return acc;
            },
            {
                expired_card: { count: 0, label: t('Expired Card') },
                card_declined: { count: 0, label: t('Card Declined') },
                other: { count: 0, label: 'Other' },
            }
        );

        $('#settlement-summary-container').append(`
          <div class="tab-content-overview__table-wrapper">
            <table class="tab-content-overview__table">
                <thead>
                    <tr>
                        <th>${t('Failed Transactions')}</th>
                        <th class="text-right">${t('Count')}</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys(failedPayments)
                        .map((key) => {
                            return `
                                <tr>
                                    <td><a class="clickable btn-show-group-failed-payments" data-code="${key}">${t(failedPayments[key].label)}</a></td>
                                    <td class="text-right"><a class="clickable btn-show-group-failed-payments" data-code="${key}">${failedPayments[key].count}</a></td>
                                </tr>
                            `;
                        })
                        .join('')}
                </tbody>
            </table>
          </div>
        `);

        $('#settlement-summary-container .btn-show-group-failed-payments').on('click', (e) => {
            const value = $(e.currentTarget).data('code');
            $('.search-bar').val(`failed_payment:${value}`).trigger('input');
            $('#payments-panel-tab').trigger('click');
        });

        // Info tooltip(s) needs to be rendered once we've drawn the settlement summaries
        $('[data-toggle="tooltip"]').tooltip({
            container: '#settlement-summary-container',
        });
    };

    const loadPendingPayoutSingleStatCounter = () => {
        const totals = currencies.reduce((acc, currency) => {
            const filteredRecords = forecastedFutureRecords.filter(
                (record) => recordHasFuturePayout(record) && record.currency === currency
            );

            acc[currency] = filteredRecords.reduce((acc, record) => {
                return acc + record.netAmount
            }, 0);

            return acc;
        }, {});

        const filteredTotals = Object.keys(totals).filter((currency) => totals[currency] > 0);

        const elements = filteredTotals.map((currency) => {
            const showCurrency = filteredTotals.length > 1
                ? `<small style="text-transform: uppercase; font-size: 14px;">(${currency})</small>`
                : '';
            return `<div>${(totals[currency] * 100).formatStripeCurrency(currency)}${showCurrency}</div>`
        })
        .join(' ') || 'N/A'

        $('#pending-payouts-stat-counter .number').html(elements);
        toggleLoading($('#pending-payouts-stat-counter'), false);
    };

    const renderDashboardGraphs = () => {
        $('#dashboard-container-loader').hide();
        loading = loading.filter((c) => c !== '#dashboard-container');

        if (records.length <= 0 && payouts.length <= 0) {
            return emptyDataMessage('#dashboard-container', 'show');
        }

        emptyDataMessage('#dashboard-container', 'hide');

        const singleStatCounters = [
            {
                title: t('Total Payments'),
                info: t('Total amount of payouts during selected reporting period'),
                getValue: () => {
                    return Object.keys(recordSummaryTotals.collected.currencies)
                        .map((currency) => {
                            const total = recordSummaryTotals.collected.currencies[currency];
                            return `<div>${total.formatStripeCurrency(currency)}</div>`;
                        })
                        .join(' ') || 'N/A';
                },
                color: 'blue',
                icon: 'total-payments'
            },
            {
                title: t('Charges & Fees'),
                info: t('Total amount of charges and fees during selected reporting period'),
                getValue: () => {
                    const currencyTotals = Object.keys(feesTotals).reduce((acc, key) => {
                        const feesTotal = feesTotals[key];

                        Object.keys(feesTotal.totals).forEach((currency) => {
                            let value = acc[currency] || 0;
                            value += feesTotal.totals[currency];
                            acc[currency] = value;
                        });

                        return acc;
                    }, {});

                    return Object.keys(currencyTotals)
                        .map((currency) => {
                            const total = currencyTotals[currency] * 100;
                            return `<div>${total.formatStripeCurrency(currency)}</div>`
                        })
                        .join(' ') || 'N/A';
                },
                color: 'red',
                icon: 'charges'
            },
            {
                id: 'pending-payouts-stat-counter',
                title: t('Pending Payouts'),
                info: t('Funds still awaiting clearance before being paid to your account'),
                getValue: () => `<div>0</div>`,
                color: 'green',
                icon: 'pending-payouts'
            },
            {
                title: t('Unique Members Charged'),
                info: t('Total number of unique customers charged during selected reporting period'),
                getValue: () =>
                    records.reduce((acc, record) => {
                        if (record.customer?.id && !acc.includes(record.customer.id)) {
                            acc.push(record.customer.id);
                        }
                        return acc;
                    }, []).length || 'N/A',
                color: 'pink',
                icon: 'unique-members'
            },
            {
                title: t('Avg Member Spend'),
                info: t('The average amount your members spend (per charge/payment) during the selected reporting period'),
                getValue: () => {
                    const totalSpending = records.reduce((acc, record) => {
                        const value = acc[record.currency] || { amount: 0, count: 0 };
                        value.amount += record.amount;
                        value.count += 1;
                        acc[record.currency] = value;
                        return acc;
                    }, {});

                    return Object.keys(totalSpending)
                        .map((currency) => {
                            const { amount, count } = totalSpending[currency];

                            return `
                                    <div>
                                        ${(amount / count).formatStripeCurrency(currency)}
                                    </div>
                                `;
                        })
                        .join(' ') || 'N/A';
                },
                color: 'yellow',
                icon: 'avg-spend'
            },
        ];

        $('#dashboard-container').html(`
            <div class="the-info-box__row lg" style="margin-bottom: 2rem;">
                ${singleStatCounters
                    .map(
                        (stat) => `
                            <div ${stat.id ? `id="${stat.id}"` : ''} class="w gm-api ph-the-info-box the-info-box" id="currentMembers">
                              <div class="the-info-box__data">
                                <div class="the-info-box__icon__wrapper the-info-box__icon__wrapper--stroked the-info-box__icon__wrapper--stroked--${stat.color} use-skeleton">
                                  <i class="the-info-box__icon the-info-box__icon--${stat.icon}"></i>
                                </div>
                                <div class="the-info-box__name__wrapper">
                                  <div class="the-info-box__name">
                                    <h6><span class="use-skeleton isInlineBlock rounded">${stat.title}</span></h6><i class="the-info-box__info" data-toggle="tooltip" data-placement="right" title="${stat.info}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                                  </div>
                                  <div class="the-info-box__content">
                                    <div class="number use-skeleton isInlineBlock rounded">${stat.getValue()}</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                        `
                    )
                    .join(' ')}
            </div>
            <div class="dashboard-section revenue__section col-2">
              <div id="gross-volume" class="the-info-box ph-the-info-box the-info-box--lg">
                <div class="the-info-box__header the-info-box__header--bordered">
                  <div class="the-info-box__header__content">
                    <div class="the-info-box__header__content-col-left">
                      <h3>
                        <span class="use-skeleton rounded">${t('Gross Volume')}</span>
                      </h3>
                    </div>
                    <div class="the-info-box__header__content-col-right">
                      <i
                        class="the-info-box__info the-info-box__info--relative"
                        data-toggle="tooltip"
                        data-placement="right"
                        title="${t('Total of your payments INCLUDING any Fees & Charges during the selected Reporting Period')}"
                        ><span
                          class="use-skeleton use-skeleton--absolute rounded--full"
                        ></span
                      ></i>
                    </div>
                  </div>
                </div>
                <div class="the-info-box__body body">
                  <div class="upcoming-bookings-widget-chart use-skeleton rounded">
                    <div id="gross-volume-graph" style="position: relative; height: 300px;">
                        <canvas></canvas>
                    </div>
                  </div>
                </div>
              </div>

              <div id="net-payout" class="the-info-box ph-the-info-box the-info-box--lg">
                <div class="the-info-box__header the-info-box__header--bordered">
                  <div class="the-info-box__header__content">
                    <div class="the-info-box__header__content-col-left">
                      <h3>
                        <span class="use-skeleton rounded">${t('Net Payouts')}</span>
                      </h3>
                    </div>
                    <div class="the-info-box__header__content-col-right">
                      <i
                        class="the-info-box__info the-info-box__info--relative"
                        data-toggle="tooltip"
                        data-placement="right"
                        title="${t('Total of your payouts minus any Fees & Charges during the selected Reporting Period')}"
                        ><span
                          class="use-skeleton use-skeleton--absolute rounded--full"
                        ></span
                      ></i>
                    </div>
                  </div>
                </div>
                <div class="the-info-box__body body">
                  <div class="upcoming-bookings-widget-chart use-skeleton rounded">
                    <div id="net-payouts-graph" style="position: relative; height: 300px;">
                        <canvas></canvas>
                    </div>
                  </div>
                </div>
              </div>

              <div id="successful-and-failed-payments" class="the-info-box ph-the-info-box the-info-box--lg">
                <div class="the-info-box__header the-info-box__header--bordered">
                  <div class="the-info-box__header__content">
                    <div class="the-info-box__header__content-col-left">
                      <h3>
                        <span class="use-skeleton rounded">${t('Successful/Failed Payments')}</span>
                        <i
                          class="the-info-box__info the-info-box__info--relative"
                          data-toggle="tooltip"
                          data-placement="right"
                          title="${t('Breakdown of successful/failed of payments during the selected Reporting Period')}"
                          ><span
                            class="use-skeleton use-skeleton--absolute rounded--full"
                          ></span
                        ></i>
                      </h3>
                    </div>
                    <div class="the-info-box__header__content-col-right">
                      <button class="btn btn-danger btn-outline normal-case waves-effect show-all-failed-payments use-skeleton"><s class="btn__icon custom-icon" aria-hidden="true">
                        <s class="fa fa-exclamation-triangle" aria-hidden="true"></s>
                      </s> <span>${t('View Failed Payments')}</span></button>
                    </div>
                  </div>
                </div>
                <div class="the-info-box__body body">
                  <div class="upcoming-bookings-widget-chart use-skeleton rounded">
                    <div id="number-of-payments-graph" style="position: relative; height: 300px;">
                        <canvas></canvas>
                    </div>
                  </div>
                </div>
              </div>

              <div id="forecasted-payouts" class="the-info-box ph-the-info-box the-info-box--lg">
                <div class="the-info-box__header the-info-box__header--bordered">
                  <div class="the-info-box__header__content">
                    <div class="the-info-box__header__content-col-left">
                      <h3>
                        <span class="use-skeleton rounded">${t('Forecasted Future Payouts')}</span>
                        <i
                          class="the-info-box__info the-info-box__info--relative"
                          data-toggle="tooltip"
                          data-placement="right"
                          title="${t('Estimated upcoming Payouts that be paid to your nominated account once funds have been settled. Note values are best effort estimations based on your payments that have not yet been settled')}"
                          ><span
                            class="use-skeleton use-skeleton--absolute rounded--full"
                          ></span
                        ></i>
                      </h3>
                    </div>
                    <div class="the-info-box__header__content-col-right">
                      <button class="btn btn-outline normal-case waves-effect show-all-clearing-payments use-skeleton"><s class="btn__icon  custom-icon" aria-hidden="true">
                        <s class="fa fa-university" aria-hidden="true"></s>
                      </s> <span>${t('Payouts Pending Clearance')}</span></button>
                    </div>
                  </div>
                </div>
                <div class="the-info-box__body body">
                  <div class="upcoming-bookings-widget-chart use-skeleton rounded">
                    <div id="forecasted-future-payouts-graph" style="position: relative; height: 300px; width: 100%;">
                        <canvas></canvas>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            
        `);

        // Info tooltips needs to be rendered on dashboard once the elements have been created
        $('[data-toggle="tooltip"]').tooltip({
            container: '#dashboard-container',
        });

        toggleLoading($('#pending-payouts-stat-counter'));

        $('.show-all-failed-payments').on('click', () => {
            $('.search-bar').val(t('Failed')).trigger('input');
            $('#payments-panel-tab').trigger('click');
        });

        $('.show-all-clearing-payments').on('click', () => {
            $('.search-bar').val(t('Funds Clearing')).trigger('input');
            $('#payments-panel-tab').trigger('click');
        });

        renderGrossVolumeGraph();
        renderNetPayoutsGraph();
        renderNumberOfPaymentsGraph();
    };

    const renderGrossVolumeGraph = () => {
        const periods = getGraphPeriods();

        const data = periods.reduce((acc, period) => {
            const periodRecords = records.filter((record) =>
                period.from === period.to
                    ? record.created === period.from
                    : record.created >= period.from && record.created <= period.to
            );

            currencies.forEach((currency) => {
                if (!acc[currency]) {
                    acc[currency] = {};
                }

                const totalAmount = periodRecords
                    .reduce((acc, record) => {
                        if (record.currency !== currency) return acc;
                        return acc + record.amount;
                    }, 0)
                    .formatStripeCurrency(currency, false)
                    .replace(',', '');

                acc[currency][period.label] = { ...period, totalAmount };
            });

            return acc;
        }, {});

        const colors = [
            { border: 'rgb(33, 150, 243)', background: 'rgba(33, 150, 243, 0.2)' },
            { border: 'rgb(188, 131, 238)', background: 'rgba(188, 131, 238, 0.2)' },
            { border: 'rgb(255, 106, 183)', background: 'rgba(255, 106, 183, 0.2)' },
            { border: 'rgb(255, 118, 104)', background: 'rgba(255, 118, 104, 0.2)' },
        ];

        const datasets = Object.keys(data).map((currency, index) => ({
            label: currency,
            data: Object.keys(data[currency]).map((period) => data[currency][period].totalAmount),
            borderColor: colors[index].border,
            borderWidth: 1,
            fill: true,
            backgroundColor: createChartGradient,
            tension: 0.3,
        }));

        if (!$('#gross-volume-graph > canvas').length) return;
        const ctx = $('#gross-volume-graph > canvas')[0].getContext('2d');
        memberPaymentsDashboardCharts.grossVolume?.destroy();
        memberPaymentsDashboardCharts.grossVolume = new Chart(ctx, {
            type: 'line',
            data: {
                labels: periods.map((period) => period.label),
                datasets,
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: setChartGridColor(),
                    },
                    y: {
                        beginAtZero: true,
                        grid: setChartGridColor(),
                    },
                },
            },
        });
    };

    const getPeriodColor = (periods, defaultColor) => (context) => {
        const pointIndex = context.dataIndex || context.datasetIndex || 0;
        const period = periods[pointIndex].from;
        const isBusinessDay = moment.unix(period).tz(config.selectedTimezone).isBusinessDay();
        return interval === 'day' && !isBusinessDay ? 'red' : defaultColor;
    };

    const getPeriodTitle = (periods) => (contexts) => {
        const context = contexts[0];
        const pointIndex = context.dataIndex || context.datasetIndex || 0;
        const period = periods[pointIndex].from;
        const isBusinessDay = moment.unix(period).tz(config.selectedTimezone).isBusinessDay();
        return isBusinessDay ? context.label : `${context.label} (weekend)`;
    };

    const renderNetPayoutsGraph = () => {
        const periods = getGraphPeriods();
        let maxNetPayout = 0;

        const data = periods.reduce((acc, period) => {
            const periodPayouts = payouts.filter((payout) => {
                const created = moment.unix(payout.created).tz(config.selectedTimezone);
                const from = moment.unix(period.from).tz(config.selectedTimezone);
                const to = moment.unix(period.to).tz(config.selectedTimezone);
                return created.isSameOrAfter(from, 'day') && created.isBefore(to, 'day');
            });

            currencies.forEach((currency) => {
                if (!acc[currency]) {
                    acc[currency] = {};
                }

                const totalPayouts = +periodPayouts
                    .reduce((acc, payout) => {
                        if (payout.currency === currency) {
                            return acc + payout.amount;
                        }

                        return acc;
                    }, 0)
                    .formatStripeCurrency(currency, false)
                    .replace(',', '');

                if (totalPayouts > maxNetPayout) {
                    maxNetPayout = totalPayouts;
                }

                acc[currency][period.label] = { ...period, totalPayouts };
            });

            return acc;
        }, {});

        const colors = [
            { border: 'rgb(173, 154, 240)', background: 'rgba(173, 154, 240, 0.2)' },
            { border: 'rgb(38, 187, 226)', background: 'rgba(38, 187, 226, 0.2)' },
            { border: 'rgb(98, 174, 247)', background: 'rgba(98, 174, 247, 0.2)' },
            { border: 'rgb(230, 129, 202)', background: 'rgba(230, 129, 202, 0.2)' },
        ];

        const datasets = Object.keys(data).map((currency, index) => setChartDataset({
            label: currency,
            data: Object.keys(data[currency]).map((period) => data[currency][period].totalPayouts),
            borderColor: colors[index].border,
            fill: true,
            pointBackgroundColor: getPeriodColor(periods, colors[index].border),
            pointBorderColor: getPeriodColor(periods, colors[index].border),
        }));

        const annotations =
            interval !== 'day'
                ? {}
                : periods.reduce((acc, period, index) => {
                      const periodMoment = moment.unix(period.from).tz(config.selectedTimezone);
                      const isBusinessDay = periodMoment.isBusinessDay();
                      const isWeekend = !periodMoment.isBusinessDay() && !periodMoment.isHoliday();
                      if (isBusinessDay) return acc;

                      acc[`box-${index}`] = {
                          drawTime: 'afterDatasetsDraw',
                          type: 'box',
                          xMin: index - 0.45,
                          xMax: index + 0.45,
                          backgroundColor: 'rgba(255, 99, 132, 0.1)',
                          borderColor: 'rgba(0, 0, 0, 0)',
                      };

                      const labelSize = Math.max(19 - periods.length, 8);

                      acc[`label-${index}`] = {
                          type: 'label',
                          xValue: index,
                          yAdjust: 35,
                          content: isWeekend ? t('WEEKEND') : t('HOLIDAY'),
                          font: {
                              size: labelSize,
                              weight: 'bold',
                          },
                          color: 'rgba(255, 99, 132, 0.8)',
                          rotation: labelSize > 8 ? 0 : -70,
                      };

                      return acc;
                  }, {});

        if (!$('#net-payouts-graph > canvas').length) return;
        const ctx = $('#net-payouts-graph > canvas')[0].getContext('2d');
        memberPaymentsDashboardCharts.netPayouts?.destroy();
        memberPaymentsDashboardCharts.netPayouts = new Chart(ctx, {
            type: 'line',
            data: {
                labels: periods.map((period) => period.label),
                datasets,
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: setChartGridColor(),
                    },
                    y: {
                        beginAtZero: true,
                        grid: setChartGridColor(),
                    },
                },
                plugins: {
                    autocolors: false,
                    annotation: {
                        annotations,
                    },
                    tooltip: {
                        callbacks: {
                            title: getPeriodTitle(periods),
                        },
                    },
                },
            },
        });
    };

    const renderNumberOfPaymentsGraph = () => {
        const periods = getGraphPeriods();

        const data = periods.map((item) => {
            const filteredRecords = records.filter((record) =>
                item.from === item.to
                    ? record.created === item.from
                    : record.created >= item.from && record.created <= item.to
            );
            item.success = filteredRecords.filter((r) => r.status === 'succeeded').length;
            item.failed = filteredRecords.filter((r) => r.status !== 'succeeded').length;
            return item;
        });

        if (!$('#number-of-payments-graph > canvas').length) return;
        const ctx = $('#number-of-payments-graph > canvas')[0].getContext('2d');
        memberPaymentsDashboardCharts.numOfPayments?.destroy();
        memberPaymentsDashboardCharts.numOfPayments = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map((item) => item.label),
                datasets: [
                    {
                        label: t('# of Successful Payments'),
                        data: data.map((item) => item.success),
                        borderColor: '#a77ce6',
                        borderWidth: 1,
                        backgroundColor: 'rgba(167, 124, 230, 0.2)',
                    },
                    {
                        label: t('# of Failed Payments'),
                        data: data.map((item) => item.failed),
                        borderColor: 'rgb(235, 89, 177)',
                        borderWidth: 1,
                        backgroundColor: 'rgba(235, 89, 177, 0.2)',
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: setChartGridColor(),
                    },
                    x: {
                        grid: setChartGridColor(),
                    },
                },
            },
        });
    };

    const recordHasFuturePayout = (record) =>
        record.status === 'succeeded' &&
        (!record.payout || !['paid', 'failed', 'canceled'].includes(record.payout.status));

    const renderForecastedFuturePayoutsGraph = () => {
        const periods = forecastedFutureRecords
            .reduce((acc, record) => {
                if (recordHasFuturePayout(record)) {
                    const date = moment.unix(record.created).tz(config.selectedTimezone).format('YYYY/MM/DD');
                    !acc.includes(date) && acc.push(date);
                }

                return acc;
            }, [])
            .sort((a, b) => moment.tz(a, 'YYYY/MM/DD', config.selectedTimezone).unix() - moment.tz(b, 'YYYY/MM/DD', config.selectedTimezone).unix());

        const data = periods.reduce((acc, period) => {
            const periodRecords = forecastedFutureRecords.filter(
                (record) =>
                    recordHasFuturePayout(record)
                    && moment.unix(record.created).tz(config.selectedTimezone).format('YYYY/MM/DD') === period
            );

            currencies.forEach((currency) => {
                const currencyRecords = periodRecords.filter((record) => record.currency === currency);

                let expectedPayout = currencyRecords
                    .reduce((acc, record) => acc + record.netAmount, 0);

                expectedPayout = (expectedPayout * 100)
                    .formatStripeCurrency(currency, false)
                    .replace(',', '');

                acc[currency] = acc[currency] || {};
                acc[currency][period] = expectedPayout;
            });

            return acc;
        }, {});

        const colors = [
            { border: 'rgb(206, 66, 87)', background: 'rgba(206, 66, 87, 0.2)' },
            { border: 'rgb(193, 110, 112)', background: 'rgba(193, 110, 112, 0.2)' },
            { border: 'rgb(138, 201, 38)', background: 'rgba(138, 201, 38, 0.2)' },
            { border: 'rgb(255, 202, 58)', background: 'rgba(255, 202, 58, 0.2)' },
        ];

        const datasets = Object.keys(data).map((currency, index) => {
            return {
                label: currency,
                data: Object.keys(data[currency]).map((period) => data[currency][period]),
                borderColor: colors[index].border,
                borderWidth: 1,
                borderDash: [10],
                fill: true,
                backgroundColor: createChartGradient,
                tension: 0.3,
            };
        });

        if (!$('#forecasted-future-payouts-graph > canvas').length) return;
        const ctx = $('#forecasted-future-payouts-graph > canvas')[0].getContext('2d');
        memberPaymentsDashboardCharts.forecasted?.destroy();
        memberPaymentsDashboardCharts.forecasted = new Chart(ctx, {
            type: 'line',
            data: datasets, 
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                    },
                    x: {
                        grid: setChartGridColor(),
                    },
                },
            },
        });

        toggleLoading($('#forecasted-future-payouts-graph'), false);
    };

    const getGraphTimeInterval = () => {
        const diffIn = (time = 'seconds') => config.to.diff(config.from, time);

        if (diffIn('years') > 2) {
            return 'year';
        } else if (diffIn('months') > 2) {
            return 'month';
        } else if (diffIn('weeks') > 2) {
            return 'week';
        } else {
            return 'day';
        }
    };

    const getGraphPeriods = () => {
        const data = [];
        let lastDate = config.from.clone();
        let currentDate = config.from.clone().add(1, interval);

        const formats = {
            day: 'DD MMM',
            week: 'DD MMM',
            month: 'MMM',
            year: 'YYYY',
        };

        while ((interval === 'day' ? lastDate : currentDate).isSameOrBefore(config.to)) {
            data.push({
                from: lastDate.unix(),
                to: currentDate.unix(),
                label: (interval === 'day' ? lastDate : currentDate).format(formats[interval]),
            });
            lastDate = currentDate.clone();
            currentDate = lastDate.clone().add(1, interval);
        }

        return data;
    };

    const paymentRecordRow = (record) => {

        let now = moment().locale(defaultLanguage);
        let created = moment.unix(record.created).tz(config.selectedTimezone);

        let formatString = 'MMM D H:mm A';
        if (created.year() !== now.year()) {
            formatString = 'MMM D YYYY, H:mm A';
        }
        const dateString = created.format(formatString);
        const dateExportString = created.format('YYYY-MM-DD HH:mm A');
        const charges = getRecordCharges(record);

        let payoutString = '';
        let payoutStringExport = '';
        let payoutBatchID = '';
        let payoutBatchIDExport = '';
        let payoutSort = 0;

        if (record.fetchingPayout) {
            payoutString = `${t('Fetching')} <img src="/assets/images/tail-spin.svg" width="14px"></img>`;
            payoutStringExport = t('Fetching');
            payoutBatchID = `${t('Fetching')} <img src="/assets/images/tail-spin.svg" width="14px"></img>`;
            payoutBatchIDExport = t('Fetching');
        } else if (record.payout) {
            payoutString = (record.netAmount * 100).formatStripeCurrency(record.currency, false);
            payoutStringExport = payoutString;
            payoutBatchID = `<a class="btn-show-all-grouped-payouts clickable">${record.payout.id}</a>`;
            payoutBatchIDExport = record.payout.id;
            payoutSort = record.netAmount;
        } else if (record.status === 'succeeded') {
            payoutString = t('Pending');
            payoutStringExport = payoutString;
            payoutBatchID = t('Pending');
            payoutBatchIDExport = payoutBatchID;
        } else {
            payoutString = `<span class="text-danger">${(0).formatStripeCurrency(record.currency, false)}</span>`;
            payoutStringExport = (0).formatStripeCurrency(record.currency, false);
        }

        const status = overallStatusLabel(record.status, record.payout, record);

        const appFeeCols = uniqueFeeNames.map(({ name: feeName }) => {
            const fee = record.applicationFees.find((fee) => fee.name === feeName);
            const amount = fee ? (fee.amount + fee.tax) * 100 : 0;
            const formattedAmount = amount.formatStripeCurrency(record.currency, false);
            const formattedFeeTax = (fee?.tax ?? 0).formatCurrency(2);
            return `
                <td class="selectable hide-lg" data-sort="${amount}" data-export="${formattedAmount}">
                    <div class="m-0 shorten-text" style="max-width: 150px;">
                        ${formattedAmount}
                    </div>
                </td>
                <td class="selectable hide-lg" data-sort="${fee ? fee.tax * 100 : 0}" data-export="${formattedFeeTax}">
                    ${formattedFeeTax}
                </td>
            `;
        }).join('');

        let unaccountedFeesTd = '';
        if (unaccountedFeesColVisible) {
            const fee = ((record.unaccountedFees || 0) * 100).formatStripeCurrency(record.currency, false);
            unaccountedFeesTd = `
                <td class="selectable hide-lg" data-sort="${fee}" data-export="${fee}">
                    <div class="m-0 shorten-text" style="max-width: 150px;" title="${fee}">
                        ${fee}
                    </div>
                </td>
            `;
        }

        const paymentMethods = charges.map((charge) => {
            const card = charge.payment_method_details.card;
            if (!card) return '';
            const { brand, last4, wallet } = card;

            return `
                <div class="d-flex" style="width: max-content;">
                    ${getStripeWalletIcon(wallet?.type)}
                    ${returnKnownCardBrandLogos(brand, true)}
                    <span class="hidden">${wallet?.type ?? ''} ${brand} </span><span style="padding-left: 1rem; letter-spacing: 2px;">•••• ${last4}</span>
                </div>
            `;
        }).join('');

        const paymentMethodsExport = charges.map((charge) => {
            const card = charge.payment_method_details.card;
            if (!card) return '';
            const { brand, last4, wallet } = card;

            const typeSymbols = {
                apple_pay: '',
                google_pay: 'G',
                samsung_pay: 'S',
                amex_express_checkout: 'A',
                masterpass: 'M',
                link: 'L',
                visa_checkout: 'V',
            }

            return [brand, typeSymbols[wallet?.type] ?? '', '••••', last4]
                .filter((v) => v).join(' ');
        }).join(' | ');

        const paymentMethodId = charges[0]?.payment_method ?? '';
        const declineReason = record.status !== 'succeeded' ? record.last_payment_error?.message || record.status : '';

        return `<tr class="open-record-dialog" data-id="${record.id}">

            <td class="selectable" data-sort="${record.amount.formatStripeCurrency(record.currency, false)}" data-export="${record.amount.formatStripeCurrency(record.currency, false)}">
                ${record.amount.formatStripeCurrency(record.currency, true)}
            </td>

            <td class="hidden" data-export="${record.currency}" style="text-transform: uppercase;">
                ${record.currency}
            </td>

            <td class="selectable" data-sort="${status[0]}" data-export="${status[0]}">
                <div class="shorten-text">${status[1]}</div>
            </td>

            <td class="selectable hide-mobile" data-export="${paymentMethodsExport}">
                <div class="d-flex flex-column" style="gap: 4px;">${paymentMethods}</div>
            </td>

            ${appFeeCols}
            ${unaccountedFeesTd}

            <td class="selectable" width="75px" data-sort="${payoutSort}" data-export="${payoutStringExport}">
                ${payoutString}
            </td>

            <td
                class="selectable"
                data-sort="${record.created}"
                data-filter="${[
                    dateString,
                    record.payout ? record.payout.id : '',
                    record.id,
                    record.payment_method,
                    record.last_payment_error && record.last_payment_error.payment_method
                        ? record.last_payment_error.payment_method.id
                        : '',
                    record.last_payment_error
                        ? `failed_payment:${
                              ['expired_card', 'card_declined'].includes(record.last_payment_error.code)
                                  ? record.last_payment_error.code
                                  : 'other'
                          }`
                        : '',
                    record.status !== 'succeeded' ? 'failed_payment' : 'successful_payment',
                ].join(' ')}"
                data-export="${dateExportString}"
            >
                <div style="width: 110px;">${dateString}</div>
            </td>

            <td class="selectable xl-screen-only" data-export="${payoutBatchIDExport}">${payoutBatchID}</td>
            <td class="selectable hidden" data-sort="${record.id}" data-export="${record.id}">
                ${record.id}
            </td>
            <td class="hidden" data-export="${paymentMethodId}">${paymentMethodId}</td>
            <td class="selectable hide-mobile" data-export="${record.customer?.email || ''}">
                <div class="m-0 shorten-text customer-email-col">
                    ${record.customer?.email || ''}
                </div>
            </td>

            <td class="hidden">${declineReason}</td>
        </tr>`;
    };

    const emptyDataMessage = (
        element = '#payments-table-container',
        action = 'show',
        message = `<center>
            <h3>${t('There are no payment records for this club during the reporting period requested')}</h3>
            <hr>
            <p>${t('Please change the date/time that you are reporting on to a period that you have collected payments via the UBX Payment Processor')}</p>
            <p>${t('If you believe this is an error, please contact Support to further troubleshoot your account.')}</p>
        </center>`
    ) => {
        $('.payments-search-container-loader').hide();

        if (action === 'show') {
            $(element).html(`<div class="empty-data-message">${message}</div>`);
            $('.payments-search-container').hide();
            $('.payments-search-container').hide();
        } else {
            $(`${element} .empty-data-message`).remove();
            $('.payments-search-container').show();
            $('.no-stripe').hide();
            $('.payments-container').show();
        }
    };

    const loadDateRange = () => {
        let start = config.from;
        let end = config.to;
        let newTimezone = config.selectedTimezone;
        interval = getGraphTimeInterval();

        function setDateRange(start, end, updateHref = true) {
            $('#page-daterange .date-string').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
            updateHref && updateHrefQuery({ fromDate: start.unix(), toDate: end.unix() });
        }

        const rangeObject = {}
        rangeObject[t('Today')] = [
            moment().startOf('day'),
            moment().endOf('day')
        ];
        rangeObject[t('Yesterday')] = [
            moment().subtract(1, 'day').startOf('day'),
            moment().subtract(1, 'day').endOf('day'),
        ];
        rangeObject[t('This month (to date)')] = [
            moment().startOf('month').startOf('day'),
            moment().endOf('day')
        ];
        rangeObject[t('Last week')] = [
            moment().subtract(1, 'week').startOf('week').startOf('day'),
            moment().subtract(1, 'week').endOf('week').endOf('day'),
        ];
        rangeObject[t('Last month')] = [
            moment().subtract(1, 'month').startOf('month').startOf('day'),
            moment().subtract(1, 'month').endOf('month').endOf('day'),
        ];
        rangeObject[t('Last 7 days')] = [
            moment().subtract(6, 'days').startOf('day'),
            moment().endOf('day')
        ];
        rangeObject[t('Last 14 days')] = [
            moment().subtract(13, 'days').startOf('day'),
            moment().endOf('day')
        ];
        rangeObject[t('Last 30 days')] = [
            moment().subtract(29, 'days').startOf('day'),
            moment().endOf('day')
        ];    

        $('#page-daterange .selectbox').daterangepicker(
            {
                startDate: start,
                endDate: end,
                maxDate: moment(),
                alwaysShowCalendars: true,
                ranges: rangeObject,
                locale: {
                    format: 'MMMM DD, YYYY',
                    applyLabel: t('Apply'),
                    cancelLabel: t('Cancel'),
                    customRangeLabel: t('Custom Range'),
                    monthNames: [
                        t('Jan'),
                        t('Feb'),
                        t('Mar'),
                        t('Apr'),
                        t('May'),
                        t('Jun'),
                        t('Jul'),
                        t('Aug'),
                        t('Sep'),
                        t('Oct'),
                        t('Nov'),
                        t('Dec'),
                    ],
                    daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
                },
            },
            setDateRange
        );
        
        $('#page-daterange .selectbox').on('show.daterangepicker', function (ev, picker) {
            if (picker.container.find('.timezone-select').length) return;
            const options = [selectedClubObj.localisation.timezone, 'UTC']
                .map((timezone) => `<option value="${timezone}" ${timezone === config.selectedTimezone ? 'selected' : ''}>${timezone}</option>`)

            const timezoneSelect = $(`
                <span style="margin-right: 10px;" class="timezone-select">
                    <span>${t('Timezone: ')}</span>
                    <select>
                        ${options.join('')}
                    </select>
                </span>
            `)

            timezoneSelect.find('select').selectpicker();
            timezoneSelect.find('select').on('changed.bs.select', (e) => {
                newTimezone = $(e.currentTarget).val();
            });

            picker.container.find('.drp-buttons').prepend(timezoneSelect);
        });

        $('#page-daterange .selectbox').on('apply.daterangepicker', function (_, picker) {
            const diffInDays = picker.endDate.diff(picker.startDate, 'days');
            if (diffInDays <= 30) return changeDateRange();
    
            confirmDialog(
                'Extended Date Range May Delay Processing',
                'Selecting a custom range of more than 30 days may take a long time to generate as the data is fetched form Stripe servers directly. Proceed?',
                changeDateRange,
                () => cancelDateRangeChange(null, picker)
            );

            function changeDateRange() {
                if (newTimezone !== config.selectedTimezone) {
                    config.selectedTimezone = newTimezone;
                    $('.selected-timezone').html(newTimezone);
                    updateHrefQuery({ timezone: config.selectedTimezone });
                }

                config.from = picker.startDate;
                config.to = picker.endDate;
                interval = getGraphTimeInterval();

                loadMemberPaymentsData();
            }
        });

        $('#page-daterange .selectbox').on('cancel.daterangepicker', cancelDateRangeChange);

        setDateRange(start, end, false);

        function cancelDateRangeChange(_, picker) {
            $('#page-daterange .selectbox').data('daterangepicker').setStartDate(config.from);
            $('#page-daterange .selectbox').data('daterangepicker').setEndDate(config.to);
            picker.container.find('.drp-buttons .timezone-select select').selectpicker('val', config.selectedTimezone);
            setDateRange(config.from, config.to);
        }
    };

    // Some browsers don't yet support Intl.DisplayNames - Mar 2022 - DN
    // ... https://caniuse.com/?search=Intl.DisplayNames

    function returnCountryFromISO(paymentMethod) {
        try {
            const regionNames = new Intl.DisplayNames(['en'], { type: 'region' });
            const regionName = regionNames.of(paymentMethod.country);
            return `<div class="d-flex gap-1"><span>${regionName}</span> <img style='height: 20px; width: 20px;' src='/assets/images/flags/${paymentMethod.country.toUpperCase()}.svg'></div>`;
        } catch (err) {
            console.error(err);
            return `<div class="d-flex gap-1"><span>${paymentMethod.country}</span> <img style='height: 20px; width: 20px;' src='/assets/images/flags/${paymentMethod.country.toUpperCase()}.svg'></div>`;
        }
    }

    function returnKnownCardBrandLogos(type, imgOnly = false) {
        const img = `<img style='height: 20px; width: 20px;' src='/assets/images/stripe-known-card-types/${type.toLowerCase()}.svg'>`;
        if (imgOnly) return img;
        return `<span class="d-flex gap-1"><span>${type.capitalize()}</span> ${img}</span>`;
    }

    function getStripeWalletIcon(type) {
        if (!type) return '';
        return `
            <div class="d-flex align-center justify-center" style="height: 20px; width: 20px; background: #F6F8FA;">
                <img style='height: 12px; width: 12px;' src='/assets/images/stripe_pm_wallet_icons/${type.toLowerCase()}.svg'>
            </div>        
        `;
    }

    const getRecordPaymentMethodDetails = (record) => {
        let paymentMethod = {};

        const charges = getRecordCharges(record);
        paymentMethod.id = charges[0].payment_method;
        paymentMethod.type = charges[0].payment_method_details.type;
        paymentMethod = { ...paymentMethod, ...charges[0].payment_method_details[paymentMethod.type] };

        const rows = [
            {
                label: 'Payment Method',
                value: paymentMethod.type.capitalize(),
            },
            {
                label: 'Internal ID',
                value: paymentMethod.id,
            },
            {
                label: 'Origin',
                value: returnCountryFromISO(paymentMethod),
            },
            {
                label: 'Number',
                value: `<span style="letter-spacing: 2px;">•••• ${paymentMethod.last4}</span>`,
            },
            {
                label: 'Expires',
                value: `${paymentMethod.exp_month} / ${paymentMethod.exp_year}`,
            },
            {
                label: 'Card Brand',
                value: `
                    <div class="d-flex align-center">
                        ${returnKnownCardBrandLogos(paymentMethod.brand)}
                        ${getStripeWalletIcon(paymentMethod.wallet?.type)}
                    </div>
                `
            },
            {
                label: 'Card Type',
                value: paymentMethod.funding.capitalize(),
            },
        ];

        return `
            <div class="grow-1">
                <h1 style="font-size: 20px; font-weight: bold;">
                    ${t('Payment Method')}
                </h1>
                <hr class="m-0"/>
                <div class="d-flex py-1 flex-column">
                    <table>
                        ${rows
                            .map(
                                ({ label, value }) => `
                                    <tr>
                                        <td>${t(label)}</td>
                                        <td>
                                            ${
                                                label === 'Internal ID'
                                                    ? `
                                                        <span class="copy-clipboard">
                                                            <code class="shorten-text">${value}</code>
                                                        </span>
                                                    `
                                                    : value
                                            }
                                        </td>
                                    </tr>
                                `
                            )
                            .join('')}
                    </table>
                </div>
            </div>
        `;
    };

    const getRecordTimeline = (record) => {
        let timeline = [];
        const charges = getRecordCharges(record);
        const refundedCharge = charges.find((charge) => charge.refunded);
        const refundedData = refundedCharge?.refunds.data[0];

        if (refundedCharge) {
            timeline.push({
                label: `${t('Payment refunded')}`,
                date: refundedData?.created ?? refundedCharge.created,
                icon: 'payment-refunded.svg',
            });
        }

        if (record.payout) {
            const { payout } = record;

            payout.status === 'paid' &&
                timeline.push({
                    label: `${t('Payout expected to have arrived at account ending')} ${payout.destination.last4}`,
                    date: payout.arrival_date,
                    icon: 'succeeded.svg',
                });

            payout.status === 'in_transit' &&
                timeline.push({
                    label: `${t('Payout expected arrival to account ending')} ${payout.destination.last4}`,
                    date: payout.arrival_date,
                    icon: 'payout-expected-arrival.svg',
                });

            payout.status === 'failed' &&
                timeline.push({
                    label: `${t('Payout failed because of')} ${payout.failure_code.replace('_', ' ')}`,
                    date: payout.arrival_date,
                    icon: 'failed.svg',
                });

            payout.status === 'cancelled' &&
                timeline.push({
                    label: `${t('Payout cancelled')}`,
                    date: payout.arrival_date,
                    icon: 'failed.svg',
                });

            ['paid', 'in_transit', 'cancelled', 'failed'].includes(payout.status) &&
                timeline.push({ label: 'Payout in transit', date: payout.arrival_date, icon: 'payout-in-transit.svg' });

            timeline.push({
                label: `${t('Payout initiated')} ${payout.automatic ? 'automatically' : 'manually'}`,
                date: payout.created,
                icon: 'payout-initiated.svg',
            });
        } else if (!record.fetchingPayout && record.status === 'succeeded' && !refundedCharge ) {
            const charges = (record.object === 'charge' ? [record] : record.charges.data) ?? [];
            if (charges[0]?.refunded > 0) {
                timeline.push({
                    label: `${t('Funds refunded')}`,
                    date: charges[0].created,
                    icon: 'payment-refunded.svg',
                });
            } else if (!record.last_payment_error) {
                timeline.push({
                    label: `${t('Awaiting funds clearance')}`,
                    date: `${t('~ Allow 1-2 days')}`,
                    icon: 'funds-clearing.svg',
                });
            }
        }

        if (record.last_payment_error) {
            timeline.push({
                label: `${t('Payment failed because:')} ${
                    record.last_payment_error.code !== undefined
                        ? record.last_payment_error.code.replace('_', ' ').capitalize()
                        : record.last_payment_error.decline_code.replace('_', ' ').capitalize()
                }`,
                date: record.created,
                icon: 'failed.svg',
            });
        } else if (record.status !== 'succeeded') {
            timeline.push({
                label: `${t('Payment failed because:')} ${t(record.status)}`,
                date: record.created,
                icon: 'failed.svg',
            });
        } else {
            timeline.push({
                label: t('Payment succeeded'),
                date: record.created,
                icon: 'succeeded.svg',
            });
        }

        timeline.push({
            label: t('Payment started'),
            date: record.created,
            icon: 'payment-initiated.svg',
        });

        return timeline;
    };

    const recordInfoDialog = (record) => {
        const html = document.createElement('div');
        const timeline = getRecordTimeline(record);
        const status = overallStatusLabel(record.status, record.payout, record);

        let customerDetails = 'None';

        if (record.customer?.name) {
            customerDetails = `<span class="shorten-text">${record.customer.name}</span>`;
        }

        if (record.customer?.email) {
            customerDetails += `<br><span class="copy-clipboard" style="margin-left: -4px;"><code class="shorten-text">${record.customer.email}</code></span>`;
        }

        if (!record.customer?.name && !record.customer?.email && record.customer?.id) {
            customerDetails = `<span class="copy-clipboard" style="margin-left: -4px;"><code class="shorten-text">${record.customer.id}</code></span>`;
        }

        $(html).addClass('record-info-dialog');
        $(html).html(`
            <div class="ph-dialog-v2" style="padding: 20px;">
                <fieldset>

                    <div class="d-flex justify-between align-center">
                        <div class="d-flex align-center gap-1">
                            <h1 style="font-size: 20px; font-weight: bold;">
                                ${record.amount.formatStripeCurrency(record.currency)}
                                <span style="text-transform: uppercase;">(${record.currency})</span>
                            </h1>
                            ${status[1]}
                        </div>
                        <span class="copy-clipboard">
                            <code class="shorten-text">${record.id}</code>
                        </span>
                    </div>

                    ${
                        record.status !== 'succeeded'
                            ? `

                                <hr class="m-0" />
                                <div class="alert alert-red">
                                    <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                                    ${t(record.last_payment_error?.message ?? record.status)}
                                </div>

                            `
                            : ``
                    }

                    <hr class="m-0" />

                    <div class="d-flex gap-3 py-1">
                        ${[
                            { label: t('Date'), value: moment.unix(record.created).tz(config.selectedTimezone).format('DD MMMM YYYY, hh:mm A') },
                            {
                                label: t('Customer'),
                                value: customerDetails,
                            },
                            { label: t('Description'), value: record.description ?? '' },
                        ]
                            .map(
                                ({ label, value }) => `
                                    <div>
                                        <label class="m-0" style="font-size: 14px; color: #6A7383; font-weight: bold;">${label}</label>
                                        <p class="m-0" style="font-size: 14px;">${value}</p>
                                    </div>
                                `
                            )
                            .join('')}
                    </div>

                    ${
                        record.applicationFees.length > 0
                            ? `
                                <h1 style="font-size: 20px; font-weight: bold;">
                                    ${t('Payment Breakdown')}
                                </h1>
                                <hr class="m-0"/>
                                <table class="table">
                                    <thead>
                                        <th>${t('Name')}</th>
                                        <th>${t('Fee')}</th>
                                        <th>${t('Tax')}</th>
                                        <th>${t('Total')}</th>
                                    </thead>
                                    <tbody>
                                    ${record.applicationFees
                                        .map(
                                            (fee) => `
                                                <tr>
                                                    <td>${t(fee.name)}</td>
                                                    <td>${(fee.amount * 100).formatStripeCurrency(record.currency)}</td>
                                                    <td>
                                                        ${(fee.tax * 100).formatStripeCurrency(record.currency)}
                                                        ${(fee.taxType || '').toUpperCase()}
                                                    </td>
                                                    <td>
                                                        ${(fee.tax * 100 + fee.amount * 100).formatStripeCurrency(record.currency)}
                                                    </td>
                                                </tr>
                                            `
                                        )
                                        .join('')}
                                        <tr>
                                            <td>${t('Net Payout')}</td>
                                            <td>${(record.netAmount * 100).formatStripeCurrency(record.currency)}<td/>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                            `
                            : ''
                    }

                    <div class="d-flex gap-2 flex-wrap">
                    ${
                        record.payment_method
                            ? getRecordPaymentMethodDetails(record)
                            : record.last_payment_error
                            ? // Error processing payment...
                              `
                                <div class="grow-1">
                                    <h1 style="font-size: 20px; font-weight: bold;">
                                        ${t('Failed Payment Details')}
                                    </h1>
                                    <hr class="m-0"/>
                                    <div class="d-flex py-1 flex-column">
                                        <table>
                                            <tr>
                                                <td>${t('Internal ID')}</td>
                                                <td>
                                                    <span class="copy-clipboard">
                                                        <code class="shorten-text">${
                                                            record.last_payment_error.payment_method.id
                                                        }</code>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>${t('Card Brand')}</td>
                                                <td>
                                                    ${returnKnownCardBrandLogos(record.last_payment_error.payment_method.card.brand)}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>${t('Number')}</td>
                                                <td>•••• ${record.last_payment_error.payment_method.card.last4}</td>
                                            </tr>
                                            <tr>
                                                <td>${t('Expires')}</td>
                                                <td>
                                                    ${record.last_payment_error.payment_method.card.exp_month} /
                                                    ${record.last_payment_error.payment_method.card.exp_year}
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            `
                            : ''
                    }
                    ${
                        record.fetchingPayout
                            ? `
                                <div class="grow-1">
                                    <h1 style="font-size: 20px; font-weight: bold;">
                                        ${t('Payout Batch Details')}
                                    </h1>
                                    <hr class="m-0"/>
                                    <div class="d-flex py-1 gap-1">
                                        <p>${t('Fetching payout')}</p>
                                        <div>
                                            <img src="/assets/images/tail-spin.svg" width="14px"></img>
                                        </div>
                                    </div>
                                </div>
                            `
                            : record.payout
                            ? `
                                <div class="grow-1">
                                    <h1 style="font-size: 20px; font-weight: bold;">
                                        ${t('Payout Batch Details')}
                                    </h1>
                                    <hr class="m-0"/>
                                    <div class="d-flex py-1 flex-column">
                                        <table>
                                        ${[
                                            { label: 'Batch ID', value: record.payout.id },
                                            {
                                                label: 'Created',
                                                value: moment.unix(record.payout.created).tz(config.selectedTimezone).format('DD MMMM YYYY, hh:mm A'),
                                            },
                                            {
                                                label: 'Total Amount',
                                                value: record.payout.amount.formatStripeCurrency(record.payout.currency),
                                            },
                                            {
                                                label: 'Status',
                                                value: payoutStatusLabels(record.payout.status),
                                            },
                                        ]
                                            .map(
                                                ({ label, value }) => `
                                                    <tr>
                                                        <td>${t(label)}</td>
                                                        <td>
                                                            ${
                                                                label === 'Batch ID'
                                                                    ? `
                                                                        <span class="copy-clipboard">
                                                                            <code class="shorten-text">${record.payout.id}</code>
                                                                        </span>
                                                                    `
                                                                    : value
                                                            }
                                                        </td>
                                                    </tr>
                                                `
                                            )
                                            .join('')}

                                            <tr>
                                                <td colspan="2" class="pt-1">
                                                    <a
                                                        data-payoutid="${
                                                            record.payout === undefined ||
                                                            record.payout.id === undefined ||
                                                            record.payout.id == ''
                                                                ? ''
                                                                : record.payout.id
                                                        }"
                                                        class="btn btn-xs btn-default waves-effect btn-show-all-grouped-payouts"
                                                    >
                                                        <i class="fa fa-university" aria-hidden="true"></i>
                                                        ${t('Show all Payments in this Batch')}
                                                    </a>
                                                </td>
                                            </tr>

                                        </table>
                                    </div>
                                </div>
                            `
                            : // No payout record found yet...
                              `
                                ${
                                    record.payment_method
                                        ? `
                                            <div class="grow-1">
                                                <h1 style="font-size: 20px; font-weight: bold;">
                                                    ${t('Payout Batch Details')}
                                                </h1>
                                                <hr class="m-0"/>
                                                <div class="d-flex py-1 flex-column">
                                                    <p>${t('Funds have not yet cleared')}</p>
                                                </div>
                                            </div>
                                        `
                                        : ''
                                }
                            `
                    }
                    </div>

                    <div>
                        <h1 style="font-size: 20px; font-weight: bold;">
                            ${t('Timeline of Events')}
                        </h1>
                        <hr class="m-0"/>
                        <div class="payment-timeline-container">
                            ${timeline
                                .map(
                                    ({ label, date, icon }) => `
                                        <div class="stripe-timeline">
                                            <img src="/assets/images/stripe-payment-statuses/${icon}">
                                            <div>
                                                <div class="label">${label}</div>
                                                <div class="date">${
                                                    String(date).startsWith('~')
                                                        ? date
                                                        : moment.unix(date).tz(config.selectedTimezone).format('DD MMMM YYYY, hh:mm A')
                                                }</div>
                                            </div>
                                        </div>
                                    `
                                )
                                .join('')}
                        </div>
                    </div>

                </fieldset>
            </div>
        `);

        addClipboardCopyButtons($(html).find('.copy-clipboard'));
        $(html)
            .find('.btn-show-all-grouped-payouts')
            .click(function () {
                // Trigger a search based on the payout ID group...
                $('.search-bar').val($(this).data('payoutid')).trigger('input');
                swalWithBootstrapButtons.close();
            });

        $(html).on('click', '.close-dialog', () => {
            swalWithBootstrapButtons.close();
        });

        swalWithBootstrapButtons.fire({
            html,
            title: t('Details of Payment'),
            width: '900px',
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
        });
    };

    const attachAppFeesToRecords = (records) => {
        if (feesConfig.length < 1 || !records) return;

        return records.map((record) => {
            const feeConfig = feesConfig.find((config) => {
                return config.dateEffective <= record.created && config.dateExpired > record.created
            });

            const appFees = feeConfig?.fees
                .map((feeConfig) => {
                    let amount = feeConfig.fixed + ((record.amount / 100) * feeConfig.percentage) / 100;
                    let tax = 0;

                    let recordFee = {
                        name: feeConfig.name,
                        amount,
                        tax,
                    };

                    if (feeConfig.taxRule && feeConfig.taxes.length > 0) {
                        const taxConfig = feeConfig.taxes[0];
                        const taxPercentOfAmount = amount * (taxConfig.percentage / 100);
                        tax = taxConfig.fixed + taxConfig.percentage > 0 ? taxPercentOfAmount : 0;

                        // if rule is NET, get the tax and subtract that from fee amount
                        if (feeConfig.taxRule === 'net') {
                            amount = amount - tax;
                        }

                        recordFee = { ...recordFee, amount, tax, taxType: taxConfig.type, taxRule: feeConfig.taxRule };
                    }

                    return recordFee;
                }) || [];

            // Multiply amount and tax by 100 to avoid floating point math
            const totalFees = appFees.reduce((acc, fee) => acc + fee.amount * 100 + fee.tax * 100, 0);

            let unaccountedFees = record.status === 'succeeded'
                ? (+record.application_fee_amount - totalFees) / 100
                : 0;

            unaccountedFees = unaccountedFees > 0 ? unaccountedFees : 0;
            unaccountedFees = +unaccountedFees.formatCurrency(2);
            const netAmount = (record.amount - totalFees) / 100;

            return {
                ...record,
                applicationFees: appFees,
                unaccountedFees,
                netAmount,
            };
        });
    };

    function rowHasStatus(row, status) {
        const statusColIndex = getTableIndexByHeader(t('Status'));
        return row[statusColIndex]['@data-sort'].includes(status);
    }

    init();
};
