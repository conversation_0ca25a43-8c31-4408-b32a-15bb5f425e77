const openUserPreferencesDialog = async () => {
    let selectedLanguageCode = null;

    const html = document.createElement('div');
    $(html).addClass('ph-dialog-v2 mb-2');
    const browserLanguageCode = _get(navigator, 'language', 'en').substring(0, 2);

    html.innerHTML = `
        <div class="" style="padding: 20px; padding-bottom: 0px;">
            <fieldset>
                <form>
                    <div class="mb-2 d-flex flex-column gap-2">
                        ${selectpicker({
                            label: t('Default Organisation'),
                            name: 'defaultOrganisationId',
                            info: t('Select the default organisation to be used when you log in.'),
                        })}
                        <div style="transform: translateX(-10px);">
                            ${switchLever(null, t('Consolidate all available organisations into Facilities selector'), 'showAllFacilities', false)}
                        </div>
                        ${selectpicker({
                            label: t("Preferred Language"),
                            name: 'preferredLanguage',
                        })}
                        ${selectpicker({
                            label: t("Unit of Measurement"),
                            name: 'unitOfMeasurement',
                            options: [
                                { label: t("Metric System"), value: 'metric' },
                                { label: t("Imperial System"), value: 'imperial' },
                            ],
                        })}
                        <div style="transform: translateX(-10px);">
                            ${switchLever(null, t("Show Performance Hub Tutorial"), 'enableTutorialWizard', false)}
                        </div>
                    </div>
                </form>
                <em class="text-danger form-error-msg"></em>
            </fieldset>
        </div>
    `;

    $(html).find('[data-toggle="tooltip"]').tooltip({});
    $(html).find('select').selectpicker();

    let data = {};

    async function preConfirm() {
        const formData = getSerializedObjectFromForm($(html).find('form'));
        const language = _get(formData, 'preferredLanguage', 'auto');
        const unitOfMeasurement = _get(formData, 'unitOfMeasurement', 'metric');
        
        data = {
            defaultOrganisationId: formData.defaultOrganisationId,
            tutorialWizardExited: _get(formData, 'enableTutorialWizard', 'off') === 'off',
            preferredLanguage: language === 'auto' ? null : language,
            showAllFacilities: formData.showAllFacilities === 'on',
            unitOfMeasurement,
        };

        // set global active unit of measurement
        globalStore.activeUnitOfMeasurement = unitOfMeasurement;

        const result = await updateUserPreferences(data);

        if (result.status !== 'success') {
            return Swal.showValidationMessage(t('Failed to update user preferences. Please try again later.'));
        }

        return true;
    }

    swalWithBootstrapButtons.fire({
        html,
        title: t("User Preferences"),
        showConfirmButton: true,
        showCancelButton: true,
        showCloseButton: true,
        confirmButtonText: t("Save"),
        cancelButtonText: t("Cancel"),
        preConfirm,
        onOpen: async () => {
            loadingOverlay($(html).find('form'), true, true);
            const [userPrefRes, langRes] = await Promise.all([getUserPreferencesAPI(), getLanguagesAPI()]);
            loadingOverlay($(html).find('form'), false);

            if (!userPrefRes[0] || !langRes[0]) {
                Swal.close();
                instantNotification('Failed to load user preferences', 'error');
                return;
            }

            const defaultOrgSelect = $(html).find('[name="defaultOrganisationId"]');
            defaultOrgSelect.empty();

            defaultOrgSelect.append(`
                <option value="" ${!userPrefRes[1].defaultOrganisationId ? 'selected' : ''}>
                    ${t('Last selected organisation')}
                </option>
            `);

            defaultOrgSelect.prop('disabled', userPrefRes[1].showAllFacilities);

            signedInUserData?.organisations.forEach((org) => {
                const selectedText = org.organisationId === userPrefRes[1].defaultOrganisationId ? 'selected' : '';
                defaultOrgSelect.append(`<option value="${org.organisationId}" ${selectedText}>${org.name}</option>`);
            });

            defaultOrgSelect.selectpicker('refresh');

            globalStore.activeUnitOfMeasurement = userPrefRes[1].unitOfMeasurement;
            createCookie("activeUnitOfMeasurement", userPrefRes[1].unitOfMeasurement, 1080);
            
            $(html).find('[name="unitOfMeasurement"]').val(userPrefRes[1].unitOfMeasurement).selectpicker('refresh');
        
            const defaultLanguageCode = langRes[1].languages.includes(browserLanguageCode) ? browserLanguageCode : 'en';
            const defaultLanguageName = getLanguageNameFromCode(defaultLanguageCode, defaultLanguageCode);
            const languageOptions = [
                { label: `${getLanguageNameFromCode('en', selectedLanguageCode)} (English)`, value: 'en' },
            ];
            selectedLanguageCode = userPrefRes[1].preferredLanguage || defaultLanguageCode;
        
            langRes[1].languages.forEach((code) => {
                languageOptions.push({
                    label: `${getLanguageNameFromCode(code, selectedLanguageCode)} (${getLanguageNameFromCode(code, code)})`,
                    value: code,
                });
            });

            const autoSelected = 'auto' === userPrefRes[1].preferredLanguage ? 'selected' : '';
            $(html)
                .find('[name="preferredLanguage"]')
                .append(`<optgroup><option value="auto" ${autoSelected}>Auto [${defaultLanguageName}]</option></optgroup>`)
                .append(`<optgroup></optgroup>`);
        
            languageOptions.forEach((option) => {
                const selectedText = option.value === userPrefRes[1].preferredLanguage ? 'selected' : '';
                $(html)
                    .find('[name="preferredLanguage"] optgroup:last-child')
                    .append(`<option value="${option.value}" ${selectedText}>${option.label}</option>`);
            });

            $(html).find('[name="preferredLanguage"]').selectpicker('refresh');
            $(html).find('[name="enableTutorialWizard"]').prop('checked', !userPrefRes[1].tutorialWizardExited);
            $(html).find('.save-btn').prop('disabled', false);

            const swalElement = Swal.getPopup();
            $(swalElement).draggable({
                handle: '.swal2-header',  // Makes the title bar the drag handle
            });
            // Only change the cursor to move for the title bar
            $(swalElement).find('.swal2-header').css('cursor', 'move');
            $(html).find('[name="showAllFacilities"]').prop('checked', userPrefRes[1].showAllFacilities);

            $(html).find('[name="showAllFacilities"]').on('change', function () {
                const isChecked = $(this).is(':checked');
                defaultOrgSelect.prop('disabled', isChecked);
                defaultOrgSelect.selectpicker('refresh');
            });
        },
    }).then((result) => {
        if (!result.value) return;

        if ((data?.preferredLanguage || 'en') !== selectedLanguageCode) {
            return window.location.reload();
        }

        getAuthedClubs(selectedOrgID);
        return instantNotification('User preferences updated successfully', 'success');
    });
};
