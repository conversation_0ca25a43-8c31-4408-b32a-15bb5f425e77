let userPermissionsTable = null,
    administerUsersSearch;

function sUserPermissions() {
    const root = $('#admin-portal #user-permissions');
    const usersTableContainer = root.find('.users-table-container');
    let access = null;
    let roles = [];

    function init() {
        // Try to load filters from localStorage
        let savedFilters = {};
        try {
            const savedFiltersString = localStorage.getItem('ADMIN_FILTERS_USER_PERMISSIONS');
            if (savedFiltersString) {
                savedFilters = JSON.parse(savedFiltersString);
                if (savedFilters.search) {
                    administerUsersSearch = savedFilters.search;
                }
            }
        } catch (error) {
            console.error('Error loading filters from localStorage:', error);
        }
        
        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => userPermissionsTable && userPermissionsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => userPermissionsTable && userPermissionsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => userPermissionsTable && userPermissionsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => userPermissionsTable && userPermissionsTable.button(3).trigger() },
        ];
        const buttons = [
            { label: t('Associate User'), icon: 'fa-user-plus', onClick: () => userDialog() },
            ...exportButtons,
        ];
        searchbarHeader(
            root.find('.search-bar-container'),
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'user-permissions-export-buttons',
                },
            }
        );

        root.find('input.search-bar').on('input', (e) => {
            const value = $(e.currentTarget).val();
            userPermissionsTable?.search(value).draw();
            administerUsersSearch = value;
            
            // Save search filter to localStorage
            try {
                localStorage.setItem('ADMIN_FILTERS_USER_PERMISSIONS', JSON.stringify({ search: value }));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }
        });

        refreshUsersTable(function () {
            // Check if we've been referred to this screen from an 'authorise club' email - if so automatically pop-up the new user dialog...
            const { email, club } = getQueryStringsObject();
            if (email && club) userDialog({ id: email, clubs: club });
        });
    }

    async function refreshUsersTable(callback) {
        let export_filename = 'User-Permissions__' + (new Date().toISOString());
        userPermissionsTable?.clear();
        userPermissionsTable?.destroy();
        usersTableContainer.empty();

        loadingOverlay(usersTableContainer);
        const [usersRes, accessRes, rolesRes] = await Promise.all([
            listUsersAPI(),
            getMyOrgAccessForModuleAPI('access-control'),
            listRolesAPI(),
        ]);
        loadingOverlay(usersTableContainer, false);

        if (!usersRes[0] || !accessRes[0] || !rolesRes[0]) {
            return usersTableContainer.html(
                '<div class="alert alert-red" role="alert">Failed to load users list!</div>'
            );
        }

        const usersList = usersRes[1];
        access = accessRes[1];
        roles = rolesRes[1];

        usersTableContainer.html(`
            <table class="table ph-table dataTable" style="width: 100%;">
                <thead>
                    <tr>
                        <th>${t('Email')}</th>
                        <th>${t('Roles')}</th>
                        <th style="width: 0px;"></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        usersList.forEach(function (user) {
            const removeUserEl = signedInUserData?.isGlobalAdmin
                ? `<a class="remove-user" data-index="${user.email}" href="#" title="Remove User"><i class="fa fa-user-times"></i></a>`
                : '';

            const userItem = $(`
                <tr>
                    <td class="selectable" data-email="${user.email}">${user.email}</td>
                    <td>
                        <div class="d-flex flex-wrap role-cards align-start gap-1"></div>
                    </td>
                    <td>
                        <div class="btn ph-btn ph-btn-sm btn-outline btn-default btn-danger d-flex gap-1 btn-delete-user row-actions">
                            ${removeUserEl}
                        </div>
                    </td>
                </tr>
            `);

            if (user.isGlobalAdmin) {
                userItem
                    .find('.role-cards')
                    .append(
                        roleCard(user, { roleId: 'global-admin', name: 'Super Admin', organisationName: 'Global' })
                    );
            } else {
                user.roles?.forEach((role) => {
                    userItem.find('.role-cards').append(roleCard(user, role));
                });
            }

            const addRoleCard = $(`
                <div class="card role-card add-role-card add-role" style="padding: 1rem;">
                    <div class="d-flex justify-center align-center">
                        <a href="#" class="text-center d-flex align-center flex-gap-2" data-index="${user.email}" title="Add Role">
                            <i class="material-icons">add_circle_outline</i>
                            <div class="add-role-text">Add Role</div>
                        </a>
                    </div>
                </div>
            `);

            userItem.find('.role-cards').append(addRoleCard);

            $(userItem)
                .find('.add-role')
                .on('click', (e) => {
                    e.preventDefault();
                    userRoleDialog(user);
                });

            $(userItem)
                .find('.btn-delete-user')
                .on('click', (e) => {
                    e.preventDefault();
                    if (!signedInUserData?.isGlobalAdmin) return;

                    confirmDeleteDialog(
                        t('Are you sure you want to remove this user?'),
                        [
                            t('You are about to remove this user from Performance Hub.'),
                            t('This action cannot be undone.'),
                        ].join('<br>'),
                        async () => {
                            const email = $(e.currentTarget).data('index');
                            const [ok, message] = await removeUserAPI(email);
                            if (ok) {
                                instantNotification(t('Successfully removed user'), 'success');
                                userItem.remove();
                            } else {
                                instantNotification(t(message), 'error');
                            }
                        }
                    );
                });

            usersTableContainer.find('tbody').append(userItem);
        });

        userPermissionsTable = usersTableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            iDisplayLength: 50,
            initComplete: function (settings, json) {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            },
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'User Permissions',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf'
                },
            ],
            columnDefs: [
                { targets: [0], width: '30%' },
                { targets: [1, 2], orderable: false },
            ],
        });

        wrapTableWithScrollableDiv(usersTableContainer.find('table'));

        if (administerUsersSearch) {
            setTimeout(function () {
                $('input.search-bar').val(administerUsersSearch);
                userPermissionsTable && userPermissionsTable.search(administerUsersSearch).draw();
            }, 50); // Prevents race condition below.
        }
    }

    function roleCard(user, role) {
        const facilities = role.facilities?.map((f) => f.name) || [];
        const regions = role.regions?.map((iso) => getCountryNameFromCode(iso)) || [];
        const access = [...regions, ...facilities].join(', ');

        const card = $(`
            <div class="card role-card edit-role-dialog" style="padding: 1rem;">
                <div class="d-flex gap-3 justify-between align-start">
                    <div class="ph-text">
                        <span class="role-name">${role.name}</span> <span class="text-muted mx-1">|</span> <span class="text-muted">${role.organisationName}</span>
                        <div><small class="text-muted">${access}</small></div>
                    </div>
                    <a class="remove-role">
                        <i class="material-icons">close</i>
                    </a>
                </div>
            </div>
        `);

        $(card)
            .on('click', async (e) => {
                // Remove role clicked
                if ($(e.target).closest('.remove-role').length > 0) {
                    const removePhrase = t('You are about to remove the role');
                    confirmDeleteDialog(
                        t('Are you sure you want to remove this role?'),
                        [
                            `<span class="ph-text">${removePhrase} "<strong>${role.name}</strong>" ${t('from')} <strong>${user.email}</strong>.</span>`,
                            t('This action cannot be undone.'),
                        ].join('<br>'),
                        async () => {
                            const [ok, message] = await removeUserRoleAPI(user.email, role.roleId);
                            if (ok) {
                                instantNotification(t('Successfully removed role'), 'success');
                                card.remove();
                            } else {
                                instantNotification(t(message), 'error');
                            }
                        }
                    );
                } else {
                    // Whole card clicked
                    userRoleDialog(user, role);
                }
            });

        return card;
    }

    function userDialog() {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');
        $(div).css('padding', '20px');

        $(div).html(textField({ label: t('Email'), name: 'email' }));

        let createdUser = null;
        swalWithBootstrapButtons
            .fire({
                html: div,
                title: t('Add User by Email'),
                showConfirmButton: true,
                showCloseButton: true,
                showCancelButton: true,
                confirmButtonText: t('Proceed'),
                cancelButtonText: t('Cancel'),
                preConfirm: async () => {
                    createdUser = { email: $(div).find('input[name=email]').val(), isGlobalAdmin: false, roles: [] };
                    const [ok, message] = await addUserAPI(createdUser);
                    if (!ok) Swal.showValidationMessage(message);
                    return true;
                },
            })
            .then((result) => {
                if (!result.value) return;
                instantNotification('Successfully Added User');

                // slight delay so the dialog doesn't look like it didn't close
                setTimeout(() => userRoleDialog(createdUser), 800);
            });
    }

    function userRoleDialog(user, role) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');

        $(div).html(`
            <div class="body">
                <div class="d-flex flex-column gap-1">
                    ${selectpicker({
                        label: t('Select Role'),
                        name: 'roleId',
                        options: [],
                    })}
                    ${selectpicker({
                        label: t('Regions'),
                        name: 'regions',
                        info: t('Regions need to be assigned manually for this role'),
                        options: [],
                        multiple: true,
                        selectClx: 'mui',
                    })}
                    ${selectpicker({
                        label: t('Facilities'),
                        name: 'facilities',
                        info: t('Facilities need to be assigned manually for this role'),
                        options: [],
                        multiple: true,
                        selectClx: 'mui',
                    })}
                </div>
            </div>
        `);

        const roleSelect = $(div).find('select[name=roleId]');
        if (signedInUserData?.isGlobalAdmin) {
            const selected = role?.roleId === 'global-admin' ? 'selected' : '';
            roleSelect.append(`
                <optgroup label="Global Roles">
                    <option value="global-admin" ${selected}>Super Admin</option>
                </optgroup>
            `);
        }

        const groupedRoles = roles.reduce((acc, { roleId, name, organisation }) => {
            const key = organisation?.name ?? 'No Organisation';

            const curr = acc[key] || [];
            curr.push({ value: roleId, label: name });

            acc[key] = curr.sort((a, b) => a.label.localeCompare(b.label));
            return acc;
        }, {});

        Object.keys(groupedRoles)
            .sort((a, b) => a.localeCompare(b))
            .forEach((key) => {
                const optGroup = $(`<optgroup label="Org: ${key}"></optgroup>`);
                groupedRoles[key].forEach(({ value, label }) => {
                    const selected = value === role?.roleId ? 'selected' : '';
                    optGroup.append(`<option value="${value}" ${selected}>${label}</option>`);
                });

                roleSelect.append(optGroup);
            });

        function displayFacilitiesAndRegionsSelect() {
            const facilitiesEl = $(div).find('select[name=facilities]');
            facilitiesEl.empty();

            const regionsEl = $(div).find('select[name=regions]');
            regionsEl.empty();

            const roleId = roleSelect.val();
            const orgRole = roles.find((r) => r.roleId === roleId);

            if (orgRole?.accessScope !== 'template') {
                facilitiesEl.closest('.form-group').hide();
                regionsEl.closest('.form-group').hide();
                return;
            }

            facilitiesEl.closest('.form-group').show();
            const org = access.orgs.find((o) => o.organisationId === orgRole.organisationId);

            org?.facilities.forEach((facility) => {
                const selected = role?.facilities?.some(f => f.id === facility.id) ? 'selected' : '';
                facilitiesEl.append(`<option value="${facility.id}" ${selected}>${facility.name}</option>`);
            });

            org?.regions.forEach((region) => {
                const selected = role?.regions?.some((iso) => iso === region.iso_code) ? 'selected' : '';
                regionsEl.append(`<option value="${region.iso_code}" ${selected}>${region.full_name}</option>`);
            });

            // prevent facilities/regions outline from overlapping with roleId select
            facilitiesEl.closest('.form-group').css('z-index', 0);
            regionsEl.closest('.form-group').css('z-index', 0);

            facilitiesEl.select2({ theme: 'material' });
            regionsEl.select2({ theme: 'material' });
        }

        roleSelect.selectpicker();
        $(div).find('[data-toggle=tooltip]').tooltip({ container: 'body' });
        roleSelect.on('change', displayFacilitiesAndRegionsSelect);

        displayFacilitiesAndRegionsSelect();
        swalWithBootstrapButtons
            .fire({
                html: div,
                title: user.email,
                showConfirmButton: true,
                showCloseButton: true,
                showCancelButton: true,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                preConfirm: async () => {
                    const doc = {
                        id: user.email,
                        roleId: roleSelect.val(),
                        facilities: $(div).find('select[name=facilities]').val(),
                        regions: $(div).find('select[name=regions]').val(),
                    };

                    const role = roles.find((r) => r.roleId === doc.roleId);
                    if (!role && doc.roleId !== 'global-admin') {
                        return Swal.showValidationMessage('Please select a role');
                    }

                    const roleRequiresFacilitiesOrRegions = role?.accessScope === 'template';
                    const noFacilityOrRegionSelected = !doc.facilities?.length && !doc.regions?.length;
                    if (roleRequiresFacilitiesOrRegions && noFacilityOrRegionSelected) {
                        return Swal.showValidationMessage('Please select at least one facility');
                    }

                    if (!Array.isArray(doc.facilities)) {
                        doc.facilities = [doc.facilities];
                    }

                    if (!Array.isArray(doc.regions)) {
                        doc.regions = [doc.regions];
                    }

                    const [ok, message] = await upsertUserRoleAPI(user.email, doc.roleId, doc.facilities, doc.regions);
                    if (!ok) Swal.showValidationMessage(message);
                    return true;
                },
            })
            .then((result) => {
                if (!result.value) return;
                instantNotification('Successfully Added Role to User');
                refreshUsersTable();
            });
    }

    init();
}
