
function reloadStoreFrameBasedOnClubID() {
    // User changed the club in the dropdown combo...
    $('#storeContainer').attr('src', `/store/?club=${selectedID}`);
}

function sStoreV2(iframePath) {
    const storePrefix = getStorePrefix();
    addContainerClasses('#storeV2');
    
    const defaultPath = storePrefix.includes('admin')
        ? `/store/admin/dashboard?club=${selectedID}`
        : `/store/?club=${selectedID}`;

    if (iframePath === 'about:blank') iframePath = null;
    const [pathname, queryString] = (iframePath || defaultPath).split('?');

    const qsObject = getQueryStringsObject(queryString);
    const updatedQSObject = { ...qsObject, club: selectedID };
    const newIframePath = `${pathname}${stringifyQueryStringsObject(updatedQSObject)}`;

    // Make the side nav small on this page...
    // if(smallSidebar == false) toggleSmallNav();

    $('.storeV2-popout button').hide();
    // loadingOverlay('.iframe-placeholder')
    $('.store-v2-sl').addClass('isLoading');

    $('#storeContainer').attr('src', newIframePath);

    $('.iframe-placeholder iframe').ready(function() {
        setTimeout(function(){
            $(".iframe-placeholder iframe").show();
            // $('.iframe-placeholder').LoadingOverlay("hide");
            $('.store-v2-sl').removeClass('isLoading');
            // $('.storeV2-popout button').show();
        }, 3000);
    });

    $('.storeV2-popout button').on('click', function () {
        window.open($('#storeContainer').contents().get(0).location.href,'storeContainerPopup',`height=${screen.availHeight},width=${screen.availWidth},resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=yes,directories=no,status=no`);
    });

    $(window).resize(function() {
        sPositionPopoutButtons();
    });

    $('.iframe-placeholder iframe').ready(function () {
        enableModalBackdrop($('#storeV2'));
        $('#modalBackdrop').on('click', function () {
            $('.iframe-placeholder iframe')[0].contentWindow.postMessage('closeModal', '*');
        });
    });

    window.onmessage = function(e){
        if(e.data == 'urlChange') {
            sammy.quietRoute(`/${getStorePrefix()}/#` + encodeURIComponent($('#storeContainer').contents().get(0).location.href.replace(document.location.origin, '')));
        }
    };
}

function getStorePrefix() {
    const currentPath = window.location.pathname;
    let storePrefix = currentPath.split('/')[1];
    
    if (!['store-admin-ui', 'store-ui'].includes(storePrefix)) {
        storePrefix = 'store-ui';
    }

    return storePrefix;
}

function sStoreURI() {
    sammy.quietRoute(`/${getStorePrefix()}/#` + encodeURIComponent($('#storeContainer').contents().get(0).location.href.replace(document.location.origin, '')));
    // alert('myframe is loaded');

    $(".iframe-placeholder iframe").show();
    $('.iframe-placeholder').LoadingOverlay("hide");
    $('.store-v2-sl').removeClass('isLoading');
    
    sPositionPopoutButtons();
    // $('.storeV2-popout button').show();
}

function sPositionPopoutButtons() {
    var frm = $(".iframe-placeholder iframe")[0];
    var iIsVrScrollBar =  frm.contentWindow.document.documentElement.scrollHeight>frm.contentWindow.document.documentElement.offsetHeight ? false : true;

    if(iIsVrScrollBar) {
        $(".storeV2-popout").removeClass("noPopoutButtonPading")
    } else {
        $(".storeV2-popout").addClass("noPopoutButtonPading")
    }
}