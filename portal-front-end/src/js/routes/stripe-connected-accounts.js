let stripeConnectedAccountsTable;

function sStripeConnectedAccounts() {
    const exportFilename = 'Stripe Connected Accounts_' + new Date().toISOString();

    const qsObject = getQueryStringsObject();
    const filters = {
        search: qsObject.search || '',
        status: qsObject.status || 'active',
    };

    async function init() {
        renderSearchNav();
        loadSettingsTable();
    }

    function loadSettingsTable() {
        if (stripeConnectedAccountsTable) {
            stripeConnectedAccountsTable.clear();
            stripeConnectedAccountsTable.destroy();
        }

        function createDataTable() {
            stripeConnectedAccountsTable = $('#stripe-connected-accounts-table').DataTable({
                responsive: true,
                paging: false,
                dom: 'rtip',
                buttons: [
                    { extend: 'copy', title: exportFilename },
                    { extend: 'print', title: exportFilename },
                    { extend: 'csv', title: exportFilename },
                    { extend: 'pdf', title: exportFilename },
                ],
                language: {
                    info: 'Showing _TOTAL_ entries',
                    infoEmpty: '',
                    infoFiltered: '',
                    lengthMenu: '',
                },
                pagingType: 'simple',
                serverSide: false,
                order: [[1, 'desc']],
                autoWidth: false,
                columns: [
                    { data: 'name' },
                    {
                        data: 'stripeConnectedAccountID',
                        render: (account, _, row) => {
                            if (!account) return `<span class="badge badge-grey">${t('NO ACCOUNT ID')}</span>`;

                            return `
                                <div class="d-flex gap-1">
                                    <div class="copy-id-container"><code>${account}</code></div>
                                </div>
                            `;
                        },
                    },
                    {
                        data: 'status',
                        render: (account, _, row) => {
                            let status = '';
                            if (row.status === 'complete') {
                                status = `<span class="badge badge-green">${t('ONBOARDING COMPLETE')}</span>`;
                            } else if (row.status === 'restricted') {
                                status = `<span class="badge badge-red">${t('RESTRICTED')}</span>`;
                                status = `
                                    <span
                                        class="badge badge-red clickable"
                                        data-toggle="tooltip"
                                        title="More information required in order to enable payouts for this account."
                                    >
                                        ${t('RESTRICTED')}
                                    </span>
                                `;
                            }

                            let invalidAccount = '';
                            if (row.invalidAccount) {
                                invalidAccount = `<span class="badge badge-red">${t('INVALID ACCOUNT')}</span>`;
                            }

                            return `
                                <div class="d-flex gap-1">
                                    ${status}
                                    ${invalidAccount}
                                </div>
                            `;
                        },
                    },
                    {
                        data: 'applePayAndGooglePayActive',
                        render: (isActive, _, row) => {
                            if (!row.stripeConnectedAccountID) return '';

                            if (isActive === undefined) return `<span class="badge badge-red clickable" data-toggle="tooltip" title="Something might be wrong with this Stripe account">${t('ERROR')}</span>`;

                            if (!isActive) {
                                return `<button data-club="${row.id}" class="btn btn-outline enable-apple-pay-google-pay" data-type="enable">${t('Enable')}</button>`;
                            }

                            return `<span class="badge badge-green">${t('ACTIVE')}</span>`;
                        },
                    },
                    {
                        data: 'disputes',
                        render: (disputes, _, row) => {
                            const disputeLabel = disputes?.length
                                ? `${disputes.length} ${t('requires response')}`
                                : '';

                            const disputesURL = row?.disputesURL;

                            return `
                                <div class="">
                                    <span class="badge badge-orange">${disputeLabel}</span>
                                    ${disputesURL
                                        ? `<a class="btn btn-outline stripe-link" href="${disputesURL}" target="_blank">${t('Manage')}</a>`
                                        : ''
                                    }
                                </div>
                            `;
                        },
                    },
                    {
                        data: 'stripeURL',
                        render: (url, _, row) => {
                            if (url) {
                                return `<a class="btn btn-outline stripe-link" href="${url}" target="_blank">${t('View Account On Stripe')}</a>`;
                            }

                            return '';
                        },
                    },
                ],
            });

            stripeConnectedAccountsTable.on('click', '.stripe-link', async function (e) {
                // Stop propagating into the row click event
                e.stopPropagation();
            });

            stripeConnectedAccountsTable.on('click', '.enable-apple-pay-google-pay', async function (e) {
                e.preventDefault();
                e.stopPropagation();
                const { club } = $(e.currentTarget).data();

                const [isOK] = await enableApplePayGooglePay(club);
                if (isOK) {
                    instantNotification(`Successfully enabled Apple Pay and Google Pay!`, 'success');
                } else {
                    instantNotification(`Failed to enable Apple Pay and Google Pay!`, 'error');
                }
            });

            stripeConnectedAccountsTable.on('click', 'tr', function (e) {
                const row = stripeConnectedAccountsTable.row(this).data();
                if (!row) return;

                editConnectedAccountDialog(row);
            });

            $('.search-bar').on('input change', (e) => {
                const search = e.target.value;
                updateHrefQuery({ search });

                stripeConnectedAccountsTable?.search(search)?.draw();
            });
        }

        createDataTable();

        $('#stripe-connected-accounts-table > tbody').hide();
        $('#stripe-connected-accounts-loader').show();

        // Fetch the data
        $.ajax({
            url: '/api/clubs/stripe/connected-account',
            type: 'GET',
            success: function(response) {
                const data = response.data;

                $('#stripe-connected-accounts-table > tbody').show();
                $('#stripe-connected-accounts-loader').hide();

                stripeConnectedAccountsTable.rows.add(data);
                stripeConnectedAccountsTable.draw();
            },
            error: function(xhr, status, error) {
                console.error('Failed to fetch data:', error);
            }
        });

        $('#stripe-connected-accounts-table .badge[data-toggle="tooltip"]').tooltip();
    }

    function renderSearchNav() {
        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => stripeConnectedAccountsTable && stripeConnectedAccountsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => stripeConnectedAccountsTable && stripeConnectedAccountsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => stripeConnectedAccountsTable && stripeConnectedAccountsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => stripeConnectedAccountsTable && stripeConnectedAccountsTable.button(3).trigger() },
        ];
        searchbarHeader('.search-nav-container', [
            ...exportButtons,
            // { label: 'Show Unpublished', icon: 'fa-file-pdf', onClick: () => stripeConnectedAccountsTable && stripeConnectedAccountsTable.button(3).trigger() },
        ], {
            usePhTheme: true,
            expandableButtons: {
                startIndex: 0,
                count: exportButtons.length,
                label: t('Export'),
                icon: 'fa-ellipsis-v',
                id: 'stripe-connected-accounts-export-buttons',
            },
        });
        if (filters.search) $('.search-bar').val(filters.search);
    }

    function editConnectedAccountDialog(data) {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');

        $(html).html(`
            <div class="body">
                <p>${data?.name}</p>
                <br>
                ${textField({
                    label: t('Stripe Account ID'),
                    placeholder: 'acct_xxxxxxxxxxxxxx',
                    name: 'stripeConnectedAccountID',
                    value: data.stripeConnectedAccountID,
                })}
                <br>
                <label class="form-label">${t('Apple Pay &amp; Google Pay')}</label>
                ${data.stripeConnectedAccountID
                        ? `<div>
                            ${data.applePayAndGooglePayActive === undefined
                                ? `<span class="badge badge-red clickable" data-toggle="tooltip" title="Something might be wrong with this Stripe account">${t('ERROR')}</span>`
                                : data.applePayAndGooglePayActive
                                    ? `<div class="badge badge-green">${t('ACTIVE')}</div>`
                                    : `<button data-club="${data.id}" class="btn btn-outline enable-apple-pay-google-pay" data-type="enable">${t('Enable')}</button>`
                                }
                        </div>`
                    : '<div><small>Requires Account ID</small></div>'
            }
            </div>
        `);

        $(html).on('click', '.enable-apple-pay-google-pay', async function (e) {
            e.preventDefault();
            e.stopPropagation();
            const { club } = $(e.currentTarget).data();

            const [isOK] = await enableApplePayGooglePay(club);
            if (isOK) {
                instantNotification(`Successfully enabled Apple Pay and Google Pay!`, 'success');
            } else {
                instantNotification(`Failed to enable Apple Pay and Google Pay!`, 'error');
            }
        });

        async function handleEditRow() {
            const stripeConnectedAccountID = $(html).find('input[name=stripeConnectedAccountID]').val();
            const [isOK] = await updateClubIntegration(data.id, { stripeConnectedAccountID });
            if (!isOK) return Swal.showValidationMessage(`Failed to update ${data.name}!`);

            instantNotification(`Successfully updated ${data.name}!`, 'success');
            init();
        }

        swalWithBootstrapButtons.fire({
            title: t('Update Stripe Connected Account'),
            html,
            showCancelButton: true,
            showConfirmButton: true,
            showCloseButton: true,
            confirmButtonText: t('Save'),
            cancelButtonText: t('Cancel'),
            preConfirm: handleEditRow,
        });
    }

    init();
}
