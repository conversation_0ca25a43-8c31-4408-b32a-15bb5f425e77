function sSaaSBillingCostBreakdown() {
    const queryStrings = getQueryStringsObject();
    const rootElement = $('#cost-breakdown');
    const contentElement = rootElement.find('.breakdown-container');

    let csvExportDefinitions = [];
    let periodFilter = !queryStrings.period ? moment().format('MMMM YYYY') : decodeURIComponent(queryStrings.period);

    function init() {
        renderSearchNav();
        loadBillingPeriods();
        loadSaasBilling();
    }

    async function loadSaasBilling() {
        contentElement.empty();

        rootElement.find('.search-nav .export-btn').prop('disabled', true);
        showLoadingIndicator();
        $('.heading-summary-body').addClass('isLoading');

        csvExportDefinitions = [];

        const { month, year } = getFilterQueryStringObj();
        const reportsRes = await getClubSaasBillingMonthlyReportAPI(selectedID, month, year);
        hideLoadingIndicator();

        if (!reportsRes[0]) {
            return contentElement.html(`
                <div class="no-devices-table-message">
                    <h4>${t('Failed to retrieve reports')}</h4>
                </div>
            `);
        }

        if (periodFilter !== moment().format('MMMM YYYY')) {
            rootElement.find('.search-nav .export-btn').prop('disabled', false);
        }

        generateSaaSReportUI(reportsRes[1]);
    }

    function getFilterQueryStringObj() {
        const monthMoment = moment(periodFilter, 'MMMM YYYY');
        if (!monthMoment.isValid()) {
            return contentElement.html(`
                <div class="no-devices-table-message">
                    <h4>
                        ${t('Invalid period filter. Please select a valid period.')}
                    </h4>
                </div>
            `);
        }

        return { month: monthMoment.format('MM'), year: monthMoment.format('YYYY') };
    }

    function renderSearchNav() {
        rootElement.find('.search-nav').html(`
            <div class="use-skeleton" style="max-width: 250px; width: 100%;">
                <select id="period-select" class="form-control selectpicker">
                </select>
            </div>
            <button class="use-skeleton export-btn export-csv btn btn-default">${t('Export CSV')} <i class="fa fa-file-csv pl-4 mr-0"></i></button>
            <button class="use-skeleton export-btn export-pdf btn btn-default">${t('Download Invoice')} <i class="fa fa-file-pdf pl-4 mr-0"></i></button>
        `);

        $('#period-select').selectpicker();
        $('.export-pdf').on('click', exportPdf);
        $('.export-csv').on('click', exportCSV);
    }

    function loadBillingPeriods() {
        $('#period-select').prop('disabled', true);
        $('#period-select').selectpicker('refresh');

        getClubSaasBillingPaymentSettingsAPI(selectedID).then(([success, response]) => {
            if (!success) return;
            $('#period-select').prop('disabled', false);
            $('#period-select').empty();

            const thisMonthText = t('This Month (to date)');

            const periods = getSaasBillingPeriodsBasedOnDailyLogStartMonth(response.dailyBillingStartMonth);
            periods.forEach((period) => {
                const selected = period === periodFilter ? 'selected' : '';
                const label = moment(period, 'MMMM YYYY').isSame(moment(), 'month') ? thisMonthText : t(period);
                $('#period-select').append(`<option value="${period}" ${selected}>${label}</option>`);
            });

            const currentPeriod = moment().format('MMMM YYYY');
            if (!periods.includes(currentPeriod)) {
                const selected = periodFilter === currentPeriod ? 'selected' : '';
                $('#period-select').prepend(
                    `<option value="${currentPeriod}" ${selected}>${thisMonthText}</option>`
                );
            }

            $('#period-select').selectpicker('refresh');
            $('#period-select').on('changed.bs.select', (e) => {
                periodFilter = $(e.target).val();
                updateHrefQuery({ period: periodFilter });
                loadSaasBilling();
            });
        });
    }

    function showLoadingIndicator() {
        // hard coded prices are just used to seed some data for the skeleton loader
        generateSaaSReportUI(
            {
                breakdownId: 'loader-breakdown-id',
                chargeBreakdown: {
                    pricePerGBStored: 0.25,
                    pricePerCoachingScreen: 3.5,
                    pricePerDuressButton: 2,
                    performanceHubEASoftware: 10,
                    pricePerCamera: 4,
                },
                periodStart: 1706054399,
                periodEnd: 1706572799,
                charges: [
                    { total: 10, rate: 10, count: 3, label: 'Extended Access Software' },
                    { total: 4, rate: 4, count: 3, label: 'Camera Software' },
                    {
                        total: 0.1,
                        rate: 0.25,
                        count: { gbDays: '84.86', count: '12.12 GB' },
                        label: 'CCTV Storage Consumed',
                        period: '23 Jan - 29 Jan 2024',
                    },
                    {
                        total: 0.03,
                        rate: 0.03,
                        count: { gbDays: '390.72', count: '30.06 GB' },
                        period: '09 Jan - 22 Jan 2024',
                        label: 'CCTV Storage Consumed',
                    },
                ],
            },
            true
        );
        rootElement.find('.search-nav').addClass('isLoading');
    }

    function hideLoadingIndicator() {
        rootElement.find('.search-nav').removeClass('isLoading');
        contentElement.html('');
    }

    // "CCTV Storage": "pricePerGBStored",
    // "Extended Access Software": "performanceHubEASoftware",
    // "Camera Software": "pricePerCamera",
    // "Duress Buttons": "pricePerDuressButton",
    // "Coaching Screen Device & Software Charge": "pricePerCoachingScreen"

    function formatAmount(amount) {
        if (amount === '-') return amount;
        const abs = Math.abs(parseFloat(amount)).toFixed(2);
        return amount < 0 ? `-$${abs}` : `$${abs}`;
    }

    function generateSaaSReportUI(data, isLoading) {
        const container = $('<div>').addClass('saas-billing-report').appendTo(contentElement);
        if (isLoading) container.addClass('isLoading');

        if (csvExportDefinitions.length > 0) {
            csvExportDefinitions.push(['']);
            csvExportDefinitions.push(['']);
        }

        const docTitle = `Cost Breakdown: ${selectedID}`;
        const periodStart = moment.unix(data.periodStart).tz('UTC').format('DD MMM');
        const periodEnd = moment.unix(data.periodEnd).tz('UTC').format('DD MMM YYYY');
        const periodLabel = `${periodStart} - ${periodEnd}`;
        const periodCurrency = `USD`;

        $(".heading-summary").html(`
            <div class="heading-summary-body">
                <div><h3 class="use-skeleton">${docTitle}</h3></div>
                <div><h5 class="use-skeleton">Period: ${periodLabel}</h5></div>
                <div><h5 class="use-skeleton">Billing Currency: ${periodCurrency}</h5></div>
            </div>
        `)

        csvExportDefinitions.push([periodLabel]);

        const table = $(`
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th><span class="use-skeleton">Service</span></th>
                        <th><span class="use-skeleton">Count</span></th>
                        <th><span class="use-skeleton">Rate</span></th>
                        <th><span class="use-skeleton">Total</span></th>
                    <tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        container.append(table);
        csvExportDefinitions.push(['']);
        csvExportDefinitions.push(['Item', 'Count', 'Rate', 'Total']);

        const groups = {
            'Coaching Screens': ['CoachingScreens'],
            'Extended Access Technology': ['DuressButtons', 'cameraSoftware', 'storage', 'performanceHubSoftware'],
        };

        Object.entries(groups).forEach(([groupName, groupKeys]) => {
            const groupCharges = data.charges?.filter((charge) => groupKeys.includes(charge.key)) ?? [];
            if (groupCharges.length < 1) return;

            table
                .find('tbody')
                .append(`<tr><td colspan="100%"><strong class="use-skeleton">${groupName}</strong></td></tr>`);

            groupCharges.forEach(renderChargeRow);
        });

        const additionalCharges = data.charges?.filter(
            (charge) => !Object.values(groups).some((keys) => keys.includes(charge.key))
        ) ?? [];

        if (additionalCharges.length > 0) {
            table
                .find('tbody')
                .append(`<tr><td colspan="100%"><strong class="use-skeleton">Additional Charges</strong></td></tr>`);
            additionalCharges.forEach(renderChargeRow);
        }

        table.find('tbody').append(
            `
                <tr>
                    <td colspan="3" class="text-right"><strong class="use-skeleton">SaaS Usage Total:</strong></td>
                    <td><strong class="use-skeleton">$${data.grandTotal}</strong></td>
                </tr>
            `
        );
        csvExportDefinitions.push(['', '', '', `SaaS Usage Total: $${data.grandTotal}`]);
        csvExportDefinitions.push(['']);

        function renderChargeRow(charge) {
            const labelMapping = {
                CoachingScreens: 'Coaching Screens',
                storage: 'CCTV Storage',
                cameraSoftware: 'CCTV Device',
            };

            let label = labelMapping[charge.key] || charge.label;
            let count = charge.count;
            let rate = charge.rate;

            if (charge.key === 'storage') {
                let periodStart = moment.unix(charge.periodStart).tz('UTC').format('DD MMM - ');
                let periodEnd = moment.unix(charge.periodEnd).tz('UTC').format('DD MMM YYYY');
                if (charge.periodStart === charge.periodEnd) periodStart = '';

                label = `${label} <small>(${periodStart}${periodEnd})</small>`;
                count = `${charge.count.gbDays} Gigabyte Days`;

                const monthMoment = moment.unix(data.periodEnd).tz('UTC');
                const daysInMonth = monthMoment.daysInMonth();
                const gbDayPrice = charge.gbDayPrice.toFixed(12);
                const rateTitle =
                    `Effective Gigabyte Day Price: $${gbDayPrice}. ` +
                    `This price has been calculated on a factor of: ${daysInMonth} ` +
                    `days in the month of ${monthMoment.format('MMMM')} / $${rate} per GB per Month.`;

                rate = `${charge.rate} <i class="material-icons info-icon" data-toggle="tooltip" title="${rateTitle}" style="font-size: 12px;">info_outline</i>`;
            }

            table.find('tbody').append(
                `
                    <tr>
                        <td><span class="use-skeleton" style="padding-left: 2rem;">${label}</span></td>
                        <td><span class="use-skeleton">${count}</span></td>
                        <td><span class="use-skeleton">${formatAmount(rate)}</span></td>
                        <td><span class="use-skeleton">${formatAmount(charge.total)}</span></td>
                    </tr>
                `
            );

            $('[data-toggle="tooltip"]').tooltip();

            csvExportDefinitions.push([
                label.removeHTML(),
                `${count}`.removeHTML(),
                formatAmount(charge.rate),
                formatAmount(charge.total),
            ]);
        }
    }

    function exportPdf() {
        const { month, year } = getFilterQueryStringObj();
        const billingPeriod = moment(`${month} ${year}`, 'MM YYYY');
        const queryString = `month=${billingPeriod.format('MM')}&year=${billingPeriod.format('YYYY')}`;
        window.open(`/api/saas-billing/extended-access/monthly-usage-report/${selectedID}/invoice?${queryString}`);
    }

    function exportCSV() {
        const rows = [[`SaaS Billing For ${selectedID}`], ...csvExportDefinitions];
        const csvContent = 'data:text/csv;charset=utf-8,' + rows.map((e) => e.join(',')).join('\n');
        window.open(encodeURI(csvContent));
    }

    init();
}
