var storedDesigns = {}, designGroups = {}, isNameSortAsc = true, isDateSortAsc = false, defaultSortName = 'asc', currentFilteredDesign = [], publicFilterResults, dataSort = 'date updated', storedTagsObj = {}, initialLoad = true, dataView = localStorage.getItem('designView') || 'grid', searchResults = {}, searchTags = {};
let loadedVariables = {};
let tagsList = '';
const activeUUID = uuidv4()

let urlParams = new URLSearchParams(window.location.search);
let tagParam = urlParams.get('tag') ? decodeURIComponent(urlParams.get('tag')) : false;
let currentFilteredDesignTag = false;
let designTable = null;
let designSearch = urlParams.get('designSearch') ? decodeURIComponent(urlParams.get('designSearch')) : '';
let loadedDesignSearch = '';
let hasMPAdminAccess = false;
let mpAccessibleRegions = [];

async function sMarketingVariables() {
    const response = await listClubVariablesAPI(selectedID);
    if (!response[0]) return;
    loadedVariables = response[1].Values;
}

async function sDesigns(pathname = 'designs', loadRenderID=false, designId=false, designSearchValue='') {

    const [access, customPagesRes] = await Promise.all([
        getMyOrgAccessForModuleAPI('designer-admin'),
        listFacilityDesignerCustomPagesAPI(selectedID),
    ]);

    hasMPAdminAccess = access[0] && access[1].orgs.some(org => org.facilities.some(f => f.id === selectedID));
    mpAccessibleRegions = access[0] ? access[1].allRegions : [];

    let customPages = [];
    if (customPagesRes[0]) customPages = customPagesRes[1]; 

    // If the user does not have access to all clubs or regions, set the localStorage item to prevent all designs showing
    if(!hasMPAdminAccess) {
        localStorage.setItem('designerAllDesigns', 'club');
        $('.mp-designer__new-design').hide();
    } else {
        $('.mp-designer__new-design').show();
    }

    urlParams = new URLSearchParams(window.location.search);
    tagParam = urlParams.get('tag') ? decodeURIComponent(urlParams.get('tag')) : false;

    currentFilteredDesignTag = tagParam ? tagParam : false;

    if(designSearchValue) {
        designSearch = designSearchValue;
    }

    renderCustomPages();
    loadLogic(pathname);

    $('.designer .nav-tabs a').on("click", function (e) {
        const href = $(this).attr("href");
        const formattedHref = '/designer/' + href.replace('#', '');

        // clear search
        if(href !== '#designs-search') {
            designSearch = '';
            loadedDesignSearch = '';
            $('.mp-designer .search-bar').val(designSearch);
        }

        history.pushState({ tabChange: true }, null, formattedHref);
        loadLogic($(this).data("function"))

        e.preventDefault();
    });

    function renderCustomPages() {
        customPages.forEach((page) => {
            const active = pathname === page.slug ? 'active' : '';
            $('.mp-designer-tabs').append(`
                <li role="presentation" class="${active}">
                    <a href="#${page.slug}" data-toggle="tab" aria-expanded="true">${page.name}</a>
                </li>    
            `)

            const tabPaneIn = pathname === page.slug ? 'in active' : '';
            const tabPane = $(`
                <div role="tabpanel" class="tab-pane ${tabPaneIn}" id="${page.slug}">
                    <div class="designer-custom-page-content">
                        <iframe></iframe>
                    </div>
                </div>
            `);

            if (page.type === 'wysiwyg') {
                tabPane.find('iframe').attr('srcdoc', page.wysiwygContent);
            } else if (page.type === 'iframe') {
                tabPane.find('iframe').attr('src', page.iframeUrl);
            } else if (page.type === 'pdf') {
                PDFObject.embed(page.pdfUrl, tabPane.find('.designer-custom-page-content')[0], {
                    fallbackLink: `<iframe data-hj-allow-iframe='' class='fallback-agreement-iframe' src='[url]#zoom=50'></iframe>`,
                    pdfOpenParams: {
                        pagemode: 'thumbs',
                        navpanes: 0,
                        toolbar: 0,
                        statusbar: 0,
                    },
                    onError: function (error) {
                        console.error('PDFObject Error:', error);
                    }
                });
            }

            $('.tab-content').append(tabPane);
        });
    }

    function loadLogic(functionality) {   
        $(`.designer .nav-tabs a[href="#${functionality}"]`).tab('show');
        
        if (hasMPAdminAccess) {
            $(".admin-designs-wrapper").css("visibility", "inherit");
        } else {
            $('#nav-asset-manager-tab').hide();
        }

        switch(functionality) {
            case "designs":
                // Load design as default lists
                loadDesignsList(false, designId);

                // clear search
                designSearch = '';
                loadedDesignSearch = '';
                $('.mp-designer__search-bar__input').val('');
                
                break;

            case "exports":
                loadDesignsExportsList('newest', designId)
                break;

            case "guidelines":
                loadBrandGuidelines()
                break;

            case "designs-search":
                loadDesignsSearch();
                loadDesignsList(false, designId);
                break;
        }
    }

    $(".nav-help-container .btn").click(function() {
        firstLoad = true; sammy.quiet = false;
        sammy.setLocation("/wiki/#%2Fopenkb%2Fkb%2FDKfXpubVUu4MUtCI");
        firstLoad = false;
    })
}

function getS3FileType(url) {
    const array = url.split('.')
    return array[array.length - 1]
}

function chooseDesignType() {

    const setupHTML = `<div class="create-new-design-option panel-accordian-group ph-dialog-v2" style="padding: 20px;">
        <h4>${t('Which type of design would you like to create?')}</h4>
        <div class="panel-group full-body" id="accordion_5" role="tablist" aria-multiselectable="true">
        <div class="panel">
            <div class="panel-heading" role="tab" id="dynamicDesign">
                <div class="inputGroup">
                    <input id="dynamic_radio" name="select_radio" type="radio" />
                    <label for="dynamic_radio" role="button" id="dynamic_design" href="#collapseOne_5" aria-controls="collapseOne_5" class="collapsed">${t('Dynamic Design')}</label>
                </div>
            </div>
            <div id="collapseOne_5" class="panel-collapse in" role="tabpanel" aria-labelledby="dynamicDesign">
                <div class="panel-body">
                    <p>${t('A dynamic design may have user/dynamically generated variables (such as text inputs, pictures, logos, etc) generated based on either the ID of a club, or the Brand of the club.')}</p>
                    <p>${t('Dynamically generated variables are rendered in real-time by our server before being downloaded in the desired format. Formats include JPG, TIFF, PNG, PDF, and PSD, and colour profiles for professional printing such as CMYK:FOGRA39 may also be selected/embedded in the exported image to ensure maximum print accuracy and colour reproduction.')}</p>
                </div>
            </div>
        </div>
        <div class="panel">
            <div class="panel-heading" role="tab" id="staticDesign">
                <div class="inputGroup">
                    <input id="static_radio" name="select_radio" type="radio"/>
                    <label for="static_radio" role="button" id="static_design" href="#collapseTwo_5" aria-controls="collapseTwo_5" class="collapsed">${t('Static File Upload')}</label>
                </div>
            </div>
            <div id="collapseTwo_5" class="panel-collapse in" role="tabpanel" aria-labelledby="staticDesign">
                <div class="panel-body">
                    <p>${t("Static designs or plain 'File Uploads' are may be uploaded to the Performance Hub Cloud as a single file/attachment.")}</p>
                    <p>${t('Static designs may be "filtered" per club/brand, however no dynamic generation/modification of design files are performed. Uploaded files are directly downloaded to the end user\'s computer.')}</p>
                </div>
            </div>
        </div>
    </div>`

    let newDesignType;
    swalWithBootstrapButtons.fire({
        width: 600,
        title: t('New Design'),
        showConfirmButton: true,
        confirmButtonText: t('Create'),
        cancelButtonText: t('Cancel'),
        showCancelButton: true,
        html: setupHTML,
        animation: false,
        showCloseButton: true,
        onOpen: function() {

            // Remove all errors
            $('.create-new-design-option').find('.error').remove()
            $(".swal2-confirm").prop('disabled', true);
            
            $('#dynamic_design').on('click', function() {
                $('#static_radio').attr('checked', false)
                $('#dynamic_radio').attr('checked', true)
            })
        
            $('#static_design').on('click', function() {
                $('#dynamic_radio').attr('checked', false)
                $('#static_radio').attr('checked', true)
            })

            // Once an option is selected, allow creating a new design.
            $('input:radio').on('click', function() {
                $(".swal2-confirm").prop('disabled', false);
            })

            const swalElement = Swal.getPopup();
            $(swalElement).draggable({
                handle: '.swal2-header',  // Makes the title bar the drag handle
            });
            // Only change the cursor to move for the title bar
            $(swalElement).find('.swal2-header').css('cursor', 'move');

        },
        preConfirm: () => {

            // Remove all errors
            $('.create-new-design-option').find('.error').remove()

            const isValid = $('input:radio[name=select_radio]:checked').length !== 0

            if(!isValid) {
                $('.create-new-design-option').append(`
                    <p class="error">${t('You must select a design type to continue.')}</p>
                `)
            } else {
                $('input:radio[name=select_radio]:checked').each(function () {
                    if($(this).attr('id') === 'dynamic_radio') {
                        newDesignType = 'dynamic';
                    } else {
                        newDesignType = 'static';
                    }
                });
            }

            return isValid
        }
    }).then(result => {

        if(result.value) {
            switch(newDesignType) {
                case 'dynamic':
                    newDesignDialog(currentFilteredDesignTag);
                    break;
                case 'static':
                    newStaticDesignDialog(currentFilteredDesignTag);
                    break;
            }
        }
    })
}

function loadBrandGuidelines() {

    let pdfConfigOpts = {
        fallbackLink: "<iframe data-hj-allow-iframe='' class='fallback-agreement-iframe' src='[url]#zoom=50'></iframe>",
        pdfOpenParams: {
            pagemode: "thumbs",
            navpanes: 0,
            toolbar: 0,
            statusbar: 0,
            // view: "FitV"
        }
    };
}

function loadDesignsExportsList(sortFilter='newest', loadRenderID=false) {

    // Display a skeleton screen while waiting for 'exports' to load...
    $(".design-exports").html(`
        <div class="container-fluid isLoading" style="margin-top:20px;">
          <!-- Main Content Skeleton -->
          <div class="row" style="height: calc(100vh - 240px);">
            <div class="col-xs-12 col-md-8 col-xl-11 col-lg-10">
              <!-- Large Placeholder for Main Content Title -->
              <div class="use-skeleton" style="height: 30px; width: 70%; margin-bottom: 20px;"></div>
              <!-- Multiple Line Placeholders for Main Content Text -->
              <div class="use-skeleton" style="height: 10px; width: 90%; margin-bottom: 10px;"></div>
              <div class="use-skeleton" style="height: 10px; width: 85%; margin-bottom: 10px;"></div>
              <div class="use-skeleton" style="height: 10px; width: 80%; margin-bottom: 10px;"></div>
              <!-- Block Placeholder for Main Content Image/Chart -->
              <div class="use-skeleton" style="height: 200px; width: 100%; margin-bottom: 20px;"></div>
            </div>
            <!-- Sidebar Skeleton -->
            <div class="col-xs-12 col-md-4 col-xl-1 col-lg-2">
              <!-- Small Block Placeholders for Sidebar Thumbnails -->
              <div class="use-skeleton" style="height: 100px; width: 100%; margin-bottom: 10px;"></div>
              <div class="use-skeleton" style="height: 100px; width: 100%; margin-bottom: 10px;"></div>
              <div class="use-skeleton" style="height: 100px; width: 100%; margin-bottom: 10px;"></div>
            </div>
          </div>
        </div>
    `);

    let storedRenders = {}

    $.ajax({
        url: `/api/marketing/designs/${ selectedID }/exports?sort=${sortFilter}`,
        method: 'GET',
        contentType: 'application/json',
        success: function(files){

            $(".design-exports").html(`
                <div class="export-filters-toolbar hidden-xs hidden-sm">

                    <label for="sortExportsBy">${t('Sort By')}:</label>
                    <select name="sortExportsBy" class="sortExportsBy">
                      <option value="newest" ${(sortFilter == 'newest') ? 'selected' : ''}>${t('Most Recent Export')}</option>
                      <option value="oldest" ${(sortFilter == 'oldest') ? 'selected' : ''}>${t('Oldest Exports First')}</option>
                      <option value="design" ${(sortFilter == 'design') ? 'selected' : ''}>${t('Group by Design')}</option>
                    </select>

                </div>

                <div id="inline-gallery-container" class="inline-gallery-container"></div>
            `);

            $(".design-exports .sortExportsBy").on('change', function (e) {
                let optionSelected = $("option:selected", this);
                loadDesignsExportsList(this.value);
            });


            const lgContainer = document.getElementById('inline-gallery-container');
            let exportedItems = []

            if(files.length == 0) {
                $(".design-exports").html(`
                    <div class="row" style="padding-top: 50px; padding-bottom: 50px;">
                        <div class="col-sm-12">
                            <center>
                                <h3>${t('There are no exported designs for your club:')} ${selectedID}</h3>
                                <hr>
                                <p>
                                    ${t('After you have \'Exported\' a design, previous historic exports will appear in a gallery here.')}
                                </p>
                                <p>
                                    <a href="/designer/designs">${t('Browse designs to begin')}</a>
                                </p>
                            </center>
                        </div>
                    </div>
                `);
            } else {

                files.forEach(exportedDesign => {

                    // Push into local object...
                    storedRenders[exportedDesign.renderId] = exportedDesign;
                    let thumbnailURL = exportedDesign.files.filter(file => { return file.type === 'thumbnail.sm' })[0].location;

                    // Known file icons/types...
                    let designFileIcon = 'unknown'
                    let fileExtension = (exportedDesign.metadata.fileFormat === undefined) ? 'unknown' : exportedDesign.metadata.fileFormat

                    let renderPreviewImageURL = exportedDesign.files.filter(file => { return file.type === 'thumbnail.lg' })[0].location;
                    let renderThumbURL = exportedDesign.files.filter(file => { return file.type === 'thumbnail.sm' })[0].location;
                    let renderURL = exportedDesign.files.filter(file => { return file.type === 'render' })[0].location;

                    const iconLibraryFormats = ['jpeg', 'jpg', '7z', 'aac', 'ai', 'archive', 'arj', 'audio', 'avi', 'css', 'csv', 'dbf',
                        'doc', 'dwg', 'exe', 'fla', 'flac', 'gif', 'html', 'iso', 'js', 'json', 'mdf', 'mp2', 'mp3', 'mp4',
                        'mxf', 'nrg', 'pdf', 'png', 'ppt', 'psd', 'rar', 'rtf', 'svg', 'text', 'tiff', 'txt', 'unknown', 'video',
                        'wav', 'wma', 'xls', 'xml', 'zip'
                    ];
                    if(iconLibraryFormats.includes(fileExtension)) designFileIcon = fileExtension;

                    exportedItems.push({
                        src: renderPreviewImageURL,
                        thumb: renderThumbURL,
                        renderId: exportedDesign.renderId,
                        subHtml: `
                            <div class="metadata">
                                <div class="d-flex justify-between">
                                    <div class="metadata-title">
                                        <h3>${exportedDesign.metadata.fileName}</h3>
                                    </div>
                                    <div class="metadata-buttons">
                                        <button data-renderid="${exportedDesign.renderId}" class="btn btn-sm btn-success btn-outline waves-effect download-rendered-file">
                                            <i class="fa fa-cloud-download" aria-hidden="true"></i>
                                            <span>${t('Download File')}</span>
                                        </button>
                                        <button data-renderid="${exportedDesign.renderId}" data-designid="${exportedDesign.designId}" class="btn btn-sm btn-primary btn-outline waves-effect re-render-design">
                                            <i class="fa fa-file-image-o" aria-hidden="true"></i>
                                            <span>${t('Re-Render Design')}</span>
                                        </button>
                                        <button data-designid="${exportedDesign.designId}" class="btn btn-sm btn-default btn-outline waves-effect load-original-design">
                                            <i class="fa-solid fa-circle-info"></i>
                                            <span>${t('Design Details')}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-left">
                                        <div class="metadata-container">
                                            <div class="icon hidden-xs hidden-sm">
                                                <img target="_blank" class="file-img" src="/assets/images/pretty_file_icons/${designFileIcon}.svg" alt="${designFileIcon}.svg" />
                                            </div>
                                            <div class="data">
                                                <span>${t('Exported')}: ${ moment(exportedDesign.renderedAt * 1000).format('LLLL') } by: ${exportedDesign.renderedByUser }</span><br>
                                                <span>${t('Color Mode')}: ${exportedDesign.metadata.colorMode}, ICC Profile: ${exportedDesign.metadata.colorProfile}, Resolution: ${exportedDesign.metadata.resolution} DPI</span><br>
                                                <span>${t('Export ID')}: <code>${exportedDesign.renderId}</code></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `,
                    })

                });

                const lgConfig = {
                    container: lgContainer,

                    addClass: 'lg-custom-thumbnails',
                    appendSubHtmlTo: '.lg-item',
                    appendThumbnailsTo: '.lg-outer',

                    plugins: [lgZoom, lgThumbnail],

                    // Turn off hash plugin in case if you are using it as we don't want to change the url on slide change
                    hash: false,
                    closable: false,
                    download: false,
                    counter: false,
                    showMaximizeIcon: false,

                    // Append caption inside the slide item
                    // to apply some animation for the captions (Optional)
                    animateThumb: false,
                    allowMediaOverlap: true,

                    // Delay slide transition to complete captions animations
                    // before navigating to different slides (Optional)
                    slideDelay: 0,

                    dynamic: true,
                    dynamicEl: exportedItems,
                };

                // Go to a specific "render/side ID" if it is specified...
                const defaultSlideIndex = _.findIndex(exportedItems, { renderId: loadRenderID })
                if (defaultSlideIndex !== -1) lgConfig.index = defaultSlideIndex;

                const lg = lightGallery(lgContainer, lgConfig);

                lgContainer.addEventListener('lgAfterAppendSubHtml', () => {

                    // Only bind listener once (we may cycle through the previews more than once)
                    $(lgContainer).find(".load-original-design").off('click').click(function() {

                        try {
                            let design = storedDesigns[$(this).data('designid')]

                            if(design === undefined) {

                                instantNotification(
                                    // Message...
                                    `${t('Could not find source design.')}+\n${t('The design you are trying to open may have been removed or no longer published.')}\n\n${t('Please contact support if you require additional assistance.')}`,
                                    // Type...
                                    'error',
                                    // Timeout (1m)...
                                    20000
                                );

                            } else {
                                console.log('design', design)
                                const thumbnailMedium = (design.thumbnails !== undefined) ? design.thumbnails.filter(file => { return file.type === 'thumbnail.md' })[0].location : design.thumbnails_large[0] || 'about:blank';
                                console.log('thumbnailMedium', thumbnailMedium)
                                openDesignDialog(thumbnailMedium, $(this).data('designid'))
                            }

                        } catch(e) {
                            // Could not get the current design URl - it's probably not loaded in the browser's cache.
                            openDesignDialog('about:blank', $(this).data('designid'))
                        }

                    });

                    // Only bind listener once (we may cycle through the previews more than once)
                    $(lgContainer).find(".re-render-design").off('click').click(function() {

                        try {

                            let renderData = storedRenders[$(this).data('renderid')]
                            if(renderData === undefined) {

                                instantNotification(
                                    // Message...
                                    `${t('Could not find source design.')}+\n${t('The design you are trying to open may have been removed or no longer published.')}\n\n${t('Please contact support if you require additional assistance.')}`,
                                    // Type...
                                    'error',
                                    // Timeout (1m)...
                                    20000
                                );


                            } else {

                                let prefillValues = {}

                                if(renderData.metadata.userVariables !== undefined) {                                
                                    renderData.metadata.userVariables.forEach(variable => {
                                        prefillValues[variable.name] = variable.value
                                    })
                                }

                                if(renderData.metadata.textAreaVariables !== undefined) {                                
                                    renderData.metadata.textAreaVariables.forEach(variable => {
                                        prefillValues[variable.name] = variable.value
                                    })
                                }

                                quickRenderDocument(renderData.designId, false, prefillValues)

                            }

                        } catch(e) {

                            // Could not get the current design URl - it's probably not loaded in the browser's cache.
                            console.log('error:', e)
                            instantNotification(
                                // Message...
                                `${t('Could not find source design.')}+\n${t('The design you are trying to open may have been removed or no longer published.')}\n\n${t('Please contact support if you require additional assistance.')}`,
                                // Type...
                                'error',
                                // Timeout (1m)...
                                20000
                            );
                        }


                    })

                    $(lgContainer).find(".download-rendered-file").off('click').click(function() {
                        
                        // console.log($(this).data('renderid'))
                        let exportedDesign = storedRenders[$(this).data('renderid')]
                        mp_downloadFile(exportedDesign.files.filter(file => { return file.type === 'render' })[0].location, exportedDesign.metadata)
                        
                    })
                    
                });

                lg.openGallery();

            }

        },
        error: function(error) {
            console.log(error)
        }
    });

}

function renderItemThumb(design, getActive = false, dontLoadThumbnail = false) {
  let designVisibilityTags, brandTags, designSearchTags;

  let lastUpdated = moment((design.updated === undefined || design.updated === false) ? new Date() : design.updated * 1000).format('LLLL');
  let scaleTo = (design.scaleTo === undefined) ? 1 : design.scaleTo;
  let printDimensions = (design.width === undefined || design.height === undefined) ? 'Automatic' : (design.width * scaleTo) + 'x' + (design.height * scaleTo);
  let printDPI = (design.resolution === undefined) ? 75 : design.resolution;

  switch((design.designVisibility === undefined) ? '' : design.designVisibility.toLowerCase()) {
      case 'club':
          designVisibilityTags = `<span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--club">${t('Club')}: ${ design.club }</span>`
          break;
      case 'region':
          designVisibilityTags = `<span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--region">${design.designRegion}</span>`
          
          break;
      default:
          // Default to Global Design
          designVisibilityTags = `<span class="mp-designer__thumb__details__tag">${t('Global')}</span>`
  }

  brandTags = (design.brand.toLowerCase() == 'all') ? `<span class="mp-designer__thumb__details__tag">${t('Multi-brand')}</span>` : `<span class="mp-designer__thumb__details__tag">${t('Brand')}: ${ design.brand.toUpperCase() }</span>`

  let scalingText;
  if(design.scaling) {
      scalingText = `<i>(${t('Device Scaling')}: ${scaleTo} + :1)</i>`
  } else {
      scalingText = '';
  }

  design.tags.forEach(function(tag) {
      designSearchTags += ` ${tag}`
  });

  let thunmnailSmall = design.thumbnails?.filter(file => file.type === 'thumbnail.sm')?.[0]?.location ?? 
      (design.thumbnail ?? 'about:blank');

  let thunmnailMedium = design.thumbnails?.filter(file => file.type === 'thumbnail.md')?.[0]?.location ?? 
      (design.thumbnails_large?.[0] ?? 'about:blank');

  const activeThumbnail = thunmnailMedium !== 'about:blank' ? thunmnailMedium : thunmnailSmall;

  if(getActive) return activeThumbnail;

  const notApprovedBadge = `<span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--not-approved use-skeleton">${t('Not Approved')}</span>`;

  const thumbnailSrc = `${ activeThumbnail + '?cachebust=' + activeUUID }`;

  const designItemThumb = `
    <div class="mp-designer__thumb animate-fade-in" data-id="${ design.id }" data-draft="${ design.designType === 'dynamic' ? thunmnailMedium : design.thumbnails_large || design.draft }">
      <div class="mp-designer__thumb__images use-skeleton rounded mp-designer__thumb__images--single">
        <div class="mp-designer__thumb__img"><img ${dontLoadThumbnail ? `data-src="${ thumbnailSrc }"` : `src="${ thumbnailSrc }"`} onerror="this.classList.add('img-not-found'); this.classList.remove('img-loading')" onload="this.classList.remove('img-loading')" class="img-loading"></div>
      </div>
      <div class="mp-designer__thumb__details">
        <p class="mp-designer__thumb__details__title use-skeleton rounded" data-search="${ design.name + designSearchTags + design.id }">
        ${ design.name }
        </p>
        <div class="mp-designer__thumb__details__meta">
          <span class="mp-designer__thumb__details__meta-icon use-skeleton rounded">
            <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 1.5A6.5 6.5 0 1 0 14.5 8 6.507 6.507 0 0 0 8 1.5Zm0 12A5.5 5.5 0 1 1 13.5 8 5.506 5.506 0 0 1 8 13.5ZM12 8a.5.5 0 0 1-.5.5H8a.5.5 0 0 1-.5-.5V4.5a.5.5 0 1 1 1 0v3h3a.5.5 0 0 1 .5.5Z" fill="currentColor"/></svg>
          </span>
          <span class="mp-designer__thumb__details__meta-text use-skeleton rounded">
            ${ lastUpdated }
          </span>
        </div>
        <div class="mp-designer__thumb__details__meta">
          <span class="mp-designer__thumb__details__meta-icon use-skeleton rounded">
            <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.667 13.333V10h.666v2.667H6v.666H2.667Zm7.333 0v-.666h2.667V10h.666v3.333H10ZM2.667 6V2.667H6v.666H3.333V6h-.666Zm10 0V3.333H10v-.666h3.333V6h-.666Z" fill="currentColor"/></svg>
          </span>
          <span class="mp-designer__thumb__details__meta-text use-skeleton rounded">
            ${ 
                (design.designType === 'dynamic') ?
                `${t('Dynamic Design')} : ${ printDimensions + ' @ ' + printDPI + ' DPI ' + scalingText }` :
                `${t('Static Design')} : ${ design.name }`
            }
          </span>
        </div>
        <div class="mp-designer__thumb__details__tags">
          ${ designVisibilityTags }
          ${ brandTags }
          ${
            (design.isPublished === true) ? `` : `<span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--not-published use-skeleton">${t('Not Yet Published')}</span>`
          }
          ${
            (design.approvalStatus === undefined || design.false) ?
            notApprovedBadge : (design.approvalStatus == false) ?
            notApprovedBadge : ``
          }
        </div>
      </div>
    </div>
  `;
  return designItemThumb;
}

function renderDesignsList(designItems, tag = false, fileType = 'all', disableNoResult = false, designsWrapperEl = '.mp-designer__items', disableViewAll = false, disableRerender = false, appendDesigns = [], foundItems = 0) {

  const designThumbsWrapper = $(designsWrapperEl);

  const filteredDesignItems = filterDesignsFiletype(designItems, fileType);

  const fileTypeCapitalized = fileType.charAt(0).toUpperCase() + fileType.slice(1);

  if(disableNoResult && filteredDesignItems.length === 0) return;

  if(filteredDesignItems.length === 0) {
    // ${t('There are no available Files or Designs for your club:')}
    designThumbsWrapper.append(`<div class="mp-designer__items__thumb-view">
      <div class="mp-designer__divider-text divider-text">
        ${
          disableViewAll ? '' : `<a class="btn btn-default waves-effect view-all-designs">
              <i class="material-icons">keyboard_backspace</i>
            ${t('View All Designs')}
          </a>`
        }
        <span class="use-skeleton rounded">${t('There are no available')} ${fileType !== 'all' ? t(fileTypeCapitalized) : ''} ${t('Designs for')} &quot;${tag || designSearch}&quot;</span>
      </div>
    </div>`);
    return;
  };

  let thumbnailItems = filteredDesignItems.map((design, i) => renderItemThumb(design, false));

  if(disableRerender && appendDesigns.length > 0) {
    thumbnailItems = appendDesigns.map((design, i) => renderItemThumb(design, false));

    designThumbsWrapper.find('.mp-designer__items__thumb-view').eq(1).append(thumbnailItems.join(''));
    return;
  }

  if(tag) {
    designThumbsWrapper.append(`<div class="mp-designer__items__thumb-view">
      <div class="mp-designer__divider-text divider-text">
        <a class="btn btn-default waves-effect view-all-designs">
            <i class="material-icons">keyboard_backspace</i>
            ${t('View All Designs')}
        </a>
        <span class="use-skeleton rounded">${t('Folder')}: &quot;${tag}&quot; (${filteredDesignItems.length} ${t('Designs')})</span>
      </div>
      ${thumbnailItems.join('')}
    </div>`);
  } else {
    designThumbsWrapper.append(`<div class="mp-designer__items__thumb-view">
      <div class="mp-designer__divider-text divider-text">
        ${
          designSearch && !disableViewAll
            ? `<a class="btn btn-default waves-effect view-all-designs">
                <i class="material-icons">keyboard_backspace</i>
                ${t('View All Designs')}
              </a>`
            : ''
        }

        <span class="use-skeleton rounded">${
            designSearch ? t('Search Results for: ') + `&quot;${designSearch}&quot; (<span class="mp-designer__items-count">${foundItems > 0 ? foundItems : filteredDesignItems.length}</span> ${t('Designs')})` : t('Files')
        }</span>
      </div>
      ${thumbnailItems.join('')}
    </div>`);
  }

}

function filterDesignsFiletype(designs, fileType, tag) {
  if(fileType === 'all') return designs;

  const tagFilteredDesigns = tag ? designs.filter(design => design.tags.includes(tag)) : designs;
  
  return tagFilteredDesigns.filter(design => design.designType === fileType);
}

function renderDesignsTable(designs, tag, fileType, designsWrapperEl = '.mp-designer__items', designSearch = false,  foundItems = 0) {
  const designThumbsWrapper = $(designsWrapperEl);

  const fileTypeCapitalized = fileType.charAt(0).toUpperCase() + fileType.slice(1);

  designThumbsWrapper.html('<div class="mp-designer__items__thumb-view mp-designer__items__thumb-view--no-grid"></div>');

  const filteredDesigns = designs.filter(design => (fileType === 'all' ? true : design?.designType === fileType) || design.items);

  if(filteredDesigns.length === 0) {
    designThumbsWrapper.find('.mp-designer__items__thumb-view').html(`
      <div class="mp-designer__divider-text divider-text">
        <a class="btn btn-default waves-effect view-all-designs">
            <i class="material-icons">keyboard_backspace</i>
            ${t('View All Designs')}
        </a>
        <span class="use-skeleton rounded">${t('There are no available')} ${fileType !== 'all' ? t(fileTypeCapitalized) : ''} ${t('Designs for')} &quot;${tag || designSearch}&quot;</span>
      </div>
    `);
    return;
  };
  
  if(tag || designSearch) {
    designThumbsWrapper.find('.mp-designer__items__thumb-view').html(`
      <div class="mp-designer__divider-text divider-text">
        <a class="btn btn-default waves-effect view-all-designs">
            <i class="material-icons">keyboard_backspace</i>
            ${t('View All Designs')}
        </a>
        <span class="use-skeleton rounded">${ designSearch ? t('Search Results for:') + `&quot;${designSearch}&quot; (${foundItems > 0 ? foundItems : filteredDesigns.length})` : t('Folder') + `: &quot;${tag}&quot; (${filteredDesigns.length}) ${t('Designs')}`}</span>
      </div>
    `);
  }

  designThumbsWrapper.find('.mp-designer__items__thumb-view').append(`
    <table width="100%" class="table animate-fade-in">
      <thead>
          <tr>
              <th class="hidden-xxs">
                  <div class="dataTable__header">
                      <span class="use-skeleton rounded"></span>
                      <span class="dataTable__header__icon use-skeleton rounded"></span>
                  </div>
              </th>
              <th>
                  <div class="dataTable__header">
                      <span class="use-skeleton rounded">${t('Folder/Filename')}</span>
                      <span class="dataTable__header__icon use-skeleton rounded"></span>
                  </div>
              </th>
              <th class="hidden-xxs">
                  <div class="dataTable__header">
                      <span class="use-skeleton rounded">${t('File Type')}</span>
                      <span class="dataTable__header__icon use-skeleton rounded"></span>
                  </div>
              </th>
              <th class="hidden-xs">
                  <div class="dataTable__header">
                      <span class="use-skeleton rounded">${t('Design Count')}</span>
                      <span class="dataTable__header__icon use-skeleton rounded"></span>
                  </div>
              </th>
              <th>
                  <div class="dataTable__header dataTable__header--justify-end">
                      <span class="use-skeleton rounded">${t('Date/Time')}</span>
                      <span class="dataTable__header__icon use-skeleton rounded"></span>
                  </div>
              </th>
          </tr>
      </thead>
      <tbody></tbody>
    </table>
  `);

  function renderSingleDesign(item) {
    if(!item) return '';

    return `
      <tr data-id="${ item.id }" data-draft="${ renderItemThumb(item, true) }">
        <td class="hidden-xxs">
          <div class="mp-designer__thumb--sm">
            <div class="mp-designer__thumb__img--sm use-skeleton rounded"><img src="${renderItemThumb(item, true)}?cachebust=${ activeUUID }"onerror="this.classList.add('img-not-found'); this.classList.remove('img-loading')" onload="this.classList.remove('img-loading')" class="img-loading" /></div>
          </div>
        </td>
        <td><span class="use-skeleton rounded">${item.name}</span></td>
        <td class="text-capitalize hidden-xxs"><span class="use-skeleton rounded">${t(item.designType)}</span></td>
        <td class="hidden-xs"><span class="use-skeleton rounded">1 ${t('Design Available')}</span></td>
        <td class="text-right"><span class="use-skeleton rounded">${moment(Number(item.updated) * 1000).format('MMM DD, YYYY - hh:mm A')}</span></td>
      </tr>
    `;
  }

  designThumbsWrapper.find('tbody').append(filteredDesigns.map(design =>{

    let thumbnails = '';

    if(design.tag === 'noTags' && design?.items?.length > 0) {
      let items = '';
      design.items.forEach(item => {

        items += renderSingleDesign(item);
      });

      return items;
    }

    if(design?.items) {
      thumbnails = design.items.map(item => renderItemThumb(item, true)).filter(thumb => thumb !== 'about:blank').slice(0, 5).map(thumbnail => `<div class="mp-designer__thumb__img--sm"><img src="${thumbnail}?cachebust=${ activeUUID }" onerror="this.classList.add('img-not-found'); this.classList.remove('img-loading')" onload="this.classList.remove('img-loading')" class="img-loading" /></div>`);

      const availableDesignTypes = [...new Set(design.items.map(item => item.designType))];

      const count = fileType ? filterDesignsFiletype(design.items, fileType).length : design.count;

      const lastModifiedDate = design.items.reduce((latest, item) => {

        return new Date(item.updated) > new Date(latest.updated) ? item : latest;
      }, design.items[0]);

      if(!count && !designSearch) return;

      return  `
        <tr data-tag="${design.tag}">
          <td class="hidden-xxs">
            <div class="mp-designer__thumb--sm">
              ${thumbnails.length ? thumbnails.join('') : `<div class="mp-designer__thumb__img--sm use-skeleton rounded">${designSearch && searchTags[design.tag] ? '<i class="material-icons">folder_open</i>' : '<img src="about:blank" class="img-not-found">'}</div>`}
            </div>
          </td>
          <td>
            <span class="use-skeleton rounded">${design.tag}</span>
          </td>
          <td class="text-capitalize hidden-xxs">
            <span class="use-skeleton rounded">${availableDesignTypes.length ? (availableDesignTypes.length > 1 ? t('Mixed') : availableDesignTypes[0]) : ''}</span>
          </td>
          <td class="hidden-xs">
            <span class="use-skeleton rounded">${count ? `${count} ${t('Designs Available')}` : ''}</span>
          </td>
          <td class="text-right">
            <span class="use-skeleton rounded">${lastModifiedDate?.updated ? moment(Number(lastModifiedDate.updated) * 1000).format('MMM DD, YYYY - hh:mm A') : ''}</span>
          </td>
        </tr>
      `;
    }

    if(design?.name) {
      return renderSingleDesign(design);
    }

    return '';
  }).join(''));

  const dtConfig = {
    responsive: true,
    dom: '<"toolbar">rtip',
    order: [[ 1, "asc" ]],
    // stateSave: true,
    pageLength: designSearch ? -1 : 10,
    columnDefs: [
      {
        targets: "no-sort",
        orderable: false,
      },
      { "width": "110px", "targets": 0 },
    ],
    paging: designSearch || filteredDesigns.length <= 10 ? false : true,
    info: false,
    language: {
      infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
      emptyTable: t('No data available in table'),
      zeroRecords: t('No matching records found'),
      infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
      aria: {
        sortAscending:  `: ${t('activate to sort column ascending')}`,
        sortDescending: `: ${t('activate to sort column descending')}`
      }
    },
  }
  if(getOS() == 'ios' || getOS() == 'android') dtConfig.scrollX = true;
  designTable = new DataTable(`${designsWrapperEl} .table`, dtConfig);

  $(`${designsWrapperEl}`).on('click', 'tbody tr',function() {
    const dataTag = $(this).data('tag');
    const file = $(this).data('draft');
    const id = $(this).data('id');

    if(dataTag) {
      showTagView(dataTag);
      return
    }

    openDesignDialog(file, id)
  });
}

function renderDesignTags(tagsObj, fileType = false, designsWrapperEl = '.mp-designer__items', designSearch = false) {

  const designThumbsWrapper = $(designsWrapperEl);
  designThumbsWrapper.html('');
  designThumbsWrapper.html(`<div class="mp-designer__items__thumb-view"><div class="mp-designer__divider-text divider-text">
    ${designSearch ? `<a class="btn btn-default waves-effect view-all-designs">
            <i class="material-icons">keyboard_backspace</i>
            ${t('View All Designs')}
        </a>` : ''}
    <span class="use-skeleton rounded">${designSearch ? t('Found folders for:') + ' &quot;' + designSearch + '&quot;' : t('Folders')}</span>
  </div></div>`);

  let tagsList = '';

  // Grouping tags found...
  Object.keys(tagsObj).sort((a, b) => {
    if(dataSort === 'name' && !isNameSortAsc) {
      return a.localeCompare(b);
    }
  
    if(dataSort === 'name' && isNameSortAsc) {
      return b.localeCompare(a);
    }

    if(dataSort === 'date updated') {
      
      const aItems = tagsObj[a].items.sort(designItemsSort);
      const bItems = tagsObj[b].items.sort(designItemsSort);
      const aItemsUpdated = aItems[0]?.updated || 0;
      const bItemsUpdated = bItems[0]?.updated || 0;

      if(!isDateSortAsc) {
        return Number(aItemsUpdated) - Number(bItemsUpdated);
      }

      return Number(bItemsUpdated) - Number(aItemsUpdated);
    }
  }).forEach(function(tag) {
    if(tag === 'noTags') return;

    const thumbnails = tagsObj[tag].items.map((design) => renderItemThumb(design, true)).filter(thumb => thumb !== 'about:blank').slice(0, 10).map((thumbnail, i) => {
      const thumbnailSrc = `${thumbnail}?cachebust=${ activeUUID }`;
      return `<div class="mp-designer__thumb__img"><img ${i === 0 ? `src="${thumbnailSrc}"` : `data-src="${thumbnailSrc}"`} onerror="this.classList.add('img-not-found'); this.classList.remove('img-loading')" onload="this.classList.remove('img-loading')" class="img-loading" loading="lazy" /></div>`
    });
    let count = fileType ? filterDesignsFiletype(tagsObj[tag].items, fileType).length : tagsObj[tag].count;

    if(count === 0 && !designSearch) return;

    if(designSearch) {
      count = tagsObj[tag].count;
    }

    tagsList += `
          <div class="mp-designer__thumb animate-fade-in" data-tag="${tag}">
            ${count ? `<span class="mp-designer__thumb__count use-skeleton rounded--full">${count}</span>` : ''}
            <div class="mp-designer__thumb__images use-skeleton rounded">
                ${thumbnails.length ? thumbnails.join('') : `<svg width="100" height="100" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path d="M13.5 4.625H8.156L6.412 2.881a.871.871 0 0 0-.619-.256H2.5a.875.875 0 0 0-.875.875v9.039a.837.837 0 0 0 .836.836h11.095a.82.82 0 0 0 .819-.82V5.5a.875.875 0 0 0-.875-.875Zm-11-1.25h3.293c.033 0 .065.013.088.037l1.213 1.213H2.375V3.5a.125.125 0 0 1 .125-.125Zm11.125 9.18a.07.07 0 0 1-.07.07H2.462a.087.087 0 0 1-.086-.086V5.375H13.5a.125.125 0 0 1 .125.125v7.056Z" fill="currentColor"/></svg>`}
            </div>
            <div class="mp-designer__thumb__details">
              <p class="mp-designer__thumb__details__title use-skeleton rounded">
              ${tag}
              </p>
              <div class="mp-designer__thumb__details__meta mp-designer__thumb__details__meta--highlight">
                <span class="mp-designer__thumb__details__meta-icon use-skeleton rounded">
                  <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.5 4.625H8.156L6.412 2.881a.871.871 0 0 0-.619-.256H2.5a.875.875 0 0 0-.875.875v9.039a.837.837 0 0 0 .836.836h11.095a.82.82 0 0 0 .819-.82V5.5a.875.875 0 0 0-.875-.875Zm-11-1.25h3.293c.033 0 .065.013.088.037l1.213 1.213H2.375V3.5a.125.125 0 0 1 .125-.125Zm11.125 9.18a.07.07 0 0 1-.07.07H2.462a.087.087 0 0 1-.086-.086V5.375H13.5a.125.125 0 0 1 .125.125v7.056Z" fill="currentColor"/></svg>
                </span>
                <span class="mp-designer__thumb__details__meta-text use-skeleton rounded">${count ? count : ''} ${t('Designs Available')}</span>
              </div>
            </div>
          </div>
          `;
  });

  designThumbsWrapper.find('.mp-designer__items__thumb-view').append(tagsList);

  designThumbsWrapper.find('.mp-designer__thumb').each(function(){
    
    let count = 0;
    const currentThumbEl = $(this);
    const thumbsContainer = currentThumbEl.find(".mp-designer__thumb__images");
    const thumbnailImgs = thumbsContainer.find('.mp-designer__thumb__img');
    let timoutId = undefined;
    let done = false;

    currentThumbEl.hover(function() {
      // add hover event to toggle images
      if(typeof timoutId !== 'undefined') {
        clearTimeout(timoutId);
      }

      thumbsContainer.find('img').each(function(){
        const dataSrc = $(this).data('src');
        if(dataSrc) {
          $(this).attr('src', dataSrc);
          $(this).removeAttr('data-src');
        }
      });
  
      function updateActiveThumb() {
        if(done) return;
        thumbsContainer.find('.mp-designer__thumb__img').removeClass('mp-designer__thumb__img--active');
        thumbsContainer.find('.mp-designer__thumb__img').eq(1).addClass('mp-designer__thumb__img--active');
        thumbsContainer.find('.mp-designer__thumb__img').eq(0).clone().appendTo(thumbsContainer);
        count++;
        if(count >= thumbnailImgs.length) {
          count = 0;
        }
  
        timoutId = setTimeout(() => {
          thumbsContainer.find('.mp-designer__thumb__img').eq(0).remove();
          updateActiveThumb();
        }, 1500);
      }
  
      currentThumbEl.addClass('showing-active');
      thumbsContainer.find('.mp-designer__thumb__img').removeClass('mp-designer__thumb__img--active');
      thumbsContainer.find('.mp-designer__thumb__img').eq(0).addClass('mp-designer__thumb__img--active');
      done = false;
      setTimeout(() => {
        updateActiveThumb();
      }, 1500);
      
    }, function() {
      done = true;
      currentThumbEl.removeClass('showing-active');
      thumbsContainer.find('.mp-designer__thumb__img').removeClass('mp-designer__thumb__img--active');
      // thumbsContainer.find('.mp-designer__thumb__img').eq(0).addClass('mp-designer__thumb__img--active');
      clearTimeout(timoutId);
    })
  });
}

const designItemsSort = (a, b) => {
  if(dataSort === 'name' && !isNameSortAsc) {
    return a.name.localeCompare(b.name);
  }

  if(dataSort === 'name' && isNameSortAsc) {
    return b.name.localeCompare(a.name);
  }

  if(dataSort === 'date updated' && !isDateSortAsc) {
    return Number(b.updated) - Number(a.updated);
  }
  return Number(a.updated) - Number(b.updated);
}

function matchDesignSearch(design) {
  const designName = design?.name?.toLowerCase() || '';
  const designTags = design?.tags?.map(tag => tag.toLowerCase()) || [];
  const designTag = design?.tag?.toLowerCase() || '';
  const searchLower = designSearch.toLowerCase();

  return designName.includes(searchLower) || designTags.some(tag => tag.includes(searchLower)) || designTag.includes(searchLower);
}

async function loadDesignsList(dontFetchData = false, designId = false) { 
    // scroll to top
    $('.mp-designer > .container-fluid').scrollTop(0);

    // trigger new design dialog
    $('.mp-designer__new-design').on('click', () => {
      chooseDesignType();
    });

    const activeView = localStorage.getItem('designerAllDesigns');
    const adminAllDesigns = activeView === 'admin';

    const loadedDesign = [];

    const savedFileType = localStorage.getItem('designFileType') || 'all';
    const fileType = initialLoad ? 'all' : savedFileType;
  

    let getStaticDesign = () => new Promise(function (resolve, reject) {

        if(fileType === 'dynamic') return resolve([]);

        $.ajax({
            url: '/api/marketing/designs/static/' + selectedID + '/list',
            method: 'GET',
            data: { 
                brand: selectedBrand,
                designAdmin: adminAllDesigns
            },
            contentType: 'application/json',
            success: function(response){

                response.forEach(res => {
                    res.designType = 'static'
                    loadedDesign.push(res)
                })
                resolve(response)
            },
            error: function(error) {
                console.log(error)
                reject(error)
            }
        });
    });

    let getDynamicDesign = () => new Promise(function (resolve, reject) {

      if(fileType === 'static') return resolve([]);
      
        $.ajax({
            url: '/api/marketing/designs/' + selectedID + '/list',
            method: 'GET',
            data: { 
                brand: selectedBrand,
                designAdmin: adminAllDesigns
            },
            contentType: 'application/json',
            success: function(response){

                response.forEach(res => {

                    res.designType = 'dynamic'
                    loadedDesign.push(res)
                })
                resolve(response)
            },
            error: function(error) {
                console.log(error)
                reject(error)
            }
        });
    });
    
    $('#designs-filter-section').addClass('isLoading');
    initNewVersion().designerSkeletonLoaders(true, false, '#designs-list');

    if(!dontFetchData) {
      await Promise.all([getStaticDesign(), getDynamicDesign()]);

      $('.mp-designer__view__option').filter(`[data-view="${dataView}"]`).addClass('mp-designer__view__option--active');

      if(dataView === 'table') {
        $('.mp-designer__filter-wrapper--sort').addClass('hidden');
      }

      initialLoad = false;
    }

    initNewVersion().generateDesignFilters(filterDesigns);
    initNewVersion().designerSkeletonLoaders(true, true, '#designs-list');

    
    // apply initial load sorting
    let data = loadedDesign.sort(designItemsSort);

    $('#designs-filter-section').removeClass('isLoading');

    if(!dontFetchData) {
      // Reset designs object...
      storedDesigns = {};
      storedTagsObj = {};
      
      // save data to storedDesigns 
      data.forEach(design => {
        storedDesigns[design.id] = design
      });

      storedTagsObj.noTags = {
        tag: 'noTags',
        count: data.filter(design => !design?.tags?.length).length,
        tagEl: '',
        items: data.filter(design => !design?.tags?.length)
      };
      
      // apply sorting here...
      const designTags = data.map(design => design.tags).flat();
      
      const uniqueDesignTags = [...new Set(designTags)];

      // all tag items will be part of object prop
      uniqueDesignTags.forEach(tag => {
        if(storedTagsObj[tag] === undefined) {
          storedTagsObj[tag] = {
            tag: tag,
            count: 0,
            tagEl: '',
            items: []
          };
          designGroups[tag] = tag;
        }
      });

      // add count to each tag object
      uniqueDesignTags.forEach(tag => {
        storedTagsObj[tag].count = designTags.filter(t => t === tag).length;
        storedTagsObj[tag].items = data.filter(design => design.tags.includes(tag)).map(design => ({
          ...design,
          tag
        }));

        if(searchTags[tag]) {
          searchTags[tag] = storedTagsObj[tag];
        }
      });

      // updateMPIndex();
    }

    if(Object.keys(storedDesigns).length === 0) {            
        $(".designs-list").html(`
            <div class="row" style="padding-top: 50px; padding-bottom: 50px;">
                <div class="col-sm-12">
                    <center>
                        <h3>${t('There are no available Files or Designs for your club:')} ${selectedID}</h3>
                    </center>
                </div>
            </div>
        `);
        return;
    }

    if(dataView === 'table') {
      const designs = currentFilteredDesignTag ? storedTagsObj[currentFilteredDesignTag]?.items?.sort(designItemsSort) || [] : Object.values(storedTagsObj);
      renderDesignsTable(designs, currentFilteredDesignTag, savedFileType, '#designs-list');
    } else {
      if(currentFilteredDesignTag) {
        renderDesignsList(storedTagsObj[currentFilteredDesignTag]?.items?.sort(designItemsSort) || [], currentFilteredDesignTag, savedFileType, false, '#designs-list');
      }
  
      if(!currentFilteredDesignTag) {
        renderDesignTags(storedTagsObj, savedFileType, '#designs-list');
        renderDesignsList(filterDesignsFiletype(storedTagsObj.noTags.items.sort(designItemsSort), savedFileType, currentFilteredDesignTag, true), currentFilteredDesignTag, savedFileType, true, '#designs-list');
      }
    }

    // open design dialog if designId is provided
    if(designId && storedDesigns[designId]) {
      const designEl = $(`[data-id="${designId}"]`);
      const file = designEl.length ? designEl.data('draft') : storedDesigns[designId]?.draft;
      openDesignDialog(file, designId)
    }

    bindDesignActions();
}

function searchDesigns(options = {}) {
  const {
    searchTerms = '',
    club = selectedID,
    designType = null, 
    page = 1,
    onSuccess = () => {},
    onError = () => {}
  } = options;

  return $.ajax({
    url: '/api/algolia/search-marketing-print',
    method: 'POST',
    data: JSON.stringify({
      searchTerms,
      club,
      designType: designType !== 'all' ? designType : null,
      adminQuery: localStorage.getItem('designerAllDesigns') === 'admin',
      page
    }),
    contentType: 'application/json',
    success: function(response) {
      const designs = response.designs;
      const tags = response.tags;
      
      onSuccess({
        designs,
        tags,
        pagination: response.pagination
      });
    },
    error: function(error) {
      console.error('Error searching designs:', error);
      onError(error);
    }
  });
}

function loadDesignsSearch(dontFetchData = false) {
  $('.mp-designer > .container-fluid').scrollTop(0);

  $('#designs-search-filter-section').addClass('isLoading');
  initNewVersion().designerSkeletonLoaders(true, false, '#designs-search-results');
  const savedFileType = localStorage.getItem('designFileType') || 'all';

  if(designSearch === '' || dontFetchData) {
    $('#designs-search-filter-section').removeClass('isLoading');
    initNewVersion().designerSkeletonLoaders(true, true, '#designs-search-results');
  }
  
  if(designSearch === '')  { 
    setTimeout(() => {
      $('.nav-tabs a[href="#designs"]').click();
      $('.nav-tabs a[href="#designs"]').tab('show');
    }, 500);

    loadDesignsList(true);
    return
  }

  loadedDesignSearch = designSearch;

  const onSuccess = ({designs, tags, pagination}) => {

    // remove loadMoreEl if it exists
    $('.load-more-designs').remove();
    
    // empty search results & tags for new search
    if(pagination.currentPage === 1) {
      $('#designs-search-filter-section').removeClass('isLoading');
      initNewVersion().designerSkeletonLoaders(true, true, '#designs-search-results');
      searchResults = {};
      searchTags = {};
    }

    // save search results
    if(designs.length) {
      designs.forEach(design => {
        searchResults[design.id] = design
      });
    }

    // render tags - render only on first page
    if(tags.length && pagination.currentPage === 1) {
      tags.forEach(tag => {
        searchTags[tag] = {
          tag,
          items: storedTagsObj[tag]?.items || [],
          count: storedTagsObj[tag]?.count || undefined
        };
      });
      renderDesignTags(searchTags, savedFileType, '#designs-search-results', designSearch);
    }

    if(dataView === 'grid') {
      renderDesignsList(Object.values(searchResults), false, savedFileType, tags.length, '#designs-search-results', tags.length, pagination.currentPage > 1, designs, pagination.totalHits || 0);
    } else {
      renderDesignsTable([...Object.values(searchResults), ...Object.values(searchTags)], false, savedFileType, '#designs-search-results', designSearch);
    }

    // remove container scroll event
    $('.mp-designer > .container-fluid').off('scroll');

    if(pagination.totalPages > 1 && pagination.currentPage < pagination.totalPages) {
      const loadMoreEl = $(`<div class="load-more-designs isLoading">${dataView === 'table' ? DesigntableSkeletonLoader : DesignSkeletonLoader}</div>`);
      $('#designs-search-results').append(loadMoreEl);

      $('.mp-designer > .container-fluid').on('scroll', () => {
        // check if loadMoreEl is visible in the viewport
        const loadMoreElRect = loadMoreEl[0].getBoundingClientRect();
        const isVisible = (loadMoreElRect.top - 100) < $('.mp-designer > .container-fluid').height();
        
        if(isVisible) {
          searchDesignOptions.page = pagination.currentPage + 1;
          searchDesigns(searchDesignOptions);
          $('.mp-designer > .container-fluid').off('scroll');
        }
      });
    }

    bindDesignActions();
  }

  const searchDesignOptions = {
    searchTerms: designSearch,
    designType: savedFileType,
    page: 1,
    onSuccess,
    onError: (error) => {
      initNewVersion().designerSkeletonLoaders(true, true, '#designs-search-results');
      $('#designs-search-filter-section').removeClass('isLoading');
    }
  }
  
  if(!dontFetchData) {
    searchDesigns(searchDesignOptions);
    return;
  }

  const designs = Object.values(searchResults);
  const tags = Object.values(searchTags);

  if(dataView === 'grid') {
    if(tags.length) {
      renderDesignTags(searchTags, savedFileType, '#designs-search-results', designSearch);
    }
    renderDesignsList(designs, false, savedFileType, tags.length, '#designs-search-results', tags.length);
  } else {
    renderDesignsTable([...designs, ...tags], false, savedFileType, '#designs-search-results', designSearch);
  }
  bindDesignActions();
}

function loadDesignsByTag(tag, sortedData = null, sortName = '', sortDate = '') {
    // Sort name by default ascending
    if(!sortedData) {
        sortByNameAsc(currentFilteredDesign)
    }
    
    $("#designs .designs-list").html(`
        <div class="all-designs-container">
            <a class="btn btn-default waves-effect view-all-designs">
                <i class="material-icons">keyboard_backspace</i>
                ${t('View All Designs')}
            </a>
        </div>
        <div class="group-title">
            <span>
                ${t('Viewing Folder')}: &quot;${tag}&quot;
            </span>
        </div>
        <div class="no-filter-results hidden m-t-60">
            <center>
                <h4>
                    ${t('No results found for the term')}: &quot;<span class="search-term"></span>&quot;
                </h4>
            </center>
        </div>
        <div class="sort-container">
            <button type="button" class="btn btn-default waves-effect btn-sm sort-name">
                ${t('Name')}
                <div class="sort-toggle ${sortName} ${defaultSortName}">
                    <span class="caret asc"></span>
                    <span class="caret desc"></span>
                </div>
            </button>
        
            <button type="button" class="btn btn-default waves-effect btn-sm sort-date">
                ${t('Date Modified')}
                <div class="sort-toggle ${sortDate}">
                    <span class="caret asc"></span>
                    <span class="caret desc"></span>
                </div>
            </button>
        </div>
        <div class="design-list-container">
            ${sortedData ? sortedData.join(" ") : designGroups[tag].elements.join(" ")}
        </div>
    `);

    $("#designs .tags-list").html("");

    currentFilteredDesignTag = tag;

    // Bind functions that is related to design
    bindSortActions(tag)
    bindDesignActions();
    bindSearchActions();
}

const sortByNameAsc = (data) => (
    data.sort((a, b) => {
        const aName = $(a).find('.design-text .title-stack').html()
        const bName = $(b).find('.design-text .title-stack').html()
        return aName.localeCompare(bName)
    })
)

const sortByNameDesc = data => (
    data.sort((a, b) => {
        const aName = $(a).find('.design-text .title-stack').html()
        const bName = $(b).find('.design-text .title-stack').html()
        return aName.localeCompare(bName)
    }).reverse()
)

const sortByDateAsc = data => (
    data.sort((a, b) => {
        const aDate = new Date($(a).find('.last-updated').html()).getTime()
        const bDate = new Date($(b).find('.last-updated').html()).getTime()
        return bDate - aDate
    })
)

const sortByDateDesc = data => (
    data.sort((a, b) => {
        const aDate = new Date($(a).find('.last-updated').html()).getTime()
        const bDate = new Date($(b).find('.last-updated').html()).getTime()
        return aDate - bDate
    })
)

function bindSortActions(tag) {

    let sortName;
    let sortDate;

    $('.sort-name, .sort-date').click(function () {
        $("#designs .designs-list").html(" ");
        defaultSortName = ''
    })

    // Sort design by name asc/desc
    $('.sort-name').on("click", function () {
        let sorted;
        isDateSortAsc = false

        if (isNameSortAsc) {
            sorted = sortByNameDesc(currentFilteredDesign);
            sortName = 'desc'
        } else {
            sorted = sortByNameAsc(currentFilteredDesign);
            sortName = 'asc'
        }

        isNameSortAsc = !isNameSortAsc
        loadDesignsByTag(tag, sorted, sortName, sortDate = '')
    })

    // Sort design by date asc/desc
    $('.sort-date').on('click', function () {
        let sorted;
        isNameSortAsc = false

        if (isDateSortAsc) {
            sorted = sortByDateDesc(currentFilteredDesign);
            sortDate = 'desc'
        } else {
            sorted = sortByDateAsc(currentFilteredDesign);
            sortDate = 'asc'
        }

        isDateSortAsc = !isDateSortAsc
        loadDesignsByTag(tag, sorted, sortName = '', sortDate)
    })
}

/**
 * Bind Delete Design & Preview Functionality  
 * for both static & dynamic designs
 * 
 */
function bindDesignActions() {

    $(".view-all-designs").off('click').on('click', function () {

        currentFilteredDesignTag = false;
        designSearch = '';
        loadedDesignSearch = '';

        loadDesignsList(true);

        const href = `/designer/designs`
        history.pushState({ designTagChange: true, }, null, href);

        $('.nav-tabs a[href="#designs"]').tab('show');

        $(".search-bar").val("");
    });

    // Show preview of design (limited to the pre-rendered "draft" image...)
    $("#designs .designs-list .design-link, #designs .mp-designer__thumb, #designs-search .mp-designer__thumb").off('click').on('click', function () {

      const dataTag = $(this).data('tag');

        if(dataTag) {
          showTagView(dataTag);
          return;
        }

        const file = $(this).data('draft')
        openDesignDialog(file, $(this).data('id'))
    });

    const viewOption = $('.mp-designer__view__option');
    viewOption.removeClass('mp-designer__view__option--active');

    viewOption.filter(`[data-view="${dataView}"]`).addClass('mp-designer__view__option--active');

    viewOption.off('click').on('click', function() {

      dataView = $(this).data('view');

      viewOption.removeClass('mp-designer__view__option--active');
      $(`[data-view="${dataView}"]`).addClass('mp-designer__view__option--active');

      if(dataView === 'grid') {
        $('.mp-designer__filter-wrapper--sort').removeClass('hidden');
      } else {
        $('.mp-designer__filter-wrapper--sort').addClass('hidden');
      }
      
      localStorage.setItem('designView', dataView);

      if(designSearch) {
        loadDesignsSearch(true);
      } else {
        loadDesignsList(true);
      }
    });

    
    $('.designs-search-form').off('submit').on('submit', function(e) {
      e.preventDefault();

      if(designSearch === '' || loadedDesignSearch === designSearch) return;

      history.pushState({ designTagChange: true }, null,'/designer/designs-search?designSearch=' + encodeURIComponent(designSearch));
      $('#nav-designs-search-tab a').tab('show');

      loadDesignsSearch();
    });

    $('.mp-designer .search-bar').val(designSearch);
    let searchTimeout;
    $('.mp-designer .search-bar').off('input').on('input', function() {
      clearTimeout(searchTimeout);
      designSearch = $(this).val();
      $('.mp-designer .search-bar').val(designSearch);
      
      searchTimeout = setTimeout(() => {
        $('.designs-search-form').submit();
      }, 3000);
    });
}

function showTagView(tag) {

    if(!tag) return

    // render tag items
    currentFilteredDesignTag = tag;

    const encoded = encodeURIComponent(currentFilteredDesignTag)
    const href = `/designer/designs?tag=${encoded}`;

    history.pushState({ designTagChange: true }, null, href);

    // change tab to designs
    $('.nav-tabs a[href="#designs"]').tab('show');

    // clear designSearch 
    designSearch = '';
    loadedDesignSearch = '';

    // load designs
    loadDesignsList(true);
}

/**
 * Bind Design Search Action
 * 
 */
function bindSearchActions() {
    // Bind basic search/filter functionality...

    $(".search-bar").on("keyup search", function() {
        filterResults($(this).val().toLowerCase());
    });

    function filterResults(text) {
        let currentFilteredDesignCount = 0,
        noResultsEl = $('#designs .designs-list .no-filter-results'),
        sortBtnEl = $('#designs .designs-list .sort-container')

        // Set to its original state
        currentFilteredDesign = [];
        sortBtnEl.removeClass('hidden')
        noResultsEl.removeClass('visible').addClass('hidden')

        // We can't just use "toggle" directly on flex elements...
        $(".tags-list .design-text .title-stack").filter(function() {
            if($(this).data('search').toLowerCase().indexOf(text.toLowerCase()) > -1) {
                $(this).parent().parent().parent().parent().removeClass("hidden")
            } else {
                $(this).parent().parent().parent().parent().addClass("hidden")
            }
        });

        $(".designs-list .design-text .title-stack").filter(function() {
            $(this).parent().parent().parent().toggle(
                $(this).data('search').toLowerCase().indexOf(text.toLowerCase()) > -1
            )

            // Use the filtered element on the sorting
            $(this).parent().parent().parent().parent().each(function () {

                currentFilteredDesign.push($(this).prop('outerHTML'))

                //Check how many visible elements after filter
                if($(this).children().is(':visible')) currentFilteredDesignCount++;
            })
        });

        if(text !== '' && currentFilteredDesignCount === 0) {
            noResultsEl.removeClass('hidden').addClass('visible')
            sortBtnEl.addClass('hidden')

            // Set what the current search term was...
            $(".no-filter-results .search-term").html(text);
        }

        // if(text == "") { $("#designs .designs-list .group-title").show(); } else { $("#designs .designs-list .group-title").hide(); }
    }

    publicFilterResults = filterResults;
}

function openDesignDialog(url, id) {
   
    let showAdminElements = hasMPAdminAccess ? (localStorage.getItem("designerAllDesigns") === null) ? false : (localStorage.getItem("designerAllDesigns").toLowerCase() == 'true' || localStorage.getItem("designerAllDesigns").toLowerCase() == 'admin') ? true : false : false

    const previewUrls = url.split(',');
    
    const design = storedDesigns[id] || searchResults[id];
    console.log('design:', id, design);

    const { thumbnails_large = `[]` } = design || {};
    const updatedString = typeof thumbnails_large === 'string' ? thumbnails_large.replace(/\\/g, '').replace(/\"\"/g, '').replace(/\"\[/g, '[').replace(/\]\"/g, ']') : null;
            
    // console.log({ updatedString, id, thumbnails_large });
    const thumbnailParse = updatedString ? JSON.parse(updatedString) : thumbnails_large;
    let renderPreviewImgLargeURL = (design.thumbnails !== undefined) ? design.thumbnails.filter(file => { return file.type === 'thumbnail.lg' })[0].location : thumbnailParse[0] || previewUrls[0];

    let renderPreviewImg = new Image();
    let magnifyObj;

    renderPreviewImg.src = thumbnailParse[0] || previewUrls[0];
    renderPreviewImg.id = 'previewRenderImg';


    const designDialogContainer = document.createElement("div");
    designDialogContainer.className = "preview-render-modal";
    designDialogContainer.classList.add("ph-dialog-v2");
    
    let lastUpdated = moment((design.updated === undefined || design.updated === false) ? new Date() : design.updated * 1000).format('LLLL');

    let scaleTo = (design.scaleTo === undefined) ? null : design.scaleTo;
    let printDimensions = (design.width === undefined || design.height === undefined) ? null : (design.width * scaleTo) + 'x' + (design.height * scaleTo);
    let printDPI = (design.resolution === undefined) ? null : design.resolution;
    let designFormat = (design.format === undefined) ? null : design.format;
    let designFileFormat = (design.url === undefined) ? null : design.url.split('.').pop();
    let designFilePages = (design.thumbnails_large === undefined) ? null : (
        (design.thumbnails_large.length > 1) ? design.thumbnails_large.length : null
    );

    let designTags = '', variablesTags = '';

    // Grouping tags found...
    if(design.tags !== undefined) {
        design.tags.forEach(function(tag) {
            designTags += `
                <span class="badge badge-tag filter"><i class="fa fa-folder-o" aria-hidden="true"></i>&nbsp; ${tag}</span>
            `;
        });
    }

    if(design.variablesInHtml !== undefined) {    
        design.variablesInHtml.forEach(function(variable) {
            variablesTags += `
                <span class="badge badge-variable filter">${variable}</span>
            `;
        });
    }

    const userCanEditData = (design.designVisibility === 'global' && hasMPAdminAccess)
        || (design.designVisibility === 'club' && ClubsObj.some(club => club.id === design.club))
        || (design.designVisibility === 'region' && design.designRegion.every(region => mpAccessibleRegions.some(r => r.iso_code === region)));

    const userCantEditAlert = userCanEditData ? '' : `
        <div class="alert alert-blue" role="alert">
            ${t('You do not have permission to edit this design. Please contact your administrator for more information.')}
        </div>
    `;

    const visibilityOptions = [
        { value: 'club', label: t('Restrict to Club') },
        { value: 'region', label: t('Restrict to Region') },
    ];

    if (!userCanEditData || hasMPAdminAccess) {
        visibilityOptions.push({ value: 'global', label: t('Globally Accessible') })
    }

    $(designDialogContainer).html(`

        <ul class="nav nav-tabs" role="tablist" style="display: block;">
            <li class="hidden-xs hidden-sm active" role="presentation" style="transform: translateY(5px);"><a href="#design_preview" data-toggle="tab" aria-expanded="true">${t('Overview')}</a></li>

        ${
            // -- User has permission to administer document
            // NB API PERMISSIONS APPLY HERE FOR USER PERMISSION - THIS LOGIC JUST HIDES FRONT-END ELEMENTS THAT END USERS CAN'T ACCESS

            (!showAdminElements) ? '' : `
            <li class="pull-right hidden-xs hidden-sm" role="presentation" style="transform: translateY(5px);"><a href="#design_edit" id="edit-metadata-tab" data-toggle="tab" aria-expanded="false"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> ${t('Edit Metadata / Design')}</a></li>
            <li class="pull-right hidden-xs hidden-sm" role="presentation" style="transform: translateY(5px);"><a href="#design_status" data-toggle="tab" aria-expanded="false">${t('Status / Publishing')}</a></li>
        `}

        </ul>

        <div class="tab-content">
            <div role="tabpanel" class="tab-pane fade active in" id="design_preview">

                <div class="isLoading loader">
                <h3>${t('Loading Design')}...</h3>
                <div class="row reorder-xs"><div class="col-xs-12 col-sm-8"><div class=preview-image><div class="use-skeleton magnify"></div></div></div><div class="col-xs-12 col-sm-4 overview-metadata"><div class=row><div class=col-sm-12><div class=usage-guidelines-container><h4 class="use-skeleton rounded">Usage & Guidelines:</h4><p><ul><li class="use-skeleton rounded">QR Will navigate you to the new club 'Links' service</ul><p></div></div></div><hr><div class=row><div class=col-sm-12><span class="use-skeleton rounded badge filter badge-tag"><i class="fa fa-folder-o"aria-hidden=true></i>  Club Essentials</span> <span class="use-skeleton rounded badge filter badge-tag"><i class="fa fa-folder-o"aria-hidden=true></i>  UBX Links</span><div class=metadata-content><p><i class="use-skeleton rounded material-icons rounded--full">access_time</i><span class="use-skeleton rounded">Monday, September 4, 2023 3:43 PM<span></span></span><p><i class="use-skeleton material-icons rounded--full">photo_size_select_large</i><span class="use-skeleton rounded">Design Scaling: 4</span><hr><p class=input-variables-title><b class="use-skeleton rounded">Input Variables:</b><p><span class="use-skeleton rounded badge filter badge-variable">ClubID</span> <span class="use-skeleton rounded badge filter badge-variable">ClubBrandShort</span> <span class="use-skeleton rounded badge filter badge-variable">Domain</span></div><div class=download-button><button class="use-skeleton rounded btn btn-outline btn-primary btn-sm render-design waves-effect"data-id=""type=button><i class="fa fa-paper-plane"aria-hidden=true></i> <span>Edit / Export</span></button></div></div></div></div></div></div>
                <div class="row reorder-xs preview-content" style="display: none;">
                    <div class="col-xs-12 col-sm-8">
                        ${
                            // set preview image html to be either single image download, or multiple pages file preview.                        
                            previewUrls.length > 1 ? `<div class="render-design-gallery"></div>` : `<div class="preview-image"></div>`
                        }
                    </div>
                    <div class="col-xs-12 col-sm-4 overview-metadata">
                        ${ 
                            (design.usageGuidelines === undefined || design.usageGuidelines === '') ? '' : `

                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="usage-guidelines-container">
                                            <h4>${t('Usage & Guidelines')}:</h4>
                                            ${ design.usageGuidelines }
                                        </div>
                                    </div>
                                </div>
                                <hr>

                            `
                        }

                        <div class="row">
                            <div class="col-sm-12">
                                ${designTags}
                                <div class="metadata-content">
                                    <p><i class="material-icons">access_time</i><span>${lastUpdated}<span></p>
                                    <p><i class="material-icons">cloud</i><span><code>${design.id}</code><span></p>

                                    ${(scaleTo === null) ? '' : `<p><i class="material-icons">photo_size_select_large</i><span>${t('Design Scaling')}: ${scaleTo}</span></p>`}
                                    ${(printDimensions === null) ? '' : `<p><i class="material-icons">crop_free</i><span>${t('Design Dimensions')}: ${printDimensions}</span></p>`}
                                    ${(printDPI === null) ? '' : `<p><i class="material-icons">blur_linear</i><span>${t('DPI')}: ${printDPI}</span></p>`}

                                    ${(designFormat === null) ? '' : `<p><i class="material-icons">file_download</i><span>${t('Preferred Format')}: ${designFormat}</span></p>`}
                                    ${(designFileFormat === null) ? '' : `<p><i class="material-icons">file_download</i><span>${t('File Format')}: ${designFileFormat}</span></p>`}
                                    ${(designFilePages === null) ? '' : `<p><i class="material-icons">restore_page</i><span>${t('File Pages')}: ${designFilePages}</span></p>`}

                                    <hr>
                                    ${(variablesTags) == '' ? '' : `
                                        <p class="input-variables-title"><b>${t('Input Variables')}:</b></p>
                                        <p>${variablesTags}</p>
                                    `}
                                </div>
                                <div class="download-button"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

    ${
        // -- User has permission to administer document
        // NB API PERMISSIONS APPLY HERE FOR USER PERMISSION - THIS LOGIC JUST HIDES FRONT-END ELEMENTS THAT END USERS CAN'T ACCESS

        (!showAdminElements) ? '' : `

            <div role="tabpanel" class="tab-pane fade" id="design_status">
                ${userCantEditAlert}
                <div class="edit-design-status-container text-left d-flex flex-column" style="height: 100%;">

                    <div class="row grow-1" style="overflow-y: auto; overflow-x: hidden;">
                        <div class="col-sm-7 col-md-6 publish-automation-container bottom-margin-for-bottom-buttons">

                            <b>${t('Current Status')}:</b> ${
                                (design.isPublished === true) ? `<span class="badge bg-light-blue">${t('Published')}</span>` : `<span class="badge bg-orange">${t('Not Yet Published')}</span>`
                            }
                            ${
                                (design.approvalStatus === undefined || design.false) ?
                                    `<span class="badge bg-orange">${t('Not Approved')}</span> <br><small>${t('Unapproved designs will not be shown to individual facilities')}</small>` : (design.approvalStatus == false) ?
                                        `<span class="badge bg-orange">${t('Not Approved')}</span> <br><small>${t('Unapproved designs will not be shown to individual facilities')}</small>` : `<span class="badge bg-light-blue">${t('Approved')}</span>`
                            }
                            <hr>

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>
                                        ${t('Set publish date (restrict access until this date)')} <br>
                                        <small>${t('Note timezone for publishing automation is based on UTC +0 Timezone')}</small>
                                    </p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <label>Date</label>
                                            <input data-hj-allow class='form-control publish-date' type='text' name='date' ${userCanEditData ? '' : 'disabled'}/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <label>Time</label>
                                            <input data-hj-allow class="form-control timepicker publish-time" ${userCanEditData ? '' : 'disabled'} />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-check styled-checkbox" style="margin-bottom: 25px;">
                                        <input type="checkbox" id="chk_publish_now" class="filled-in chk-col-blue" ${userCanEditData ? '' : 'disabled'}>
                                        <label for="chk_publish_now" class="checkbox-label">${t('Publish Now')}</label>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>
                                        ${t('Set expiration date (restrict access after this date)')} <br>
                                        <small>${t('Note timezone for publishing automation is based on UTC +0 Timezone')}</small>
                                    </p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <label>${t('Date')}</label>
                                            <input data-hj-allow class='form-control expire-date' type='text' name='date' ${userCanEditData ? '' : 'disabled'} />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <label>${t('Time')}</label>
                                            <input data-hj-allow class="form-control timepicker expire-time" ${userCanEditData ? '' : 'disabled'}>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-check styled-checkbox" style="margin-bottom: 25px;">
                                        <input type="checkbox" id="chk_never_expire" class="filled-in chk-col-blue" ${userCanEditData ? '' : 'disabled'}>
                                        <label for="chk_never_expire" class="checkbox-label">${t('Never Expire')}</label>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>
                                        ${t('Approval Status')} <br>
                                        <small>${t('Changing this status will be logged using')}: ${signedinUser}</small>
                                    </p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="switch">
                                        <label>${t('Unapproved')}<input data-hj-allow type="checkbox" id="approval_status" ${userCanEditData ? '' : 'disabled'}><span class="lever lever switch-col-blue"></span>${t('Approved')}</label>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="col-sm-5 col-md-4 col-md-push-2 bottom-margin-for-bottom-buttons">
                            <div class="row">
                                <div class="col-sm-12">
                                    <b>${t('Comments')}:</b> <br>
                                    <p>
                                        <small>${t('Please note, only Performance Hub admins can view and comment on designs, assets & files')}</small>
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <textarea data-hj-allow="" class="form-control design-conversation" placeholder="${t('Post a comment about this file')}" ${userCanEditData ? '' : 'disabled'}></textarea>
                                        </div>
                                    </div>
                                    <div class="comment-log"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    ${userCanEditData ? `
                        <div class="d-flex justify-end">
                            <button type="button" data-name="${ design.name }" data-id="${ design.id }" class="btn btn-sm btn-primary btn-border btn-save-design waves-effect">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                                ${t('Save Changes')}
                            </button>
                        </div>
                    ` : '' }
                </div>

            </div>
            <div role="tabpanel" class="tab-pane fade" id="design_edit">
                ${userCantEditAlert}
                <div class="design-metadata text-left d-flex flex-column" style="overflow-y: auto; height: 100%;">

                    <div class="d-flex gap-2 grow-1" style="overflow-y: auto;">
                        <div class="grow-1">

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>${t('Name')}</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <input data-hj-allow="" type="text" class="form-control" name="design-name" placeholder="${t('Name of file')}" value="${design.name}" ${userCanEditData ? '' : 'disabled'} />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>${t('Usage & Guidelines')}</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="design-guidelines-trumbowyg"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>
                                        ${t('Folders')} <br>
                                        <small>${t('To create a new folder, simply type in a new name and it will be created & assigned to this file')}</small>
                                    </p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <div class="doc-folders-container">
                                            <select class="design-folders" ${userCanEditData ? '' : 'disabled'}></select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-sm-12">
                                    <p>${t('Visibility')}</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-4">
                                    <select class="visibility-restrictions" data-width="100%" ${userCanEditData ? '' : 'disabled'}>
                                        ${visibilityOptions.map(({value, label, selected}) => `<option value="${value}" ${selected ? 'selected' : ''}>${label}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select data-width="100%" class="visibility-restrict-club" data-live-search="true" ${userCanEditData ? '' : 'disabled'}></select>
                                    <select data-width="100%" class="visibility-restrict-region" multiple="multiple" data-live-search="true" ${userCanEditData ? '' : 'disabled'}></select>
                                </div>
                            </div>

                            <div class="row" style="padding-top: 20px;">
                                <div class="col-sm-12">
                                    <p>${t('Brand Restrictions')}</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-4">
                                    <select class="brand-restrictions" data-width="100%" ${userCanEditData ? '' : 'disabled'}>
                                        <option value="ALL" ${ (design.brand !== undefined && design.brand == 'ALL') ? ' selected ' : '' }>${t('All - (Design works with any Brand)')}</option>
                                        <option value="12RND" ${ (design.brand !== undefined && design.brand == '12RND') ? ' selected ' : '' }>12RND</option>
                                        <option value="UBX" ${ (design.brand !== undefined && design.brand == 'UBX') ? ' selected ' : '' }>UBX</option>
                                    </select>
                                </div>
                            </div>

                        </div>
                        <div class="">
                            <div class="edit-small-img-preview"></div>

                            ${
                                (userCanEditData && design.designType !== undefined && design.designType.toLowerCase() == 'dynamic') ?
                                    `
                                        <div class="edit-delete-buttons">
                                            <button type="button" data-name="${ design.name }" data-id="${ design.id }" class="btn btn-sm btn-primary btn-outline btn-edit-dynamic-design waves-effect">
                                                <i class="fa fa-paint-brush" aria-hidden="true"></i>
                                                ${t('Author Dynamic Design in Editor')}
                                            </button>
                                        </div>
                                    ` : ``
                            }

                        </div>
                    </div>

                    ${userCanEditData ? `
                        <div class="d-flex justify-between">
                            <button type="button" data-name="${ design.name }" data-type="${design.designType}" data-id="${ design.id }" class="btn btn-sm btn-danger btn-outline btn-delete-file waves-effect">
                                <i class="fa fa-trash-o" aria-hidden="true"></i>
                                ${t('Delete')}
                            </button>
                            <button type="button" data-name="${ design.name }" data-id="${ design.id }" class="btn btn-sm btn-primary btn-border btn-save-design waves-effect">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                                ${t('Save Changes')}
                            </button>
                        </div>
                    ` : ``}

                </div>
            </div>

            <div class="modal fade" id="confirm-delete-design" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>${t('Delete Design?')}</h3>
                        </div>
                        <div class="modal-body">
                            <p>
                                <b>${t('Are you sure you wish to delete the file')} '${design.name}'?</b>
                            </p>
                            <p>
                                <b>${t("Please carefully check the file name before proceeding .... this cannot be undone, there is no 'recycling bin' and will require manual restoration should you delete by accident.")}</b>
                            </p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">${t('Cancel')}</button>
                            <button type="button" class="btn btn-danger btn-confirm-delete-design">${t('Delete')}</button>
                        </div>
                    </div>
                </div>
            </div>

        `
    // -- End user is admin...
    }
        </div>
    `);


    swalWithBootstrapButtons.fire({
        html: designDialogContainer,
        title: design.name,
        width: 'auto',
        showCloseButton: true,
        animation: false,
        showCancelButton: false,
        showConfirmButton: false,
        allowEscapeKey : false,
        allowOutsideClick: false,
        onBeforeOpen: () => {
            
            // ------ OVERVIEW ------

            addClipboardCopyButtons('.overview-metadata');

            let setupHTML = `
                <div class="gallery-container">
                    <div class="row">
                        <div class="col-xs-3 col-page-thumbnails">`;
        
                previewUrls.map((url, index) => {
                    setupHTML += `
                            <div class="column">
                                <img
                                    class="column-thumbnail"
                                    src="${url}"
                                    data-id="${index + 1}"
                                    alt="Preview-${index + 1}"
                                >
                            </div>
                    `
                })
        
                setupHTML += `
                        </div>
                        <div class="col-xs-9 col-preview-image">
                `;
        
                previewUrls.map(url => {
                    setupHTML += `
                            <div class="slides individual-thumbnail">
                                <img
                                    src="${url}"
                                    style="width:100%"
                                >
                            </div>
                    `
                })

                setupHTML += `
                        </div>
                    </div>
                </div>`;

            $('.render-design-gallery').html(setupHTML)

            // ------ STATUS, PUBLISHING & METADATA ------
            if (showAdminElements) {
                // Bind WYSIWYG
                $(".design-guidelines-trumbowyg").trumbowyg({
                    svgPath: '/assets/images/trumbowyg.svg',
                    removeformatPasted: true,
                    defaultLinkTarget: '_blank',
                    tagsToRemove: ['script', 'link', 'iframe', 'audio', 'image', 'video', 'basefont', 'base', 'body', 'bb', 'button', 'canvas', 'code', 'embed', 'frame', 'frameset', 'link', 'form', 'input', 'map', 'meta', 'noframes', 'noscript', 'object', 'textarea', 'track'],
                    btns: [
                        ['strong', 'italic', 'underline', 'strikethrough'],
                        ['unorderedList', 'orderedList'],
                    ],
                    disabled: !userCanEditData,
                });

                $(".design-guidelines-trumbowyg").trumbowyg('html', (design.usageGuidelines === undefined) ? '' : design.usageGuidelines);
                
                // Bind datepickers...
                $(designDialogContainer).find('input[name=date]').daterangepicker({
                    singleDatePicker: true,
                    showDropdowns: true,
                    // startDate: specialHour.date ? moment(specialHour.date * 1000) : moment(),
                    startDate: moment(),
                    ranges: {
                       'Today': [moment(), moment()],
                       'Tomorrow': [moment().add(1, 'days'), moment().add(1, 'days')],
                       '+7 Days': [moment().add(7, 'days'), moment().add(7, 'days')],
                    },
                    locale: {
                        format: 'MMMM DD, YYYY'
                    }
                });        
                // Set z-index after binding daterangepickers...
                $('.daterangepicker').css('z-index', 3000);
        
                // Bind Timepickers...
                $(designDialogContainer).find(".timepicker").timepicker();

                // Bind the 'tags' folder...
                // Todo: add the tags that are selected for the current design...

                const tags = Object.keys(designGroups)

                $('.design-folders').select2({
                    placeholder: 'Add or Select a Tag',
                    multiple: true,
                    tags: Object.keys(designGroups),
                    allowClear: true,
                    width: '100%',
                    dropdownCssClass: `design-tags__dropdown`
                })

                if(design.tags.length) {
                    let existingTags = []
                    let addedTags = ''

                    design.tags.forEach(function(tag) {

                        // Check if newly added tag is exists on the current tag
                        // If not, add an option then append the new option
                        if($.inArray(tag, tags) !== -1) {
                            existingTags.push(tag)
                        } else {
                            addedTags += `<option value=${tag} selected>${tag}</option>`
                            existingTags.push(tag)
                        }
                    })

                    $('.design-folders').val(existingTags).trigger('change')
                    $('.design-folders').append(addedTags).trigger('change')
                }

                // Build Regions Dropdown...
                mpAccessibleRegions.forEach(region => {
                    $('.visibility-restrict-region').append($('<option>', {
                        value: region.iso_code,
                        text: region.full_name
                    }));
                });

                // Build Clubs Dropdown...
                ClubsObj.forEach(club => {
                    $('.visibility-restrict-club').append($('<option>', {
                        value: club.id,
                        text: club.name
                    }));
                });

                // Bind delete, save & edit buttons in the admin/edit screen.../
                $(".btn-edit-dynamic-design").on('click', function (event) {

                    // Open editor (if it's not active in the UI)
                    $('.nav-tabs a[href="#editor"]').tab('show');
                    $("#nav-editor-tab").show();
                    Swal.close();

                    const designId = $(this).data('id');

                    if($("#nav-editor-tab").is(":visible")) {

                        if(docUnsaved) {
                            swalWithBootstrapButtons.fire({
                                title: t('Replace existing design'),
                                html:   `
                                        <div class="ph-dialog-v2" style="padding: 20px;">
                                        ${t("A design is currently being edited, and it's most recent changes have not yet been saved. Loading a new design will overwrite your work, and any changes that have not been saved will be lost. Continue?")}
                                        </div>
                                        `,
                                width: 500,
                                showCancelButton: true,
                                confirmButtonText: t('Yes, load new design'),
                                cancelButtonText: t('Cancel'),
                            }).then((result) => {
                                if (result.value) {
                                    loadDesignWrapper();
                                } else {
                                    callback(false);
                                }
                            })
                        } else {
                            loadDesignWrapper();
                        }

                    } else {
                        loadDesignWrapper();
                    }

                    function loadDesignWrapper() {
                        resetDocObject();
                        loadDesign(designId, true, function() {

                            // Delay clearing till animation of tab click is complete...
                            setTimeout(function(){
                                $(".search-bar").val("");
                            }, 200)
                        });
                    }

                    event.stopPropagation();
                    return true;
                });

                // Set selectpicker...
                $(designDialogContainer).find('select').selectpicker();
                setVisibilityFilters();

                // Set optional dropdown based on selected visibility...
                $('.visibility-restrictions').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
                    setVisibilityFilters();
                });

                function setVisibilityFilters() {
                    switch($('.visibility-restrictions').selectpicker('val')) {
                        case 'club':
                            $(".visibility-restrict-club").show();
                            $(".visibility-restrict-region").hide();
                            break;
                        case 'region':
                            $(".visibility-restrict-club").hide();
                            $(".visibility-restrict-region").show();
                            break;
                        default:
                            // Default to Global...
                            $(".visibility-restrict-club").hide();
                            $(".visibility-restrict-region").hide();
                    }
                }

                // Set current values in visibility filters...
                $('.visibility-restrictions').selectpicker('val', (design.designVisibility === undefined || design.designVisibility === null) ? 'global' : design.designVisibility);
                $('.visibility-restrict-region').selectpicker('val', (design.designRegion === undefined || design.designRegion === null) ? '' : design.designRegion);
                $('.visibility-restrict-club').selectpicker('val', (design.club === undefined || design.club === null || design.club === 'global') ? selectedID : design.club);

                // Publishing & Approval....
                $("#chk_publish_now").change(function() {
                    setPublishingStateDisabled(this.checked)
                });

                $("#chk_never_expire").change(function() {
                    setExpireStateDisabled(this.checked)
                });

                function setPublishingStateDisabled(state) {
                    if(state) {
                        $(".publish-date").prop("disabled", true);
                        $(".publish-time").prop("disabled", true);
                    } else {
                        $(".publish-date").prop("disabled", false);
                        $(".publish-time").prop("disabled", false);   
                    }
                }

                function setExpireStateDisabled(state) {
                    if(state) {
                        $(".expire-date").prop("disabled", true);
                        $(".expire-time").prop("disabled", true);
                    } else {
                        $(".expire-date").prop("disabled", false);
                        $(".expire-time").prop("disabled", false);
                    }
                }

                $("#approval_status").prop('checked', (design.approvalStatus === undefined || design.false) ? false : design.approvalStatus);

                let publishFromDate = (design.publishingAutomation === undefined  || design.publishingAutomation.autoPublish === undefined) ? moment().unix() * 1000 : design.publishingAutomation.autoPublish * 1000
                let pupublishFromDate = (design.publishingAutomation === undefined || design.publishingAutomation.autoUnPublish === undefined) ? moment().unix() * 1000 : design.publishingAutomation.autoUnPublish * 1000

                $('.publish-date').data('daterangepicker').setStartDate(moment(publishFromDate).format('MMMM Do YYYY, h:mm:ss a').split(',')[0])
                $('.publish-date').data('daterangepicker').setEndDate(moment(publishFromDate).format('MMMM Do YYYY, h:mm:ss a').split(',')[0])
                $('.publish-time').timepicker('setTime', new Date(publishFromDate));

                $('.expire-date').data('daterangepicker').setStartDate(moment(pupublishFromDate).format('MMMM Do YYYY, h:mm:ss a').split(',')[0])
                $('.expire-date').data('daterangepicker').setEndDate(moment(pupublishFromDate).format('MMMM Do YYYY, h:mm:ss a').split(',')[0])
                $('.expire-time').timepicker('setTime', new Date(pupublishFromDate));

                $("#chk_publish_now").prop('checked', (design.publishingAutomationPublishNow === undefined || design.false) ? true : design.publishingAutomationPublishNow);
                $("#chk_never_expire").prop('checked', (design.publishingAutomationNeverExpire === undefined || design.false) ? true : design.publishingAutomationNeverExpire);
                
                if((design.publishingAutomationPublishNow === undefined || design.false) ? true : design.publishingAutomationPublishNow) setPublishingStateDisabled(true)
                if((design.publishingAutomationNeverExpire === undefined || design.false) ? true : design.publishingAutomationNeverExpire) setExpireStateDisabled(true)

                if(design.internalComments !== undefined) {
                    design.internalComments.forEach(comment => {
                        $(".comment-log").append(`
                            <div class="comment-block">
                                <b>${(comment.user === undefined) ? t('Unknown User') : comment.user}</b>:<br> <span>${ (comment.comments === undefined) ? '' : comment.comments }</span>
                            </div>
                        `)
                    });
                    if(design.internalComments.length == 0) $(".comment-log").hide();
                } else {
                    $(".comment-log").hide();
                }

                $(".btn-save-design").on('click', function (event) {

                    // Update design object...
                    let designObject = design;

                    designObject.name = $(".design-metadata").find("input[name='design-name']").val()
                    designObject.usageGuidelines = $(".design-metadata").find('.design-guidelines-trumbowyg').trumbowyg('html')
                    designObject.tags = $(".design-metadata").find($(".design-folders")[1]).val()

                    // Set auto publishing
                    designObject.publishingAutomationPublishNow = $('#chk_publish_now').prop('checked')
                    designObject.publishingAutomationNeverExpire = $('#chk_never_expire').prop('checked')
                    
                    // Combine the date & time inputs into a single unix datetime obj.
                    let publishDateTime = moment(`${ $('.publish-date').data('daterangepicker').startDate.format('YYYY-MM-DD')}T${moment($('.publish-time')[0].timepickerObj.selectedValue, "h:mm:ss A").format("HH:mm:ss")}`).unix()
                    let unpublishDateTime = moment(`${ $('.expire-date').data('daterangepicker').startDate.format('YYYY-MM-DD')}T${moment($('.expire-time')[0].timepickerObj.selectedValue, "h:mm:ss A").format("HH:mm:ss")}`).unix()

                    designObject.publishingAutomation = {
                        autoPublish: publishDateTime,
                        autoUnPublish: unpublishDateTime,
                    }

                    // Approval...
                    designObject.approvalStatus = $('#approval_status').prop('checked')

                    // Comments/Design Conversation
                    if(designObject.internalComments === undefined) designObject.internalComments = []

                    if($(".design-conversation").val().length > 1) {
                        designObject.internalComments.push({
                            user: signedinUser,
                            comments: $(".design-conversation").val()
                        })
                    }

                    // Folder visibility...
                    designObject.tags = $(".design-metadata").find($(".design-folders")[1]).val()

                    // Set visibility based on visibility parameters...
                    let designClub = ($('.visibility-restrictions').selectpicker('val') !== 'club') ? selectedID : $(".visibility-restrict-club").selectpicker('val')
                    delete designObject.club

                    designObject.designVisibility = $('.visibility-restrictions').selectpicker('val')
                    designObject.designRegion = ($('.visibility-restrictions').selectpicker('val') !== 'region') ? null : $(".visibility-restrict-region").selectpicker('val')
                    designObject.brand = $('.brand-restrictions').selectpicker('val')

                    console.log('designObject', designObject);

                    saveDesignToDB(
                        (design.designType.toLowerCase() == 'dynamic') ?
                            // Save endpoint for Dynamic designs
                            `/api/marketing/designs/${ designClub }/save` :
                            // Save endpoint for Static designs
                            `/api/marketing/designs/static/${ designClub }/save`,

                        // Save the current loaded design-object...
                        designObject, function(callback) {
                            if(callback) {

                                instantNotification(
                                    // Message...
                                    t('Changes Saved')
                                );

                                loadDesignsList(true);
                                docUnsaved = false;
                            }
                        }
                    )

                    $.ajax({
                      url: `/api/algolia/update-marketing-print?facilityId=${selectedID}`,
                      method: 'POST',
                      data: JSON.stringify({ objectID: designObject.id, data: designObject }),
                      contentType: 'application/json'
                    });

                });

                $(".btn-delete-file").on('click', function (event) {

                    
                    const designType = $(this).data('type');
                    const designId = $(this).data('id');
                    const designIndex = $(this).parents('.design-link').index()                    

                    let designClub = (design.club === undefined || design.club === null) ? selectedID : design.club

                    console.log('designType', designType)
                    deleteDynamicOrStaticDesign(

                        // Save Endpoint:
                        (designType.toLowerCase() == 'dynamic') ?
                            // Save endpoint for Dynamic designs
                            `/api/marketing/designs/${ designClub }/save` :
                            // Save endpoint for Static designs
                            `/api/marketing/designs/static/${ designClub }/save`,

                        // Get Design Details Endpoint:
                        (designType.toLowerCase() == 'dynamic') ?
                            // Save endpoint for Dynamic designs
                            `/api/marketing/designs/get/${ designId }` :
                            // Save endpoint for Static designs
                            `/api/marketing/designs/static/get/${ designId }`,

                        // Callback logic...
                        function(callback) {
                            if(callback) {
                                currentFilteredDesign.splice(designIndex, 1)
                            }
                        }
                    )



                    event.stopPropagation();
                    return true;
                });

                function validate() {
                    var isValid = true;

                    // Elements that require at least some value...
                    if(! $("input[name='design-name']").val()) isValid = false;
                    if( ($('.visibility-restrictions').selectpicker('val') === 'region') ? $('.visibility-restrict-region').selectpicker('val') : 'true') { } else { isValid = false; }

                    if(isValid) {
                        $('.preview-render-modal .btn-save-design').prop('disabled', false);
                    } else {
                        $('.preview-render-modal .btn-save-design').prop('disabled', true);
                    }
                }
                validate();

                $(".preview-render-modal").click(function() { validate(); });
                $(".preview-render-modal").keyup(function() { validate(); });

            }

        },
        onOpen: () => {

            // Bind resizer event for admin window...
            if (hasMPAdminAccess) {
                $(window).resize(resizeAdminWindowDivs);
            }

            // ---------------- REGULAR VIEW ACCESS ----------------
            // Setup the 'pagination' if design has multiple files/pages...

            if(previewUrls.length > 1) {

                let slides = $('.render-design-gallery .slides');
                let thumbnails = $('.render-design-gallery .column-thumbnail');

                let slideIndex = 1;
                showSlides(slideIndex);

                thumbnails.on('click', function() {
                    const slideId = $(this).data('id');

                    currentSlide(slideId)
                })

                // Thumbnail image controls
                function currentSlide(n) {
                    showSlides(slideIndex = n);
                }

                function showSlides(n) {
                    var i;

                    if (n > slides.length) { slideIndex = 1 }
                    if (n < 1) { slideIndex = slides.length }

                    for (i = 0; i < slides.length; i++) {
                        slides[i].style.display = 'none';
                    }

                    for (i = 0; i < thumbnails.length; i++) {
                        thumbnails[i].className = thumbnails[i].className.replace(' active', '');
                    }

                    slides[slideIndex-1].style.display = 'block';
                    thumbnails[slideIndex-1].className += ' active';
                }
            }

            // Special functions to make the dialog Draggable...
            const swalElement = Swal.getPopup();
            $(swalElement).draggable({
                handle: '.swal2-header',  // Makes the title bar the drag handle
                // containment: [-500, -500, 1024, 1204]
                // containment: 'window'     // Prevents dragging outside the viewport
            });
            // Only change the cursor to move for the title bar
            $(swalElement).find('.swal2-header').css('cursor', 'move');


        },
        onClose: () => {
            // Remove resizer binding/events...
            $(window).off("resize", resizeAdminWindowDivs);
            try {
                magnifyObj.destroy();
            } catch(e) {}
        }
    })
    renderPreviewImg.onerror = function() {
        setPreviewImageDialog(false);
    }

    renderPreviewImg.onload = function() {
        setPreviewImageDialog(true);
    }


    function resizeAdminWindowDivs() {
        $(".design-metadata").height(
            $(".preview-render-modal").height() - 100
        );
    }

    function setPreviewImageDialog(status) {
        if(status) {

            $(designDialogContainer).find(".preview-content .preview-image, .edit-small-img-preview").html(renderPreviewImg)
            $(designDialogContainer).find(".preview-content #previewRenderImg").attr("data-magnify-src", renderPreviewImgLargeURL);
            
            // On mobile devices - don't setup the 'magnify' tool...
            switch(getOS()) {
              case 'ios':
                break;
              case 'android':
                break;
              default:
                magnifyObj = $(designDialogContainer).find("#previewRenderImg").magnify();
            }



        } else {
            $(designDialogContainer).find(".preview-content .preview-image, .preview-content .edit-small-img-preview").html(`
                <div class="preview-image-no-image">
                    <svg style="max-width: 50px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M474.1 474.1H37.9V322.4H0V512h512V322.4h-37.9zM0 0v267.6l58.5 30.1 63.8-55.5 66.6 59.9 67.1-56.7 67.1 56.7 66.6-59.9 63.8 55.5 58.5-30.1V0H0zm474.1 244.4l-15.1 7.8-69.7-60.7-66.9 60.2-66.3-56-66.3 56-66.9-60.2L53 252.2l-15.1-7.8V37.9H474v206.5z"/></svg>
                    <p>${t('No preview available')}</p>
                </div>
            `)
        }


        $(designDialogContainer).find(".loader").html('').hide();
        $(designDialogContainer).find(".row").show();

        if(design.designType === 'dynamic') {
            $(designDialogContainer).find('.download-button').append(`
                <button type="button" data-id="" class="btn btn-sm btn-primary btn-outline waves-effect render-design">
                    <i class="fa fa-paper-plane" aria-hidden="true"></i>
                    <span>${t('Edit / Export')}</span>
                </button>
            `);
        } else {
            $(designDialogContainer).find('.download-button').append(`
                <button type="button" data-id="" class="btn btn-sm btn-primary btn-outline waves-effect static-file-download">
                    <i class="fa fa-download" aria-hidden="true"></i>
                    <span>${t('Download File Directly')}</span>
                </button>
            `);
        }

        $(designDialogContainer).find('.render-design').on('click', function (event) {
            quickRenderDocument(id);
            event.stopPropagation();
            return true;
        });

        $(designDialogContainer).find('.static-file-download').on('click', function (event) {
            downloadStaticFile(id, design.name, design.url, getS3FileType(design.url))
            event.stopPropagation();
            return true;
        });
        // quickRenderDocument(id);
    }
}

const DesignThumbItem = `
      <div class="mp-designer__thumb">
        <span class="mp-designer__thumb__count use-skeleton rounded--full">45</span>
        <div class="mp-designer__thumb__images use-skeleton rounded">
  
        </div>
        <div class="mp-designer__thumb__details">
          <p class="mp-designer__thumb__details__title use-skeleton rounded">
            Club Essentials
          </p>
          <div class="mp-designer__thumb__details__meta mp-designer__thumb__details__meta--highlight">
            <span class="mp-designer__thumb__details__meta-icon use-skeleton rounded"></span>
            <span class="mp-designer__thumb__details__meta-text use-skeleton rounded">Meta text goes here</span>
          </div>
          <div class="mp-designer__thumb__details__tags">
            <span class="mp-designer__thumb__details__tag use-skeleton">Tag 1</span>
            <span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--not-approved use-skeleton">Tag 2</span>
          </div>
        </div>
      </div>
    `;
const DesignSkeletonLoader = `
  <div class="mp-designer__items__thumb-view isLoading">
    <div class="mp-designer__divider-text divider-text">
      <span class="use-skeleton rounded">{{Pinned}}</span>
    </div>
    ${
      Array.from({ length: 4 }).map( () => DesignThumbItem).join('')
    }
  </div>
`;

const DesigntableSkeletonLoader = `
    <table class="dataTable isLoading no-footer table" style="width:100%"width="100%"><thead><tr><th aria-controls="designs-table"class="sorting"colspan="1"rowspan="1"style="width:110px"tabindex="0"><div class="dataTable__header"><span class="rounded use-skeleton"></span> <span class="rounded use-skeleton dataTable__header__icon"></span></div><th aria-controls="designs-table"class="sorting sorting_asc"colspan="1"rowspan="1"style="width:506px"tabindex="0"aria-sort="ascending"><div class="dataTable__header"><span class="rounded use-skeleton">${t('Folder/Filename')}</span> <span class="rounded use-skeleton dataTable__header__icon"></span></div><th aria-controls="designs-table"class="sorting"colspan="1"rowspan="1"style="width:132px"tabindex="0"><div class="dataTable__header"><span class="rounded use-skeleton">File Type</span> <span class="rounded use-skeleton dataTable__header__icon"></span></div><th aria-controls="designs-table"class="sorting"colspan="1"rowspan="1"style="width:173px"tabindex="0"><div class="dataTable__header"><span class="rounded use-skeleton">${t('Design Count')}</span> <span class="rounded use-skeleton dataTable__header__icon"></span></div><th aria-controls="designs-table"class="sorting"colspan="1"rowspan="1"style="width:227px"tabindex="0"><div class="dataTable__header dataTable__header--justify-end"><span class="rounded use-skeleton">${t('Date/Time')}</span> <span class="rounded use-skeleton dataTable__header__icon"></span></div><tbody><tr class="odd"data-draft="https://s3.amazonaws.com/content.gymsystems.co/hub/design-render-exports/prd/24eb5b35-c1e7-42f1-8229-b0a5ad97389e.thumbnail.md"data-id="68bdab81-1ace-4dcb-842e-161d8b360c6f"><td><div class="mp-designer__thumb--sm"><div class="rounded use-skeleton mp-designer__thumb__img--sm"><img onerror='this.classList.add("img-not-found"),this.classList.remove("img-loading")'onload='this.classList.remove("img-loading")'src="https://s3.amazonaws.com/content.gymsystems.co/hub/design-render-exports/prd/24eb5b35-c1e7-42f1-8229-b0a5ad97389e.thumbnail.md?cachebust=30d7e871-6b47-4e9e-acfc-ba0fa23fb6e9"></div></div><td class="sorting_1"><span class="rounded use-skeleton">Corporate Business Cards - HQ - Page 1</span><td class="text-capitalize"><span class="rounded use-skeleton">dynamic</span><td><span class="rounded use-skeleton">1 Design Available</span><td class="text-right"><span class="rounded use-skeleton">Oct 17, 2022 - 07:22 AM</span><tr class="odd"data-draft="https://s3.amazonaws.com/content.gymsystems.co/hub/design-render-exports/prd/24eb5b35-c1e7-42f1-8229-b0a5ad97389e.thumbnail.md"data-id="68bdab81-1ace-4dcb-842e-161d8b360c6f"><td><div class="mp-designer__thumb--sm"><div class="rounded use-skeleton mp-designer__thumb__img--sm"><img onerror='this.classList.add("img-not-found"),this.classList.remove("img-loading")'onload='this.classList.remove("img-loading")'src="https://s3.amazonaws.com/content.gymsystems.co/hub/design-render-exports/prd/24eb5b35-c1e7-42f1-8229-b0a5ad97389e.thumbnail.md?cachebust=30d7e871-6b47-4e9e-acfc-ba0fa23fb6e9"></div></div><td class="sorting_1"><span class="rounded use-skeleton">Corporate Business Cards - HQ - Page 1</span><td class="text-capitalize"><span class="rounded use-skeleton">dynamic</span><td><span class="rounded use-skeleton">1 Design Available</span><td class="text-right"><span class="rounded use-skeleton">Oct 17, 2022 - 07:22 AM</span><tr class="odd"data-draft="https://s3.amazonaws.com/content.gymsystems.co/hub/design-render-exports/prd/24eb5b35-c1e7-42f1-8229-b0a5ad97389e.thumbnail.md"data-id="68bdab81-1ace-4dcb-842e-161d8b360c6f"><td><div class="mp-designer__thumb--sm"><div class="rounded use-skeleton mp-designer__thumb__img--sm"><img onerror='this.classList.add("img-not-found"),this.classList.remove("img-loading")'onload='this.classList.remove("img-loading")'src="https://s3.amazonaws.com/content.gymsystems.co/hub/design-render-exports/prd/24eb5b35-c1e7-42f1-8229-b0a5ad97389e.thumbnail.md?cachebust=30d7e871-6b47-4e9e-acfc-ba0fa23fb6e9"></div></div><td class="sorting_1"><span class="rounded use-skeleton">Corporate Business Cards - HQ - Page 1</span><td class="text-capitalize"><span class="rounded use-skeleton">dynamic</span><td><span class="rounded use-skeleton">1 Design Available</span><td class="text-right"><span class="rounded use-skeleton">Oct 17, 2022 - 07:22 AM</span></table>
`;

function initNewVersion() {
  let designFilters = {
    fileType: localStorage.getItem("designFileType") !== null ? localStorage.getItem("designFileType") : 'all', // static, dynamic
    view: localStorage.getItem("designerAllDesigns") === 'admin' ? 'admin' : 'club',
  };

  function generateDesignFilters(filterDesigns = () => {}) {

    new PageFilters(
        '.mp-designer__filters-container__filters',
        [
            {
                id: 'fileType',
                label: t('File Type'),
                options: [
                    {
                        label: t('All'),
                        value: 'all',
                        selected: designFilters['fileType'] === 'all',
                    },
                    {
                      label: t('Dynamic Design'),
                      value: 'dynamic',
                      selected: designFilters['fileType'] === 'dynamic',
                    },
                    {
                      label: t('Static File'),
                      value: 'static',
                      selected: designFilters['fileType'] === 'static',
                    },
                ],
                optional: false,
                visible: true,
                closedValue: 'active',
            },
            {
              id: 'view',
              label: t('View'),
              options: [
                  {
                      label: t('All Designs (Admin)'),
                      value: 'admin',
                      selected: designFilters['view'] === 'admin',
                  },
                  {
                    label: t('Published Designs Only'),
                    value: 'club',
                    selected: designFilters['view'] === 'club',
                },
              ],
              optional: false,
              visible: true,
              closedValue: 'active',
          }
        ],
        (filters) => {
            designFilters = { ...designFilters, ...filters };
            filterDesigns(designFilters);
        },
        true
    );
    $('.mp-designer__filters-container__filters').addClass(`mp-designer__filters--${hasMPAdminAccess ? 'admin' : 'club'}`);
  }

  function designerSkeletonLoaders(thumb = true, clear = false, parentSelector = '.mp-designer__items') {

    const elParent = $(parentSelector);

    if(clear) {
      elParent.empty();
      elParent.removeClass('isLoading');
      return;
    }
  
    // if thumb is false it will show the skeleton loader for the list view
    elParent.empty().html(dataView === 'table' ? DesigntableSkeletonLoader : DesignSkeletonLoader);
  
  }

  function feSortData() {
    const descIcon= `<svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.333 10.96V4.5a.5.5 0 0 0-1 0v6.46l-1.48-1.48a.5.5 0 1 0-.706.707l2.333 2.333a.5.5 0 0 0 .707 0l2.333-2.333a.5.5 0 1 0-.707-.707l-1.48 1.48Zm-11-6.127a.5.5 0 0 1 .5-.5h6.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.334a.5.5 0 0 1 .5-.5H5.5a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.333a.5.5 0 0 1 .5-.5h2.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Z" fill="currentColor"/></svg>`;

    const ascIcon = `<svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.333 11.5a.5.5 0 1 1-1 0V5.04l-1.48 1.48a.5.5 0 1 1-.706-.707L11.48 3.48a.5.5 0 0 1 .707 0l2.333 2.333a.5.5 0 0 1-.707.707l-1.48-1.48v6.46Zm-10.5.167a.5.5 0 1 1 0-1h6.334a.5.5 0 0 1 0 1H1.833Zm0-3.334a.5.5 0 0 1 0-1H5.5a.5.5 0 0 1 0 1H1.833Zm0-3.333a.5.5 0 0 1 0-1h2.334a.5.5 0 0 1 0 1H1.833Z" fill="currentColor"/></svg>`;

    $('.mp-designer__sort-by').off('click').on('click', function() {
      dataSort = $(this).data('sort');
      $('.mp-designer__sort-by .mp-designer__sort-by__icon').html('');
      $('.mp-designer__sort-by').removeClass('mp-designer__sort-by--active');
      $(this).addClass('mp-designer__sort-by--active');

      if(dataSort === 'date updated') {
        isDateSortAsc = !isDateSortAsc;

        if(isDateSortAsc) {
          $(this).find('.mp-designer__sort-by__icon').html(ascIcon);
        } else {
          $(this).find('.mp-designer__sort-by__icon').html(descIcon);
        }
      }

      if(dataSort === 'name') {
        isNameSortAsc = !isNameSortAsc;

        if(isNameSortAsc) {
          $(this).find('.mp-designer__sort-by__icon').html(ascIcon);
        }
        else {
          $(this).find('.mp-designer__sort-by__icon').html(descIcon);
        }
      }

      loadDesignsList(true);
    });
  }

  feSortData();

  return {
    generateDesignFilters,
    designerSkeletonLoaders
  }
}

function filterDesigns(designFilters) {
  if(!designFilters) return;

  const { fileType, view } = designFilters;

  const savedView = localStorage.getItem('designerAllDesigns');
  const savedFileType = localStorage.getItem('designFileType');

  localStorage.setItem('designerAllDesigns', view);
  localStorage.setItem('designFileType', fileType);

  if(designSearch) {
    loadDesignsSearch(fileType === savedFileType && savedView === view);
    return;
  }

  // check if the view selected and local storage is the same
  if(view === savedView) {
    loadDesignsList(true) // don't refetch data
    return;
  }

  loadDesignsList(false)
}

/**
 * remove this function after testing (this will be moved to cron job)
 */
async function updateMPIndex() {
  try {
    const response = await $.ajax({
      url: `/api/algolia/bulk-update-marketing-print?facilityId=${selectedID}`,
      method: 'POST'
    });

    console.log('Algolia index update response:', response);
  } catch (error) {
    console.error('Error updating Algolia index:', error);
  }
}