function sConfluenceUI(iframePath) {
    loadingOverlay('.iframe-placeholder');

    if (iframePath === 'about:blank') {
        iframePath = null;
    }
    $('#confluenceContainer').attr('src', iframePath || `/confluence/display/MF`);

    $('iframe#confluenceContainer').ready(function () {
        setTimeout(function () {
            $('.iframe-placeholder iframe').show();
            $('.iframe-placeholder').LoadingOverlay('hide');
        }, 2000);
    });
}

function sConfluenceURI() {
    const href = $('#confluenceContainer').contents().get(0).location.href;
    if (href && href !== 'about:blank') {
        sammy.quietRoute('/confluence-ui/#' + encodeURIComponent(href.replace(document.location.origin, '')));
    }

    $('.iframe-placeholder iframe').show();
    $('.iframe-placeholder').LoadingOverlay('hide');
}
