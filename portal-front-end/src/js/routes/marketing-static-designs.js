let staticDesignObject = {}

/**
 * Reset design object to its original state
 * 
 */
function resetDesignObject() {
    staticDesignObject = {
        id: null,
        name: null,
        tags: [],
        url: '',
        thumbnail: '',
        thumbnails_large: null,
        deleted: 0,

        // Metadata Elements
        brand: 'ALL',
        designVisibility: hasMPAdminAccess ? 'global' : 'region',
        club: 'global',
        usageGuidelines: '',
        designRegion: hasMPAdminAccess ? null : mpAccessibleRegions.map(r => r.iso_code),
        internalComments: [],
        approvalStatus: false,
        publishingAutomation: {
            autoPublish: moment().unix(),
            autoUnPublish: moment().add(10, 'years').unix(),
        },
        publishingAutomationPublishNow: true,
        publishingAutomationNeverExpire: true,
    }
}
 
/**
 * Create new static designs
 * 
 */
function newStaticDesignDialog(groupingTags=false) {
    resetDesignObject();
    const adminView = (localStorage.getItem('designerAllDesigns') || 'false')
        .toLowerCase() == 'true';

    if (!adminView) {
        staticDesignObject.club = selectedID;
        staticDesignObject.designVisibility = 'club';
        staticDesignObject.approvalStatus = true;
    }

    const setupHTML = `
        <div class="create-static-design-dialog ph-dialog-v2" style="padding: 20px;">
            <fieldset>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert alert-blue">
                            <small>
                                ${t('Please note that \'Metadata\' such as publishing status, guidelines, brand/visibility etc may be set AFTER the initial design has been uploaded and saved. Initial designs will be uploaded in a &quot;Unapproved&quot; state, and not visible to Clubs.')}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12">
                        <label for="asset_name">Name</label>
                        <div class="form-group">
                            <div class="form-line">
                                <input type="text" id="asset_name" class="form-control" placeholder="${t('Enter static File name')}">
                            </div>
                        </div>
                    </div>
                </div>
                
                <label for="asset_tags">${t('Folders')}</label>
                <div class="form-group design-tags__container">
                    <select class="select-tags" id="asset_tags"></select>
                </div>
                <div class="m-t-20">
                    <div class="dropzone-holder">
                        <div id="imageDZUploads" class="dropzone"></div>
                    </div>
                </div>
            </fieldset>
        </div>
    `

    let assetUpload = []
    swalWithBootstrapButtons.fire({
        width: 500,
        html: setupHTML,
        title: t('New File Upload'),
        confirmButtonText: t('Save'),
        cancelButtonText: t('Cancel'),
        animation: false,
        showCancelButton: true,
        allowEscapeKey : false,
        allowOutsideClick: false,
        showCloseButton: true,
        onBeforeOpen: () => {

            // Grouping Tags Select Multiple Autocomplete
            setGroupingTags(staticDesignObject.tags, 'assets');

            // If it's a new doc - pre-set the tags to whatever folder we are in...
            if(groupingTags) $('.doc-tags, .select-tags').val([groupingTags]).trigger('change')

            // Custom toggle focus
            $('#asset_name').focus(function(){
                $(this).parent().addClass('focused')
            }).blur(function() {
                $(this).parent().removeClass('focused')
            })

            function setNameFromDZ(file) {
                if (!$("#asset_name").val()) {
                    // Set name based on file & trim file extension
                    $("#asset_name").val(file.name.replace(/\.[^/.]+$/, ""))
                }
            }

            dzFileUpload('.jpeg,.jpg,.png,.pdf', swalWithBootstrapButtons, assetUpload, setNameFromDZ, function(response) {
                if(response) {
                    assetUpload = response
                }
            })
        },
        preConfirm: () => {

            //Render validations
           const isValid = validate();
           const isAssetUploaded = assetUpload.length !== 0
           $('[class^="upload-status"]').remove()

            if(!isAssetUploaded) {
                $('#imageDZUploads').addClass('error')
                $('.dropzone-holder').prepend(`
                    <small class="upload-status-error">${t('Please upload one file.')}</small>
                `)
            } else {
                $('#imageDZUploads').removeClass('error')
            }

            return (isAssetUploaded && isValid)
        },
        onClose: () => {

            // Just ignore any errors - we only 'save' of the user clicks save anyway...
            try {
                const { s3Id, variations } = assetUpload[0]

                let userInputs = {
                    name: $('#asset_name').val(),
                    tags: $('#asset_tags').val(),
                    id: s3Id,
                    url: variations.image?.[0] ?? variations.pdf?.[0] ?? variations?.other?.[0] ?? '',
                    thumbnail: variations.thumbnails?.[0] ?? '',
                    thumbnails_large: variations.thumbnails_large ?? '',
                }

                staticDesignObject = {...staticDesignObject, ...userInputs}

            } catch(err) {
                console.error(err);
            }

        }
    }).then(result => {
        if(result.value) {

            const saveUrl = `/api/marketing/designs/static/${selectedID}/save`

            saveDesignToDB(saveUrl, staticDesignObject, function(response) {
                const design = response

                if(design) {
                    resetDesignObject();

                    instantNotification(
                        // Message...
                        t('Static design saved successfully!'),
                        // Type...
                        'success',
                        // Timeout (1m)...
                        60000
                    );

                    swalWithBootstrapButtons.fire({
                        html: `
                            <h3>${t('Waiting for new design metadata')}...</h3>
                        `,
                        animation: false,
                        showCancelButton: false,
                        showCloseButton: false,
                        allowEscapeKey : false,
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        width: 500,
                    })

                    // Update the UI...
                    loadDesignsList(currentFilteredDesignTag, false, function() {
                                       
                        // Open the designs dialog with the new design & meta-data selection view to add metadata...
                        openDesignDialog(design.thumbnail, design.id)
                        $("#edit-metadata-tab").click()

                    });


                }
            })

            $.ajax({
              url: `/api/algolia/update-marketing-print?facilityId=${selectedID}`,
              method: 'POST',
              data: JSON.stringify({ objectID: staticDesignObject.id, data: staticDesignObject }),
              contentType: 'application/json'
            });
        }
    });
}


/**
 * Get s3 Object
 * 
 * @params file and format of the design to be downloaded
 * @method GET
 * @returns blob
 */
function downloadFileFromS3(file, url, format, callback) {

    $.ajax({
        // url: `/api/marketing/designs/static/${file}/download`,
        url: url,
        method: 'GET',
        data: { format },
        xhrFields:{
            responseType: 'blob'
        },
        success: function(response) {
            callback(response)
        },
        error: function(error) {

            console.log(error);
            instantNotification(
                // Message...
                '<b>'+t('Failed to export design. Please contact HQ with the design you are trying to export for details.'),
                // Type...
                'error',
                // Timeout (30sec)...
                30000
            );
        }
    })
}

function downloadStaticFile(designId, name, url, format) {
    // console.log(designId, name, url, format);

    const loader = renderProgressbar();
    downloadFileFromS3(designId, url, format, function(response) {

        if(response) {
            let a;

            // Trick for making downloadable link
            // Make this reusable
            a = document.createElement('a');
            const url = window.URL || window.webkitURL;
            a.href = url.createObjectURL(response);
            a.download = name.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.' + format
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();

            loader.close();
        }
    })

}
