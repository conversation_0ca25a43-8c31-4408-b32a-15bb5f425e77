function initKYCContactDetails() {
    let clubKYCData = {};
    let selectedContactID = null;
    let selectedContact = {};

    async function init() {
        selectedContactID = location.pathname.match(/^\/club-kyc\/contact-details\/(.+)$/)[1];
        renderBreadcrumbs();

        const status = await fetchData();
        if (!status) {
            return $('.form-container').html(
                `<div class="d-flex flex-column align-center justify-center" style="height: 100%;">
                    <i class="material-icons" style="font-size: 100px;">error</i>
                    <h3>${t('Failed to fetch KYC data!')}</h3>
                    <p>${t('Could not fetch data at this time. Please contact HQ for assistance.')}</p>
                </div>`
            );
        }

        renderFormInputs();
        renderFormIds();
        renderDeleteContactButton();
        $('.hidden-footer').addClass('active');
        $('.hidden-footer').on('click', '.save-btn', handleSubmit);
    }

    async function fetchData() {
        loadingOverlay('.form-container');
        const response = await getClubKycAPI(selectedID);
        loadingOverlay('.form-container', false);
        if (!response[0]) return false;
        clubKYCData = response[1];
        selectedContact = _get(clubKYCData, 'contactDetails', []).find((record) => record.id === selectedContactID);
        return true;
    }

    function renderBreadcrumbs() {
        breadcrumbsComponent('.breadcrumbs-container', [
            { label: 'KYC', url: '/club-kyc' },
            { label: t('Contact Details'), url: '/club-kyc' },
            { label: selectedContactID, active: true },
        ]);
    }

    function renderFormInputs() {
        const form = $('#kyc-contact-page form');
        const roleOptions = [
            { label: t('Owner'), value: 'owner' },
            { label: t('Manager'), value: 'manager' },
            { label: t('Staff Member'), value: 'staff' },
        ];
        form.html(`
            <div class="row mb-2 d-flex flex-row md:flex-column flex-wrap flex-gap-4">
                <div class="d-flex flex-column gap-2 flex-auto">
                    ${textField({
                        label: t('Name'),
                        name: 'name',
                        value: _get(selectedContact, 'name', ''),
                        required: true,
                    })}
                    ${selectpicker({
                        label: t('Role'),
                        name: 'role',
                        value: _get(selectedContact, 'role', ''),
                        options: roleOptions,
                    })}
                    ${textField({
                        label: t('Date of Birth'),
                        name: 'birthday',
                    })}
                </div>
                <div class="d-flex flex-column gap-2 flex-auto">
                    ${textField({
                        label: t('Phone'),
                        name: 'phone',
                        value: _get(selectedContact, 'phone', ''),
                        type: 'tel',
                    })}
                    ${textField({
                        label: t('Primary Email'),
                        name: 'email',
                        value: _get(selectedContact, 'email', ''),
                        info: ('Please use the email address used to log into performance hub if possible')
                    })}
                    ${textField({
                        label: t('Secondary Email(s)'),
                        name: 'alternativeEmails',
                        classNames: 'd-flex flex-column',
                    })}
                    <small>${t('Use a comma')} <code>,</code> ${t('to separate emails and/or add the email into the list.')}</small>
                </div>
            </div>
            <div class="d-flex flex-column gap-2 mb-2">
                <div class="kyc-ids-container"></div>
                ${textArea({ label: t('Address'), name: 'address', value: _get(selectedContact, 'address', '') })}
                ${textArea({ label: t('Other Details & Comments'), name: 'other', value: _get(selectedContact, 'other', '') })}
            </div>
            <hr class="ph-hr">
            <div class="row mb-2">
                <div class="col-md-6 d-flex flex-column gap-2">
                    <div class="form-check styled-checkbox">
                        <input type="checkbox" class="form-check-input filled-in chk-col-blue" name="useAsSaasBillingContact">
                        <label class="form-check-label" for="useAsSaasBillingContact">${t('Nominated Saas Billing Contact')}</label>
                        <p>
                            <small>
                                ${t("As the Nominated SaaS Billing Contact, this individual will automatically receive essential notifications and reminders related to the account's usage and billing details for the Performance Hub. They will also have the authority to modify payment settings and access the account's financial history, ensuring they can effectively manage financial responsibilities associated with the account.")}
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        `);

        const defaultBirthday = _get(selectedContact, 'birthday', '');

        form.find('input[name=birthday]').daterangepicker({
            timePicker: true,
            timePickerIncrement: 5,
            singleDatePicker: true,
            showDropdowns: true,
            startDate: defaultBirthday ? moment(defaultBirthday, 'YYYY-MM-DD') : moment(),
            parentEl: form.find('input[name=birthday]').parent(),
            locale: {
                format: 'MMMM DD, YYYY',
                applyLabel: t('Apply'),
                cancelLabel: t('Cancel'),
                customRangeLabel: t('Custom Range'),
            },
        });
        form.find('input[name=alternativeEmails]').val(JSON.stringify(_get(selectedContact, 'alternativeEmails', [])));
        form.find('input[name=alternativeEmails]').multiple_emails({
            position: 'top',
            theme: 'bootstrap',
            checkDupEmail: true,
        });
        new google.maps.places.Autocomplete(form.find('textarea[name=address]')[0], {
            componentRestrictions: { country: _get(selectedClubObj, 'localisation.country.iso_code') },
        });
        form.find('input[name=phone]').inputmask({ regex: '^\\+{0,1}([\\d]+\\s{0,1})*' });
        form.find('select[name=role]').selectpicker();
        form.find('select[name=role]')
            .closest('.dropdown')
            .find('.bs-searchbox input')
            .on('input', (e) => {
                const value = $(e.currentTarget).val();
                const filteredOptions = roleOptions.filter(
                    (option) =>
                        option.label.toLowerCase().includes(value.toLowerCase()) ||
                        option.value.toLowerCase().includes(value.toLowerCase())
                );
                const options = filteredOptions.length > 0 ? filteredOptions : [{ label: value, value }];
                form.find('select[name=role]').html(
                    options.map((option) => `<option value="${option.value}">${option.label}</option>`).join('')
                );
                form.find('select[name=role]').selectpicker('refresh');
            });
        form.find('input[name=useAsSaasBillingContact]').prop('checked', _get(selectedContact, 'useAsSaasBillingContact', false));
        form.find('input[name=useAsSaasBillingContact] + label').on('click', (e) => {
            const isChecked = $(e.currentTarget).prev().prop('checked');
            $(e.currentTarget).prev().prop('checked', !isChecked);
        });

        // Logic to handle role changes affecting the 'Nominated SaaS Billing Contact' checkbox - basically we want to have this automatically checked if the user adds the member as an Admin.
        // const isBillingContact = _get(selectedContact, 'useAsSaasBillingContact', false)
        form.find('select[name="role"]').change(function() {
            let role = $(this).val();
            let checkbox = $('input[name=useAsSaasBillingContact]');
            if (role === 'owner') {
                checkbox.prop('checked', true); // Automatically check if role is 'owner'
            } else {
                checkbox.prop('checked', false); // Uncheck if role is changed away from 'owner'
            }
        });

    }

    function renderFormIds() {
        $('.kyc-ids-container').html(`
            <label>${t('Uploaded IDs')}</label>
            <div class="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); grid-gap: 2rem;">
                ${_get(selectedContact, 'ids', [])
                    .map((record) => {
                        return `
                                <div data-id="${record.key}" class="card p-8 d-flex align-center gap-2 justify-between clickable preview-id" style="margin-bottom: 0px;">
                                    <div class="shorten-text">${record.originalname}</div>
                                    <i class="material-icons delete-id-btn">delete</i>
                                </div>
                            `;
                    })
                    .join('')}

                <div>
                    <button type="button" class="btn btn-square btn-primary btn-outline open-file-upload-btn">
                        <i class="material-icons">add</i>
                    </button>
                </div>
            </div>
        `);

        $('.form-container')
            .find('.open-file-upload-btn')
            .on('click', () =>
                openUploadFileDialog(
                    t('Upload ID'),
                    (records) => {
                        selectedContact = selectedContact || {};
                        const ids = _get(selectedContact, 'ids', []);
                        const newIds = [...ids, ...records];
                        selectedContact.ids = newIds;
                        renderFormIds();
                    },
                    { url: `/api/clubs/${selectedID}/kyc-assets` }
                )
            );
        $('.form-container').find('.delete-id-btn').on('click', handleDeleteId);

        $('.form-container')
            .find('.preview-id')
            .on('click', (e) => {
                const id = $(e.currentTarget).data('id');
                const record = _get(selectedContact, 'ids', []).find((record) => record.key === id);
                if (!record) return;
                viewClubKYCAsset(record);
            });
    }

    function handleDeleteId(e) {
        e.stopPropagation();
        const id = $(e.currentTarget).closest('.card').data('id');
        const ids = _get(selectedContact, 'ids', []);
        const index = ids.findIndex((record) => record.key === id);
        if (index < 0) return;
        confirmDeleteDialog(null, t('Are you sure you want to delete this ID?'), async () => {
            ids.splice(index, 1);
            selectedContact.ids = ids;
            renderFormIds();
        });
    }

    function validateForm(doc) {
        let valid = true;
        const form = $('#kyc-contact-page form');
        const contactDetails = _get(clubKYCData, 'contactDetails', []);

        if (!doc.name) {
            form.find('input[name=name]').closest('.form-group').find('em').html(t('Name is required'));
            valid = false;
        } else if (contactDetails.some((contact) => contact.name === doc.name && contact.id !== doc.id)) {
            form.find('input[name=name]').closest('.form-group').find('em').html(t('Name already exists'));
            valid = false;
        } else {
            form.find('input[name=name]').closest('.form-group').find('em').html('');
        }

        return valid;
    }

    async function handleSubmit() {
        const footer = $('#kyc-contact-page .hidden-footer');
        const form = $('.form-container form');

        const contactDetails = _get(clubKYCData, 'contactDetails', []);
        const doc = getSerializedObjectFromForm(form);
        const alternativeEmails = JSON.parse(_get(doc, 'alternativeEmails', '[]'));
        doc.id = selectedContactID === 'new' ? uuidv4() : selectedContactID;
        doc.alternativeEmails = alternativeEmails;
        doc.ids = _get(selectedContact, 'ids', []);
        doc.birthday = moment(doc.birthday, 'MMMM DD, YYYY').format('YYYY-MM-DD');
        doc.useAsSaasBillingContact = form.find('input[name=useAsSaasBillingContact]').prop('checked');
        const filteredContactDetails = contactDetails.filter((contact) => contact.id !== doc.id);
        const newContactDetails = [...filteredContactDetails, doc];

        const validate = validateForm(doc);
        if (!validate) return;

        footer.find('.save-btn,.cancel-btn').prop('disabled', true);
        loadingOverlay(form);
        const response = await updateClubKycAPI(selectedID, { ...clubKYCData, contactDetails: newContactDetails });
        footer.find('.save-btn,.cancel-btn').prop('disabled', false);
        loadingOverlay(form, false);

        if (!response[0]) {
            return instantNotification(
                t('There was an error while updating the contact details. Please try again later.'),
                'error'
            );
        }

        sammy.setLocation('/club-kyc');
        return instantNotification(t('Contact details updated successfully'), 'success');
    }

    function renderDeleteContactButton() {
        $('.delete-contact-btn').addClass('hidden');
        if (selectedContactID === 'new') return;
        $('.delete-contact-btn').removeClass('hidden');
        $('.delete-contact-btn').on('click', () => {
            confirmDeleteDialog(null, t('Are you sure you want to delete this contact?'), async () => {
                const contactDetails = _get(clubKYCData, 'contactDetails', []);
                const filteredContactDetails = contactDetails.filter((contact) => contact.id !== selectedContactID);

                loadingOverlay('.form-container');
                $('.delete-contact-btn').prop('disabled', 'disabled');
                $('.hidden-footer').removeClass('active');
                const response = await updateClubKycAPI(selectedID, {
                    ...clubKYCData,
                    contactDetails: filteredContactDetails,
                });
                loadingOverlay('.form-container', false);
                $('.delete-contact-btn').prop('disabled', false);

                if (!response[0]) {
                    return instantNotification(
                        t('There was an error while deleting the contact details. Please try again later.'),
                        'error'
                    );
                }
                sammy.setLocation('/club-kyc');
                return instantNotification('Contact details deleted successfully', 'success');
            });
        });
    }

    init();
}
