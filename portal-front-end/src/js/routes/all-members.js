function loadAllMembers() {
    $(document).ready( function () {
        refreshAllMemberData();
    });
}

function refreshAllMemberData() {
    loadingOverlay("#gm-members-table-container");
    loadingOverlay("#tc-app-members-table-container");
    loadingOverlay("#tc-leads-table-container");
    loadingOverlay("#trainAtHome-table-container");

        // ----------------------------------------------------------------
        // GYM MASTER MEMBER DATA
        // ----------------------------------------------------------------

            $('#gm-members-table').DataTable().clear();
            $('#gm-members-table').DataTable().destroy();

            $("#gm-members-table-container").empty();
            $("#gm-members-table-container").html(
                `<table width="100%" id="gm-members-table" class="table table-hover dataTable">
                    <thead>
                        <tr>
                            <th class="table-header">${t('Name')}</th>
                            <th class="table-header">${t('Email')}</th>
                            <th class="table-header">${t('Phone')}</th>
                            <th class="table-header">${t('Date Joined')}</th>
                            <th class="table-header">${t('Club')}</th>
                            <th class="table-header">${t('Address')}</th>
                            <th class="table-header">${t('Status')}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>`
            );

            // This table data is loaded server-side so the client does not hang with thousands of members...
            var gmMembersExclusiveStartKey = false, gmStatusFilter = "NONE";
            var gmMembers = $('#gm-members-table').DataTable({
                "responsive": true,
                "dom": '<"toolbar">frtip',
                //"order": [[ 3, "desc" ]],
                "ordering": false,
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/api/members-leads/gymmaster/all/members",
                    "data": function (d) {
                        if(gmMembersExclusiveStartKey) {
                            d.ExclusiveStartKey = gmMembersExclusiveStartKey;
                        }

                        // Show members based on selected filter...
                        if(gmStatusFilter != "NONE") {
                            d.status = gmStatusFilter
                        }

                    }
                },
                "columns": [
                    { "data": "fullname" },
                    { "data": "email" },
                    { "data": "phone" },
                    { "data": "joindate",
                        render: function (data, type, row) {
                            if(data == "") {
                                return "";
                            } else {
                                var memberDate = new Date(data).toLocaleDateString("en-AU", { weekday: "short", year: "numeric", month: "short", day: "numeric" })
                                return memberDate;
                            }
                        }
                    },
                    { "data": "clubID" },
                    { "data": "address" },
                    { "data": "status",
                        render: function (data, type, row) {
                            console.log(data, type, row);

                            var payingTag = ""
                            if(row.isPaying) payingTag = ` <span class="badge badge-green ">${t('IS PAYING')}</span>`

                            return `${data}${payingTag}`;
                        }
                    },
                ]

            });

            // DynamoDB Uses a "start key" which we need to store for pagination of these results...
            gmMembers.on( 'xhr', function () {
                var json = gmMembers.ajax.json();
                gmMembersExclusiveStartKey = (json.ExclusiveStartKey === undefined) ? false : json.ExclusiveStartKey;

                if(json.queryType == "search") {
                    $("#gm-members-table-container .paging_simple_numbers").hide();
                    $(".filter-by-options select").prop( "disabled", true);
                } else {
                    $("#gm-members-table-container .paging_simple_numbers").show();
                    $(".filter-by-options select").prop( "disabled", false);
                }

                $('#gm-members-table-container').LoadingOverlay("hide");
            });

            // Add download/export functions & FIlter Dropdown...
            $("#gm-members-table-container div.toolbar").html(`
                <div class="export-data">
                    <h5>${t('Export data:')}</h5>
                    <a class="btn btn-sm btn-default" href="/api/members-leads/gymmaster/all/members?status=Current&orpaying=true&export=true&trainingcamp=true" target="_blank">
                        <span><i class="fa fa-lg fa-envelope-o"></i> <span class="btn-text">${t('MC CSV')} <span class="badge badge-tc">${t('Training Camp')}</span></span></span>
                    </a>
                    <a class="btn btn-sm btn-default" href="/api/members-leads/gymmaster/all/members?notstatus=Expired&export=true&trainingcamp=true" target="_blank">
                        <span><i class="fa fa-lg fa-envelope-o"></i> <span class="btn-text">${t('MC CSV')} <span class="badge badge-not-expired">${t('All Not Expired')}</span></span></span>
                    </a>
                    <a class="btn btn-sm btn-default" href="/api/members-leads/gymmaster/all/members?export=true" target="_blank">
                        <span><i class="fa fa-lg fa-database"></i> <span class="btn-text">${t('Export Entire DB')}</span></span>
                    </a>
                </div>
                <div class="filter-by-options">
                    <h5>${t('Filter Members By:')}</h5>
                    <select>
                      <option value="NONE">${t('- SHOW ALL -')}</option>
                      <option value="Current">${t('Current')}</option>
                      <option value="Expired">${t('Expired')}</option>
                      <option value="Hold">${t('Hold')}</option>
                      <option value="Concession Pack">${t('Concession Pack')}</option>
                      <option value="Recently Expired">${t('Recently Expired')}</option>
                    </select>
                </div>
            `);
            $("#gm-members-table-container .filter-by-options select").change(function () {
                var selectedFilter = $(this).children("option:selected").val();
                gmStatusFilter = selectedFilter;
                gmMembers.ajax.reload();
            });


        // ----------------------------------------------------------------
        // WEBSITE LEADS
        // ----------------------------------------------------------------

            $('#ws-leads-table').DataTable().clear();
            $('#ws-leads-table').DataTable().destroy();

            $("#website-leads-table-container").empty();
            $("#website-leads-table-container").html(
                `<table width="100%" id="ws-leads-table" class="table table-hover dataTable">
                    <thead>
                        <tr>
                            <th>${t('Name')}</th>
                            <th>${t('Email')}</th>
                            <th>${t('Phone')}</th>
                            <th>${t('Date Of Lead')}</th>
                            <th>${t('Club')}</th>
                            <th>${t('Campaign Name')}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>`
            );

            // This table data is loaded server-side so the client does not hang with thousands of members...
            var wsLeadsExclusiveStartKey = false;
            var wsLeads = $('#ws-leads-table').DataTable({
                "responsive": true,
                "dom": '<"toolbar">frtip',
                "ordering": false,
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/api/members-leads/website/all/leads",
                    "data": function (d) {
                        if(wsLeadsExclusiveStartKey) {
                            d.ExclusiveStartKey = wsLeadsExclusiveStartKey;
                        }
                    }
                },
                "columns": [
                    { "data": "fullname" },
                    { "data": "email" },
                    { "data": "phone" },
                    { "data": "leaddate",
                        render: function (data, type, row) {
                            var memberDate = new Date(data).toLocaleDateString("en-AU", { weekday: "short", year: "numeric", month: "short", day: "numeric" })
                            return memberDate;
                        }
                    },
                    { "data": "clubID" },
                    { "data": "campaign" }
                ]

            });

            // DynamoDB Uses a "start key" which we need to store for pagination of these results...
            wsLeads.on( 'xhr', function () {
                var json = wsLeads.ajax.json();
                wsLeadsExclusiveStartKey = (json.ExclusiveStartKey === undefined) ? false : json.ExclusiveStartKey;
                $('#website-leads-table-container').LoadingOverlay("hide");

                if(json.queryType == "search") {
                    $("#website-leads-table-container .paging_simple_numbers").hide();
                } else {
                    $("#website-leads-table-container .paging_simple_numbers").show();
                }

            });

            // Add download/export functions...
            $("#website-leads-table-container div.toolbar").html(`
                <h5>${t('Export data:')}</h5>
                <a class="btn btn-sm btn-default" href="/api/members-leads/website/all/leads?export=true" target="_blank">
                    <span><i class="fa fa-lg fa-database"></i> <span class="btn-text">${t('Export Table as CSV')}</span></span>
                </a>
            `);



        // ----------------------------------------------------------------
        // TRAINING CAMP APP
        // ----------------------------------------------------------------

            $('#tc-members-table').DataTable().clear();
            $('#tc-members-table').DataTable().destroy();

            $("#tc-app-members-table-container").empty();
            $("#tc-app-members-table-container").html(
                `<table width="100%" id="tc-members-table" class="table table-hover dataTable">
                    <thead>
                        <tr>
                            <th>${t('Name')}</th>
                            <th>${t('Email')}</th>
                            <th>${t('Date Signed Up')}</th>
                            <th>${t('Club')}</th>
                            <th>${t('Verified')}</th>
                            <th>${t('No# Camps')}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>`
            );

            // This table data is loaded server-side so the client does not hang with thousands of members...
            var tcMembersExclusiveStartKey = false;
            var tcMembers = $('#tc-members-table').DataTable({
                "responsive": true,
                "dom": '<"toolbar">frtip',
                //"order": [[ 3, "desc" ]],
                "ordering": false,
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/api/members-leads/trainingcamp/all/app-members",
                    "data": function (d) {
                        if(tcMembersExclusiveStartKey) {
                            d.ExclusiveStartKey = tcMembersExclusiveStartKey;
                        }
                    }
                },
                "columns": [
                    { "data": "fullname" },
                    { "data": "email" },
                    { "data": "joindate",
                        render: function (data, type, row) {
                            var memberDate = new Date(data).toLocaleDateString("en-AU", { weekday: "short", year: "numeric", month: "short", day: "numeric" })
                            return memberDate;
                        }
                    },
                    { "data": "clubID" },
                    { "data": "verified" },
                    { "data": "season" }
                ]

            });

            // DynamoDB Uses a "start key" which we need to store for pagination of these results...
            tcMembers.on( 'xhr', function () {
                var json = tcMembers.ajax.json();
                tcMembersExclusiveStartKey = (json.ExclusiveStartKey === undefined) ? false : json.ExclusiveStartKey;
                $('#tc-app-members-table-container').LoadingOverlay("hide");

                if(json.queryType == "search") {
                    $("#tc-app-members-table-container .paging_simple_numbers").hide();
                } else {
                    $("#tc-app-members-table-container .paging_simple_numbers").show();
                }

            });

            // Add download/export functions...
            $("#tc-app-members-table-container div.toolbar").html(`
                <h5>${t('Export data:')}</h5>
                <a class="btn btn-sm btn-default" href="/api/members-leads/trainingcamp/all/app-members?export=true&trainingcamp=true" target="_blank">
                    <span><i class="fa fa-lg fa-envelope-o"></i> <span class="btn-text">${t('MC Import CSV')}</span></span>
                </a>
                <a class="btn btn-sm btn-default" href="/api/members-leads/trainingcamp/all/app-members?export=true" target="_blank">
                    <span><i class="fa fa-lg fa-database"></i> <span class="btn-text">${t('Export Table as CSV')}</span></span>
                </a>
            `);




        // ----------------------------------------------------------------
        // TRAINING CAMP LEADS
        // ----------------------------------------------------------------

            $('#tc-leads-table').DataTable().clear();
            $('#tc-leads-table').DataTable().destroy();

            $("#tc-leads-table-container").empty();
            $("#tc-leads-table-container").html(
                `<table width="100%" id="tc-leads-table" class="table table-hover dataTable">
                    <thead>
                        <tr>
                            <th>${t('Name')}</th>
                            <th>${t('Email')}</th>
                            <th>${t('Phone')}</th>
                            <th>${t('Date Registered')}</th>
                            <th>${t('Club')}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>`
            );

            // This table data is loaded server-side so the client does not hang with thousands of members...
            var tcLeadsExclusiveStartKey = false;
            var tcLeads = $('#tc-leads-table').DataTable({
                "responsive": true,
                "dom": '<"toolbar">frtip',
                //"order": [[ 3, "desc" ]],
                "ordering": false,
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/api/members-leads/trainingcamp/all/leads",
                    "data": function (d) {
                        if(tcLeadsExclusiveStartKey) {
                            d.ExclusiveStartKey = tcLeadsExclusiveStartKey;
                        }
                    }
                },
                "columns": [
                    { "data": "fullname" },
                    { "data": "email" },
                    { "data": "phone" },
                    { "data": "leaddate",
                        render: function (data, type, row) {
                            var memberDate = new Date(data).toLocaleDateString("en-AU", { weekday: "short", year: "numeric", month: "short", day: "numeric" })
                            return memberDate;
                        }
                    },
                    { "data": "clubID" }
                ]

            });

            // DynamoDB Uses a "start key" which we need to store for pagination of these results...
            tcLeads.on( 'xhr', function () {
                var json = tcLeads.ajax.json();
                tcLeadsExclusiveStartKey = (json.ExclusiveStartKey === undefined) ? false : json.ExclusiveStartKey;
                $('#tc-leads-table-container').LoadingOverlay("hide");

                if(json.queryType == "search") {
                    $("#tc-leads-table-container .paging_simple_numbers").hide();
                } else {
                    $("#tc-leads-table-container .paging_simple_numbers").show();
                }

            });

            // Add download/export functions...
            $("#tc-leads-table-container div.toolbar").html(`
                <h5>${t('Export data:')}</h5>
                <a class="btn btn-sm btn-default" href="/api/members-leads/trainingcamp/all/leads?export=true&trainingcamp=true" target="_blank">
                    <span><i class="fa fa-lg fa-envelope-o"></i> <span class="btn-text">${t('MC Import CSV')}</span></span>
                </a>
                <a class="btn btn-sm btn-default" href="/api/members-leads/trainingcamp/all/leads?export=true" target="_blank">
                    <span><i class="fa fa-lg fa-database"></i> <span class="btn-text">${t('Export Table as CSV')}</span></span>
                </a>
            `);


        // ----------------------------------------------------------------
        // TRAIN AT HOME
        // ----------------------------------------------------------------

            $('#trainAtHome-table').DataTable().clear();
            $('#trainAtHome-table').DataTable().destroy();

            $("#trainAtHome-table-container").empty();
            $("#trainAtHome-table-container").html(
                `<table width="100%" id="trainAtHome-table" class="table table-hover dataTable">
                    <thead>
                        <tr>
                            <th>${t('Name')}</th>
                            <th>${t('Email')}</th>
                            <th>${t('Phone')}</th>
                            <th>${t('Date Registered')}</th>
                            <th>${t('Club')}</th>
                            <th>${t('Verified')}</th>
                            <th>${t('Suburb')}</th>
                            <th>${t('Country')}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>`
            );

            // This table data is loaded server-side so the client does not hang with thousands of members...
            var trainAtHomeExclusiveStartKey = false;
            var trainAtHome = $('#trainAtHome-table').DataTable({
                "responsive": true,
                "dom": '<"toolbar">frtip',
                //"order": [[ 3, "desc" ]],
                "ordering": false,
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/api/members-leads/train-at-home/all/users",
                    "data": function (d) {
                        if(trainAtHomeExclusiveStartKey) {
                            d.ExclusiveStartKey = trainAtHomeExclusiveStartKey;
                        }
                    }
                },
                "columns": [
                    { "data": "fullname" },
                    { "data": "email" },
                    { "data": "phone" },
                    { "data": "dateCreated",
                        render: function (data, type, row) {
                            var memberDate = new Date(data).toLocaleDateString("en-AU", { weekday: "short", year: "numeric", month: "short", day: "numeric" })
                            return memberDate;
                        }
                    },
                    { "data": "clubID" },
                    { "data": "verified" },
                    { "data": "suburb" },
                    { "data": "countryCode" }
                ]

            });

            // DynamoDB Uses a "start key" which we need to store for pagination of these results...
            trainAtHome.on( 'xhr', function () {
                var json = trainAtHome.ajax.json();
                trainAtHomeExclusiveStartKey = (json.ExclusiveStartKey === undefined) ? false : json.ExclusiveStartKey;
                $('#trainAtHome-table-container').LoadingOverlay("hide");

                if(json.queryType == "search") {
                    $("#trainAtHome-table-container .paging_simple_numbers").hide();
                } else {
                    $("#trainAtHome-table-container .paging_simple_numbers").show();
                }

            });

            // Add download/export functions...
            $("#trainAtHome-table-container div.toolbar").html(`
                <h5>${t('Export data:')}</h5>
                <a class="btn btn-sm btn-default" href="/api/members-leads/train-at-home/all/users?export=true" target="_blank">
                    <span><i class="fa fa-lg fa-database"></i> <span class="btn-text">${t('Export Table as CSV')}</span></span>
                </a>
            `);



}