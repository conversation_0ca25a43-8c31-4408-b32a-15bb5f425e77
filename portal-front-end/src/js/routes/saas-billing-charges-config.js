let saasBillingBaseChargesTable = null;
function sSaasBillingChargesConfig() {
    
    const filtersRightElement = $("#charges-config").find('.charges-configuration-filters-right-container');

    let configBlocks = [];
    let billingAccessibleRegions = [];

    const qsObject = getQueryStringsObject();
    const filters = {
        search: qsObject.search || '',
        status: qsObject.status || 'active',
        frequency: qsObject.frequency || 'all',
        minDateEffective: qsObject.minDateEffective || moment().startOf('day').format('YYYY-MM-DD'),
        maxDateEffective: qsObject.maxDateEffective || moment().endOf('year').format('YYYY-MM-DD'),
    };

    const chargesObj = {
        pricePerGBStored: 'Price per GB Stored',
        pricePerCoachingScreen: 'Price per Coaching Screen',
        pricePerDuressButton: 'Price per Duress Button',
        performanceHubEASoftware: 'Performance Hub EA Software',
        pricePerCamera: 'Price per Camera',
    };

    async function init() {
        renderSearchNav();
        renderPageFilters();
        loadAdditionalFees();
        loadBlockSkeletons();

        const [configBlocksRes, access] = await Promise.all([
            listSaasBillingConfigBlocksAPI(),
            getMyOrgAccessForModuleAPI('billing-admin'),
        ]);

        if (!configBlocksRes[0] || !access[0]) return instantNotification(configBlocksRes[1], 'error');
        $('.searchbar-header').removeClass('isLoading');

        configBlocks = configBlocksRes[1];
        billingAccessibleRegions =  access[1].allRegions;

        loadConfigBlocks();
    }

    function renderPageFilters() {
        new PageFilters(
            '#charges-config .filters-container',
            [
                {
                    id: 'status',
                    label: 'Status',
                    options: [
                        {
                            label: 'Active',
                            value: 'active',
                            selected: filters.status === 'active',
                        },
                        {
                            label: 'Archived',
                            value: 'archived',
                            selected: filters.status === 'archived',
                        },
                    ],
                },
                {
                    id: 'frequency',
                    label: 'Frequency',
                    options: [
                        {
                            label: 'All',
                            value: 'all',
                            selected: filters.frequency === 'all',
                        },
                        {
                            label: 'Monthly',
                            value: 'monthly',
                            selected: filters.frequency === 'monthly',
                        },
                        {
                            label: 'One Time',
                            value: 'once',
                            selected: filters.frequency === 'once',
                        },
                    ],
                },
            ],
            (newFilters) => {
                Object.entries(newFilters).forEach(([key, value]) => {
                    filters[key] = value;
                });
                loadAdditionalFees();
            }
        );

        new PageFilters(
            filtersRightElement,
            [
                {
                    id: 'results-shown-charges',
                    label: 'Results Per Page',
                    options: [
                        { label: '10', value: '10', selected: false},
                        { label: '25', value: '25', selected: false},
                        { label: '50', value: '50', selected: false},
                        { label: '100', value: '100', selected: true},
                        { label: '500', value: '500', selected: false},
                        { label: '1000', value: '1000', selected: false},
                    ],
                }
            ],
            (newFilters) => {
                saasBillingBaseChargesTable.page.len(parseInt(newFilters['results-shown-charges'])).draw();
            }
        );

        datePicker(
            $('.date-effective-filter'),
            {
                startDate: moment(filters.minDateEffective, 'YYYY-MM-DD'),
                endDate: moment(filters.maxDateEffective, 'YYYY-MM-DD'),
                ranges: {
                    'From Today': [moment(), moment().endOf('year')],
                    'Last Month': [
                        moment().subtract(1, 'month').startOf('M'),
                        moment().subtract(1, 'month').endOf('M'),
                    ],
                    'Last 3 Months': [moment().subtract(3, 'month').startOf('M'), moment().endOf('M')],
                    'Last 6 Months': [moment().subtract(6, 'month').startOf('M'), moment().endOf('M')],
                    'Last Year': [moment().subtract(1, 'year').startOf('M'), moment().endOf('M')],
                },
            },
            (start, end) => {
                filters.minDateEffective = start.format('YYYY-MM-DD');
                filters.maxDateEffective = end.format('YYYY-MM-DD');
                updateHrefQuery({
                    minDateEffective: filters.minDateEffective,
                    maxDateEffective: filters.maxDateEffective,
                });
                updateDateEffectiveFilterText();
                loadAdditionalFees();
            }
        );

        updateDateEffectiveFilterText();
    }

    function updateDateEffectiveFilterText() {
        const { minDateEffective, maxDateEffective } = filters;
        const min = moment(minDateEffective, 'YYYY-MM-DD').format('YYYY/MM/DD');
        const max = moment(maxDateEffective, 'YYYY-MM-DD').format('YYYY/MM/DD');
        $('.date-effective-filter').html(`${min} - ${max}`);
    }

    function renderSearchNav() {
        searchbarHeader(
            '.search-nav-container',
            [
                { label: 'New Per Product Configuration Block', icon: 'fa-plus', onClick: () => configBlockDialog() },
                { label: 'New Base Charge', icon: 'fa-plus', onClick: () => additionalConfigBlockDialog() },
            ],
            { isLoading: true }
        );

        if (filters.search) $('.search-bar').val(filters.search);

        let searchTimeout = null;
        $('.search-bar').on('input', (e) => {
            filters.search = e.target.value;
            updateHrefQuery({ search: filters.search });
            loadConfigBlocks();

            searchTimeout && clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                loadAdditionalFees();
            }, 500);
        });
    }

    function loadBlockSkeletons() {
        const configTemplate = {
            charges: {
                pricePerGBStored: 0,
                pricePerCoachingScreen: 0,
                pricePerDuressButton: 0,
                performanceHubEASoftware: 0,
                pricePerCamera: 0,
            },
        };

        $('.saas-blocks-container').html('');
        [0, 0, 0].forEach(() => $('.saas-blocks-container').append(renderConfigBlock(configTemplate, false, true)));
    }

    const loadConfigBlocks = () => {
        $('.saas-blocks-container').html('');

        const searchRegEx = new RegExp(filters.search, 'igm');
        const filteredConfigBlocks = configBlocks.filter((block) => {
            return searchRegEx.test(block.target);
        });

        filteredConfigBlocks.forEach((block) => $('.saas-blocks-container').append(renderConfigBlock(block)));

        if (!filteredConfigBlocks.length) {
            $('.saas-blocks-container').append('<div class="text-center">No results found</div>');
        }
    };

    function loadAdditionalFees() {
        const tableKeys = [null];

        if (saasBillingBaseChargesTable) {
            saasBillingBaseChargesTable.clear();
            saasBillingBaseChargesTable.destroy();
        }

        const rowsPerPage = 100;
        saasBillingBaseChargesTable = $('#additional-charges-table').DataTable({
            responsive: true,
            dom: 'rtip',
            iDisplayLength: rowsPerPage,
            lengthMenu: [ [10, 25, 50, 100, 500, 1000], [10, 25, 50, 100, 500, 1000] ], // Dropdown options for number of entries to show
            pagingType: 'simple',
            ajax: {
                url: `/api/saas-billing/additional-line-items`,
                type: 'GET',
                data: function (d) {
                    const page = d.start / rowsPerPage;
                    const keyToUse = tableKeys[page];
                    if (keyToUse) d.lastKey = JSON.stringify({ id: keyToUse });
                    d.filters = filters;
                },
                dataSrc: function (json) {
                    if (!tableKeys.includes(json.lastKey?.id)) tableKeys.push(json.lastKey?.id);
                    return json.data;
                },
            },
            processing: true,
            serverSide: true,
            order: [[1, 'desc']],
            autoWidth: false,
            columns: [
                {
                    orderable: false,
                    render: () => `<i class="fa fa-chevron-right expand-icon"></i>`,
                },
                { data: 'dateEffective', render: (date) => moment.unix(date).tz('UTC').format('DD MMM YYYY') },
                { orderable: false, data: 'recurring', render: (recurring) => (recurring ? 'Monthly' : 'One Time') },
                { orderable: false, data: 'restrictedTo' },
                { orderable: false, data: 'description', render: (description) => description || '' },
                {
                    orderable: false,
                    data: 'items',
                    render: (items) => formatDiscount(items.reduce((acc, item) => acc + item.amount, 0)),
                },
                {
                    orderable: false,
                    data: 'archived',
                    render: (archived) => {
                        const deleteIcon = archived === 'true' ? 'fa-trash-restore' : 'fa-trash';
                        return `
                            <div class="d-flex flex-gap-2 justify-end">
                                <a class="btn btn-link action-btn ph-btn ph-btn-md btn-outline ph-btn-accent-hover" data-type="edit"><i class="fa fa-edit mr-0"></i></a>
                                <a class="btn btn-link action-btn ph-btn ph-btn-md btn-outline ph-btn-accent-hover" data-type="add-note"><i class="fa fa-comment mr-0"></i></a>
                                <a class="btn btn-link action-btn ph-btn ph-btn-md btn-outline ph-btn-accent-hover" data-type="view-log"><i class="fa fa-clock-rotate-left mr-0"></i></a>
                                <a class="btn btn-link action-btn ph-btn ph-btn-md btn-outline ${archived === 'true' ? 'ph-btn-accent-hover btn-warning' : 'btn-danger'}" data-type="archive"><i class="fa ${deleteIcon} mr-0"></i></a>
                            </div>
                        `;
                    },
                },
            ],
            language: {
                info: "Filtered from _TOTAL_ total records in database",
                zeroRecords: "No records found based on the current configured filters",
                infoFiltered: "(filtered from _MAX_ total entries)"
            }
        });

        saasBillingBaseChargesTable.draw();
        saasBillingBaseChargesTable.off('click', '.action-btn');
        saasBillingBaseChargesTable.on('click', '.action-btn', function (e) {
            e.preventDefault();
            e.stopPropagation();
            const { type } = $(e.currentTarget).data();
            const data = saasBillingBaseChargesTable.row($(e.currentTarget).parents('tr')).data();

            if (type === 'edit') return additionalConfigBlockDialog(data);
            if (type === 'add-note') return addNoteDialog(data);
            if (type === 'view-log') return additionalBlockLogsDialog(data);
            if (type === 'archive') return deleteAdditionalConfigBlock(data);
        });

        saasBillingBaseChargesTable.off('click', 'tr.odd,tr.even');
        saasBillingBaseChargesTable.on('click', 'tr.odd,tr.even', function (e) {
            const icon = $(e.currentTarget).find('i.expand-icon');
            const row = saasBillingBaseChargesTable.row(e.currentTarget);

            if (row.child.isShown()) {
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                return row.child.hide();
            }

            icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            row.child(additionalTableItemInfo(row.data())).show();
        });
    }

    function additionalTableItemInfo(data) {
        const div = $(`
            <tr>
                <td colspan="100%">
                    <div class="row">
                        <div class="col-sm-6">
                            <legend class="mb-0" style="font-size: 14px;">Items</legend>
                            <ul class="list-group list-group-flush w-full mb-0 charges"></ul>
                        </div>
                        <div class="col-sm-6">
                            <legend class="mb-0" style="font-size: 14px;">Notes</legend>
                            <ul class="list-group list-group-flush notes"></ul>
                        </div>
                    </div>
                </td>
            </td>
        `);

        data.items.forEach((item) => {
            div.find('ul.charges').append(`
                <li class="list-group-item d-flex gap-1 justify-between" style="background-color: inherit; padding: 8px 0px;">
                    <span>${item.name}</span>
                    <span>${formatDiscount(item.amount)}</span>
                </li>
            `);
        });

        data.notes?.forEach((note) => {
            div.find('.notes').append(`
                <li class="list-group-item">
                    <div>${note.content}</div>
                    <div class="d-flex gap-1 justify-end">
                        <span><small>${note.createdBy}</small></span>
                        <span><small>${moment.unix(note.createdAt).format('DD MMM YYYY hh:mm a')}</small></span>
                    </div>
                </li>
            `);
        });

        return div;
    }

    function formatDiscount(amount) {
        const absAmt = Math.abs(amount).formatCurrency(2);
        const isDiscount = amount < 0;
        const formatted = isDiscount ? `-$${absAmt}` : `+$${absAmt}`;
        return `<span class="${isDiscount ? 'text-danger' : 'text-success'}">${formatted}</span>`;
    }

    const renderConfigBlock = (block, isLog, isLoading) => {
        const cardClx = isLog ? '' : 'card';
        const bodyClx = isLog ? '' : 'body';
        const isLoadingClx = isLoading ? 'isLoading' : '';
        const deleteDisabled = block.target === 'global' ? 'disabled' : '';

        const actionButtons = isLog
            ? ''
            : `
                <div class="action-buttons">
                    <a class="btn-edit-config-block action-btn">
                        <i class="fa fa-edit"></i>
                    </a>
                    <a class="btn-delete-config-block action-btn ${deleteDisabled}">
                        <i class="fa fa-trash"></i>
                    </a>
                    <a class="btn-view-logs action-btn">
                        <i class="fa fa-list"></i>
                    </a>
                </div>
            `;

        const renderFeeElement = ([name, value]) => {
            return `
                <div class="block-fee d-flex justify-between gap-1">
                    <span class="use-skeleton">${friendlyNameMapping(name)}</span>
                    <span class="use-skeleton">$${value.formatCurrency(2)}</span>
                </div>
            `;
        };

        const div = $(`
            <div class="${cardClx} ${isLoadingClx} config-block">
                <div class="${bodyClx}">
                    <h4 class="use-skeleton" style="margin: 0; margin-bottom: 1rem;">${block.target}</h4>
                    ${actionButtons}
                    <div class="d-flex flex-column" style="gap: 2px;">
                        ${Object.keys(block.charges)
                            .map((key) => renderFeeElement([key, block.charges[key]]))
                            .join('')}
                    </div>
                </div>
            </div>
        `);

        $(div)
            .find('.btn-edit-config-block')
            .on('click', () => configBlockDialog(block));

        $(div)
            .find('.btn-view-logs')
            .on('click', () => blockLogsDialog(block));

        $(div)
            .find('.btn-delete-config-block')
            .on('click', async () => deleteConfigBlock(block));

        return div;
    };

    function deleteConfigBlock(block) {
        confirmDeleteDialog(null, null, async () => {
            const deleteResponse = await deleteSaasBillingConfigBlockAPI(block.target);
            if (!deleteResponse[0]) return instantNotification(deleteResponse[1], 'error');
            instantNotification(t('Per Product Configuration Block deleted successfully'));
            configBlocks = configBlocks.filter((b) => b.target !== block.target);
            loadConfigBlocks();
        });
    }

    function deleteAdditionalConfigBlock(block) {
        const archived = block.archived === 'true';
        const title = archived ? t('Restore Base Charge') : t('Archive Base Charge');
        const message = archived
            ? t('Are you sure you want to restore this Base Charge?')
            : t('Are you sure you want to archive this Base Charge?');

        confirmDeleteDialog(title, message, async () => {
            const deleteResponse = await updateSaasBillingAdditionalItemAPI(block.id, {
                archived: archived ? 'false' : 'true',
            });
            if (!deleteResponse[0]) return instantNotification(deleteResponse[1], 'error');
            instantNotification(t('Config updated successfully'));
            loadAdditionalFees();
        });
    }

    function friendlyNameMapping(key) {
        return chargesObj[key] || key.capitalize();
    }

    function configBlockDialog(block) {
        const html = document.createElement('div');
        $(html).addClass('config-block-dialog ph-dialog-v2');

        const selectedTargets = configBlocks.map((b) => b.target);
        const regionOptions = billingAccessibleRegions
            .filter((country) => block || !selectedTargets.includes(country.iso_code))
            .map((country) => {
                const selected = country.iso_code === block?.target ? 'selected' : '';
                return `<option value="${country.iso_code}" ${selected}>${country.full_name}</option>`;
            })
            .join('');

        const clubOptions = ClubsObj.filter((club) => block || !selectedTargets.includes(club.id))
            .map((club) => {
                const selected = club.id === block?.target ? 'selected' : '';
                return `<option value="${club.id}" ${selected}>${club.name}</option>`;
            })
            .join('');

        $(html).html(`
            <div class="body">
                <div class="alert alert-blue"><i class="fa fa-bell" style="color: #2262b3;" aria-hidden="true"></i>
                    All charges are in $USD, calculated daily and billed monthly
                </div>
                <form id="config-block-form">
                    <div class="d-flex flex-column gap-1">
                        <div class="d-flex gap-1">
                            ${selectpicker({
                                label: 'Type',
                                name: 'type',
                                options: [
                                    { label: 'Global', value: 'global' },
                                    { label: 'Region', value: 'country' },
                                    { label: 'Club', value: 'club' },
                                ].filter(({ value }) => block || !selectedTargets.includes(value)),
                                value: block?.type ?? 'global',
                            })}
                            ${selectpicker({ label: 'Target', name: 'target', options: [] })}
                        </div>
                    </div>
                </form>
            </div>
        `);

        const globalObj = configBlocks.find((b) => b.target === 'global');
        Object.keys(chargesObj).forEach((key) => {
            const value = block ? block.charges?.[key] ?? 0 : globalObj?.charges?.[key] ?? 0;

            $(html)
                .find('form > div')
                .append(textField({ label: friendlyNameMapping(key), name: key, value, type: 'number' }));
        });

        $(html).find('select').selectpicker();
        handleTypeChange();

        if (block) {
            $(html).find('select[name=type]').prop('disabled', true);
            $(html).find('select[name=target]').prop('disabled', true);
        } else {
            $(html).find('select[name=type]').on('change', handleTypeChange);
        }

        $(html).find('select').selectpicker('refresh');

        function handleTypeChange() {
            const type = $(html).find('select[name=type]').val();
            const targetEl = $(html).find('select[name=target]');

            if (type === 'global') {
                targetEl.html('').attr('disabled', true);
                $(html).find('select[name=target]').selectpicker('refresh');
            } else if (type === 'country') {
                targetEl.html(regionOptions).attr('disabled', false);
                $(html).find('select[name=target]').selectpicker('refresh');
            } else {
                targetEl.html(clubOptions).attr('disabled', false);
                $(html).find('select[name=target]').selectpicker('refresh');
            }
        }

        async function onSubmit() {
            const newCharges = Object.keys(chargesObj).reduce((acc, key) => {
                const value = $(html).find(`input[name=${key}]`).val();
                return { ...acc, [key]: +value };
            }, {});

            const target = block?.target ?? $(html).find('select[name=target]').val() ?? 'global';
            const createResponse = await updateSaasBillingConfigBlockAPI(target, newCharges);

            if (!createResponse[0]) {
                instantNotification(createResponse[1], 'error');
                return false;
            }

            instantNotification('Config block updated successfully');

            if (block) {
                const index = configBlocks.findIndex((b) => b.target === block.target);
                configBlocks[index] = createResponse[1];
            } else {
                configBlocks = [...configBlocks, createResponse[1]];
            }

            loadConfigBlocks();
            return false;
        }

        swalWithBootstrapButtons.fire({
            title: `${block ? 'Edit' : 'New'} Per Product Configuration Block`,
            html,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: true,
            confirmButtonText: 'Save/Update',
            preConfirm: onSubmit,
        });
    }

    function additionalConfigBlockDialog(block) {
        const html = document.createElement('div');
        $(html).addClass('additional-charge-dialog config-block-dialog ph-dialog-v2');

        const regionOptions = billingAccessibleRegions
            .map((country) => {
                const selected = country.iso_code === block?.target ? 'selected' : '';
                return `<option value="${country.iso_code}" ${selected}>${country.full_name}</option>`;
            })
            .join('');

        const clubOptions = ClubsObj.map((club) => {
            const selected = club.id === block?.target ? 'selected' : '';
            return `<option value="${club.id}" ${selected}>${club.name}</option>`;
        }).join('');

        $(html).html(`
            <div class="body">

                <div class="alert alert-blue">
                    <i class="fa fa-bell" style="color: #2262b3;" aria-hidden="true"></i> Please Note:
                    <div class="d-flex flex-column">
                        <em><small>• Line item amounts are in USD</small></em>
                        <em><small>• Line item amounts can be negative to represent discounts</small></em>
                        <em><small>• The charges won\'t appear on the saas billing page until the effective date</small></em>
                    </div>
                </div>

                <form id="config-block-form">
                    <div class="d-flex flex-column gap-1">
                        <div class="d-flex gap-1">
                            ${selectpicker({
                                label: 'Type',
                                name: 'restrictionType',
                                options: [
                                    { label: 'Global', value: 'GLOBAL' },
                                    { label: 'Region', value: 'region' },
                                    { label: 'Club', value: 'club' },
                                ],
                                value: block?.restrictionType ?? 'GLOBAL',
                            })}
                            ${selectpicker({ label: 'Target', name: 'restrictedTo', options: [] })}
                        </div>
                        <div class="d-flex gap-1">
                            ${textField({
                                label: t('Date Effective'),
                                name: 'effective-date',
                                value: block?.dateEffective
                                    ? moment.unix(block.dateEffective).tz('UTC').format('YYYY-MM-DD')
                                    : moment().format('YYYY-MM-DD'),
                                type: 'date',
                            })}
                            ${selectpicker({
                                label: 'Frequency',
                                name: 'frequency',
                                options: [
                                    { label: 'Once', value: 'once' },
                                    { label: 'Recurring (monthly)', value: 'monthly' },
                                ],
                                value: block?.recurring ? 'monthly' : 'once',
                            })}
                        </div>
                        ${textArea({ label: 'Description', name: 'description', value: block?.description ?? '' })}
                        <div class="d-flex gap-1 flex-column additional-charges"></div>
                        <a class="add-item" href="#"><i class="fa fa-plus"></i> Add Item</a>

                    </div>
                </form>
            </div>
        `);

        handleTypeChange();

        if (block?.restrictedTo) {
            $(html).find('select[name=restrictedTo]').val(block.restrictedTo);
            $(html).find('select[name=restrictedTo]').selectpicker('refresh');
        }

        if (block) {
            $(html).find('select[name=restrictionType]').prop('disabled', true);
            $(html).find('select[name=restrictedTo]').prop('disabled', true);
            $(html).find('input[name=effective-date]').prop('disabled', true);
            $(html).find('select[name=frequency]').prop('disabled', true);
            block.items.forEach((item, index) => addLineItem(index === 0, item));
        } else {
            addLineItem(true, { name: 'New Fee', amount: 0 });
            $(html).find('select[name=restrictionType]').on('change', handleTypeChange);
            datePicker($(html).find('input[name=effective-date]'), {
                singleDatePicker: true,
                startDate: moment(),
                minDate: moment().add(1, 'days'),
            });
        }

        $(html).find('select').selectpicker('refresh');
        $(html)
            .find('.add-item')
            .on('click', (e) => {
                e.preventDefault();
                addLineItem(false);
            });

        function addLineItem(showLabel = false, item = null) {
            const deleteDisabled = !showLabel ? '' : 'disabled';
            const div = $(`
                <div class="d-flex align-end gap-1">
                    ${textField({
                        label: showLabel ? t('Line Items') : '',
                        name: 'name',
                        value: item?.name ?? '',
                        type: 'text',
                    })}
                    ${textField({
                        label: showLabel ? t('Amount') : '',
                        name: 'amount',
                        value: item?.amount ?? 0,
                        type: 'number',
                    })}
                    <button type="button" class="btn btn-danger delete-item" ${deleteDisabled}>
                        <i class="fa fa-minus mr-0"></i>
                    </button>
                </div>
            `);

            !showLabel && div.find('.delete-item').on('click', () => div.remove());
            $(html).find('.additional-charges').append(div);
        }

        function handleTypeChange() {
            const type = $(html).find('select[name=restrictionType]').val();
            const targetEl = $(html).find('select[name=restrictedTo]');

            if (type === 'GLOBAL') {
                targetEl.html('').attr('disabled', true);
                $(html).find('select[name=restrictedTo]').selectpicker('refresh');
            } else if (type === 'region') {
                targetEl.html(regionOptions).attr('disabled', false);
                $(html).find('select[name=restrictedTo]').selectpicker('refresh');
            } else {
                targetEl.html(clubOptions).attr('disabled', false);
                $(html).find('select[name=restrictedTo]').selectpicker('refresh');
            }
        }

        async function onSubmit() {
            const newCharges = [];

            $(html)
                .find('.additional-charges > div')
                .map((index, element) => {
                    const name = $(element).find('input[name=name]').val();
                    const amount = $(element).find('input[name=amount]').val();
                    newCharges.push({ name, amount: parseFloat(amount) });
                });

            if (newCharges.some((item) => !item.name)) {
                return Swal.showValidationMessage('Please enter a name for the charge');
            }

            const data = {
                description: $(html).find('textarea[name=description]').val(),
                dateEffective: $(html).find('input[name=effective-date]').val(),
                recurring: $(html).find('select[name=frequency]').val() === 'monthly',
                items: newCharges,
                restrictionType: $(html).find('select[name=restrictionType]').val(),
                restrictedTo: $(html).find('select[name=restrictedTo]').val(),
            };

            // convert date effective to a unix timestamp in UTC
            data.dateEffective = moment.tz(data.dateEffective, 'YYYY-MM-DD', 'UTC').unix();

            if (data.restrictionType === 'GLOBAL') {
                data.restrictedTo = 'GLOBAL';
            }

            const response = await (block
                ? updateSaasBillingAdditionalItemAPI(block.id, data)
                : createSaasBillingAdditionalItemAPI(data));

            if (!response[0]) {
                instantNotification(response[1], 'error');
                return false;
            }

            instantNotification('Per Product Configuration Block saved successfully');
            loadAdditionalFees();
        }

        swalWithBootstrapButtons.fire({
            title: `${block ? 'Edit' : 'New'} Base Charge`,
            html,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: true,
            confirmButtonText: 'Save/Update',
            preConfirm: onSubmit,
        });
    }

    function blockLogsDialog(block) {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');

        if (block.changeLog) {
            const log = block.changeLog;
            $(html).html(`
                <div class="body">
                    <div class="d-flex justify-between">
                        <span>${log.modifiedBy}</span>
                        <span>${moment.unix(log.modifiedDate).format('YYYY-MM-DD HH:mm')}</span>
                    </div>
                </div>
            `);
            $(html).find('.body').append(renderConfigBlock(log, true));
        } else {
            $(html).html(`
                <div class="body">
                    <div class="d-flex justify-between">
                        <span>No previous logs</span>
                    </div>
                </div>
            `);
        }

        swalWithBootstrapButtons.fire({
            title: 'Change Log',
            html,
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
        });
    }

    function additionalBlockLogsDialog(block) {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');

        if (block.historicalData?.length > 0) {
            $(html).html(`
                <ul class="list-group list-group-flush">
                </ul>
            `);

            block.historicalData.forEach((log, index) => {
                const modifiedAt = moment.unix(log.modifiedAt).format('YYYY-MM-DD HH:mm');
                const logItem = $(`
                    <li class="list-group-item clickable" data-toggle="collapse" data-target="#log-${index}">
                        <div class="d-flex justify-between">
                            <span>${log.modifiedBy}</span>
                            <span>${modifiedAt}</span>
                        </div>
                    </li>
                    <li id="log-${index}" class="collapse" style="background-color: #f9f9f9;">
                        <div style="padding: 10px 15px;" class="log-data">
                        </div>
                    </li>
                `);

                log.items.forEach((item) => {
                    const amountValue = formatDiscount(item.amount);
                    logItem.find('.log-data').append(`
                        <div class="d-flex gap-1 justify-between">
                            <span>${item.name}</span>
                            <span>${amountValue}</span>
                        </div>
                    `);
                });

                $(html).find('.list-group').append(logItem);
            });
        } else {
            $(html).html(`
                <div class="body">
                    <div class="d-flex justify-between">
                        <span>No previous logs</span>
                    </div>
                </div>
            `);
        }

        swalWithBootstrapButtons.fire({
            title: 'Change Log',
            html,
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
        });
    }

    function addNoteDialog(block) {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');
        $(html).html(`
            <div class="body">
                <textarea name="note" class="form-control" placeholder="Say something..." rows="12"></textarea>
            </div>
        `);

        async function onSubmit() {
            const content = $(html).find('textarea[name=note]').val();
            const response = await updateSaasBillingAdditionalItemAPI(block.id, { notes: [{ content }] });

            if (!response[0]) {
                return Swal.showValidationMessage(response[1]);
            }

            instantNotification('Note added successfully');
            loadAdditionalFees();
        }

        swalWithBootstrapButtons.fire({
            title: 'Add Note',
            html,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: true,
            confirmButtonText: 'Save',
            preConfirm: onSubmit,
        });
    }

    init();
}
