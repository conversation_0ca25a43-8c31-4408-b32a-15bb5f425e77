// release-history.js

async function sReleaseHistory() {

    $(window).off('resize.adjustSidebar').on('resize.adjustSidebar', function() { // using a named event namespace for better specificity
        adjustSidebarPosition();
    });

    await $.ajax({
        type: "GET",
        url: '/api/release-history',
        success: function(history) {
            generateReleaseUI(history);
        }
    });

    function generateReleaseUI(historyItems) {
        // Sort by date
        historyItems.sort((a, b) => new Date(b.date) - new Date(a.date));

        const rootElement = $(".release-history-container");
        const container = $('<div>').addClass('content col-sm-12 col-md-9').appendTo(rootElement);

        // Create the header
        const header = $('<div>').addClass('release-history-header').appendTo(container);
        $('<h1>').text(t('UBX SaaS and Websites Release History / Changelog')).appendTo(header);
        $('<p>').text(t('Additions and updates to the Performance Hub SaaS and the wider UBX Platform can be found in the following Changelog')).appendTo(header);

        // Loop through sorted historyItems directly
        historyItems.forEach(item => {
            const service = item.service;
            
            // Format the date as "10th, August, 2023"
            const dateObject = new Date(item.date);
            const unixTimestamp = dateObject.getTime(); // Unix timestamp
            const day = dateObject.getDate();
            let suffix = "th";
            if (day === 1 || day === 21 || day === 31) suffix = "st";
            else if (day === 2 || day === 22) suffix = "nd";
            else if (day === 3 || day === 23) suffix = "rd";
            const formattedDate = `${day}${suffix}, ${dateObject.toLocaleString('default', { month: 'long' })}, ${dateObject.getFullYear()}`;

            // Creating card container for each item
            const releaseCard = $('<div>').addClass('release-history-card').appendTo(container);

            // Adding service
            $('<h3>').addClass(`release-history-service ${service}`).text(service).appendTo(releaseCard);

            // Adding date at the top right of the card
            $('<div>')
                .addClass('release-date')
                .text(formattedDate)
                .attr('data-date', unixTimestamp) // Unix timestamp as a data attribute
                .appendTo(releaseCard);

            // Adding summary
            // $(item.htmlSummary).appendTo(releaseCard);
            $(document.createElement('div')).html(item.htmlSummary).appendTo(releaseCard);

        });

        // Continue with sidebar creation
        createSidebar(historyItems);
    }


    function createSidebar(historyItems) {
        const sidebar = $("<div>").addClass("release-history-sidebar col-sm-3 hidden-sm hidden-xs").appendTo($(".release-history-container"));

        // Title for Service Filter
        $("<h4>").text("Filter by Service").appendTo(sidebar);

        const services = [...new Set(historyItems.map(item => item.service))];

        services.forEach(service => {
            const checkboxDiv = $("<div>").addClass("filter-checkbox").appendTo(sidebar);
            const checkbox = $("<input>").attr("type", "checkbox").attr("checked", true).attr("id", `chk-${service}`).addClass("filled-in chk-col-blue").val(service).appendTo(checkboxDiv);
            $("<label>").attr("for", `chk-${service}`).text(service).appendTo(checkboxDiv); // Added 'for' attribute for label

            // Handle the checkbox change event
            checkbox.on("change", function() {
                applyFilters();
            });
        });

        // Date Range Filters
        const dateFilterDiv = $("<div>").addClass("date-filter").appendTo(sidebar);
        $("<h4>").text("Filter by Date").appendTo(dateFilterDiv);

        const dateRanges = [
            { label: "This Month", start: moment().startOf('month'), end: moment().endOf('month') },
            { label: "Last Month", start: moment().subtract(1, 'months').startOf('month'), end: moment().subtract(1, 'months').endOf('month') },
            { label: "This Quarter", start: moment().subtract(3, 'months').startOf('month'), end: moment().endOf('month') },
            { label: "This Year", start: moment().startOf('year'), end: moment().endOf('year') }
        ];

        const currentYear = moment().year();
        const years = [...new Set(historyItems.map(item => moment(item.date).year()))].sort().reverse();

        years.forEach(year => {
            if (year !== currentYear && historyItems.some(item => moment(item.date).year() === year)) { 
                dateRanges.push({ label: year.toString(), start: moment(year, 'YYYY').startOf('year'), end: moment(year, 'YYYY').endOf('year') });
            }
        });

        // Add "All" filter at the end
        dateRanges.push({ label: "All", start: null, end: null });

        dateRanges.forEach(range => {
            const btn = $("<button>").text(range.label)
                                     .data("start", range.start)
                                     .data("end", range.end)
                                     .appendTo(dateFilterDiv);

            // If range is "All", set it as active by default
            if (range.label === "All") {
                btn.addClass("filter-active");
            }

            btn.on("click", function() {
                $(".date-filter button").removeClass("filter-active"); // Remove active class from all buttons
                $(this).addClass("filter-active"); // Add active class to the clicked button
                applyFilters();
            });
        });

        // Message and Reset button
        const noResultsDiv = $("<div>").addClass("no-results-message").hide();

        // Insert the message immediately after the release-history-header div
        noResultsDiv.insertAfter($(".release-history-header"));

        $("<h4>").text("No results found for the current selected filters").appendTo(noResultsDiv);
        const resetBtn = $("<button>").text("Reset Filters").appendTo(noResultsDiv);
        resetBtn.on("click", function() {
            resetFilters();
        });

        adjustSidebarPosition();
    }

    function applyFilters() {

        const activeDateFilterBtn = $(".date-filter button.filter-active");
        const activeStartDate = activeDateFilterBtn.data("start") ? moment(activeDateFilterBtn.data("start")) : null;
        const activeEndDate = activeDateFilterBtn.data("end") ? moment(activeDateFilterBtn.data("end")) : null;

        const selectedServices = $(".release-history-sidebar .filter-checkbox input:checked").map(function() {
            return $(this).val();
        }).get();

        let visibleItemsCount = 0;

        $(".release-history-card").each(function() {
            const cardUnixTimestamp = $(this).find(".release-date").attr('data-date');
            const cardDate = moment.unix(cardUnixTimestamp / 1000); // Convert to seconds
            const cardService = $(this).find(".release-history-service").text().trim();

            if (selectedServices.includes(cardService) && (!activeStartDate || cardDate.isBetween(activeStartDate, activeEndDate, null, '[]'))) {
                $(this).show();
                visibleItemsCount++;
            } else {
                $(this).hide();
            }
        });

        // Show or hide the no results message
        if(visibleItemsCount === 0) {
            $(".no-results-message").show();
        } else {
            $(".no-results-message").hide();
        }
    }

    function resetFilters() {
        // Reset date filter
        $(".date-filter button.filter-active").removeClass("filter-active");
        $(".date-filter button").last().addClass("filter-active"); // Set "All" as active
        // Reset service filter
        $(".filter-checkbox input[type='checkbox']").prop('checked', true);
        // Hide the no results message
        $(".no-results-message").hide();
        // Apply filters
        applyFilters();
    }

    function adjustSidebarPosition() {
        const sidebar = $(".release-history-sidebar");
        const container = $(".release-history-container");
        const windowHeight = $(window).height();

        if ((sidebar.height() + 100) < windowHeight) {
            sidebar.addClass('pinned-sidebar');
            const leftPosition = container.offset().left + container.width() - sidebar.outerWidth();
            sidebar.css('left', `${leftPosition}px`);
        } else {
            sidebar.removeClass('pinned-sidebar');
            sidebar.css('left', ''); // Resetting the left value
        }
    }

}

