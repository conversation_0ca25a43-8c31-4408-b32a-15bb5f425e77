function sDisplayAdmin() {
    const rootEl = $('#display-admin');
    const basePath = '/display-admin';
    const tabs = ['display-ui-options', 'display-data-sources'];

    // Hide everything if user is not a global admin
    if (!signedInUserData.isGlobalAdmin) {
        rootEl.addClass('hidden');
    }

    function init() {
        initUrl();
        initTabs();
        handleTabChange();
    }

    function initUrl() {
        const tab = location.pathname.split('/').pop(); // TODO Do a better path extraction, handle query strings like search terms
        if (tabs.includes(tab)) return;
        history.pushState({ tabChange: true }, null, `${basePath}/${tabs[0]}`);
    }

    function initTabs() {
        const tab = location.pathname.split('/').pop();

        $(`.nav-tabs a[href="#${tab}"]`).tab('show');
        $('.nav-tabs a').on('click', function (e) {
            e.preventDefault();
            const href = $(this).attr('href');
            const formattedHref = `${basePath}/${href.replace('#', '')}`;
            history.pushState({ tabChange: true }, null, formattedHref);

            rootEl.find('.tab-pane').removeClass('in active');
            rootEl.find(`.tab-pane${href}`).addClass('in active');

            handleTabChange();
        });
    }

    function handleTabChange() {
        const tab = location.pathname.split('/').pop();
        if (tab === 'display-ui-options') return sDisplayUIOptions();
        if (tab === 'display-data-sources') return sDisplayDataSources();
    }

    init();
}
