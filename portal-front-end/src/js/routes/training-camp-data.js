const loadTrainingCampMemberInfo = (clubId) => {
    const seasonFilterId = 'tc-members-season';
    const memberInfoPageId = '#training-camp-member-info-page';
    const currentEpochLength = Math.floor(Date.now() / 1000).toString().length;
    let overrideShowCampSelect = undefined;
    let selectedSeason;

    $(document).ready(function () {
        loadPage();
    });

    const renderBreadcrumbs = () => {
        breadcrumbsComponent(
            '.breadcrumbs-container',
            [
                { label: t('Training Camp'), url: '/training-camp' },
                { label: t('Member Info'), active: true },
            ],
            true
        );
    };

    const loadPage = () => {
        const pathname = location.pathname;
        const pathParts = pathname.split('/');
        let userId = null;

        if (pathParts.length >= 2) {
            const lastPart = pathParts[pathParts.length - 1];
            if (lastPart !== 'training-camp-info') {
                userId = lastPart;
            }
        }

        renderBreadcrumbs();

        if (userId && clubId) {
            // Show skeleton
            renderMemberInfo(null, true);
            renderTrainingCampInfo(null, true);

            // Fetch member training camp info
            fetchTCMemberInfo({ clubId, userIds: [userId] })
                .then(({ tcData }) => {
                    const memberTcData = tcData && tcData.length > 0 ? tcData[0] : null;

                    renderMemberInfo(memberTcData, false);
                    renderTrainingCampInfo(memberTcData, false);
                })
                .catch((error) => {
                    console.error('Error fetching member data:', error);

                    renderMemberInfo(null, false);
                    renderTrainingCampInfo();
                });
        } else {
            // Display error messages
            const $memberInfoPage = $(`${memberInfoPageId} .mem-info_body`);
            let errorTitle = 'Failed to fetch training member info';
            const errorList = [];
            let errorMessage;

            !userId && errorList.push('<li>No User ID provided</li>');
            !clubId && errorList.push('<li>No Club ID provided</li>');

            errorMessage = errorList.join('');

            $memberInfoPage.html(`
                <div class="ph-alert ph-alert_danger">
                    <div class="ph-alert__title">${errorTitle}</div>
                    <div class="ph-alert__description ${
                        errorMessage ? '' : 'd-none'
                    }"><ul class="pl-5 m-0">${errorMessage}</ul></div>
                </div>
            `);
        }
    };

    const fetchTCMemberInfo = async ({ clubId = null, userIds }) => {
        const queryParams = {
            userIds,
            isUserMemberInfo: true,
        };
        const queryString = buildQueryParams(queryParams);
        const results = await apiCall(
            `/api/members-leads/trainingcamp/club/season-members/${clubId}${queryString}`,
            'GET',
            null,
            '5m'
        );

        if (results && 'status' in results && results.status === 'success') {
            const { tcData, missingMMSUsers } = results.data;

            return Promise.resolve({ tcData, missingMMSUsers });
        }

        return Promise.reject(results);
    };

    const renderMemberInfo = (memberData = null, isLoading = false) => {
        const $memberInfoPage = $(`${memberInfoPageId} .mem-info_body`);
        const defaultInfo = {
            fullName: 'John Doe',
            email: '<EMAIL>',
            phone: '+1234567890',
            addressDetails: {
                address1: '123 Main St',
                address2: 'Apt 1',
                city: 'Anytown',
                state: 'CA',
                zip: '12345',
                country: 'AU',
            },
            gender: 'Male',
            dateOfBirth: '2024-01-01',
        };

        // Use provided memberData or fall back to default values
        const data = isLoading ? defaultInfo : memberData;

        const memberInfo = [
            { label: t('Name'), value: data?.fullName || 'N/A' },
            { label: t('Email Address'), value: data?.email || 'N/A' },
            { label: t('Phone'), value: data?.phone || 'N/A' },
            { label: t('Address'), value: parseMMSUserAddress(data?.addressDetails) || 'N/A' },
            { label: t('Gender'), value: t(data?.gender) || 'N/A' },
            { label: t('Birthdate'), value: data?.dateOfBirth || 'N/A' },
        ];

        if (isLoading) {
            $memberInfoPage.addClass('isLoading');
        } else {
            $memberInfoPage.removeClass('isLoading');
        }

        $memberInfoPage.html(`
            ${memberInfo
                .map(
                    (info) => `
                <div class="mem-info_item">
                    <div class="mem-info_label use-skeleton">${info.label}</div>
                    <div class="mem-info_value use-skeleton">${info.value}</div>
                </div>
            `
                )
                .join('')}
        `);
    };

    const genTCMetricCardValueTopEl = ({ goalDiff, isProgress, isSame, isGoalMet, metricUnit }, label) => {
        let valueTopEl = '';

        if (isGoalMet) {
            valueTopEl += `${genBadge({ value: 'Goal', trailingIcon: 'done_all' })}`;
        }

        if (isSame) {
            valueTopEl += ` ${genBadge({
                value: t('No Progress'),
                color: 'orange--old',
            })}`;
        }

        if (goalDiff) {
            let numSign = goalDiff > 0 ? '+' : '';
            valueTopEl += ` ${genBadge({
                value: `${numSign}${goalDiff}${metricUnit ? ' ' + metricUnit : ''}`,
                color: isSame ? 'orange' : isProgress ? 'green' : 'red',
            })}`;
        }

        return valueTopEl;
    };

    const genTCMetricCard = (
        { label, value, classes, valueClasses, valueTopEl, valueLeftEl, labelTrailingEl, start, ...metaData },
        comparisonCard
    ) => {
        const metricUnit = metaData?.metricUnit;
        const labelText = t(label);

        if (!valueTopEl) {
            valueTopEl = genTCMetricCardValueTopEl(metaData, label);
        }

        return `
        <div class="d-flex flex-column flex-gap-3">
            <div class="tc-metric-card ${classes ? classes?.join(' ') : ''}">
                <div class="tc-metric-card_header">
                    <div class="use-skeleton">${labelText}</div>
                    <div class="use-skeleton d-flex align-items-center">${labelTrailingEl ? labelTrailingEl : ''}</div>
                </div>
                <div class="tc-metric-card_body ${valueClasses ? valueClasses?.join(' ') : ''}">
                    <div class="d-flex flex-gap-2">
                        <div class="use-skeleton">${valueTopEl ? valueTopEl : ''}</div>
                    </div>
                    <div class="d-flex flex-gap-4 ml-auto"> 
                        ${
                            start
                                ? `
                            <div class="__body-start_value use-skeleton">${start}${
                                      metricUnit && start ? ` <span class="__body-type">${metricUnit}</span>` : ''
                                  }</div>
                            <div class="use-skeleton"><img src="/assets/images/metric-chevron.svg" /></div>
                        `
                                : valueLeftEl
                                ? valueLeftEl
                                : ''
                        } 
                        <div class="${!value ? '__body-empty' : ''} use-skeleton">
                        ${value ? value : 'N/A'}${
            metricUnit && value ? ` <span class="__body-type">${metricUnit}</span>` : ''
        }</div>
                    </div>
                </div>
            </div>
            ${comparisonCard ? genMetricComparisonCard(comparisonCard) : ''}
        </div>
       `;
    };

    const genTCMetricCardsWrapper = ({ title, cards, icon, wrapperClasses, comparisonData }) => {
        const titleText = t(title);
        const noDataText = t('data not available');

        return `
            <div class="d-flex flex-column flex-gap-4 ${wrapperClasses ? wrapperClasses : ''}">
                <div class="tc-metric_header"><i class="material-icons">${icon}</i><div>${titleText}</div></div>
                <div class="d-flex flex-gap-4 flex-wrap">
                    ${
                        cards && cards.length > 0
                            ? cards
                                  ?.map((card) => {
                                      let comparison;

                                      if (comparisonData && card.label in comparisonData) {
                                          comparison = comparisonData[card.label];
                                      }

                                      return genTCMetricCard(card, comparison);
                                  })
                                  .join('')
                            : `<div class="ph-alert"><div class="ph-alert__description">${titleText} ${noDataText}.</div></div>`
                    }
                </div>
            </div>
        `;
    };

    const genBadge = ({ value, color = 'green', classes, trailingIcon, leadingIcon }) => {
        return `<div class="ph-badge ${color ? `badge-${color}` : ''} ph-badge--borderless flex-gap-1 ${
            classes ? classes : ''
        } use-skeleton">${leadingIcon ? `<i class="material-icons">${leadingIcon}</i>` : ''}${value}${
            trailingIcon ? `<i class="material-icons">${trailingIcon}</i>` : ''
        }</div>`;
    };

    const genNoteEntryItem = ({ content, footerTrailingEl, footerLeadingEl }) => {
        return `
            <div class="note-entry">
                <div class="note-entry_content use-skeleton ${content ? '' : '__content-empty'}">${
            content ? content : 'No description given...'
        }</div>
                <div class="note-entry_footer">
                    <div class="__footer __footer-trailing use-skeleton">
                        ${footerTrailingEl ? footerTrailingEl : ''}
                    </div>
                    <div class="__footer __footer-leading use-skeleton">
                        ${footerLeadingEl ? footerLeadingEl : ''}
                    </div>
                </div>
            </div>
        `;
    };

    const genJournalEnergyLevel = ({ level }) => {
        if (level) {
            return `
                <div class="journal-level --journal-energy --energy-${level}">
                    <div class="__level-icon"><i class="material-icons">local_fire_department</i></div>
                    <div class="__level-value">${level}</div>
                </div>
            `;
        }

        return '';
    };

    const genJournalMoodLevel = ({ level }) => {
        if (level) {
            return `
                <div class="journal-level --journal-mood --mood-${level}">
                    ${
                        level > 0 && level <= 3
                            ? `<div class="__level-icon"><img src="/assets/images/mood-${level}.svg" /></div>`
                            : ''
                    }
                    <div class="__level-value">${level}</div>
                </div>
            `;
        }

        return '';
    };

    const genRangeBar = ({ minValue, maxValue, startValue, endValue, goalValue, label, reverseRangeDiff = false }) => {
        const range = reverseRangeDiff ? minValue - maxValue : maxValue - minValue;
        let positionValue = startValue ? startValue : endValue;
        let startPercent = 0;
        let activeWidth = '0px';

        if (range <= 0) {
            console.error(`Invalid range: range is ${range}, should be greater than 0.`);
            return '';
        }

        if (positionValue) {
            startPercent = parseFloat(Math.abs(((positionValue - minValue) / range) * 100).toFixed(3));
            activeWidth = parseFloat(Math.abs(((endValue - startValue) / range) * 100).toFixed(3)) + '%';
        }

        if ((startValue === null) !== (endValue === null)) {
            activeWidth = '4px';
        }

        const goalPercent = goalValue ? parseFloat(Math.abs(((goalValue - minValue) / range) * 100).toFixed(3)) : 0;

        return `
            <div class="d-flex flex-gap-2 align-items-center">
                <div class="ph-range-bar_label">${label}</div>
                <div class="ph-range-bar_container">
                    <div class="ph-range-bar_active" style="left: ${startPercent}%; width: ${activeWidth};"></div>
                    <div class="ph-range-bar_goal" style="left: ${goalPercent}%; display: ${
            goalPercent ? 'block' : 'none'
        };"></div>
                </div>
            </div> 
        `;
    };

    const genMetricComparisonCard = (metric) => {
        const { minValue = 0, maxValue = 100, reverseRangeDiff, data } = metric;
        let rangeBars = '';

        data.forEach((item) => {
            const { campSeason, startValue, endValue, goalValue } = item;
            const rangeBarHTML = genRangeBar({
                minValue,
                maxValue,
                startValue,
                endValue,
                goalValue,
                label: campSeason,
                reverseRangeDiff,
            });

            rangeBars += rangeBarHTML;
        });

        return `
            <div class="metric-comparison-card">
                <div class="d-flex flex-gap-2 align-items-center">
                    <img class="comparison-tc-icon" src="/assets/images/tc-icon.svg" />
                    <div class="comparison-card_details">
                        <span>${minValue}</span>
                        <span><img class="comparison-card_chevron" src="/assets/images/metric-chevron.svg" /></span>
                        <span>${maxValue}</span>
                    </div>
                </div> 
                <div class="comparison-card_range-bar">
                    ${rangeBars}
                </div> 
            </div>
        `;
    };

    const parseGoalEntries = (data) => {
        const goalEntries = [];

        if (data) {
            data.forEach((item) => {
                const { description, dueDate } = item;
                const formattedDueDate = formatDateMDY(dueDate);

                goalEntries.push({
                    content: description,
                    footerTrailingEl: `${dueDate ? t('Due on') + ' ' + formattedDueDate : ''}`,
                    dueDate: new Date(dueDate),
                });
            });

            goalEntries.sort((a, b) => b.dueDate ?? 0 - a.dueDate ?? 0);
        }

        return goalEntries;
    };

    const parseJournalEntries = (data) => {
        try {
            const journalEntries = [];

            if (data) {
                data.forEach((item) => {
                    const { winText, weekId, day, addedAt, energyLevel, progressFeeling } = item;
                    const weekLabel = weekId ? `<span class="">${t('Week')} ${weekId}</span>` : '';
                    const dayLabel = day ? `<span class="">${t('Day')} ${day}</span>` : '';
                    const weekDayLabel = `<div class="d-flex flex-gap-2">${weekLabel} ${dayLabel}</div>`;
                    const timestamp = normalizeTimestampToSeconds(addedAt, currentEpochLength);

                    journalEntries.push({
                        content: winText,
                        footerTrailingEl: `<div class="d-flex flex-gap-2 align-items-center">${weekDayLabel} ${
                            weekLabel || dayLabel ? '<div>|</div>' : ''
                        } ${unixTimeFormat(timestamp)}</div>`,
                        footerLeadingEl: `${genJournalEnergyLevel({ level: energyLevel })} ${genJournalMoodLevel({
                            level: progressFeeling,
                        })}`,
                        timestamp,
                    });
                });

                journalEntries.sort((a, b) => b.timestamp - a.timestamp);
            }

            return journalEntries;
        } catch (e) {
            console.error(e);
            return [];
        }
    };

    const parseLeaderboardEntries = (type, data) => {
        const labelMap = {
            visits: t('Club Visits'),
            meps: t('MEPS'),
        };

        if (data) {
            const classes = ['--size-small'];
            const clubRanking = data.clubRanking;
            const badgeEl = {
                value: clubRanking ? `#${clubRanking}` : '',
            };
            let rating = data.rating;

            if (clubRanking >= 1 && clubRanking <= 3) {
                classes.push('--color-yellow');
                badgeEl.leadingIcon = 'emoji_events';
                badgeEl.color = 'yellow';
            } else {
                classes.push('--color-gray');
                badgeEl.color = '';
            }

            if (rating == 0) {
                rating = '0';
            }

            return {
                label: labelMap[type],
                value: rating ?? '00',
                classes,
                valueLeftEl: genBadge(badgeEl),
            };
        }

        return null;
    };

    const renderTrainingCampInfo = (data = null, isLoading = false) => {
        const $trainingCampInfoPage = $(`${memberInfoPageId} .tc-info-body`);
        const tcData = data?.trainingCamp ?? null;
        const error = {
            tcInfo: false,
            metricsInfo: false,
            preferencesInfo: false,
            leaderboardInfo: false,
            goalsInfo: false,
            journalsInfo: false,
        };
        const defErrorInfo = {
            description: `${t('Please contact HQ')}.`,
            type: 'danger',
            icon: 'report',
        };

        const leaderboardCards = [
            {
                label: 'Club Visits',
                value: 100,
                classes: ['--size-small', '--color-gray'],
                valueLeftEl: genBadge({ value: '#1', color: '', leadingIcon: 'emoji_events' }),
            },
            {
                label: 'MEPS',
                value: 1821,
                classes: ['--size-small', '--color-gray'],
                valueLeftEl: genBadge({ value: '#743', color: '' }),
            },
        ];

        const defPrefCardInfo = {
            classes: ['--size-small'],
            valueClasses: ['--size-small'],
        };
        const tooltipText = t(
            `Myzone is a device that allows you to track your heart rate and other metrics. It is a small, wearable device that you can wear around your wrist`
        );
        const myzoneInfoEl = `<i style="cursor: pointer;" class="material-icons" data-toggle="tooltip" data-placement="right" title="${tooltipText}.">info</i>`;

        const preferencesCards = [
            {
                label: 'Meal Plan',
                value: 'Vegetarian',
                ...defPrefCardInfo,
            },
            {
                label: 'Myzone Belt',
                value: 'Enabled',
                ...defPrefCardInfo,
                labelTrailingEl: myzoneInfoEl,
            },
        ];

        const metricComparisonCards = {
            Weight: {
                minValue: 74.4,
                maxValue: 69.5,
                reverseRangeDiff: true,
                data: [
                    { campSeason: '21', startValue: 71.8, endValue: 69.5, goalValue: 70.0 },
                    { campSeason: '20', startValue: 72.4, endValue: 71.0, goalValue: 70.5 },
                    { campSeason: '19', startValue: 74.4, endValue: 72.8, goalValue: 73.0 },
                ],
            },
            'Body Fat': {
                minValue: 74.4,
                maxValue: 69.5,
                reverseRangeDiff: true,
                data: [
                    { campSeason: '21', startValue: 71.8, endValue: 69.5, goalValue: 70.0 },
                    { campSeason: '20', startValue: 71.4, endValue: null, goalValue: 70.5 },
                    { campSeason: '19', startValue: 74.4, endValue: 72.8, goalValue: 73.0 },
                ],
            },
            'Muscle Mass': {
                minValue: 74.4,
                maxValue: 69.5,
                reverseRangeDiff: true,
                data: [
                    { campSeason: '21', startValue: 71.8, endValue: 69.5, goalValue: 70.0 },
                    { campSeason: '20', startValue: 72.4, endValue: 71.0, goalValue: 70.5 },
                    { campSeason: '19', startValue: 74.4, endValue: 72.8, goalValue: 73.0 },
                ],
            },
        };
        const metricsCards = [
            {
                label: 'Weight',
                value: '69.2kg',
                start: '70.3kg',
                valueTopEl: `${genBadge({ value: 'Goal', trailingIcon: 'done_all' })} ${genBadge({ value: '-1.8kg' })}`,
            },
            {
                label: 'Body Fat',
                value: '24.8%',
                start: '23.3%',
                valueTopEl: `${genBadge({ value: '+2.5%', color: '' })}`,
            },
            {
                label: 'Muscle Mass',
                start: '23.3%',
            },
            {
                label: 'Anaerobic Conditioning',
                value: '24.8%',
            },
            {
                label: t('Muscular Endurance'),
                value: null,
            },
            {
                label: 'Power',
                value: '24.8%',
            },
        ];
        const skeletonGoal = [
            {
                content: 'Improve deadlift form and reach 100kg',
                footerTrailingEl: 'Due on Mar. 9, 2025',
            },
            {
                content: 'Improve deadlift form and reach 100kg',
                footerTrailingEl: 'Due on Apr. 11, 2025',
            },
        ];
        const skeletonJournal = [
            {
                content: 'Feeling strong and motivated! Feeling strong and motivated! Feeling strong and motivated!',
                footerTrailingEl: 'Week 1 Day 1 | Mar. 9, 2025',
                footerLeadingEl: `${genJournalEnergyLevel({ level: 4 })} ${genJournalMoodLevel({ level: 3 })}`,
            },
            {
                content: 'Great first session!',
                footerTrailingEl: 'Week 1 Day 2 | Mar. 10, 2025',
                footerLeadingEl: '2024-01-01',
            },
        ];
        const urlParams = getQueryStringsObject(window.location.search);
        let recentCampSeason = '';
        let recentCamp = null;
        let userMetrics = isLoading ? metricsCards : [];
        let userGoals = isLoading ? skeletonGoal : [];
        let userJournals = isLoading ? skeletonJournal : [];
        let userPreferences = isLoading ? preferencesCards : [];
        let userLeaderboard = isLoading ? leaderboardCards : [];
        let pageHtml = '';
        let seasons = [];

        if (!isLoading) {
            error.tcInfo = condAlertBox(
                () => {
                    const myzoneInfo = {
                        label: t('Myzone Belt'),
                        value: t('Not Found'),
                        ...defPrefCardInfo,
                        labelTrailingEl: myzoneInfoEl,
                    };

                    overrideShowCampSelect = undefined;

                    if ('mealPlanName' in data) {
                        const mealPlan = t(data?.mealPlanName) ?? 'N/A';
                        userPreferences.push({
                            label: t('Meal Plan'),
                            value: mealPlan,
                            ...defPrefCardInfo,
                        });
                    }

                    if ('myzoneBeltId' in data && data.myzoneBeltId) {
                        myzoneInfo.value = t('Enabled');
                    }

                    userPreferences.push(myzoneInfo);
                    seasons = 'seasons' in tcData ? Object.keys(tcData.seasons).sort((a, b) => b - a) : [];
                    let querySeason =
                        [seasonFilterId] in urlParams ? urlParams[seasonFilterId] : tcData?.recentSeason ?? null;
                    if (querySeason == 'all') {
                        querySeason = null;
                    }

                    error.tcInfo = condAlertBox(
                        () => {
                            recentCampSeason = querySeason;

                            if (recentCampSeason && !isNaN(recentCampSeason)) {
                                selectedSeason = recentCampSeason;
                            } else {
                                selectedSeason = seasons[0];
                            }

                            error.tcInfo = condAlertBox(
                                () => {
                                    recentCamp =
                                        selectedSeason && tcData?.seasons ? tcData.seasons[selectedSeason] : null;

                                    if (recentCamp) {
                                        error.metricsInfo = condAlertBox(
                                            () => (userMetrics = recentCamp.metrics),
                                            'metrics' in recentCamp,
                                            { title: 'No metrics data available', error },
                                            defErrorInfo
                                        );

                                        if ('goals' in recentCamp) {
                                            userGoals = parseGoalEntries(recentCamp?.goals);
                                        }

                                        if ('journal' in recentCamp) {
                                            userJournals = parseJournalEntries(recentCamp?.journal);
                                        }

                                        if ('visits' in recentCamp) {
                                            userLeaderboard.push(parseLeaderboardEntries('visits', recentCamp?.visits));
                                        }

                                        if ('meps' in recentCamp) {
                                            userLeaderboard.push(parseLeaderboardEntries('meps', recentCamp?.meps));
                                        }
                                    }
                                },
                                tcData && 'recentSeason' in tcData,
                                { title: 'No training camp data available' },
                                defErrorInfo
                            );
                        },
                        ((querySeason && querySeason in tcData?.seasons) || !querySeason) && seasons.length > 0,
                        {
                            title: `${t('No training camp data available')}: ${t('Camp')} ${urlParams[seasonFilterId]}`,
                            description: `${t('Camps available for this member')}: ${Object.keys(tcData?.seasons)
                                .sort((a, b) => b - a)
                                .join(', ')}`,
                        },
                        defErrorInfo,
                        () => {
                            overrideShowCampSelect = true;
                        }
                    );
                },
                !!data,
                { title: 'No training camp data available' },
                defErrorInfo
            );
        }

        if (error.tcInfo) {
            pageHtml = genAlertBox(error.tcInfo);
        } else {
            pageHtml = `
                <div class="d-flex flex-gap-8">
                    ${genTCMetricCardsWrapper({
                        title: 'Leaderboard',
                        cards: userLeaderboard,
                        icon: 'leaderboard',
                        wrapperClasses: 'sm:w-min',
                    })}
                    <div class="h-auto py-2"><hr class="w-px h-full" /></div>
                    ${genTCMetricCardsWrapper({
                        title: 'Preferences',
                        cards: userPreferences,
                        icon: 'tune',
                        wrapperClasses: 'sm:w-min',
                    })}
                </div>
                <div class="w-full"><hr class="w-full" /></div>
                <div class="d-flex flex-gap-6">
                    ${
                        error.metricsInfo
                            ? genAlertBox(error.metricsInfo)
                            : genTCMetricCardsWrapper({
                                  title: 'Benchmark & Metrics',
                                  cards: userMetrics,
                                  icon: 'fitness_center',
                                  // comparisonData: metricComparisonCards,
                              })
                    }
                </div>
                <div class="w-full"><hr class="w-full" /></div>
                <div class="sm:flex-column d-flex flex-gap-8">
                    <div class="entries-col w-full d-flex flex-column flex-gap-4">
                        <div class="tc-metric_header"><i class="material-icons">flag</i><div>${t('Goals')}</div></div>
                        <div class="d-flex flex-column flex-gap-4">
                            ${
                                userGoals?.length > 0
                                    ? userGoals.map((entry) => genNoteEntryItem(entry)).join('')
                                    : `<div class="ph-alert"><div class="ph-alert__description">${t(
                                          'No goal entries yet'
                                      )}...</div></div>`
                            }
                        </div>
                    </div>
                    <div class="h-auto py-2 sm:d-none"><hr class="w-px h-full" /></div>
                    <div class="entries-col w-full d-flex flex-column flex-gap-4">
                        <div class="tc-metric_header"><i class="material-icons">book</i><div>${t(
                            'Journals'
                        )}</div></div>
                        <div class="d-flex flex-column flex-gap-4">
                            ${
                                userJournals?.length > 0
                                    ? userJournals.map((entry) => genNoteEntryItem(entry)).join('')
                                    : `<div class="ph-alert"><div class="ph-alert__description">${t(
                                          'No journal entries yet'
                                      )}...</div></div>`
                            }
                        </div>
                    </div>
                </div>
            `;
        }

        if (isLoading) {
            $trainingCampInfoPage.addClass('isLoading');
        } else {
            $trainingCampInfoPage.removeClass('isLoading');
        }

        if ((seasons.length > 1 && overrideShowCampSelect === undefined) || overrideShowCampSelect) {
            // TODO: refactor to component function in shared functions
            const $tcCardSelect = $('#training-camp-info-select-container');
            const seasonOptions = seasons.map((season) => ({
                label: season,
                value: season,
            }));
            const selectLabel = `<div class="d-flex align-items-center flex-gap-2"><img src="/assets/images/tc-icon.svg" class="select-icon" />${t(
                'Camp'
            )}</div>`;

            const phSelectHTML = `
                <div class="ph-select-container">
                    <label class="ph-select-label">${selectLabel}</label>
                    <div class="ph-select">
                        <div class="ph-select-trigger">
                            <span class="ph-select-value">${selectedSeason || 'Select'}</span>
                            <i class="material-icons">arrow_drop_down</i>
                        </div>
                        <div class="ph-select-options">
                            ${seasonOptions
                                .map(
                                    (option) =>
                                        `<div class="ph-select-option ${
                                            option.value === selectedSeason ? 'selected' : ''
                                        }" 
                                      data-value="${option.value}">${option.label}</div>`
                                )
                                .join('')}
                        </div>
                    </div>
                </div>
            `;

            $tcCardSelect.html(phSelectHTML);

            const $phSelect = $tcCardSelect.find('.ph-select');
            const $phSelectTrigger = $phSelect.find('.ph-select-trigger');
            const $phSelectOptionItems = $phSelect.find('.ph-select-option');

            $phSelectTrigger.on('click', function (e) {
                e.stopPropagation();
                $phSelect.toggleClass('opened');
            });

            $phSelectOptionItems.on('click', function (e) {
                e.stopPropagation();
                const selectedValue = $(this).data('value');
                const selectedText = $(this).text();

                $phSelect.find('.ph-select-value').text(selectedText);
                $phSelectOptionItems.removeClass('selected');
                $(this).addClass('selected');
                $phSelect.removeClass('opened');

                if (selectedValue !== selectedSeason) {
                    handleSeasonChange(selectedValue, data);
                }
            });

            $(document).on('click', function () {
                $phSelect.removeClass('opened');
            });
        }

        $trainingCampInfoPage.html(pageHtml);
    };

    const handleSeasonChange = (newSeason, data) => {
        if (!newSeason) return;

        selectedSeason = newSeason;
        const tcData = data?.trainingCamp ?? null;
        const selectedCamp = tcData?.seasons ? tcData.seasons[selectedSeason] : null;

        updateHrefQuery({
            [seasonFilterId]: newSeason,
        });

        if (selectedCamp) {
            renderTrainingCampInfo(
                {
                    ...data,
                    trainingCamp: {
                        ...tcData,
                        recentSeason: selectedSeason,
                    },
                },
                false
            );
        } else {
            renderTrainingCampInfo(data, false);
        }
    };
};
