// agreementsAdmin.js
const DEFAULT_CLUB_USER_AGR = {
    name: '',
    content: '',
    clubId: 'Global',
    region: '',
    type: 'waiver',
    template: true,
    createdByAdmin: true,
};

let countryList = null;
let adminAgreementList = null;
let agreementsChart = null;
let table = null;
let agreementsAdminAccess = {};

async function sAgreementsAdmin() {
    updateInitUrl();
    initTabs();
    loadingOverlay('#agreements-container');
    loadingOverlay('#overview');
    getAllAgreements();

    $('#default-agreements-container').prepend(
        `<div id='list-agreement-container'>
            <table id="default-agreement-table" class="table table-hover dataTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Type</th>
                        <th>Region</th>
                        <th>Template</th>
                        <th>Created</th>
                        <th>&nbsp;</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>`
    );

    $('#club-variations').html(`
        <p><strong>Club variations</strong></p>
        <table id="club-list-config-agreement" class="table table-hover dataTable">
            <thead>
                <tr>
                    <th>Club</th>
                    <th>Title</th>
                    <th>Template</th>
                    <th>Created</th>
                    <th>Region</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    `);

    const access = await getMyOrgAccessForModuleAPI('agreements-admin');
    if (!access[0]) return instantNotification('Error loading access data', 'error');

    agreementsAdminAccess = access[1];
    await fetchAdminAgreements(renderAdminAgreementList);
    await fetchClubUsedAgreement(renderAdminClubAgreementUsed);

    countryList = await getCountriesOfClubsAvailableToUser();
    countryList = countryList.sort((a, b) => a.name.localeCompare(b.name));

    function updateInitUrl() {
        if (location.pathname !== '/agreements-admin') return;
        history.pushState({ tabChange: true }, null, '/agreements-admin/overview');
    }

    function initTabs() {
        const tab = location.pathname.split('/').pop();

        // Simple logic (taken from marketing & print) - to select whatever "tab" the user links to from a deeplink...
        $(`#agreements-admin-tabs.nav-tabs a[href="#${tab}"]`).tab('show');
        $('#agreements-admin-tabs.nav-tabs a').on('click', function (e) {
            e.preventDefault();
            const querystring = location.search;
            const href = $(this).attr('href');
            const formattedHref = '/agreements-admin/' + href.replace('#', '') + querystring;
            history.pushState({ tabChange: true }, null, formattedHref);
        });
    }

    function newAgreementModal(agreementData) {
        const title = agreementData?.agreementid ? 'Edit Agreement' : 'New Agreement';
        let lJQ = $;

        let orgOptions =
            agreementsAdminAccess.orgs?.map((org) => ({
                label: org.name,
                value: org.organisationId,
            })) || [];

        if (agreementsAdminAccess.isGlobalAdmin) {
            orgOptions = [
                {
                    label: 'Global (Applies to all Orgs)',
                    value: 'global',
                },
                ...orgOptions,
            ];
        }

        let agreementsDialog = `
            <div class="new-agreement-dialog">
                <fieldset><legend>${title}</legend>
                    <div class="row">
                        <div class="col-sm-2">
                            Title:
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group form-group-no-margin">
                                <div class="form-line">
                                    <input class="form-control" id="agreementTitle" type="text" value="${
                                        agreementData == undefined || agreementData.title == undefined
                                            ? ''
                                            : agreementData.title
                                    }" placeholder="Name/Agreement Title">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            Date Displayed:
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group form-group-no-margin">
                                <div id="dateDisplayed" class="selectbox selectbox-form-control pull-left">
                                    <i class="material-icons" style="transform: translateY(2.5px);">calendar_today</i>
                                    <span></span> <b class="caret"></b>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-2">
                            Organisation:
                        </div>
                        <div class="col-sm-4">
                            ${selectpicker({
                                name: 'organisation-id',
                                options: orgOptions,
                                value: agreementData?.organisationId ?? 'global',
                            })}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-2">
                            Agreement Type:
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group form-group-no-margin">
                                ${selectpicker({
                                    name: 'agreement-type',
                                    options: [
                                        { label: 'All Facilities', value: 'global' },
                                        { label: 'Individual Facility(s)', value: 'club' },
                                        { label: 'Region(s)', value: 'region' },
                                    ],
                                    value: agreementData?.agreementType ?? 'global',
                                })}
                            </div>
                        </div>
                        <div class="col-sm-2">
                            Date Effective:
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group form-group-no-margin">
                                <div id="dateEffective" class="selectbox selectbox-form-control pull-left">
                                    <i class="material-icons" style="transform: translateY(2.5px);">calendar_today</i>
                                    <span></span> <b class="caret"></b>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-2">
                            <div class="individual-club-container">
                                Selected Club(s):
                            </div>
                            <div class="region-container">Region(s):</div>
                        </div>
                        <div class="col-sm-4"> 
                            <div class="individual-club-container">
                                ${selectpicker({
                                    name: 'individual-club',
                                    options: [],
                                    multiple: true,
                                })}
                            </div>
                            <div class="region-container">
                                ${selectpicker({
                                    name: 'region',
                                    options: [],
                                    multiple: true,
                                })}
                            </div>
                        </div>
                        <div class="col-sm-2">
                            Required Acknowledgement:
                        </div>
                        <div class="col-sm-4">
                            ${selectpicker({
                                name: 'required-acknowledgement',
                                options: [
                                    {
                                        label: 'None',
                                        value: 'none',
                                    },
                                    {
                                        label: 'Popup Only',
                                        value: 'popup',
                                    },
                                    {
                                        label: '"I Agree" Checkbox',
                                        value: 'checkbox',
                                    },
                                    {
                                        label: 'Signature',
                                        value: 'signature',
                                    },
                                ],
                                value: agreementData?.requiredAcknowledgement ?? 'checkbox',
                            })}
                        </div>
                    </div>

                </fieldset>
                <fieldset><legend>Agreement Summary</legend>
                    <div class="row">
                        <div class="col-sm-12">

                            <div class="form-group">
                                <div class="form-line">
                                    <textarea rows="4" class="form-control no-resize" id="agreementSummary" placeholder="Provide a high level summary about this agreement ... This will be shown Before/Above the Agreement.">${
                                        agreementData == undefined || agreementData.summary == undefined
                                            ? ''
                                            : agreementData.summary
                                    }</textarea>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <ul class="nav nav-tabs" role="tablist">
                                <li role="presentation" class="active"><a href="#file" aria-controls="file" role="tab" data-toggle="tab">Agreement File Upload</a></li>
                                <li role="presentation"><a href="#html" aria-controls="html" role="tab" data-toggle="tab">Text Input</a></li>
                            </ul>

                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="file">

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="list-attachments"></div>
                                        </div>
                                        <div class="col-sm-12">
                                            <small>Please only upload PDF files, other formats will not be viewable via Performance Hub.</small>
                                            <div id="agreementDZUploads" class="dropzone"></div>
                                        </div>
                                    </div>

                                </div>
                                <div role="tabpanel" class="tab-pane" id="html">

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <small>You may also create agreements, change logs / notes, etc using the Editor, or by directly entering HTML.</small>
                                        </div>
                                        <div class="col-sm-2">
                                            File Title:
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group form-group-no-margin">
                                                <div class="form-line">
                                                    <input class="form-control" id="agreementHtmlTitle" type="text" value="${
                                                        agreementData == undefined ||
                                                        agreementData.agreementHtmlTitle == undefined
                                                            ? ''
                                                            : agreementData.agreementHtmlTitle
                                                    }" placeholder="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div id="wysiwyg-editor"></div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </fieldset>

            </div>
        `;

        var agreementAttachments = [];
        let agreementObj = {};

        swalWithBootstrapButtons
            .fire({
                html: agreementsDialog,
                width: '90vw',
                showCloseButton: false,
                showCancelButton: true,
                allowEscapeKey: false,
                confirmButtonText: `${
                    agreementData == undefined || agreementData.agreementid == undefined ? 'Create' : 'Update'
                }`,
                cancelButtonText: `Cancel`,
                // heightMax: 300,
                allowOutsideClick: false,

                onBeforeOpen: () => {
                    // UI Code inside the actual SWAL element...
                    agreementTypeVisibility(agreementData?.agreementType);
                    onOrgChange();

                    // lJQ.AdminBSB.input.activate();
                    lJQ('[name=organisation-id]').selectpicker();
                    lJQ('[name=required-acknowledgement]').selectpicker();
                    lJQ('[name=individual-club]').selectpicker();
                    lJQ('[name=region]').selectpicker();
                    lJQ('[name=agreement-type]').selectpicker();

                    if (agreementData?.agreementType === 'club') {
                        lJQ('[name=individual-club]').selectpicker('val', agreementData.clubIds);
                    }

                    if (agreementData?.agreementType === 'region') {
                        lJQ('[name=region]').selectpicker('val', agreementData.regions);
                    }

                    lJQ('[name=agreement-type]').on('change', (e) => {
                        agreementTypeVisibility(e.target.value, true);
                    });

                    function agreementTypeVisibility(type, open = false) {
                        $('.individual-club-container').addClass('hidden');
                        $('.region-container').addClass('hidden');

                        if (type === 'club') {
                            $('.individual-club-container').removeClass('hidden');
                            open &&
                                setTimeout(
                                    () => $('.individual-club-container').find('.dropdown-toggle').trigger('click'),
                                    50
                                );
                        } else if (type === 'region') {
                            $('.region-container').removeClass('hidden');
                            open &&
                                setTimeout(() => $('.region-container').find('.dropdown-toggle').trigger('click'), 50);
                        }
                    }

                    function onOrgChange() {
                        const orgId = $('[name=organisation-id]').selectpicker('val');

                        const orgs = agreementsAdminAccess.orgs.filter(
                            (org) => orgId === 'global' || org.organisationId === orgId
                        );

                        const { facilities, regions } = orgs.reduce(
                            (acc, org) => {
                                org.facilities.forEach((f) => {
                                    if (acc.facilities.some((af) => af.id === f.id)) return;
                                    acc.facilities.push(f);
                                });

                                org.regions.forEach((r) => {
                                    if (acc.regions.some((ar) => ar.iso_code === r.iso_code)) return;
                                    acc.regions.push(r);
                                });

                                return acc;
                            },
                            { facilities: [], regions: [] }
                        );

                        $('[name=individual-club]').empty();
                        facilities.forEach((f) => {
                            $('[name=individual-club]').append(`<option value="${f.id}">${f.name}</option>`);
                        });

                        $('[name=region]').empty();
                        regions.forEach((r) => {
                            $('[name=region]').append(`<option value="${r.iso_code}">${r.full_name}</option>`);
                        });

                        $('[name=individual-club]').selectpicker('refresh');
                        $('[name=region]').selectpicker('refresh');
                    }

                    $('[name=organisation-id]').on('change', onOrgChange);

                    // Bind daterangepicker...
                    const displayDateRange = (id) => (date) => {
                        $(`${id} span`).html(date.format('MMMM D, YYYY'));
                    };

                    ['#dateDisplayed', '#dateEffective'].forEach((id) => {
                        const key = id.replace('#', '');
                        $(`${id} span`).html(moment().format('MMMM D, YYYY'));

                        const dateMoment = agreementData?.[key] ? moment(agreementData[key] * 1000) : moment();

                        renderDateRangePopup(id, dateMoment);
                        displayDateRange(id)(dateMoment.clone());

                        $(id).css('width', '100%');
                    });

                    function renderDateRangePopup(id, startDate = moment()) {
                        const dateRangeConfig = {
                            singleDatePicker: true,
                            showDropdowns: true,
                            startDate,
                            autoclose: true,
                            ranges: {
                                Today: [moment(), moment()],
                                Tomorrow: [moment().add(1, 'days'), moment().add(1, 'days')],
                                '+7 Days': [moment().add(7, 'days'), moment().add(7, 'days')],
                            },
                        };

                        $(id).attr('data-date', startDate.unix());
                        $(id).daterangepicker(dateRangeConfig, displayDateRange(id));
                        $(id).find('.daterangepicker').css('min-width', 'fit-content');

                        $(id).on('apply.daterangepicker', (e, picker) => {
                            $(id).data('date', picker.startDate.unix());
                        });
                    }

                    // Setup Dropzone within the new agreement dialog...
                    $('div#agreementDZUploads').dropzone({
                        url: '/api/admin/agreements/upload-attachment',
                        maxFilesize: 50, // MB
                        parallelUploads: 1,
                        acceptedFiles: 'application/pdf',
                        success: function (file, response) {
                            if (
                                response !== undefined &&
                                response.location !== undefined &&
                                response.originalname !== undefined
                            ) {
                                agreementAttachments.push({
                                    fileName: response.originalname,
                                    fileURL: response.location,
                                });
                                updateAgreementAttachmentsList();
                            }
                        },
                    });

                    $('#wysiwyg-editor').trumbowyg({
                        svgPath: '/assets/images/trumbowyg.svg',
                        // The text editing zone will extend itself when writing a long text
                        autogrow: true,
                        // Links should open in new tabs by default...
                        defaultLinkTarget: '_blank',
                        // Allow image upload in the image dropdown...
                        btnsDef: {
                            // Create a new dropdown
                            image: {
                                dropdown: ['insertImage', 'upload'],
                                ico: 'insertImage',
                            },
                        },
                        btns: [
                            ['viewHTML'],

                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-history
                            ['historyUndo', 'historyRedo'],

                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-fontfamily
                            ['fontfamily'],
                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-fontsize
                            ['fontsize'],

                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-lineheight
                            ['lineheight'],

                            ['formatting'],
                            ['removeformat'],

                            ['strong', 'em', 'del'],
                            ['superscript', 'subscript'],

                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-colors
                            ['foreColor', 'backColor'],

                            ['link', 'image'],
                            ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                            ['unorderedList', 'orderedList'],
                            ['horizontalRule'],

                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-table
                            'table',
                            // https://alex-d.github.io/Trumbowyg/demos/#plugins-upload
                            'upload',

                            ['fullscreen'],
                        ],
                        plugins: {
                            resizimg: {
                                minSize: 64,
                                step: 16,
                            },
                            upload: {
                                serverPath: '/api/admin/agreements/upload-attachment',
                                fileFieldName: 'file',
                                statusPropertyName: 'success',
                                urlPropertyName: 'location',
                            },
                        },
                    });

                    // If we're loading an existing agreement...
                    if (agreementData !== undefined && agreementData.agreementHtml !== undefined)
                        $('#wysiwyg-editor').trumbowyg('html', agreementData.agreementHtml);
                    if (agreementData !== undefined && agreementData.agreementAttachments !== undefined) {
                        agreementAttachments = agreementData.agreementAttachments;
                        updateAgreementAttachmentsList();
                    }
                },
                preConfirm: async () => {
                    agreementObj = {
                        title: $('#agreementTitle').val(),
                        summary: $('#agreementSummary').val(),
                        agreementHtmlTitle: $('#agreementHtmlTitle').val(),
                        dateEffective:
                            $('#dateEffective').data('date') === undefined ||
                            $('#dateEffective').data('date') == '' ||
                            $('#dateEffective').data('date') == null
                                ? moment().unix()
                                : Number($('#dateEffective').data('date')),
                        dateDisplayed:
                            $('#dateDisplayed').data('date') === undefined ||
                            $('#dateDisplayed').data('date') == '' ||
                            $('#dateDisplayed').data('date') == null
                                ? moment().unix()
                                : Number($('#dateDisplayed').data('date')),
                        agreementHtml: $('#wysiwyg-editor').trumbowyg('html'),
                        agreementAttachments: agreementAttachments,
                        agreementType: $('[name=agreement-type]').selectpicker('val'),
                        clubIds:
                            $('[name=agreement-type]').selectpicker('val') == 'club'
                                ? $('[name=individual-club]').selectpicker('val')
                                : [],
                        regions:
                            $('[name=agreement-type]').selectpicker('val') == 'region'
                                ? $('[name=region]').selectpicker('val')
                                : [],
                        requiredAcknowledgement: $('[name=required-acknowledgement]').selectpicker('val'),
                        organisationId: $('[name=organisation-id]').selectpicker('val'),
                    };

                    if (agreementObj.title == '')
                        return Swal.showValidationMessage('Please enter a title for this agreement.');

                    if (agreementObj.agreementType == '') return Swal.showValidationMessage('Please select a type.');

                    if (agreementObj.agreementType == 'region' && agreementObj.regions.length < 1)
                        return Swal.showValidationMessage('Please select at least one region.');

                    if (agreementObj.agreementType == 'club' && agreementObj.clubIds.length < 1)
                        return Swal.showValidationMessage('Please select at least one club.');

                    if (agreementObj.agreementHtml == '' && agreementObj.agreementAttachments.length < 1)
                        return Swal.showValidationMessage(
                            'Please enter some text or upload a file for this agreement.'
                        );

                    // If we're saving/updating an existing agreement, set the ID of the agreement we want to save/update...
                    if (agreementData !== undefined && agreementData.agreementid !== undefined)
                        agreementObj.agreementid = agreementData.agreementid;

                    const [ok] = await createAgreementAPI(agreementObj);

                    if (!ok) {
                        return Swal.showValidationMessage(
                            'An error occurred while trying to save this agreement. Please contact HQ.'
                        );
                    }

                    return true;
                },
            })
            .then((response) => {
                if (!response.value) return;

                instantNotification('Agreement Saved');
                getAllAgreements();
            });

        function updateAgreementAttachmentsList() {
            $('.new-agreement-dialog .list-attachments').html('<div class="list-group"></div>');

            agreementAttachments.forEach(function (attachment, item) {
                $('.new-agreement-dialog .list-attachments .list-group').append(`
                    <a target="_blank" href="${attachment.fileURL}" data-id="${item}" class="list-group-item list-group-item-action">
                        ${attachment.fileName}
                        <button type="button" class="btn btn-xs btn-danger btn-square delete-attachment"><i class="material-icons">delete</i></button>
                    </a>
                `);
            });

            $('.new-agreement-dialog .list-attachments .delete-attachment').off('click');
            $('.new-agreement-dialog .list-attachments .delete-attachment').click(function (e) {
                // Prevent opening hyperlink to attachment in new tab
                e.preventDefault();

                // Remove attachment from array & then re-render attachments...
                agreementAttachments.splice($(this).parent().data('id'), 1);
                updateAgreementAttachmentsList();
            });
        }
    }

    function getAllAgreements() {
        var req = $.ajax({
            url: '/api/admin/agreements/list-agreements',
            method: 'GET',
            contentType: 'application/json',
            success: function (data) {
                if (data === undefined ? false : data !== false) {
                    renderAgreementsLibrary(data);
                    // Add devices to UI...
                    if (data.length > 0) {
                        loadAgreementsOverview(data);
                    } else {
                        noAgreements();
                    }
                } else {
                    noAgreements();
                }
            },
            error: function (xhr, textStatus, errorThrown) {
                console.error(xhr, textStatus, errorThrown);
                noAgreements();
            },
        });

        function noAgreements() {
            $(`#agreements-container`).LoadingOverlay('hide');
            $('#overview').LoadingOverlay('hide');
        }

        function renderAgreementsLibrary(data) {
            const exportButtons = [
                { label: 'COPY', icon: 'fa-copy', onClick: () => table && table.button(0).trigger() },
                { label: 'PRINT', icon: 'fa-print', onClick: () => table && table.button(1).trigger() },
                { label: 'CSV', icon: 'fa-file-csv', onClick: () => table && table.button(2).trigger() },
                { label: 'PDF', icon: 'fa-file-pdf', onClick: () => table && table.button(3).trigger() },
            ];
            const buttons = [
                { label: 'New Agreement', icon: 'add', onClick: newAgreementModal },
                ...exportButtons,
            ];
            searchbarHeader('.agreements-library-nav', buttons, {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'agreements-export-buttons',
                },
            });

            let tableFilters = getQueryStringsObject();
            tableFilters['status-filter'] = tableFilters['status-filter'] ?? 'active';
            tableFilters['acknowledgement-filter'] = tableFilters['acknowledgement-filter'] ?? 'all';

            const filterGroups = [
                {
                    id: 'status-filter',
                    label: 'Status',
                    options: [
                        { label: 'All', value: 'all' },
                        { label: 'Active', value: 'active' },
                        { label: 'Archived', value: 'archived' },
                    ],
                },
                {
                    id: 'acknowledgement-filter',
                    label: 'Acknowledgement',
                    options: [
                        { label: 'All', value: 'all', selected: true },
                        { label: 'None', value: 'none' },
                        { label: 'Popup', value: 'popup' },
                        { label: 'Checkbox', value: 'checkbox' },
                        { label: 'Signature', value: 'signature' },
                    ],
                },
            ].map((group) => ({
                ...group,
                options: group.options.map((option) => ({
                    ...option,
                    selected: option.value === tableFilters[group.id],
                })),
            }));

            new PageFilters('.filters', filterGroups, (newFilters) => {
                tableFilters = { ...tableFilters, ...newFilters };
                handleTableFilters();
            });

            agreementsObj = {};

            $(`#agreements-container`).empty();
            $(`#agreements-container`).html(`
                 <table id="agreements-table" class="table" style="width: 100%;">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Summary</th>
                            <th>Effective From</th>
                            <th>Displayed On</th>
                            <th>Last Updated</th>
                            <th>Agreement ID</th>
                            <th class="agreement-attachments"></th>
                            <th class="agreement-tags"></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            `);

            data.forEach(function (agreement) {
                agreementsObj[agreement.agreementid] = agreement;

                let badges = '',
                    attachments = '';

                // Has attachments?
                if (agreement.agreementAttachments !== undefined && agreement.agreementAttachments.length != 0)
                    attachments += `<i class="material-icons">attach_file</i>`;

                const organisation =
                    agreement.organisationId === 'global'
                        ? 'Global'
                        : agreementsAdminAccess.orgs.find((org) => org.organisationId === agreement.organisationId)
                              ?.name;

                badges += `<span class="badge badge-secondary badge-blue">${organisation ?? 'No org'}</span>`;

                const agreementType =
                    agreement.agreementType === 'global' ? 'All Clubs' : camelize(agreement.agreementType);

                badges += `<span class="badge badge-secondary badge-green">${agreementType}</span>`;

                if (agreement.agreementType === 'club') {
                    agreement.clubIds?.forEach((clubId) => {
                        badges += `<span class="badge badge-blue">${clubId}</span>`;
                    });
                }

                if (agreement.agreementType === 'region') {
                    agreement.regions?.forEach((region) => {
                        badges += `<span class="badge badge-blue">${region}</span>`;
                    });
                }

                if (agreement.requiredAcknowledgement == 'signature') {
                    badges += `<span class="badge badge-yellow">Signature Required</span>`;
                } else if (agreement.requiredAcknowledgement == 'popup') {
                    badges += `<span class="badge badge-yellow">Popup Required</span>`;
                } else if (agreement.requiredAcknowledgement == 'checkbox') {
                    badges += `<span class="badge badge-yellow">Checkbox Required</span>`;
                }

                if (agreement.deleted) {
                    badges += `<span class="badge badge-red">Archived</span>`;
                }

                var agreementItem = `
                    <tr class="agreement" data-id="${agreement.agreementid}">
                        <td>${agreement.title}</td>
                        <td style="word-break: break-word;">${agreement.summary?.trunc(120) ?? ''}</td>
                        <td data-sort="${agreement.dateEffective}">
                            ${moment.unix(agreement.dateEffective).format('YYYY-MM-DD')}
                        </td>
                        <td data-sort="${agreement.dateDisplayed}">
                            ${moment.unix(agreement.dateDisplayed).format('YYYY-MM-DD')}
                        </td>
                        <td data-sort="${agreement.dateUpdated}">
                            ${moment.unix(agreement.dateUpdated).format('YYYY-MM-DD HH:mm A')}
                        </td>
                        <td>${agreement.agreementid}</td>
                        <td width="10" class="agreement-attachments">${attachments}</td>
                        <td class="agreement-tags">${badges}</td>
                        <td>${agreement.deleted ? 'archived' : 'active'}</td>
                        <td>${agreement.requiredAcknowledgement}</td>
                        <td style="width: 0%;">
                            <button class="btn btn-default archive-agreement" data-id="${agreement.agreementid}">
                                Archive
                            </button>
                        </td>
                    </tr>
                `;
                $(`#agreements-table tbody`).append(agreementItem);
            });

            const exportFileName = 'Contracts-and-Agreements__' + new Date().toISOString();
            const dtConfig = {
                responsive: true,
                dom: '<"toolbar">rtip',
                order: [[0, 'desc']],
                iDisplayLength: 50,
                columnDefs: [
                    { targets: [8, 9], visible: false },
                    { targets: 10, orderable: false },
                ],
                buttons: [
                    {
                        text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">Copy</span>',
                        extend: 'copy',
                        className: 'btn btn-sm btn-default',
                    },
                    {
                        text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">Print</span>',
                        extend: 'print',
                        title: 'Contracts & Agreements',
                        className: 'btn btn-sm btn-default',
                    },
                    {
                        text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">CSV</span>',
                        extend: 'csv',
                        className: 'btn btn-sm btn-default',
                        title: exportFileName,
                        extension: '.csv',
                    },
                    {
                        text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">PDF</span>',
                        extend: 'pdf',
                        className: 'btn btn-sm btn-default',
                        title: exportFileName,
                        extension: '.pdf',
                    },
                ],
            };

            table = $(`#agreements-table`).DataTable(dtConfig);
            table.on('draw', function () {
                bindAgreementEdit();
            });
            wrapTableWithScrollableDiv('#agreements-table');
            bindAgreementEdit();

            $(`#agreements-library .search-bar`).on('keyup', function () {
                table.search(this.value).draw();
            });

            $(`#agreements-library .archive-agreement`).on('click', (e) => {
                e.stopPropagation();
                const agreementId = $(e.target).data('id');
                const agreement = agreementsObj[agreementId];
                if (!agreement) return;
                confirmDeleteDialog(
                    'Archive Agreement',
                    'Club owners will no longer be required to acknowledge this agreement. Are you sure you want to continue?',
                    () => archiveAgreement(agreement),
                    'Proceed'
                );
            });

            function handleTableFilters() {
                if (!table) return;

                let statusFilter = tableFilters['status-filter'] ?? 'active';
                let acknowledgementFilter = tableFilters['acknowledgement-filter'] ?? 'all';

                if (acknowledgementFilter === 'all') acknowledgementFilter = '';
                if (statusFilter === 'all') statusFilter = '';

                table.column(8).search(statusFilter).draw();
                table.column(9).search(acknowledgementFilter).draw();
            }

            function archiveAgreement(agreement) {
                $.ajax({
                    url: '/api/admin/agreements/save',
                    method: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify({ ...agreement, deleted: true }),
                    success: function (data) {
                        instantNotification('Agreement Saved');
                        getAllAgreements();
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        console.error(xhr, textStatus, errorThrown);
                        instantNotification(
                            '<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to add/save your agreement to the system.<br>Please contact HQ for further details.',
                            'error',
                            60000
                        );
                    },
                });
            }

            function bindAgreementEdit() {
                // Unbind any old events first
                $(`#agreements-table tbody .agreement`).off('click');

                // Bind on message click to open message details...
                $(`#agreements-table tbody .agreement`).click(function () {
                    newAgreementModal(agreementsObj[$(this).data('id')]);
                });
            }

            // All done loading message history...
            $(`#agreements-container`).LoadingOverlay('hide');
            handleTableFilters();
        }
    }

    function camelize(str) {
        return str.replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, function (match, index) {
            if (+match === 0) return ''; // or if (/\s+/.test(match)) for white spaces
            return index === 0 ? match.toLowerCase() : match.toUpperCase();
        });
    }

    function agreemnentTypeSortValue({ agreementType }) {
        if (agreementType === 'global') return 3;
        if (agreementType === 'brand') return 2;
        if (agreementType === 'club') return 1;
        return 0;
    }

    function loadAgreementsOverview(data) {
        data = data || [];
        data = data.sort((a, b) => agreemnentTypeSortValue(b) - agreemnentTypeSortValue(a));
        const outstandingAgreements = [];
        const signedAgreements = [];

        data.forEach((agreement) => {
            $(`#overview .list-group-item[data-id=${agreement.agreementid}]`).off('click');

            if (agreement.missingSignatures.length > 0) {
                outstandingAgreements.push(agreement);
            }

            if (agreement.signatures.length > 0) {
                signedAgreements.push(agreement);
            }
        });

        $('#total-agreements').html(data.length);
        drawAgreementsChart(data);

        // Get total of outstanding signatures
        const totalOutstanding = outstandingAgreements.reduce(
            (acc, agreement) => acc + agreement.missingSignatures.length,
            0
        );
        $('#outstanding-total').html(totalOutstanding);

        // Get total of signed documents
        const totalSigned = signedAgreements.reduce((acc, agreement) => acc + agreement.signatures.length, 0);
        $('#signed-total').html(totalSigned);

        // Create outstanding agreements list
        $('#outstanding-agreements').html(
            outstandingAgreements.map((agreement) => getOverviewAgreement(agreement, 'unsigned')).join(' ')
        );

        // Create outstanding agreements list
        $('#signed-agreements').html(
            signedAgreements.map((agreement) => getOverviewAgreement(agreement, 'signed')).join(' ')
        );

        // bind click event
        data.forEach((agreement) => {
            $(`#overview #outstanding-agreements .list-group-item[data-id=${agreement.agreementid}]`).on('click', () =>
                overviewAgreementDialog(agreement, 'unsigned')
            );
        });

        // bind click event
        data.forEach((agreement) => {
            $(`#overview #signed-agreements .list-group-item[data-id=${agreement.agreementid}]`).on('click', () =>
                overviewAgreementDialog(agreement, 'signed')
            );
        });

        $('#overview').LoadingOverlay('hide');
    }

    function getOverviewAgreement(
        { agreementid, agreementType, title, missingSignatures, signatures, ...agreement },
        type
    ) {
        const isSigned = type === 'signed';

        let clubs = '';

        if (isSigned) {
            clubs = signatures.length > 0 ? `${signatures[0].club}` : '';
            clubs += signatures.length > 1 ? ` and ${signatures.length - 1} other` : '';
            clubs += signatures.length > 2 ? 's' : '';
        } else {
            clubs = missingSignatures.length > 0 ? `${missingSignatures[0].clubName}` : '';
            clubs +=
                missingSignatures.length > 1
                    ? ` and <b style='color:red;'>${missingSignatures.length - 1} other</b>`
                    : '';
            clubs += missingSignatures.length > 2 ? `<b style='color:red;'>s</b>` : '';
        }

        const date = moment((!isSigned ? agreement.dateEffective : agreement.dateUpdated) * 1000);
        const isOverDue = date.isBefore(moment());
        const diffDays = moment().diff(date, 'days');

        const dueDate =
            diffDays > 1
                ? `Due ${diffDays} days ago <i>(${date.format('YYYY-MM-DD')}</i>)`
                : diffDays === 1
                ? `Due Yesterday <i>(${date.format('YYYY-MM-DD')}</i>)`
                : diffDays === 0
                ? `Due Today`
                : diffDays === -1
                ? `Due Tomorrow <i>(${date.format('YYYY-MM-DD')}</i>)`
                : `Due in ${Math.abs(diffDays)} days <i>(${date.format('YYYY-MM-DD')}</i>)`;

        return `
            <a href="#" class="list-group-item list-group-item-action" data-id="${agreementid}">
                <div>
                    <div class="title">${title}</div>
                    ${clubs}
                </div>
                <span class="text-right">
                    <div
                        class="${!isSigned && isOverDue ? 'text-danger' : ''}"
                        title="${!isSigned ? 'Overdue!' : ''}"
                    >
                        ${dueDate}
                    </div>
                    <span class="badge agreement-type">${agreementType}</span>
                </span>
            </a>
        `;
    }

    const overviewAgreementDialogHeader = ({ missingSignatures, signatures }, type = 'signed') => {
        const total = signatures.length + missingSignatures.length;
        const title = type === 'signed' ? 'Signed Agreements' : 'Unsigned Agreements';

        return `
            <div class="d-flex align-center justify-between">
                <div>
                    <h4 style="display: inline; margin-right: 1rem;">
                        ${title}
                    </h4>
                    <span>${type === 'signed' ? signatures.length : missingSignatures.length}/${total}</span>
                </div>
                <div class="icon-search search-bar-wrapper">
                    <div class="input-group" style="margin: 0">
                        <span class="input-group-addon">
                            <i class="material-icons">search</i>
                        </span>
                        <div class="form-line">
                            <input type="text" class="form-control dialog-search-bar" placeholder="Search">
                        </div>
                    </div>
                </div>
            </div>
        `;
    };

    function overviewAgreementDialog(agreement, type = 'signed') {
        const agreementSigned = type === 'signed';
        const html = document.createElement('div');

        if (!agreementSigned) {
            $(html).append(`
                <div>
                    ${overviewAgreementDialogHeader(agreement, type)}
                    <ul class="list-group dialog-list" style="max-height: 50vh; overflow-y: auto;">
                        ${agreement.missingSignatures
                            .sort((a, b) => a.clubName.localeCompare(b.clubName))
                            .map((signature) => {
                                return `
                                    <li class="list-group-item" data-search="${signature.clubName} ${signature.clubBrand}">
                                        <div class="d-flex align-center justify-between">
                                            <span>${signature.clubName}</span>
                                            <span class="badge agreement-type">${signature.clubBrand}</span>
                                        </div>
                                    </li>
                                `;
                            })
                            .join('')}
                    </ul>
                </div>
            `);
        } else {
            $(html).append(`
                <div>
                    ${overviewAgreementDialogHeader(agreement, type)}
                    <div class="list-group dialog-list" style="max-height: 50vh; overflow-y: auto;">
                        ${agreement.signatures
                            .sort((a, b) => a.club.localeCompare(b.club))
                            .map((signature) => {
                                return `
                                    <a 
                                        class="list-group-item list-group-item-action"
                                        data-toggle="collapse"
                                        href="#collapse-${agreement.agreementid}-${signature.club}"
                                        data-search="${signature.clubName} ${signature.clubBrand}"
                                    >
                                        <div class="d-flex align-center justify-between">
                                            <span>${signature.clubName}</span>
                                            <span class="badge agreement-type">${signature.clubBrand}</span>
                                        </div>
                                        <div
                                            id="collapse-${agreement.agreementid}-${signature.club}"
                                            class="panel-collapse collapse"
                                        >
                                            <div class="panel-body" style="padding: 0; padding-top: 1rem;">
                                                <iframe data-hj-allow-iframe="" class="agreement-iframe" style="width: 100%; height: 350px;"></iframe>
                                            </div>
                                        </div>
                                    </a>
                                `;
                            })
                            .join('')}
                    </div>
                </div>
            `);
        }

        swalWithBootstrapButtons.fire({
            html,
            showCloseButton: false,
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: 'Back',
            onBeforeOpen: () => {
                // Unbind key up
                $('.dialog-search-bar').off('keyup');
                $('.dialog-search-bar').on('keyup', (e) => {
                    const value = $(e.currentTarget).val();
                    const regex = new RegExp(`(.*)${value}(.*)`, 'i');
                    let hasOpen = false;

                    $('.dialog-list > a, .dialog-list > li').map((_, item) => {
                        const { search } = $(item).data();
                        const isExpanded = $(item).attr('aria-expanded') === 'true';

                        if (regex.test(search)) {
                            $(item).show();

                            if (isExpanded && !hasOpen) {
                                hasOpen = true;
                            } else if (!isExpanded && !hasOpen) {
                                $(item).trigger('click');
                                hasOpen = true;
                            } else if (isExpanded) {
                                $(item).trigger('click');
                            }
                        } else {
                            $(item).hide();

                            if (isExpanded) {
                                $(item).trigger('click');
                            }
                        }
                    });
                });

                // Trigger search once
                $('.dialog-search-bar').trigger('keyup');

                // Setup document log
                if (agreement.requiredAcknowledgement !== 'none' && agreementSigned) {
                    agreement.signatures.forEach((signature) => {
                        const docLogFrame = $(`#collapse-${agreement.agreementid}-${signature.club} .agreement-iframe`);
                        docLogFrame.ready(function () {
                            const title = !agreement?.title ? 'Untitled Agreement' : agreement.title;
                            const signedOn = moment(signature.signedDate * 1000).format('DD MMM YYYY');

                            const signedByText =
                                agreement.requiredAcknowledgement === 'signature'
                                    ? `${t('Signed By')}:               ${signature.signedBy}\n`
                                    : '';

                            const signatureHtml =
                                agreement.requiredAcknowledgement === 'signature'
                                    ? `${t('Signature')}:\n<img src="${signature.signatureData}">`
                                    : '';

                            docLogFrame
                                .contents()
                                .find('body')
                                .append(
                                    dedent(`
                                        <h3>Document Log: &quot;${title}&quot;</h3>
                                        <hr>
                                        <pre>
                                        ${signedByText}
                                        ${t('On Behalf Of')}:            ${signature.club}\n
                                        ${t('Acknowledged On')}:         ${signedOn}\n
                                        ${t('Performance Hub Account')}: ${signature.signedEmail}\n
                                        ${t('Logged IP Address')}:       ${signature.signedIP}\n
                                        ${signatureHtml}
                                        </pre>
                                    `)
                                );
                        });
                    });
                }
            },
        });
    }

    function drawAgreementsChart(data) {
        $('#agreements-chart').html('');
        agreementsChart && agreementsChart.destroy();

        const chartData = {
            GLOBAL: 0,
            CLUB: 0,
            REGION: 0,
        };

        data.forEach((agreement) => {
            chartData[agreement.agreementType.toUpperCase()] += 1;
        });

        const chartEl = $('#agreements-chart');
        const hoverLabel = {
            id: 'hoverLabel',
            afterDraw(chart, args, options) {
                const {
                    ctx,
                    chartArea: { width, height },
                } = chart;
                ctx.save();

                const isActive = chart._active.length > 0;
                const textLabel = isActive ? chart.config.data.labels[chart._active[0].index] : `Club`;
                const memberValue = isActive
                    ? `${chart.config.data.datasets[0].data[chart._active[0].index]}`
                    : `${chartData['CLUB']}`;
                const fillColor = isActive
                    ? chart.config.data.datasets[0].borderColor[chart._active[0].index]
                    : 'rgb(33, 150, 243)';

                (ctx.font = '16px Arial'), (ctx.fillStyle = fillColor);
                ctx.textAlign = 'center';
                ctx.fillText(memberValue, width / 2, height / 1.6);

                ctx.font = 'bold 18px Arial';
                ctx.fillStyle = 'black';
                ctx.fillText(textLabel, width / 2, height / 2.1);
            },
        };
        const checkActive = Chart.getChart('member_split_chart');
        if (checkActive !== undefined) {
            checkActive.destroy();
        }
        agreementsChart = new Chart(chartEl, {
            type: 'doughnut',
            data: {
                labels: ['GLOBAL', 'Club', 'Region'],
                datasets: [
                    {
                        data: [chartData['GLOBAL'], chartData['CLUB'], chartData['REGION']],
                        backgroundColor: [
                            'rgba(188, 131, 238, 0.2)',
                            'rgba(255, 106, 183, 0.2)',
                            'rgba(255, 118, 104, 0.2)',
                            'rgba(33, 150, 243, 0.2)',
                        ],
                        borderColor: [
                            'rgb(188, 131, 238)',
                            'rgb(255, 106, 183)',
                            'rgb(255, 118, 104)',
                            'rgb(33, 150, 243)',
                        ],
                        borderWidth: 1,
                        cutout: '70%',
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                    },
                    title: {
                        display: false,
                    },
                    tooltip: {
                        enabled: false,
                    },
                },
            },
            plugins: [hoverLabel],
        });
    }
}

const renderAdminAgreementList = (data, clubId = null) => {
    if (data && data.length) {
        $('#empty-admin-agreement-data').empty();
        $('#default-agreement-table tbody').empty();
        data.forEach((tna, index) => {
            $('#default-agreement-table tbody').append(getAdminAgreementRow(index, tna));
        });
    } else {
        $('#default-agreement-table tbody').empty();
        $('#empty-admin-agreement-data').empty();
        $('#list-agreement-container').append(
            '<div id="empty-admin-agreement-data" style="text-align: center;">' +
                'There are no agreements for now.' +
                '</div>'
        );
    }

    // show create agreement form
    $('#admin-user-agreement-form').on('click', function () {
        showAgreementForm();
    });

    // show edit agreement form
    $('#default-agreement-table tbody tr .onEdit').on('click', function () {
        showAgreementForm(null, { id: $(this).data('editagrid') });
    });

    // show confirm delete
    $('#default-agreement-table tbody tr .onDeleteAgreement').on('click', function () {
        const forAdminDelete = data.find((d) => d.id == $(this).data('deleteagrid'));
        if (!forAdminDelete) return;

        const html = `<p>Are you sure you want to delete <strong>${forAdminDelete.name}</strong> agreement?</p>`;
        confirmDeleteDialog('Delete Agreement', html, async () => {
            await deleteAdminAgreement(clubId, forAdminDelete.id, {
                name: forAdminDelete.name,
                content: forAdminDelete.content,
            });
            await fetchAdminAgreements(renderAdminAgreementList, clubId);
        });
    });

    // view agreement
    $('#default-agreement-table tbody tr .view').on('click', function () {
        const forAdminView = data.find((d) => d.id == $(this).data('agreementid'));
        if (forAdminView) {
            const agreementContainer = document.createElement('div');
            $(agreementContainer).html(`<div style="text-align: left; margin: 0 15px;">${forAdminView.content}</div>`);

            swalWithBootstrapButtons.fire({
                title: forAdminView.name,
                html: agreementContainer,
                showCloseButton: true,
                animation: false,
                width: '500px',
                showCancelButton: false,
                showConfirmButton: false,
                allowEscapeKey: false,
            });
        }
    });
};

const parseAgreementRegionsString = (region) => {
    if (!region) return [];
    if (region === 'global') return [region];

    return region.split(',').map((regionString) => {
        const parsedRegionString = regionString.split('|');
        return parsedRegionString.length > 1 ? parsedRegionString[1] : regionString.capitalize();
    });
};

// human display of region name
const displayRegionName = (region) => {
    if (region === 'global') return region.capitalize();
    if (region) {
        let regionNames = [];
        let regions = region.split(',');
        regions.forEach((reg) => {
            regionNames.push(reg.split('|').length > 1 ? reg.split('|')[1] : reg.capitalize());
        });

        return regionNames.join(', ');
    }

    return '';
};

/** agreement - row **/
const getAdminAgreementRow = (index, row) => {
    return `
        <tr
            data-index="${index}"
            data-created="${row.created_at}"
            data-name="${row.name}"
            data-agreementid="${row.id}"
        >
            <td class="view" data-agreementid="${row.id}">${row.name ? row.id : ''}</td>
            <td class="view" data-agreementid="${row.id}">${row.name ? row.name : ''}</td>
            <td class="view" data-agreementid="${row.id}">${row.type}</td>
            <td class="view" data-agreementid="${row.id}">${displayRegionName(row.region)}</td>
            <td class="view min text-center" data-agreementid="${row.id}">${row.template ? 'yes' : 'no'}</td>
            <td class="view" data-agreementid="${row.id}">
                ${
                    'created_at' in row && row.created_at !== 'undefined' && row.created_at
                        ? unixTimeFormat(row.created_at)
                        : ''
                }
            </td>
            <td>
                <div class="text-center">
                    <button type="button" data-editagrid="${row.id}" class="btn btn-info onEdit" ${
        row.hasUsers ? 'disabled' : ''
    }>Edit</button>
                    <button type="button" data-deleteagrid="${row.id}" class="btn btn-default onDeleteAgreement" ${
        row.hasUsers ? 'disabled' : ''
    }>Delete</button>
                </div>
            </td>
        /tr>
    `;
};

const showAgreementForm = async (clubId = '', inData = null) => {
    let agreementId = 'INVALID-ID';
    let mode = 'Create';
    let editData = null;

    // dropdown options html only
    const buildDropdownRegionOptions = () => {
        const regions = [{ iso_code: 'global', full_name: 'Global' }, ...agreementsAdminAccess.allRegions];

        if (regions.length < 1) {
            return `<option value="">No region available</option>`;
        }

        return regions.map((cl) => {
            const selected = editData?.regions?.includes(cl.iso_code) ? 'selected' : '';
            return `<option value="${cl.iso_code}" ${selected}>${cl.full_name}</option>`;
        });
    };

    if (inData && 'id' in inData) {
        mode = 'Edit';
        agreementId = inData.id;
    }

    const agreementDialog = `
        <fieldset>
            <form>
                <div class="form-group">
                    <label for="adminAgrTitle">Title</label>
                    <input type="text" class="form-control" id="adminAgrTitle" name="name" placeholder="Enter title" required>
                    <em class="text-danger text-sm"></em>
                </div>

                <div id="radio-purpose-container" class="form-group" style="margin-bottom: 0px;">
                    <div>
                        <input type="radio" name="type" value="editable" id="editable-template" class="with-gap radio-col-blue" checked >
                        <label for="editable-template">
                            <div style="font-size: 12px;">Editable Template</div>
                        </label>
                        <i>(Templates are used by clubs to create a tailored Waiver or Agreement specific to their club.)</i>
                    </div>
                    <div>
                        <input type="radio" name="type" value="pre-filled" id="region-agreement" class="with-gap radio-col-blue">
                        <label for="region-agreement">
                            <span style="font-size: 12px;">
                                Default Pre-filled <span id="text-document">Waiver</span>
                            </span>
                        </label>
                        <i>(A Default Waiver|Agreement is used by a club if they do not have any agreements created based on a template.)</i>
                    </div>
                </div>
                <div class="form-group d-flex gap-1 align-center" style="padding-left: 50px; margin-bottom: 1rem;">
                    <label for="adminTemplateOrgs">Organisations</label>
                    <div style="max-width: 300px; width: 100%;">
                        <select
                            id="adminTemplateOrgs"
                            name="organisations"
                            class="form-control show-tick selectpicker"
                            multiple
                            required
                        >
                        </select>
                        <em class="text-danger text-sm"></em>
                    </div>
                </div>
                <div class="form-group d-flex gap-1 align-center" style="padding-left: 50px;">
                    <label for="adminTemplateRegion" style="padding-right: 42px;">Region</label>
                    <div style="max-width: 300px; width: 100%;">
                        <select
                            id="adminTemplateRegion"
                            name="regions"
                            class="form-control show-tick selectpicker"
                            multiple
                            required
                        >
                        </select>
                        <em class="text-danger text-sm"></em>
                    </div>
                </div>

                <div class="form-group">
                    <label for="adminAgreementContentEditor" style="margin: 0px;">Content</label>
                    <p style="line-height: 1;"><small>You can refer to Facility Variables by enclosing them in double braces, such as <code>{{ClubName}}</code>, as listed on the <a target="_blank" href="/facility-variables">Facility Variables</a> page.</small></p>
                    <div id="adminAgreementContentEditor" style="min-height: 250px;"></div>
                    <em id="content-error" class="text-danger text-sm"></em>
                </div>
            </form>
        </fieldset>
    `;

    const formContainer = document.createElement('div');
    $(formContainer).addClass('text-left create-agreement-dialog');
    $(formContainer).html(agreementDialog);
    $(formContainer).append(swalActionButtons(handleSubmit));

    function validateForm() {
        const data = getSerializedObjectFromForm($(formContainer).find('form'));

        if (!data.name) {
            $('#adminAgrTitle').siblings('em').text('Title is required');
            return false;
        } else {
            $('#adminAgrTitle').siblings('em').text('');
        }

        if (data.type === 'pre-filled' && !data.regions) {
            $('#adminTemplateRegion').closest('div').siblings('em').text('Region is required');
            return false;
        } else {
            $('#adminTemplateRegion').closest('div').siblings('em').text('');
        }

        if (!data.adminAgreementContentEditor) {
            $('#content-error').text('Content is required');
            return false;
        } else {
            $('#content-error').text('');
        }

        return true;
    }

    async function handleSubmit() {
        if (!validateForm()) return;
        const data = getSerializedObjectFromForm($(formContainer).find('form'));

        const dataParams = {
            ...DEFAULT_CLUB_USER_AGR,
            name: data.name,
            content: data.adminAgreementContentEditor,
            type: 'waiver',
            template: data.type === 'editable',
            region:
                data.type !== 'pre-filled'
                    ? ''
                    : typeof data.regions === 'string'
                    ? data.regions
                    : typeof data.regions === 'object'
                    ? data.regions.join(',')
                    : '',
            organisationIds:
                typeof data.organisations === 'string'
                    ? [data.organisations]
                    : Array.isArray(data.organisations)
                    ? data.organisations
                    : [],
        };

        if (mode == 'Edit') {
            await updateAdminAgreement(clubId, agreementId, dataParams);
        } else {
            await createAdminAgreement(clubId, dataParams);
        }

        Swal.close();

        await fetchAdminAgreements(renderAdminAgreementList, clubId);
    }

    // agreement form
    swalWithBootstrapButtons.fire({
        title: `${mode} Document`,
        html: formContainer,
        showConfirmButton: false,
        allowEscapeKey: false,
        allowOutsideClick: false,
        width: '800px',
        onBeforeOpen: async () => {
            // get info if edit mode
            if (mode == 'Edit') {
                loadingOverlay(formContainer);
                $('.save-btn').prop('disabled', true);
                const agreement = await apiAdminCall(`/api/admin/club-user-agreements/${agreementId}`);
                if (
                    agreement &&
                    Object.keys(agreement).length &&
                    'status' in agreement &&
                    agreement.status === 'success'
                ) {
                    editData = agreement.data;
                }
                loadingOverlay(formContainer, false);
                $('.save-btn').prop('disabled', false);
            }

            const orgOptions =
                agreementsAdminAccess.orgs?.map((org) => ({
                    label: org.name,
                    value: org.organisationId,
                    selected: editData?.organisationIds?.includes(org.organisationId),
                })) || [];

            orgOptions.forEach((org) => {
                $('#adminTemplateOrgs').append(
                    `<option value="${org.value}" ${org.selected ? 'selected' : ''}>${org.label}</option>`
                );
            });

            $('#adminTemplateOrgs').selectpicker();

            $('#adminTemplateRegion').empty().append(buildDropdownRegionOptions());
            $('#adminTemplateRegion').selectpicker();

            $('#adminAgreementContentEditor').trumbowyg({
                svgPath: '/assets/images/trumbowyg.svg',
                btns: [
                    ['viewHTML'],
                    ['formatting'],
                    ['strong', 'em', 'del'],
                    ['superscript', 'subscript'],
                    ['link'],
                    ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                    ['unorderedList', 'orderedList'],
                    ['horizontalRule'],
                    ['removeformat'],
                ],
            });

            if (editData) {
                $('#adminAgrTitle').val(editData.name);
                $('#adminAgreementContentEditor').trumbowyg('html', editData.content);

                $('#adminTemplateRegion').val((editData.region || '').split(','));
                $('#adminTemplateRegion').selectpicker('refresh');

                $('#editable-template').prop('checked', editData.template);
                $('#region-agreement').prop('checked', !editData.template);
            }
        },
    });
};

const renderAdminClubAgreementUsed = (data) => {
    const setEmptyHtml = () => {
        $('#club-list-config-agreement tbody').empty();
        $('#empty-admin-agreement-config').empty();
        $('#club-variations-container').append(
            '<div id="empty-admin-agreement-config" style="text-align: center;">' +
                'There are no club configured agreements for now.' +
                '</div>'
        );
    };

    if (data && data.length) {
        $('#empty-admin-agreement-config').empty();
        $('#club-list-config-agreement tbody').empty();
        let dataConfig = [];
        data.forEach((tna) => {
            const foundAdminTna = adminAgreementList.find((agr) => agr.id === tna.templateId);
            if (foundAdminTna) {
                dataConfig.push(tna);
            }
        });
        if (dataConfig.length) {
            dataConfig.forEach((conf, index) => {
                $('#club-list-config-agreement tbody').append(getAdminAgreementConfigRow(index, conf));
            });
        } else {
            setEmptyHtml();
        }
    } else {
        setEmptyHtml();
    }
};

// the club variation row
const getAdminAgreementConfigRow = (index, row) => {
    const getAgreementProp = (agreementId, prop = 'name') => {
        const fa = adminAgreementList.find((agr) => agr.id === agreementId);
        if (fa) {
            return fa[prop];
        }
        return '';
    };

    return (
        '<tr' +
        ` data-index="${index}"` +
        ` data-created="${row.created_at}"` +
        ` data-agreementid="${row.id}"` +
        '>' +
        `<td class="view" data-agreementid="${row.id}">${row.clubId ? row.clubId : ''}</td>` +
        `<td class="view" data-agreementid="${row.id}">${row.name}</td>` +
        `<td class="view" data-agreementid="${row.id}">${getAgreementProp(row.templateId)}</td>` +
        `<td class="view" data-agreementid="${row.id}">${
            'created_at' in row && row.created_at !== 'undefined' && row.created_at
                ? unixTimeFormat(row.created_at)
                : ''
        }</td>` +
        `<td class="view" data-agreementid="${row.id}">${row.region ? displayRegionName(row.region) : ''}</td>` +
        '</tr>'
    );
};

const fetchAdminAgreements = async (cb) => {
    loadingOverlay('#club-user-default-agreements');
    const result = await apiAdminCall(`/api/admin/club-user-agreements?createdByAdmin=true`);
    if (result && 'status' in result && result.status == 'success') {
        adminAgreementList = result.data;
        cb(result.data);
    }
    $('#club-user-default-agreements').LoadingOverlay('hide');
};

const createAdminAgreement = async (clubId, dataParams) => {
    loadingOverlay('#club-user-default-agreements');
    const result = await apiAdminCall(`/api/admin/club-user-agreements?organisationId=${selectedOrgID}`, 'POST', {
        ...dataParams,
    });
    $('#club-user-default-agreements').LoadingOverlay('hide');
    if (result && 'status' in result && result.status == 'success') {
        return true;
    }
    return false;
};

const updateAdminAgreement = async (clubId, id, dataParams) => {
    loadingOverlay('#club-user-default-agreements');
    const result = await apiAdminCall(
        `/api/admin/club-user-agreements/${id}?organisationId=${selectedOrgID}`,
        'PATCH',
        { ...dataParams, id }
    );
    $('#club-user-default-agreements').LoadingOverlay('hide');
    if (result && 'status' in result && result.status == 'success') {
        return true;
    }
    return false;
};

const deleteAdminAgreement = async (clubId, id, dataParams) => {
    loadingOverlay('#club-user-default-agreements');
    const result = await apiAdminCall(
        `/api/admin/club-user-agreements/${id}?organisationId=${selectedOrgID}`,
        'DELETE',
        { ...dataParams, clubId }
    );
    $('#club-user-default-agreements').LoadingOverlay('hide');
    if (result && 'status' in result && result.status == 'success') {
        return true;
    }
    return false;
};

const fetchClubUsedAgreement = async (cb) => {
    let result = await apiAdminCall(`/api/admin/club-agreement-variation?organisationId=${selectedOrgID}`);
    if (result && 'status' in result && result.status == 'success') {
        cb(result.data);
    }
};

const apiAdminCall = (url, verb = 'GET', dataObj = null) => {
    const promise = new Promise((resolve, reject) => {
        let config = {
            url,
            method: verb,
            headers: {
                Accept: 'application/json',
            },
        };

        if (dataObj) {
            config = {
                ...config,
                contentType: 'application/json',
                data: JSON.stringify(dataObj),
            };
        }

        $.ajax({
            ...config,
            success: (result) => {
                resolve({ status: 'success', data: result });
            },
            error: (xhr, textStatus, errorThrown) => {
                console.error(xhr, textStatus, errorThrown);
                reject({ status: 'fail', data: errorThrown });
            },
        });
    });

    return promise.then((response) => response).catch((error) => error);
};
