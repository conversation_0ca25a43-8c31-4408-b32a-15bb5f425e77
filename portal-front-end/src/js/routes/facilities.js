let adminFacilitiesTable = null;

async function sFacilitiesManagement() {
    const root = $('#admin-portal #facilities');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');

    const filters = {
        organisations: [],
        brands: [],
        regions: [],
        status: [
            { label: t('Published'), value: 'published' },
            { label: t('Unpublished'), value: 'unpublished' },
        ],
    };

    const query = getQueryStringsObject();
    
    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('ADMIN_FILTERS_FACILITIES');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }
    
    // Use query params first, then localStorage, then defaults
    let selectedFilters = {
        organisations: query.organisations || savedFilters.organisations || 'all',
        brands: query.brands || savedFilters.brands || 'all',
        regions: query.regions || savedFilters.regions || 'all',
        status: query.status || savedFilters.status || 'all',
        search: query.search || savedFilters.search || '',
    };

    let facilities = [];

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const [countriesRes, organisationsRes, userRes] = await Promise.all([
            listAllCountriesAPI(),
            listOrganisationsAPI(['brands']),
            listAuthorisedAPI(),
        ]);

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!countriesRes[0] || !organisationsRes[0] || !userRes[0]) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        facilities = userRes[1].clubs || [];
        countries = countriesRes[1];

        filters.organisations = organisationsRes[1].map((org) => ({
            label: org.name,
            value: org.organisationId,
            brands: org.brands,
        }));

        filters.regions = facilities.reduce((acc, facility) => {
            const country = facility.localisation?.country;
            if (!country?.iso_code || !country.full_name) return acc;
            if (acc.some((r) => r.value === country.full_name)) return acc;
            return [...acc, { label: country.full_name, value: country.full_name }];
        }, []);

        updateBrandsFilter('all');

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => adminFacilitiesTable && adminFacilitiesTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => adminFacilitiesTable && adminFacilitiesTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => adminFacilitiesTable && adminFacilitiesTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => adminFacilitiesTable && adminFacilitiesTable.button(3).trigger() },
        ];
        const buttons = [
            { label: 'Add Facility', icon: 'fa-shop', onClick: newFacilityDialog },
            ...exportButtons,
        ];
        searchbarHeader(
            searchbarContainer,
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'facilities-export-buttons',
                },
            }
        );

        root.find('.search-bar').val(selectedFilters.search);
        renderPageFilters();

        root.find('.search-bar').on('input', function () {
            selectedFilters.search = $(this).val();
            updateHrefQuery({ search: selectedFilters.search });
            
            // Save search filter to localStorage
            try {
                localStorage.setItem('ADMIN_FILTERS_FACILITIES', JSON.stringify(selectedFilters));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }
            
            renderFacilitiesTable();
        });

        renderFacilitiesTable();
    }

    function renderPageFilters() {
        filtersContainer.html('');

        new PageFilters(
            filtersContainer,
            Object.keys(filters).map((key) => ({
                id: key,
                label: key.charAt(0).toUpperCase() + key.slice(1),
                options: [
                    { label: 'All', value: 'all', selected: selectedFilters[key] === 'all' },
                    ...filters[key]
                        .map((option) => ({
                            label: option.label,
                            value: option.value,
                            selected: selectedFilters[key] === option.value,
                        }))
                        .sort((a, b) => a.label.localeCompare(b.label)),
                ],
            })),
            (newFilters) => {
                selectedFilters = { ...selectedFilters, ...newFilters };

                if (selectedFilters.organisations) {
                    updateBrandsFilter(selectedFilters.organisations);
                    renderPageFilters();
                }

                Object.keys(selectedFilters).forEach((key) => {
                    updateHrefQuery({ [key]: selectedFilters[key] === 'all' ? null : selectedFilters[key] });
                });
                
                // Save filters to localStorage
                try {
                    localStorage.setItem('ADMIN_FILTERS_FACILITIES', JSON.stringify(selectedFilters));
                } catch (error) {
                    console.error('Error saving filters to localStorage:', error);
                }

                renderFacilitiesTable();
            },
            true
        );
    }

    function updateBrandsFilter(orgId) {
        const orgs = filters.organisations.filter((org) => orgId === 'all' || org.value === orgId);

        const brands = orgs.reduce((acc, org) => {
            org.brands.forEach((brand) => {
                if (!acc.some((b) => b.value === brand.brandId)) {
                    acc.push({ label: brand.name, value: brand.brandId });
                }
            });
            return acc;
        }, []);

        if (!brands.some((b) => b.value === selectedFilters.brands)) {
            selectedFilters.brands = 'all';
        }

        filters.brands = brands;
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>
            <div class="d-flex gap-1 my-4">
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 7%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 13%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                </div>
            `);
        });
    }

    async function renderFacilitiesTable() {
        let export_filename = 'Facilities__' + (new Date().toISOString());
        adminFacilitiesTable?.clear();
        adminFacilitiesTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('Facility')}</th>
                        <th>${t('Organisation')}</th>
                        <th>${t('Brand')}</th>
                        <th>${t('Region')}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        facilities
            .filter((facility) => {
                const searchRegex = new RegExp(selectedFilters.search, 'igm');
                const searchMatch =
                    facility.id.match(searchRegex) ||
                    facility.name?.match(searchRegex) ||
                    facility.organisation?.match(searchRegex) ||
                    facility.brand?.match(searchRegex) ||
                    facility.localisation?.country?.full_name?.match(searchRegex);

                if (!searchMatch) return false;

                const selectedOrg = filters.organisations.find((org) => org.value === selectedFilters.organisations);
                const orgMatch = !selectedOrg || facility.organisation === selectedOrg.label;
                if (!orgMatch) return false;

                const selectedBrand = filters.brands.find((org) => org.value === selectedFilters.brands);
                const brandMatch = !selectedBrand || facility.brand === selectedBrand.label;
                if (!brandMatch) return false;

                const selectedRegion = filters.regions.find((org) => org.value === selectedFilters.regions);
                const country = facility.localisation?.country?.full_name;
                const regionMatch = !selectedRegion || country === selectedRegion.value;
                if (!regionMatch) return false;

                const publishedMatch =
                    selectedFilters.status === 'all' ||
                    (selectedFilters.status === 'published' && facility.clubPublished) ||
                    (selectedFilters.status === 'unpublished' && !facility.clubPublished);
                if (!publishedMatch) return false;

                return true;
            })
            .forEach((facility) => {
                const publishedBadge = facility.clubPublished
                    ? `<span class="ph-badge badge-blue">${t('Published')}</span>`
                    : `<span class="ph-badge">${t('Unpublished')}</span>`;

                tableBodyEl.append(`
                    <tr data-id="${facility.id}">
                        <td>${facility.name}</td>
                        <td>${
                            facility.organisation ||
                            '<span class="text-muted">No Organisation</span>'
                        }</td>
                        <td>${facility.brand || '<span class="text-muted">No Brand</span>'}</td>
                        <td>${
                            facility.localisation.country?.full_name ||
                            '<span class="text-muted">No Region</span>'
                        }</td>
                        <td style="text-align: right;">${publishedBadge}</td>
                    </tr>
                `);
            });

        adminFacilitiesTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [[0, 'asc']],
            autoWidth: false,
            iDisplayLength: 50,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Facilities',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf'
                },
            ],
            columnDefs: [{ targets: 4, width: '0%', sortable: false }],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const facilityId = $(this).data('id');
                    const facility = facilities.find((f) => f.id === facilityId);
                    if (!facility) return;
                    manageFacilityDialog(facility);
                });
            },
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));
    }

    function showFacilityCreationSuccessModal(facility) {
        swalWithBootstrapButtons.fire({
            title: t('Facility Created Successfully!'),
            html: `
                <div class="ph-dialog-v2">
                    <div class="text-center ph-text my-8 mx-2 d-flex flex-column align-center">
                        <div class="ph-alert ph-alert_success mb-3">
                            <div class="ph-alert__title">
                                <i class="fa fa-check-circle"></i>
                                <span>${t('Facility')} "<strong>${facility.name}</strong>" ${t('has been created successfully.')}</span>
                            </div>
                        </div>
                        <p class="text-secondary mb-4">${t('What would you like to do next?')}</p>
                    </div>
                    <div class="ph-faux-dialog-footer d-flex gap-2">
                        <button type="button" class="btn ph-btn btn-default waves-effect mr-auto" id="createAnother">
                            <i class="fa fa-shop"></i> ${t('Add Another Facility')}
                        </button>
                        <button type="button" class="btn ph-btn btn-primary waves-effect" id="goToDetails">
                            <i class="fa fa-edit"></i> ${t('Go to Facility Details')}
                        </button>
                        <button type="button" class="btn ph-btn btn-secondary waves-effect" id="closeModal">
                            <i class="fa fa-times"></i> ${t('Close')}
                        </button>
                    </div>
                </div>
            `,
            showCloseButton: true,
            allowOutsideClick: false,
            allowEscapeKey: true,
            showConfirmButton: false,
            showCancelButton: false,
            width: 600,
            onOpen: () => {
                $('#goToDetails').on('click', () => {
                    console.log('go to details');
                    swalWithBootstrapButtons.close();
                    window.location.href = `/club-details?club=${facility.id}`;
                });
                
                $('#createAnother').on('click', () => {
                    console.log('create another');
                    swalWithBootstrapButtons.close();
                    newFacilityDialog();
                });
                
                $('#closeModal').on('click', () => {
                    console.log('close modal');
                    swalWithBootstrapButtons.close();
                });
            },
            onClose: () => {
                $('#goToDetails').off('click');
                $('#createAnother').off('click');
                $('#closeModal').off('click');
            }
        });
    }

    function newFacilityDialog() {
        const newUUID = uuidv4();

        const windowHTML = `
            <div class="col-sm-12">
                <div class="row permissions">
                    <div class="col-sm-12">
                        <div class="col-sm-4">
                            <b>${t('Organisation')}:</b>
                        </div>
                        <div class="col-sm-8">
                            <select class="form-control show-tick" name="organisationId" required>
                                <option value="">- Please Select -</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <b>${t('Brand')}:</b>
                        </div>
                        <div class="col-sm-8">
                            <select class="form-control show-tick" name="brandId" required>
                                <option value="">- Please Select -</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <b>${t('Region')}:</b>
                        </div>
                        <div class="col-sm-8">
                            <select class="form-control show-tick" name="region" data-live-search="true" required>
                                <option value="">- Please Select -</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <b>${t('Name')}:</b>
                        </div>
                        <div class="col-sm-8">
                            <div class="form-group">
                                <div class="form-line">
                                    <input class="form-control" value="" name="name" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <b>${t('Slug')}:</b>
                        </div>
                        <div class="col-sm-8">
                            <div class="form-group">
                                <div class="form-line">
                                    <input class="form-control" value="" name="slug" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <b>${t('Facility ID')}
                                <i class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t(
                                    `Note that the 'Facility ID' will be based on the name provided - This can not be changed one created, so please ensure this is correct!`
                                )}">info_outline</i>:
                            </b>
                        </div>
                        <div class="col-sm-8 hidden facility-id-input-col">
                            <div class="form-group">
                                <div class="form-line">
                                    <input class="form-control" value="${newUUID}" name="id" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-8 facility-id-col">
                            <div class="d-flex gap-1">
                                <div id="facility-id-display" class="ph-badge flex-gap-1"><span>${newUUID}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <hr>
                        <div class="ph-alert mb-4">
                            <div class="ph-alert__title">
                                <i class="fa fa-info-circle"></i>
                                <span>${t(`More Facility Details`)}</span>
                            </div>
                            <div class="ph-alert__description">
                                <p>
                                    ${t(`After facility creation, you can chose to be redirected to the Facility's page where you can update the email address, location, phone number, social handles, and more`)}.
                                </p>
                            </div>
                        </div>
                        <div class="ph-alert ph-alert_info">
                            <div class="ph-alert__title">
                                <i class="fa fa-info-circle"></i>
                                <span>${t(`Facility Status`)}</span>
                            </div>
                            <div class="ph-alert__description">
                                <p>
                                    ${t(
                                        `Listings will be added in an 'Unpublished' state and will not be initially displayed on any websites, until published!`
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const div = document.createElement('div');
        $(div).addClass('row new-facility-dialog ph-dialog-v2');
        $(div).css('padding', '20px');
        $(div).html(windowHTML);

        // Info tooltip needs to be rendered
        $(div).find('[data-toggle="tooltip"]').tooltip({ container: 'body' });
        $(div).find('input[name=slug]').inputmask({ regex: '^[a-z0-9]+(?:-[a-z0-9]+)*$' });

        $(div)
            .find('input[name=name]')
            .on('input', function () {
                const facilityName = $(this).val();
                const slug = facilityName
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-|-$/g, '')
                    .replace(/--+/g, '-');

                $(div).find('input[name=slug]').val(slug);
            });

        $(div)
            .find('input[name=id]')
            .keyup(function () {
                const facilityId = $(this).val().capitalize().replace(/\s/g, '');
                $(this).val(facilityId);
            });

        const facilityRegions = ClubsObj.reduce((acc, facility) => {
            const country = facility.localisation?.country;
            if (country?.full_name && !acc.some((r) => r.iso_code === country.iso_code)) acc.push(country);
            return acc;
        }, []);

        facilityRegions.forEach((country) => {
            const selected = country.code === 'AU' ? 'selected' : '';
            $(div)
                .find('select[name=region]')
                .append(`<option value="${country.iso_code}" ${selected}>${country.full_name}</option>`);
        });

        $(div)
            .find('select[name=region]')
            .append(
                '<option disabled style="border: 0.5px solid rgb(217 216 216); height: 0px; padding: 0px;"></option>'
            )
            .append('<option value="add-region" data-icon="fa-plus">Add Region</option>');

        filters.organisations
            .sort((a, b) => a.label.localeCompare(b.label))
            .forEach((org) => {
                $(div).find('select[name=organisationId]').append(`<option value="${org.value}">${org.label}</option>`);
            });

        $(div).find('select').selectpicker();

        // If the user is an admin, we need to allow them to add a new region...
        $(div)
            .find('select[name=region]')
            .on('change', async function () {
                const region = $(this).val();
                if (region !== 'add-region') return;

                $(div).find('select[name=region]').html('<option value="">- Please Select -</option>');
                countries.forEach((country) => {
                    $(div)
                        .find('select[name=region]')
                        .append(`<option value="${country.code}">${country.name}</option>`);
                });

                $('select[name=region]').selectpicker('refresh');
                // Wait for the selectpicker to render...
                await new Promise((r) => setTimeout(r, 100));
                $('select[name=region]').siblings('button.dropdown-toggle').click();
            });

        function renderBrandOptions() {
            const orgId = $(div).find('select[name=organisationId]').val();
            const org = filters.organisations.find((o) => o.value === orgId);
            if (!org) return;

            const brandSelect = $(div).find('select[name=brandId]');
            brandSelect.html('<option value="">- Please Select -</option>');

            org.brands
                .sort((a, b) => a.name.localeCompare(b.name))
                .forEach((brand) => {
                    brandSelect.append(`<option value="${brand.brandId}">${brand.name}</option>`);
                });

            brandSelect.selectpicker('refresh');
        }

        $(div).find('select[name=organisationId]').on('change', renderBrandOptions);

        let newFacility = null;
        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 800,
                title: t('Add New Facility'),
                showCloseButton: true,
                allowOutsideClick: false,
                allowEscapeKey: true,
                showCancelButton: true,
                showConfirmButton: true,
                confirmButtonText: t('Create New Facility'),
                cancelButtonText: t('Cancel'),
                onOpen: () => {
                    addClipboardCopyButtons('#facility-id-display', { childEl: 'span' });
                },
                preConfirm: async () => {
                    newFacility = {
                        id: $(div).find('input[name=id]').val(),
                        name: $(div).find('input[name=name]').val(),
                        brandId: $(div).find('select[name=brandId]').val(),
                        regionIso: $(div).find('select[name=region]').val(),
                        regionFull: $(div).find('select[name=region] option:selected').text(),
                        slug: $(div).find('input[name=slug]').val(),
                    };

                    if (Object.values(newFacility).some((value) => !value)) {
                        return Swal.showValidationMessage('Please fill in all fields');
                    }

                    const response = await createClubAPI(newFacility, selectedOrgID);
                    if (!response[0]) return Swal.showValidationMessage(response[1]);
                    return true;
                },
            })
            .then((response) => {
                if (!response.value) return;
                showFacilityCreationSuccessModal(newFacility);
            });
    }

    function manageFacilityDialog(facility) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');

        $(div).html(`
            <div class="body">
                <div class="d-flex flex-column gap-2">
                    <div>
                        <label class="form-label">${t('Contact Details')}</label>
                        <div class="contacts-list d-flex gap-2" style="min-height: 69px;"></div>
                    </div>
                    <div class="admin-notes-container hidden">
                        <label class="form-label">${t('Admin Notes')}</label>
                        <div class="admin-notes d-flex flex-column gap-1 mb-3"></div>
                        <div class="d-flex gap-2 align-start">
                            ${textArea({
                                name: 'adminNotes',
                                value: facility.adminNotes || '',
                                placeholder: t('Add a note...'),
                            })}
                            <button class="btn btn-primary add-admin-note">
                                <i class="fa fa-send"></i> Add Note
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);

        function contactListItem(contact) {
            return `
                <div class="contact-item card p-3" style="margin-bottom: 0px">
                    <div>${contact.name}</div>
                    <a href="mailto: ${contact.email}">${contact.email}</a>
                    <div>${contact.phone || ''}</div>
                </div>
            `;
        }

        function adminNoteItem(note) {
            const div = $(`
                <div class="admin-note-item card p-3" style="margin-bottom: 0px">
                    <div class="d-flex gap-1 align-start justify-between">
                        <div class="grow-1">
                            <div class="d-flex gap-3 justify-between text-muted" style="font-size: 0.8em;">
                                <div>${note.modifiedBy}</div>
                                <span>${moment.unix(note.modifiedAt).format('YYYY-MM-DD HH:mm:ss')}</span>
                            </div>
                            <div>${note.content || '<em class="text-muted" style="opacity: 0.5">No content</em>'}</div>
                        </div>

                        <button class="btn btn-icon btn-square text-danger delete-admin-note">
                            <i class="fa fa-close"></i>
                        </button>
                    </div>
                </div>
            `);

            $(div)
                .find('.delete-admin-note')
                .on('click', async (e) => {
                    $(e.currentTarget).prop('disabled', true);
                    const deleteRes = await deleteAdminNoteFromFacilityAPI(facility.id, note.noteId);
                    $(e.currentTarget).prop('disabled', false);

                    if (!deleteRes[0]) {
                        return Swal.showValidationMessage(deleteRes[1]);
                    }

                    $(div).remove();
                });

            return div;
        }

        $(div)
            .find('.add-admin-note')
            .on('click', async (e) => {
                const content = $(div).find('textarea[name=adminNotes]').val();

                $(e.currentTarget).prop('disabled', true);
                const updateRes = await upsertAdminNoteToFacilityAPI(facility.id, { content });
                $(e.currentTarget).prop('disabled', false);

                if (!updateRes[0]) {
                    return Swal.showValidationMessage(updateRes[1]);
                }

                $(div).find('.admin-notes').append(adminNoteItem(updateRes[1]));
                $(div).find('textarea[name=adminNotes]').val('');
                $(div).find('textarea[name=adminNotes]').focus();
            });

        swalWithBootstrapButtons.fire({
            html: div,
            width: 800,
            title: facility.name,
            showCloseButton: true,
            showCancelButton: false,
            showConfirmButton: false,
            onBeforeOpen: async () => {
                const contactList = $(div).find('.contacts-list');

                const [kycRes, adminNotesRes] = await Promise.all([
                    getClubKycAPI(facility.id),
                    listFacilityAdminNotesAPI(facility.id),
                ]);

                if (!kycRes[0]) {
                    Swal.showValidationMessage(kycRes[1]);
                    contactList.html(`<em class="text-danger">${kycRes[1]}</em>`);
                } else if (!kycRes[1].contactDetails?.length) {
                    const message = t('No KYC contact details available for this facility.');
                    contactList.html(`<em class="text-muted">${message}</em>`);
                } else {
                    kycRes[1].contactDetails?.forEach((contact) => contactList.append(contactListItem(contact)));
                }

                if (adminNotesRes[0]) {
                    $(div).find('.admin-notes-container').removeClass('hidden');

                    adminNotesRes[1].forEach((note) => {
                        $(div).find('.admin-notes').append(adminNoteItem(note));
                    });
                }
            },
        });
    }

    init();
}
