let lastWikiPageVisited = "/openkb";
let lastWikiClubId = null;
let wikiInnerPage = document.location.href.includes('articles') || document.location.href.includes('%2Fkb%2F') ? true : false;

// User changed the club in the dropdown combo...
function reloadWikiFrameBasedOnClubID(clubId, internalRoute) {
    $('#wiki-loader').show();

    // Prevent this from running if the user is on the same club
    if (!lastWikiClubId || lastWikiClubId === selectedID) return;

    if(internalRoute.length > 0) {
        //go to other wiki pages

        // TODO - Restore previous logic once wiki live...
        // $('#wikiContainer').attr('src', `/openkb/${internalRoute}`);
        $('#wikiContainer').attr('src', `/openkb/${internalRoute}/?club=${clubId}`);

    } else {
        // TODO - Restore previous logic once wiki live...
        // $('#wikiContainer').attr('src', `${lastWikiPageVisited}`);
        $('#wikiContainer').attr('src', `${lastWikiPageVisited}?club=${clubId}`);
    }
}

function sWiki() {
    // loadingOverlay('.iframe-placeholder');
    $('#wiki-loader').show();
    
    wikiInnerPage = document.location.href.includes('articles') || document.location.href.includes('%2Fkb%2F') ? true : false;

    if(wikiInnerPage) {
      $('#wikipage-wrap').hide();
      $('#wikipage-wrap-2').show();
    } else {
      $('#wikipage-wrap').show();
      $('#wikipage-wrap-2').hide();
    }

    $('.iframe-placeholder iframe').ready(function() {
        setTimeout(function(){
            $(".iframe-placeholder iframe").show();
            $('#wiki-loader').fadeOut();
        }, 10000);
    });

	window.onmessage = function (e) {
		if (e.data == 'modalShow') {
			console.log('show');
			$('#wikiOverlay').show();
			$('#wikiContent').addClass('iframe-placeholder-zindexOn');
		}
		if (e.data == 'modalHide') {
			$('#wikiOverlay').hide();
			$('#wikiContent').removeClass('iframe-placeholder-zindexOn');
		}
	};
}

function sWikiURI(e) {
    console.log('sWikiURI', e);

    let perfhub = document.location.origin; //'http://localhost:3000';
    let wiki = $('#wikiContainer').contents().get(0).location.href;
    let route = wiki.replace(perfhub, '');
    let encodedRoute = encodeURIComponent(route);

    //store last visited route globally, so when user changes the dropdown, we can reload the same page where the user left off
    lastWikiPageVisited = route;
    lastWikiClubId = selectedID;
    //remove the parameters after '?' because we will attach the clubID when user changes the clubId
    if(route.includes('?')) {
        lastWikiPageVisited = route.substring(0, route.indexOf("?"));
    }

    const basePath = document.location.pathname.includes('wiki-admin')
        ? '/wiki-admin'
        : '/wiki';

    sammy.quietRoute(`${basePath}/#${encodedRoute}`);
    setTimeout(() => {
      $('#wiki-loader').fadeOut();
    }, 2000)
}


function applyIframeOnLoad() {
  const wikiContainer = $('#wikiContainer');

  if(wikiContainer.length) {
    return wikiContainer.on('load', sWikiURI);
  }

  return setTimeout(applyIframeOnLoad, 800);
}

applyIframeOnLoad();