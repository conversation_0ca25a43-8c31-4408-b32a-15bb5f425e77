let adminRolesTable = null;

async function sRoleManagement() {
    const root = $('#admin-portal #roles');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');
    let roles = [];
    let countries = [];

    const filters = {
        organisations: [],
        modules: [],
        regions: [],
        facilities: [],
    };

    const query = getQueryStringsObject();
    
    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('ADMIN_FILTERS_ROLES');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }
    
    // Use query params first, then localStorage, then defaults
    let selectedFilters = {
        organisations: query.organisations || savedFilters.organisations || 'all',
        modules: query.modules || savedFilters.modules || 'all',
        regions: query.regions || savedFilters.regions || 'all',
        facilities: query.facilities || savedFilters.facilities || 'all',
        search: query.search || savedFilters.search || '',
    };

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const [rolesRes, organisationsRes, modulesRes, countriesRes] = await Promise.all([
            listRolesAPI(),
            listOrganisationsAPI(['facilities', 'regions']),
            listModulesAPI(),
            listAllCountriesAPI(),
        ]);

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!rolesRes[0] || !organisationsRes[0] || !modulesRes[0] || !countriesRes[0]) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        roles = rolesRes[1];
        countries = countriesRes[1];

        filters.organisations = organisationsRes[1].map((org) => ({
            value: org.organisationId,
            label: org.name,
            modules: org.modules,
            regions: org.regions,
            facilities: org.facilities,
        }));

        filters.modules = modulesRes[1].map((mod) => ({
            value: mod.moduleId,
            label: mod.name,
        }));

        filters.facilities = ClubsObj?.map((facility) => ({
            value: facility.id,
            label: facility.name,
        }));

        filters.regions = ClubsObj?.reduce((acc, facility) => {
            const { country } = facility?.localisation || {};
            if (!country?.full_name || !country?.iso_code || acc.some((r) => r.value === country.iso_code)) return acc;
            acc.push({ value: country.iso_code, label: country.full_name });
            return acc;
        }, []);

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => adminRolesTable && adminRolesTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => adminRolesTable && adminRolesTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => adminRolesTable && adminRolesTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => adminRolesTable && adminRolesTable.button(3).trigger() },
        ];
        const buttons = [
            { label: 'Add Role', icon: 'fa-tag', onClick: () => roleDialog() },
            ...exportButtons,
        ];
        searchbarHeader(
            root.find('.search-bar-container'),
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'roles-export-buttons',
                },
            }
        );

        root.find('.search-bar').val(selectedFilters.search);

        root.find('.search-bar').on('input', function () {
            selectedFilters.search = $(this).val();
            updateHrefQuery({ search: selectedFilters.search });
            
            // Save search filter to localStorage
            try {
                localStorage.setItem('ADMIN_FILTERS_ROLES', JSON.stringify(selectedFilters));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }
            
            renderRolesTable();
        });

        renderPageFilters();
        renderRolesTable();
    }

    function renderPageFilters() {
        filtersContainer.html('');

        return new PageFilters(
            filtersContainer,
            Object.keys(filters).map((key) => ({
                id: key,
                label: key.charAt(0).toUpperCase() + key.slice(1),
                options: [
                    { label: 'All', value: 'all', selected: selectedFilters[key] === 'all' },
                    ...filters[key]
                        .filter((option) => {
                            if (key === 'regions' && selectedFilters.organisations !== 'all') {
                                const selectedOrg = filters.organisations.find(
                                    (org) => org.value === selectedFilters.organisations
                                );

                                return selectedOrg?.regions.some((r) => r.iso_code === option.value);
                            }

                            if (key === 'facilities' && selectedFilters.organisations !== 'all') {
                                const selectedOrg = filters.organisations.find(
                                    (org) => org.value === selectedFilters.organisations
                                );

                                return selectedOrg?.facilities.some((f) => f.id === option.value);
                            }

                            return true;
                        })
                        .map((option) => ({
                            label: option.label,
                            value: option.value,
                            selected: selectedFilters[key] === option.value,
                        }))
                        .sort((a, b) => a.label.localeCompare(b.label)),
                ],
            })),
            (newFilters) => {
                selectedFilters = { ...selectedFilters, ...newFilters };
                
                // if org is selected, filter selected regions and facilities to be within that org
                if (selectedFilters.organisations !== 'all') {
                    const selectedOrg = filters.organisations.find(
                        (org) => org.value === selectedFilters.organisations
                    );

                    if (
                        selectedFilters.regions !== 'all' &&
                        !selectedOrg?.regions.some((r) => r.iso_code === selectedFilters.regions)
                    ) {
                        selectedFilters.regions = 'all';
                    }

                    if (
                        selectedFilters.facilities !== 'all' &&
                        !selectedOrg?.facilities.some((f) => f.id === selectedFilters.facilities)
                    ) {
                        selectedFilters.facilities = 'all';
                    }
                }

                Object.keys(selectedFilters).forEach((key) => {
                    updateHrefQuery({ [key]: selectedFilters[key] === 'all' ? null : selectedFilters[key] });
                });
                
                // Save filters to localStorage
                try {
                    localStorage.setItem('ADMIN_FILTERS_ROLES', JSON.stringify(selectedFilters));
                } catch (error) {
                    console.error('Error saving filters to localStorage:', error);
                }

                renderPageFilters();
                renderRolesTable();
            },
            true
        );
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>
            <div class="d-flex gap-1 my-4">
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 139px; height: 24px; border-radius: 24px;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 5%;"></div>
                </div>
            `);
        });
    }

    async function renderRolesTable() {
        let export_filename = 'Roles__' + (new Date().toISOString());
        adminRolesTable?.clear();
        adminRolesTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('Name')}</th>
                        <th>${t('Organisation')}</th>
                        <th>${t('Modules')}</th>
                        <th>${t('Access Scope')}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        roles
            .filter((role) => {
                const searchRegex = new RegExp(selectedFilters.search, 'igm');
                const searchMatch =
                    role.roleId.match(searchRegex) ||
                    role.name.match(searchRegex) ||
                    role.organisation?.name.match(searchRegex) ||
                    role.modules?.some((module) => module.name.match(searchRegex)) ||
                    role.regions?.some((region) => getCountryNameFromCode(region).match(searchRegex)) ||
                    role.facilities?.some((facility) => facility.match(searchRegex)) ||
                    role.moduleMode.match(searchRegex) ||
                    role.accessScope.match(searchRegex);

                if (!searchMatch) return false;

                const orgMatch =
                    selectedFilters.organisations === 'all' || role.organisationId === selectedFilters.organisations;
                if (!orgMatch) return false;

                const regionMatch =
                    selectedFilters.regions === 'all' || role.regions?.some((r) => r === selectedFilters.regions);
                if (!regionMatch) return false;

                const facilityMatch =
                    selectedFilters.facilities === 'all' ||
                    role.facilities?.some((f) => f === selectedFilters.facilities);
                if (!facilityMatch) return false;

                const moduleMatch =
                    selectedFilters.modules === 'all' ||
                    role.modules.some((m) => m.moduleId === selectedFilters.modules);
                if (!moduleMatch) return false;

                return true;
            })
            .forEach((role) => {
                tableBodyEl.append(`
                    <tr data-id="${role.roleId}">
                        <td>${role.name}</td>
                        <td>
                            ${
                                role.organisation?.name ||
                                '<div class="text-muted" style="opacity: 0.5;">No organisation</div>'
                            }
                        </td>
                        <td>
                            ${getModuleBadges({
                                modules: role.modules,
                                allowAllExcept: role.moduleMode === 'allowAllExcept',
                                allModules: role.organisation.modules,
                                search: selectedFilters.search,
                            })}
                        </td>
                        <td>
                            <div class="d-flex flex-wrap" style="gap: 2px;">
                                ${
                                    accessScopeBadges(role, selectedFilters.search) ??
                                    '<div class="text-muted" style="opacity: 0.5;">No access</div>'
                                }
                            </div>
                        </td>
                        <td>
                            <div class="btn ph-btn ph-btn-sm btn-outline btn-danger d-flex gap-1 btn-delete-role row-actions">
                                <a class="remove-role" data-id="${
                                    role.roleId
                                }" href="#" title="Remove Role"><i class="fa fa-times"></i></a>
                            </div>
                        </td>
                    </tr>
                `);
            });

        adminRolesTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [[0, 'asc']],
            autoWidth: false,
            iDisplayLength: 50,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Roles',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf'
                },
            ],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const roleId = $(this).data('id');
                    const role = roles.find((r) => r.roleId === roleId);
                    roleDialog(role);
                });

                $(row)
                    .find('.btn-delete-role')
                    .on('click', async function (e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const roleId = $(this).find('a').data('id');
                        const role = roles.find((r) => r.roleId === roleId);

                        if (!role) return;
                        deleteRoleDialog(role);
                    });
            },
            columnDefs: [{ targets: 4, orderable: false, width: '0px' }],
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));

        selectedFilters.search && adminRolesTable.search(selectedFilters.search).draw();
    }

    function deleteRoleDialog(role) {
        const html = document.createElement('div');
        $(html).addClass('ph-dialog-v2');

        $(html).html(`
            <div class="body">
                ${t('You are about to remove this role from Performance Hub.')}
                <br />
                ${t('This action cannot be undone.')}
            </div>
        `);

        swalWithBootstrapButtons
            .fire({
                title: t('Are you sure you want to remove this role?'),
                html,
                showCancelButton: true,
                confirmButtonText: t('Delete'),
                cancelButtonText: t('Cancel'),
                preConfirm: async () => {
                    const response = await deleteRoleAPI(role.roleId);
                    if (!response[0]) return Swal.showValidationMessage(response[1]);
                    return true;
                },
            })
            .then(async (result) => {
                if (!result.value) return;
                instantNotification(t('Role deleted successfully'), 'success');
                roles = roles.filter((r) => r.roleId !== role.roleId);
                renderRolesTable();
            });
    }

    function accessScopeBadges(role, search, maxItems = 2) {
        if (role.accessScope === 'organisation') {
            return `<span class="badge ph-badge badge-blue">${t('Organisation')}</span>`;
        }

        if (role.accessScope === 'template') {
            return `<span class="badge ph-badge">${t('Template')}</span>`;
        }

        const items = role[role.accessScope] || [];
        const searchRegex = new RegExp(search, 'igm');
        const filteredItems = items.filter((item) => item.match(searchRegex)).slice(0, maxItems);
        const filteredLength = filteredItems.length || 0;

        const itemsText = filteredItems
            .map((item) => {
                const name = role.accessScope === 'regions' ? getCountryNameFromCode(item) : item;
                return `<span class="badge ph-badge badge-green">${name}</span>`;
            })
            .join('');

        const itemsLeft = Math.max(0, items.length - filteredLength);
        return itemsLeft ? `${itemsText} + ${itemsLeft} more` : itemsText;
    }

    function roleDialog(role) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');
        
        // Store reference to module group picker for proper cleanup
        let moduleGroupPicker = null;

        $(div).html(`
            <div class="body">
                <div class="row clearfix">
                    <div class="col-sm-4">
                        <div class="d-flex flex-column gap-3">
                            ${textField({ label: t('Name'), name: 'name', value: role?.name })}
                            ${selectpicker({
                                label: t('Organisation'),
                                name: 'organisationId',
                                options: filters.organisations.map((org) => ({
                                    value: org.value,
                                    label: org.label,
                                })),
                                value: role?.organisationId,
                            })}
                            ${checkBox({
                                label: `
                                    <span>${t('Organisation Admin')}</span>
                                    ${infoPopup(
                                        t(
                                            'Grants elevated access to manage and view organisation-level settings and integrations across all linked facilities.'
                                        )
                                    )}`,
                                name: 'organisation-admin',
                                checked: role?.isOrganisationAdmin,
                            })}
                        </div>
                    </div>
                    <div class="col-sm-8">
                        <div class="d-flex flex-column gap-1">
                            <div>
                                <label class="form-label">${t('Access Scope')}</label>
                                <div class="access-scope-mode d-flex align-center gap-4 mb-4"></div>
                                ${selectpicker({
                                    name: 'facilities',
                                    options: [],
                                    multiple: true,
                                    selectClx: 'mui',
                                    usePhTheme: true,
                                })}
                                ${selectpicker({
                                    name: 'regions',
                                    options: [],
                                    multiple: true,
                                    selectClx: 'mui',
                                    usePhTheme: true,
                                })}
                            </div>
                            <div>
                                <div class="d-flex align-center justify-between">
                                    <label class="form-label">${t('Module Access')}</label>
                                    ${checkBox({
                                        id: 'module-mode',
                                        label: t('Allow All Except'),
                                        name: 'module-mode',
                                        checked: role?.moduleMode === 'allowAllExcept' ? 'selected' : '',
                                    })}
                                </div>
                                <div class="module-groups mt-3 d-flex flex-column gap-2" style="max-height: 50vh; overflow-y: auto;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `);

        $(div).find('select[name="organisationId"]').selectpicker();
        $(div)
            .find('select.mui')
            .select2({
                theme: 'material',
                templateResult: (state) => {
                    switch (state.id) {
                        case 'horizontal-line':
                            return $('<hr />');
                        case 'add-region':
                            return $(
                                '<div style="color: #666;"><i class="fa fa-plus" style="padding-right: 3px;"></i> Add Region</div>'
                            );
                        default:
                            return state.text;
                    }
                },
            });

        [
            { label: 'Facilities', value: 'facilities' },
            { label: 'Regions', value: 'regions' },
            {
                label: 'Organisation',
                value: 'organisation',
                info: t('This role has access to all facilities and regions in the organisation'),
            },
            {
                label: 'Template',
                value: 'template',
                info: t('Assign access to specific facilities/regions when assigning this role to a user'),
            },
        ].forEach(({ value, label, info }) => {
            const checked = (role?.accessScope ?? 'facilities') === value ? 'checked' : '';
            const infoEl = info ? infoPopup(info) : '';

            $(div).find('.access-scope-mode').append(`
                <div style="margin-top:5px;" class="form-check ph-form-check">
                    <input
                        id="${value}-access-scope"
                        class="form-check-input"
                        type="radio"
                        name="access-scope"
                        value="${value}"
                        ${checked}
                    />
                    <label class="form-check-label ph-form-check-label" for="${value}-access-scope">${t(label)} ${infoEl}</label>
                </div>
            `);
        });

        $(div).find('[data-toggle="tooltip"]').tooltip({});

        setAccessScopeSelectVisibility();
        handleOrgChange();
        $(div).find('input[name="access-scope"]').on('change', setAccessScopeSelectVisibility);
        $(div).find('select[name="organisationId"]').on('change', handleOrgChange);

        $(div)
            .find('select[name=regions]')
            .on('change', async function () {
                const regions = $(this).val();
                if (!regions.includes('add-region')) return;

                const selected = regions.filter((r) => r !== 'add-region');
                $(div).find('select[name=regions]').val(selected);
                $(div).find('select[name=regions]').select2({ theme: 'material' });

                $(div).find('select[name=regions]').html('<option value="" disabled>- Please Select -</option>');
                countries.forEach((country) => {
                    const selectedProp = selected.some((r) => r === country.code) ? 'selected' : '';
                    $(div)
                        .find('select[name=regions]')
                        .append(`<option value="${country.code}" ${selectedProp}>${country.name}</option>`);
                });

                await new Promise((r) => setTimeout(r, 100));
                $('select[name=regions]').closest('.form-group').find('.selection > span').trigger('click');
            });

        async function setAccessScopeSelectVisibility() {
            const scope = $(div).find('input[name="access-scope"]:checked').val();
            $(div).find('select[name="facilities"]').closest('.form-group').hide();
            $(div).find('select[name="regions"]').closest('.form-group').hide();
            $(div).find(`select[name="${scope}"]`).closest('.form-group').show();
        }

        async function handleOrgChange() {
            const orgId = $(div).find('select[name="organisationId"]').val();
            const org = filters.organisations.find((org) => org.value === orgId);
            if (!org) return;

            // Clean up existing module group picker before creating a new one
            const moduleGroupsContainer = $(div).find('.module-groups');
            if (moduleGroupPicker) {
                moduleGroupPicker.destroy(); // Use the built-in destroy method
            }
            
            moduleGroupPicker = renderModuleGroupPickers(moduleGroupsContainer, org.modules, role?.modules || []);

            $(div).find('select[name="regions"]').empty();

            const roleRegions = (role?.regions || []).map((isoCode) => ({
                iso_code: isoCode,
                full_name: getCountryNameFromCode(isoCode),
            }));

            org.regions.forEach((r) => {
                if (!roleRegions.some((region) => region.iso_code === r.iso_code)) {
                    roleRegions.push(r);
                }
            });

            roleRegions
                .sort((a, b) => a.full_name.localeCompare(b.full_name))
                .forEach((region) => {
                    const selected = role?.regions?.some((r) => r === region.iso_code) ? 'selected' : '';
                    $(div).find('select[name="regions"]').append(`
                    <option value="${region.iso_code}" ${selected}>${region.full_name}</option>
                `);
                });

            $(div).find('select[name="facilities"]').empty();
            org.facilities.forEach((facility) => {
                const selected = role?.facilities?.some((f) => f === facility.id) ? 'selected' : '';
                $(div).find('select[name="facilities"]').append(`
                    <option value="${facility.id}" ${selected}>${facility.name}</option>
                `);
            });

            $(div).find('select[name="regions"]').append('<option disabled value="horizontal-line"></option>').append(`
                    <option value="add-region">Add Region</option>
                `);

            $(div).find('select[name="modules"]').selectpicker('refresh');
        }

        async function preConfirm() {
            const doc = {
                name: $(div).find('input[name=name]').val(),
                organisationId: $(div).find('select[name="organisationId"]').val(),
                moduleMode: $(div).find('input[name="module-mode"]').is(':checked') ? 'allowAllExcept' : 'manual',
                modules: [],
                accessScope: $(div).find('input[name="access-scope"]:checked').val(),
                facilities: $(div).find('select[name="facilities"]').val(),
                regions: $(div).find('select[name="regions"]').val(),
                isOrganisationAdmin: $(div).find('input[name="organisation-admin"]').is(':checked'),
            };

            // Collect selected modules from hidden inputs (created by ModuleGroupPicker)
            $(div).find('input[name="modules"]').each(function() {
                doc.modules.push($(this).val());
            });

            const response = !role ? await createRoleAPI(doc) : await updateRoleAPI(role.roleId, doc);

            if (!response[0]) Swal.showValidationMessage(response[1]);
            return response[1];
        }

        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 800,
                title: !role ? t('Add Role') : t('Edit Role'),
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false,
                showCloseButton: true,
                confirmButtonText: t('Save'),
                preConfirm,
            })
            .then((result) => {
                if (result.value) return init();
            });
    }

    init();
}

function getModuleBadges({ modules = [], allModules = [], allowAllExcept = false, maxItems = 2, search = '' }) {
    if (!allModules?.length) {
        return `<div class="text-muted">${t('No modules')}</div>`;
    }

    const filteredModules = modules.filter(({ moduleId }) =>
        allModules.some((m) => m.moduleId === moduleId || m === moduleId)
    );

    const hasAccessToAll = allowAllExcept ? filteredModules.length < 1 : allModules.length === filteredModules.length;
    const allModulesBadge = `<span class="badge ph-badge badge-blue">${t('All modules')}</span>` 

    if (hasAccessToAll) {
        return allModulesBadge;
    }

    const searchRegex = new RegExp(search, 'igm');
    const searchedModules =
        filteredModules?.filter((m) => searchRegex.test(m.name)).filter((_, i) => i < maxItems) || [];
    const searchedLength = searchedModules.length || 0;

    const emptyModules = `<div class="text-muted">${t('No modules')}</div>`;
    const moduleBadges =
        searchedModules?.map((m) => `<span class="badge ph-badge ${allowAllExcept ? 'badge-red ph-badge--opaque' : ''}">${t(m.name)}</span>`).join('') || '';
    const modulesText = `<div class="d-flex flex-wrap flex-gap-2">${moduleBadges || emptyModules}</div>`;

    const modulesLeft = Math.max(0, filteredModules?.length - searchedLength);
    const header = allowAllExcept ? `<div class="mb-2">${allModulesBadge} except: </div>` : '';
    return modulesLeft ? `${header} <div class="d-flex flex-wrap flex-gap-2">${modulesText} <span class="ph-badge ${allowAllExcept ? 'ph-badge--opaque badge-red' : 'badge-light'}">+ ${modulesLeft} more</span></div>` : modulesText;
}
