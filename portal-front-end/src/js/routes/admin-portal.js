function sAdminPortal() {
    const rootEl = $('#admin-portal');
    const basePath = '/admin-portal';
    const tabs = ['user-permissions', 'roles', 'facilities', 'brands'];

    // show organisation tab if user is not a global admin
    if (signedInUserData.isGlobalAdmin) {
        tabs.push('organisations');
        $('.org-tab').removeClass('hidden');
    }

    function init() {
        initUrl();
        initTabs();
        handleTabChange();
    }

    function initUrl() {
        const tab = location.pathname.split('/').pop();
        if (tabs.includes(tab)) return;
        history.pushState({ tabChange: true }, null, `${basePath}/${tabs[0]}`);
    }

    function initTabs() {
        const tab = location.pathname.split('/').pop();

        $(`.nav-tabs a[href="#${tab}"]`).tab('show');
        $('.nav-tabs a').on('click', function (e) {
            e.preventDefault();
            const href = $(this).attr('href');
            const formattedHref = `${basePath}/${href.replace('#', '')}`;
            history.pushState({ tabChange: true }, null, formattedHref);

            rootEl.find('.tab-pane').removeClass('in active');
            rootEl.find(`.tab-pane${href}`).addClass('in active');

            handleTabChange();
        });
    }

    function handleTabChange() {
        const tab = location.pathname.split('/').pop();
        if (tab === 'user-permissions') return sUserPermissions();
        if (tab === 'roles') return sRoleManagement();
        if (tab === 'facilities') return sFacilitiesManagement();
        if (tab === 'brands') return sBrandsManagement();
        if (tab === 'organisations') return sOrganisationsManagement();
    }

    init();
}
