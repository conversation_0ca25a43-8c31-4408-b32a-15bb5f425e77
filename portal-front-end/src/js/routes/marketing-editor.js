// Design manager system...

var gjs = null, docUnsaved = false, mp_displayRenderFileModal

// Only one document can be open at a time ...
// here's an example of the metadata we need to store/render/save the document object...
var documentObject = {}

function resetDocObject() {
    documentObject = {

        // Core design elements...
        id: null,
        name: null,
        height: 500,
        width: 500,
        resolution: 72,
        crop: false,
        scaling: false,
        format: "pdf",
        brand: "ALL",
        scaleTo: 1,
        bleed: false,
        tags: [],
        global: false,
        inputCharacterLimit: 0,
        inputRequired: false,
        deleted: 0,

        // Metadata Elements
        designVisibility: hasMPAdminAccess ? 'global' : 'region',
        club: 'global',
        usageGuidelines: '',
        designRegion: hasMPAdminAccess ? null : mpAccessibleRegions.map(r => r.iso_code),
        internalComments: [],
        approvalStatus: false,
        publishingAutomation: {
            autoPublish: moment().unix(),
            autoUnPublish: moment().add(10, 'years').unix(),
        },
        publishingAutomationPublishNow: true,
        publishingAutomationNeverExpire: true,

    }
}

function sEditor() {

    grapeJS();

    // $("html").css("font-size", "16px");
    // $("body").removeClass("materialize");

    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href") // activated tab
        if(target == '#editor') {
            $(".nav-help-container").hide();
            $("html").css("font-size", "16px");
            $("body").removeClass("materialize");
        } else {
            $(".nav-help-container").show();
            $("html").css("font-size", "10px");
            $("body").addClass("materialize");
        }
    });

    // Cancel/close editor button...
    $(".btn-gjs-cancel").on('click', function (e) {

        if(docUnsaved) {

            const swb = Swal.mixin({})

            swb.fire({
                title: t('Unsaved Changes'),
                html: `
                    <div class="ph-dialog-v2" style="padding: 20px;">
                       ${ t('The design you are currently editing has unsaved changes. Save changes before closing?') } 
                    </div>
                `,
                footer: `
                    <div style="padding-bottom: 10px;">
                        <a class="btn btn-margin btn-primary swb-save waves-effect">${t('Save')}</a>
                        <a class="btn btn-margin btn-danger swb-discard waves-effect">${t('Discard')}</a>
                        <a class="btn btn-margin btn-default swb-cancel waves-effect">${t('Cancel')}</a>
                    </div>
                `,
                width: 500,
                showConfirmButton: false,
                showCancelButton: false,
                onOpen: () => {
                    const swalElement = Swal.getPopup();
                    $(swalElement).draggable({
                        handle: '.swal2-header',  // Makes the title bar the drag handle
                    });
                    // Only change the cursor to move for the title bar
                    $(swalElement).find('.swal2-header').css('cursor', 'move');
                },
                onBeforeOpen: () => {
                   $(document).on('click', '.swb-save', function(e) {
                        e.preventDefault();

                        swb.close();
                        saveDesign();

                        $("#nav-editor-tab").hide();
                        $('.nav-tabs a[href="#designs"]').tab('show');
                        loadDesignsList(currentFilteredDesignTag);
                        docUnsaved = false;

                   });

                   $(document).on('click', '.swb-discard', function(e) {
                        e.preventDefault();
                        swb.close();

                        $("#nav-editor-tab").hide();
                        $('.nav-tabs a[href="#designs"]').tab('show');
                        loadDesignsList(currentFilteredDesignTag);
                        docUnsaved = false;

                   });

                   $(document).on('click', '.swb-cancel', function(e) {
                        e.preventDefault();
                        swb.close();
                   });

                }
            });

        } else {
            $("#nav-editor-tab").hide();
            $('.nav-tabs a[href="#designs"]').tab('show');

            loadDesignsList(currentFilteredDesignTag);
        }

    });

    $(".btn-gjs-save").on('click', function () {
        saveDesign();
    });

    $(".btn-gjs-docsetup, .document-display-title").on('click', function () {
        designerDocSetup(false, false, function(response) {

            if(response) {
                // Update document sizes...

                // callback response names are the same as the doc-object...
                documentObject = {...documentObject, ...response}
                if(response.scaling) { documentObject.scaling = true } else { documentObject.scaling = false; }

                // Update grapesjs canvas size & ui...
                deviceName = String(uuidv4())

                gjs.DeviceManager.add(deviceName, documentObject.width, {
                    height: documentObject.height
                });
                gjs.setDevice(deviceName);

                // Update/set title (if it changed)
                $(".document-display-title").html(documentObject.name);

                // Mark changes as not yet saved..
                docUnsaved = true;
            }

        });
    });

}

function newDesignDialog(groupingTags=false) {
    const adminView = (localStorage.getItem('designerAllDesigns') || 'false')
        .toLowerCase() == 'true';

    resetDocObject();

    documentObject.id = uuidv4();
    documentObject.name = "Untitled Design"

    if(groupingTags) {
        documentObject.tags = [groupingTags];
    }

    if (!adminView) {
        documentObject.club = selectedID;
        documentObject.designVisibility = 'club';
        documentObject.approvalStatus = true;
    }

    $(".document-display-title").html(documentObject.name);
    $('.nav-tabs a[href="#editor"]').tab('show');
    $("#nav-editor-tab").show();

    // Update grapesjs canvas size & ui to 1x1px (until we actually "create" the document)...
    deviceName = String(uuidv4())
    gjs.DeviceManager.add(deviceName, 1, {
        height: 1
    });
    gjs.setDevice(deviceName);

    designerDocSetup(true, groupingTags, function(response) {

        if(response) {
            // Update document sizes...

            // callback response names are the same as the doc-object...
            documentObject = {...documentObject, ...response}
            if(response.scaling) { documentObject.scaling = true } else { documentObject.scaling = false; }

            // Update grapesjs canvas size & ui...
            deviceName = String(uuidv4())

            gjs.DeviceManager.add(deviceName, documentObject.width, {
                height: documentObject.height
            });
            gjs.setDevice(deviceName);

            gjs.CssComposer.getAll().reset();
            gjs.setComponents(`
                <body>
                    <h1>${documentObject.name}</h1>
                    <hr>
                    <h3>${t("Please refer to the 'Help' section in the Editor toolbar for information around adding Dynamic Elements to this document.")}</h3>
                </body>
            `);
            gjs.setStyle();

            $(".document-display-title").html(documentObject.name);

        } else {

            // Hide editor...
            // No need to 'save' the design as it's not yet been touched...
            $("#nav-editor-tab").hide();
            $('.nav-tabs a[href="#designs"]').tab('show');
        }

    });
}


function designerDocSetup(newDoc=false, groupingTags=false, callback) {
    let globalSelected, clubSelected;

    if(documentObject.global) {
        globalSelected = 'selected';
        clubSelected = '';
    } else {
        if(newDoc) {
            globalSelected = 'selected';
            clubSelected = '';
        } else {
            globalSelected = '';
            clubSelected = 'selected';
        }
    }

    let brand12RNDSelected, brandUBXSelected, brandAllSelected, scaling, scaleTo

    if(documentObject.brand == "12RND") {
        brand12RNDSelected = 'selected';
        brandAllSelected = '';
        brandUBXSelected = '';
    } else if(documentObject.brand == "UBX") {
        brandUBXSelected = 'selected';
        brandAllSelected = '';
        brand12RNDSelected = '';
    } else {
        brandAllSelected = 'selected';
        brand12RNDSelected = '';
        brandUBXSelected = '';
    }

    if(documentObject.scaling) {
        scaling = 'checked';
    } else {
        scaling = '';
    }

    if(documentObject.scaleTo) {
        scaleTo = documentObject.scaleTo;
    } else {
        scaleTo = 10;
    }

    let setupHTML = `<div class="doc-setup-dialog ph-dialog-v2" style="padding: 20px;">
        <fieldset><legend>${t('Metadata')}</legend>
            <div class="row">
                <div class="col-sm-12">
                    <div class="alert alert-blue">
                        <small>
                            ${t('Please note that \'Metadata\' such as publishing status, guidelines, brand/visibility etc may be set AFTER the initial design has been created/saved. Initial designs will be created in a "Unapproved" state, and not visible to Clubs.')}
                        </small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-3">
                    ${t('Title')}:
                </div>
                <div class="col-sm-9">
                    <input data-hj-allow class="doc-name" type="text" value="${documentObject.name}" placeholder="${t('Untitled Document')}">
                </div>
            </div>
            <div class="row">
                <div class="col-sm-3 resolution-label">
                    ${t('Folders')}:
                </div>
                <div class="col-sm-9 doc-tags__container">
                    <select class="doc-tags"></select>
                </div>
            </div>
            <div class="row doc-input-settings">
                <div class="col-sm-3 resolution-label">
                    ${t('Input Settings')}:
                </div>
                <div class="col-sm-9">
                    <div class="col-sm-4 p-0">
                        <input data-hj-allow class="doc-input-required" id="doc-input-required" type="checkbox" ${documentObject.inputRequired ? 'checked' :''}>
                        <label for="doc-input-required">${t('Required')} </label> 
                    </div>
                    <div class="col-sm-8 p-0">
                        <div class="col-sm-6 p-0">
                            <label>${t('Character Limit')}: </label> 
                        </div>
                        <div class="col-sm-4 p-0">
                            <input data-hj-allow class="doc-input-limit" type="number" min="0" value="${documentObject.inputCharacterLimit}"/>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>

        <fieldset><legend>Canvas</legend>
            <div class="row">
                <div class="col-sm-3">
                    ${t('Width')}: <sup>(px)</sup>
                </div>
                <div class="col-sm-3">
                    <input data-hj-allow class="doc-width" type="number" min="1" max="5000" value="${documentObject.width}">
                </div>
                <div class="col-sm-3">
                    ${t('Height')}: <sup>(px)</sup>
                </div>
                <div class="col-sm-3">
                    <input data-hj-allow class="doc-height" type="number" min="1" max="5000" value="${documentObject.height}">
                </div>
            </div>
            <div class="row">
                <div class="col-sm-3 resolution-label">
                    ${t('Resolution')}: <sup>(dpi)</sup>
                </div>
                <div class="col-sm-3">
                    <input data-hj-allow class="doc-resolution" type="number" min="50" max="1000" value="${documentObject.resolution}">
                </div>
            </div>
        </fieldset>

        <fieldset><legend>Export Options</legend>
            <div class="row">
                <div class="col-sm-3 resolution-label">
                    ${t('Default Format')}:
                </div>
                <div class="col-sm-9">
                    <select class="doc-default-format">
                        <option value="jpg">Jpeg - RGB</option>
                        <option value="tiff">Tiff - CMYK</option>
                        <option value="png">PNG - RGB</option>
                        <option value="pdf">PDF - CMYK</option>
                        <option value="psd">PSD - CMYK</option>
                    </select>
                </div>
                <div class="col-sm-12">
                    <div class="help-subtext" style="padding-top: 5px;">
                        ${t('Note that users may override the default format, colour mode &amp; colour profiles when rendering an export by toggling advanced export settings.')}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12" style="margin-top:10px;">
                    <input data-hj-allow class="doc-device-scaling" id="doc-device-scaling" type="checkbox" ${scaling} />
                    <label for="doc-device-scaling">&nbsp;${t('Use "Design Upscaling" for high resolution exports')}</label>

                    <div class="slider-wrapper">
                        <div class="slider" id="device_scale_slider"></div>
                    </div>

                    <br><div class="help-subtext">
                        ${t('This will upscale your exports by using pixel doubling (based on the range selected). For example, a 100x100px document with a scale of "4" would be \'pixel doubled\' to 400x400px, however when editing, the base document will still be 100x100px.')}
                    </div>
                </div>
            </div>
        </fieldset>

    </div>`;

    // <br><br>It is important to use Device Scaling for large documents (such as A3 and A2 high resolution documents to keep the physical size of the document you are editing in the browser &quot;smaller&quot;. This minimizes the amount of RAM your browser will use, thus providing a better user experience for the designer.

    let confirmText
    if(newDoc) {
        confirmText = t("Create!");
    } else {
        confirmText = t("OK");
    }

    let deviceScaleSlider, modalConfigurationObj;

    swalWithBootstrapButtons.fire({
        title: t('Document Setup'),
        html: setupHTML,
        width: 500,

        showCloseButton: true,
        animation: false,
        showCancelButton: true,
        showConfirmButton: true,
        allowEscapeKey : false,
        allowOutsideClick: false,

        confirmButtonText: confirmText,
        cancelButtonText: t("Cancel"),
        onBeforeOpen: () => {

            validate();

            // Grouping Tags Select Multiple Autocomplete
            setGroupingTags(documentObject.tags);

            // If it's a new doc - pre-set the tags to whatever folder we are in...
            if(groupingTags) $('.doc-tags, .select-tags').val([groupingTags]).trigger('change')

            // Set the default format..
            $(".doc-default-format").val(documentObject.format)

            deviceScaleSlider = document.getElementById('device_scale_slider');
            noUiSlider.create(deviceScaleSlider, {
                start: [scaleTo],
                connect: 'lower',
                // snap: true,
                step: 1,
                // pips: {mode: 'count', values: 5},
                pips: {
                    mode: 'count',
                    values: 10,
                    density: 10
                },
                range: {
                    'min': [1],
                    'max': [10]
                }
            });

            var pips = deviceScaleSlider.querySelectorAll('.noUi-value');

            function clickOnPip() {
                var value = Number(this.getAttribute('data-value'));
                deviceScaleSlider.noUiSlider.set(value);
            }

            for (var i = 0; i < pips.length; i++) {
                pips[i].style.cursor = 'pointer';
                pips[i].addEventListener('click', clickOnPip);
            }

            // Function to 'format' the variable-name name so we don't get non alpha characters...
            $(".doc-setup-dialog input, .doc-device-scaling").keyup(function() {
                validate()
            });
            $(".doc-device-scaling").click(function() {
                validate()
            });

            function validate() {
                var isValid = true;

                if(! $(".doc-name").val()) isValid = false;
                if(! $(".doc-height").val()) isValid = false;
                if(! $(".doc-width").val()) isValid = false;
                if(! $(".doc-resolution").val()) isValid = false;
                if($(".doc-device-scaling").is(":checked")) {
                    $(".slider-wrapper").show();
                } else {
                    $(".slider-wrapper").hide();
                }

                if(isValid) {
                    $('.swal2-actions .swal2-confirm').prop('disabled', false);
                } else {
                    $('.swal2-actions .swal2-confirm').prop('disabled', true);
                }
            }
        },
        onClose: () => {
            modalConfigurationObj = {                
                "name": $(".doc-name").val(),
                "tags": $('.doc-tags').val(),

                "height": Number($(".doc-height").val().replace(/\D/g,'')),
                "width": Number($(".doc-width").val().replace(/\D/g,'')),
                "resolution": Number($(".doc-resolution").val().replace(/\D/g,'')),

                "scaling": $(".doc-device-scaling").is(":checked"),
                "scaleTo": deviceScaleSlider.noUiSlider.get(),
                "format": $(".doc-default-format option:selected").val(),
                "inputRequired": $(".doc-input-required").is(":checked"),
                "inputCharacterLimit": Number($(".doc-input-limit").val().replace(/\D/g,'')),
            }

        }
    }).then(response => {

        if (response.value) {
            callback(modalConfigurationObj);
        } else {
            callback(false);
        }
    });


}

function saveDesign() {

    var designObject = { ...documentObject, html: getCanvasCode() }
    
    saveDesignToDB(`/api/marketing/designs/${ selectedID }/save`, designObject, function(callback) {
        if(callback) {

            instantNotification(
                // Message...
                t('Changes Saved')
            );
            loadDesignsList(currentFilteredDesignTag);
            docUnsaved = false;
        }
    })

    $.ajax({
      url: `/api/algolia/update-marketing-print?facilityId=${selectedID}`,
      method: 'POST',
      data: JSON.stringify({ objectID: designObject.id, data: designObject }),
      contentType: 'application/json'
    });

    function getCanvasCode(){
        var html = gjs.editor.getHtml();
        var css = gjs.editor.getCss();

        var exportBody = document.createElement("html");

        $(exportBody).html('<head></head><body></body>')
        $(exportBody).find('head').append(`<style>${css}</style>`)
        $(exportBody).find('head').append('<link rel="stylesheet" href="https://content.gymsystems.co/hub/design-assets/fonts/hub/stylesheet.css">')
        $(exportBody).find("body").html(html)

        return $(exportBody).html();
    }

}

// Shows a dialog to 'render' the document, and prompts the user to input any variables that are not set,
// or any toggles in the design that the user wishes to hide/show.

function quickRenderDocument(id, auto=false, prefillContent = {}) {
    var toggleContent = "", variableContent = "", autoVariables = "", hasOptions = false;

    var scaleTo = (storedDesigns[id].scaleTo === undefined) ? 1 : storedDesigns[id].scaleTo;
    var printHeight = (storedDesigns[id].height === undefined) ? 0 : (storedDesigns[id].height * scaleTo);
    var printWidth = (storedDesigns[id].width === undefined) ? 0 : (storedDesigns[id].width * scaleTo);

    // get value from prefillContent using variable. ie: H1
    const getPrefillValue = (variable = "", isTextArea = false) => {
        const value = prefillContent[variable] || ""
        
        if(value) {

            // for input fields
            if(!isTextArea) {
                return `value="${value}"`;
            }

            return value

        }
            

        return ""
    }

    if(Array.isArray(storedDesigns[id].detectedToggles) && storedDesigns[id].detectedToggles.length) {
        var toggles = storedDesigns[id].detectedToggles;

        toggles.forEach(function(toggle) {
            toggleContent += `
                <div class="t-container" data-toggle="${toggle}">
                    <b>${toggle}:</b>
                    <div class="switch">
                        <label>${t('Hide')}<input data-hj-allow type="checkbox" checked><span class="lever lever switch-col-blue"></span>${t('Show')}</label>
                    </div>
                </div>
            `;
        });
    }

    if(Array.isArray(storedDesigns[id].variablesInHtml) && storedDesigns[id].variablesInHtml.length) {
        var variables = storedDesigns[id].variablesInHtml;

        const filteredVariables = variables.filter(function(element, index, self){
            return index === self.indexOf(element); 
        });

        filteredVariables.forEach(function(variable) {
            let inputMaxLen = '';
            const [variableName, blockKey] = variable.split('_');
            const isUserVariable = !loadedVariables[variableName];

            if(storedDesigns[id].inputCharacterLimit > 0) {
                inputMaxLen += `maxlength=${storedDesigns[id].inputCharacterLimit}`
            }

            if(isUserVariable) {
                variableContent += `
                    <div class="usr-variables" data-variable="${variable}">
                        <b>${variable}: ${storedDesigns[id].inputRequired ? '*' : ''}</b>
                        <div class="form-group">
                            <div class="form-line">
                                <input data-hj-allow type="text" id="${variable.toLowerCase()}" class="form-control t-variable" placeholder="${t('Start Typing')}..." required ${inputMaxLen} ${getPrefillValue(variable)}>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                autoVariables += `<li>${variable}</li>`;
                const blockValue = replaceTextVariables(`{{${variable}}}`, loadedVariables);

                if (blockKey && blockValue) {
                    variableContent += `
                        <div class="usr-variables hidden" data-variable="${variable}">
                            <input type="text" value="${blockValue}">
                        </div>
                    `
                }
            }
        });
    }

    if(Array.isArray(storedDesigns[id].detectedTextAreas) && storedDesigns[id].detectedTextAreas.length) {
        let filteredVariables = storedDesigns[id].detectedTextAreas.filter(function(element, index, self){ return index === self.indexOf(element); });
        filteredVariables.forEach(function(variable) {
            variableContent += `
                <div class="textarea-variables" data-variable="${variable}">
                    <b>${variable}: ${storedDesigns[id].inputRequired ? '*' : ''}</b>
                    <div class="form-group">
                        <div class="textarea-variable-trumbowyg">
                            ${getPrefillValue(variable, true)}
                        </div>
                    </div>
                </div>
            `;
        });
    }

    var largeDesignWarning = "";
    if(printHeight > 5000 || printHeight > 5000) largeDesignWarning = `
        <div class="alert alert-red">
            <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
            <b>${t('This is a large design')}</b>
            <br>
            <p style="font-weight: 300;">
                ${t('Renders may take from 30 seconds to several minutes to export. Remember you can always come back & download your exported design in the "Exports" tab in Marketing & Print after your exporting is complete.')}
            </p>
        </div>
    `;

    var renderOptions = `
            <div class="render-design-dialog ph-dialog-v2" style="padding: 20px;">
                ${largeDesignWarning}
    `;

    if(toggleContent !== "") {
        hasOptions = true;
        renderOptions += `<fieldset>
            <legend>${t('Toggle Design Elements')}</legend>
            <div class="row design-toggles">
                ${toggleContent}
            </div>
        </fieldset>`;
    }

    if(Array.isArray(storedDesigns[id].detectedTaglines) && storedDesigns[id].detectedTaglines.length) {
        const taglines = storedDesigns[id].detectedTaglines;

        renderOptions += `<fieldset class="render-design-tagline">
            <legend>${t('Design Tagline')}</legend>
            <p><b><i>${t('The following taglines can be selected')}:</i></b></p>
            <div class="tagline-option">
            `

            taglines.forEach(function(tagline) {
                renderOptions += `<div>
                    <input data-hj-allow name="render-tagline" type="radio" class="with-gap" id="${tagline.name}" value="${tagline.name}">
                    <label for="${tagline.name}">${tagline.value}</label>
                </div>`;
            })

        renderOptions += `</div></fieldset>`;
    }

    if(variableContent !== "" || autoVariables !== "") {
        hasOptions = true;
        renderOptions += `<fieldset>
            <legend>${t('Text Inputs & Variables')}</legend>
        `;

        if(variableContent !== "") renderOptions += `
            <div class="row text-variable-inputs">
                ${variableContent}
            </div>`;


        if(autoVariables !== "") renderOptions += `
            <p><b><i>${t('The following variables will be automatically set')}:</i></b><p>
            <ul>
                ${autoVariables}
            </ul>
        `;

        renderOptions += `</fieldset>`;
    }

    // Wrapper for design assets
    renderOptions += `<div class="render-design-assets"></div>`

    if(!hasOptions) {

        var scaleTo = (storedDesigns[id].scaleTo === undefined) ? 1 : storedDesigns[id].scaleTo;
        var printDimensions = (storedDesigns[id].width === undefined || storedDesigns[id].height === undefined) ? 'Automatic' : (storedDesigns[id].width * scaleTo) + 'x' + (storedDesigns[id].height * scaleTo);
        var printDPI = (storedDesigns[id].resolution === undefined) ? 75 : storedDesigns[id].resolution;
        let scalingText = '';
        
        if(storedDesigns[id].scaling) {
            scalingText = `<i>(${t('Device Scaling')}: ' + scaleTo + ':1)</i>`
        }

        renderOptions += `
            <p class="render-summary">
                <b>${t('Design will be exported with the following')}:</b> <br>
                ${printDimensions} @ ${printDPI} DPI ${scalingText}
            </p>
        `;
    }

    renderOptions += `
                <fieldset class="t-export-format toggle-formats">
                    <a class="btn btn-xs btn-default waves-effect expand-export-format"><i class="material-icons">keyboard_arrow_down</i><span>${t('Show Advanced')}</span></a>
                    <legend>${t('Export Format')}</legend>
                    <div class="row export-format">
                        <input data-hj-allow name="fileFormat" type="radio" id="jpg" value="jpg" class="with-gap radio-col-blue">
                        <label for="jpg">JPEG</label>

                        <input data-hj-allow name="fileFormat" type="radio" id="tiff" value="tiff" class="with-gap radio-col-blue">
                        <label for="tiff">TIFF</label>

                        <input data-hj-allow name="fileFormat" type="radio" id="png" value="png" class="with-gap radio-col-blue">
                        <label for="png">PNG</label>

                        <input data-hj-allow name="fileFormat" type="radio" id="pdf" value="pdf" class="with-gap radio-col-blue">
                        <label for="pdf">PDF</label>

                        <input data-hj-allow name="fileFormat" type="radio" id="psd" value="psd" class="with-gap radio-col-blue">
                        <label for="psd">PSD</label>

                    </div>
                    <div class="row colour-mode-selector">
                        <b>${t('Colour Mode')}:</b>
                        <select name="colour-mode">
                            <option value="RGB">RGB</option>
                            <option value="CMYK">CMYK</option>
                        </select>
                    </div>
                    <div class="row colour-profile-selector">
                        <b>${t('Colour Profile')}:</b>

                        <select name="colour-profile-rgb">
                            <option value="AdobeRGB1998">Adobe RGB (1998)</option>
                            <option value="sRGB">sRGB IEC61966-2.1</option>
                            <option value="AppleRGB">Apple RGB</option>
                            <option value="ProPhoto">KODAK ProPhoto RGB</option>
                        </select>

                        <select name="colour-profile-cmyk">
                            <option value="CoatedFOGRA39">Coated FOGRA39 (ISO 12647-2:2004)</option>
                            <option value="USWebCoatedSWOP">U.S. Web Coated (SWOP) v2</option>
                            <option value="PSO_Uncoated_ISO12647_eci">PSO Uncoated ISO12647 (ECI)</option>
                            <option value="JapanColor2001Coated">Japan Color 2001 Coated</option>
                            <option value="JapanColor2003WebCoated">Japan Color 2003 Web Coated</option>
                            <option value="ISOcoated_v2_300_eci">ISO Coated v2 300% (ECI)</option>
                        </select>
                    </div>

                    <br><div class="help-subtext">
                        ${t('If you are unsure what Colour profile to use, please speak with your local print supplier BEFORE exporting, this will ensure that your print supplier will have colours according to our brand guidelines.')}
                    </div>

                </fieldset>
            </div>
    `;

    var lJQ = $;
    let renderParams = {};
    swalWithBootstrapButtons.fire({
        html: renderOptions,
        width: 500,
        title: t('Render Design for Export'),
        animation: false,
        showCloseButton: true,
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: t("Export"),
        cancelButtonText: t("Cancel"),
        onOpen: () => {

            $.ajax({
                url: '/api/marketing/assets/get',
                method: 'GET',
                // data: JSON.stringify({ tags }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    const assets = []

                    response.map(resp => {
                        resp.tags = JSON.parse(resp.tags)
                        assets.push(resp)
                    })

                    let assetHTML = '';
                    storedDesigns[id].assetsInHtml.map(assetInHtml => {
                        const assetName = assetInHtml.name.split(" ").join("")

                        const filteredAssets = assets.filter(o => {
                            return $.inArray(assetInHtml.tag, o.tags) !== -1 && (o.brand.toLowerCase() === selectedBrand.toLowerCase() || o.brand === "ALL")
                        });

                        if (filteredAssets.length) {
                            assetHTML += `<fieldset> 
                                <legend>${assetInHtml.name.split('-').join(" ")}</legend>
                                <div class="asset-container" data-name="${assetName}" style="display: flex; overflow-y: scroll;">
                            `

                            filteredAssets.map(asset => {
                                assetHTML += `
                                    <div data-img="${asset.url}" class="asset-holder${assetInHtml.id === asset.id ? ' selected': ''}">
                                        <img src=${asset.thumbnail} alt=${asset.name}/>
                                        <i class="material-icons">check_box</i>
                                    </div>
                                `;
                            })
                            assetHTML += `</div></fieldset>`
                        }

                        $('.render-design-assets').html(assetHTML)
                    })

                    $('.asset-holder').click(function(){
                        const parent = $(this).parent().data('name')
                        $('[data-name='+ parent + ']').find('.asset-holder').removeClass('selected')
                        $(this).addClass('selected')
                    })
                },
                error: function(error) {
                    console.log(error)
                }
            });
        },
        onBeforeOpen: () => {

            // Setup WYSIWYG editor if it's there..
            $(".textarea-variable-trumbowyg").trumbowyg({
                svgPath: '/assets/images/trumbowyg.svg',
                autogrow: true,
                removeformatPasted: true,
                autogrowOnEnter: true,
                defaultLinkTarget: '_blank',
                tagsToRemove: ['script', 'link', 'iframe', 'audio', 'image', 'video', 'basefont', 'base', 'body', 'bb', 'button', 'canvas', 'code', 'embed', 'frame', 'frameset', 'link', 'form', 'input', 'map', 'meta', 'noframes', 'noscript', 'object', 'textarea', 'track'],
                btns: [
                    // ['historyUndo', 'historyRedo'],
                    ['fontsize'],
                    ['foreColor'],
                    ['strong', 'italic', 'underline', 'strikethrough'],
                    ['emoji'],
                    ['unorderedList', 'orderedList'],
                ],
                plugins: {
                    //colors: {
                    //    colorList:[
                    //        'ff0000', '0000ff', '000000', 'E72419', '003186',  '002369', '4FC7E7', '00CAD4'
                    //    ],
                    //},
                    fontsize: {
                        //sizeList: [
                        //    '14px',
                        //    '18px',
                        //    '22px'
                        //],
                        allowCustomSize: false
                    }
                }
            });

            $('.tagline-option div').first().find('input[type=radio]').prop("checked", true);

            $('#available_assets').html(`<p>${t('Loading')}.....</p>`);

            $(".expand-export-format").click(function() {
                $('.t-export-format').toggleClass('toggle-formats', 100);
                if($('.t-export-format').hasClass('toggle-formats')) {
                    $(".expand-export-format span").html(t("Hide Advanced"));
                    $(".expand-export-format i").html("keyboard_arrow_up");
                } else {
                    $(".expand-export-format span").html(t("Show Advanced"));
                    $(".expand-export-format i").html("keyboard_arrow_down");
                }
            })

            // Set the default format...
            var defaultFormat = (storedDesigns[id].format === undefined) ? "jpg" : storedDesigns[id].format;

            $("input[name=fileFormat][value=" + defaultFormat + "]").attr('checked', 'checked');
            displayColourFormats(defaultFormat)

            //Bind 'material design' to elements...
            $("body").addClass("materialize");
            $(".nav-help-container").show();

            $( "select[name=colour-mode]").change(function() {
                setColourMode(this.value);
            });

            $('input[name=fileFormat]').change(function() {
                displayColourFormats(this.value)
            });

            function displayColourFormats(ext) {
                switch(ext) {
                    case "jpg":
                        enableCMYK(true);
                        setColourMode("RGB");
                        break;
                    case "tiff":
                        enableCMYK(true);
                        setColourMode("CMYK");
                        break;
                    case "png":
                        enableCMYK(false);
                        setColourMode("RGB");
                        break;
                    case "pdf":
                        enableCMYK(true);
                        setColourMode("CMYK");
                        break;
                    case "psd":
                        enableCMYK(true);
                        setColourMode("CMYK");
                        break;
                    default:
                        enableCMYK(true);
                }

                function enableCMYK(enabled) {
                    //console.log(enabled);
                    if(enabled) {
                        $("select[name=colour-mode] option[value=CMYK]").removeAttr('disabled');
                    } else {
                        $("select[name=colour-mode] option[value=CMYK]").attr('disabled','disabled');

                        // Select RGB (if not already selected);
                        setColourMode("RGB");
                    }
                }
            }

            function setColourMode(mode) {
                switch(mode) {
                    case "RGB":
                        $("select[name=colour-profile-rgb]").show();
                        $("select[name=colour-profile-cmyk]").hide();

                        $("select[name=colour-mode] option[value=RGB]").attr('selected','selected');
                        $("select[name=colour-mode] option[value=CMYK]").removeAttr('selected');
                        break;

                    case "CMYK":
                        $("select[name=colour-profile-cmyk]").show();
                        $("select[name=colour-profile-rgb]").hide();

                        $("select[name=colour-mode] option[value=CMYK]").attr('selected','selected');
                        $("select[name=colour-mode] option[value=RGB]").removeAttr('selected');
                        break;
                }
            }

            function validate() {
                var isValid = true;

                // if(! $(".doc-name").val()) isValid = false;

                if(isValid) {
                    $('.swal2-actions .swal2-confirm').prop('disabled', false);
                } else {
                    $('.swal2-actions .swal2-confirm').prop('disabled', true);
                }
            }

            validate();

            // Set the default view/layout based on what's currently checked by default...
            displayColourFormats($('input[name=fileFormat]:checked').val())
            
            // If we're trying to auto-render from the GJS editor...
            if(auto) {
                $(".render-design-dialog").parent().parent().parent().find(".swal2-confirm").click();
            }
        },
        preConfirm: () => {
            //Render validations
            let isValid = true;

            $('.usr-variables').each(function () {
                const variableId = $(this).data('variable').toLowerCase();
                const inputValue = $(this).find('input').val();

                if (storedDesigns[id].inputRequired) {
                    $(this).find(`#${variableId}-error`).remove();
                    $(this).find('.form-line').removeClass('error');

                    if (inputValue === '') {
                        $(this).find('.form-line').addClass('error');
                        $(this).find('.form-group').append(`
                            <label id="${variableId}-error" class="error" for="${variableId}">
                                ${t('This field is required')}.
                            </label>
                        `);

                        isValid = false;
                    }
                }
            });

            // trigger render if fields are valid
            if(isValid) {
                renderParams = triggerRender(id);
            }

            return isValid;
        },
        onClose: function() {
            $(".nav-help-container").show();
            $('body').addClass('materialize')
        }
    }).then(response => {

        // console.log(response)
        if(response.value !== undefined && response.value == true) renderDesign(renderParams);
    });

}

function triggerRender(id) {

    let toggles = [];
    $('.t-container').each(function () {
        toggleObj = {
            name: $(this).data('toggle'),
        };

        if ($(this).find('input[type="checkbox"]').is(':checked')) {
            toggleObj.value = true;
        } else {
            toggleObj.value = false;
        }

        toggles.push(toggleObj);
    });

    let usrVariables = [];
    $('.usr-variables').each(function () {
        variableObject = {
            name: $(this).data('variable'),
            value: $(this).find('input').val(),
        };

        usrVariables.push(variableObject);
    });

    let designAssets = {};
    $('.render-design-assets')
        .children()
        .each(function () {

            const name = $(this).find('.asset-container').data('name');
            const url = $(this).find('.asset-holder.selected').data('img');

            designAssets[name] = url;
        });

    let textAreaVariables = [];
    $('.textarea-variables').each(function () {
        textAreaVariables.push({
            name: $(this).data('variable'),
            value: $(this).find('.textarea-variable-trumbowyg').trumbowyg('html'),
        });
    });

    renderParams = {
        id: id,
        fileName: storedDesigns[id].name,
        fileFormat: $('input[name=fileFormat]:checked').val(),
        toggles: toggles,
        taglineOption: $('.tagline-option div').find('input[name="render-tagline"]:checked').val(),
        userVariables: usrVariables,
        textAreaVariables: textAreaVariables,
        designAssets: designAssets,
        club: selectedID,
        colorMode: $('.colour-mode-selector option:selected').val(),
        colorProfile: null,
    };

    if (renderParams.colorMode == 'RGB') {
        renderParams.colorProfile = $('.colour-profile-selector select[name=colour-profile-rgb] option:selected').val();
    } else {
        renderParams.colorProfile = $(
            '.colour-profile-selector select[name=colour-profile-cmyk] option:selected'
        ).val();
        if (renderParams.fileFormat == 'png' && renderParams.colorMode == 'CMYK') {
            // Not possible to export CMYK with PNG files...
            renderParams.colorMode = 'RGB';
            renderParams.colorProfile = 'AdobeRGB1998';
        }
    }

    return renderParams
}

function renderDesign(params, auto=false) {
    // Renders a design (via the render API) and then saves to disk...
    console.log({renderDesign: params})

    // Socket will only download the file if the session tab ID matches...
    const newMPSessionId = uuidv4();
    const mpSessionIds = JSON.parse(getSessionStorage('mpSessionIds') || '[]'); 
    mpSessionIds.push(newMPSessionId);
    setSessionStorage('mpSessionIds', JSON.stringify(mpSessionIds));

    mp_displayRenderFileModal = displayRenderFileModal

    const renderProgress = Swal.mixin({
        toast: true,
        position: 'bottom-end',
        showConfirmButton: false,
        showCancelButton: false,
        timer: 10000,
        onOpen: (toast) => {
            Swal.toggleTimer()
            console.log('stop timer')
        }
    });

    renderProgress.fire({
        title: `
            <div style="max-width: 250px; text-align: left;">
                <div style="float: left; width: 100%; font-weight: bold; font-size: 15px; padding-top: 5px; padding-left: 5px; margin-top: -40px;">
                    <img style="float: left; margin-top: 10px; height:18px; margin-right: 5px;" src="/assets/images/tail-spin.svg">
                    ${t('Rendering Design')}
                </div>
                <div style="margin-top: 10px; float: left; width: 100%; font-size: 10px; font-style: italic;">${t('You may continue doing other tasks.')}\n\n${t('If you close this window the design will be available for download when completed in the <b>"Renders/Exports"</b> tab.')}</div>
            </div>
        `
    })

    let req = $.ajax({
        url: '/api/marketing/designs/dynamic/render-design',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ ...params, sessionId: newMPSessionId }),
        success: function(res){
            try {
                if(res) {

                    // TODO: Move logic for downloading designs to websocket response
                    // renderProgress.close();

                    //if(auto) {
                    //    displayRenderFileModal(res.location, res.metadata);
                    //} else {
                    //    mp_downloadFile(res.location, res.metadata)
                    //}

                } else {
                    instantNotification(
                        // Message...
                        `<b>${t('Design Render Failed')}:</b><br>${(res.error == undefined) ? t('Contact Administrator for Details.') : res.error }`,
                        // Type...
                        'error',
                        // Timeout (30sec)...
                        30000
                    );
                }
            } catch (e) {
                instantNotification(
                    // Message...
                    `<b>${t('Design Render Failed')}:</b><br>${(res.error == undefined) ? t('Contact Administrator for Details.') : e }`,
                    // Type...
                    'error',
                    // Timeout (30sec)...
                    30000
                );
                console.log(e)
            }
        },
        error: function(xhr, textStatus, errorThrown){

            renderProgress.close();
            console.log(xhr, textStatus, errorThrown);

            instantNotification(
                // Message...
                `<b>${t('Design Render Failed')}'</b><br>${textStatus}`,
                // Type...
                'error',
                // Timeout (30sec)...
                30000
            );
        }
    });

    function displayRenderFileModal(url, metadata) {
        var progressBar = '<div class="progress">' +
            '<div class="progress-bar progress-bar-info progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">' +
            '</div>' +
        '</div>';

        swalWithBootstrapButtons.fire({
            title: `${t('Render Preview')}: [${metadata.fileName}] ${t('Using Club')}: [${selectedID}]`,
            html: `<div class="quick-render-preview"><img src="${url}"></div>`,
            width: 'auto',
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false
        });
    }

}

function mp_downloadFile(url, metadata) {

    let r = renderProgressbar();

    // Use XMLHttpRequest instead of Jquery $ajax - https://stackoverflow.com/questions/28165424/download-file-via-jquery-ajax-post
    xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {


        var a;
        if (xhttp.readyState === 4 && xhttp.status === 200) {

            console.log(xhttp)

            // Trick for making downloadable link
            a = document.createElement('a');

            a.href = window.URL.createObjectURL(xhttp.response);

            // Give filename you wish to download
            a.download = metadata.fileName.replace(/[^a-z0-9]/gi, '_').toLowerCase() + "." + metadata.fileFormat;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();

            // hide render progress...
            r.close();
            // Progress bar library has an annoying bug that adds CSS to the body element each time and does not properly remove it, causing our UI to break...
            setTimeout(function(){
                $("body").css({ 'padding-right' : '' });
            }, 500);


        }
        if (xhttp.readyState === 4 && xhttp.status !== 200) {
            instantNotification(
                // Message...
                `<b>${t('Failed to export design. Please contact HQ with the design you are trying to export for details.')}`,
                // Type...
                'error',
                // Timeout (30sec)...
                30000
            );
        }
    };

    // Post data to URL which handles post request
    xhttp.open("GET", url);
    xhttp.responseType = 'blob';
    xhttp.send();

    // xhttp.setRequestHeader("Content-Type", "application/json");
    // You should set responseType as blob for binary responses
}

function renderProgressbar() {
    var progressBar = '<div class="progress">' +
        '<div class="progress-bar progress-bar-info progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">' +
        '</div>' +
    '</div>';

    swalWithBootstrapButtons.fire({
        title: `${t('Downloading design file')}...`,
        html: "<div class='preload-progress-bar'>" + progressBar + "</div>",
        width: 500,
        showCloseButton: true,
        showConfirmButton: false,
        showCancelButton: false,
        onOpen: () => {

            var count = 0;
            var innterval = setInterval(function() {
                if (count == 100)
                    clearInterval(innterval);
                count++;
                $(".preload-progress-bar .progress-bar").attr('aria-valuenow', count).css('width', count + "%");
            }, 20);

        }
    });

    return swalWithBootstrapButtons;
}


function loadGJSVariables(callback) {

    var req = $.ajax({
        url: '/api/marketing/variables/' + selectedID + '/list',
        method: 'GET',
        success: function(data){

            try {
                callback(data.Variables)
            } catch (e) {
                callback(false);
            }
        },
        error: function(xhr, textStatus, errorThrown){
            console.log(xhr, textStatus, errorThrown);
            callback(false);
        }
    });

}

function loadDesign(id, showUI, callback) {

    var req = $.ajax({
        url: `/api/marketing/designs/get/${id}`,
        method: 'GET',
        success: function(data){

            try {
                if(data) {
                    gjs.CssComposer.getAll().reset();
                    gjs.setComponents(data.html);
                    deviceName = String(uuidv4())

                    gjs.DeviceManager.add(deviceName, data.width, {
                        height: data.height
                    });
                    gjs.setDevice(deviceName);

                    $(".document-display-title").html(`${data.name} <i class="fa fa-edit"></i>`);

                    // Bring in doc default - then add any data from the API...
                    // This allows for backwards compatibility from old designs not yet migrated in our DB...

                    resetDocObject()
                    documentObject = { ...documentObject, ...data }

                    if(showUI) {
                        $('.nav-tabs a[href="#editor"]').tab('show');
                        $("#nav-editor-tab").show();

                        // There's a JS Library somewhere that keeps fucking adding padding to the UI!....
                        $("body").css({ 'padding-right' : '' });

                    }

                    // Toggle crop/bleed markers...
                    toggleCropMarkers(null);

                    // New doc - set state to unsaved, false...
                    setTimeout(function(){ docUnsaved = false; }, 500);

                    callback(true);
                }
            } catch (e) {
                callback(false);
            }
        },
        error: function(xhr, textStatus, errorThrown){
            console.log(xhr, textStatus, errorThrown);
            callback(false);
        }
    });


}

function grapeJS() {

    let editor = grapesjs.init({

        canvas: {
            // Include 12RND/UBX Gotham font in all canvas's...
            styles: [
                'https://content.gymsystems.co/hub/design-assets/fonts/gotham/stylesheet.css',
                'https://fonts.googleapis.com/css?family=Roboto:400,700'
            ]
        },

        noticeOnUnload: 0,
        container: '#gjs',

        fromElement: true,
        showOffsets: 1,
        avoidInlineStyle: 1,
        showDevices: false,

        // Allow in-line JS
        allowScripts: 1,

        // Creates a "photoshop" style layout allowing users to drag elements etc - Use toggleAbsoluteMode(true/false) to set state
        //dragMode: 'absolute',

        // Editor should be 100% of parent
        height: '100%',
        width: '100%',

        colorPicker: {
            appendTo: 'parent',
            offset: { top: 26, left: -166, },
        },

        assetManager: {
            //assets: [],
            multiUpload: false,
            dropzone: 1,
            upload: '/api/marketing/assets/upload',
            uploadName: 'file',
            addBtnText: 'Link File',
        },

        // Keymaps are used for shortcuts...
        keymaps: {
           defaults: {
               'gjs:zoomOut': {
                   keys: '⌘+⌥+-',
                   handler: function () {
                       zoomOut();
                   }
               },
               'gjs:zoomIn': {
                   keys: '⌘+⌥+=',
                   handler: function () {
                       zoomIn();
                   }
               },
               'gjs:help': {
                   keys: '⌘+/',
                   handler: function () {
                       editorHelpMode(true);
                   }
               },
               'gjs:render': {
                   keys: '⌘+⌥+r',
                   handler: function () {
                       quickRenderDocument(documentObject.id, true);
                   }
               }
           }
        },

        plugins: [
            'gjs-blocks-basic',
            'grapesjs-style-filter',
            'gjs-style-gradient',
            'grapesjs-tui-image-editor',
            'grapesjs-plugin-export',
            'gjs-preset-webpage',
            'grapesjs-custom-code',
            'grapesjs-rulers',
        ],

        pluginsOpts: {
            'grapesjs-custom-code': {
                "placeholderContent": `<span>${t('Please replace this span with your custom code')}</span>`,
                "toolbarBtnCustomCode": { label: '</>', attributes: { title: t('Open custom code') } },
                "modalTitle": t("Custom Code"),
            },

            // Add custom styles etc into designer...
            'gjs-preset-webpage': {
                // Disable the following 'bundled' plugins by setting them to false/empty objs..
                formsOpts: {},
                blocksBasicOpts: {},
                countdownOpts: {},
                navbarOpts: {},
                aviaryOpts: false,
                filestackOpts: false,

                customStyleManager: [
                    {
                        name: 'Typography',
                        open: false,
                        buildProps: ['font-fadesignIDy', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow'],
                        properties: [
                            { name: 'Font', property: 'font-family'},
                            { name: 'Weight', property: 'font-weight'},
                            { name:  'Font color', property: 'color'},
                            {
                                property: 'text-align',
                                type: 'radio',
                                defaults: 'left',
                                list: [
                                    { value : 'left',  name : 'Left',    className: 'fa fa-align-left'},
                                    { value : 'center',  name : 'Center',  className: 'fa fa-align-center' },
                                    { value : 'right',   name : 'Right',   className: 'fa fa-align-right'},
                                    { value : 'justify', name : 'Justify',   className: 'fa fa-align-justify'}
                                ],
                            },
                            {
                                property: 'text-decoration',
                                type: 'radio',
                                defaults: 'none',
                                list: [
                                    { value: 'none', name: 'None', className: 'fa fa-times'},
                                    { value: 'underline', name: 'underline', className: 'fa fa-underline' },
                                    { value: 'line-through', name: 'Line-through', className: 'fa fa-strikethrough'}
                                ],
                            },
                            {
                                property: 'text-shadow',
                                properties: [
                                    { name: 'X position', property: 'text-shadow-h'},
                                    { name: 'Y position', property: 'text-shadow-v'},
                                    { name: 'Blur', property: 'text-shadow-blur'},
                                    { name: 'Color', property: 'text-shadow-color'}
                                ],
                            }
                        ]
                    }
                ],
            },

            // Content blocks / types available to drop into the design...
            'gjs-blocks-basic': {
                blocks: [
                    'column1',
                    'column2',
                    'column3',
                    'column3-7',
                    'text',
                    'image'
                ]
            },

            // Export plugin
            'grapesjs-plugin-export': {},

            // Allows for image editor as part of the app
            'grapesjs-tui-image-editor': {
                config: {
                    includeUI: {
                        initMenu: 'filter',
                    },
                },
                icons: {
                    'menu.normalIcon.path': '/assets/images/svg/icon-d.svg',
                    'menu.activeIcon.path': '/assets/images/svg/icon-b.svg',
                    'menu.disabledIcon.path': '/assets/images/svg/icon-a.svg',
                    'menu.hoverIcon.path': '/assets/images/svg/icon-c.svg',
                    'submenu.normalIcon.path': '/assets/images/svg/icon-d.svg',
                    'submenu.activeIcon.path': '/assets/images/svg/icon-c.svg',
                },
            },

        },

        // Allow saving and undo/redo history in localstorage...
        storageManager: {
            id: 'gjs-',             // Prefix identifier that will be used on parameters
            type: 'local',          // Type of the storage
            autosave: true,         // Store data automatically
            autoload: true,         // Autoload stored data on init
            stepsBeforeSave: 1,     // If autosave enabled, indicates how many changes are necessary before store method is triggered
        },

        styleManager : {
            sectors: [{
                    name: 'General',
                    open: false,
                    buildProps: ['float', 'display', 'position', 'top', 'right', 'left', 'bottom']
                },{
                    name: 'Flex',
                    open: false,
                    buildProps: ['flex-direction', 'flex-wrap', 'justify-content', 'align-items', 'align-content', 'order', 'flex-basis', 'flex-grow', 'flex-shrink', 'align-self']
                },{
                    name: 'Dimension',
                    open: false,
                    buildProps: ['width', 'height', 'max-width', 'min-height', 'margin', 'padding'],
                },{
                    name: 'Typography',
                    open: false,
                    buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-shadow'],
                },{
                    name: 'Decorations',
                    open: false,
                    buildProps: ['border-radius-c', 'background-color', 'border-radius', 'border', 'box-shadow', 'background'],
                },{
                    name: 'Extra',
                    open: false,
                    buildProps: ['transition', 'perspective', 'transform'],
                }
            ],
        },

    });

    let pn = editor.Panels;
    let modal = editor.Modal;
    let cmdm = editor.Commands;

    // Add Tooltips to Layout...
    let shortcuts = {};
    let keyCmd = 'Ctrl';
    let keyShift = 'Shift';

    if(getOS() == 'mac') {
        keyCmd = '\u2318';
        keyShift = '\u21E7';
    }

    shortcuts.undo = keyCmd + ' + Z';
    shortcuts.redo = keyCmd + ' + ' + keyShift + ' + Z';
    shortcuts.zoomOut = `⌥ + ${keyCmd} + -`;
    shortcuts.zoomIn = `⌥ + ${keyCmd} + =`;
    shortcuts.help = `${keyCmd} + ?`;
    shortcuts.render = `⌥ + ${keyCmd} + r`;

    // Add in 'club map/directions component'
    editor.BlockManager.add('map', {
        label: t('Club Map'),
        attributes: { class:'fa fa-map-o' },
        content: {
          type: 'map',
          style: { height: '350px' },
          mapType: 'q', // q - Roadmap / w - Satellite
          address: '{{ClubAddress}}',
          zoom: 13, // max 20
        },
    });


    // Rules support ... https://github.com/Ju99ernaut/grapesjs-rulers
    editor.on('run:preview', () => editor.stopCommand('ruler-visibility'));


    // Todo - add functionality to add/remove rulers...
    // editor.Panels.addButton('options', [ { id: 'rulers', className: 'fa fa-ellipsis-h icon-blank', command: function(editor, sender) { alert('Toggle Rulers') }, attributes: { title: 'Toggle Rulers' } }, ]);

    editor.Panels.addButton('options', [

        {
            id: 'clear',
            label: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M500 850c12.9 0 23.3-10.4 23.3-23.3v-490c0-12.9-10.4-23.3-23.3-23.3s-23.3 10.4-23.3 23.3v490c0 12.8 10.4 23.3 23.3 23.3zm-116.7 0c12.9 0 23.3-10.4 23.3-23.3l-23.3-490c0-12.9-10.4-23.3-23.3-23.3s-23.3 10.4-23.3 23.3l23.3 490c0 12.8 10.4 23.3 23.3 23.3zM920 173.3H710v-70c0-51.5-41.8-93.3-93.3-93.3H383.3c-51.5 0-93.3 41.8-93.3 93.3v70H80c-12.9 0-23.3 10.4-23.3 23.3s10.4 23.3 23.3 23.3h97.9l65.5 676.7c0 51.6 41.8 93.3 93.3 93.3h326.7c51.6 0 93.3-41.8 93.3-93.3L822.1 220H920c12.9 0 23.3-10.5 23.3-23.3 0-12.9-10.4-23.4-23.3-23.4zm-583.3-70c0-25.8 20.9-46.7 46.7-46.7h233.3c25.8 0 46.7 20.9 46.7 46.7v70H336.7v-70zM710 896.7c0 25.8-20.9 46.7-46.7 46.7H336.7c-25.8 0-46.7-20.9-46.7-46.7L224.5 220h551L710 896.7zM616.7 850c12.9 0 23.3-10.4 23.3-23.3l23.3-490c0-12.9-10.4-23.3-23.3-23.3s-23.3 10.4-23.3 23.3l-23.3 490c-.1 12.8 10.4 23.3 23.3 23.3z"/></svg>`,
            context: 't-clear',
            className: 'gjs-fixed-toolbar-icon',
            togglable: false,
            active: false,
            command: {
                run: function (e) {

                    if(confirm(t('Are you sure you want to clear the design?'))) {
                        gjs.DomComponents.clear();
                        // setTimeout(function(){ localStorage.clear()}, 0)
                    }
                },
            },
            attributes: {
                title: t('Clear Design')
            }
        },
        {
            id: 'ruler-visibility',
            label: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M74 71H44V11c0-3.31-2.69-6-6-6H26c-3.31 0-6 2.69-6 6v78c0 3.31 2.69 6 6 6h48c3.31 0 6-2.69 6-6V77c0-3.31-2.69-6-6-6zm2 18c0 1.1-.9 2-2 2h-7v-6c0-1.1-.9-2-2-2s-2 .9-2 2v6h-6v-2c0-1.1-.9-2-2-2s-2 .9-2 2v2h-6v-6c0-1.1-.9-2-2-2s-2 .9-2 2v6h-6v-2c0-1.1-.9-2-2-2s-2 .9-2 2v2h-7c-1.1 0-2-.9-2-2v-7h2c1.1 0 2-.9 2-2s-.9-2-2-2h-2v-6h6c1.1 0 2-.9 2-2s-.9-2-2-2h-6v-6h2c1.1 0 2-.9 2-2s-.9-2-2-2h-2v-6h6a2 2 0 100-4h-6v-6h2c1.1 0 2-.9 2-2s-.9-2-2-2h-2v-6h6c1.1 0 2-.9 2-2s-.9-2-2-2h-6v-6h2c1.1 0 2-.9 2-2s-.9-2-2-2h-2v-7c0-1.1.9-2 2-2h12c1.1 0 2 .9 2 2v62c0 1.1.9 2 2 2h32c1.1 0 2 .9 2 2v12z"/></svg>`,
            className: 's-ruler-icon',
            command: 'ruler-visibility',
            togglable: true,
            active: false,
            context: 'toggle-rulers', //prevents rulers from being toggled when another views-panel button is clicked 
            attributes: {
                title: t('Toggle Rulers')
            }
        },
        {
            id: 'cropmarks',
            context: 't-cropmarks',
            label: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 290.642 290.642"><path d="M44.995.001a5 5 0 00-4.924 5.07v35h-35a5 5 0 100 10h40a5 5 0 005-5v-40a5 5 0 00-5.076-5.07zm100.25 0a5 5 0 00-4.924 5.07v35h-20a5.001 5.001 0 10-.001 10h50a5.001 5.001 0 10.001-10h-20v-35a5 5 0 00-5.076-5.07zm100.25 0a5 5 0 00-4.924 5.07v40a5 5 0 005 5h40a5.001 5.001 0 10.001-10h-35v-35a5 5 0 00-5.077-5.07zm-200.5 115.25a5 5 0 00-4.924 5.07v20h-35c-6.762-.096-6.762 10.096 0 10h35v20a5.001 5.001 0 1010 .001v-50a5 5 0 00-5.076-5.071zm200.5 0a5 5 0 00-4.924 5.07v50a5.001 5.001 0 1010 .001v-20h35c6.762.096 6.762-10.096 0-10h-35v-20a5 5 0 00-5.076-5.071zM5.071 240.571a5.001 5.001 0 10-.001 10h35v35a5.001 5.001 0 1010 .001v-40a5 5 0 00-5-5H5.071zm115.25 0a5.001 5.001 0 10-.001 10h20v35c-.096 6.762 10.096 6.762 10 0v-35h20a5.001 5.001 0 10.001-10h-50zm125.25 0a5 5 0 00-5 5v40a5.001 5.001 0 1010 .001v-35h35a5.001 5.001 0 10.001-10h-40.001z"/></svg>`,
            className: 's-cropmarks-icon',
            togglable: true,
            active: false,
            command: {
                run: function (e) {
                    toggleCropMarkers(true);
                },
                stop: function (e) {
                    toggleCropMarkers(false);
                }
            },
            attributes: {
                title: t('Toggle Crop Marks')
            }
        },
        {
            id: 'absoluteMode',
            context: 't-absoluteMode',
            label: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><path d="M388.7 12.95c-1.03 1.55-1.55 27.85-1.55 58.53.52 59.31 1.55 64.72 12.63 61.37 7.22-2.32 8.25-8.25 8.25-50.54V44.92l44.35 44.35 44.61 44.61-4.64 6.96c-12.64 19.34-22.17 55.18-18.82 68.85 1.81 7.22 10.57 11.09 15.47 6.96 1.81-1.55 4.13-10.57 4.9-19.86 5.93-63.18 70.66-100.57 126.87-73.23 15.21 7.48 34.55 27.33 41.52 42.55 16.25 35.33 7.74 75.81-21.66 102.37-5.67 5.16-14.18 11.35-18.31 13.67-8 3.87-9.29 7.48-6.45 15.21 2.32 6.19 11.6 4.9 24.75-3.35l11.6-7.48 46.16 46.16 46.16 46.16h-41.52c-41 0-47.45 1.29-47.45 9.28 0 1.8 1.81 5.16 4.13 7.22 3.35 3.61 12.38 4.13 58.8 4.13 43.58 0 55.7-.77 58.02-3.61 2.06-2.32 2.84-20.37 2.32-59.57-.77-50.28-1.29-56.47-5.41-59.05-3.09-2.06-5.67-2.06-9.03 0-3.87 2.58-4.38 8.25-5.16 45.13l-.77 42.03-45.9-45.9-46.16-46.16 7.48-11.6c11.6-18.31 16.25-35.33 15.99-58.54 0-23.21-4.9-39.97-17.02-58.79l-7.74-12.64 42.8-42.8 43.06-43.06v41.52c0 41 1.29 47.45 9.28 47.45 1.81 0 5.16-1.81 7.22-4.13 3.61-3.35 4.12-12.38 4.12-58.79 0-43.58-.77-55.7-3.61-58.02-2.32-2.06-20.37-2.84-59.57-2.32-50.28.77-56.47 1.29-59.05 5.16-2.06 3.35-2.06 5.93 0 9.03 2.58 4.13 8.51 4.9 44.61 5.93l41.52 1.29-42.29 42.29-42.29 42.03-20.11-9.8c-19.6-9.8-20.63-10.06-48.48-10.06-27.08 0-29.66.52-45.39 8.25-9.28 4.64-18.82 9.8-21.4 11.6-3.87 2.84-9.54-1.81-48.74-41.26l-44.61-44.35h39.2c21.4 0 40.74-.77 43.06-1.55 2.32-1.03 4.13-4.64 4.13-8.77s-1.81-7.74-4.13-8.77C497 8.83 390.51 9.86 388.7 12.95z"/><path d="M552.96 164.58c-7.48 2.58-17.53 8.51-22.43 13.15-5.67 5.42-35.58 59.05-86.64 155.23l-78.4 146.98-3.09-24.24c-4.64-34.81-13.67-56.21-30.68-72.2-23.72-22.17-65.24-20.63-87.93 3.35-15.21 15.99-16.5 23.21-17.53 83.29-.26 32.75-2.32 66.27-4.9 84.06-8.77 60.08-9.8 74.01-8.51 99.28 1.81 31.72 10.31 65.76 26.3 104.44 6.71 16.25 13.41 32.75 14.96 36.62 2.58 6.19 1.81 9.28-5.67 23.72-9.54 18.56-9.28 28.11.51 37.65C259 865.71 495.46 990 503.97 990c11.86 0 16.5-3.87 28.11-23.46 7.22-12.38 14.44-20.89 22.43-26.3 68.33-47.19 104.18-86.13 137.96-148.79 6.45-12.12 22.95-47.19 36.62-78.13 13.41-30.95 32.23-72.2 41.51-91.54 15.21-31.46 17.02-37.13 17.02-51.06.26-34.04-27.08-62.4-60.08-62.92H718l2.32-10.31c2.58-11.09-.26-28.88-6.7-41-9.04-17.55-36.88-33.79-58.03-33.79-6.19 0-6.96-1.29-9.8-15.47-5.93-28.37-28.11-47.71-58.28-51.31l-14.18-1.55 30.17-56.73 30.17-56.73v-18.05c-.26-29.65-15.99-51.06-43.58-59.31-16.24-4.9-20.37-4.65-37.13 1.03zm30.17 40.22c7.99 3.87 10.57 8.51 10.57 18.31 0 3.35-27.85 58.54-62.14 122.74-34.3 64.21-62.66 120.17-63.18 124.55-2.58 16.5 17.28 34.04 34.81 30.69 11.34-2.32 18.31-11.35 41.51-55.18 11.6-21.92 23.47-42.03 26.3-44.61 7.99-6.71 19.34-5.67 28.11 3.09 4.13 4.13 7.48 10.31 7.48 13.41 0 3.09-8.77 22.18-19.34 42.55-21.91 41.77-23.46 48.99-11.85 60.59 5.93 5.67 10.05 7.48 17.79 7.48 13.67 0 18.83-4.9 33.52-31.98 14.7-27.59 21.92-33.78 35.33-31.46 11.09 2.06 20.37 14.44 18.31 25.01-.77 3.61-8.25 19.85-17.02 35.84-18.31 34.04-20.11 40.23-14.44 51.06 5.16 10.06 11.34 13.41 23.72 13.41 10.31 0 15.21-4.38 32.75-28.37 6.96-9.54 10.06-11.86 17.54-12.38 6.19-.52 11.35 1.03 15.73 4.38 12.38 9.8 11.6 15.47-8 56.73-9.8 20.63-25.01 53.89-33.78 73.75-40.74 93.35-72.72 137.44-134.35 185.66-31.2 24.75-42.81 36.87-54.15 56.73-4.38 7.74-9.03 14.7-10.06 15.47-1.29.77-49.25-23.72-106.75-54.15l-104.44-55.7 5.16-10.83c7.22-16.5 6.19-27.85-5.67-55.44-34.29-80.45-38.16-108.3-26.82-192.62 4.9-36.87 7.22-66.01 7.74-103.15 1.03-49.25 1.29-51.83 6.71-56.21 15.21-12.12 28.62-7.22 39.71 14.96l7.74 15.47v55.46c0 30.43 1.03 58.02 2.06 60.85 2.58 7.48 14.96 12.38 25.53 10.06 12.38-2.84 37.13-29.14 58.79-62.14 21.14-32.49 73.23-135.12 108.04-213.77 13.41-29.91 28.11-62.15 33.01-71.69 10.82-21.66 19.33-26.3 34.03-18.57z"/></svg>`,
            className: 's-absolute-mode-icon',
            // className: 'fa fa-mouse-pointer icon-blank',
            togglable: true,
            active: false,
            command: {
                run: function (e) {
                    toggleAbsoluteMode(true);
                },
                stop: function (e) {
                    toggleAbsoluteMode(false);
                }
            },
            attributes: {
                title: t('Absolute Mode (Allows Drag & Drop of Elements)')
            }
        },
        {
            id: 'zoomOut',
            className: 'fa fa-search-minus',
            togglable: false,
            active: false,
            command: {
                run: function () {
                    zoomOut();
                }
            },
            attributes: {
                title: `${t('Zoom Out')} (${shortcuts.zoomOut})`
            }
        },
        {
            id: 'zoomIn',
            className: 'fa fa-search-plus',
            togglable: false,
            active: false,
            command: {
                run: function () {
                    zoomIn();
                }
            },
            attributes: {
                title: `${t('Zoom In')} (${shortcuts.zoomIn})`
            }
        },
        {
            id: 'quickRender',
            className: 's-quick-render-icon',
            context: 't-quick-render',
            label: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M384 121.9c0-6.3-2.5-12.4-7-16.9L279.1 7c-4.5-4.5-10.6-7-17-7H256v128h128v-6.1zM192 336v-32c0-8.84 7.16-16 16-16h176V160H248c-13.2 0-24-10.8-24-24V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V352H208c-8.84 0-16-7.16-16-16zm379.05-28.02l-95.7-96.43c-10.06-10.14-27.36-3.01-27.36 11.27V288H384v64h63.99v65.18c0 14.28 17.29 21.41 27.36 11.27l95.7-96.42c6.6-6.66 6.6-17.4 0-24.05z"/></svg>`,
            togglable: false,
            active: false,
            command: {
                run: function () {
                    quickRenderDocument(documentObject.id, true);
                }
            },
            attributes: {
                title: `${t('Preview Render Document')} (${shortcuts.render})`
            }
        },
        {
            id: 'editorHelp',
            className: 'fa fa-question-circle',
            togglable: false,
            active: false,
            command: {
                run: function () {
                    editorHelpMode(true)
                }
            },
            attributes: {
                title: `${t('Templating Guide')} (${shortcuts.help})`
            }
        },
    ]);

    function zoomOut() {
        let currentZoom = gjs.Canvas.getZoom();
        if(currentZoom > 10) gjs.runCommand('set-zoom', {zoom: (Number(currentZoom) - 10)})

        setTimeout(function() {
            gjs.Panels.getButton('options', 'zoomOut').set('active', 0)
        }, 100);
    }

    function zoomIn() {
        let currentZoom = gjs.Canvas.getZoom();
        if(currentZoom < 400) gjs.runCommand('set-zoom', {zoom: (Number(currentZoom) + 10)})

        setTimeout(function() {
            gjs.Panels.getButton('options', 'zoomIn').set('active', 0)
        }, 100);
    }

    // Finally re-render the UI
    //editor.Panels.render();

    // editor.BlockManager.add('testBlock', {
    //     label: 'Block',
    //     attributes: { class:'gjs-fonts gjs-f-b1' },
    //     content: `<div style="padding-top:50px; padding-bottom:50px; text-align:center">Test block</div>`
    // })

    // Add custom panels...
    // editor.Panels.addPanel({
    //   id: 'additional-actions',
    //   el: '.panel__additional-actions',
    //   buttons: [
    //     {
    //       id: 'visibility',
    //       active: true, // active by default
    //       className: 'btn-toggle-borders',
    //       label: '<u>B</u>',
    //       command: 'sw-visibility', // Built-in command
    //     }
    //   ],
    // });

    // Disable selecting the 'body' element (we want this to feel more like a design editor, not a HTML editor...)
    editor.DomComponents.getWrapper().set({
        badgable: false,
        selectable: false,
        hoverable: false,
        name: "Canvas Root",
        removable: false,
        draggable: false,
        highlightable: false,
        resizable: false,
        layerable: false,
        editable: false,
    });

    [
        ['sw-visibility', t('Show Layer/Object Borders')],
        ['preview', t('Preview')],
        ['fullscreen', 'Fullscreen'],
        ['undo', t('Undo') + '(' + shortcuts.undo + ')'], ['redo',  t('Redo') + '(' + shortcuts.redo + ')'],
        //['code', 'Canvas Code']
        // ['canvas-clear', 'Clear canvas'],
        ['gjs-open-import-webpage', t('Import HTML')],
        ['export-template', t('View Canvas Code')]
    ]
    .forEach(function(item) {
        pn.getButton('options', item[0]).set('attributes', {title: item[1], 'data-tooltip-pos': 'bottom'});
    });

    [
        ['open-sm', t('Style Manager')],
        ['open-layers', t('Layers')],
        ['open-blocks', t('Blocks')]
    ]
    .forEach(function(item) {
        pn.getButton('views', item[0]).set('attributes', {title: item[1], 'data-tooltip-pos': 'bottom'});
    });

    var titles = $('#gjs *[title]');
    for (var i = 0; i < titles.length; i++) {
        var el = titles[i];
        el.setAttribute('data-toggle', 'tooltip');
    }

    // Render tooltips...
    $('#gjs [data-toggle="tooltip"]').tooltip({ container: 'body' });

    // Change names of some of the layout..
    $(".gjs-device-label").text("Layout");

    // Add hook to allow 'resize' of elements when selected...
    editor.on("component:selected", function(args) { args.set("resizable", true); });

    // Hide/show document title when going into small 'preview' mode...
    editor.on('run:preview', () => {
        $('.document-display-title').hide();
    });
    editor.on('stop:preview', () => {
        $('.document-display-title').show();
    });

    $(".gjs-editor .gjs-pn-btn.fa.fa-trash").hide();

    // Show borders by default
    pn.getButton('options', 'sw-visibility').set('active', 0);
    gjs = editor;


    //const someEl = document.querySelector('.gjs-frame');
    //canvasSize.observe(someEl);

    // Load in our custom variables...
    loadGJSVariables(function(response) {
        if(response) {
            // remove any previously loaded variables...
            editor.RichTextEditor.remove('custom-vars');

            var customVariablesHTML = '<select class="gjs-field"><option value="">- '+t('Add Variable')+ '-</option>';

            response.forEach(function(variable) {
                customVariablesHTML += '<option value="{{' + variable.variable + '}}">' + variable.variable + '</option>'
            });

            customVariablesHTML += '</select>';

            editor.RichTextEditor.add('custom-vars', {
                icon: customVariablesHTML,
                // Bind the 'result' on 'change' listener
                event: 'change',
                result: (rte, action) => rte.insertHTML(action.btn.firstChild.value),
                // Reset the select on change
                update: (rte, action) => { action.btn.firstChild.value = "";}
            })
        }
    });

    // Inject Document Title Placeholder
    if($('.gjs-editor .document-display-title').length  == 0) {
        $(".gjs-editor").prepend('<div class="document-display-title">xxx</div>');
    }

    // Disable scrollbar INSIDE the editor..
    var editorFrame = $(".gjs-cv-canvas iframe");
    editorFrame.contents().find('html').css("overflow", "hidden");

    // Create 'transparent squares' in the background (like photoshop)...
    editorFrame.contents().find('body').css({
        'background-color': '#FEFEFE',
        'background-image': 'linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB), linear-gradient(45deg, #CBCBCB 25%, transparent 25%, transparent 75%, #CBCBCB 75%, #CBCBCB)',
        'background-size': '30px 30px',
        'background-position': '0 0, 15px 15px',
    });

    // Reload assets when upload finished - or GJS first loaded...
    loadGJSAssets();
    editor.on('asset:upload:end', () => {
        loadGJSAssets();
    });

    editor.on('change:changesCount', e => {
        docUnsaved = true;
    });

}

function loadGJSAssets() {

    // console.log("Load Assets!");
    var req = $.ajax({
        url: '/api/marketing/assets/list',
        method: 'GET',
        success: function(data){

            const am = gjs.AssetManager;
            //console.dir(data);
            var amData = data;

            // Remove any loaded assets from asset manager...
            data.forEach(function(item) {
                am.remove(item.src)
            });
            am.getAll().models.forEach(function(model) {
                am.remove(model.attributes.src)
            });

            // Add in new data from API response.
            am.add(amData);

        },
        error: function(xhr, textStatus, errorThrown){
            console.log(xhr, textStatus, errorThrown);
            //callback(false);
        }
    });

}

function toggleAbsoluteMode(state) {
    if(state == true) {
        // gjs.getModel().set('dmode', 'absolute');
        gjs.editor.setDragMode('absolute')
    } else {
        // gjs.getModel().set('dmode', 'translate');
        gjs.editor.setDragMode('none')
    }
}

function toggleCropMarkers(state) {

    var cropMarkers, fixedConfiguration = {
        badgable: false,
        selectable: false,
        hoverable: false,
        name: t("Crop Marks"),
        removable: false,
        draggable: false,
        droppable: false,
        stylable: false,
        highlightable: false,
        copyable: false,
        resizable: false,
        layerable: false,
        editable: false,
    };

    // Draw cropmarkers directly onto the canvas... (they will be saved as HTML in the design, and rendered via chrome)
    if(state == true) {
        // If cropmarkers already exist, bind to them...
        cropMarkers = gjs.DomComponents.getWrapper().find('.crop-marks')[0];
        var cropImage = 'https://content.gymsystems.co/hub/design-assets/crop.svg'

        // Otherwise create new elements...
        if(cropMarkers === undefined) {
            cropMarkers = gjs.addComponents(`
                <div class="crop-marks">
                    <img style="z-index: 9999; height: 28.81px; width: 28.81px; position: absolute; top: 0; left: 0;" src="${cropImage}">
                    <img style="z-index: 9999; height: 28.81px; width: 28.81px; transform: rotate(90deg); position: absolute; top: 0; right: 0;" src="${cropImage}">
                    <img style="z-index: 9999; height: 28.81px; width: 28.81px; transform: rotate(180deg); position: absolute; bottom: 0; right: 0;" src="${cropImage}">
                    <img style="z-index: 9999; height: 28.81px; width: 28.81px; transform: rotate(270deg); position: absolute; bottom: 0; left: 0;" src="${cropImage}">
                </div>
            `);
        }

        cropMarkers = gjs.DomComponents.getWrapper().find('.crop-marks')[0];
        cropMarkers.set(fixedConfiguration);

        // Set "name" attribute in layer manager...
        for (i = 0; i < 4; i++) {
            cropMarkers.attributes.components.models[i].set(fixedConfiguration);
        }

    } else if(state == null) {
        cropMarkers = gjs.DomComponents.getWrapper().find('.crop-marks')[0];
        if(cropMarkers !== undefined) {
            //toggleCropMarkers(true)
            const cropBtn = gjs.Panels.getButton('options', 'cropmarks');
            cropBtn.set('active', 1)
        }
    } else {
        // Remove Crop Markers...
        cropMarkers = gjs.DomComponents.getWrapper().find('.crop-marks')[0];
        if(cropMarkers !== undefined) cropMarkers.remove();
    }

}

function bindZIndexes() {
    $('.gjs-frame').css('z-index', -1);

    $("#gjs").mousedown(function() {
        $('.gjs-frame').css('z-index', 1);
    });

    $("#gjs").mouseup(function() {
        $('.gjs-frame').css('z-index', -1);
    });

    $(".rul_ruler").mousedown(function() {
        setTimeout(function(){ $('.gjs-frame').css('z-index', -1); }, 10)
    });
}

function setGroupingTags(selectedTags, type='design') {

    // Grouping Tags Select Multiple Autocomplete
    const tags = Object.keys(designGroups)

    $('.doc-tags, .select-tags').select2({
        placeholder: t('Add or Select one or more Folders'),
        multiple: true,
        tags: tags,
        allowClear: true,
        width: '100%',
        dropdownCssClass: `${type}-tags__dropdown`
    })

    if(selectedTags.length) {
        let existingTags = []
        let addedTags = ''

        selectedTags.forEach(function(tag) {

            // Check if newly added tag is exists on the current tag
            // If not, add an option then append the new option
            if($.inArray(tag, tags) !== -1) {
                existingTags.push(tag)
            } else {
                addedTags += `<option value=${tag} selected>${tag}</option>`
                existingTags.push(tag)
            }
        })

        $('.doc-tags, .select-tags').val(existingTags).trigger('change')
        $('.doc-tags, .select-tags').append(addedTags).trigger('change')
    }
}
