let panicButtonSVG = `<svg style="height: 25px; margin-top: -3px; margin-bottom: -3px; filter: invert(48%) sepia(10%) saturate(510%) hue-rotate(185deg) brightness(91%) contrast(90%);" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 574 574"><path d="M402.9 545.8H171.1c-33.1 0-60-26.9-60-60V88.2c0-33.1 26.9-60 60-60h231.8c33.1 0 60 26.9 60 60v397.7c0 33.1-26.8 59.9-60 59.9z" fill="none" stroke="#000" stroke-width="35" stroke-miterlimit="10"/><circle cx="287" cy="168" r="77"/><path fill="none" stroke="#000" stroke-width="20" stroke-miterlimit="10" d="M287 385.7V441"/></svg>`;
let skeletonLoader = $('#skeleton-loader');
let devicesTable = null;
const defaultDeviceFilters = {
  range: {   
      text: 'This Week',
      value: 'this week'
  },
  type: {
      text: 'All',
      value: 'all',
  },
  connection: {
      text: 'All',
      value: 'all',
  }
};
const defaultDeviceFiltersString = JSON.stringify(defaultDeviceFilters);
let deviceSearch = '';
const filtersNotDefault = (jsonDeviceFilters) => {
  const defaultDeviceFilters = JSON.parse(defaultDeviceFiltersString)

  if(!jsonDeviceFilters || !jsonDeviceFilters?.type) return false;

  const result = jsonDeviceFilters.type.value !== defaultDeviceFilters.type.value || jsonDeviceFilters.range.value !== defaultDeviceFilters.range.value || jsonDeviceFilters.connection.value !== defaultDeviceFilters.connection.value
  return result
}

function getKnownDeviceBugs(device) {
    let bugs = [];

    // Only proceed if the device type is 'camera'
    if (device?.deviceType === 'camera') {
        // Check if TPU is not found
        if (device?.metadata?.systemStats?.tpuInfo?.tpuFound === false) {
            bugs.push(t('AI Accelerator Chip Not Found - AI enabled features will not function on this device, including facial recognition.'));
        }

        // Check for the Sandisk SD card with 99GB capacity
        if (device?.metadata?.sdMetrics?.sdCards?.mmcblk1?.decodedCID?.MID === 'Shenzen Market' && 
            device?.metadata?.sdMetrics?.sdCards?.mmcblk1?.capacity === '38.16Gbyte') {
            bugs.push(t('Bad SD Card Detected - This device contains an SD card that may experience performance issues over time. For optimal performance and reliability, please contact support to discuss replacement options.'));
        }

        // Check for a possible fan fault if the total clock speed is less than 6GHz
        // console.log('clockspeed', parseFloat(device.metadata.systemStats.cpu.clockSpeedInfo.totalClockSpeedGHz))
        if (
            device?.metadata?.systemStats?.cpu?.clockSpeedInfo?.totalClockSpeedGHz &&
            parseFloat(device.metadata.systemStats.cpu.clockSpeedInfo.totalClockSpeedGHz) < 5
        ) {
            const cpuTemperature = device?.metadata?.systemStats?.cpu?.temperature;
            if (cpuTemperature && cpuTemperature > 75) {
                bugs.push(
                    t('Possible Fan Fault Detected - The device may be overheating due to a fan failure. This will reduce its performance and can cause overheating. Please contact support to organize a replacement.')
                );
            }
        }
    }

    // Return the bug messages as a bullet point list (or null if no bugs)
    return bugs.length ? '• ' + bugs.join("\n\n• ") : null;
}

$.sDevices = {};
$.sDevices.base = {
    activeTab: localStorage.activeTab || 'devices', 
    isInitialised: false,
    refreshDevicesLoaded: false,
    currentScreenshot: null,
    cctvOverheatingTemperature: 77,
    knownBuildVersions: [],
    drawDeviceType: function(icon) {
        var iconLowerCase = icon ? icon.toLowerCase() : '';
        switch(iconLowerCase) {
            case 'camera':
                return '<i class="fa-solid fa-video"></i>'
                break;
            case 'screen':
                return '<i class="fa-solid fa-display"></i>'
                break;
            case 'duress':
                return `<i style="zoom: 0.7">${panicButtonSVG}</i>`
                break;
            default:
                return '';
        }
    },
    lastRequestedRouterTunnel: null,
    changeWifiSpinner: function() {
        const changeWifiSpinner = Swal.mixin({
            toast: true,
            position: 'bottom-end',
            showConfirmButton: false,
            showCancelButton: false,
            timer: 10000,
            onOpen: (toast) => {
                Swal.toggleTimer()
            }
        });

        changeWifiSpinner.fire({
            title: `
                <div style="max-width: 250px; text-align: left;">
                    <div style="float: left; width: 100%; font-weight: bold; font-size: 15px; padding-top: 5px; padding-left: 5px; margin-top: -40px;">
                        <img style="float: left; margin-top: 10px; height:18px; margin-right: 5px;" src="/assets/images/tail-spin.svg">
                        ${t('Changing Wifi Networks')}
                    </div>
                    <div style="margin-top: 10px; float: left; width: 100%; font-size: 10px; font-style: italic;">${t('Change wifi network request sent. Please allow 2-3 minutes for changes to take effect and be reflected in UI.')}</div>
                </div>
            `
        })
    },
    getMetadataBySerialNumber: function (serialNumber, callback) {
        $.ajax({
            url: `/api/devices/metadata/serial/${serialNumber}`,
            method: 'GET',
            contentType: 'application/json',
            success: function(metadata) {
                callback(null, metadata);
            },
            error: function(xhr, textStatus, errorThrown) {
                callback(errorThrown, {});
            }
        });
    },
    getPercentClass: function (percent) {

        if(percent < 40) {
            return "bad"
        }
        if(percent < 50) {
            return "warning-bad"
        }
        if(percent < 60) {
            return "warning"
        }
        if(percent < 80) {
            return "ok-medium"
        }
        return "ok"
    },
    checkBtnIsOnline: function (device) {

        if(device.deviceType !== "duress") return false
        
        const lastUpdated = device.metadata && device.metadata.updated ? device.metadata.updated : false
        
        const result = lastUpdated && moment(lastUpdated).isAfter(moment().subtract(16, 'minutes'))

        return result
    },
    getSignalQuality: function (device) {
        let percentage = 0
        if(!device || !device.metadata) return percentage

        if (device.deviceType === 'duress') {
            return $.sDevices.base.rssiToPercent(device.metadata.aveDeviceStats?.rssi);
        }

        return device.metadata.wifi?.quality ?? 0;
    },
    returnEquipmentID: function (id) {
        switch(id) {
            case '1':
                return t('Equipment: #1 - Boxing 1')
                break;
            case '2':
                return t('Equipment: #2 - Boxing 2')
                break;
            case '3':
                return t('Equipment: #3 - Floor to ceiling')
                break;
            case '4':
                return t('Equipment: #4 - Strength 1')
                break;
            case '5':
                return t('Equipment: #5 - Strength 2')
                break;
            case '6':
                return t('Equipment: #6 - Speedball')
                break;
            case '7':
                return t('Equipment: #7 - Sled')
                break;
            case '8':
                return t('Equipment: #8 - Boxing 4')
                break;
            case '9':
                return t('Equipment: #9 - Boxing 5')
                break;
            case '10':
                return t('Equipment: #10 - Cell')
                break;
            case '11':
                return t('Equipment: #11 - Strength 3')
                break;
            case '12':
                return t('Equipment: #12 - Strength 4')
                break;
            case 'warmup1':
                return t('Equipment: Warm-up')
                break;
            case 'relay1':
                return t('Equipment: Relay Controller')
                break;

            default:
                return id;
        }
    },
    rssiToPercent: function (rssi) {
        if(!rssi) return 0
        // / Nordic nRF52 series
        let minRssi = -110; // define minimum RSSI
        let maxRssi = -30;  // define maximum RSSI
        let percent = 0; 
      
        if (rssi >= maxRssi) {
          percent = 100;
        } else if (rssi <= minRssi) {
          percent = 0;
        } else {
          percent = Math.floor(((rssi - minRssi) / (maxRssi - minRssi)) * 100);
        }
      
        return percent;
    },
    // No Devices UI
    noDevices: function () {

        $("#ics-devices-container").empty();
        $("#ics-devices-container").html(`
            <div class="no-devices-table-message">
                <h4>
                    ${t('There are no devices setup yet for this location')}
                </h4>
            </div>
        `);
        skeletonLoader.hide();
        $('#devices').LoadingOverlay("hide");
    },
    setNetworkOptions: function(data) {

        const networks = data.items.map( item => {
            const value = $.sDevices.base.getDeviceSSID(item);
            return value;
        });

        $.sDevices.base.deviceOptions.connection = [
            {
                text: t('All'),
                value: 'all'
            },
            ...[...new Set(networks)].map(value => ( {
                text: value,
                value,
            }))
        ]

        $.sDevices.base.renderOptions('connection', $('#filter-connection .filter__options'));
    },
    changeWifiNetworks: function(deviceID) {
        let metadata
        $.sDevices.base.fetchDeviceStats((deviceMetadata) => {
            // console.log('deviceMetadata', deviceMetadata);
            let currentDevice = deviceMetadata.items.find(item => {
                return item.balenaUUID === deviceID || item.deviceID === deviceID;
            });
            
            metadata = currentDevice.metadata;
            console.log('currentDevice', currentDevice, 'metadata', metadata)
            changeWifiNetworksSetup();

        });

        function changeWifiNetworksSetup() {

            function returnSignalStrength(quality) {
                let waveStrength;

                if(quality.between(0, 25)) waveStrength = 1;
                if(quality.between(26, 50)) waveStrength = 2;
                if(quality.between(51, 75)) waveStrength = 3;
                if(quality.between(75, 100)) waveStrength = 4;

                return `<div class="waveStrength-${waveStrength}">
                  <div class="wv4 wifiWave" style="">
                    <div class="wv3 wifiWave" style="">
                      <div class="wv2 wifiWave" style="">
                        <div class="wv1 wifiWave">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>`;
            }

            var discoveredWifiNetworks = `
                <select
                    data-live-search="true"
                    title="${t('Select a network to connect to')}..."
                    class="selected-ssid"
                >
                `
            var availableNetworks = (metadata === undefined || metadata.wifiNetworks === undefined) ? false : metadata.wifiNetworks;

            if(availableNetworks) {
                availableNetworks.forEach(function(network) {
                    //console.log(network);
                    if(network.ssid) discoveredWifiNetworks += `
                        <option

                            data-channel="${network.channel}"
                            data-mac="${network.mac}"
                            data-quality="${network.quality}"
                            data-frequency="${network.frequency}"
                            data-security="${network.security}"

                            value="${network.ssid}"
                            data-content='
                                <div class="wifi-option">
                                    <div class="col-sm-10">
                                        <div class="row">
                                            <p>${network.ssid.replace(/'/g, '&apos;')}</p>
                                        </div>
                                        <div class="row">
                                            <div class="macaddr">${network.mac}</div>
                                            <span class="badge bg-wifi-${ (network.frequency === undefined || network.frequency == '') ? '2' : String(network.frequency).slice(0, 1) }">${(network.frequency === undefined || network.frequency == '') ? '' : String(network.frequency).slice(0, 2).splice(1, 0, ".")} GHz</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="row security-stats">
                                            ${ (network.security === undefined || network.security == "") ? '<div class="wifi-ssid-secured"><i class="material-icons"></i></div>' : `
                                                <div class="wifi-ssid-secured">
                                                    <i class="material-icons">lock</i>
                                                </div>
                                            ` }
                                            <div class="wifi-strength-wrapper">
                                                <div class="wifi-strength">${ returnSignalStrength(network.quality) }</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            '
                        >${network.ssid}</option>
                    `;
                });
            }

            discoveredWifiNetworks +=`</select>`;


            var changeWifiNetworksHTML = `
            <div class="change-wifi-networks-dialog ph-dialog-v2" style="padding: 20px;">
                <fieldset class="ph-fieldset">
                    <div class="row">
                        <div class="col-xs-12 col-sm-5">
                            ${t('Network SSID')}:
                        </div>
                        <div class="col-xs-12 col-sm-7">
                            ${discoveredWifiNetworks}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 col-sm-5"></div>
                        <div class="col-xs-12 col-sm-7">
                            <div class="ssid-info"></div>
                        </div>
                    </div>
                    <div class="row password-input">
                        <div class="col-xs-12 col-sm-5">
                            ${t('Password')}:
                        </div>
                        <div class="col-xs-12 col-sm-7">
                            <input class="ssid-password" type="password" value="" placeholder="">
                            <button type="button" class="btn btn-xs btn-primary btn-outline waves-effect toggle-ssid-password" style="height: 95%; border-radius: 0px; padding-right: 2px;">
                                <i class="material-icons">remove_red_eye</i>
                            </button>
                        </div>
                    </div>
                </fieldset>

                <div class="alert light-info">
                    ${t('Note your device will connect to the new network. If your network details are incorrect or the new network does not have Internet access, the device will automatically re-connect to your original network after a few minutes.')}
                </div>

            </div>`;

            let changeWifiDeviceObj;

            swalWithBootstrapButtons.fire({
                title: t('Network Setup'),
                html: changeWifiNetworksHTML,
                width: 500,
                showCloseButton: false,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonText: t("Change"),
                cancelButtonText: t("Cancel"),
                onBeforeOpen: () => {

                    $('.selected-ssid').selectpicker();

                    $(".selected-ssid").on("change", function() {
                        validate();
                        var selected = $(this).find('option:selected');
                        if(selected[0].dataset.mac === undefined && selected[0].dataset.channel === undefined) {
                            $(".ssid-info").html(``);
                        } else {
                            $(".ssid-info").html(`
                                <span>
                                    ${t('Channel')}: ${ (selected[0].dataset.channel === undefined) ? t("Unknown") : selected[0].dataset.channel }
                                    <span class="badge bg-wifi-${ (selected[0].dataset.frequency === undefined || selected[0].dataset.frequency == '') ? '2' : String(selected[0].dataset.frequency).slice(0, 1) }">${(selected[0].dataset.frequency === undefined || selected[0].dataset.frequency == '') ? '' : String(selected[0].dataset.frequency).slice(0, 2).splice(1, 0, ".")} GHz</span>
                                </span>
                                <br>
                                <span>
                                    ${t('Mac')}: ${ (selected[0].dataset.mac === undefined) ? t("Unknown") : selected[0].dataset.mac }
                                </span>
                                <span>
                                <br>
                                    ${t('Signal Strength')}: ${ (selected[0].dataset.quality === undefined) ? "0" : selected[0].dataset.quality }%
                                </span>
                            `);
                        }
                    });
                    $(".ssid-password").keyup(function() {
                        validate();
                    });
                    $(".toggle-ssid-password").click(function() {
                        // Toggle between plain text and password mode...
                        if ($(".ssid-password")[0].type === "password") {
                            $(".ssid-password")[0].type = "text";
                        } else {
                            $(".ssid-password")[0].type = "password";
                        }
                    });
                    validate();

                    function validate() {
                        let selectedSSID = $(".selected-ssid").find('option:selected');

                        let isValid = true;
                        let selectedSecurity = (selectedSSID[0].dataset.security === undefined) ? '' : selectedSSID[0].dataset.security

                        if(! $(".selected-ssid")[1].value) isValid = false;
                        // Only check if password is required if the network is a network with a password field required...
                        if(selectedSecurity) {
                            $(".password-input").show();
                            if(! $(".ssid-password").val()) isValid = false;
                            // WEP minimum password characters is 5 - WPA is 8
                            if($(".ssid-password").val().length < 5) isValid = false;
                        } else {
                            $(".password-input").hide();
                        }

                        if(isValid) {
                            $('.swal2-actions .swal2-confirm').prop('disabled', false);
                        } else {
                            $('.swal2-actions .swal2-confirm').prop('disabled', true);
                        }
                    }
                },
                onClose: () => {
                    let selectedSSID = $(".selected-ssid").find('option:selected');
                    changeWifiDeviceObj = {
                        uuid: deviceID,
                        ssid: $(".selected-ssid")[1].value,
                        apMacAddr: selectedSSID[0].dataset.mac,
                        password: $(".ssid-password").val()
                    }
                }
            }).then(response => {
                if (response.value) {
                    changeDeviceSSID(changeWifiDeviceObj);
                }
            });

            function changeDeviceSSID(params) {
                // console.dir(params);

                var req = $.ajax({
                    url: `/api/ics/devices/changewifi/${selectedID}`,
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(params),
                    success: function(data){
                        if((data === undefined) ? false : data !== false) {
                            $.sDevices.base.changeWifiSpinner();
                        } else {
                            if(data.error) {
                                instantNotification(
                                    // Message...
                                    `<b>$${t('FAILED TO MODIFY DEVICE!')}</b><br><br>${data.error}`,
                                    // Type...
                                    'error',
                                    // Timeout (1m)...
                                    60000
                                );
                            } else {
                                instantNotification(
                                    `<b>${t('FAILED TO MODIFY DEVICE!')}</b><br><br>${t('An error occurred while trying change Wifi Configuration... Please try again later')}`,
                                    'error',
                                    60000
                                );
                            }
                            console.dir(response);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown){
                        console.error(xhr, textStatus, errorThrown);

                        if(xhr.responseJSON.error) {
                            instantNotification(
                                `<b>${t('FAILED TO MODIFY DEVICE!')}</b><br><br>${xhr.responseJSON.error}`,
                                'error',
                                60000
                            );
                        } else {
                            instantNotification(
                                `<b>${t('FAILED TO MODIFY DEVICE!')}</b><br><br>${t('An error occurred while trying change Wifi Configuration... Please try again later')}`,
                                'error',
                                60000
                            );
                        }
                    }
                });

            }
        }

    },
    renderDevices: function (data) {
        $("#ics-devices-container").empty();
        

        
        $("#ics-devices-container").html(`

             <table id="ics-devices-table" width="100%" class="table devices-table">
                <thead>
                    <tr>
                        <th>
                            <div class="device-screen-table__header">
                                <span>${t('Device')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="hidden-xs">
                            <div class="device-screen-table__header">
                                <span>${t('Metadata')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="visible-xl">
                            <div class="device-screen-table__header">
                                <span>${t('Local IP')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="hidden-sm hidden-xs">
                            <div class="device-screen-table__header">
                                <span>${t('Signal Strength')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="visible-xl">
                            <div class="device-screen-table__header">
                                <span>${t('Activity Down/Up')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="hidden-xs">
                            <div class="device-screen-table__header">
                                <span>${t('Temperature')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="hidden-xs">
                            <div class="device-screen-table__header">
                                <span>${t('Uptime')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                        <th class="visible-xl">
                            <div class="device-screen-table__header device-screen-table__header--justify-end">
                                <span>${t('Serial Number')}</span>
                                <span class="device-screen-table__header__icon"></span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        // Reset devices obj...
        currentDevicesObj = {};

        // render filter options
        $.sDevices.base.renderOptions('range', $('#filter-range .filter__options'));
        $.sDevices.base.renderOptions('type', $('#filter-device-type .filter__options'));
        $.sDevices.base.renderOptions('connection', $('#filter-connection .filter__options'));
        
        // hide loader for filters
        $('#device-filters').removeClass('isLoading');

        if(filtersNotDefault($.sDevices.base.deviceFilters)) {
          $('#reset-filter').removeClass('hidden');
        }

        // reset function
        $('#reset-filter').on('click', function() {
          $.sDevices.base.deviceFilters = JSON.parse(defaultDeviceFiltersString);
          localStorage.setItem('deviceFilters', defaultDeviceFiltersString);
          $.sDevices.base.renderDevices($.sDevices.base.deviceData);
          $(this).addClass('hidden');
        });

        // Helper function to format text that should be in an "alert" state.
        function formatTemperatureText(temp, alarm) {
            // Convert temp to a number
            let tempNumber = parseFloat(temp);
            
            // Check if it's a valid number after conversion
            if (isNaN(tempNumber)) {
                // console.error('Invalid temperature:', temp);
                return t('Unknown');
            }
            // Chances are it's also unknown
            if(tempNumber == 0) {
                return t('Unknown');
            }

            let formattedTemp = (tempNumber % 1 === 0) ? Math.round(tempNumber) : tempNumber.toFixed(1);

            // Check if the temperature is over 82°C or if the alarm is triggered in the metadata
            let isAlertState = alarm || tempNumber > 82;

            if (isAlertState) {
                return `<span style="color: red; font-weight: bold; ">${ formatTemperature(formattedTemp, globalStore.activeUnitOfMeasurement) }</span>`;
            } else {
                return formatTemperature(formattedTemp, globalStore.activeUnitOfMeasurement);
            }
        }

        // Helper function to determine which temperature to use for the device alerts
        function getTemperature(device) {
            if (device.deviceType === 'duress' && device.metadata.aveDeviceStats) {
                return device.metadata.aveDeviceStats.deviceTemperature;
            } else {
                let chipsetTemp = device?.metadata?.systemStats?.chipsetTemperature;
                if (chipsetTemp === false || chipsetTemp === null || chipsetTemp === undefined) {
                    return device?.metadata?.systemStats?.cpu?.temperature ?? 0;
                }
                return chipsetTemp;
            }
        }

        // filtered items
        const filteredItems = data.items
            .filter( item => {
                let result = true;

                const range = $.sDevices.base.deviceFilters?.range.value;
                const { metadata = {}, updated: updatedParent = false } = item || {};
                const { isOnline, updated: updatedMetaData = false } = metadata || {};
                const updated = updatedMetaData || updatedParent;
                
                if(range !== 'all') {
                    switch (range) {
                        case 'only online':
                            result = isOnline || $.sDevices.base.checkBtnIsOnline(item);
                            break;

                        case 'this week':

                            const startOfWeek = moment().startOf('week');
                            const endOfWeek = moment().endOf('week');
                            result = updated && moment(updated).isBetween(startOfWeek, endOfWeek, undefined, '[]');

                            break;

                        case 'last 30 days':

                            const prevMonth = moment().subtract(30, 'day').startOf('week');

                            result = updated && moment(updated).isBetween(prevMonth, moment(), undefined, '[]');
                            
                            break;
                    
                        default:
                            break;
                    }
                }

                return result;
            }).filter(item => {
                let result = true;
                const type = $.sDevices.base.deviceFilters?.type.value;

                if(type !== 'all') {
                    result = item.deviceType === type
                }

                return result;
                
            }).filter( item => {
                let result = true;
                const connection = $.sDevices.base.deviceFilters?.connection.value;

                if(connection !== 'all') {
                    const value = $.sDevices.base.getDeviceSSID(item)

                    result = connection === value;
                }

                return result;
            })

        filteredItems.forEach(function(device) {

            let deviceStatus = (device.metadata === undefined || device.metadata.isOnline === undefined) ? false : device.metadata.isOnline;

            let alarmTriggerd = device.metadata &&
                device.metadata.alarmStatuses &&
                (
                    (device.metadata.alarmStatuses['single-press'] && device.metadata.alarmStatuses['single-press'].alarmTriggered) || 
                    (device.metadata.alarmStatuses['double-press'] && device.metadata.alarmStatuses['double-press'].alarmTriggered)
                ) && device.metadata.alarmStatuses['long-press'] && !device.metadata.alarmStatuses['long-press'].alarmTriggered ? true : false;

            let temperature = getTemperature(device);
            let isOverheating = device?.metadata?.systemStats?.overheatingAlarm;

            // There's no 'isoverheating' logic for CCTV yet - so for now we'll just used the default thermal throttle temperature...
            if(device.deviceType === 'camera' && (temperature > $.sDevices.base.cctvOverheatingTemperature)) { // 77c devices throttle - 85c recording will stop - 95c - devices will reach critical shutdown temperature
                isOverheating = true;
            }

            let statusTags = '';

            // Additional device status tags
            if (device.metadata?.systemStats?.systemUpdates === "downloading") {
                statusTags += `
                    <div class="device-cell__tag device-cell__tag--updates">
                        <span>${t('Updates Available')}</span>
                        <i></i>
                    </div> `;
            }
            if (device.metadata?.isProvisioning === true) {
                statusTags += `
                    <div class="device-cell__tag device-cell__tag--provisioning">
                        <span>${t('Provisioning')}</span>
                        <i></i>
                    </div> `;
            }

            const averageStats = device.metadata.aveDeviceStats || {};
            const isOrganisationAdmin = signedInUserData?.organisations?.find((org) => org.organisationId === selectedOrgID)?.isOrganisationAdmin || false;

            let metaData = `
                <tr 
                    class="device d-status-${ deviceStatus } ${ (alarmTriggerd == true) ? 'alarm-triggered' : ''} ${ (isOverheating == true) ? 'alarm-triggered' : ''}" 
                    data-deviceid="${ (device.deviceID === undefined) ? ((device.balenaUUID === undefined) ? device.deviceSerialNumber : device.balenaUUID) : device.deviceID }"
                    >
                    <td class="device-status-badges device-cell" data-order="${device.sortOrder}" data-search="${device.searchData}">
                        <div class="device-cell__device">
                            <div class="device-cell__device__icon ${device.deviceType}">
                                ${device.deviceType}
                            </div>
                            <div class="device-cell__device__details">
                                <p class="device-cell__device__details__name">${ (device.displayName === undefined) ? "" : device.displayName}${ (device.deviceName === undefined) ? "" : device.deviceName}${ (device.deviceType === 'duress') ? t("Duress Button") : ""}</p>
                                <div class="device-cell__device__details__status">
                                    ${
                                        (deviceStatus === true || $.sDevices.base.checkBtnIsOnline(device) ? 
                                                
                                            `
                                                <div class="device-cell__tag device-cell__tag--success">
                                                    <span>${t('Online')}</span>
                                                    <i></i>
                                                </div>
                                                ${statusTags}
                                            `
                                             : 
                                            `<div class="device-cell__tag device-cell__tag--danger">
                                                <span>${t('Offline')}</span>
                                                <i></i>
                                            </div>`
                                        )
                                    }
                                    ${
                                        (
                                            device.balenaUUID === undefined ? 
                                            '' : 
                                            `<span class="device-cell__tag device-cell__tag--outlined">${device.balenaUUID.substring(0, 8)}</span>`
                                        )   
                                    }
                                    ${
                                        (
                                            averageStats.batteryPercent ?
                                            `<span
                                                data-toggle="tooltip"
                                                data-placement="right"
                                                title="${averageStats.batteryPercent}"
                                                class="device-cell__tag device-cell__tag--battery battery-${$.sDevices.base.getPercentClass(parseInt(averageStats.batteryPercent))} ${!$.sDevices.base.checkBtnIsOnline(device) ? 'device-offline' : ''}">
                                                <span style="width: ${averageStats.batteryPercent}"></span>
                                            </span>` : ''
                                        )
                                    }
                                    ${
                                        (deviceStatus == true && isOrganisationAdmin && getKnownDeviceBugs(device)) ? `
                                        <span 
                                          class="device-cell__tag device-cell__tag--outlined"
                                          data-toggle="tooltip"
                                          data-placement="right"
                                          title="${getKnownDeviceBugs(device)}"
                                          style="display: inline-block; background-color: #ffe7f2; border: 1px solid #b3093c; border-radius: 3px; color: #b3093c; font-size: 12px;"
                                        >
                                          <i style="margin-left: 0px; margin-left: -2px; margin-right: -5px; color: #b3093c; background: none;" class="fa-solid fa-bug"></i>
                                        </span>
                                        ` : ``
                                    }
                                    ${

                                        (deviceStatus == true && device.deviceType == 'screen' && (!!device?.metadata?.systemStats?.relayDeviceConnected)) ? `
                                            <span 
                                                class="device-cell__tag device-cell__tag--outlined"
                                                data-toggle="tooltip"
                                                data-placement="right"
                                                title="${t('Relay Controller - This device can turn on & off an electrical circuit/relay based on the configured variables.')}"
                                            ><i style="margin-left:0px; background: none;" class="fa-solid fa-microchip"></i></span>
                                        ` : ``

                                    }
                                    ${
                                        (
                                            device.metadata === undefined || device.metadata.sonos2MqttMaster === undefined || device.metadata.sonos2MqttMaster === false ? 
                                            '' : (
                                                (deviceStatus === true || $.sDevices.base.checkBtnIsOnline(device) ?
                                                    `<span 
                                                        class="device-cell__tag device-cell__tag--sonos"
                                                        data-toggle="tooltip"
                                                        data-placement="right"
                                                        title="${t('This device has been automatically selected to control your Sonos devices')}"
                                                    ><i></i></span>` : ``
                                                )
                                            )
                                        )
                                    }
                                    ${
                                        (
                                            device.metadata === undefined || device.metadata.masterClock === undefined || device.metadata.masterClock === false ?
                                            '' : (
                                                (deviceStatus === true || $.sDevices.base.checkBtnIsOnline(device) ?
                                                    `<span 
                                                        class="device-cell__tag device-cell__tag--masterclock"
                                                        data-toggle="tooltip"
                                                        data-placement="right"
                                                        title="${t('This device has been automatically selected to be the Master Clock - The Master Clock triggers the bell timer on all your devices in unison')}"
                                                    ><i></i></span>` : ``
                                                )
                                            )
                                        )
                                    }
                                </div>
                            </div>
                        </div>
                    </td>
                    
                    <td class="hidden-xs device-cell" data-order="${(device.equipmentRound == undefined) ? device.cameraGroup : device.equipmentRound}">
                        <div class="device-cell__meta">
                            ${ 
                                (device.equipmentRound === undefined) ? 
                                    (   device.deviceType === 'duress' ? `${t('Room:')} ${device.deviceRoom}` :
                                        (device.cameraGroup === undefined) ? "" : 
                                        `${t('Location')}: ${t(device.cameraGroup)}`) :
                                        $.sDevices.base.returnEquipmentID(device.equipmentRound)
                            }
                            ${
                                // If device is a duress device - show an 'alarm triggered' badge if triggered.
                                (alarmTriggerd == true) ? `<br><div class="device-cell__tag device-cell__tag--danger"><span>${t('Alarm Triggered')}</span><i style="background-image: none;" class="fa-solid fa-triangle-exclamation"></i></div>` : ''
                            }
                            ${ 
                                (isOverheating == true) ? `<br><div class="device-cell__tag device-cell__tag--danger"><span>${t('Device Overheating')}</span><i style="background-image: none;" class="fa-solid fa-triangle-exclamation"></i></div>` : ''
                            }
                            ${ 
                                (parseInt(device?.metadata?.sdMetrics?.diskSpace?.percentUsed.trim()) > 90) ? `
                                    <br><div class="device-cell__tag device-cell__tag--danger"><span>${t('Low Disk Space - Recording May Fail')}</span><i style="background-image: none;" class="fa-solid fa-triangle-exclamation"></i></div>
                                ` : ``
                            }
                        </div>
                    </td>

                    <td class="visible-xl device-cell">
                        <div class="device-cell__ip">
                            ${ (device.metadata === undefined || device.metadata.systemStats === undefined || device.metadata.systemStats.network === undefined || device.metadata.systemStats.network.default_interface_ip === undefined) ? t("N/A") : device.metadata.systemStats.network.default_interface_ip }
                        </div>
                    </td>
                    <td class="hidden-sm hidden-xs device-cell" data-order="${ (device.metadata === undefined || device.metadata.wifi === undefined || device.metadata.wifi.quality === undefined) ? 0 : device.metadata.wifi.quality }">
                        <div class="device-cell__signal">
                            <p class="device-cell__signal__${device?.metadata?.defaultNetworkSource === 'ethernet' ? 'ethernet' : 'wifi' }">
                                ${ 

                                    device?.metadata?.defaultNetworkSource === 'ethernet' ?
                                    `${t('Ethernet Connected')} <span class="ethernet-icon"></span>` :
                                        ((device.metadata === undefined || device.metadata.wifi === undefined || device.metadata.wifi.ssid === undefined) && device.deviceType !== 'duress') ?
                                        t("Unknown") :
                                        device.deviceType === 'duress' ? `${t('Bluetooth Mesh')} <span class="bluetooth-icon"></span>` :
                                        device.metadata.wifi.ssid
                                }
                            </p>

                            ${ 
                                device?.metadata?.defaultNetworkSource === 'ethernet' ? '' : `
                                    <div class="device-cell__signal-grp">
                                        <div class="wifi-experience-container-sm device-cell__signal__bar signal-${$.sDevices.base.getPercentClass($.sDevices.base.getSignalQuality(device))}" data-signal="${
                                            (deviceStatus == false) ? 0 :
                                                ((device.metadata === undefined || device.metadata.wifi.quality === undefined) ? 0 : device.metadata.wifi.quality)
                                        }">
                                            <div class="device-cell__signal__quality" style="width: ${$.sDevices.base.getSignalQuality(device)}%"></div>
                                        </div>
                                        <span>${$.sDevices.base.getSignalQuality(device)}%</span>
                                    </div>
                                `
                            }

                        </div>
                    </td>
                    <td class="visible-xl device-cell" data-order="${(device.metadata === undefined || device.metadata.systemStats === undefined || device.metadata.systemStats.network === undefined || device.metadata.systemStats.network.rx_bytes === undefined) ? 0 : device.metadata.systemStats.network.rx_bytes}">
                        <div class="device-cell__activity">
                            ${
                                humanFileSize((device.metadata === undefined || device.metadata.systemStats === undefined || device.metadata.systemStats.network === undefined || device.metadata.systemStats.network.rx_bytes === undefined) ? 0 : device.metadata.systemStats.network.rx_bytes, true)
                            } / ${
                                humanFileSize((device.metadata === undefined || device.metadata.systemStats === undefined || device.metadata.systemStats.network === undefined || device.metadata.systemStats.network.tx_bytes === undefined) ? 0 : device.metadata.systemStats.network.tx_bytes, true)
                            }
                        </div>
                    </td>
                    <td class="hidden-xs device-cell" data-order="${temperature}">
                        <div class="device-cell__temperature">
                            ${formatTemperatureText(temperature, isOverheating)}
                        </div>
                    </td>
                    <td class="hidden-xs device-cell" data-order="${device?.metadata?.systemStats?.uptime || null}">
                        <div class="device-cell__uptime">
                        ${
                            (   
                                (device.deviceType === 'duress' && !$.sDevices.base.checkBtnIsOnline(device)) || 
                                (device.deviceType !== 'duress' && deviceStatus == false)) ? 
                                `<div class="device-cell__tag device-cell__tag--danger device-cell__tag--no-icon">
                                        ${t('Last seen')}: ${ (device.metadata && device.metadata.updated) ? timeDifference(new Date(), new Date(device.metadata.updated)) : t('Never')}
                                </div>` : (device.metadata && device.metadata.systemStats && device.metadata.systemStats.uptime ? secondsToDhms(device.metadata.systemStats.uptime) : `${t('Updated')}: ` + timeDifference(new Date(), new Date(device.metadata.updated)) ) 
                        }
                        </div>
                    </td>
                    <td class="visible-xl device-cell device-cell--right"><div class="device-cell__serial">
                        ${ 
                            ((device.deviceType === 'camera' || device.deviceType === 'duress') && device.deviceSerialNumber
                            ) ? 
                            (
                                device.deviceSerialNumber.length > 18 ?
                                device.deviceSerialNumber.slice(0,18) + '...' : device.deviceSerialNumber
                            ) :
                            (device.metadata === undefined || device.metadata.deviceVersions === undefined || device.metadata.deviceVersions.DEVICE_SN === undefined) ? t("Unknown") : device.metadata.deviceVersions.DEVICE_SN
                        }
                        ${
                            // For NUCS/Sticks/Computers...
                            (device?.metadata?.deviceVersions?.DEVICE_MODEL && device.metadata.deviceVersions.DEVICE_MODEL !== "Docker Container")
                            ? `<br><small class="device-type">${
                                (device.metadata.deviceVersions.DEVICE_MODEL === "TS10") ? "Asus " :
                                (device.metadata.deviceVersions.DEVICE_MODEL === "STK1AW32SC") ? "Intel " :
                                (device.metadata.deviceVersions.DEVICE_MODEL.includes("PN41")) ? "Asus " : ""
                            }${device.metadata.deviceVersions.DEVICE_MODEL}</small>`
                            : ''
                        }
                        ${
                            // For Cameras...
                            (device.deviceType === 'camera' && device?.metadata?.activeCamera)
                            ? `<br><small class="device-type">
                                ${ device?.metadata?.activeCamera?.vendor }
                                ${ device?.metadata?.activeCamera?.model } (${ device?.metadata?.activeCamera?.devicePath })
                                </small>`
                            : ''
                        }
                    </div></td>
                </tr>
            `;

            if ($('#hide-offline-devices').is(':checked')) {
                if(deviceStatus) {
                    appendToTable();
                } else {
                    let deviceLastSeen = (device.metadata === undefined || device.metadata.updated === undefined) ? false : device.metadata.updated;
                    let hideDevice = moment(deviceLastSeen).isBefore(moment().subtract(1, 'months'))

                    //console.log('hideDevice', hideDevice)
                    //console.log('lastSeen', deviceLastSeen, device)

                    if(!hideDevice) appendToTable();

                }
            } else {
                appendToTable();
            }

            function appendToTable() {
                $("#ics-devices-table tbody").append(metaData);
                currentDevicesObj[(device.deviceID === undefined) ? ((device.balenaUUID === undefined) ? device.deviceSerialNumber : device.balenaUUID) : device.deviceID] = device;
            }

        });

        // wifiExperience(`wbar-${device.deviceID}`, `wpercent-${device.deviceID}`, signalStrength)
        $("#ics-devices-table tbody .wifi-experience-container-sm").each(function() {
            $.sDevices.base.wifiExperience($(this).find(".wifi-experience-bar-sm")[0], $(this).find(".wifi-experience-text-sm")[0], $(this).data("signal"))
        });

        // Generate export filename with timestamp
        let exportFilename = 'Device-Management__' + (new Date().toISOString());
        
        var dtConfig = {
            responsive: true,
            dom: '<"toolbar">rtip',
            order: [[ 0, "asc" ]],
            stateSave: true,
            // Show all results in table...
            iDisplayLength: -1,
            columnDefs: [
                {
                    targets: "no-sort",
                    orderable: false,
                },
                { "width": "110px", "targets": 0 },
            ],
            paging: false,
            info: false,
            language: {
                // paginate: {
                //     previous: t('Previous'),
                //     next: t('Next'),
                // },
                infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
                // info: t('Showing') + ' _START_ ' + t('to') + ' _END_ ' + t('of') + ' _TOTAL_ ' + t('entries'),
                emptyTable: t('No data available in table'),
                zeroRecords: t('No matching records found'),
                infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
                aria: {
                    sortAscending:  `: ${t('activate to sort column ascending')}`,
                    sortDescending: `: ${t('activate to sort column descending')}`
                }
            },
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Device Management',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.pdf'
                },
            ],
        }
        if(getOS() == 'ios' || getOS() == 'android') dtConfig.scrollX = true;
        
        // Clear previous table instance
        devicesTable?.clear();
        devicesTable?.destroy();
        
        devicesTable = $('#ics-devices-table').DataTable(dtConfig);

        // Apply current search value to new table
        const currentSearch = localStorage.getItem('deviceSearch') || '';
        if (currentSearch) {
            devicesTable?.search(currentSearch)?.draw();
        }

        $("#ics-devices-table tbody .device").click(function () {
            $.sDevices.base.deviceDetailsModel($(this).data("deviceid"), currentDevicesObj);
        });

        $('.device [data-toggle="tooltip"]').tooltip({ container: 'body' });

        function drawSonos2MqttControllerIcon(state) {

            if(state) {
                return '<div class="badge badge-green"><i class="fa-solid fa-music"></i></div>';
            } else {
                return ''
            }
        }

        function drawOnlineStatus(status) {
            if (status) {
                return `<div class="badge badge-green">${t('Online')} <i class="fa fa-check" aria-hidden="true"></i></div>`
            } else {
                return `<div class="badge badge-red" title="Open device for details">${t('Offline')} <i class="fa fa-exclamation-triangle" aria-hidden="true"></i></div>`
            }
        }

        skeletonLoader.hide();

    },
    getchBuildVersions: function() {

        let req = $.ajax({
            url: '/api/devices/listReleases',
            method: 'GET',
            contentType: 'application/json',
            success: function(data){
                if((data === undefined) ? false : data !== false) {
                    $.sDevices.base.knownBuildVersions = data;
                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
                $.sDevices.base.knownBuildVersions = [];
            }
        });


    },
    fetchDeviceStats: function(callback) {

        function addDeviceSortKeys(data) {
            // Ensure that data.items is an array, if not return data as is
            if (!Array.isArray(data.items)) {
                return data;
            }

            // Regular expression to match a number in the string
            const regex = /\d+/;

            // Priority for device types
            const deviceTypePriority = {
                "duress": 1,
                "camera": 2,
                "screen": 3,
            };

            // Sort the array
            data.items.sort((a, b) => {
                // Compare device types
                const typeA = deviceTypePriority[a.deviceType] || 4;
                const typeB = deviceTypePriority[b.deviceType] || 4;
                if (typeA !== typeB) return typeA - typeB;

                // Get the display name, device name or device room
                const nameA = a.displayName || a.deviceName || a.deviceRoom || "";
                const nameB = b.displayName || b.deviceName || b.deviceRoom || "";

                // Extract the number from the string using the regex
                const numA = regex.exec(nameA);
                const numB = regex.exec(nameB);

                // If both strings have numbers, compare the numbers
                if (numA && numB) {
                    return numA[0] - numB[0];
                } else if (numA) {
                    // If only a has a number, a comes first
                    return -1;
                } else if (numB) {
                    // If only b has a number, b comes first
                    return 1;
                } else {
                    // If neither string has a number, compare the strings alphabetically
                    return nameA.localeCompare(nameB);
                }
            });

            // Add sortOrder and searchData keys to each item
            for (let i = 0; i < data.items.length; i++) {
                data.items[i].sortOrder = i + 1;

                // Create an array of values from the specified keys
                const searchValues = ["displayName", "deviceName", "balenaUUID", "deviceRoom"]
                    .map(key => data.items[i][key])
                    .filter(Boolean); // Remove undefined or null values

                // Check for Sonos
                if (data.items[i].metadata && data.items[i].metadata.sonos2MqttMaster === true) {
                    searchValues.push("Sonos");
                }

                // Check for Clock
                if (data.items[i].metadata && data.items[i].metadata.masterClock === true) {
                    searchValues.push("Master Clock");
                }

                // Push in the device type also
                searchValues.push(data.items[i].deviceType);

                // Join the values with a space to create the searchData key
                data.items[i].searchData = searchValues.join(" ");
            }

            return data;
        }

        function getLatestInternetSpeedFromDeviceMetadata(data) {
            // Ensure that data.items is an array, if not return null
            if (!Array.isArray(data.items)) {
                return null;
            }

            // Initialize the latestInternetSpeed object and the latestDate variable
            let latestInternetSpeed = null;
            let latestDate = 0;

            // Iterate over the items
            for (const item of data.items) {
                // Check if the item has metadata and internetSpeed
                if (item.metadata && item.metadata.internetSpeed) {
                    // Check if this internetSpeed object is more recent than the current latest one
                    if (item.metadata.internetSpeed.lastUpdated > latestDate) {
                        // If so, update latestInternetSpeed and latestDate
                        latestInternetSpeed = item.metadata.internetSpeed;
                        
                        // Add the device UUID into the speedtest results
                        latestInternetSpeed.deviceUUID = item.balenaUUID;
                        latestInternetSpeed.deviceName = item.displayName

                        latestDate = item.metadata.internetSpeed.lastUpdated;
                    }
                }
            }

            return latestInternetSpeed;
        }

        let req = $.ajax({
            url: '/api/devices/list/' + selectedID,
            method: 'GET',
            contentType: 'application/json',
            success: function(data){
                if((data === undefined) ? false : data !== false) {

                    // Add devices to UI...
                    if(data.count > 0) {
                        // console.log('data-arr', data)
                        let deviceList = addDeviceSortKeys(data)

                        $.sDevices.base.renderDevices(deviceList);
                        $.sDevices.base.deviceData = deviceList;
                        $.sDevices.base.setNetworkOptions(deviceList);

                        // Update the "internet speed" data in the UI
                        updateNetSpeed(getLatestInternetSpeedFromDeviceMetadata(data))

                        // Call the callback only if it's a function
                        if (typeof callback === 'function') {
                            callback(deviceList); 
                        }

                    } else {
                        $.sDevices.base.noDevices();
                        $('#device-filters').removeClass('isLoading');
                    }

                } else {
                    $.sDevices.base.noDevices();
                    $('#device-filters').removeClass('isLoading');
                }

            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
                // hide loader for filters
                $('#device-filters').removeClass('isLoading');
                noDevices();

                // Call the callback only if it's a function
                if (typeof callback === 'function') {
                    callback([]); 
                }

            }
        });

        function updateNetSpeed(data) {

            function formatSpeedInMBps(speedInBytesPerSecond) {
                let speedInMBps = speedInBytesPerSecond / 1000000;

                if (speedInMBps > 200) {
                    return Math.round(speedInMBps);
                }

                return Math.round(speedInMBps * 100) / 100;
            }

            if(data) {
                $(".internet-stats").html(`
                    <div 
                        class="netSpeedContainer"
                        data-toggle="tooltip"
                        data-placement="below"
                        title="${t('This speed test was initiated from the device') + ': ' + data.deviceName + ' (' + data.deviceUUID.substring(0,8) + '), ' + t('to the server') + ': ' + data.server.sponsor + ', ' + data.server.name + ' (' + data.server.host + ')'}"
                    >
                        <div class="speedtest-header">
                            ${t('Current Internet Speed (Updated')} ${timeAgo(data.lastUpdated)})
                        </div>
                        <div class="speedtest-results">
                            ${t('Download')}: ${ formatSpeedInMBps(data.download) } ${t('MB/s')},
                            ${t('Upload')}: ${ formatSpeedInMBps(data.upload) } ${t('MB/s')},
                            ${t('Latency')}: ${ data.ping } (${data.server.name})
                        </div>
                    </div>
                `)
            }

            // Re-render the popover tooltop
            $('[data-toggle="tooltip"]').tooltip({
                container: 'body',
                content: function() {
                    const title = $(this).attr('title');
                    return title ? title.replace(/\n/g, "<br>") : '';
                }
            });

            $('[data-toggle="popover"]').popover();
        }


        function iframeOnMessage () {    
            window.addEventListener('message',function(event) {
    
                try {
                    if (typeof(event.data) == 'object') {
                        return;
                    }
        
                    if (event.data) {
                        const dataObj = JSON.parse(event.data);

                        if(dataObj.type === 'coachingScreens') return;
                        
                        // trigger modal if balenaUUID is present
                        if (dataObj.balenaUUID) {
                            $.sDevices.base.deviceDetailsModel(dataObj.balenaUUID, currentDevicesObj);
                        }

                        // trigger add device modal if deviceID is present
                        if(dataObj.addDevice) {
                            $.sDevices.base.addDevice();
                        }
                    }
                } catch(e) { console.log('unhanded error', e) }
            });
        }
    
        iframeOnMessage();

    },
    deviceFilters: defaultDeviceFilters,
    deviceOptions: {
        range: [
            {
                text: 'Only Online',
                value: 'only online',
            },
            {   
                text: 'This Week',
                value: 'this week'
            },
            {   
                text: 'Last 30 days',
                value: 'last 30 days'
            },
            {   
                text: 'All',
                value: 'all'
            },
            
        ],
        type: [
            {
                text: 'All',
                value: 'all',
            },
            {
                text: 'Camera',
                value: 'camera',
            },
            {
                text: 'Screen',
                value: 'screen',
            },
            {
                text: 'Duress Button',
                value: 'duress'
            },
        ],
        connection: [
            {
                text: 'All',
                value: 'all',
            },
        ]
    },
    deviceData: {},
    getDeviceSSID: function(device) {
        return ((device.metadata === undefined || device.metadata.wifi === undefined || device.metadata.wifi.ssid === undefined) && device.deviceType !== 'duress') ?
        "Unknown" :
        device.deviceType === 'duress' ? `${t('Bluetooth Mesh')}` :
        device.metadata.wifi.ssid
    },
    wifiExperience: function(barSelector, textSelector, wifiExperience) {

        var wifiExperienceColor, wifiExperienceClass
        switch (true) {
            case (wifiExperience < 30):
                wifiExperienceColor = "#b3093c"
                wifiExperienceClass = "color-bad"
                break;
            case (wifiExperience < 40):
                wifiExperienceColor = "#ff9d4d"
                wifiExperienceClass = "color-warning-bad"
                break;
            case (wifiExperience < 50):
                wifiExperienceColor = "#ffcc63"
                wifiExperienceClass = "color-warning"
                break;
            case (wifiExperience < 70):
                wifiExperienceColor = "#bae774"
                wifiExperienceClass = "color-ok-medium"
                break;
            case (wifiExperience < 100):
                wifiExperienceColor = "#00a40d"
                wifiExperienceClass = "color-ok"
                break;
            default:
                wifiExperienceColor = "#00a40d"
                wifiExperienceClass = "color-ok"
                break;
        }

        if(barSelector) {
            var bar = new ProgressBar.Line(barSelector,
                {
                    easing: 'easeInOut',
                    strokeWidth: 2,
                    color: wifiExperienceColor,
                    trailColor: '#eee',
                    trailWidth: 1
                }
            );
            bar.animate((wifiExperience / 100));  // Value from 0.0 to 1.0
            $(textSelector).html(`${wifiExperience}%`);
            $(textSelector).addClass(wifiExperienceClass)
        }

    },
    equipmentSelect: function(){
        return `<select class="equipment-round p-0 selectpicker-border ph-select-bootstrap" title="${t('Please Select')}" data-live-search="true">
        <optgroup>
            <option value="1">${t('#1 - Boxing 1')}</option>
            <option value="2">${t('#2 - Boxing 2')}</option>
            <option value="3">${t('#3 - Floor to ceiling')}</option>
            <option value="4">${t('#4 - Strength 1')}</option>
            <option value="5">${t('#5 - Strength 2')}</option>
            <option value="6">${t('#6 - Speedball')}</option>
            <option value="7">${t('#7 - Sled')}</option>
            <option value="8">${t('#8 - Boxing 4')}</option>
            <option value="9">${t('#9 - Boxing 5')}</option>
            <option value="10">${t('#10 - Cell')}</option>
            <option value="11">${t('#11 - Strength 3')}</option>
            <option value="12">${t('#12 - Strength 4')}</option>
            <option data-divider="true"></option>
            <option value="warmup1">${t('Warm-up')}</option>
            <option value="relay1">${t('Relay Controller')}</option>
        </optgroup>
    </select>`

    },

    // This function should be global - it's referenced in screens.js
    sendDeviceCommand: function(command, args=null, deviceUUID=false, broadcast=false, successDialog=false, silent=false) {
        let postData = {
                uuid: deviceUUID,
                command: command,
                arguments: args,
                broadcast: broadcast
            }

        let req = $.ajax({
            url: '/api/ics/devices/command/' + selectedID,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(postData),
            success: function(data){
                if((data === undefined) ? false : data !== false) {

                    if(successDialog) {
                        swalWithBootstrapButtons.fire(successDialog);
                    } else {
                        if(successDialog != null) {
                            if(!silent) instantNotification(
                                t('Command Sent')
                            );
                        }
                    }

                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
                instantNotification(
                    `<b>${t('Error sending command to one or more devices')}</b>`,
                    'error',
                    30000
                );
            }
        });
    },

    updateDeviceAPIReq: function(data, devicetype) {

        console.log('updateDeviceAPIReq', data)

        let updateURL, updateMethod;
        switch(devicetype) {
            case 'screen':
                updateURL = `/api/ics/devices/update/${ selectedID }`
                updateMethod = 'PUT'
                break;

            case 'camera':
                updateURL = `/api/cctv/devices`
                updateMethod = 'POST'
                break;

            case 'duress':
                updateURL = `/api/devices/updateGenericDevice/${ selectedID }`
                updateMethod = 'POST'
                break;

        }

        let req = $.ajax({
            url: updateURL,
            method: updateMethod,
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(data){
                if((data === undefined) ? false : data !== false) {

                    instantNotification(
                        // Message...
                        t('Configuration Saved - The device may automatically restart for your changes to take effect.')
                    );
                    $.sDevices.base.fetchDeviceStats();

                } else {
                    if(data.error) {
                        instantNotification(
                            // Message...
                            `<b>${t('FAILED TO SAVE DEVICE CONFIGURATION!')}</b><br><br>${data.error}`,
                            // Type...
                            'error',
                            // Timeout (1m)...
                            60000
                        );
                    } else {
                        instantNotification(
                            `<b>${t('FAILED TO SAVE DEVICE CONFIGURATION!')}</b><br><br>${t('An error occurred while trying to save the device configuration changes.<br>Please contact HQ for further details.')}`,
                            'error',
                            60000
                        );
                    }
                    console.dir(response);
                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);

                if(xhr.responseJSON.error) {
                    instantNotification(
                        `<b>${t('FAILED TO SAVE DEVICE CONFIGURATION!')}</b><br><br>${xhr.responseJSON.error}`,
                        'error',
                        60000
                    );
                } else {
                    instantNotification(
                        `<b>${t('FAILED TO SAVE DEVICE CONFIGURATION!')}</b><br><br>${t('An error occurred while trying to save the device configuration changes.<br>Please contact HQ for further details.')}`,
                        'error',
                        60000
                    );
                }
            }
        });

    },

    deviceCCTVMotionZoneConfig: async function(deviceID, imageData) {
        console.log('deviceCCTVMotionZoneConfig', deviceID, imageData)

        let currentDevice = $.sDevices.base.deviceData.items
          .filter(item => item.balenaUUID === deviceID)

        // These need to be defined outside of the swal events - so that way when the dialog closes we can remove the bindings from the window...
        let dragPoint = null;
        let canvas;

        function handleMouseUp(e) {
            dragPoint = null;
        }

        function handleMouseOut(e) {
            if (!e.buttons) {
                dragPoint = null;
            }
        }
        swalWithBootstrapButtons.fire({
            //title: 'Design Setup',
            html: `
            <div class="motion-zone-parent ph-dialog-v2" style="padding: 20px;">
                <div class="motion-zone-dialog">
                    <fieldset>

                        <div class="camera-settings" style="">
                            <div class="row">
                                <div class="col-sm-2"></div>
                                <div class="col-sm-8 video-container">
                                    <div class="iframe-loading-message" style="background-color: #333333; width: 100%; height: 400px; display: flex; justify-content: center; align-items: center; color: white; font-size: 20px;">
                                        <h4>
                                            <span class="video-connection-status">${t('Connecting to Camera')}...</span>
                                            <br><br>
                                            <div class="preloader pl-size-xl" style="padding: 10px;">
                                                <img src="/assets/images/tail-spin.svg" style="filter: invert(1);">
                                            </div>
                                        </h4>
                                    </div>
                                    <div id="videoContainer" style="position: relative; width: 640px; height: 360px; display: inline-block;"></div>
                                </div>
                                <div class="col-sm-2"></div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-12">
                                    ${t('Adjust the mask/points on the snapshot from your camera that you would like to exclude motion detection from.')}<br>
                                    ${t('If required you may add additional points to your exclusion mask by clicking the small circle between the larger white circles on the mask.')}<br>
                                    ${t('Double click on a point to remove it from the mask.')}
                                </div>
                                <div class="col-sm-12">
                                    <svg fill="currentColor" viewBox="0 0 20 20" class="1icon__DxQf1eVn 1icon-light__DxQf1eVn" width="32px" height="32px" baseclassname="" twotonedclassname="hidden__DxQf1eVn1" fillclassname="hidden__DxQf1eVn" fillwithtwotoneclassname="hidden__DxQf1eVn" stroke="currentColor" stroke-width="0" prefixid="f0fabcaa-e7ea-408a-8c5d-3da8e974dc05" color="#AB46BC" style="flex-shrink: 0; margin-right: 8px; vertical-align: middle;"><path fill="currentColor" d="M17 15.5a1.5 1.5 0 01-2.914.5H5.914A1.5 1.5 0 114 14.086V5.914A1.498 1.498 0 014.5 3a1.5 1.5 0 011.414 1h8.172A1.499 1.499 0 1116 5.914v8.172a1.5 1.5 0 011 1.414z" class="hidden__DxQf1eVn"></path><path fill="currentColor" d="M16 14.086V5.914A1.5 1.5 0 1014.086 4H5.914A1.5 1.5 0 104 5.914v8.172A1.498 1.498 0 004.5 17a1.5 1.5 0 001.414-1h8.172A1.499 1.499 0 1016 14.086zM15.5 4a.5.5 0 110 1 .5.5 0 010-1zm-11 0a.5.5 0 110 1 .5.5 0 010-1zm0 12a.5.5 0 110-1 .5.5 0 010 1zm9.586-1H5.914A1.5 1.5 0 005 14.086V5.914A1.5 1.5 0 005.914 5h8.172c.15.426.487.764.914.914v8.172c-.427.15-.764.488-.914.914zm1.414 1a.5.5 0 110-1 .5.5 0 010 1z" class=""></path><path fill="currentColor" d="M16.05 13.694V6.306A1.75 1.75 0 0015.25 3a1.75 1.75 0 00-1.5.85h-7.5A1.75 1.75 0 103.8 6.219v7.562a1.75 1.75 0 102.506 2.269h7.388a1.75 1.75 0 003.306-.8 1.75 1.75 0 00-.95-1.556zm-1.5-.047c-.403.176-.727.5-.903.903H6.353a1.753 1.753 0 00-1.053-.961V6.411A1.756 1.756 0 006.394 5.35h7.212c.164.449.508.813.944 1.003v7.294z" class="hidden__DxQf1eVn"></path></svg>
                                    ${t('Any motion that occurs within the solid purple area will trigger a recording.')}
                                </div>
                            </div>

                        </div>

                    </fieldset>
                </div>
                <div class="actions" style="display: flex; justify-content: center; width: 100%; margin: 1.25em auto 0; align-items: center; flex-wrap: wrap;">
                    <button type="button" class="b-save swal2-confirm btn btn-primary waves-effect" aria-label="" style="margin: 5px; display: inline-block;">
                        ${t('Save Changes')} (${t('Device will restart')})
                    </button>
                    <button type="button" class="b-load-defaults btn btn-default waves-effect" aria-label="" style="margin: 5px; display: inline-block;">
                        ${t('Load/Reset Defaults')}
                    </button>
                    <button type="button" class="b-cancel btn btn-default waves-effect" aria-label="" style="margin: 5px; display: inline-block;">
                        ${t('Cancel')}
                    </button>
                </div>
            </div>

            `,
            width: 1100,
            title: `${t('Motion Zones')} - ${currentDevice[0].deviceName} (${currentDevice[0].cameraGroup})`,
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
            onBeforeOpen: async () => {


                $(".b-load-defaults").prop('disabled', true);
                $(".b-save").prop('disabled', true);

                $(".b-cancel").click(function() {
                    swalWithBootstrapButtons.close()
                });

                if(!imageData) {
                    $(".video-connection-status").html(t('Fetching Data from Camera'))
                    $("#videoContainer").hide();
                } else {

                    $("#videoContainer").html(`
                        <img id="backgroundImage" src="data:image/jpeg;base64,${imageData}" style="width: 100%; height: 100%; user-select: none;"  />
                        <canvas id="canvas" style="position: absolute; z-index: 1; left: 0px; top: 0px; width: 100%; height: 100%;"></canvas>
                    `)
                    drawCanvasMask();

                }

                function enableMaskUI() {
                    $(".iframe-loading-message").hide();
                    $("#videoContainer").show();
                    $(".b-load-defaults").prop('disabled', false);
                    $(".b-save").prop('disabled', false);
                }

                function drawCanvasMask() {

                    canvas = document.getElementById('canvas');
                    let ctx = canvas.getContext('2d');
                    let backgroundImage = document.getElementById('backgroundImage');
                    let margin = 25;

                    let mask = {
                        points: [],
                        color: "rgba(128, 0, 128, 0.5)"
                    };

                    let pointRadius = 8;
                    let midPointRadius = 4;
                    let midPoints = [];
                    let savedMasks = null;

                    let currentWidth = 640;
                    let currentHeight = 360;

                    let drawMask = function() {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        ctx.beginPath();
                        ctx.moveTo(mask.points[0].x, mask.points[0].y);
                        for (var i = 1; i < mask.points.length; i++) {
                            ctx.lineTo(mask.points[i].x, mask.points[i].y);
                        }
                        ctx.closePath();
                        ctx.fillStyle = mask.color;
                        ctx.fill();

                        ctx.strokeStyle = "purple";
                        ctx.lineWidth = 2;
                        ctx.stroke();

                        midPoints = [];
                        for (var i = 0; i < mask.points.length; i++) {
                            var point = mask.points[i];
                            var nextPoint = mask.points[(i + 1) % mask.points.length];
                            var midPoint = {
                                x: (point.x + nextPoint.x) / 2,
                                y: (point.y + nextPoint.y) / 2
                            };
                            midPoints.push(midPoint);

                            ctx.beginPath();
                            ctx.arc(midPoint.x, midPoint.y, midPointRadius, 0, 2 * Math.PI);
                            ctx.fillStyle = "purple"; // Reversed colors
                            ctx.fill();
                        }

                        for (var i = 0; i < mask.points.length; i++) {
                            var point = mask.points[i];
                            ctx.beginPath();
                            ctx.arc(point.x, point.y, pointRadius, 0, 2 * Math.PI);
                            ctx.fillStyle = "lightgray"; // Reversed colors
                            ctx.fill();
                        }
                    };

                    canvas.width = currentWidth;
                    canvas.height = currentHeight;
                    enableMaskUI();

                    if (currentDevice?.motionMask) {
                        savedMasks = currentDevice.motionMask
                        
                        let desiredSize = "1920x1080"; // Specify the size you want to load
                        let savedWidth = parseInt(desiredSize.split("x")[0]);
                        let savedHeight = parseInt(desiredSize.split("x")[1]);
                        mask.points = savedMasks[desiredSize].coordinates.map(p => ({
                            x: p.x * (currentWidth / savedWidth),
                            y: p.y * (currentHeight / savedHeight)
                        }));
                        drawMask();
                        console.log('Mask loaded:', mask);

                    } else {                            
                        mask.points = [
                            {x: 0, y: 0},
                            {x: canvas.width, y: 0},
                            {x: canvas.width, y: canvas.height},
                            {x: 0, y: canvas.height}
                        ];
                        drawMask();
                    }
                    

                    // Event Listeners
                    document.addEventListener('mouseup', handleMouseUp);
                    canvas.addEventListener('mouseout', handleMouseOut);

                    canvas.addEventListener('mousedown', function(e) {
                        var rect = canvas.getBoundingClientRect();
                        var x = e.clientX - rect.left;
                        var y = e.clientY - rect.top;

                        for (var i = 0; i < mask.points.length; i++) {
                            var point = mask.points[i];
                            var dx = point.x - x;
                            var dy = point.y - y;

                            if (Math.sqrt(dx * dx + dy * dy) <= pointRadius) {
                                dragPoint = point;
                                break;
                            }
                        }

                        if (!dragPoint) {
                            for (var i = 0; i < midPoints.length; i++) {
                                var midPoint = midPoints[i];
                                var dx = midPoint.x - x;
                                var dy = midPoint.y - y;

                                if (Math.sqrt(dx * dx + dy * dy) <= midPointRadius) {
                                    mask.points.splice(i + 1, 0, { x: midPoint.x, y: midPoint.y });
                                    drawMask();
                                    break;
                                }
                            }
                        }
                    });

                    canvas.addEventListener('dblclick', function(e) {
                        var rect = canvas.getBoundingClientRect();
                        var x = e.clientX - rect.left;
                        var y = e.clientY - rect.top;

                        for (var i = 0; i < mask.points.length; i++) {
                            var point = mask.points[i];
                            var dx = point.x - x;
                            var dy = point.y - y;

                            if (Math.sqrt(dx * dx + dy * dy) <= pointRadius) {
                                mask.points.splice(i, 1);
                                drawMask();
                                break;
                            }
                        }
                    });

                    canvas.addEventListener('mousemove', function(e) {
                        var rect = canvas.getBoundingClientRect();
                        var x = e.clientX - rect.left;
                        var y = e.clientY - rect.top;

                        // Ensure x/y are within canvas boundaries
                        x = Math.min(Math.max(x, 0), canvas.width);
                        y = Math.min(Math.max(y, 0), canvas.height);

                        // Rest of the logic for hovering over mask points
                        for (var i = 0; i < mask.points.length; i++) {
                            var point = mask.points[i];
                            var dx = point.x - x;
                            var dy = point.y - y;

                            if (Math.sqrt(dx * dx + dy * dy) <= pointRadius) {
                                canvas.style.cursor = 'pointer';
                                break;
                            } else {
                                canvas.style.cursor = '';
                            }
                        }

                        for (var i = 0; i < midPoints.length; i++) {
                            var midPoint = midPoints[i];
                            var dx = midPoint.x - x;
                            var dy = midPoint.y - y;

                            if (Math.sqrt(dx * dx + dy * dy) <= midPointRadius) {
                                canvas.style.cursor = 'pointer';
                                break;
                            } else {
                                canvas.style.cursor = '';
                            }
                        }

                        if (dragPoint) {
                            dragPoint.x = x;
                            dragPoint.y = y;
                            drawMask();
                        }
                    });

                    canvas.addEventListener('mouseup', function(e) {
                        dragPoint = null;
                    });

                    $(".b-load-defaults").click(function() {
                        mask.points = [
                            {x: 0, y: 0},
                            {x: canvas.width, y: 0},
                            {x: canvas.width, y: canvas.height},
                            {x: 0, y: canvas.height}
                        ];
                        drawMask();
                    });

                    $(".b-save").click(function() {

                        savedMasks = {
                            "1920x1080": {
                                coordinates: mask.points.map(p => ({
                                    x: p.x * (1920 / currentWidth),
                                    y: p.y * (1080 / currentHeight)
                                }))
                            },
                            "1280x720": {
                                coordinates: mask.points.map(p => ({
                                    x: p.x * (1280 / currentWidth),
                                    y: p.y * (720 / currentHeight)
                                }))
                            },
                            "640x360": {
                                coordinates: mask.points.map(p => ({
                                    x: p.x,
                                    y: p.y
                                }))
                            }
                        };

                        $.ajax({
                            url: `/api/cctv/devices/save-camera-mask/${selectedID}`,
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                "uuid": deviceID,
                                "mask": savedMasks
                            }),
                            success: function(res) {
                                swalWithBootstrapButtons.close()
                                instantNotification(
                                    // Message...
                                    t('Configuration Mask Saved - The device will automatically restart for your changes to take effect.')
                                );
                            },
                            error: function(err) {
                                alert(`Unable to save device settings at this time. Please try again later.\n\nError:\n${err}`)
                                // NO CAMERA DATA?
                            }
                        });
                    });
                }
            },
            onClose: () => {
                try {                
                    document.removeEventListener('mouseup', handleMouseUp);
                    canvas.removeEventListener('mouseout', handleMouseOut);
                } catch(e) {}
            }
        }).then(response => {
            if (response.value) {}
        });
    },

    deviceWifiDetailsModel: function(deviceID, wifiConfiguration) {
        console.log('wifiConfiguration', wifiConfiguration)

        // Function to convert your SSID array into a string
        function ssidToString(ssid) {
            return ssid.map((num) => String.fromCharCode(num)).join('');
        }


        // Sort the wifiConfiguration array
        wifiConfiguration.sort((a, b) => {
            // Move networks with highest Autoconnect Priority to top
            if (a["autoconnect-priority"] !== b["autoconnect-priority"]) {
                return b["autoconnect-priority"] - a["autoconnect-priority"];
            }

            // Move networks with undefined or blank SSID to bottom
            let aSSID = ssidToString(a["802-11-wireless"].ssid);
            let bSSID = ssidToString(b["802-11-wireless"].ssid);

            if (aSSID === "undefined" || aSSID === "") {
                return 1;
            } else if (bSSID === "undefined" || bSSID === "") {
                return -1;
            }

            // If all else is equal, maintain original order
            return 0;
        });


        // Initialize an empty string for table rows
        let tableRowsHTML = "";

        // Iterate over each WiFi detail object and generate a table row
        wifiConfiguration.forEach((wifiDetail, index) => {

            try {
                let wifiDetailRowHTML = `
                    <tr ${ (wifiDetail.currentActiveConnection) ? `style="background-color: #f3f9ff"` : `` }>
                        <td>
                            ${ssidToString(wifiDetail["802-11-wireless"].ssid)}
                        </td>
                        <td><code style="font-size: 10px; max-width: 15ch; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;" data-toggle="tooltip" title="${wifiDetail.uuid}">${wifiDetail.uuid}</code></td>
                        <td>${wifiDetail.type}</td>
                        <td style="text-align: center;">${wifiDetail["autoconnect-priority"]}</td>
                        <td>${wifiDetail["802-11-wireless-security"]["key-mgmt"]}</td>
                        <td><a style="cursor: pointer;" onclick="event.preventDefault(); this.outerHTML = '<code>' + '${wifiDetail["802-11-wireless-security"].psk}' + '</code>';">Reveal</a></td>
                        <td>${wifiDetail["802-11-wireless"].mode}</td>
                        <td>${wifiDetail.ipv4.method}</td>
                        <td>${wifiDetail.ipv6.method}</td>
                        <td>${(wifiDetail.currentActiveConnection) ? `
                            <small><b>** Currently Connected **</b></small>
                        ` : `
                            <a data-uuid="${wifiDetail.uuid}" class="p-connect-wifi-network btn btn-xs btn-outline btn-success waves-effect">${t('Connect')}</a>
                            <a data-uuid="${wifiDetail.uuid}" class="p-delete-wifi-network btn btn-xs btn-outline btn-danger waves-effect"><i style="margin-right: 0px; font-size: 14px;" class="fa-regular fa-trash-can"></i></a>
                        `}</td>
                    </tr>
                `;

                tableRowsHTML += wifiDetailRowHTML;
            } catch(e) {}

        });

        // Complete table HTML with headers and rows
        let wifiDetailsHTML = `
            <table class="table">
                <thead>
                    <tr>
                        <th>${t('SSID')}</th>
                        <th>${t('Device UUID')}</th>
                        <th>${t('Type')}</th>
                        <th>
                            ${t('Connection Priority')}
                            <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right"
                                title="${t('Networks with the highest value will be connected first. Networks the same priority value will be connected in the order of the last connection time.')}">info_outline</i>
                        </th>
                        <th>${t('Security Mode')}</th>
                        <th>${t('Password')}</th>
                        <th>${t('Mode')}</th>
                        <th>${t('IPv4')}</th>
                        <th>${t('IPv6')}</th>
                        <th style="min-width: 115px;"></th>
                    </tr>
                </thead>
                <tbody style="text-align: left;">
                    ${tableRowsHTML}
                </tbody>
            </table>
        `;

        let currentDevice = $.sDevices.base.deviceData.items
          .filter(item => item.balenaUUID === deviceID)
        // console.log('currentDevice', currentDevice)

        swalWithBootstrapButtons.fire({
            title: `${t('Wifi Configuration stored on Device')} ${(currentDevice.length && currentDevice[0].displayName !== undefined) ? ": " + currentDevice[0].displayName : ''}`, 
            html: `
            <div class="wifi-details-parent ph-dialog-v2" style="padding: 20px;">
                <div class="wifi-details-container" style="max-height: 500px; overflow-y: scroll; min-height: 200px;">
                    ${wifiDetailsHTML}
                </div>
                <div>
                    <button type="button" class="p-change-wifi btn btn-primary waves-effect" aria-label="" style="display: inline-block;">${t('Connect to New Wifi Network')}</button>
                    <button type="button" class="p-close btn btn-default waves-effect" aria-label="" style="display: inline-block;">${t('Close')}</button>
                </div>
            </div>`,
            width: 1100,
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
            onBeforeOpen: async () => {
                $('.wifi-details-parent [data-toggle="tooltip"]').tooltip({ container: 'body' });
                addClipboardCopyButtons('.wifi-details-parent');

                $(".p-connect-wifi-network").click(function() {
                    $.sDevices.base.sendDeviceCommand('connectToWifiUUID', $(this).data('uuid'), deviceID, false, false, true);
                    $.sDevices.base.changeWifiSpinner();
                })

                $(".p-delete-wifi-network").click(function() {

                    $(this).html(`
                        <img style="filter: invert(11%) sepia(91%) saturate(4421%) hue-rotate(334deg) brightness(87%) contrast(100%); height: 14px;" src="/assets/images/tail-spin.svg">
                    `)
                    $(this).css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                        // Add any other styles you want for your disabled state
                    })
                    .off('click');

                    $.sDevices.base.sendDeviceCommand('deleteWifiConnectionByUUID', $(this).data('uuid'), deviceID, false, false, true);
                    // Wait a few moments before requesting to refresh the ui...
                    setTimeout(function() { $.sDevices.base.sendDeviceCommand('detailedWifi', null, deviceID, false, false, true); }, 1000);

                })
                $(".p-change-wifi").click(function() {

                    $(this).parent().append(`<img style="height:20px; margin-left: 10px;" src="/assets/images/tail-spin.svg">`);

                    // Modify CSS properties directly and disable the click event to indicate it's now disabled.
                    $(this).css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                        // Add any other styles you want for your disabled state
                    })
                    .off('click');

                    $.sDevices.base.changeWifiNetworks(deviceID)
                })
                $(".p-close").click(function() {
                    swalWithBootstrapButtons.close()
                })
            },
            onClose: () => {}
        }).then(response => {
            if (response.value) {
                // 
            }
        });

    },

    deviceDetailsModel: function(deviceID, currentDevicesObj) {

        function getFileSystemStateClass(state) {
          switch(state.toLowerCase()) {  // converting state to lower case
            case 'clean':
              return 'state-clean';
            case 'dirty':
              return 'state-dirty';
            case 'errors':
              return 'state-errors';
            case 'orphaned inodes':
              return 'state-orphan-inodes';
            case 'non-contiguous':
              return 'state-non-contiguous';
            case 'recovering':
              return 'state-recovering';
            case 'needs recovery':
              return 'state-needs-recovery';
            case 'read-only':
              return 'state-read-only';
            default:
              return '';
          }
        }

        function getFileSystemStateDescription(state) {
          switch(state.toLowerCase()) {  // converting state to lower case
            case 'clean':
              return t('The file system is in good condition with no detected errors.');
            case 'dirty':
              return t('The file system has been mounted and there are changes that have not yet been written to disk. It is possible that a crash could result in loss of data.');
            case 'errors':
              return t('There are errors in the file system that require attention.');
            case 'orphaned inodes':
              return t('There are inodes that were open when the system crashed, and are no longer linked to any directory.');
            case 'non-contiguous':
              return t('The file system has non-contiguous blocks, which could impact performance.');
            case 'recovering':
              return t('The file system is currently in the process of recovering from a crash.');
            case 'needs recovery':
              return t('The file system has experienced a crash and requires recovery, but this process has not yet been initiated.');
            case 'read-only':
              return t('The file system has errors that have caused it to be mounted in a read-only state to prevent further damage.');
            default:
              return t('State unknown or not provided.');
          }
        }

        // match a hash from the device metadata back to application release metadata from balena - such as build: 200 etc ...
        function getApplicationReleaseFromHash(hash) {
            for (let obj of $.sDevices.base.knownBuildVersions) {
                if (obj.commit === hash) {
                    return obj;
                }
            }
            return false;
        }

        const currentDevice = currentDevicesObj[deviceID] || {}
        const { metadata, balenaUUID, lastIP, displayName, orientation, scheduledReboot, resolution, equipmentRound, updated: updatedDevice, relayStates } = currentDevice
        const { lastIP: metadataLastIP } = metadata || {}

        let deviceStatus = (metadata === undefined || metadata.isOnline === undefined) ? false : metadata.isOnline;
        const { updated: updatedMetaData } = metadata || {}
        const deviceLastSeen = updatedMetaData || updatedDevice;
        
        const deviceSerialNumber = currentDevice.deviceSerialNumber;
        const deviceType = currentDevice.deviceType;

        const deviceTypeMapping = {
          camera: t('CCTV Camera'),
          duress: t('Duress Button'),
          screen: t('Coaching Screen')
        };

        const deviceName = (() => {
            const deviceType = currentDevice.deviceType;
            switch (deviceType) {
                case 'camera':
                    return (currentDevice.deviceName === undefined) ? "" : currentDevice.deviceName
                case 'duress':
                    return (currentDevice.deviceRoom === undefined) ? '' : currentDevice.deviceRoom
                case 'screen':
                    return (currentDevice.displayName === undefined) ? "" : currentDevice.displayName
                default:
                    return '';
            }
        })();

        const modalIcon = (() => {
            const deviceType = currentDevice.deviceType;
            switch (deviceType) {
                case 'camera':
                    return '<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.667 2.5H3.333a.833.833 0 0 0-.833.833V5c0 .46.373.833.833.833h13.334c.46 0 .833-.373.833-.833V3.333a.833.833 0 0 0-.833-.833ZM10 15a3.333 3.333 0 1 0 0-6.667A3.333 3.333 0 0 0 10 15Z" stroke="currentColor" stroke-width="1.083" stroke-linecap="round" stroke-linejoin="round"/><path d="M15.833 5.833v5.834a5.833 5.833 0 1 1-11.666 0V5.833M10 11.667h.008" stroke="currentColor" stroke-width="1.083" stroke-linecap="round" stroke-linejoin="round"/></svg>';
                case 'duress':
                    return '<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.038 19.017H5.962a2.092 2.092 0 0 1-2.09-2.09V3.073c0-1.153.936-2.09 2.09-2.09h8.076c1.154 0 2.09.937 2.09 2.09V16.93a2.087 2.087 0 0 1-2.09 2.087Z" stroke="currentColor" stroke-width="1.016" stroke-miterlimit="10"/><path d="M10 8.537a2.683 2.683 0 1 0 0-5.366 2.683 2.683 0 0 0 0 5.366Z" fill="currentColor"/><path d="M10 13.439v1.927" stroke="currentColor" stroke-width=".581" stroke-miterlimit="10"/></svg>';
                case 'screen':
                    return '<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.051 18h7.897M2.103 4.462v9.025a1.128 1.128 0 0 0 1.128 1.128h13.538a1.129 1.129 0 0 0 1.128-1.128V4.461a1.128 1.128 0 0 0-1.128-1.128H3.231a1.128 1.128 0 0 0-1.128 1.128Z" stroke="currentColor" stroke-width="1.25"/></svg>';
                default:
                    return '';
            }
        })();

        let recordingEnabled = (currentDevice.recordingEnabled === undefined) ? true : currentDevice.recordingEnabled;
        const currentModalTitle = `${deviceName} - (${deviceTypeMapping[currentDevice.deviceType]})` || '';

        // Fire off a MQTT request to return the status of relays connected to the device...
        if(deviceStatus == true && currentDevice.deviceType == 'screen' && (!!metadata?.systemStats?.relayDeviceConnected)) {
            $.sDevices.base.sendDeviceCommand('relayDeviceStates', null, balenaUUID, false, false, true);
        }

        function getTpuTemperatureClass(temp) {
            let cssState;
            switch (true) {
                case (temp < 60):
                    cssState = "devices-color-ok";  // Green - Safe temperature
                    break;
                case (temp < 80):
                    cssState = "devices-color-warning";  // Orange - Caution zone
                    break;
                case (temp >= 80):
                    cssState = "devices-color-bad";  // Red - Overheating
                    break;
                default:
                    cssState = "";
                    break;
            }
            return cssState;
        }

        function getDeviceTemperatureClass(temp) {
            let cssState;
            switch (true) {
                case (temp < 68):
                    cssState = "devices-color-ok";  // Green for temperatures below 68°C
                    break;
                case (temp < 82):
                    cssState = "devices-color-warning";  // Orange for temperatures between 68°C and 82°C
                    break;
                case (temp >= 82):
                    cssState = "devices-color-bad";  // Red for temperatures above 82°C
                    break;
                default:
                    cssState = "";
                    break;
            }
            return cssState;
        }

        const averageStats = currentDevice.metadata?.aveDeviceStats || {};


        const LEGACY_DEVICE = (() => {
            try {
                const model = metadata?.deviceVersions?.DEVICE_MODEL;
                console.log('model', model)
                return model === "TS10" || model === "STK1AW32SC";
            } catch (e) {
                return false;
            }
        })();

        let deviceDetailsHTML = `
        <div class="device-details-dialog device-details-dialog--${currentDevice.deviceType} device-details-dialog--v2 ph-dialog-v2">

                ${
                  (deviceStatus === true || $.sDevices.base.checkBtnIsOnline(currentDevice)) 
                    ? (LEGACY_DEVICE && currentDevice.deviceType === 'screen' 
                        ? `
                            <div class="alert alert-red" style="margin-top: 20px; margin-bottom: 0px;">
                              <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                              <b>${t('This device has reached end-of-life and has been sunset')}</b><br>
                              <p style="font-weight: 300;">
                                ${t('Configuration options are no longer available as support for this device ended on <strong>Monday, 14 April 2025</strong>.')}
                              </p>
                            </div>
                          ` 
                        : ``)
                    : `
                        <div class="alert alert-red" style="margin-top: 20px; margin-bottom: 0px;">
                            <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                            <b>${t('Device is currently Offline')}</b><br>
                            <p style="font-weight: 300;">
                                ${t('Data displayed is the last know data recorded from the device.<br> Updates will be automatically applied when device reconnects next.')}
                            </p>

                              ${
                                // For devices that are offline - allow user to request to have device deleted...
                                (!deviceStatus) ? `
                                    <a href="https://forms.monday.com/forms/a58ac65fac9b656dd6bf9a341978295c?r=use1" style="margin-top: 10px;" class="btn btn-danger btn-outline waves-effect">${ t('Request to Remove Device from Account') }</a>
                                ` : ``
                              }

                        </div>
                      `
                }


              <ul class="nav nav-tabs tab-nav-right" role="tablist">
                <li role="presentation" class="active">
                  <a class="devices-config-tab" href="#device-dialog__details" data-toggle="tab">${t('Details')}</a>
                </li>
                <li role="presentation">
                  <a class="devices-config-tab" href="#device-dialog__configuration" data-toggle="tab">${t('Configuration')}</a>
                </li>
                ${
                  (deviceStatus == true && currentDevice.deviceType !== 'duress' && (!!metadata?.systemStats?.relayDeviceConnected)) ? `
                    <li role="presentation">
                      <a class="devices-config-tab" href="#device-dialog__relay" data-toggle="tab"><i style="margin-left:0px; background: none;" class="fa-solid fa-microchip"></i>&nbsp;${t('Relay Configuration')}</a>
                    </li>
                  ` : ``
                }
                ${
                  (deviceStatus != true || currentDevice.deviceType == 'duress') ? '' : `
                  <li role="presentation" class="device-dialog__advanced-tab">
                    <a class="devices-config-tab" href="#device-dialog__advanced" data-toggle="tab">${t('Advanced')}</a>
                  </li>
                  `
                }
              </ul>

              <div class="tab-content">
                <div role="tabpanel" class="tab-pane active" id="device-dialog__details">
                  <fieldset class="device-dialog__details">
                      ${
                          deviceType !== 'duress' ? `
                              <div class="row">
                                  <div class="col-sm-5">
                                      ${t('Device UUID')}:
                                  </div>
                                  <div class="col-sm-7">
                                      <div><code class="code-90">${ 
                                          (balenaUUID === undefined) ? t("Unknown") : balenaUUID }</code></div>
                                  </div>
                              </div>
                          ` : ''
                      }

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('Public IP')}:
                          </div>
                          <div class="col-xs-7">
                              <div><code>${
                                  metadataLastIP ? `${metadataLastIP}` :
                                  (lastIP === undefined || lastIP === "0.0.0.0") ? "" : ((lastIP == "") ? "" : lastIP)
                              }</code></div>
                          </div>
                      </div>

                      ${
                          deviceType !== 'duress' ? `
                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Local/LAN IP')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                          <code>${ (metadata?.systemStats?.network?.default_interface_ip === undefined) ? "" : metadata.systemStats.network.default_interface_ip}</code>
                                      </div>
                                  </div>
                              </div>
                          ` : ''
                      }

                      ${
                          deviceType == 'duress' ? `

                              <div class="row">
                                  <div class="col-sm-5">
                                      ${t('Temperature')}:
                                  </div>
                                  <div class="col-sm-7">

                                      <div>
                                          ${(averageStats.deviceTemperature === undefined) 
                                              ? t("Unknown") 
                                              : formatTemperature(averageStats.deviceTemperature, globalStore.activeUnitOfMeasurement)}
                                      </div>

                                  </div>
                              </div>

                              <div class="row">
                                  <div class="col-sm-5">
                                      ${t('Battery Level')}:
                                  </div>
                                  <div class="col-sm-7">
                                      <div style="align-items: center; display: flex; margin-top: 3px;">
                                        ${
                                          averageStats.batteryPercent ? `
                                              <span class="device-cell__tag device-cell__tag--battery battery-${$.sDevices.base.getPercentClass(parseInt(averageStats.batteryPercent))} ${$.sDevices.base.checkBtnIsOnline(currentDevice) ? '' : 'device-offline'}">
                                                <span style="width: ${averageStats.batteryPercent}"></span>
                                              </span>
                                            `
                                            : ''
                                        }
                                        ${averageStats.batteryVoltage ?? t("Unknown")} mV
                                        ${averageStats.batteryPercent && `(${averageStats.batteryPercent})`}
                                      </div>
                                  </div>
                              </div>


                          ` : ''
                      }

                      ${
                          metadata?.defaultNetworkSource === 'ethernet' ? '' : `
                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Signal Strength')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div class="wifi-experience-container">
                                          <div class="wifi-experience-bar"></div>
                                          <div class="wifi-experience-text"></div>
                                      </div>
                                  </div>
                              </div>
                          `
                      }

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('Connection')}:
                              ${ (deviceStatus == false) ? `<br><small>(${t('Last known details')})</small>` : "" }
                          </div>

                          <div class="col-xs-7">
                            <div class="ssidName">
                              ${
                                  metadata?.defaultNetworkSource === 'ethernet' ?
                                      `${t('Ethernet Connected')} <span class="ethernet-icon"></span>` :
                                      $.sDevices.base.getDeviceSSID(currentDevice)
                              }
                              ${ deviceType === 'duress' ? '<span class="bluetooth-icon"></span>' : '' }
                            </div>
                            ${
                              metadata?.defaultNetworkSource != 'ethernet' && deviceType !== 'duress' ?
                              `
                                <div class="smallText">
                                  ${t('Security')}: ${metadata?.wifi?.security ?? t('None')}
                                </div>
                                <div class="smallText">
                                  ${t('Channel')}: ${metadata?.wifi?.channel ?? ''}
                                </div>
                                <div class="smallText">
                                  ${t('Frequency')}: ${metadata?.wifi?.frequency ?? t('Unknown')}
                                  <span class="badge bg-wifi-${ (metadata?.wifi?.frequency === undefined) ? '2' : String(metadata.wifi.frequency).slice(0, 1) }">${( metadata?.wifi?.frequency === undefined) ? '' : String(metadata.wifi.frequency).slice(0, 2).splice(1, 0, ".")} GHz</span>
                                </div>
                                <div class="smallText">
                                  ${t('Manufacturer')}: ${t(metadata?.wifi?.manufacturer) ?? t('Unknown')}
                                </div>
                              ` : ''
                            }
                          </div>

                      </div>

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('Device Serial Number')}:
                          </div>
                          <div class="col-xs-7">
                              <div>
                                  <code class="code-90">${ 
                                      ((deviceType === 'camera' || deviceType === 'duress') && deviceSerialNumber
                                      ) ? 
                                      deviceSerialNumber
                                      :
                                      (metadata?.deviceVersions?.DEVICE_SN === undefined) ? t("Unknown") : metadata.deviceVersions.DEVICE_SN }
                                  </code>

                              ${
                                  // For Sticks/Nucs/Devices with an OS that can return a serial number
                                  (metadata?.deviceVersions?.DEVICE_MODEL && metadata.deviceVersions.DEVICE_MODEL !== "Docker Container")
                                  ? `<br><small class="device-type">${
                                      (metadata.deviceVersions.DEVICE_MODEL === "TS10") ? "Asus " :
                                      (metadata.deviceVersions.DEVICE_MODEL === "STK1AW32SC") ? "Intel " :
                                      (metadata.deviceVersions.DEVICE_MODEL.includes("PN41")) ? "Asus " : ""
                                  }${metadata.deviceVersions.DEVICE_MODEL}</small>`
                                  : ''
                              }
                              ${
                                  // If device is a camera - try include the SN of the attached USB camera also
                                  deviceType == 'camera' ? `
                                      <br><small class="device-type"><code class="code-90">${ metadata?.activeCamera?.serialShort ?? '' }</code> (${ t('Camera Chip') }}</small>
                                  ` : ''
                              }

                              </div>
                          </div>
                      </div>

                      ${
                          deviceType !== 'duress' ? `

                          <div class="row ${ (deviceStatus == false) ? " hidden" : "" }">
                              <div class="col-xs-5">
                                  ${t('CPU')}:
                              </div>
                              <div class="col-xs-7">
                                  <div class="${getDeviceTemperatureClass(parseFloat(metadata?.systemStats?.cpu?.temperature))}">
                                      ${ (metadata?.systemStats?.cpu?.temperature ?? null) === null 
                                          ? t("Unknown") 
                                          : formatTemperature(metadata.systemStats.cpu.temperature, globalStore.activeUnitOfMeasurement) } 
                                      <div class="cpu-used-percent"></div>
                                  </div>
                              </div>
                          </div>

                          <div class="row ${ (deviceStatus == false) ? " hidden" : "" }">
                              <div class="col-xs-5">
                                  ${t('Activity Down/Up')}:
                              </div>
                              <div class="col-xs-7">
                                  <div>${
                                      humanFileSize((metadata?.systemStats?.network?.rx_bytes === undefined) ? 0 : metadata.systemStats.network.rx_bytes, true)
                                  } / ${
                                      humanFileSize((metadata?.systemStats?.network?.tx_bytes === undefined) ? 0 : metadata.systemStats.network.tx_bytes, true)
                                  }</div>
                              </div>
                          </div>

                          <div class="row ${ (deviceStatus == false) ? " hidden" : "" }">
                              <div class="col-xs-5">
                                  ${t('Uptime')}:
                              </div>
                              <div class="col-xs-7">
                                  <div>${ secondsToDhms((metadata?.systemStats?.uptime === undefined) ? 0 : metadata.systemStats.uptime) }</div>
                              </div>
                          </div>
                          ` : ''
                      }

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('Last seen')}:
                          </div>
                          <div class="col-xs-7">
                              <div>${

                                  (deviceStatus === true) ? t('Online') :
                                      ((deviceLastSeen === false) ? `<div class="badge badge-red">Never<i class="fa fa-exclamation-triangle" aria-hidden="true"></i></div>` :
                                          `<div class="badge badge-red">${ timeDifference(new Date(), new Date(deviceLastSeen)) } <i class="fa fa-exclamation-triangle" aria-hidden="true"></i></div>`
                                      )
                              }</div>
                          </div>
                      </div>

                      ${
                          (currentDevice.deviceType == 'screen') ? `

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Pairing ID')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div><code>${ String(deviceID).slice(0,3) + '-' + String(deviceID).slice(3, 6) }</code></div>
                                  </div>
                              </div>


                              ${
                                  (
                                      (currentDevice?.metadata?.sonos2MqttMaster === false && currentDevice?.metadata?.masterClock === false)
                                  ) ? '' : `

                                  <div class="row" style="margin-top:3px;">
                                      <div class="col-xs-5">
                                          ${t('Peripheral Services')}:
                                      </div>

                                      <div class="col-xs-7">
                                        <div>
                                          ${ 
                                            currentDevice?.metadata?.sonos2MqttMaster ? `
                                                  <span 
                                                    class="device-cell__tag device-cell__tag--sonos"
                                                    data-toggle="tooltip"
                                                    data-placement="right"
                                                    title="${t('This device has been automatically selected to control your Sonos devices')}"
                                                  ><i></i></span>
                                                `
                                              : '' 
                                          }
                                          ${ 
                                            currentDevice?.metadata?.masterClock ? `
                                                  <span 
                                                    class="device-cell__tag device-cell__tag--masterclock"
                                                    data-toggle="tooltip"
                                                    data-placement="right"
                                                    title="${t('This device has been automatically selected to be the Master Clock - The Master Clock triggers the bell timer on all your devices in unison')}"
                                                  ><i></i></span>
                                                `
                                              : '' 
                                          }
                                        </div>
                                      </div>

                                  </div>
                              `}
                              
                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Connected Display')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                        ${ metadata?.displayInformation?.edidData?.generalInfo ? `${getManufacturerNameFromAssetCode(metadata.displayInformation.edidData.generalInfo.manufacturer)} ${metadata.displayInformation.edidData.generalInfo.displayProductName}` : t("Unknown")  }
                                        ${ metadata?.displayInformation?.currentResolution ? `
                                            <br>
                                            <small style="font-weight: bold;">
                                                (${t('Outputting at')} ${ metadata?.displayInformation?.currentResolution ?? ''})
                                            </small>` : ``
                                        }
                                      </div>
                                  </div>
                              </div>

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Display Serial Number')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                        <code class="code-90">
                                          ${ metadata?.displayInformation?.edidData?.generalInfo?.displayProductSerialNumber ?? metadata?.displayInformation?.edidData?.generalInfo?.serialNumber ?? t("Unknown") }
                                        </code>
                                      </div>
                                  </div>
                              </div>

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Display Audio Support')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                        ${ metadata?.displayInformation?.edidData?.audioData?.type ? `${metadata.displayInformation.edidData.audioData.type} (${metadata.displayInformation.edidData.audioData.maxChannels} ${t('Channels')} )` : t("Not Found")  }
                                      </div>
                                  </div>
                              </div>


                          ` : ``
                      }

                      ${
                          (currentDevice.deviceType == 'camera') ? `

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('SD Storage Utilization')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                          ${metadata?.sdMetrics?.diskSpace?.percentUsed ?? t('Unknown')} 
                                          (${metadata?.sdMetrics?.diskSpace?.used} ${t('Used')} / ${metadata?.sdMetrics?.diskSpace?.total ?? t('Unknown')})
                                      </div>
                                  </div>
                              </div>

                            ${
                                metadata?.sdMetrics?.sdCards?.mmcblk1?.mountPoint === '/media/sdcard' ? `
                                    <div class="row">
                                        <div class="col-xs-5">
                                            ${t('SD Storage Type')}:
                                        </div>
                                        <div class="col-xs-7">
                                            <div class="sd-storage-stats">
                                                ${metadata?.sdMetrics?.sdCards?.mmcblk1?.decodedCID?.MID ?? t('Unknown')} 
                                                <small>(${t('Class')} ${metadata?.sdMetrics?.sdCards?.mmcblk1?.class ?? t('Unknown')}, ${metadata?.sdMetrics?.sdCards?.mmcblk1?.speed ?? t('Unknown')} ${t('Speed')})</small>
                                            </div>
                                        </div>
                                    </div>
                                ` : ''
                            }

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('SD Filesystem State')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div class="filesystem-state-stats ${getFileSystemStateClass( metadata?.sdMetrics?.fileSystemState ?? t('Unknown') )}">
                                          ${ t(metadata?.sdMetrics?.fileSystemState) ?? t('Unknown')}
                                      <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${ getFileSystemStateDescription( metadata?.sdMetrics?.fileSystemState ?? t('Unknown') ) }">info_outline</i>
                                      </div>
                                  </div>
                              </div>

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('SD Writes Remaining')}:
                                      <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t('The estimated lifespan of the internal SD card (writes remaining) is a measure of the estimated remaining life of the SD card, calculated based on the total data that has been written to it so far and the typical lifespan of an SD card, with the assumption that the SD card has a total write capacity of ~64-128TB (depending on the size of your SD)')}">info_outline</i>
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                          ${(metadata?.sdMetrics?.estimatedLifeRemaining !== undefined && metadata?.sdMetrics?.estimatedLifeRemaining !== null && !isNaN(metadata?.sdMetrics?.estimatedLifeRemaining)) ? Number(metadata?.sdMetrics?.estimatedLifeRemaining).toFixed(2) : t('Unknown')}%
                                          <small>(${ metadata?.sdMetrics?.totalDataWritten } ${t('written to-date')})</small>
                                      </div>
                                  </div>
                              </div>

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Uploads Queued')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                        ${metadata?.streamController?.queue?.pendingPromises ?? t('None')} 
                                      </div>
                                  </div>
                              </div>


                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Camera Make')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div class="filesystem-state-stats">
                                          ${ metadata?.activeCamera?.vendor ?? t('Unknown')} ${ metadata?.activeCamera?.model }
                                          <small class="device-type">(${ metadata?.activeCamera?.devicePath })</small>
                                      </div>
                                  </div>
                              </div>

                              ${currentDevice?.lensType 
                                ? `
                                  <div class="row">
                                      <div class="col-xs-5">
                                          ${t('Camera Lens Type')}:
                                      </div>
                                      <div class="col-xs-7">
                                          <div class="">
                                              ${ currentDevice?.lensType ?? t('Unknown')} ${t('Millimeter')}
                                          </div>
                                      </div>
                                  </div>
                                ` 
                                : ''
                              }

                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('AI Accelerator Chip')}:
                                  </div>
                                  <div class="col-xs-7">
                                      ${
                                          metadata?.systemStats?.tpuInfo?.tpuFound ? `
                                              <div class="tpu-stats">
                                                  ${t('Status')}: 
                                                  <span class="${metadata?.systemStats?.tpuInfo?.status === 'ALIVE' ? 'devices-color-ok' : 'devices-color-bad'}">
                                                      ${metadata?.systemStats?.tpuInfo?.status ?? t('Unknown')}
                                                  </span>, 
                                                  ${t('Temperature')}: 
                                                  <span class="${getTpuTemperatureClass(parseFloat(metadata?.systemStats?.tpuInfo?.temperature))}">
                                                      ${metadata?.systemStats?.tpuInfo?.temperature ?? t('Unknown')}°C
                                                  </span>
                                              </div>
                                              <div class="tpu-stats">
                                                  ${t('Driver')}: ${metadata?.systemStats?.tpuInfo?.driverVersion ?? t('Unknown')}, 
                                                  ${t('Framework')}: ${metadata?.systemStats?.tpuInfo?.frameworkVersion ?? t('Unknown')}
                                              </div>
                                          ` : `
                                              <div class="tpu-stats" style="color: red;">
                                                  ${t('NOT FOUND')}
                                              </div>
                                          `
                                      }
                                  </div>
                              </div>

                              ${

                                  (metadata?.systemStats?.cpu?.temperature ?? null) === null ? '' : 
                                      (metadata.systemStats.cpu.temperature > $.sDevices.base.cctvOverheatingTemperature) ? `


                                      <div class="row">
                                          <div class="col-xs-12">
                                              <div class="alert alert-red">
                                                  <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                                                  <b>${t('Device Overheating')}</b><br>
                                                  <p style="font-weight: 300;">
                                                      ${t('Performance will be limited, this device is Thermal Throttled. Devices exceeding 95°C will shut down')}
                                                  </p>
                                              </div>

                                          </div>
                                      </div>

                                      ` : ``
                              }


                          ` : ``
                      }

                      ${
                          // Only show if we have release information available...
                          (getApplicationReleaseFromHash(metadata?.deviceVersions?.APPLICATION_RELEASE ?? "")) ? `
                              <div class="row">
                                  <div class="col-xs-5">
                                      ${t('Software Information')}:
                                  </div>
                                  <div class="col-xs-7">
                                      <div>
                                        <div class="smallText">
                                          ${t('Build')}: ${ (getApplicationReleaseFromHash(metadata?.deviceVersions?.APPLICATION_RELEASE ?? "")) ? getApplicationReleaseFromHash(metadata?.deviceVersions?.APPLICATION_RELEASE ?? "").revision : t('Unknown') }
                                        </div>
                                        <div class="smallText">
                                          ${t('Released')}: ${ (getApplicationReleaseFromHash(metadata?.deviceVersions?.APPLICATION_RELEASE ?? "")) ? moment(getApplicationReleaseFromHash(metadata?.deviceVersions?.APPLICATION_RELEASE ?? "").created_at).format('YYYY-MM-DD') : t('Unknown') }
                                        </div>
                                        <div class="smallText">
                                          ${ t(metadata?.deviceVersions?.BALENA_HOST_OS_VERSION) ?? '' } (${t('Supervisor')}: ${ t(metadata?.deviceVersions?.BALENA_SUPERVISOR_VERSION) ?? t('Unknown') })
                                        </div>
                                      </div>
                                  </div>
                              </div>
                          ` : ``
                      }

                      ${
                        // Show the Display ON|OFF buttons if a the very bottom if the device is a screen.
                        (currentDevice.deviceType == 'screen' && deviceStatus === true) ? `
                          <div class="row device-toggles">
                              <div class="col-sm-12 text-center" style="margin-top: 10px;">
                                  <a class="p-display p-display-off waves-effect">
                                      ${t('Turn Display OFF')}
                                  </a>
                                  <a class="p-display p-display-on waves-effect">
                                      ${t('Turn Display ON')}
                                  </a>
                              </div>
                          </div>
                          ` : `` }

                  </fieldset>
                </div>
                <div role="tabpanel" class="tab-pane" id="device-dialog__configuration">
                  <fieldset class="device-dialog__configuration">
                      ${ 
                          // Screen Specific UI ...
                          (currentDevice.deviceType == 'screen' || currentDevice.deviceType == 'camera') ? `

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Device Name')}:
                              </div>
                              <div class="col-xs-7">
                                  <input data-hj-allow class="display-name" type="text" value="${ (currentDevice.displayName === undefined) ? "" : currentDevice.displayName}${ (currentDevice.deviceName === undefined) ? "" : currentDevice.deviceName}" placeholder="Round #1 or Door Camera">
                              </div>
                          </div>
                      
                      ` : '' }

                      ${ 
                          // Screen Specific UI ...
                          (currentDevice.deviceType == 'screen') ? `
                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Equipment Associated')}:
                              </div>
                              <div class="col-xs-7">
                                  ${$.sDevices.base.equipmentSelect()}
                              </div>
                          </div>

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Display Orientation')}:
                              </div>
                              <div class="col-xs-7">
                                  <select class="display-orientation">
                                      <option value="normal">${t('Normal')}</option>
                                      <option value="left">${t('Rotate 90° Left')}</option>
                                      <option value="right">${t('Rotate 90° Right')}</option>
                                      <option value="inverted">${t('Inverted (Upside Down)')}</option>
                                  </select>
                              </div>
                          </div>

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Screen Resolution')}:
                              </div>
                              <div class="col-xs-7">
                                  <select class="display-resolution" style="width:100%;">
                                      <option value="auto">${t('Automatic')}</option>
                                      <option data-divider="true"></option>
                                      <option value="1920x1080">1920x1080</option>
                                      <option value="1280x720">1280x720</option>
                                      <option value="720x400" data-subtext="${t('Lowest, use on slow devices')}">720x400</option>
                                  </select>
                              </div>
                          </div>

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Scheduled Reboot')}:
                                  <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right"
                                      title="${t('Use Scheduled Reboots to automatically restart the device at 2 AM (local time) to enhance performance, clears memory leaks, and facilitate updates. This also helps provide smoother operation on older devices.')}">info_outline</i>
                              </div>
                              <div class="col-xs-7">
                                  <select class="scheduled-reboot" style="width:100%;">
                                      <option value="disabled">${t('Disabled')}</option>
                                      <option data-divider="true"></option>
                                      <option value="daily" data-subtext="${t('2AM')}">Daily</option>
                                      <option value="weekly" data-subtext="${t('Sunday - 2AM')}">Weekly</option>
                                      <option value="monthly" data-subtext="${t('First Sunday of the month - 2AM')}">Monthly</option>
                                  </select>
                              </div>
                          </div>

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Sonos & Master Clock')}:
                                  <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right"
                                      title="${t('Use this configuration to enable/disable this device from either controlling your Sonos Devices or Becoming the Master Clock (the device which synchronizes the timer to the rest of your displays). We recommend disabling Master Clock and Sonos Controller on devices with bad Wifi Signal or that often freeze up. Note you require a minimum of 4 Devices or your timer/Sonos will totally stop.')}">info_outline</i>
                              </div>
                              <div class="col-xs-7">
                                  <div class="hide-devices-group">
                                      <div class="form-check styled-checkbox" style="margin-top:5px;">
                                          <input id="prevent-from-becoming-master-clock" type="checkbox" class="filled-in chk-col-blue" ${ (currentDevice.preventMasterClock !== undefined && currentDevice.preventMasterClock == true) ? `checked="checked"` : '' }>
                                          <label for="prevent-from-becoming-master-clock">${t('Prevent from becoming Master Clock')}</label>
                                      </div>
                                  </div>
                                  <div class="hide-devices-group">
                                      <div class="form-check styled-checkbox" style="margin-top:5px;">
                                          <input id="disable-audio-on-device" type="checkbox" class="filled-in chk-col-blue" ${ (currentDevice.disableMasterClockAudio !== undefined && currentDevice.disableMasterClockAudio == true) ? `checked="checked"` : '' }>
                                          <label for="disable-audio-on-device">${t('Disable audio output on this device')}</label>
                                      </div>
                                  </div>
                                  <div class="hide-devices-group">
                                      <div class="form-check styled-checkbox" style="margin-top:5px;">
                                          <input id="prevent-from-becoming-sonos-controller" type="checkbox" class="filled-in chk-col-blue" ${ (currentDevice.preventSonosController !== undefined && currentDevice.preventSonosController == true) ? `checked="checked"` : '' }>
                                          <label for="prevent-from-becoming-sonos-controller">${t('Prevent from becoming Sonos Controller')}</label>
                                      </div>
                                  </div>
                              </div>
                          </div>

                          <div class="row vertical-align-row" style="margin-top: -25px; min-height: 90px;">
                              <div class="col-sm-5">
                                  ${t('Base Display Brightness')}:
                                  <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t('This configuration sets the baseline display brightness setting. You can adjust your displays to automatically dim during evening periods, and then brighten during the day in the Coaching Screen settings')}">info_outline</i>
                              </div>
                              <div class="col-sm-7 slider-wrapper-col">
                                  <div class="slider-wrapper" style="margin-right: 10px;">
                                      <div class="slider" id="display_brightness_slider"></div>
                                  </div>
                              </div>
                          </div>

                          <div class="row vertical-align-row" style="margin-top: -25px; min-height: 90px;">
                              <div class="col-sm-5">
                                  ${t('Underscan')}:
                                  <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t('Increasing the Underscan will move your content to the center of the display - this is useful if part of the image or text is cut off on your display.')}">info_outline</i>
                              </div>
                              <div class="col-sm-7 slider-wrapper-col">
                                  <div class="slider-wrapper" style="margin-right: 10px;">
                                      <div class="slider" id="underscan_slider"></div>
                                  </div>
                              </div>
                          </div>

                      ` : `` }

                      ${ 
                          (currentDevice.deviceType == 'camera' || currentDevice.deviceType == 'duress') ? `

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Location')}:
                              </div>
                              <div class="col-xs-7">
                                  <div class="location-group-container">
                                      <select data-hj-allow class="location-group"><option></option></select>
                                  </div>
                              </div>
                          </div>

                      ` : '' }

                      ${ 
                          // Camera specific UI components...
                          (currentDevice.deviceType == 'camera') ? `

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Resolution')}:
                                  <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t('If you have slow internet, or your cameras are struggling to upload video and view the live stream simultaneously, try setting a lower resolution.')}">info_outline</i>
                              </div>
                              <div class="col-xs-7">

                                  <select class="preferred-resolution selectpicker-border" title="${t('Please Select')}">
                                      <optgroup>
                                          <option value="1920x1080" data-subtext="${t('Highest Quality (Larger files)')}">1920x1080</option>
                                          <option value="1280x720" data-subtext="">1280x720</option>
                                          <option value="640x360" data-subtext="${t('Lowest Quality (Use with slow internet)')}">640x360</option>
                                      </optgroup>
                                  </select>

                              </div>
                          </div>

                          <div class="row vertical-align-row">
                              <div class="col-xs-5">
                                  ${t('Recording')}:
                                  <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t('Setting to motion based recording will only record when motion is detected in front of the camera. To Adjust the sensitivity of motion detection, please adjust the Motion Threshold in the Advanced Settings.')}">info_outline</i>
                              </div>
                              <div class="col-xs-7">
                                  <div class="switch">
                                      <label>${t('Disabled')} <input type="checkbox" id="recordingEnabled" ${ (recordingEnabled == true) ? 'checked' : '' }><span class="lever lever switch-col-blue"></span> ${t('Enabled')}</label>
                                  </div>
                              </div>
                          </div>

                          <div class="row vertical-align-row">
                              <div class="col-xs-5"></div>
                              <div class="col-xs-7">
                                  <select class="recording-mode selectpicker-border" title="${t('Please Select')}">
                                      <optgroup>
                                          <option value="continuous">${t('Continuous')}</option>
                                          <option value="motion">${t('Motion Triggered')}</option>
                                      </optgroup>
                                  </select>
                              </div>
                          </div>

                              ${
                                  (deviceStatus != true) ? '' : `

                                  <div class="row" style="border-top: 1px solid #d3d3d3; margin: 2px;"></div>
                                  <div class="row device-toggles motion-threshold" style="margin-top: 10px; min-height: 50px; margin-bottom: 30px;">
                                      <div class="col-xs-5">
                                          ${t('Motion Threshold')}:
                                          <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="${t('The Motion Threshold is the number of pixels changed (between video frames) to \'trigger\' a recording. A lower value will trigger recordings with less movement in front of the camera, whereas higher values will result in more motion required to trigger a recording.')}">info_outline</i>
                                      </div>
                                      <div class="col-xs-7">
                                          <div class="slider-wrapper" style="margin-right: 20px;">
                                              <div class="slider" id="motion_threshold_slider"></div>
                                          </div>
                                      </div>
                                  </div>

                                  <div class="row vertical-align-row" style="margin: 0px; margin-top: 5px; padding-top: 5px;">
                                      <div class="col-sm-12 text-center">
                                          <a class="p-motion-zone btn btn-xs btn-default waves-effect">
                                              <i class="fa-solid fa-border-none"></i> ${t('Adjust Motion Zone')}
                                          </a>
                                          <a class="p-brightness-contrast btn btn-xs btn-default waves-effect">
                                              <i class="fa-solid fa-circle-half-stroke"></i> ${t('Adjust Image Settings')}
                                          </a>
                                      </div>
                                  </div>

                              `}
                          ` : `` }
                  </fieldset>
                </div>
                <div role="tabpanel" class="tab-pane" id="device-dialog__relay">

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('Connected Relay Adapter')}:
                          </div>
                          <div class="col-xs-7">
                              <div>
                                  ${ metadata?.systemStats?.relayDeviceConnected?.manufacturer ?? t("Unknown") }
                                  <br>(${ metadata?.systemStats?.relayDeviceConnected?.path ?? '' })
                              </div>
                          </div>
                      </div>

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('UID')}:
                          </div>
                          <div class="col-xs-7">
                              <div>
                                <small>
                                  ${ metadata?.systemStats?.relayDeviceConnected?.pnpId ?? t("Unknown") }
                                </small>
                              </div>
                          </div>
                      </div>

                      <div class="row">
                          <div class="col-xs-5">
                              ${t('Adapter Serial Number')}:
                          </div>
                          <div class="col-xs-7">
                              <div>
                                <code class="code-90">
                                  ${ metadata?.systemStats?.relayDeviceConnected?.serialNumber ?? t("Unknown") }
                                </code>
                              </div>
                          </div>
                      </div>

                      <hr>

                      <h5>
                          ${t('Relay Event Triggers')}:
                          <i style="font-size: 14px;" class="material-icons info-icon" data-toggle="tooltip" data-placement="right"
                            title="${t('When a specific event happens from your smart facility connected technology such as a duress button alarm is triggered, or your screens/technology turns on, the relay can be configured to be toggled on|off')}">info_outline</i>
                      </h5>

                      <div class="row vertical-align-row">
                        <div class="col-xs-2"><b>${t('Relay #')}:</b></div>
                        <div class="col-xs-5"><b>${t('Action')}:</b></div>
                        <div class="col-xs-1"></div>
                        <div class="col-xs-3"><b>${t('Current State')}:</b></div>
                      </div>

                      <div class="relay-containers"></div>

                </div>
                <div role="tabpanel" class="tab-pane" id="device-dialog__advanced">
                  ${
                    (deviceStatus != true || currentDevice.deviceType == 'duress') ? "" : `
                        <fieldset class="device-dialog__advanced">
    
                            <div class="row device-toggles">
                                <div class="col-sm-5">
                                    <b>${t('Troubleshooting')}:</b>
                                </div>
                                <div class="col-sm-7">
                                    <a class="p-reboot btn btn-xs btn-default waves-effect">
                                        <svg style="filter: invert(47%) sepia(6%) saturate(977%) hue-rotate(185deg) brightness(50%) contrast(84%); height: 21px; margin-right: 5px; margin-bottom: -5px; margin-top: -2px;margin-left: -2px;" xmlns="http://www.w3.org/2000/svg" version="1" viewBox="0 0 24 24" enable-background="new 0 0 24 24"><path style="text-indent:0;text-align:start;line-height:normal;text-transform:none;block-progression:tb;-inkscape-font-specification:Bitstream Vera Sans" d="M 12 2 L 12 4 C 16.466667 4 20 7.5333333 20 12 C 20 16.466667 16.466667 20 12 20 C 7.5333333 20 4 16.466667 4 12 C 4 10.308254 4.5299182 8.7424071 5.4375 7.4375 L 7.59375 9.59375 L 9 2.5 L 2 4 L 4.03125 6.03125 C 2.7731486 7.7064434 2 9.7599441 2 12 C 2 17.533333 6.4666667 22 12 22 C 17.533333 22 22 17.533333 22 12 C 22 6.4666667 17.533333 2 12 2 z" overflow="visible" enable-background="accumulate" font-family="Bitstream Vera Sans"/></svg>
                                        ${t('Remotely Reboot Device')}
                                    </a>
                                </div>
                            </div>
    
    
                            <div class="row device-toggles">
                                <div class="col-sm-5">
                                    <b>${t('Networking')}:</b>
                                </div>
                                <div class="col-sm-7">
                                    <a class="p-manage-wifi-networks btn btn-xs btn-default waves-effect">
                                        <i class="material-icons">wifi</i> ${t('Manage Wifi Networks')}
                                    </a>
                                </div>
                            </div>

                            <div class="row device-toggles">
                                <div class="col-sm-5">
                                    <b>${t('Updates')}:</b>
                                </div>
                                <div class="col-sm-7">
                                    <a class="p-unlock-updates btn btn-xs btn-default waves-effect">
                                        <span style="height: 17px; width: 25px;">
                                            <svg xmlns="http://www.w3.org/2000/svg" version="1" viewBox="0 0 24 24" fill="currentColor" style="padding: 1px;" enable-background="new 0 0 24 24"><path style="text-indent:0;text-align:start;line-height:normal;text-transform:none;block-progression:tb;-inkscape-font-specification:Bitstream Vera Sans" d="M 12 2 L 12 4 C 16.466667 4 20 7.5333333 20 12 C 20 16.466667 16.466667 20 12 20 C 7.5333333 20 4 16.466667 4 12 C 4 10.308254 4.5299182 8.7424071 5.4375 7.4375 L 7.59375 9.59375 L 9 2.5 L 2 4 L 4.03125 6.03125 C 2.7731486 7.7064434 2 9.7599441 2 12 C 2 17.533333 6.4666667 22 12 22 C 17.533333 22 22 17.533333 22 12 C 22 6.4666667 17.533333 2 12 2 z" overflow="visible" enable-background="accumulate" font-family="Bitstream Vera Sans"></path></svg>
                                        </span>
                                        ${t('Unlock Updates on Device')}
                                    </a>
                                </div>
                            </div>
    
                            ${ 
                                // Screen Specific UI ...
                                (currentDevice.deviceType == 'screen') ? `
    
                                    <div class="row device-toggles">
                                        <div class="col-sm-5">
                                            <b>${t('Setup & Debugging')}:</b>
                                        </div>
                                        <div class="col-sm-7">
                                            <a class="p-debug-on btn btn-xs btn-default waves-effect">
                                                <i class="material-icons">bug_report</i> ${t('Debug On')}
                                            </a>
                                            <a class="p-debug-off btn btn-xs btn-default waves-effect">
                                                <i class="material-icons">bug_report</i> ${t('Debug Off')}
                                            </a>
                                        </div>
                                    </div>
                                    <div class="row device-toggles">
                                        <div class="col-sm-5">
                                            <b>${t('Display')}:</b>
                                        </div>
                                        <div class="col-sm-7">
                                            <a class="p-capture-screenshot btn btn-xs btn-default waves-effect">
                                                <i class="material-icons">photo_camera</i> ${t('Capture Screenshot')}
                                            </a><br>
                                            <a style="margin-top:5px;" class="p-load-url btn btn-xs btn-default waves-effect">
                                                <i class="material-icons">language</i> ${t('Load Remote URL')}
                                            </a><br>
                                            <a style="margin-top:5px;" class="p-identify-display btn btn-xs btn-default waves-effect">
                                                <svg version="1.1" style="filter: invert(47%) sepia(6%) saturate(977%) hue-rotate(185deg) brightness(50%) contrast(84%); height: 21px; margin-right: 5px; margin-bottom: -5px; margin-top: -2px;margin-left: -2px;" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 800 800" xml:space="preserve"><style>.st0{fill:#222}</style><path class="st0" d="M240 40v200h40V80h480v320H600v40h200V40H240zM0 280v400h200v40h-80v40h320v-40h-80v-40h200V280H0zm40 40h480v320H40V320zm560 160v40h80v-40h-80zM240 680h80v40h-80v-40z" id="layer1"/><path class="st0" d="M370.4 554.4v26.7H221.1c-.2-6.7.9-13.1 3.2-19.3 3.8-10.2 9.9-20.2 18.3-30.1 8.4-9.9 20.5-21.3 36.3-34.2 24.6-20.1 41.2-36.1 49.8-47.9 8.6-11.8 12.9-22.9 12.9-33.4 0-11-3.9-20.3-11.8-27.8-7.9-7.6-18.1-11.3-30.7-11.3-13.4 0-24 4-32.1 12-8 8-12.1 19.1-12.2 33.3l-28.5-2.9c2-21.3 9.3-37.5 22-48.6 12.7-11.1 29.8-16.7 51.3-16.7 21.7 0 38.8 6 51.5 18s19 26.9 19 44.7c0 9-1.8 17.9-5.5 26.7-3.7 8.7-9.8 17.9-18.4 27.6-8.6 9.7-22.8 22.9-42.8 39.8-16.6 14-27.3 23.5-32.1 28.4-4.7 5-8.6 10-11.7 15h110.8zM520.7 240H493v-60.3c-6.7 6.4-15.4 12.7-26.3 19.1-10.8 6.4-20.6 11.2-29.2 14.3v-26.8c15.5-7.3 29.1-16.1 40.7-26.5 11.6-10.4 19.8-20.4 24.7-30.2h17.9V240z"/></svg>
                                                ${t('Identify Display')}
                                            </a>
                                        </div>
                                    </div>
                                ` : ``
                            }
    
                            ${ 
                                // Camera Specific UI ...
                                (currentDevice.deviceType == 'camera') ? `
    
                                    <div class="row device-toggles">
                                        <div class="col-sm-5">
                                            <b>${t('Storage')}:</b>
                                        </div>
                                        <div class="col-sm-7">
                                            <a class="p-purge-internal-storage btn btn-xs btn-default waves-effect">
                                                <i class="material-icons">sd_storage</i> ${t('Purge Internal Storage')}
                                            </a>
                                        </div>
                                    </div>
                                ` : ``
                            }
    
                        </fieldset>
                    `
                  }
                </div>
              </div>

        </div>`;

        let deviceUpdateObj;

        swalWithBootstrapButtons.fire({
            // title: t('Device Details'),
            title: `${modalIcon}${currentModalTitle}`.trim(),
            html: deviceDetailsHTML,
            width: 500,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: true,
            confirmButtonText: t("Save Changes"),
            cancelButtonText: t("Close"),
            onBeforeOpen: () => {

                // If the device is a legacy device - don't allow configuration changes
                if(LEGACY_DEVICE) {
                    $('.swal2-confirm').prop('disabled', true);
                }

                function drawRelayStates(count) {
                    const container = $(".relay-containers");
                    container.empty(); // Clear existing content

                    for (let i = 1; i <= count; i++) {
                        const relayHTML = `
                            <div class="row vertical-align-row" style="margin-bottom: 5px;">
                                <div class="col-xs-2">
                                    ${t(`#${i}`)}:
                                </div>
                                <div class="col-xs-5">
                                    <select class="select-relay dropdownRelayState-${i}">
                                        <option value="unconfigured">${t('Unconfigured')}</option>
                                        <optgroup label="${t('Duress Buttons')}">
                                            <option value="duress-alarm">${t('Duress Alarm Activated | Deactivated')}</option>
                                            <option value="duress-low-battery">${t('Duress Low Battery')}</option>
                                        </optgroup>
                                        <optgroup label="${t('General State of Club')}">
                                            <option value="mirror-screens">${t('Mirror Coaching Screens Power State')}</option>
                                        </optgroup>
                                    </select>
                                </div>
                                <div class="col-xs-1"></div>
                                <div class="col-xs-3">
                                    <center>
                                        <div class="switch-container" style="transform: translateX(-10px); position: relative;">
                                            <div class="spinner-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                                                <img src="/assets/images/tail-spin.svg" alt="Loading..." style="width: 24px; height: 24px;">
                                            </div>
                                            <div class="switch-lever" style="visibility: hidden;">
                                                ${switchLever(t('Off'), t('On'), `toggleRelay-${i}`, false)}
                                            </div>
                                        </div>
                                    </center>
                                </div>
                            </div>
                        `;
                        container.append(relayHTML); // Append the HTML to the container
                    }

                    // After rendering all switches, bind change events to each checkbox
                    $('.switch-container input[type="checkbox"]').change(function() {

                        const isChecked = $(this).is(':checked');
                        const relayNumber = $(this).attr('name').split('-')[1]; // Extract relay number from name attribute
                        const newState = isChecked ? 'ON' : 'OFF';

                        // Send the device command based on the new state
                        $.sDevices.base.sendDeviceCommand('relayDeviceSetState', { relay: relayNumber, state: newState }, balenaUUID, false, false, true);
                    });
                }


                function showAllSwitchLevers() {
                    $(".switch-container").each(function() {
                        // Show the switch lever
                        $(this).find(".switch-lever").css("visibility", "visible");
                        // Hide the spinner
                        $(this).find(".spinner-container").hide();
                    });
                }

                function updateSwitchStates(data) {
                    Object.keys(data).forEach(relayNumber => {
                        const state = data[relayNumber];
                        const checkbox = $(`input[name='toggleRelay-${relayNumber}']`); // Select the checkbox by name attribute

                        // Update the checkbox state based on the received data
                        if (state === 'ON') {
                            checkbox.prop('checked', true); // Check the checkbox if the state is 'ON'
                        } else {
                            checkbox.prop('checked', false); // Uncheck the checkbox if the state is 'OFF'
                        }
                    });
                }

                document.addEventListener('relayDeviceStates', function (event) {
                    const data = event.detail.relayStatuses; // Extract the relayStatuses data from the event
                    console.log('relayDeviceStates update received:', data);

                    updateSwitchStates(data); // Update switch states with the received data
                    showAllSwitchLevers();                    
                });

                function setRelayStates(relayStates) {
                    // Check if relayStates is undefined, null, or not an object
                    if (!relayStates || typeof relayStates !== 'object') {
                        // If invalid data, set all dropdowns to 'unconfigured'
                        $(".relay-containers .select-relay").val('unconfigured');
                        return;
                    }

                    // Iterate over each key-value pair in the relayStates object
                    for (const [relayNumber, relayValue] of Object.entries(relayStates)) {
                        // Find the select element with the corresponding relay number
                        const selectElement = $(`.dropdownRelayState-${relayNumber}`);
                        // Set the value of the select element
                        selectElement.val(relayValue);

                        // If the provided value is invalid or not in the options, set to 'unconfigured'
                        if (selectElement.val() !== relayValue) {
                            selectElement.val('unconfigured');
                        }
                    }

                    // Ensure all other dropdowns are set to 'unconfigured' if not specified in relayStates
                    $(".relay-containers .select-relay").each(function() {
                        const match = this.className.match(/dropdownRelayState-(\d+)/);
                        if (match) {
                            const relayNumber = match[1];
                            if (!relayStates.hasOwnProperty(relayNumber)) {
                                $(this).val('unconfigured');
                            }
                        }
                    });
                }

                if(currentDevice.deviceType == 'camera') {
                    // JS UI Elements...

                    setLocationTagCamera((currentDevice.cameraGroup === undefined) ? '' : currentDevice.cameraGroup, '.location-group')
                    $(".preferred-resolution").selectpicker();
                    $(".recording-mode").selectpicker();

                    $('.preferred-resolution').selectpicker('val', (currentDevice.resolution == undefined || currentDevice.resolution == '') ? '1280x720' : currentDevice.resolution);
                    $('.recording-mode').selectpicker('val', (currentDevice.recordingMode == undefined || currentDevice.recordingMode == '') ? 'motion' : currentDevice.recordingMode);

                    function setLocationTagCamera(selectedTag, ele) {

                        // Grouping Tags Select Multiple Autocomplete
                        const tags = [t('Door'), t('Inside'), t('Outside')]

                        $(ele).select2({
                            placeholder: t('Select or Enter New Location'),
                            multiple: false,
                            tags: tags,
                            allowClear: true,
                            width: '100%',
                            dropdownCssClass: `device-management__dropdown`
                        })

                        if(selectedTag) {
                            let existingTags = []
                            let addedTags = ''

                            // Check if newly added tag is exists on the current tag
                            // If not, add an option then append the new option
                            if($.inArray(selectedTag, tags) !== -1) {
                                existingTags.push(selectedTag)
                            } else {
                                addedTags += `<option value=${selectedTag} selected>${t(selectedTag)}</option>`
                                existingTags.push(selectedTag)
                            }

                            $(ele).val(existingTags).trigger('change')
                            $(ele).append(addedTags).trigger('change')
                        }
                    }

                    try {                    
                        motionThresholdSlider = document.getElementById('motion_threshold_slider');
                        noUiSlider.create(motionThresholdSlider, {
                            range: {
                                min: 1000,
                                '40%': 4000,
                                '68%': 8000,
                                '95%': 15000,
                                max: 20000
                            },
                            connect: 'lower',
                            start: [(currentDevice.motionThreshold == undefined || currentDevice.motionThreshold == '') ? 4000 : currentDevice.motionThreshold],
                            pips: {
                                mode: 'values',
                                values: [2000, 4000, 8000, 16000],
                                density: 10,
                                format: {
                                    to: function (value) {
                                        switch (value) {
                                            case 2000: return t('Subtle');
                                            case 4000: return t('Default');
                                            case 8000: return t('Energetic');
                                            case 16000: return t('Intense');
                                            default: return value;
                                        }
                                    }
                                }
                            }
                        });

                        const pips = motionThresholdSlider.querySelectorAll('.noUi-value');
                        pips.forEach(function(pip) {
                            pip.setAttribute('data-toggle', 'tooltip');
                            pip.setAttribute('data-placement', 'top');
                            const value = parseInt(pip.getAttribute('data-value'));
                            let tooltipText = '';
                            switch (value) {
                                case 2000: tooltipText = t('This setting is ideal for detecting very light and subtle movements. Suitable for monitoring spaces where minimal motion occurs, such as adjusting objects or small shifts in position.'); break;
                                case 4000: tooltipText = t('A well-balanced setting designed to capture regular movement patterns. Effective for monitoring areas with steady motion, such as people walking between rooms or performing routine activities.'); break;
                                case 8000: tooltipText = t('Optimized for environments with more frequent movement. This threshold detects faster and more frequent activities, such as people actively moving or objects shifting regularly.'); break;
                                case 16000: tooltipText = t('Best suited for environments with continuous, intense movement, such as high-traffic areas or roads. This level focuses on capturing only significant movements to avoid false positives from constant motion.'); break;
                                default: tooltipText = t('Motion Threshold'); break;
                            }
                            pip.setAttribute('title', tooltipText);
                            pip.setAttribute('title', tooltipText);
                        });

                        function clickOnPip() {
                            var value = Number(this.getAttribute('data-value'));
                            motionThresholdSlider.noUiSlider.set(value);
                        }

                        for (let i = 0; i < pips.length; i++) {
                            pips[i].style.cursor = 'pointer';
                            pips[i].addEventListener('click', clickOnPip);
                        }
                    } catch(e) {}

                }

                if(currentDevice.deviceType == 'duress') {

                    setLocationTagDuress((currentDevice.deviceRoom === undefined) ? '' : currentDevice.deviceRoom, '.location-group')

                    function setLocationTagDuress(selectedTag, ele) {

                        // Grouping Tags Select Multiple Autocomplete
                        const tags = [t('Main Room'), t('Bathroom')]

                        $(ele).select2({
                            placeholder: t('Select or Enter New Location'),
                            multiple: false,
                            tags: tags,
                            allowClear: true,
                            width: '100%',
                            dropdownCssClass: `device-management__dropdown`
                        })

                        if(selectedTag) {
                            let existingTags = []
                            let addedTags = ''

                            // Check if newly added tag is exists on the current tag
                            // If not, add an option then append the new option
                            if($.inArray(selectedTag, tags) !== -1) {
                                existingTags.push(selectedTag)
                            } else {
                                addedTags += `<option value=${selectedTag} selected>${t(selectedTag)}</option>`
                                existingTags.push(selectedTag)
                            }

                            $(ele).val(existingTags).trigger('change')
                            $(ele).append(addedTags).trigger('change')
                        }
                    }
                }

                if(currentDevice.deviceType == 'screen') {
                    try {
                        displayBrightnessSlider = document.getElementById('display_brightness_slider');
                        noUiSlider.create(displayBrightnessSlider, {
                            start: [(currentDevice.displayBrightness == undefined || currentDevice.displayBrightness == '') ? 100 : currentDevice.displayBrightness],
                            connect: 'lower',
                            // snap: true,
                            step: 1,
                            pips: {
                                mode: 'count',
                                values: 5,
                                density: 5
                            },
                            range: {
                                'min': [0],
                                'max': [100]
                            }
                        });

                        let brightnessPips = displayBrightnessSlider.querySelectorAll('.noUi-value');

                        function clickOnBrightnessPip() {
                            var value = Number(this.getAttribute('data-value'));
                            displayBrightnessSlider.noUiSlider.set(value);
                        }

                        for (var i = 0; i < brightnessPips.length; i++) {
                            brightnessPips[i].style.cursor = 'pointer';
                            brightnessPips[i].addEventListener('click', clickOnBrightnessPip);
                        }

                        // ------------------------

                        underscanSlider = document.getElementById('underscan_slider');
                        noUiSlider.create(underscanSlider, {
                            start: [(currentDevice.underscan == undefined || currentDevice.underscan == '') ? 0 : currentDevice.underscan],
                            connect: 'lower',
                            // snap: true,
                            step: 1,
                            pips: {
                                mode: 'count',
                                values: 5,
                                density: 5
                            },
                            range: {
                                'min': [0],
                                'max': [100]
                            }
                        });

                        let underscanPips = underscanSlider.querySelectorAll('.noUi-value');

                        function clickOnUnderscanPip() {
                            var value = Number(this.getAttribute('data-value'));
                            underscanSlider.noUiSlider.set(value);
                        }

                        for (var i = 0; i < underscanPips.length; i++) {
                            underscanPips[i].style.cursor = 'pointer';
                            underscanPips[i].addEventListener('click', clickOnUnderscanPip);
                        }                    

                        if(deviceStatus == true && currentDevice.deviceType !== 'duress' && (!!metadata?.systemStats?.relayDeviceConnected)) {
                            
                            // 8 Chanel USB Relay (currently supported)
                            if(metadata?.systemStats?.relayDeviceConnected?.productId == '0c02') drawRelayStates(8)
                            console.log('relayStates', relayStates)
                            setRelayStates(relayStates)

                            $(".select-relay").selectpicker();
                        }

                    } catch(e) {}
                }


                $(".equipment-round").selectpicker();
                $(".display-orientation").selectpicker();
                $(".display-resolution").selectpicker();
                $(".scheduled-reboot").selectpicker();

                $(".screens-advanced-settings").click(function() {
                    $('.t-screens-advanced-settings').toggleClass('toggle-device-advanced', 100);

                    if($('.t-screens-advanced-settings').hasClass('toggle-device-advanced')) {
                        $(".screens-advanced-settings span").html(t("Show Advanced"));
                        $(".screens-advanced-settings i").html("keyboard_arrow_down");
                    } else {
                        $(".screens-advanced-settings span").html(t("Hide Advanced"));
                        $(".screens-advanced-settings i").html("keyboard_arrow_up");
                    }
                })

                $(".p-reboot").click(function() {
                    $.sDevices.base.sendDeviceCommand('rebootDevice', null, balenaUUID);
                    
                    setTimeout(function() {
                        instantNotification(
                            // Message...
                            t('Please allow 1-2 minutes for the device to safely shut down all services and restart.')
                        );
                    }, 1000);
                })
                $(".p-debug-on").click(function() {
                    $.sDevices.base.sendDeviceCommand('gpuDebugOn', null, balenaUUID);
                })
                $(".p-debug-off").click(function() {
                    $.sDevices.base.sendDeviceCommand('gpuDebugOff', null, balenaUUID);
                })
                $(".p-unlock-updates").click(function() {
                    $.sDevices.base.sendDeviceCommand('unlockUpdates', null, balenaUUID);
                })
                $(".p-manage-wifi-networks").click(function() {

                    $.sDevices.base.sendDeviceCommand('detailedWifi', null, balenaUUID, false, false, true);
                    $(this).parent().append(`<img style="height:20px; margin-left: 10px;" src="/assets/images/tail-spin.svg">`);

                    // Modify CSS properties directly and disable the click event to indicate it's now disabled.
                    $(this).css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                        // Add any other styles you want for your disabled state
                    })
                    .off('click');

                    // If the dialog is not visible after 4 seconds - show the change wifi dialog instead...
                    setTimeout(function() {
                        if (!$('.wifi-details-parent').length) {
                            $.sDevices.base.changeWifiNetworks(deviceID)
                        }
                        // Wait a maximum of 15 seconds before assuming the MQTT response form the device is dead
                    }, (1000 * 15));

                })
                $(".p-motion-zone").click(function(e) {

                    $.sDevices.base.sendDeviceCommand('sendSingleFrameToMQTT', null, balenaUUID, false, false, true);
                    $(this).parent().append(`<img style="height:20px; margin-left: 10px;" src="/assets/images/tail-spin.svg">`);

                    $.sDevices.base.deviceCCTVMotionZoneConfig(balenaUUID, false)

                    // Modify CSS properties directly and disable the click event to indicate it's now disabled.
                    $(this).css({
                        'pointer-events': 'none',
                        'opacity': '0.5'
                        // Add any other styles you want for your disabled state
                    })
                    .off('click');

                })
                $(".p-brightness-contrast").click(function(e) {
                    e.preventDefault();
                    swalWithBootstrapButtons.close();
                    brightnessContrastSettings(deviceID);  
                })
                $(".p-display-on").click(function() {
                    $.sDevices.base.sendDeviceCommand('displayOn', null, balenaUUID);
                })
                $(".p-display-off").click(function() {
                    $.sDevices.base.sendDeviceCommand('displayOff', null, balenaUUID);
                })
                $(".p-capture-screenshot").click(function() {
                    $.sDevices.base.currentScreenshot = uuidv4();
                    $.sDevices.base.sendDeviceCommand('captureScreenshot', $.sDevices.base.currentScreenshot, balenaUUID, false, null);
                    $.sDevices.base.previewICSScreenshot(null, balenaUUID);
                })
                $(".p-identify-display").click(function(e) {
                    $.sDevices.base.sendDeviceCommand('osd', equipmentRound, balenaUUID,false,false,true);
                    e.preventDefault();
                })
                $(".p-purge-internal-storage").click(function() {

                    confirmDialog(
                        t("Purge Internal Storage?"),
                        // <div class="badge badge-red">7 Months ago <i class="fa fa-exclamation-triangle" aria-hidden="true"></i></div>
                        `
                        <div class="ph-dialog-v2" style="padding: 20px;">
                            <div class="alert alert-red">
                                <i class="fa fa-exclamation-triangle" style="color: #eb5b74;" aria-hidden="true"></i>
                                <b>${t("This action will permanently delete all recordings from your camera's internal storage")}</b>
                            </div>
                            <p>${t("If you proceed with purging the internal storage, please be aware that any recordings stored on the camera will be irretrievably wiped. This includes recordings that have not yet been uploaded to the cloud or any other storage solution. Once deleted, these recordings cannot be recovered.")}</p>
                            <p><b>${t("Please ensure that all important recordings have been safely uploaded or backed up before proceeding with this action.")}</b></p>
                        </div>
                        `, async () => {
                            deviceLogsDialog(`${t('Device Logs from')}: ${currentDevice?.deviceName} (${currentDevice?.balenaUUID})`);
                            $.sDevices.base.sendDeviceCommand('purgeSD', null, balenaUUID,false,false,true)
                            e.preventDefault();
                        },null,swalWithDangerBootstrapButtons)
                })


                $(".p-load-url").click(function() {

                    swalWithBootstrapButtons.close();

                    setTimeout(function(){

                        let url = prompt(t("Enter the full URL you would like to load - include the http://"), t("https://ubxtraining.com/what"));
                        if (url == null || url == "") {
                            console.log('invalid URL specified...')
                        } else {
                            sendDeviceLoadURL(url, balenaUUID);
                        }

                    }, 50);

                })
                

                $("#recordingEnabled").change(function() {
                    recordingEnabledDisabledUILogic()
                });

                // Also load when the modal first loads...
                recordingEnabledDisabledUILogic()

                function recordingEnabledDisabledUILogic() {
                    if(($('#recordingEnabled').is(':checked')) ? true : false) {
                        $('.recording-mode').prop('disabled', false);
                        $(".motion-threshold").show();
                    } else {
                        $('.recording-mode').prop('disabled', true);
                        $(".motion-threshold").hide();
                    }
                    $('.recording-mode').selectpicker('refresh');
                }

                // Wifi Experience Animation...
                if(metadata?.defaultNetworkSource !== 'ethernet') {
                    signalStrength = $.sDevices.base.getSignalQuality(currentDevice);
                    $.sDevices.base.wifiExperience('.wifi-experience-bar', '.wifi-experience-text', signalStrength)
                }
                
                // if(deviceStatus == true) {
                //     $.sDevices.base.wifiExperience('.wifi-experience-bar', '.wifi-experience-text', signalStrength)
                // } else {
                //     $.sDevices.base.wifiExperience('.wifi-experience-bar', '.wifi-experience-text', 0)
                // }

                var cpuUsed = (metadata === undefined || metadata.systemStats === undefined || metadata.systemStats.cpu.cpuUsedPercent === undefined) ? false : metadata.systemStats.cpu.cpuUsedPercent
                if(cpuUsed) {

                    var cssState;
                    switch (true) {
                        case (cpuUsed < 0.85):
                            cssState = "devices-color-default";  // Black for below 85%
                            break;
                        case (cpuUsed < 0.91):
                            cssState = "devices-color-warning";  // Orange for 86% to 90%
                            break;
                        case (cpuUsed >= 0.91):
                            cssState = "devices-color-bad";  // Red for 91% and higher
                            break;
                        default:
                            cssState = "devices-color-default";  // Default to black if no condition matches
                            break;
                    }

                    $(".cpu-used-percent").html(` &nbsp; - &nbsp; ${ Math.round((cpuUsed + Number.EPSILON) * 100) }% ${t('Consumed')}`)
                    $(".cpu-used-percent").addClass(cssState)
                }

                // Set orientation setting...
                $('.display-orientation').selectpicker('val', orientation);
                $('.display-resolution').selectpicker('val', resolution);
                $('.scheduled-reboot').selectpicker('val', scheduledReboot);

                // Set the default selected value for equipment round...
                // $(`.equipment-round option[value='${currentDevicesObj[deviceID].equipmentRound}']`).prop('selected', true);
                $('.equipment-round').selectpicker('val', equipmentRound);

                $(".equipment-round, .location-group").change(function() {
                    validate()
                });

                $(".display-name").keyup(function() {
                    validate()
                });

                addClipboardCopyButtons('.device-details-dialog');
                validate();

                function validate() {
                    if(!LEGACY_DEVICE) {
                        var isValid = true;

                        if(currentDevice.deviceType == 'camera' || currentDevice.deviceType == 'screen') {
                            if(! $(".equipment-round").selectpicker('val')) isValid = false;
                            if(! $(".display-name").val()) isValid = false;
                        }

                        if(currentDevice.deviceType == 'camera' || currentDevice.deviceType == 'duress') {
                            if(! $('.location-group').select2('data')[0].id) isValid = false;
                        }

                        if(isValid) {
                            $('.swal2-actions .swal2-confirm').prop('disabled', false);
                        } else {
                            $('.swal2-actions .swal2-confirm').prop('disabled', true);
                        }
                    }
                }

                const customButton = document.createElement('button');
                customButton.id = 'compliance-link';
                customButton.innerText = t('Compliance Details');
                customButton.classList.add('swal2-cancel', 'btn', 'btn-link', 'waves-effect');
                
                customButton.addEventListener('click', () => {
                  openComplianceDialog(deviceType, currentDevice);
                }); 
                
                const actions = Swal.getActions();
                const confirmButton = actions.querySelector('.swal2-confirm');

                actions.insertBefore(customButton, confirmButton);

                if (LEGACY_DEVICE && currentDevice.deviceType === 'screen') {
                  // Disable simple inputs
                  $('.display-name').prop('disabled', true);

                  // Disable selectpicker elements
                  const selectPickers = [
                    '.equipment-round',
                    '.display-orientation',
                    '.display-resolution',
                    '.scheduled-reboot'
                  ];

                  selectPickers.forEach(selector => {
                    $(selector).prop('disabled', true).selectpicker('refresh');
                  });

                  // Disable checkboxes + their labels
                  const inputsToDisable = [
                    '#prevent-from-becoming-master-clock',
                    '#disable-audio-on-device',
                    '#prevent-from-becoming-sonos-controller'
                  ];

                  inputsToDisable.forEach(selector => {
                    const $input = $(selector);
                    $input.prop('disabled', true);
                    $(`label[for="${$input.attr('id')}"]`).css({
                      'opacity': '0.5',
                      'pointer-events': 'none',
                      'cursor': 'not-allowed'
                    });
                  });

                  // Disable sliders visually and functionally
                  const slidersToDisable = [
                    '#display_brightness_slider',
                    '#underscan_slider'
                  ];

                  slidersToDisable.forEach(selector => {
                    const $el = $(selector);
                    $el.css({
                      'opacity': '0.5',
                      'pointer-events': 'none',
                      'user-select': 'none'
                    }).find('*').prop('disabled', true).css({
                      'pointer-events': 'none',
                      'user-select': 'none'
                    });
                  });
                }

            },
            onClose: () => {

                function getRelayStates() {
                    const relayStates = {};
                    const container = $(".relay-containers");

                    // Iterate through each select element in the container
                    container.find(".select-relay").each(function() {

                        // Extract the number from the class, e.g., dropdownRelayState-1
                        const match = this.className.match(/dropdownRelayState-(\d+)/);
                        if (match) {
                            const relayNumber = match[1]; // The relay number
                            relayStates[relayNumber] = $(this).val(); // Get the selected value
                        }
                    });
                    return relayStates;
                }

                switch(currentDevice.deviceType) {

                    case 'screen':
                        deviceUpdateObj = {
                            deviceType: currentDevice.deviceType,
                            deviceID: deviceID,
                            equipmentRound: $(".equipment-round").selectpicker('val'),
                            displayName: $(".display-name").val(),
                            orientation: $(".display-orientation").selectpicker('val'),
                            resolution: $(".display-resolution").selectpicker('val'),
                            scheduledReboot: $('.scheduled-reboot').selectpicker('val'),
                            preventMasterClock: $('#prevent-from-becoming-master-clock').is(':checked'),
                            preventSonosController: $('#prevent-from-becoming-sonos-controller').is(':checked'),
                            disableMasterClockAudio: $('#disable-audio-on-device').is(':checked')
                        }
                        try {
                            deviceUpdateObj.displayBrightness = Math.trunc(displayBrightnessSlider.noUiSlider.get());
                        } catch(e) {}
                        try {
                            deviceUpdateObj.underscan = Math.trunc(underscanSlider.noUiSlider.get());
                        } catch(e) {}
                        try {
                            if (!!metadata?.systemStats?.relayDeviceConnected) deviceUpdateObj.relayStates = getRelayStates()
                        } catch(e) {}

                    break;

                    case 'camera':
                        deviceUpdateObj = {
                            balenaUUID: deviceID,
                            clubID: selectedID,
                            cameraGroup: $('.location-group').val(),
                            deviceName: $(".display-name").val(),
                            resolution: $('.preferred-resolution').selectpicker('val'),
                            recordingMode: $('.recording-mode').selectpicker('val'),
                            recordingEnabled: ($('#recordingEnabled').is(':checked')) ? true : false,
                        }
                        try {
                            if(deviceUpdateObj.recordingMode == 'motion') deviceUpdateObj.motionThreshold = Math.trunc(motionThresholdSlider.noUiSlider.get());
                        } catch(e) {}
                    break;

                    case 'duress':
                        deviceUpdateObj = {
                            deviceSerialNumber: deviceID,
                            deviceRoom: $('.location-group').val()
                        }
                        try {
                            if(deviceUpdateObj.recordingMode == 'motion') deviceUpdateObj.motionThreshold = Math.trunc(motionThresholdSlider.noUiSlider.get());
                        } catch(e) {}
                    break;

                }


            },
            customClass: {
              container: 'device-details-dialog__container',
              confirmButton: 'btn btn-primary waves-effect',
              cancelButton: 'btn btn-default waves-effect'
            },
        }).then(response => {
            if (response.value) {
                $.sDevices.base.updateDeviceAPIReq(deviceUpdateObj, currentDevice.deviceType);
            }
        });

        async function brightnessContrastSettings(deviceID) {

        let currentDevice = $.sDevices.base.deviceData.items
          .filter(item => item.balenaUUID === deviceID)

            swalWithBootstrapButtons.fire({
                //title: 'Design Setup',
                html: `
                <div class="brightness-contrast-parent ph-dialog-v2" style="padding: 20px;">
                    <div class="brightness-contrast-dialog">
                        <fieldset>
                            
                            <div class="row loading-message" style="height: 400px; display: flex; justify-content: center; align-items: center;">
                                <h4>
                                    <center>
                                    ${t('Getting settings from camera')}...
                                    <br><br>
                                    <div class="preloader pl-size-xl" style="padding: 10px;">
                                      <img src="/assets/images/tail-spin.svg">
                                    </div>
                                    </center>
                                </h4>
                            </div>

                            <div class="camera-settings" style="display: none;">
                                <div class="row">
                                    <div class="col-sm-5">
                                        <div class="controls" style="overflow-y: scroll; overflow-x: hidden; height: 400px; padding-right: 10px; padding-top: 10px; padding-bottom: 10px;"></div>
                                    </div>
                                    <div class="col-sm-7 video-container">
                                        <div class="iframe-loading-message" style="background-color: #333333; width: 100%; height: 400px; display: flex; justify-content: center; align-items: center; color: white; font-size: 20px;">
                                            <h4>
                                                <center>
                                                    ${t('Connecting to Camera')}...
                                                    <br><br>
                                                    <div class="preloader pl-size-xl" style="padding: 10px;">
                                                        <img src="/assets/images/tail-spin.svg" style="filter: invert(1);">
                                                    </div>
                                                </center>
                                            </h4>
                                        </div>
                                        <div class="iframe-contents" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>

                        </fieldset>
                    </div>
                    <div class="actions" style="display: flex; justify-content: center; width: 100%; margin: 1.25em auto 0; align-items: center; flex-wrap: wrap;">
                        <button type="button" class="b-save swal2-confirm btn btn-primary waves-effect" aria-label="" style="margin: 5px; display: inline-block;">
                            ${t('Save Changes')} (${t('Device will restart')})
                        </button>
                        <button type="button" class="b-load-defaults btn btn-default waves-effect" aria-label="" style="margin: 5px; display: inline-block;">
                            ${t('Load/Reset Defaults')}
                        </button>
                        <button type="button" class="b-cancel btn btn-default waves-effect" aria-label="" style="margin: 5px; display: inline-block;">
                            ${t('Cancel')}
                        </button>
                    </div>
                </div>

                `,
                title: `${t('Image Settings')} - ${currentDevice[0].deviceName} (${currentDevice[0].cameraGroup})`,
                width: 1100,
                showCloseButton: true,
                showConfirmButton: false,
                showCancelButton: false,
                onBeforeOpen: async () => {

                    $(".b-cancel").click(function() {
                        swalWithBootstrapButtons.close()
                    });

                    $.ajax({
                        url: `/api/cctv/livestream/${selectedID}/iswatching/${deviceID}`,
                        type: 'POST',
                        contentType: 'application/json',
                        beforeSend: function() {},
                        success: function(res) {
                            if(res.data !== undefined && res.data.iFrameURL) {
                                console.log(res.data.iFrameURL)
                                $(".video-container .iframe-contents").html(`<iframe data-hj-allow-iframe="" frameborder="0" style="width: 100%; height: 400px; object-fit: contain;" src="${res.data.iFrameURL}"></iframe>`)
                                $(".video-container .iframe-contents iframe").on("load", function () {
                                    $(".iframe-loading-message").hide();
                                    $(".video-container .iframe-contents").show();
                                });

                            }
                        },
                        error: function(err) {
                            // NO CAMERA DATA?
                        }
                    });

                    function setCameraCapability(setting, value) {
                        $.ajax({
                            url: `/api/cctv/devices/set-camera-capability/${selectedID}`,
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                "uuid": deviceID,
                                "data": {                                
                                    "setting": setting,
                                    "value": value
                                }
                            }),
                            success: function(res) {
                                console.log(res)
                            },
                            error: function(err) {
                                // NO CAMERA DATA?
                            }
                        });
                    }

                    // Fire off request to get device options...        
                    async function loadCameraCaps() {
                        return new Promise(function(resolve, reject) {
                            $.ajax({
                                url: `/api/cctv/devices/list-camera-capabilities/${selectedID}`,
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    "uuid": deviceID
                                }),
                                beforeSend: function() {},
                                success: function(data) {
                                    resolve(data)
                                },
                                error: function(err) {
                                    reject(err)
                                }
                            });
                        });
                    }

                    try {
                        let options = await loadCameraCaps();
                        createControls(options)
                        $(".loading-message").hide();
                        $(".camera-settings").show();
                    } catch(e) {
                        $(".loading-message h3").html(`Could not connect to camera at this time.`);
                        console.log(e);
                    }

                    function createControls(controls) {
                        controls.forEach(ctl => {
                            console.log('ctl', ctl)

                            switch (ctl.type.toLowerCase()) {
                                case ("int"):
                                    
                                    $(".controls").append(`
                                        <div class="row">
                                            <div class="col-xs-12 col-sm-5 text-right">
                                                ${ctl.name}:
                                            </div>
                                            <div class="col-xs-12 col-sm-7">
                                                <div class="slider-wrapper" style="margin-right: 10px; height: 60px;">
                                                    <div class="slider" id="slider_${ctl.id}"></div>
                                                </div>
                                            </div>
                                        </div>
                                    `)

                                    //try {                 
                                        // let slider = $()
                                        slider = document.getElementById(`slider_${ctl.id}`);

                                        noUiSlider.create(slider, {
                                            start: [ctl.value],
                                            connect: 'lower',
                                            // snap: true,
                                            step: ctl.step,
                                            // pips: {mode: 'count', values: 5},
                                            pips: {
                                                mode: 'count',
                                                values: 3,
                                                density: 10
                                            },
                                            range: {
                                                'min': [ctl.min],
                                                'max': [ctl.max]
                                            }
                                        });

                                        slider.noUiSlider.on('change', function () {
                                            setCameraCapability(ctl.id, Math.trunc(this.get()))
                                        });

                                   // } catch(e) {}

                                    break;
                                case ("menu"):
                                    // xx
                                    break;
                                case ("bool"):
                                    // xx
                                    break;
                            }
                        });

                        $(".b-load-defaults").click(function() {
                            controls.forEach(ctl => {

                                // Reset the physical camera...
                                setCameraCapability(ctl.id, ctl.default)

                                // Reset the UI
                                switch (ctl.type.toLowerCase()) {
                                    case ("int"):
                                        slider = document.getElementById(`slider_${ctl.id}`);
                                        slider.noUiSlider.set(ctl.default);
                                    break;
                                }

                            });
                        });

                        $(".b-save").click(function() {

                            $(".brightness-contrast-parent *").attr("disabled", true);
                            loadingOverlay('.brightness-contrast-parent');

                            let customCameraSettings = {}
                            controls.forEach(ctl => {
                                switch (ctl.type.toLowerCase()) {
                                    case ("int"):
                                        slider = document.getElementById(`slider_${ctl.id}`);
                                        customCameraSettings[ctl.id] = Math.trunc(slider.noUiSlider.get())
                                }
                            });

                            // console.log('customCameraSettings', customCameraSettings)
                            $.ajax({
                                url: `/api/cctv/devices/save-camera-capabilities/${selectedID}`,
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    "uuid": deviceID,
                                    "capabilities": customCameraSettings
                                }),
                                success: function(res) {
                                    swalWithBootstrapButtons.close()
                                    instantNotification(
                                        // Message...
                                        t('Configuration Saved - The device may automatically restart for your changes to take effect.')
                                    );
                                },
                                error: function(err) {
                                    alert(`Unable to save device settings at this time. Please try again later.\n\nError:\n${err}`)
                                    $(".brightness-contrast-parent *").attr("disabled", false);
                                    $('.brightness-contrast-parent').LoadingOverlay("hide");
                                    // NO CAMERA DATA?
                                }
                            });
                            
                        });

                    }

                },
                onClose: () => {

                }
            }).then(response => {
                if (response.value) {
                    // 
                }
            });

        }

        function sendDeviceLoadURL(url, deviceUUID=false) {

            let postData = {
                uuid: deviceUUID,
                url: url
            }

            let req = $.ajax({
                url: `/api/ics/devices/loadurl/${selectedID}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(postData),
                success: function(data){
                    if((data === undefined) ? false : data !== false) {
                        if(successDialog != null) {
                            instantNotification(
                                t('Command Sent')
                            );
                        }
                    }
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                    instantNotification(
                        `<b>${t('Error sending command to one or more devices')}</b>`,
                        'error',
                        30000
                    );
                }
            });
        }
    },

    // this function will render the filter options dropdown
    renderOptions: function(filter, optionWrapper, deviceOptions = $.sDevices.base.deviceOptions, deviceFilters = $.sDevices.base.deviceFilters) {

        if(!filter || !optionWrapper) return;

        try {
          const options = deviceOptions[filter];
          const selected = deviceFilters[filter];

          optionWrapper.html('');
          
          options.forEach(option => {
              const matched = selected.value === option.value
              optionWrapper.append(`
                  <li class="filter__option ${matched ? 'filter__option--active' : ''}" data-value="${option.value}">
                      ${t(option.text)}
                  </li>
              `)

              if(matched) {
                  optionWrapper.parent().find('.filter__selected').html(t(option.text))
              }
          })
        } catch(e) {
          console.log(e)
        }
    },

    filterUpdate: function(
        target = null, 
        callback = null, 
        deviceOptions = $.sDevices.base.deviceOptions, 
        deviceFilters = $.sDevices.base.deviceFilters
    ) {
        if (target && target.hasClass('filter__selected')) {
            target.parent().find('.filter__options').addClass('filter__options--active');
            return;
        }

        if(target && target.hasClass('filter__option')) {
            const value = target.data('value');
            
            // add active class
            $('.filter__option').removeClass('filter__option--active');
            target.addClass('filter__option--active');

            // get the wrapper and the data filter
            const optionWrapper = target.parent();
            const activeFilter = optionWrapper.data('filter');
            const selectedTextEl = target.parents('.filter').find('.filter__selected');

            // replace the device filter value
            const activeOption = deviceOptions[activeFilter] ? deviceOptions[activeFilter].find(option => option.value == value) : {};

            console.log({
                activeOptions: deviceOptions[activeFilter],
                value,
                activeOption
            })
            
            deviceFilters[activeFilter] = activeOption;
            selectedTextEl.text(activeOption.text);

            localStorage.setItem("deviceFilters", JSON.stringify(deviceFilters));
            
            if(callback) callback();
            // $.sDevices.base.renderDevices($.sDevices.base.deviceData)
        }
    },

    selectedFilter: function (event) {
            
        const target = $(event.target)

        $.sDevices.base.filterUpdate(target, function() {
            $.sDevices.base.renderDevices($.sDevices.base.deviceData)
        });
    },

    // add sample items to the skeleton loader
    generateSkeletonLoader: function() {
      skeletonLoader = $('#skeleton-loader');
      const tbody = skeletonLoader.find('tbody');
      const tr = tbody.find('tr');
      const trCount = 5;

      tbody.empty();
      
      for(let i = 0; i < trCount; i++) {
        tbody.append(tr.clone());
      }
    },

    init: function() {
        // ------------------------------------------------
        
        // get the current search query if there's deviceType to be used as the current filter value
        const { deviceType: deviceTypeVal, activeTab } = convertSearchToObject(location.search)
        
        if(deviceTypeVal && deviceTypeVal === 'duress') {
            this.deviceFilters.type = this.deviceOptions.type.find(option => option.value === 'duress')
            this.deviceFilters.range = this.deviceOptions.range.find(option => option.value === 'all')
        } else {
            this.deviceFilters.type = {
              text: 'All',
              value: 'all',
            }
            this.deviceFilters.range = {   
              text: 'This Week',
              value: 'this week'
            }
        }



        $('#reset-filter').addClass('hidden');
        const savedDeviceFilters = localStorage.getItem("deviceFilters");
        const jsonDeviceFilters = savedDeviceFilters ? JSON.parse(savedDeviceFilters) : null;
        
        // update initial deviceFilters when jsonDeviceFilters is not null
        if(jsonDeviceFilters && jsonDeviceFilters.type) {
            this.deviceFilters = jsonDeviceFilters;
            
            // show reset button when there's a saved filter
            if(filtersNotDefault(jsonDeviceFilters, )) {
              $('#reset-filter').removeClass('hidden');
            }
        }

        if(activeTab) {
          this.activeTab = activeTab;
          localStorage.activeTab = activeTab;
        }

        // set active tab
        //if(activeTab === 'health-check') {
        //  $('.devices-config-tab[href="#health-check"]').tab('show');
        //}
  
        // update localStorage activeTab value when tab is changed
        $('.devices-config-tab').off('shown.bs.tab').on('shown.bs.tab', function (e) {
          const activeTab = $(e.target).attr('href').replace('#', '');
          localStorage.activeTab = activeTab;
        })

        // let QRScannerBaseConfig = { fps: 10, qrbox: { width: 250, height: 250 }, rememberLastUsedCamera: true, facingMode: {exact: "environment"}, focusMode: "continuous" };
        let currentDevicesObj = {};

        let QRScannerBaseConfig = {
          fps: 10,
          disableFlip: false,
          focusMode: "continuous",
          advanced: [{ zoom: 4.0 }],
          rememberLastUsedCamera: true,
          experimentalFeatures: {
            useBarCodeDetectorIfSupported: true,
          },
          showTorchButtonIfSupported: true,
          willReadFrequently: true,
          supportedScanTypes: [
            // Html5QrcodeScanType.SCAN_TYPE_FILE,
            Html5QrcodeScanType.SCAN_TYPE_CAMERA,
          ],
          qrbox : { width: 250, height: 250 }
        }


        // On init...
        $(document).off("change", "#hide-offline-devices").on("change", "#hide-offline-devices", function () {
            $.sDevices.base.fetchDeviceStats()
        });

        // Initial load of configuration...
        if($.sDevices.base.isInitialised != true) $.sDevices.base.getchBuildVersions();
        $.sDevices.base.fetchDeviceStats();
        
        refreshDevices();

        $(document).off("click", ".devices-config-tab").on("click", ".devices-config-tab", function() {
            $.sDevices.base.fetchDeviceStats()
        });

        // filter updates
        $(window).off('click', $.sDevices.base.selectedFilter, $.sDashboard.base.selectedFilter);
        $(window).on('click', $.sDevices.base.selectedFilter);

        renderGlobalConfig();
        async function renderGlobalConfig() {

        }

        // ------------------------------------------------

        // Help/Support
        $(document).off("click", ".nav-help-container .btn").on("click", ".nav-help-container .btn", function() {
            firstLoad = true; sammy.quiet = false;
            sammy.setLocation("/wiki/#%2Fopenkb%2Fkb%2Fcoaching-screen-install-config-guide");
            firstLoad = false;
        })

        // If we've been redirected to pair a new device from a QR code (displayed on the TV when setup)...
        let pairingCode = (getUrlVars().pair === undefined) ? false : getUrlVars().pair;
        let deviceType = (getUrlVars().device === undefined) ? false : getUrlVars().device;

        if(pairingCode == 'new' && deviceType == 'screen') {
            $("#devices").click();
            addNewHardware(false, deviceType);
        } else if(pairingCode && deviceType == 'screen') {
            $("#devices").click();
            addNewHardware(pairingCode, deviceType);
        } else if(pairingCode == 'new' && deviceType == 'camera') {
            addNewCCTV();
        } else if(pairingCode && deviceType == 'camera') {
            addNewCCTV(pairingCode);
        } else if(pairingCode == 'new' && deviceType == 'duress') {
            addNewPanicButton();
        } else if(pairingCode && deviceType == 'duress') {
            addNewPanicButton(pairingCode);
        }


        // Initialize search header with device action buttons and export buttons
        const deviceActionButtons = [
            { label: t('Add New Device'), icon: 'fa-plus', onClick: addNewDevice },
            { label: t('Unlock & Install Updates'), icon: 'fa-download', onClick: unlockUpdates },
            { label: t('Identify Devices'), icon: 'fa-search', onClick: identifyDevices },
            { label: t('Reboot All Devices'), icon: 'fa-refresh', onClick: rebootAllDevices },
            { label: t('Open Router Webpage'), icon: 'fa-network-wired', onClick: openRouterWebpage },
        ];
        
        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => devicesTable && devicesTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => devicesTable && devicesTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => devicesTable && devicesTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => devicesTable && devicesTable.button(3).trigger() },
        ];
        
        const allButtons = [...deviceActionButtons, ...exportButtons];
        
        searchbarHeader(
            $('.search-bar-container'),
            allButtons,
            {
                usePhTheme: true,
                placeholder: t('Filter Devices'),
                expandableButtons: {
                    startIndex: deviceActionButtons.length,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'device-export-buttons',
                },
            }
        );
        
        // Set the initial search bar value from localStorage
        let deviceSearch = localStorage.getItem('deviceSearch') || '';
        if (deviceSearch) {
            $('.search-bar').val(deviceSearch);
        }
        
        // Set up search event handler
        $(document).off('keyup search input', '.search-bar').on('keyup search input', '.search-bar', function () {
            const value = this.value;
            devicesTable?.search(value)?.draw();
            localStorage.setItem('deviceSearch', value);
        });

        $.sDevices.base.isInitialised = true;

        // ------------------------------------------------

        function refreshDevices() {

            function refresh() {
                // Only refresh the devices/stats from the API if the TAB is left open and visible...
                if($("#devices").hasClass("active")) {
                    $.sDevices.base.fetchDeviceStats()
                }
            }

            if(!$.sDevices.base.refreshDevicesLoaded) setInterval(refresh, 1000 * 60);
            $.sDevices.base.refreshDevicesLoaded = true;
        }

        // Device action button functions for the search header
        function addNewDevice() {
            addNewHardware();
        }
        
        function unlockUpdates() {
            let dialog = {
                title: t('Updates Unlocked'),
                html: `
                    <div class="ph-dialog-v2" style="padding: 20px;">
                        ${t('Device updates have been allowed on all your online devices. Any available updates will be installed shortly')}
                    </div>
                `
            };
            $.sDevices.base.sendDeviceCommand('unlockUpdates', null, false, true, dialog);
        }
        
        function identifyDevices() {
            const filterDevicesByOnlineScreensOnly = (devices) => {
                const filteredDevices = [];
                for (const item of devices.items) {
                    if (item.deviceType === 'screen' && item.metadata.isOnline) {
                        const filteredDevice = {
                            deviceType: item.deviceType,
                            balenaUUID: item.balenaUUID,
                            equipmentRound: item.equipmentRound,
                        };
                        filteredDevices.push(filteredDevice);
                    }
                }
                return filteredDevices;
            };
            
            // Filter through all the online devices - and send a OSD identifying the device
            filterDevicesByOnlineScreensOnly($.sDevices.base.deviceData).forEach((dev) => {
                $.sDevices.base.sendDeviceCommand('osd', dev.equipmentRound, dev.balenaUUID, false, false, true);
            });
            
            // Notify that command sent
            instantNotification(t('Command Sent'), 'success', 5000);
        }
        
        function rebootAllDevices() {
            confirmDeleteDialog(
                t("Reboot Devices"),
                `
                <div class="ph-dialog-v2" style="">
                    <p>
                        ${t("Rebooting all devices will mean that perphial devices such as Sonos, Duress Buttons, etc will be offline while your devices reboot.")}
                    </p>
                    <p style="color: red;">
                        ${t("This action should only be performed while your club is staffed should any troubleshooting steps be required post rebooting your devices.")}
                    </p>
                    <p>
                        ${t("Proceed?")}
                    </p>
                </div>
                `,
                async () => {
                    console.log("yes reboot");
                    $.sDevices.base.sendDeviceCommand('rebootDevice', null, false, true, false);
                },
                t('Yes, Reboot')
            );
        }
        
        async function openRouterWebpage() {
            $.sDevices.base.lastRequestedRouterTunnel = Math.floor(Date.now() / 1000);
            
            toastSpinnerModal(t('Connecting to router...'), { timer: 15000 });
            
            try {
                const response = await $.ajax({
                    url: `/api/devices/openRouterWebpage/${selectedID}`,
                    method: 'GET',
                });
                
                if (response && response.data && response.data.routerTunnelDetails && response.data.routerTunnelDetails.success) {
                    const urls = response.data.routerTunnelDetails.urls;
                    
                    if (Array.isArray(urls) && urls.length > 0) {
                        window.open(urls[0], "_blank");
                    } else {
                        throw new Error("No URLs returned from the tunnel response.");
                    }
                } else {
                    throw new Error("Invalid response structure or tunnel connection failed.");
                }
            } catch (error) {
                console.error("Error establishing router tunnel:", error);
                genericDialog(
                    t('Error'),
                    t('We could not establish a tunnel to the remote router at this time.')
                );
            }
        }

        function addNewCCTV(pairingCode=false) {

            let modifiedPairingCode = '';

            try {
              if (pairingCode && typeof pairingCode === "string") {
                modifiedPairingCode = (pairingCode.split('/cctv/').pop().split('/').pop() ?? '').trim();
              } else {
                modifiedPairingCode = '';
              }
            } catch (e) {
              modifiedPairingCode = pairingCode ?? ''
            }

            let addCCTVHTML = `
                <div class="add-new-device-dialog ph-dialog-v2" style="padding: 20px;">
                    <fieldset>

                        <div class="alert alert-blue" style="margin-top: 10px;">
                            <i class="fa-solid fa-circle-info"></i>
                            <b>${t('Connect CCTV Device to Network First!')}</b><br>
                            <p style="font-weight: 300; margin-top: 5px;">
                                ${t('Please ensure your CCTV Device is powered on and connected to your network before adding into the Performance Hub.')}
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-sm-5">
                                ${t('Serial Number')}:
                            </div>
                            <div class="col-sm-7">
                                <input data-hj-allow class="device-serial" type="text" value="${ modifiedPairingCode ?? '' }" placeholder="">
                                <small><a class="scan-with-qr"><i class="fa-solid fa-qrcode"></i> ${t('Scan QR Code With Camera')}</a></small>
                            </div>
                        </div>
                        <div class="row">
                            <div id="cctv-offline-error" class="col-sm-12 mb-2" style="display: none;">
                            <div class="text-center badge-red p-4 rounded">
                                <i class="fa-solid fa-triangle-exclamation"></i>
                                <span>
                                    ${t('The device you are trying to add is not currently online. Please connect the device to your network, then retry adding the device.')}
                                </span>
                            </div>
                            </div>
                        </div>
                        <div id="qr-reader" style="width: 100%; display: none;"></div>

                        <div class="row">
                            <div class="col-sm-5">
                                ${t('Camera Name')}:
                            </div>
                            <div class="col-sm-7">
                                <input data-hj-allow class="display-name" type="text" value="" placeholder="ie: ${t('Front Door')}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-5">
                                ${t('Location')}:
                            </div>
                            <div class="col-sm-7">
                                <div class="location-group-container">
                                    <select data-hj-allow class="location-group"><option></option></select>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    <div class="alert light-info">
                        ${t('Please refer to the Installation & Configuration Guide for device setup instructions if you have not yet powered on and connected your device to your network.')}
                        <br><a class="btn btn-default waves-effect btn-how-to-guide" role="button">${t('View Installation Guide for EA Devices in Knowledge Base')}</a>
                    </div>

                </div>
            `;

            let newDeviceObj;
            swalWithBootstrapButtons.fire({
                title: t('Add CCTV Camera'),
                html: addCCTVHTML,
                width: 500,
                showCloseButton: false,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonText: t("Add"),
                cancelButtonText: t("Cancel"),
                onBeforeOpen: () => {

                    // JS UI Elements...
                    $('.location-group').select2({
                        placeholder: t('Select or Enter New Location'),
                        multiple: false,
                        tags: [t('Door'), t('Inside'), t('Outside')],
                        allowClear: true,
                        width: '100%',
                        dropdownCssClass: `device-management__dropdown`
                    })

                    $(".btn-how-to-guide").click(function() {
                        swalWithBootstrapButtons.close();
                        firstLoad = true; sammy.quiet = false;
                        // CCTV Setup Guide
                        sammy.setLocation("/wiki/#%2Fopenkb%2Fkb%2F6510e7d977c016433b114b0d");
                        firstLoad = false;
                    })

                    $(".scan-with-qr").click(function() {

                        const qrScanner = new Html5QrcodeScanner("qr-reader", QRScannerBaseConfig);                        
                        qrScanner.render(onScanSuccess);
                        $(".scan-with-qr").hide();
                        $("#qr-reader").show();

                        function onScanSuccess(decodedText, decodedResult) {

                            // console.log(`Code scanned = ${decodedText}`, decodedResult);
                            let strippedDecodeResponse;
                            try {
                              strippedDecodeResponse = (decodedText?.split('/cctv/')?.pop()?.split('/')?.pop() ?? '').trim();
                            } catch (e) {
                                strippedDecodeResponse = decodedText
                            }

                            console.log('strippedDecodeResponse', strippedDecodeResponse)
                            $(".device-serial").val(strippedDecodeResponse)
                            qrScanner.clear();

                            $(".scan-with-qr").show();
                            $("#qr-reader").hide();
                        }

                    });

                    // Check device that matches serial number if it's online or not
                    let isOnline = false;
                    $(".device-serial").blur(function() {
                        if (!$('.device-serial').val()) return;

                        $.sDevices.base.getMetadataBySerialNumber($('.device-serial').val(), (err, metadata) => {
                            if (err) {
                                $("#cctv-offline-error").show();
                                isOnline = false;
                            }

                            const lastUpdated = metadata && metadata.updated ? metadata.updated : false;
                            const hasBeenOnline = lastUpdated && moment(lastUpdated).isAfter(moment().subtract(5, 'minutes'));
                            if (!hasBeenOnline) {
                                $("#cctv-offline-error").show();
                                isOnline = false;
                            } else {
                                $("#cctv-offline-error").hide();
                                isOnline = true;
                            }
                        })
                    });

                    $('.display-name').on('input', function() {
                        validate()
                    });

                    $('.display-name, .device-serial').change(function() {
                        validate()
                    });

                    $('.location-group').on('select2:select', function() {
                        validate()
                    });

                    $('.location-group').on('select2:unselect', function() {
                        validate()
                    });

                    // The validation seems to break when we "prefill" the QR pairing code...
                    if(modifiedPairingCode) {
                        setTimeout(function(){
                            $('.device-serial').trigger('change');
                            $('.device-serial').trigger('blur');
                            $('.display-name').focus();
                        }, 100);
                    }

                    function validate() {
                        let isValid = true;
                        if (!isOnline) isValid = false;
                        if (!$('.device-serial').val()) isValid = false;
                        if (!$('.display-name').val()) isValid = false;
                        if (!$('.location-group').select2('data')[0].id) isValid = false;

                        if (isValid) {
                            $('.swal2-actions .swal2-confirm').prop('disabled', false);
                        } else {
                            $('.swal2-actions .swal2-confirm').prop('disabled', true);
                        }
                    }

                    // Start validation when loading this dialog for the first time...
                    validate();
                },
                onClose: () => {
                    newDeviceObj = {
                        deviceSerialNumber: $('.device-serial').val(),
                        clubID: selectedID,
                        cameraGroup: $('.location-group').select2('data')[0].id,
                        deviceName: $('.display-name').val(),
                    }
                },
            }).then(response => {
                if (response.value) {
                    associateDevice(newDeviceObj, 'camera');
                }
            });

        }
        $.sDevices.base.addDevice = addNewCCTV

        function addNewPanicButton(pairingCode=false) {

            let modifiedPairingCode = '';

            try {
              if (pairingCode && typeof pairingCode === "string") {
                modifiedPairingCode = (pairingCode.split('/duress/').pop().split('/').pop() ?? '').trim();
              } else {
                modifiedPairingCode = '';
              }
            } catch (e) {
              modifiedPairingCode = pairingCode ?? ''
            }

            let panicButtonHTMLDialog = `
                <div class="add-new-device-dialog ph-dialog-v2" style="padding: 20px;">
                    <fieldset>
                        <div class="row">
                            <div class="col-sm-5">
                                ${t('Serial Number')}:
                            </div>
                            <div class="col-sm-7">
                                <input data-hj-allow class="device-serial" type="text" value="${ modifiedPairingCode ?? '' }" placeholder="">
                                <small><a class="scan-with-qr"><i class="fa-solid fa-qrcode"></i> ${t('Scan QR Code With Camera')}</a></small>
                            </div>
                        </div>
                        <div id="qr-reader" style="width: 100%; display: none;"></div>

                        <div class="row">
                            <div class="col-sm-5">
                                ${t('Location')}:
                            </div>
                            <div class="col-sm-7">
                                <div class="location-group-container">
                                    <select data-hj-allow class="location-group"><option></option></select>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    <div class="alert light-info">
                        ${t('Please refer to the Installation & Configuration Guide for device setup instructions if you have not yet powered on and connected your device to your network.')}
                        <br><a class="btn btn-default waves-effect btn-how-to-guide" role="button">${t('View Installation Guide for EA Devices in Knowledge Base')}</a>
                    </div>

                </div>
            `;


            function getValidStrippedMacAddress(macAddress) {
              // Remove colons from the given MAC address
              const strippedMacAddress = macAddress.replace(/:/g, '');

              // Check if the stripped MAC address has the correct length (12 characters) and is a valid hexadecimal value
              const isValid = /^([0-9A-Fa-f]{2}){6}$/.test(strippedMacAddress);

              // Return the stripped MAC address if it's valid, or false otherwise
              return isValid ? strippedMacAddress : false;
            }

            let newDeviceObj;
            swalWithBootstrapButtons.fire({
                title: t('Add Duress Button'),
                html: panicButtonHTMLDialog,
                width: 500,
                showCloseButton: false,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonText: t("Add"),
                cancelButtonText: t("Cancel"),
                onBeforeOpen: () => {

                    // JS UI Elements...
                    $('.location-group').select2({
                        placeholder: t('Select or Enter New Location'),
                        multiple: false,
                        tags: [t('Main Room'), t('Bathroom')],
                        allowClear: true,
                        width: '100%',
                        dropdownCssClass: `device-management__dropdown`
                    })

                    $(".btn-how-to-guide").click(function() {
                        swalWithBootstrapButtons.close();
                        firstLoad = true; sammy.quiet = false;
                        // CCTV Setup Guide
                        sammy.setLocation("/wiki/#%2Fopenkb%2Fkb%2F63cf40a68f2bf7d9b48cc871");
                        firstLoad = false;
                    })

                    $(".scan-with-qr").click(function() {

                        const qrScanner = new Html5QrcodeScanner("qr-reader", QRScannerBaseConfig);                        
                        qrScanner.render(onScanSuccess);
                        $(".scan-with-qr").hide();
                        $("#qr-reader").show();

                        function onScanSuccess(decodedText, decodedResult) {

                            // console.log(`Code scanned = ${decodedText}`, decodedResult);
                            let strippedDecodeResponse;
                            try {
                              strippedDecodeResponse = (decodedText?.split('/cctv/')?.pop()?.split('/')?.pop() ?? '').trim();
                            } catch (e) {
                                strippedDecodeResponse = decodedText
                            }

                            $(".device-serial").val(strippedDecodeResponse)
                            qrScanner.clear();

                            $(".scan-with-qr").show();
                            $("#qr-reader").hide();
                        }

                    });

                    $(".location-group, .device-serial").change(function() {
                        validate()
                    });

                    function validate() {
                        var isValid = true;

                        if($('.device-serial').val()) {
                            if(! getValidStrippedMacAddress($('.device-serial').val())) isValid = false;
                        } else {
                            isValid = false;
                        }
                        if(! $('.location-group').select2('data')[0].id) isValid = false;

                        if(isValid) {
                            $('.swal2-actions .swal2-confirm').prop('disabled', false);
                        } else {
                            $('.swal2-actions .swal2-confirm').prop('disabled', true);
                        }
                    }

                    // Start validation when loading this dialog for the first time...
                    validate();
                },
                onClose: () => {
                    newDeviceObj = {
                        // Make sure we use the 'stripped' mac address - remove any colons etc before storing in DB
                        deviceSerialNumber: getValidStrippedMacAddress($('.device-serial').val()),
                        deviceType: 'duress',
                        clubID: selectedID,
                        deviceRoom: $('.location-group').select2('data')[0].id,
                    }
                },
            }).then(response => {
                if (response.value) {
                    associateDevice(newDeviceObj, 'duress');
                }
            });

        }
        $.sDevices.base.addDevice = addNewPanicButton

        function addNewHardware(pairingCode=false, deviceType=false) {

            switch(deviceType) {
                case 'screen':
                    addNewScreen();
                    break;
                default:
                    selectDeviceType();
                    return;
            }

            function selectDeviceType() {
                let selectDeviceType = `
                    <div class="add-new-device-dialog ph-dialog-v2" style="padding: 20px;">
                        <p>
                            ${t('What type of device would you like to associate with your club?')}
                        </p>
                        <hr>
                        <center>
                            <a class="btn btn-outline waves-effect b-screen">
                                <i class="fa-solid fa-tv"></i>
                                ${t('Coaching Screen')}
                            </a>
                            <a class="btn btn-outline waves-effect b-cctv">
                                <i class="fa-solid fa-video"></i>
                                ${t('CCTV Camera')}
                            </a>
                            <a class="btn btn-outline waves-effect b-panic">
                                ${panicButtonSVG}
                                ${t('Duress Button')}
                            </a>
                        </center>
                        <div class="alert alert-blue" style="margin-top: 10px;">
                            <i class="fa-solid fa-circle-info" style="color: #2262b3;" aria-hidden="true"></i>
                            <b>${t('Looking to add Sonos?')}</b><br>
                            <p style="font-weight: 300;">
                                ${t('Sonos devices are automatically detected by your Coaching Screens and do not need to be manually added into the Performance Hub.')}
                            </p>
                        </div>
                    </div>
                `

                swalWithBootstrapButtons.fire({
                    title: t('Select Device Type'),
                    html: selectDeviceType,
                    width: 500,
                    showCloseButton: true,
                    showConfirmButton: false,
                    showCancelButton: false,
                    onBeforeOpen: () => {
                        $(".b-screen").click(function () { addNewScreen() });
                        $(".b-cctv").click(function () { addNewCCTV() });
                        $(".b-panic").click(function () { addNewPanicButton() });
                    }
                })
            }

            function addNewScreen() {

                let addDeviceHTML = `
                <div class="add-new-device-dialog ph-dialog-v2" style="padding: 20px;">
                    <fieldset class="ph-fieldset d-flex flex-column flex-gap-2">
                        <div class="row d-flex align-items-center">
                            <div class="col-sm-5 ph-label">
                                ${t('Pairing ID')}:
                            </div>
                            <div class="col-sm-7">

                                <div id="deviceIDInput">
                                    <div class="field-wrapper">
                                        <div class="form-group phone">
                                            <input data-hj-allow type="text" name="letters[]" class="letter"
                                                   pattern="[0-9]*" inputmode="text" maxlength="1">
                                            <input data-hj-allow type="text" name="letters[]" class="letter"
                                                   pattern="[0-9]*" inputmode="text" maxlength="1">
                                            <input data-hj-allow type="text" name="letters[]" class="letter"
                                                   pattern="[0-9]*" inputmode="text" maxlength="1">
                                            <input data-hj-allow type="text" name="letters[]" class="letter"
                                                   pattern="[0-9]*" inputmode="text" maxlength="1">
                                            <input data-hj-allow type="text" name="letters[]" class="letter"
                                                   pattern="[0-9]*" inputmode="text" maxlength="1">
                                            <input data-hj-allow type="text" name="letters[]" class="letter"
                                                   pattern="[0-9]*" inputmode="text" maxlength="1">
                                        </div>
                                    </div>
                                </div>
                                <div id="deviceIDWrapper">
                                    <input type="hidden" name="deviceID" value="">
                                </div>
                                <small><a class="scan-with-qr"><i class="fa-solid fa-qrcode"></i> ${t('Scan QR Code With Camera')}</a></small>

                            </div>
                        </div>
                        <div id="qr-reader" style="width: 100%; display: none;"></div>

                        <div class="row d-flex align-items-center">
                            <div class="col-sm-5 ph-label">
                                ${t('Equipment Associated')}:
                            </div>
                            <div class="col-sm-7">
                                ${$.sDevices.base.equipmentSelect()}
                            </div>
                        </div>
                        <div class="row d-flex align-items-center">
                            <div class="col-sm-5 ph-label">
                                ${t('Display Name')}:
                            </div>
                            <div class="col-sm-7 ph-form">
                                <input data-hj-allow class="display-name" type="text" value="" placeholder="${t('Round #1')}">
                            </div>
                        </div>
                    </fieldset>

                    <div class="ph-alert mt-4">
                        <div class="ph-alert__description --no_title d-flex flex-column flex-gap-2 align-end"> 
                            ${t('Please refer to the Installation & Configuration Guide for device setup instructions if you have not yet powered on and connected your device to your network.')}
                            <br><a class="btn btn-default ph-btn waves-effect btn-how-to-guide" role="button">${t('View Installation Guide in Knowledge Base')}</a>
                        </div>
                    </div>

                </div>`;

                let newDeviceObj;

                swalWithBootstrapButtons.fire({
                    title: t('Add Coaching Screen Hardware'),
                    html: addDeviceHTML,
                    width: 500,
                    showCloseButton: false,
                    showConfirmButton: true,
                    showCancelButton: true,
                    confirmButtonText: t("Add"),
                    cancelButtonText: t("Cancel"),
                    onBeforeOpen: () => {

                        $(".equipment-round").selectpicker();

                        $(function() {
                            $('#deviceIDInput').letteringInput({
                                inputClass: 'letter',
                                hiddenInputWrapperID: 'deviceIDWrapper',
                                hiddenInputName: 'deviceID',
                                onLetterKeyup: function ($item, event) {
                                    validate();
                                }
                            });
                            // Select the input when you click on the device ID...
                            $("#deviceIDInput input[type='text']").click(function () {
                                $(this).select();
                            });

                            // Paste data into all text boxes if pasted from clipboard...
                            $("#deviceIDInput input[type='text']").bind("paste", function(e){
                                var pastedData = e.originalEvent.clipboardData.getData('text').slice(0, 6);
                                setCode(pastedData);
                            });

                            if(pairingCode) {
                                setCode(pairingCode.slice(0, 6));
                            }

                            function setCode(pastedData) {
                                $.each($("#deviceIDInput input"), function(index, value) {

                                    $(this).val(pastedData[index]);

                                    if($(this).val() == "" || $(this).val() == null) {
                                        $(this).focus();
                                    }
                                    if(pastedData.length == 6 && index == 5) {
                                        // tab on last input (due to bug with the input mask)
                                        let e = jQuery.Event("keydown");
                                        e.which = 9; // Tab key ...
                                        $(this).trigger(e);

                                        setTimeout(function(){ $(".display-name").focus(); }, 100);
                                    }

                                });
                                $("#deviceIDInput").find("input:last").keyup();
                            }

                            $(".scan-with-qr").click(function() {

                                const qrScanner = new Html5QrcodeScanner("qr-reader", QRScannerBaseConfig);
                                qrScanner.render(onScanSuccess);
                                $(".scan-with-qr").hide();
                                $("#qr-reader").show();

                                function onScanSuccess(decodedText, decodedResult) {

                                    // http://ubx.fit/ph/tv/?123456
                                    // console.log('qr', decodedText)

                                    let lastPartOfQuerystring = decodedText.substring(decodedText.lastIndexOf('/') + 1)
                                    const queryParams = new URLSearchParams(lastPartOfQuerystring);

                                    queryParams.forEach((value, key) => {
                                        let pairingCode = key.slice(0, 6)
                                        setCode(pairingCode)
                                    });

                                    qrScanner.clear();

                                    $(".scan-with-qr").show();
                                    $("#qr-reader").hide();
                                }

                            });

                        });

                        $(".equipment-round").change(function() {
                            validate()
                        });
                        $(".display-name").keyup(function() {
                            validate()
                        });
                        $(".btn-how-to-guide").click(function() {
                            swalWithBootstrapButtons.close();
                            firstLoad = true; sammy.quiet = false;
                            sammy.setLocation("/wiki/#%2Fopenkb%2Fkb%2Fcoaching-screen-install-config-guide");
                            firstLoad = false;
                        })

                        validate();
                        setTimeout(function(){ $("#deviceIDInput > div > div > input:nth-child(1)").focus(); }, 200);

                        function validate() {
                            var isValid = true;

                            if(! $("#deviceIDWrapper input").val()) isValid = false;
                            if(! $(".equipment-round").selectpicker('val')) isValid = false;
                            if(! $(".display-name").val()) isValid = false;

                            if(isValid) {
                                $('.swal2-actions .swal2-confirm').prop('disabled', false);
                            } else {
                                $('.swal2-actions .swal2-confirm').prop('disabled', true);
                            }
                        }
                    },
                    onClose: () => {
                        newDeviceObj = {
                            deviceID: $("#deviceIDWrapper input").val().toUpperCase(),
                            equipmentRound: $(".equipment-round").selectpicker('val'),
                            displayName: $(".display-name").val()
                        }
                    }
                }).then(response => {
                    if (response.value) {
                        associateDevice(newDeviceObj, 'screen');
                    }
                });

            }

        }

        function associateDevice(data, type='screen') {

            console.log('associateDevice', data, type);

            function setBasePath(type) {
                switch(type) {
                    case 'screen':
                        return `/api/ics/devices/add/${selectedID}`
                        break;
                    case 'camera':
                        return `/api/cctv/devices/associate`
                        break;
                    case 'duress':
                        return `/api/devices/addGenericDevice/${selectedID}`
                        break;
                  default:
                    return false
                }
            }

            const notifyDuressButtonActivation = () => {
                const title = t('Duress Button Next Steps');
                const message = `
                    <div class="ph-dialog-v2" style="padding: 20px;">
                        <p>
                            <b>${t('Wait Time')}:</b> ${t('Please allow up to 15 minutes for your new Duress Button to sync with the Performance Hub. This will enable status reporting, including battery usage.')}
                        </p>
                        <p>
                            <b>${t('Device Reboot Required')}:</b> ${t('Remember to reboot your Coaching Screens when convenient for the configuration to update automatically.')}
                        </p>
                    </div>
                `;

                genericDialog(title, message);
            }

            let basePath = setBasePath(type)
            var req = $.ajax({
                url: basePath,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(data){
                    if((data === undefined) ? false : data !== false) {

                        if(type == 'duress') {
                            notifyDuressButtonActivation();
                        } else {
                            instantNotification(
                                // Message...
                                t('Added/associated device with club')
                            );
                        }

                        // Delay refreshing devices so our API calls behind the scenes will have been made...
                        setTimeout(function(){ $.sDevices.base.fetchDeviceStats() }, 1000);

                    } else {
                        if(data.error) {
                            instantNotification(
                                // Message...
                                `<b>${t('FAILED TO ADD DEVICE!')}</b><br><br>${data.error}`,
                                // Type...
                                'error',
                                // Timeout (1m)...
                                60000
                            );
                        } else {
                            instantNotification(
                                `<b>${t('FAILED TO ADD DEVICE!')}</b><br><br>${t('An error occurred while trying to associate the device with your club...<br>Please check the device has not already been added and/or contact HQ for further details.')}`,
                                'error',
                                60000
                            );
                        }
                        console.dir(response);
                    }
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);

                    if(xhr.responseJSON.error) {
                        instantNotification(
                            `<b>${t('FAILED TO ADD DEVICE!')}</b><br><br>${xhr.responseJSON.error}`,
                            'error',
                            60000
                        );
                    } else {
                        instantNotification(
                            `<b>${t('FAILED TO ADD DEVICE!')}</b><br><br>${t('An error occurred while trying to associate the device with your club...<br>Please check the device has not already been added and/or contact HQ for further details.')}`,
                            'error',
                            60000
                        );
                    }
                }
            });

        }


        this.generateSkeletonLoader();
        this.initHealthCheck();
        
    },
    previewICSScreenshot: function(imgData=null, balenaDeviceID=null) {

        const displayName = Object.values(currentDevicesObj).find(device => device?.balenaUUID === balenaDeviceID)?.displayName ?? "Coaching Screen";
    
        var screenshot = new Image();
            screenshot.src = imgData;
            screenshot.id = 'previewScreenshotImg';

        const previewScreenshotContainer = document.createElement("div");
        previewScreenshotContainer.className = "preview-image-modal";

        $(previewScreenshotContainer).html(`
            <div class="ph-dialog-v2 screenshot-container" style="max-height: 70vh;">
                <div style="padding: 40px;">
                    <h3 style="">${t('Requesting screenshot from device...')}</h3>
                    <center><img src="/assets/images/tail-spin.svg"></center>
                </div>
            </div>
        `);

        var previewWindow = swalWithBootstrapButtons.fire({
            html: previewScreenshotContainer,
            title: `${t('Screenshot from')} ${displayName}`,
            width: 'auto',
            showCloseButton: true,
            showCancelButton: false,
            showConfirmButton: false,
        })

        if(imgData !== null) {
            console.log('++ image data not null');
            screenshot.onload = function() {
                $(previewScreenshotContainer).find(".screenshot-container").html(screenshot);
            }
        }

    },
    openSpeedRequirement: function () {
      Swal.fire({
          customClass: {
            popup: 'speed-req-modal',
            container: 'speed-req-modal__container'
          },
          html: `<div class="speed-req-iframe">
                  <iframe 
                    src="/
                    /speed-requirements" 
                    frameborder="0"
                    allowfullscreen
                    allowtransparency="true"
                    scrolling="yes"
                    width="100%"
                    height="100%"
                  ></iframe>
                  <button class="custom-swal-close">
                    <svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z" fill="currentColor"/></svg>
                  </button>
                </div>`,
      });

      // custom modal close
      $('.custom-swal-close').on('click', () => {
        Swal.close();
      })

      // fix jumping button placement
      setTimeout(() => {
        $('.custom-swal-close').css({
          opacity: 1
        })
      }, 1000)
  },
  openWikiIframe: function () {
    Swal.fire({
        customClass: {
          popup: 'speed-req-modal',
          container: 'speed-req-modal__container'
        },
        html: `<div class="speed-req-iframe">
                <iframe 
                  src="/openkb/" 
                  frameborder="0"
                  allowfullscreen
                  allowtransparency="true"
                  scrolling="yes"
                  width="100%"
                  height="100%"
                ></iframe>
                <button class="custom-swal-close">
                  <svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z" fill="currentColor"/></svg>
                </button>
              </div>`,
    });

    // fix jumping button placement
    setTimeout(() => {
      $('.custom-swal-close').css({
        opacity: 1
      })
    }, 1000)
  },
  initHealthCheck: function() {
    // set active tab based on search
    const query = location.search;
    this.activeTab = query ? query.split('=')[1] : 'healthCheck';

    // accept message from health check frame
    function acceptMessage(e) {
      const { healthCheckModal, open, height } = e.data
      
      if(!healthCheckModal) return;
      
      if(healthCheckModal === 'speedRequirement') {

        if(open) $.sDevices.base.openSpeedRequirement();

        if(height) {
          $('.speed-req-modal').css({
            height: height + 48
          })
        }

        return;
      }

      if(healthCheckModal === 'wikiIframe') {
        if(open) $.sDevices.base.openWikiIframe();
        return
      }
    }
    window.addEventListener('message', acceptMessage)

    // custom modal close
    $('body').on('click', '.custom-swal-close', () => {
      Swal.close();
    })
  }
}

function openComplianceDialog(deviceType, deviceDetails) {

    function generateComplianceIcons(partialNames) {
        return partialNames.map((name, i) => {
            // Construct the full filename using the partial name
            const filename = `compliance-icon-${name}.svg`;
            // Return the HTML string for the image
            return `<img src="/assets/images/${filename}" class="device-compliance-modal__icon device-compliance-modal__icon-${i + 1}">`;
        });
    }

    const deviceTypeText = (() => {
        switch (deviceType) {
            case 'camera':
                return `UBX ${t('AI Camera with')} ${deviceDetails?.metadata?.activeCamera?.vendor ?? ''} ${deviceDetails?.metadata?.activeCamera?.model ?? ''}`;
            case 'duress':
                return t('Duress Button B1');
            case 'screen':
                return `${ t('Coaching Screen')} (${
                            (deviceDetails?.metadata?.deviceVersions?.DEVICE_MODEL === "TS10") ? "Asus " :
                            (deviceDetails?.metadata?.deviceVersions?.DEVICE_MODEL === "STK1AW32SC") ? "Intel " :
                            (deviceDetails?.metadata?.deviceVersions?.DEVICE_MODEL.includes("PN41")) ? "Asus " : ""
                            }${deviceDetails?.metadata?.deviceVersions?.DEVICE_MODEL})`
            default:
                return t('Device Details');
        }
    })();

    const icons = Array.from({ length: 16 }).map((_, i) => {
        return `<img src="/assets/images/compliance-icon-${i + 1}.svg" class="device-compliance-modal__icon device-compliance-modal__icon-${i + 1}">`;
    })

    const deviceManualsAndComplianceURLs = (() => {
        let deviceType = deviceDetails?.metadata?.deviceVersions?.DEVICE_MODEL ?? '';
        if (deviceType.includes('TS10')) {
            return `
                <div class="certifications">
                    <small>
                        <a href="https://api.cert.wi-fi.org/api/certificate/download/public?variantId=54078" target="_blank">${t('Qualcomm Atheros QCA9377 Regulatory')}</a>,
                        <a href="https://dlcdnet.asus.com/pub/ASUS/NanoPC/TS10/E13186_VivoStick_TS10_UM_V5_WEB.pdf" target="_blank">${ t('Asus VivoStick Configuration Guide')}</a>
                    </small>
                </div>
            `;
        } else if (deviceType.includes('STK1AW32SC')) {
            return `
                <div class="certifications">
                    <small>
                        <a href="https://www.intel.com/content/dam/support/us/en/documents/wireless/3165_7265_StPx_Regulatory_Webflyer.pdf" target="_blank">${ t('Intel Wireless 7265 Family Regulatory')}</a>,
                        <a href="https://www.intel.com/content/dam/support/us/en/documents/boardsandkits/computestick/STK1AW32SC_STK1A32SC_TechProdSpec.pdf" target="_blank">${ t('Intel Compute Stick Technical Specification')}</a>
                    </small>
                </div>
            `;
        } else if (deviceType.includes('PN41')) {
            return `
                <div class="certifications">
                    <small>
                        <a href="https://www.intel.com/content/dam/support/us/en/documents/wireless/ax201-regulatory-webflyer-ccg.pdf" target="_blank">${t('Intel AX201 Regulatory Certification')}</a>,
                        <a href="https://dlcdnets.asus.com/pub/ASUS/Desktop/Vivo_PC/PN41/E17726_PN41_EM_WEB.pdf?model=PN41" target="_blank">${t('ASUS PN41 Manual')}</a>
                    </small>
                </div>
            `;
        } else {
            return '';
        }
    })();

    const deviceFCCIDs = (() => {
        let deviceType = deviceDetails?.metadata?.deviceVersions?.DEVICE_MODEL ?? '';
        if (deviceType.includes('TS10')) {
            return `
                <div class="device-compliance-modal__details-row">
                  <div class="device-compliance-modal__details-col">
                    <ul>
                      <li>FCC ID: 2AKZA-QCA9377</li>
                      <li>IC: 22364-QCA9377</li>
                    </ul>
                  </div>
                  <div class="device-compliance-modal__details-col">
                    <div class="device-compliance-modal__giteki">
                      <img src="/assets/images/compliance-icon-ja-mic.svg">
                      <div>
                        <span>TELEC: 201-180629</span>
                      </div>
                    </div>
                  </div>
                </div>
                <p>${t('5GHz product for indoor use only')}</p>
            `;
        } else if (deviceType.includes('STK1AW32SC')) {
            return `
                <div class="device-compliance-modal__details-row">
                  <div class="device-compliance-modal__details-col">
                    <p class="device-compliance-modal__badge">CCAH14LP0290T0</p>
                    <ul>
                      <li>FCC ID: PD9-7265NGU</li>
                      <li>NCA Approved: 3R2-1M-140-31</li>
                    </ul>
                  </div>
                  <div class="device-compliance-modal__details-col">
                    <div class="device-compliance-modal__giteki">
                      <img src="/assets/images/compliance-icon-ja-mic.svg">
                      <div>
                        <span>003140018</span>
                        <span>D140017003</span>
                      </div>
                    </div>
                    <p>${t('5GHz product for indoor use only')}</p>
                  </div>
                </div>
            `;
        } else if (deviceType.includes('PN41')) {
            return `
                <div class="device-compliance-modal__details-row">
                  <div class="device-compliance-modal__details-col">
                    <p class="device-compliance-modal__badge">CCAH18LP3520T2</p>
                    <ul>
                      <li>FCC ID: PD9-AX201NG</li>
                      <li>NCA Approved: ZRO-M8-7E3-X2E</li>
                    </ul>
                  </div>
                  <div class="device-compliance-modal__details-col">
                    <div class="device-compliance-modal__giteki">
                      <img src="/assets/images/compliance-icon-ja-mic.svg">
                    </div>
                    <p>${t('5GHz product for indoor use only')}</p>
                  </div>
                </div>
            `;
        } else {
            return '';
        }
    })();

    const modalHtml = `
      <div class="device-compliance-modal ph-dialog-v2">
        <div class="device-compliance-modal__details">
          <div class="device-compliance-modal__details-content">
            <h6>${ deviceTypeText }</h6>

            ${
              deviceType === 'camera' ? `
                <div class="device-compliance-modal__details-row">
                  <div class="device-compliance-modal__details-col">
                    <p class="device-compliance-modal__badge">CCAJ19LP6ED0T9</p>
                    <ul>
                      <li>FCC ID: A4R-1701AA1</li>
                      <li>NCA Approved: JLM-3H-7E3-15F</li>
                    </ul>
                  </div>
                  <div class="device-compliance-modal__details-col">
                    <div class="device-compliance-modal__giteki">
                      <img src="/assets/images/compliance-icon-ja-mic.svg">
                      <div>
                        <span>020190095</span>
                        <span>D190034020</span>
                      </div>
                    </div>
                    <p>${t('5GHz product for indoor use only')}</p>
                  </div>
                </div>
              ` : ''
            }

            ${
              deviceType === 'duress' ? `
                <div class="device-compliance-modal__details-row">
                  <div class="device-compliance-modal__details-col">
                    <ul>
                      <li>FCC ID: 2AO94-B1</li>
                    </ul>
                    <ul>
                      <li>
                        <img src="/assets/images/compliance-bluetooth.svg" style="height: 20px;">
                        ${t('2.4 GHz ISM spectrum band product')}
                      </li>
                      
                    </ul>
                  </div>
                </div>
              ` : ''
            }

            ${ deviceType === 'screen' ? deviceFCCIDs : '' }

          </div>  
          <div class="device-compliance-modal__details-cerifications">
            ${ deviceType === 'duress' ? generateComplianceIcons(['ce', 'fcc', 'rhos', 'eu-recycle-mark1', 'eu-recycle-mark2', 'au-recycle-mark1', 'ja-recycle-mark1', 'ja-recycle-mark2', ]).join('') : `` }
            ${ deviceType === 'camera' ? generateComplianceIcons(['ce', 'fcc', 'eu-recycle-mark1', 'eu-recycle-mark2', 'au-recycle-mark1', 'ja-recycle-mark1', 'ja-recycle-mark2', 'sensitive-electronics', 'eu-wee-mark1', 'eu-wee-mark2', 'vci', 'au-a-tick', 'keep-dry', 'only-transport-up', 'no-litter' ]).join('') : `` }
            ${ deviceType === 'screen' ? generateComplianceIcons(['ce', 'fcc', 'eu-recycle-mark1', 'eu-recycle-mark2', 'au-recycle-mark1', 'ja-recycle-mark1', 'ja-recycle-mark2', 'eu-wee-mark1', 'eu-wee-mark2', 'vci', 'au-a-tick', 'keep-dry', 'no-litter', 'hdmi', 'energy-star' ]).join('') : `` }
          </div>    
        </div>
  
        ${
          deviceType === 'screen' ? `
            ${ deviceManualsAndComplianceURLs }
            <div class="device-compliance-modal__highlight">
              <p>
                ${t('This device should be installed with a minimum separation of 20 cm from the antenna to any person is required to comply with RF exposure limits.')}
              </p>
            </div>
  
            <div class="device-compliance-modal__content">
                <p>
                    ${t("This device is coated for electrical insulation, excluding the I/O port areas, to ensure electrical safety. It complies with Part 15 of the FCC Rules, meaning it should not cause harmful interference and must tolerate any interference received. Although this equipment is tested against Class B limits to be safely used in home environments, there's no absolute assurance against interference. If interference to radio or TV reception occurs, it is recommended to reorient the antenna, increase separation from the receiver, or consult with a technician.")}
                </p>
                <p>
                    ${t("This device should not be placed near or operate with other antennas or transmitters. In Canada, adherence to ISED radiation exposure limits is necessary. The device cannot cause interference and must accept any interference per the regulatory conditions. Lastly, the device conforms to HDMI Licensing Administrator, Inc. standards, as indicated by its compliance statement. Any unauthorized changes could void the authority to operate this equipment.")}
                </p>
            </div>
          ` : ''
        }
  
        ${
          deviceType === 'duress' ? `
            <div class="device-compliance-modal__highlight">
              <p>
                ${t("This device is IP65 rated. Do not place any hot items on or near the device as the device contains a lithium battery inside.")}
              </p>
            </div>
  
            <div class="device-compliance-modal__content">
              <p>
                ${t("Note: The Duress Button B1 has been tested and complies with the limits for a Class A digital device, pursuant to FCC Rules. It features a rechargeable 240mAh lithium battery, with a Type-C charge port and offers a prolonged operational period, which varies depending on usage conditions (e.g., up to 9 months at 0dBm Tx Power, 1000ms advertising interval). When operated in commercial environments, it adheres to standards set for minimizing harmful interference with other devices. Improper installation or use could lead to radio communication interference. If this equipment is used in a residential area, it may cause interference, necessitating the user to implement appropriate measures to correct the interference at their own expense.")}
              </p>
            </div>
          ` : ''
        }
  
        ${
          deviceType === 'camera' ? `
            <div class="device-compliance-modal__highlight">
              <p>
                <strong>${t("This product contains a SBC designed and engineered by ASUSTek Computer Inc")}</strong>. <br>
                ${t("The device conforms to the above certifications, and should be installed and operated with minimum distance 20cm between the device and the general public.")}
              </p>
            </div>
  
            <div class="device-compliance-modal__content">
              <p>
               ${t("Note: This equipment has been tested and found to comply with the limits for a Class A digital device. pursuant to part of the FCC Rules. These limits are designed to provide reasonable protection against harmful interference when the equipment is operated in a commercial environment. This equipment generates, uses and can radiate radio frequency energy and, if not installed and used in accordance with the instruction manual, may cause harmful interference to radio communications. Operation of this equipment in a  residential area may cause harmful interference in which case the user will be required to correct the interference at his own expense.")}
              </p>
            </div>
          ` : ''
        }
      </div>
    `

    swalWithBootstrapButtons.fire({
        title: t('Compliance'),
        html: modalHtml,
        width: 700,
        showCloseButton: true,
        showConfirmButton:  false,
        showCancelButton: true,
        confirmButtonText: t("Save Changes"),
        cancelButtonText: t("Close"),
        customClass: {
            container: 'device-details-dialog__container',
            cancelButton: 'btn btn-default waves-effect'
        }
    })
}
