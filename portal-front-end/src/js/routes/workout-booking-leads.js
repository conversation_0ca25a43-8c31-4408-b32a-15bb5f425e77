const initClubPreferences = {
    id: "",
    clubId: "",
    clubNotificationPreferences: {
        phones: [],
        includeClubPhone: true,
    }
};

let clubInformation = null;
let wbLeadsTable = null;

const initWBLeads = async () => {
    renderWBLeadsSearchbarHeader();
    loadWBLeadsData();
    clubInformation = await getClubInformation(selectedID);

    $('#show-upcoming-leads-only').on('change', () => {
        loadWBLeadsData();
    });

    // Attach click listener for the new main settings button
    $('#main-settings-btn').append(`<div class="settings-icon-outline nav-tab-item">${settingsIcon()}</div>`);
    $('#main-settings-btn').on('click', openConfigureFreeTrialBookings);
};

const exportButtons = [
    { label: 'COPY', icon: 'fa-copy', onClick: () => wbLeadsTable && wbLeadsTable.button(0).trigger() },
    { label: 'PRINT', icon: 'fa-print', onClick: () => wbLeadsTable && wbLeadsTable.button(1).trigger() },
    { label: 'CSV', icon: 'fa-file-csv', onClick: () => wbLeadsTable && wbLeadsTable.button(2).trigger() },
    { label: 'PDF', icon: 'fa-file-pdf', onClick: () => wbLeadsTable && wbLeadsTable.button(3).trigger() },
];

const renderWBLeadsSearchbarHeader = () => {
    searchbarHeader(
        '.searchbar-header-container',
        [   
            { label: 'DELETE SELECTED', icon: 'fa-trash', className: 'delete-lead', disabled: true },
            ...exportButtons,
        ],
        {
            usePhTheme: true,
            expandableButtons: {
                startIndex: 1,
                count: exportButtons.length,
                label: t('Export'),
                icon: 'fa-ellipsis-v',
                id: 'facilities-export-buttons',
            },
        }
    );
};

let loadWBLeadsDataTimeout = null;
const loadWBLeadsData = () => {
    loadWBLeadsDataTimeout && clearTimeout(loadWBLeadsDataTimeout);

    if ($('#wb-leads-table').length > 0) {
        $('#wb-leads-table').DataTable().clear();
        $('#wb-leads-table').DataTable().destroy();
    }

    $('#wb-leads-table-container').empty();
    $('#wb-skeleton-loader').show();
    $('#wb-leads-table-container').hide();

    // show loading text if loading takes longer than 2 seconds
    const wbLoaderText = setTimeout(() => {
      $('#wb-skeleton-loader-text').show();
      $('#wb-leads-table-container').hide();
    }, 3000);

    // timeout to prevent multiple requests from bugging the UI
    loadWBLeadsDataTimeout = setTimeout(() => {
        fetchWBLeadsData((leads) => {
            $('#wb-leads-table-container').empty();

            if (leads) {
                $('#wb-leads-table-container').prepend(
                    '<table id="wb-leads-table" class="table ph-table table-hover dataTable">' +
                        '<thead>' +
                            '<tr>' +
                                '<th>'+t('Name')+'</th>' +
                                '<th>'+t('Email')+'</th>' +
                                '<th>'+t('Phone')+'</th>' +
                                '<th>'+t('Lead Created')+'</th>' +
                                `<th>
                                    <div class="d-flex">
                                        ${t('Workout Booking Time')}
                                        <i
                                            class="material-icons info-icon"
                                            data-toggle="tooltip"
                                            data-placement="right"
                                            title="${t('Booking times listed below are relative to your Clubs Timezone')}"
                                            style="font-size: 15px; margin-left: 2px;"
                                        >
                                            info_outline
                                        </i>
                                    </div>
                                </th>` +
                                '<th>'+t('GymMaster Status')+'</th>' +
                                '<th>'+t('Campaign')+'</th>' +
                                '<th></th>' +
                            '</tr>' +
                        '</thead>' +
                        '<tbody></tbody>' +
                    '</table>'
                );

                leads.forEach((lead, index) => {
                    $('#wb-leads-table tbody').append(getLeadRow(index, lead));
                });

                let export_filename = 'Workout-Booking-Leads__' + (new Date().toISOString());
                let dtConfig = {
                    responsive: true,
                    iDisplayLength: 50,
                    dom: '<"toolbar">Br<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
                    order: [[ 3, "desc" ]],
                    buttons: [
                        {
                            text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">'+t('Copy')+'</span>',
                            extend: 'copy',
                            className: 'btn btn-sm btn-default'
                        },
                        {
                            text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">'+t('Print')+'</span>',
                            extend: 'print',
                            title: 'Workout Booking Leads & Registrations',
                            className: 'btn btn-sm btn-default'
                        },
                        {
                            text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">'+t('CSV')+'</span>',
                            extend: 'csv',
                            className: 'btn btn-sm btn-default',
                            title: export_filename,
                            extension: '.csv'
                        },
                        {
                            text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">'+t('PDF')+'</span>',
                            extend: 'pdf',
                            className: 'btn btn-sm btn-default',
                            title: export_filename,
                            extension: '.pdf'
                        },
                    ],
                    bAutoWidth: false, 
                    aoColumns : [
                        { sWidth: '20%' },
                        { sWidth: '10%' },
                        { sWidth: '15%' },
                        { sWidth: '15%' },
                        { sWidth: '20%' },
                        { sWidth: '10%' },
                        { sWidth: '10%' },
                        { sWidth: '10px' },
                    ]
                }

                wbLeadsTable = $('#wb-leads-table').DataTable(dtConfig);
                wrapTableWithScrollableDiv('#wb-leads-table');

                // Info tooltip(s) within the datatable need to be rendered once the datatable is created.
                $('[data-toggle="tooltip"]').tooltip({
                    container: '.dataTable',
                    open: handleArrowTooltipOpen
                });

                $('.workout-booking-leads-page .search-bar').on('keyup search', (e) => {
                    const value = $(e.currentTarget).val();
                    wbLeadsTable.search(value).draw();
                });

                $('#wb-leads-table tbody').on('click', 'tr .selectable:not(.mms)', (e) => {
                    editLeadDialog($(e.currentTarget).closest('tr'));
                });

                $('#wb-leads-table tbody').on('click', 'tr .selectable.mms', (e) => {
                    const { mmsId } = $(e.currentTarget).closest('tr').data();

                    if(mmsId && mmsId !== '') {
                        window.open("https://" + gymMasterDomain + "/member/view/" + mmsId, '_blank');
                    } else {
                        window.open("https://" + gymMasterDomain + "/member/add-member", '_blank');
                    }
                });

                $('#wb-leads-table').on('change', 'input[type=checkbox]', () => {
                    const leads = getSelectedLeads();

                    
                    if (leads.length <= 0) {
                        $('.delete-lead').attr('disabled', 'disabled');
                    } else {
                        $('.delete-lead').removeAttr('disabled');
                    }
                });

                $('.delete-lead').on('click', () => {
                    confirmDeleteDialog(
                        t('Delete the selected Bookings?'),
                        '<p>'+t('Note that any scheduled reminders/notifications will also be removed, and all records of this booking will be removed from the system')+'</p>',
                        deleteSelectedLeads,
                    );
                });
            }

            clearTimeout(wbLoaderText);
            $('#wb-skeleton-loader-text').hide();
            $('#wb-skeleton-loader').hide();
            $('#wb-leads-table-container').show();
        });
    }, 800);
};

const deleteSelectedLeads = () => {
    const leads = getSelectedLeads();

    if (leads.length > 0) {
        $(".save-overlay").show();
        $.ajax({
            url: `/api/members-leads/workout-booking/club/leads/${selectedID}`,
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({ leads }),
            success: () => {
                $(".save-overlay").hide();
                instantNotification(t('Workout booking leads deleted.'), 'success', 60000);
                loadWBLeadsData();
            },
            error: (xhr, textStatus, errorThrown) => {
                $(".save-overlay").hide();
                console.error(xhr, textStatus, errorThrown);
                instantNotification(xhr.responseText, 'error', 60000);
            },
        });
    }
}

const getSelectedLeads = () => {
    const elements = $('#wb-leads-table-container tr input[type=checkbox]:checked');
    const leads = [];

    elements.map((_, element) => {
        const { id } = $(element).closest('tr').data();
        leads.push(id);
    });

    return leads;
}

const getLeadRow = (index, lead) => {
    const tags = [];

    if(!lead.mms) {
        tags.push('<span class="badge badge-green ">'+t('Not Found')+ '<i><b>('+t('NEW LEAD')+')</b></i></span>');
    } else if(lead.mms.status.toLowerCase().includes("expired")){
        if(lead.mms.prospect) {
            tags.push('<span class="badge badge-blue">'+t('GymMaster')+': <i>'+t('Prospect')+'</i></span>');
        } else {
            tags.push('<span class="badge badge-orange">'+t('GymMaster')+': <i>' + lead.mms.status + '</i></span>');
        }

    } else {
        tags.push('<span class="badge badge-lightgrey">'+t('GymMaster')+': <i>' + lead.mms.status + '</i></span>');
    }

    return (
        '<tr' +
            ` data-index="${index}"` +
            ` data-name="${lead.name}"` +
            ` data-email="${lead.email}"` +
            ` data-created="${ (lead.created === undefined) ? '' : lead.created }"` +
            ` data-phone="${lead.phone}"` +
            ` data-date="${lead.date}"` +
            ` data-mms-id="${lead.mms ? lead.mms.id : ''}"` +
            ` data-mms-status="${lead.mms ? lead.mms.status : ''}"` +
            ` data-id="${lead.id}"` +
        '>' +
            '<td class="selectable">' + lead.name + '</td>' +
            '<td class="selectable">' + lead.email + '</td>' +
            '<td class="selectable" data-hj-allow>' + lead.phone + '</td>' +
            `<td class="selectable" data-hj-allow data-sort="${ (lead.created === undefined) ? '' : lead.created }">${ (lead.created === undefined) ? t('Unknown') : moment(lead.created * 1000).format('YYYY-MM-DD hh:mma') }</td>` +
            `<td class="selectable" data-hj-allow data-sort="${lead.date}">
                ${ moment(lead.date * 1000).tz(selectedClubObj.localisation.timezone).format('ddd, DD MMMM YYYY hh:mma') }
            </td>` +
            `<td class="selectable mms">${tags.join('')}</td>` +
            `<td class="selectable">${lead.campaign ? `<div class="badge badge-blue">${lead.campaign}</div>` : ''}</td>` +
            '<td>' +
                `<div class="styled-checkbox">` +
                    `<input id="checkbox-${lead.id}" type="checkbox" class="filled-in chk-col-blue">` +
                    `<label for="checkbox-${lead.id}"></label>` +
                '</div>' +
            '</td>' +
        '</tr>'
    );
}

const fetchWBLeadsData = (cb) => {
    const showAll = !$('#show-upcoming-leads-only').is(':checked');

    $.ajax({
        url: `/api/members-leads/workout-booking/club/leads/${selectedID}?all=${showAll}`,
        method: 'GET',
        success: (leads) => {
            cb(leads);
        },
        error: (xhr, textStatus, errorThrown) => {
            console.error(xhr, textStatus, errorThrown);
            cb(false);
        },
    });
};

const updateWBLeadData = (data, cb) => {
    $.ajax({
        url: `/api/members-leads/workout-booking/club/leads/${selectedID}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: () => cb(true),
        error: (xhr, textStatus, errorThrown) => {
            console.error(xhr, textStatus, errorThrown);
            instantNotification(xhr.responseText, 'error', 60000);
            cb(false);
        },
    });
}

const editLeadDialog = async (element) => {
    const leadData = element.data();
    let leadDetailsObj;

    // Build html content for popup...
    const div = document.createElement("div");

    $(div).html(`
        <div class="ph-dialog-v2" style="min-height: 480px; padding: 20px;">
            <ul class="nav nav-tabs tab-nav-right" role="tablist">
                <li role="presentation" class="active">
                    <a href="#booking-information-container" data-toggle="tab">
                        ${t('INFORMATION')}
                    </a>
                </li>
                <li role="presentation">
                    <a id="membership-details-tab" href="#booking-membership-container" data-toggle="tab">
                        ${t('MEMBERSHIP DETAILS')}
                    </a>
                </li>
            </ul>
            <div class="tab-content">
                <div role="tabpanel" class="tab-pane in active" id="booking-information-container">
                    <div class="grow-1">
                        <div class="py-1">
                            <form id="edit-trial-booking">
                                <div class="form-input form-group">
                                    <div class="form-line">
                                        <label style="margin-bottom: 0px;">Booking Date:</label>
                                        <div style="font-size: 10px; margin-bottom: 5px;"><i>${t('Note that booking date/times are based on the local timezone of your Club')}</i></div>
                                        <input class="form-control" data-hj-allow type="text" name="date" />
                                    </div>
                                </div>
                                <div class="form-input form-group">
                                    <div class="form-line">
                                        <label>${t('Name')}:</label>
                                        <input class="form-control" data-hj-allow type="text" name="name" value="${leadData.name}" />
                                    </div>
                                </div>
                                <div class="form-input form-group">
                                    <div class="form-line">
                                        <label>${t('Email')}:</label>
                                        <input class="form-control" data-hj-allow type="text" name="email" value="${leadData.email}" />
                                    </div>
                                </div>
                                <div class="form-input form-group">
                                    <div class="form-line">
                                        <label>${t('Phone')}:</label>
                                        <input class="form-control" data-hj-allow type="text" name="phone" value="${leadData.phone}" />
                                    </div>
                                    <div class="text-danger hidden" id="edit-phone-validation-error"></div>
                                    <div id="editing-alert" class="alert alert-blue hidden" style="margin-top: 1rem;">
                                        ${t('Please note editing the details of a booking will automatically send an update/confirmation SMS to the lead.')}
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div role="tabpanel" class="tab-pane fade" id="booking-membership-container">
                    <div class="grow-1">
                        <div id="membership-table-container" class="py-1">
                            <div id="membership-please-wait" class="text-center">
                                ${t('Please wait')}...
                            </div>
                            <div id="no-membership-message" class="text-center hidden">
                                ${t('Detailed member information is not yet available for this user')}
                                <hr />
                                ${t('Once the user has signed up for a Membership additional details relating to this user will be visible here.')}
                            </div>
                            <table id="booking-membership-table">
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>`
    );

    // Bind datepicker
    $(div).find('input[name=date]').daterangepicker({
        timePicker: true,
        timePickerIncrement: 5,
        singleDatePicker: true,
        showDropdowns: true,
        startDate: moment(leadData.date * 1000).tz(selectedClubObj.localisation.timezone),
        parentEl: $(div).find('input[name=date]').parent(),
        locale: {
            format: 'MMMM DD, YYYY hh:mma'
        }
    });

    /** Fetch only if tab is clicked to optimize performance **/
    $(div).find('#membership-details-tab').on('click', async () => {
        const clubId = selectedID;
        const leadPhone = leadData && 'phone' in leadData ? leadData.phone : '';
        const leadEmail = leadData && 'email' in leadData ? leadData.email : '';
        const clubpass = await getClubpassInfoByEmailOrPhone(clubId, leadPhone, leadEmail);
        const {
            hasMembership,
            membershipDetails: gymMasterRow,
            clubPassInfo
        } = getMembershipDetails(clubpass, clubId);

        let membershipRows = buildMembershipRows(
            gymMasterRow,
            hasMembership,
            clubInformation,
            clubPassInfo
        );

        $(div).find('#membership-please-wait').addClass('hidden');
        if (hasMembership) {
            $(div).find('#booking-membership-table tbody').empty().append(membershipRows);
        } else {
            $(div).find('#no-membership-message').removeClass('hidden');
        }
    });

    $(div).addClass('record-info-dialog');

    swalWithBootstrapButtons.fire({
        // title: `Update Booking Details`,
        title: t('Booking Details'),
        html: div,
        width: 500,
        animation: false,

        //true means show cancel button, with default values
        showCancelButton: true,
        reverseButtons: false,
        showCloseButton: true,
        confirmButtonText: t('Save/Update'),
        cancelButtonText: t('Cancel'),
        onBeforeOpen: () => { 
            // Original onBeforeOpen logic
            $(div).find('#edit-trial-booking').on('input', function() {
                $(div).find('#editing-alert').removeClass('hidden');
            })

            $(div).find('input[name=phone]').on('keyup', function() {
                let phone = $(this).val();
                let isValidPhone = validatePhoneNumber(phone);
                $(div).find('#edit-phone-validation-error').text('').addClass('hidden');
                if (isValidPhone === false) {
                    $(div).find('#edit-phone-validation-error').text(t('Please input a valid phone number')).removeClass('hidden');
                    Swal.getConfirmButton().setAttribute('disabled', '');
                } else {
                    Swal.getConfirmButton().removeAttribute('disabled');
                }
            });
        },
        preConfirm: async () => {
            let error = null;
            const name = $(div).find('input[name=name]').val();
            const email = $(div).find('input[name=email]').val();
            const phone = $(div).find('input[name=phone]').val();
            const date = $(div).find('input[name=date]').val();
            
            if ([name, email, phone, date].includes('')) {
                error = t('All fields are required.');
            }

            // Check if date is valid
            const { isOpen } = await validateBookingDate(moment.tz(date, 'MMMM DD, YYYY hh:mma', selectedClubObj.localisation.timezone).unix());

            if (!isOpen) {
                error = t('Could not save changes to booking hours. Please ensure that the Booking Date & time matches the hours your club is open.');
            }

            if (error) {
                Swal.showValidationMessage(error);
            }
        },
        onClose: () => {
            const dateVal = $(div).find('input[name=date]').val();
            leadDetailsObj = {
                "name": $(div).find('input[name=name]').val(),
                "email": $(div).find('input[name=email]').val(),
                "phone": $(div).find('input[name=phone]').val(),
                "date": moment.tz(dateVal, 'MMMM DD, YYYY hh:mma', selectedClubObj.localisation.timezone).unix()
            }
        }
    }).then(response => {
        if (response.value) {
            const newLead = {
                ...{id: leadData.id},
                ...leadDetailsObj
            }
            updateWBLeadData(newLead, (response) => {
                if (response) {
                    loadWBLeadsData();
                }
            });
        }
    });
}

const buildMembershipRows = (
    data=[],
    hasMembership=false,
    clubInformation=null,
    clubPassInfo={}
) => {
    return (`
        ${data.map(({ label, key, value }) => {
            return (`
                <tr>
                    <td style="min-width: 150px; max-width: 150px;">${label}</td>
                    <td>${hasMembership && key === 'status' ? `<div class="badge ${value === 'Expired' ? `badge-red` : `badge-green`}">${value}</div>` : `${value}`}</td>
                </tr>
            `)
        }).join('')}
        ${clubInformation
            && 'gymMasterDomain' in clubInformation
            && clubInformation.gymMasterDomain
            && Object.keys(clubPassInfo).length
            && 'gmID' in clubPassInfo ? `
            <tr>
                <td colspan="2" class="pt-1">
                    <a
                        href="https://${clubInformation.gymMasterDomain}/member/view/${clubPassInfo.gmID}"
                        target="_blank"
                        data-gymid="${clubPassInfo.gmID}"
                        class="btn btn-xs btn-default waves-effect btn-show-all-grouped-payouts"
                    >
                        ${t('Edit member in GymMaster')}
                    </a>
                </td>
            </tr>` : ''
        }
    `);
}

/** This is to configure free trial bookings **/
const openConfigureFreeTrialBookings = async () => {
    const clubId = selectedID;
    let params = {
        ...initClubPreferences,
        id: clubId,
        clubId,
    };
    let phones = [];
    let clubCountryCode = '';
    if (clubInformation
        && 'placeData' in clubInformation
        && 'country' in clubInformation.placeData
        && 'iso_code' in clubInformation.placeData.country
    ) {
        clubCountryCode = clubInformation.placeData['country']['iso_code'];
    }

    const html = document.createElement("div");
    let elements = `
      <div id="swal-free-trial-settings-container" class="row clearfix ph-dialog-v2" style="padding: 20px;">
        <div class="text-left col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="card">
            <div class="body">
                <h2 class="card-inside-title">
                    ${t('Mobile Notifications')}
                </h2>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="styled-checkbox">
                            <input id="include-club-phone" type="checkbox" class="filled-in chk-col-blue" checked>
                            <label style="font-size: 12px;" for="include-club-phone">${t('Include Facilities phone')}</label>
                        </div>
                        <table id="mobile-numbers" class="table table-hover dataTable">
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-group d-flex">
                            <input
                                style="margin-right: 10px; border: 1px solid #eee;"
                                id="add-number-input"
                                type="tel"
                                class="form-control"
                                name="notification-phone-number"
                                value=""
                                placeholder="${t('example')}: 0404 000000 ${t('or')} ******** 30462"
                            />
                            <button
                                id="add-number-btn"
                                type="button"
                                class="btn btn-primary"
                            >
                                ${t('ADD')}
                            </button>
                        </div>
                        <span id="phone-validation-message" class="text-danger hidden"></span>
                    </div>
                </div>
                <hr />
                <h2 class="card-inside-title">
                    ${t('Cutoff period (based on facilities trading hours) for free trial bookings')}
                </h2>
                <div class="row display-flex">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <div class="form-line">
                                <select id="club_cutoff_time" class="form-control show-tick selectpicker">
                                    ${[30,45,60,75].map(option => {
                                        return `<option value="${option}">${option} ${t('minutes prior to facility closing')}</option>`
                                    })}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          </div>
        </div>
      </div>
    `;

    $(html).html(elements);

    /** include phone checkbox **/
    $(html).find('#include-club-phone').prop('checked', params.clubNotificationPreferences.includeClubPhone);

    /** adding jquery events to remove mobile elements **/
    const table = $(html).find('#mobile-numbers');
    const addModalEvents = () => {
        $(table).find("tr .remove-button").on('click', function() {
            let rowIndex = $(this).data('removephoneindentity');
            $(html).find('#mobile-numbers tr#phone-remove-' + rowIndex).remove();
        });
    }

    addModalEvents();

    /** append number row **/
    $(html).find('#add-number-btn').on('click', async () => {
        let phoneValue = $(html).find('#add-number-input').val();
        let rowIdentity = Math.floor(Math.random() * 1000);
        let isValidPhone = validatePhoneNumber(phoneValue);
        $(html).find('#phone-validation-message').text('').addClass('hidden');
        if (!isValidPhone) {
            $(html).find('#phone-validation-message').text(t('Please input a valid phone number')).removeClass('hidden');
            return
        }

        /** parsing a phone number ex. +61 404 111 222 **/
        let isNumberExist = false
        const resultParsePhone = await phoneParser(phoneValue, clubCountryCode, true);

        if (resultParsePhone !== false) {
            const { phone: parsedPhone } = resultParsePhone;
            const listOfNumbers = $(table).find("tr .phone-text");
            listOfNumbers.each(function() {
                if (parsedPhone === $(this).text()) {
                    isNumberExist = true
                }
            });

            if (isNumberExist) {
                $(html).find('#phone-validation-message').text(t('Phone number already exist.')).removeClass('hidden');
                return
            }

            $($(html).find('#mobile-numbers tbody')).append(`
                <tr
                    id="phone-remove-${rowIdentity}"
                    data-mobile-phone=${parsedPhone.replaceAll("+", "").replaceAll(" ", "-")}
                >
                    <td class="phone-text">${parsedPhone}</td>
                    <td style="text-align: center;">
                        <button
                            type="button"
                            class="btn btn-default remove-button"
                            data-removephone="${parsedPhone.replaceAll("+", "").replaceAll(" ", "-")}"
                            data-removephoneindentity="${rowIdentity}"
                            onRemove
                        >
                            ${t('Remove')}
                        </button>
                    </td>
                </tr>
            `);
            addModalEvents();
            $(html).find('#add-number-input').val(''); // reset the value
        } else {
            const parseErrorText = 'Phone number is not applicable in the club' + (clubInformation && 'name' in clubInformation ? ` ${clubInformation.name}.` : '.');
            $(html).find('#phone-validation-message').text(parseErrorText).removeClass('hidden');
            return
        }
    });

    swalWithBootstrapButtons.fire({
        html,
        width: 500,
        showCancelButton: true,
        showCloseButton: true,
        title: t('Free Trial Booking Settings'),
        confirmButtonText: t('Save'),
        cancelButtonText: t('Cancel'),
        onBeforeOpen: async () => {
            $('#club_cutoff_time').selectpicker();
            $('#club_cutoff_time').val(45);
            if ('cutoffTime' in clubInformation && clubInformation.cutoffTime) {
                $('#club_cutoff_time').val(clubInformation.cutoffTime);
            }
            $('#club_cutoff_time').selectpicker('refresh');

            $(html).find('#add-number-btn').prop('disabled', true);
            // get the club preferences
            getClubPreferences(clubId)
            .then((response) => {
                const clubPrefData = response;
                if (clubPrefData && Object.keys(clubPrefData).length) {
                    params = { ...clubPrefData }
                    if ('phones' in params.clubNotificationPreferences) {
                        phones = params.clubNotificationPreferences.phones
                        const phoneList = `${phones.map((phone, index) => (`
                            <tr
                                id="phone-remove-${index}"
                                data-mobile-phone=${phone.replaceAll("+", "").replaceAll(" ", "-")}
                            >
                                <td class="phone-text">${phone}</td>
                                <td style="text-align: center;">
                                    <button
                                        type="button"
                                        class="btn btn-default remove-button"
                                        data-removephone="${phone.replaceAll("+", "").replaceAll(" ", "-")}"
                                        data-removephoneindentity="${index}"
                                        onRemove
                                    >
                                        ${t('Remove')}
                                    </button>
                                </td>
                            </tr>
                        `)).join('')}`;
                        $($(html).find('#mobile-numbers tbody')).append(phoneList);
                        addModalEvents();
                    }
                }
                $(html).find('#add-number-btn').prop('disabled', false);
            })
            .catch(err => {
                console.log('error in club preferences api' , err);
                $(html).find('#add-number-btn').prop('disabled', false);
            })
        }
    }).then( async (response) => {
        if (response.value) {
            let phoneNumbers = [];
            const mobileRows = $(table).find("tr .phone-text");
            mobileRows.each(function() {
                phoneNumbers.push($(this).text());
            });

            let includeClubPhone = $(html).find('#include-club-phone').prop('checked');
            await postClubPreferences(clubId, {
                ...params,
                clubNotificationPreferences: {
                    ...params.clubNotificationPreferences,
                    phones: phoneNumbers,
                    includeClubPhone
                }
            });

            const cutoffTime = parseInt($($(html).find('#club_cutoff_time')).val());
            const clubUpdatedCutoffTime = { cutoffTime, updateAll: false };
            const clubUpdateStatus = await patchClubCutoffTime(clubId, clubUpdatedCutoffTime);

            /** update the club information variable if success **/
            if (clubUpdateStatus) {
                clubInformation = { ...clubInformation, cutoffTime };
            }
        }
    });
}

const validateBookingDate = async (date) => {
    try {
        const response = await $.ajax({
            url: `/api/clubs/${selectedID}/open-on/${date}`,
            method: 'GET',
        });

        return response;
    } catch (e) {
        console.error(e);
        return { isOpen: false };
    }
}

/** get club preferences **/
const getClubPreferences = async (clubId) => {
    let url = `api/club/preferences/${clubId}`;
    const result = await uniApiConCall(url, 'GET');
    if (result && 'status' in result && result.status == 'success' && 'data' in result) {
      const { data } = result;
      return data;
    }

    return false;
}

/** post club preferences **/
const postClubPreferences = async (clubId, data) => {
    loadingOverlay("#free-trial-bookings-container");
    let url = `api/club/preferences/${clubId}`;
    const result = await uniApiConCall(url, 'POST', data);
    $('#free-trial-bookings-container').LoadingOverlay("hide");
    if (result && 'status' in result && result.status == 'success' && 'data' in result) {
      const { data } = result;
      return data;
    }

    return false;
}

const patchClubCutoffTime = async (clubId, data) => {
    loadingOverlay("#free-trial-bookings-container");
    let url = `api/clubs/clubpage/updateclub/${clubId}`;
    const result = await uniApiConCall(url, 'POST', data);
    $('#free-trial-bookings-container').LoadingOverlay("hide");

    if (result && 'status') {
      return true;
    }

    return false;
}