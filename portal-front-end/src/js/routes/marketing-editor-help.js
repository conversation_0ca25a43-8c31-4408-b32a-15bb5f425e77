/**
 * Editor Help Mode in Dynamic Design when you edit
 */
function editorHelpMode() {

    function getMarketingVariables(clubId, callback) {
        $.ajax({
            url: `/api/marketing/variables/${clubId}/list`,
            method: 'GET',
            success: function(response) {
                callback(response)
            },
            error: function(xhr, textStatus, errorThrown){
                console.log(xhr, textStatus, errorThrown);
                window.location.replace("/401.html");
            }
        })
    }

    getMarketingVariables(selectedID, response => {

        if(response) {
            let setupHTML = `
                <div class="editor-help">
                    <h3>${t('Templating Guide')}</h3>

                    <ul class="nav nav-tabs tab-nav-right" role="tablist">
                        <li role="presentation" class="active"><a href="#variables_help" data-toggle="tab" aria-expanded="true">${t('SUBSTITUTED VARIABLES')}</a></li>
                        <li role="presentation"><a href="#elements_help" data-toggle="tab" aria-expanded="false">${t('DYNAMIC ELEMENTS')}</a></li>
                    </ul>

                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane fade active in" id="variables_help">
                            <div class="help-container">
                                <div><h5 class="m-b-10">${t('Variables are automatically substituted with the relevant details of the selected Club/Venue making your designs \'Dynamic\'')}</h5></div>
                                <h5>${t('How to add')}:</h5>

                                <pre>{{ClubBrandShort}}</pre>
                                <p>${t('You may add variables directly into your HTML code or CSS (as per above example) Variables that are not automatically substituted will appear as a "text input" for users when going to render/download the design. A full list of supported variables that are dynamically generated include')}:</p>

                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>${t('Variable')}</th>
                                            <th>${t('Expected Value')} <i>(${t('based on')}: ${selectedID})<i></th>
                                        </tr>
                                    </thead>
                                    <tbody>`;

                                        $.each(response.Values, function(key, value) {
                                            const preKeys = ['ClubHours', 'UpcomingHolidays', 'ExtendedHours'];
                                            const valueText = preKeys.includes(value.variable) ? `<pre>${value.value}</pre>` : value.value;
                                            setupHTML += `
                                                <tr>
                                                    <th scope="row"><code>{{${value.variable}}}</code></th>
                                                    <td>${valueText}</td>
                                                </tr>
                                            `;

                                            if (['ClubHours', 'ExtendedHours'].includes(value.variable)) {
                                                ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'].forEach(day => {
                                                    const variableName = `${value.variable}_${day}`;
                                                    const subValue = replaceTextVariables(`{{${variableName}}}`, response.Values);
                                                    setupHTML += `
                                                        <tr>
                                                            <th scope="row"><code>{{${variableName}}}</code></th>
                                                            <td>${subValue}</td>
                                                        </tr>
                                                    `;
                                                });
                                            }
                                        });
                            
            setupHTML += `          </tbody>
                                </table>
                            </div>
                        </div>

                        <div role="tabpanel" class="tab-pane fade" id="elements_help">

                            <div class="m-b-10 text-left">
                                <h5>
                                    ${t('Dynamic Elements are elements in your design that can be interacted with by the end user before rendering/downloading a design. <br>Note should you have suggestions for additional Dynamic Elements which are required in your Design, please lodge a Feature Request with HQ.')}
                                </h5>
                            </div>

                            <hr>

                            <div class="help-container">
                                <h4>${t('Single Line User Inputs')}:</h4>

                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li>${t('User input allows for \'free text\' that users may input when rendering a design')}</li>
                                            <li>${t('Each input field represents a single \'input\' in the design')}</li>
                                            <li>${t('Inputs will be substituted anywhere in the design - including HTML elements, image URL\'s etc')}</li>
                                            <li>${t('Inputs can be configured to be \'Required\' in the design settings dialog, forcing the user to complete all inputs before rendering a design')}</li>
                                            <li>${t('A character count limit may also be set (by default it\'s unset) in the design settings dialog if required')}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <b>${t('Example code for adding User Inputs to your design HTML')}:</b>
<pre>
{{FIRST_CONTENT}}
{{SECOND_CONTENT}}
{{THIRD_CONTENT}}
</pre>
                                        <b>${t('Result when Rendering Design')}:</b><br>
                                        <img src="/assets/images/designer/user-text-input.png" alt="" />
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="help-container">
                                <h4>${t('Multi-Line User Inputs (Textarea)')}:</h4>

                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li>${t('Textarea inputs allow for multi-line free text input by users in a basic WYSIWYG (What you see is what you get) editor when rendering a design.')}</li>
                                            <li>${t('Each input field represents a single \'input\' in the design')}</li>
                                            <li>${t('Only basic formatting (Bold, Italic, Underline, Bullet Points/Lists, and Font Size) are supported - All other HTML including images will be stripped from the render.')}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <b>${t('Example code for adding one or more Textarea\'s to your design HTML')}:</b>
<pre>
{{TEXTAREA:Notice Alert Message}}
</pre>
                                        <b>${t('Result in Render Design UI')}:</b><br>
                                        <img src="/assets/images/designer/textarea-input.png" alt="" />
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="help-container">
                                <h4>${t('Toggles')}:</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li>${t('Toggles allow for content to be toggled on/off - aka Hidden/Shown')}</li>
                                            <li>${t('When a user renders a design, they will be presented with multiple \'toggle\' options that bay be selected before downloading the design.')}</li>
                                            <li>${t('Content that is wrapped inside of the <code>{{#t=Name}}{{#/}}</code> will be what\'s toggled on/off in your design.')}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <b>${t('Example code for adding Toggles to your design HTML')}:</b>
<pre>
{{#t=Free_Gloves}}&#x3C;h1&#x3E;Get Free Gloves when you Join {{ClubName}}&#x3C;/h1&#x3E;{{/#}}
{{#t=Free_Myzone}}&#x3C;h3&#x3E;Includes Free MZ3 Device&#x3C;/h3&#x3E;{{/#}}
</pre>
                                        <b>${t('Result in Render Design UI')}:</b><br>
                                        <img src="/assets/images/designer/toggle-design-elements.png" alt="" />
                                    </div>
                                </div>
                            </div>

                            <hr>
          
                            <div class="help-container">
                                <h4>${t('Selectable Pictures (Asset Library)')}:</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li>${t('You may add user-selectable pictures/references in your dynamic designs by uploading images in the ASSET MANAGER')}</li>
                                            <li>${t('Users will then be presented with an option to select one of multiple images for each <code>{{ASSET}}</code> reference.')}</li>
                                            <li>
                                                ${t('Assets can be coded/configured like')}: <code>{{ASSET:ASSET_NAME:TAGS:UUID}}</code>
                                                <ul>
                                                    <li>${t('ASSET_NAME: = Name of the you\'d like to filter by (from Asset Manager)')}</li>
                                                    <li>${t('TAGS: = Refers to the which \'grouping tags\' you would like the selectable pictures to be restricted by')}</li>
                                                    <li>${t('UUID: = Should be specified as the \'Default Asset\' if a selection is not made by the user (ie when automatically rendering design thumbnails etc)')}</li>
                                                </ul>
                                            </li>
                                            <li>${t('There are no limitations to the number of selectable images or times an asset may be referenced in your code.')}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <b>${t('Example code for adding items from the Asset Library to your design HTML')}:</b>
<pre>
{{ASSET:BackgroundImage:Pre-Sale:e23247c0-dfb6-11eb-ae68-b1bd9668d669}}
{{ASSET:Option with Space In Filename:Facebook & Instagram:0dd8d4f0-dfb9-11eb-b115-e9af61c5fe9a}}
</pre>
                                        <b>${t('Result in Render Design UI')}:</b><br>
                                        <img src="/assets/images/designer/select-background.png" alt="" />
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="help-container">
                                <h4>${t('QR Codes')}:</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li>${t('QR Codes may be dynamically generated which will redirect to any custom URL provided')}</li>
                                            <li>${t('The URL variable <b>must be encoded</b> using the \'urlencode()\' method, which can be performed using an online URL Encoder such as <a href="https://www.urlencoder.org/" target="_blank">https://www.urlencoder.org/</a>')}</li>
                                            <li>${t('Substituted Variables may be also referenced INSIDE of the URL if required, to generate unique URL\'s per club.')}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <b>${t('Example code for adding QR Codes to your design HTML')}:</b>
<pre>
[[QR:type=CustomURL&url=http%3A%2F%2F12r.co%2Fclub%2F%3F{{ClubID}}]]
</pre>
                                        <b>${t('Result when Rendering Design')}:</b><br>
                                        <img style="width: 150px;" src="/assets/images/designer/dynamic-qr.png" alt="" /> <br>
                                        <small>* ${t('An example \'Dynamically\' Generated QR which links off to a club page using our URL shortening service.')}</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="help-container">
                                <h4>${t('Taglines/Options')}:</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li>${t('You may use Taglines/Options to show user \'selectable\' content without allowing the user to edit/the raw content.')}</li>
                                            <li>${t('This is useful for A|B testing content, or providing a different variety of text options to select from when rendering.')}</li>
                                        </ul>
                                        <div class="alert alert-red">
                                            ${t('NOTE: Currently only one set of Taglines/Options are supported in a single design.')}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <b>${t('Example code for adding Taglines/Options to your design HTML')}:</b>
<pre style="max-height: 200px;">
{{#to=Option_1}}
&#x3C;div&#x3E;
  &#x3C;h1&#x3E;A 
    &#x3C;span class=&#x22;stroke&#x22;&#x3E;boxing&#x3C;/span&#x3E;
    &#x3C;br/&#x3E; workout 
    &#x3C;br/&#x3E;like no 
    &#x3C;br/&#x3E;other
  &#x3C;/h1&#x3E;
&#x3C;/div&#x3E;
{{/#}}
{{#to=Option_2}}
&#x3C;div&#x3E;
  &#x3C;h1&#x3E;
    &#x3C;span class=&#x22;stroke&#x22;&#x3E;Boxing &#x3C;br/&#x3E;&#x3C;/span&#x3E;with a 
    &#x3C;br/&#x3E;difference
  &#x3C;/h1&#x3E;
&#x3C;/div&#x3E;
{{/#}}
{{#to=Option_3}}
&#x3C;div&#x3E;
  &#x3C;h1&#x3E;Not your 
    &#x3C;br/&#x3E;average 
    &#x3C;br/&#x3E;
    &#x3C;span class=&#x22;stroke&#x22;&#x3E;boxing&#x3C;/span&#x3E; 
    &#x3C;br/&#x3E;gym
  &#x3C;/h1&#x3E;
&#x3C;/div&#x3E;
{{/#}}
</pre>
                                        <b>${t('Result in Render Design UI')}:</b><br>
                                        <img src="/assets/images/designer/taglines-options.png" alt="" />
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            `;
            const swalWithHeight = Swal.mixin({
                customClass: {
                    content: 'editor-help-dialog'
                }
            })
        
            swalWithHeight.fire({
                width: '80vw',
                showConfirmButton: false,
                showCloseButton: true,
                allowEscapeKey : true,
                allowOutsideClick: false,
                animation: false,
                html: setupHTML,
                onBeforeOpen: () => {
                    addClipboardCopyButtons('.help-container table');
                }
            })

        }
    })
}