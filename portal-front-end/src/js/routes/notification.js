const SwalSingleNotifWithCustomButtons = Swal.mixin({
  customClass: {
      confirmButton: 'btn btn-primary waves-effect',
      cancelButton: 'btn btn-default waves-effect'
  },
  buttonsStyling: false,
})

const loadNotification = (clubId, id) => {
  $(document).ready( function () {
    loadSingleNotificationPage(clubId);
  });

  const loadSingleNotificationPage = async (clubId) => {
    $("#notification-container").empty();
    loadingOverlay('#notification-container');

    await fetchNotification(renderNotification, clubId, id);
  }
}

const renderNotification = (data=null, clubId) => {
  if (data) {
    let title = data.name;
    let content = data.snippet;
    let category = 'category' in data ? data.category : '';
    if ('data' in data) {
      const { data: notificationData } = data;
      content = 'content' in notificationData ? notificationData.content : '';
      title = 'title' in notificationData ? notificationData.title : '';
    }

    const date = moment(new Date(Math.floor(data.created * 1000))).format("Do MMMM YYYY");
    $('#notification-container').empty().append(`
      <div class="card">
        <div class="row">
          <div class="col-md-12">
            <div class="header notices-header">
              <h2>${title}</h2>
              <div style="font-size: 12px;">${notificationSourceNames(category)}</div>
              <div style="font-size: 12px; margin-top: 15px;">Posted on: ${date}</div>
            </div>
          </div>
        </div>
        <div class="body notices-containers" style="min-height: calc(100vh - 230px);">
          <p>${content}</p>
        </div>
      </div>
    `);
  } else {
    $('#notification-container').empty().append(`
      <div class="row">
        <div class="col-sm-12">
          <center>
            <h4>The message cannot be found.</h4>
          </center>
        </div>
      </div>
    `);
  }
}

const fetchNotification = async (cb, clubId, id) => {
  let url = `/api/club/notification/messages/${clubId}/${id}`;
  const result = await uniApiConCall(url);
  $('#notification-container').LoadingOverlay("hide");
  if (result && 'status' in result && result.status == 'success') {
    const { data } = result
    cb(data, clubId);
  }
}