/**
 * Get design by url from database
 * 
 * @param url
 * @returns design object
 * @method GET
 */
 function getDesignById(url, callback) {
    $.ajax({
        url,
        method: 'GET',
        success: function(response) {
            callback(response)
        },
        error: function(error) {
            console.log(error)
            console.log('Unable to get design')
        }
    });
}

/**
 * Save design to database
 * 
 * @param payload
 * @returns callback response
 * @method PUT
 */
function saveDesignToDB(url, payload, callback) {
    
    $.ajax({
        url,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(payload),
        success: function(data){
            callback(data)
        },
        error: function(xhr, textStatus, errorThrown){
            console.error(xhr, textStatus, errorThrown);

            instantNotification(
                // Message...
                t('<b>FAILED TO SAVE!</b><br><br>An error occurred while trying to save your design changes...<br>Please contact HQ for further details.'),
                // Type...
                'error',
                // Timeout (1m)...
                60000
            );
            callback(false)
        }
    })
}


/**
 * Set the design field to deleted
 * 
 * @param saveUrl - url of the save design api
 * @param getUrl - url of the get design api
 */
 function deleteDynamicOrStaticDesign(saveUrl, getUrl, callback) {

    getDesignById(getUrl, function(response) {

        let storedDesign = {}
        storedDesign = response

        if(response) {

            // We can't fire a swal dialog over an already visible swal - which is why we're using a manual bootstrap dialog in this situation...

            $('#confirm-delete-design').on('show.bs.modal', function(e) {
                $(this).find('.btn-confirm-delete-design').on('click', function (event) {
                    deleteDesign();
                    $('#confirm-delete-design').modal('hide');
                });
            });

            $('#confirm-delete-design').modal({
                backdrop: false
            })

            // Grey out the contents when the delete model hides/shows...
            $('.tab-pane').css('opacity', '0.5');
            $('#confirm-delete-design').on('hide.bs.modal', function(e) { $('.tab-pane').css('opacity', '1') });

            function deleteDesign() {

                storedDesign = { ...storedDesign, deleted: 1 }
                saveDesignToDB(saveUrl, storedDesign, function(response) {

                    if(response) {

                        const targetHTML = $(`.design-list-container .design-link[data-id='${response.id}']`)
                        targetHTML.remove()

                        // Refresh the current designs view...
                        loadDesignsList(currentFilteredDesignTag);

                        instantNotification(
                            // Message...
                            t('Design marked as deleted.'),
                            // Type...
                            'success',
                            // Timeout (30sec)...
                            30000
                        );
                    }
                })
            }

        }
    })
 }
