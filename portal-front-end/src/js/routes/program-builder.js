function sProgramBuilder() {

    // Make the side nav small on this page...
    // if(smallSidebar == false) toggleSmallNav();

    loadingOverlay('.iframe-placeholder')

    $('.iframe-placeholder iframe').ready(function() {
        setTimeout(function(){
            $(".iframe-placeholder iframe").show();
            $('.iframe-placeholder').LoadingOverlay("hide");
        }, 800);
    });

    $('.program-builder-popout button').on('click', function () {
        window.open($('#wbContainer').contents().get(0).location.href,'wbProgramBuilder',`height=${screen.availHeight},width=${screen.availWidth},resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=yes,directories=no,status=no`);
    });
}

function sWBURI() {
    sammy.quietRoute("/program-builder-ui/#" + encodeURIComponent($('#wbContainer').contents().get(0).location.href.replace(document.location.origin, '')));
}