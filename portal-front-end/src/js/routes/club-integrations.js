// At the top of the file, outside any function
$.sExternalIntegrations = {};

$.sExternalIntegrations.base = {
    membershipsData: [],
    hasMemberDataChange: false,
    initialPopulationComplete: false,
    membershipsFilter: {
      paying: 'all',
      status: 'all',
      accessType: 'all',
      membership: 'all',
    },
    init: function() {

        this.destroy();

        $('.pinned-footer.form-save .btn-cancel').click(function(event) {
            event.preventDefault();
            $('.pinned-footer.form-save').removeClass('active');
        });

        // Bind to 'save' button in application...
        $("form").submit(function(event) {

            // $(".save-overlay").show();
            $('.club-details, .club-integrations-container').addClass('isLoading');

            saveIntegrationData(function(response) {
                // $(".save-overlay").hide();
                $('.club-details, .club-integrations-container').removeClass('isLoading');

                if(response == true) {
                    instantNotification(
                        // Message...
                        `${t('Integrations for the Facility:')} [ ${selectedID} ] ${t('successfully saved.')}`,
                        // Type...
                        'success',
                        // Timeout (k10sec)...
                        10000
                    );
                    // reflect the updates to display after saving
                    $.sExternalIntegrations.base.createMembershipsContent($.sExternalIntegrations.base.membershipsData, $('#club-memberships__items'));
                } else {
                    instantNotification(
                        // Message...
                        `${t('An error occurred while trying to save Integration Changes for the Facility:')} [ ${selectedID} ]. ${t('Please contact HQ to further investigate this issue.')}`,
                        // Type...
                        'error',
                        // Timeout (1m)...
                        60000
                    );
                }
            })

            event.preventDefault();
        });

        // Prevent 'enter' from submitting the form (users must click save button)...
        $(document).ready(function() {
          $(window).keydown(function(event){
            if(event.keyCode == 13) {
              event.preventDefault();
              return false;
            }
          });
        });

        const maskConfig = {
            mask: '9{1,4}',
            greedy: false,
            definitions: { '*': { validator: '[0-9]' } },
        };
        $('#back2BaseAlarmID').inputmask(maskConfig);

        $.sExternalIntegrations.base.renderNotificationEmails();
        renderEALogic();
        $('#mmsSystemType').selectpicker();

        $('#gymMasterDomain').on('paste', (e) => {
            e.preventDefault();
            const value = e.originalEvent.clipboardData.getData('text');
            const domain = stripOutGymMasterDomain(value);
            $(e.currentTarget).val(domain);
        });

        // Listen for changes in input fields, excluding .club-integrations__search-field
        $('#club-integrations').on('change input', 'input:not(.club-integrations__search-field), select, textarea', function() {
            if ($.sExternalIntegrations.base.initialPopulationComplete) {
                $.sExternalIntegrations.base.hasMemberDataChange = true;
                $('.pinned-footer.form-save').addClass('active');
            }
        });

        // Listen for tab changes
        $('.nav-tabs a').on('show.bs.tab', function(e) {
            if ($.sExternalIntegrations.base.hasMemberDataChange) {
                e.preventDefault();
                swalWithBootstrapButtons.fire({
                    title: t('Unsaved Changes'),
                    html: `<div class="ph-dialog-v2"><div class="ph-dialog-v2__content">${t('You have unsaved changes. Do you want to save them before switching tabs?')}</div></div>`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: t('Save Changes'),
                    cancelButtonText: t('Discard Changes'),
                }).then((result) => {
                    
                    if(result.value) {
                      $('form').submit();
                      $.sExternalIntegrations.base.hasMemberDataChange = false;
                      $(e.target).tab('show');
                    }
                    
                    if(result?.dismiss) {
                        // Discard changes
                        discardChanges();
                        $.sExternalIntegrations.base.hasMemberDataChange = false;
                        $('.pinned-footer.form-save').removeClass('active');
                        // Proceed with tab change
                        $(e.target).tab('show');
                    }
                    
                });
            }
        });

        // Sample Lead Forwarding Email...
        $(".viewSampleLeadForwardingEmail").click(function(e) {
            let sampleContact = generateRandomNameEmailAndPhone(defaultLanguage)

            swalWithBootstrapButtons.fire({
                title: t('Example Free Trial Booking Email'),
                html: `
                    <div class="ph-dialog-v2" style="font-family: Arial, sans-serif; text-align: left; margin: 20px;">
                        <div>
                            <p style="color: #555555; font-size: 14px;">${t('This is an example of a Free Trial Booking email that is sent via plain text to your configured Lead Forwarding addresses.')}</p>
                            <div style="padding: 15px; background-color: #F7F9FA; border: 1px solid #E5E7EB; border-radius: 5px;">
                                <code style="white-space: pre-wrap; display: block; font-size: 13px; color: #2D3748; background-color: white;">
Club: ${selectedID}
Email: ${sampleContact.email}
First Name: ${sampleContact.firstName}
Last Name: ${sampleContact.lastName}
Phone: ${sampleContact.phone}
Acceptterms: true
Appointment Time: 2024-08-07T17:15:00+10:00
Heard About Method: ubxtraining.com website
                                </code>
                            </div>
                            <p style="margin-top: 20px; font-size: 13px; color: #A0AEC0;">
                                ${t('PLEASE NOTE: This email is sent in plain text format.')}
                            </p>
                        </div>
                    </div>`,
                icon: 'info',
                confirmButtonText: t('Close')
            });
        })

        $(".toggle-password").click(function () {
            let input = $("#" + $(this).data("target"));
            let icon = $(this);

            if (input.attr("type") === "password") {
                input.attr("type", "text");
                icon.removeClass("fa-eye-slash").addClass("fa-eye");
            } else {
                input.attr("type", "password");
                icon.removeClass("fa-eye").addClass("fa-eye-slash");
            }
        });

        $('.b-test-unifi').click(function(event) {
            if ($('.pinned-footer.form-save').hasClass('active')) {
                event.preventDefault();
                swalWithBootstrapButtons.fire({
                    title: t('Pending Changes Detected'),
                    html: `<div class="ph-dialog-v2"><div class="ph-dialog-v2__content">
                        ${t('You have unsaved changes. If you made any updates to the Unifi Controller, please save them before testing. Would you like to save your changes now?')}
                    </div></div>`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: t('Save & Test'),
                    cancelButtonText: t('Cancel'),
                }).then((result) => {
                    if (result.value) {
                        // Save changes and then test Unifi Controller
                        $('form').submit();
                        $.sExternalIntegrations.base.hasMemberDataChange = false;
                        $('.pinned-footer.form-save').removeClass('active');

                        // Slight delay to ensure save completes before testing
                        $.sExternalIntegrations.base.testUnifiController(3000);
                    }

                    if (result.dismiss) {
                        // Proceed with testing Unifi Controller without saving
                    }
                });
            } else {
                // If no unsaved changes, proceed with testing immediately
                $.sExternalIntegrations.base.testUnifiController();
            }
        });

        $('.b-open-router').click(async function(event) {

            const $btn = $(this);
            const $icon = $(".router-status-icon");

            // Replace the icon with a spinner
            $icon.html(`
                <img src="/assets/images/tail-spin.svg" alt="Loading..."
                     style="margin-right: 5px; width: 16px; height: 16px; drop-shadow(0px 0px 2px black);">
            `);

            // Disable button
            $btn.prop("disabled", true).css("pointer-events", "none").attr("aria-disabled", "true");


            toastSpinnerModal(
                t('Connecting to router...'),
                {
                    timer: 15000
                }
            );

            try {
                const response = await $.ajax({
                    url: `/api/devices/openRouterWebpage/${selectedID}`,
                    method: 'GET',
                });

                if (response && response.data && response.data.routerTunnelDetails && response.data.routerTunnelDetails.success) {
                    const urls = response.data.routerTunnelDetails.urls;

                    if (Array.isArray(urls) && urls.length > 0) {
                        // Open the first available tunnel URL in a new tab
                        window.open(urls[0], "_blank");
                    } else {
                        throw new Error("No URLs returned from the tunnel response.");
                    }
                } else {
                    throw new Error("Invalid response structure or tunnel connection failed.");
                }
            } catch (error) {
                console.error("Error establishing router tunnel:", error);

                // Hide spinner modal and show error message
                genericDialog(
                    t('Error'),
                    t('We could not establish a tunnel to the remote router at this time.')
                );
            } finally {
                // Restore the original icon
                $icon.html('');

                // Re-enable the button                
                $btn.prop("disabled", false).css("pointer-events", "auto").attr("aria-disabled", "false");

            }

        });

        // ------------------------------------

        function addMembershipsFilters(arr, options, filter) {
          if (!arr || !options || !filter) return;
          options.forEach((option) => {
              arr.push({
                  label: option,
                  value: option,
                  selected: $.sExternalIntegrations.base.membershipsFilter[filter] === option,
              });
          });
        }


        function saveIntegrationData(callback) {
            const gymMasterDomainValue = $("#gymMasterDomain").val().trim();
            const gymMasterDomainStripped = stripOutGymMasterDomain(gymMasterDomainValue);

            $("#gymMasterDomain").val(gymMasterDomainStripped);
            let integrationSettings = {
                "notificationEmails": JSON.parse($("#notificationEmails").val()),
                "gymMasterDomain": gymMasterDomainStripped,
                "gymMasterAPI": $("#gymMasterAPI").val().trim(),
                "myzoneAPI": $("#myzoneAPI").val().trim(),
                "stripeConnectedAccountID": $("#stripeConnectedAccountID").val().trim(),
                "back2BaseAlarmID": $("#back2BaseAlarmID").val().trim(),
                "eaAlarmType": $("#eaAlarmIntegrationType").selectpicker('val'),
                "eaAlarmProvider": $("#eaAlarmIntegrationProvider").selectpicker('val'),
                "w4gSiteId": $("#w4gSiteId").val().trim(),
                "eaRelayModel": $("#eaRelayDeviceType").selectpicker('val'),
                "memberships": [],
                "unifiUsername": $("#unifiUsername").val().trim(),
                "unifiPassword": $("#unifiPassword").val().trim(),
                "unifiControllerIP": $("#unifiControllerIP").val().trim(),
            }

            // Collect only modified membership data
            let foundIndex = -1;
            $('.club-memberships__item').each(function() {
                const $item = $(this);
                const membershipId = $item.data('membership-id');
                const currentAccess = $item.data('original-access');
                const newAccess = $item.find('select[name="access-options"]').val();

                if (currentAccess !== newAccess) {
                    const membership = $.sExternalIntegrations.base.membershipsData.find((membership, index) => {
                      const result = membership.id === membershipId;
                      if(result) {
                        foundIndex = index;
                      }
                      return result;
                    });

                    if(foundIndex !== -1) {
                      $.sExternalIntegrations.base.membershipsData[foundIndex].accessType = newAccess;
                    }

                    if(membership) {
                      integrationSettings.memberships.push({
                        ...membership,
                        accessType: newAccess,
                        isManuallyUpdated: true,
                      });
                    }
                }

                console.log({ integrationSettingsMemberships: integrationSettings.memberships })
            });

            // If no memberships were modified, remove the memberships property
            if (integrationSettings.memberships.length === 0) {
                delete integrationSettings.memberships;
            }

            var req = $.ajax({
                url: `/api/clubs/integrations/update/${selectedID}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(integrationSettings),
                success: function(data){
                    if (data.membershipErrors && data.membershipErrors.length > 0) {
                        // Handle membership update errors
                        const errorMessages = data.membershipErrors.map(error => 
                            `Membership ${error.membershipId}: ${error.error}`
                        ).join('\n');
                        instantNotification(
                            `${t('Facility updated, but some membership updates failed:')} \n${errorMessages}`,
                            'warning',
                            10000
                        );
                    } else {
                        // Update data-original attributes with new values
                        Object.keys(integrationSettings).forEach(key => {
                            const $el = $(`#${key}`);
                            if ($el.length) {
                                $el.attr('data-original', integrationSettings[key]);
                            }
                        });
                        $('.club-memberships__item').each(function() {
                            const $item = $(this);
                            const newAccess = $item.find('select[name="access-options"]').val();
                            $item.data('original-access', newAccess);
                            if(foundIndex !== -1) {
                              $.sExternalIntegrations.base.membershipsData[foundIndex].isManuallyUpdated = true;
                            }
                        });
                    }
                    callback(data);
                },
                error: function(xhr, textStatus, errorThrown){
                    console.log(xhr, textStatus, errorThrown);
                    window.location.replace("/401.html");
                }
            });

            // Reset hasMemberDataChange flag and hide footer after saving
            $.sExternalIntegrations.base.hasMemberDataChange = false;
            $('.pinned-footer.form-save').removeClass('active');
        }

        function discardChanges() {
            $('input, select, textarea').each(function() {
                const $el = $(this);
                const originalValue = $el.attr('data-original');
                if (originalValue !== undefined) {
                    if ($el.is('select')) {
                        $el.selectpicker('val', originalValue).trigger('change');
                    } else {
                        $el.val(originalValue);
                    }
                }
            });
            $.sExternalIntegrations.base.renderNotificationEmails();
            $('.club-memberships__item').each(function() {
                const $item = $(this);
                const originalAccess = $item.data('original-access');
                $item.find('select[name="access-options"]').selectpicker('val', originalAccess);
            });
        }

        function renderEALogic() {
            $('#eaAlarmIntegrationType, #eaRelayDeviceType', '#eaAlarmIntegrationProvider').selectpicker();

            function handleContainerVisibility(changeProvider) {
                const providerEl = $('#eaAlarmIntegrationProvider');
                const alarmType = $('#eaAlarmIntegrationType').val();
                let provider = providerEl.val();

                $('#eaNoneContainer, #eaCidContainer, #eaW4GConfigContainer, #eaRelayContainer').hide(); // Hide all initially
                changeProvider && providerEl.val('none').attr('disabled', true).selectpicker('refresh');

                if (alarmType === 'none') {
                    $('#eaNoneContainer').show();
                } else if (alarmType === 'cid' && provider === 'none') {
                    $('#eaNoneContainer').show();
                    changeProvider && providerEl.val('none').attr('disabled', false).selectpicker('refresh');
                } else if (alarmType === 'cid' && provider === 'b2b') {
                    $('#eaCidContainer').show();
                    changeProvider && providerEl.val('b2b').attr('disabled', false).selectpicker('refresh');
                } else if (alarmType === 'cid' && provider === 'w4g') {
                    $('#eaW4GConfigContainer').show();
                    changeProvider && providerEl.val('w4g').attr('disabled', false).selectpicker('refresh');
                } else if (alarmType === 'relay') {
                    $('#eaRelayContainer').show();
                }
            }

            // changes the provider based on the selected alarm type
            $('#eaAlarmIntegrationType, #eaRelayDeviceType')
                .on('change', () => handleContainerVisibility(true));

            // prevent changing provider if manually changed
            $('#eaAlarmIntegrationProvider')
                .on('change', () => handleContainerVisibility(false));

            handleContainerVisibility(true); // Initial call
        }

        function stripOutGymMasterDomain(urlString) {
            try {
                const url = new URL(urlString);
                if (url?.hostname) return url.hostname;
            } catch (error) {
                console.error(error);
            }
            return urlString;
        }

    },
    testUnifiController: async function (delay = 1) {

        const $statusIcon = $('.unifi-status-icon');
        const $statusMessage = $('#unifi-status-message');

        // Show loading spinner
        $statusIcon.html(`
            <img src="/assets/images/tail-spin.svg" alt="Loading..."
                 style="margin-right: 5px; width: 16px; height: 16px; filter: invert(1); drop-shadow(0px 0px 2px white);">
        `);

        $('.b-test-unifi').prop("disabled", true).css("pointer-events", "none").attr("aria-disabled", "true");

        $statusMessage.html('');

        await new Promise(resolve => setTimeout(resolve, delay));

        try {
            const response = await $.ajax({
                url: `/api/devices/testUnifiController/${selectedID}`,
                method: 'GET',
            });

            $statusIcon.html('');
            $('.b-test-unifi').prop("disabled", false).css("pointer-events", "auto").attr("aria-disabled", "false");

            if (!response.data || response.data === false) {
                $statusMessage.html(`
                    <div class="alert alert-red">
                        <i class="fas fa-exclamation-circle"></i> ${t('We were not able to log in to the Unifi Controller.')}
                        <br>
                        ${t('Please check the')} <strong>${t('username, password, and IP address')}</strong>${t(', then try again.')}
                    </div>
                `);
                return;
            }

            // Success: Show Unifi Controller details
            const controllerData = response.data[0] || {};
            const { hostname, ubnt_device_type, uptime, ip_addrs, version, update_available } = controllerData;

            // Convert uptime (seconds) to readable format
            function formatUptime(seconds) {
                if (!seconds || isNaN(seconds)) return t('Unknown');
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return `${days}d ${hours}h ${minutes}m`;
            }

            $statusMessage.html(`
                <div class="alert alert-blue">
                    <i class="fas fa-check-circle"></i> ${t('Successfully connected to the Unifi Controller!')}
                    <br>
                    <strong>${t('Controller Name')}:</strong> ${hostname || t('Unknown')}<br>
                    <strong>${t('Device Type')}:</strong> ${ubnt_device_type || t('Unknown')}<br>
                    <strong>${t('Version')}:</strong> ${version || t('Unknown')}<br>
                    <strong>${t('Update Available')}:</strong> ${update_available ? t('Yes') : t('No')}<br>
                    <strong>${t('Uptime')}:</strong> ${formatUptime(uptime)}
                    <br><br>
                    ${t('The Performance Hub will automatically health-check smart facility devices connected to the Unifi Controller.')}
                </div>
            `);
        } catch (error) {
            console.error('Error testing Unifi Controller:', error);

            $statusIcon.html('');
            $('.b-test-unifi').prop("disabled", false).css("pointer-events", "auto").attr("aria-disabled", "false");

            if (error.status === 408) {
                $statusMessage.html(`
                    <div class="alert alert-yellow">
                        <i class="fas fa-exclamation-triangle" style="color: #856404 !important;"></i> ${t('All requests to communicate with the Unifi Controller')} <strong>${t('timed out')}</strong>.
                        <br>
                        ${t('This could mean that the')} <strong>${t('IP address is incorrect')}</strong> ${t('or that the Unifi Controller is not on the same network as your Smart Facility devices.')}
                        <br><br>
                        <strong>${t('Note')}:</strong> ${t('There must be at least')} <strong>${t('one')}</strong> ${t('ethernet-connected Smart Facility device (screen or camera) online and running for this test to work.')}
                    </div>
                `);
            } else {
                $statusMessage.html(`
                    <div class="alert alert-red">
                        <i class="fas fa-times-circle"></i> An unexpected error occurred: ${error.responseText || 'Unknown error'}
                    </div>
                `);
            }
        }
    },
    renderNotificationEmails: function() {
        var tmp = $("#notificationEmails").val();

        $(".notificationEmails-placeholder").empty();
        $(".notificationEmails-placeholder").html('<input type="text" id="notificationEmails" class="form-control date" placeholder="">');
        $("#notificationEmails").val(tmp);

        $("#notificationEmails").multiple_emails({
           position: 'top', // Display the added emails above the input
           theme: 'bootstrap', // Bootstrap is the default theme
           checkDupEmail: true // Should check for duplicate emails added
        });

        $('.notificationEmails-placeholder .multiple_emails-email').addClass('use-skeleton rounded isBlock');
    },
    createMembershipsContent: async function(memberships, wrapper) {
      if(!wrapper || !memberships || !memberships.length) return;

      async function fetchAccessTypes() {
          try {
              const response = await $.ajax({
                  url: '/api/admin/clubs/membershipaccesstypes?organisationId=' + selectedOrgID,
                  method: 'GET',
              });
              return response;
          } catch (error) {
              console.error('Error fetching access types:', error);
              return [];
          }
      }

      // Fetch access types
      const accessTypes = await fetchAccessTypes();

      // empty the wrapper before appending new content
      wrapper.empty();

      memberships?.forEach((membership = {}) => {
        const { 
          accessType = '', 
          name = '', 
          price = '', 
          description = 'No description available', 
          pricedescription, 
          onlinecash, 
          archived, 
          isManuallyUpdated 
        } = membership || {}; // Additional fallback for destructuring safety
        
        const showMoreSection = description?.length > 80 ? `
          <div class="club-memberships__item__more"><hr><a href="#" class="use-skeleton show-full-description">${t('Show more')}</a></div>
        ` : '';

        const membershipContent = `
          <div class="club-memberships__item" data-membership-id="${membership?.id || ''}" data-original-access="${accessType}">
            <div class="club-memberships__item__title-section">
              <p class="club-memberships__item__title use-skeleton">${name}</p>
              <p class="club-memberships__item__price use-skeleton">${price}</p>
            </div>
            <div class="club-memberships__item__tags">
            ${accessType?.toLowerCase()?.includes('extended access') ? `
              <span class="club-memberships__item__tag club-memberships__item__tag--ea btn__badge btn__badge--semi btn__badge--sm btn__badge--success use-skeleton">EA </span>
            ` : ''}
            ${pricedescription ? `
              <span class="club-memberships__item__tag btn__badge btn__badge--semi btn__badge--sm btn__badge--stroked use-skeleton">${pricedescription} </span>
            ` : ''}
            ${onlinecash ? `
              <span class="club-memberships__item__tag club-memberships__item__tag--success btn__badge btn__badge--semi btn__badge--highlight use-skeleton">Sell Online </span>
            ` : ''}
            ${archived ? `
              <span class="club-memberships__item__tag club-memberships__item__tag--archived btn__badge btn__badge--semi btn__badge--sm use-skeleton">Archived</span>
            ` : ''}
            </div>
            <div class="club-memberships__item__content">
              <p class="use-skeleton">${description}</p>
            </div>
            
            ${showMoreSection}
            <div class="club-memberships__item__dropdown">
              ${!isManuallyUpdated ? `
                <i 
                    class="the-info-box__info"
                    data-toggle="tooltip"
                    data-placement="right"
                    title="${t('Automatically set via AI')}"
                >
                    <span class="use-skeleton use-skeleton--absolute rounded--full"></span>
                </i>  
              ` : ``}

              <div class="form-group use-skeleton rounded">
                <div class="form-line use-skeleton">
                  <select name="access-options" class="form-control" required="" data-access="${accessType}">
                    ${(accessTypes || []).map(type => `
                      <option value="${type.id}" ${accessType === type.id ? 'selected' : ''}>${t(type.name)}</option>
                    `).join('')}
                  </select>
                </div>
              </div>
            </div>
          </div>
        `;
        wrapper.append(membershipContent);
      });

      // enable tooltip
      $('[data-toggle="tooltip"]').tooltip();

       // activate select
       $('select[name="access-options"]').selectpicker();

       wrapper.on('click', '.show-full-description', function(e) {
         e.preventDefault();
         const descEl = $(this).closest('.club-memberships__item').find('.club-memberships__item__content p');
         descEl.toggleClass('show-full-description--active');
         $('.club-memberships__item__content p').not(descEl).removeClass('show-full-description--active');
         
         // Update button text
         $('.show-full-description').text(t('Show more'));
         const buttonText = descEl.hasClass('show-full-description--active') ? t('Show less') : t('Show more');
         $(this).text(buttonText);

         $('#club-memberships__items').removeClass('club-memberships--start');
         if($('#club-memberships__items').find('.show-full-description--active').length > 0) {
           $('#club-memberships__items').addClass('club-memberships--start')
         }
       });
    },
    loadExternalIntegrationsData:  async function(clubID, callback) {

      $(".club-details, .club-integrations-container").addClass('isLoading');
      $('#club-memberships__preloader').show();
      $('#memberships-filter').show().addClass('isLoading');
      $('#club-memberships__items').hide().addClass('isLoading');
      $('#club-memberships__items').empty();

        var req = $.ajax({
            url: `/api/clubs/integrations/list/${clubID}`,
            method: 'GET',
            success: async function(data){

                console.log(data);

                if(data) {

                    // CRM Notification Emails Array & GM Config
                    updateFieldWithOriginal("#notificationEmails", JSON.stringify(data.notificationEmails));
                    updateFieldWithOriginal("#gymMasterDomain", data.gymMasterDomain);
                    updateFieldWithOriginal("#gymMasterAPI", data.gymMasterAPI);
                    updateFieldWithOriginal("#myzoneAPI", data.myzoneAPI);
                    updateFieldWithOriginal("#stripeConnectedAccountID", data.stripeConnectedAccountID);

                    // Smart Facility...
                    updateFieldWithOriginal("#back2BaseAlarmID", data.back2BaseAlarmID);
                    updateSelectWithOriginal("#eaAlarmIntegrationType", data.eaAlarmType);
                    updateSelectWithOriginal("#eaRelayDeviceType", data.eaRelayModel);
                    updateSelectWithOriginal("#eaAlarmIntegrationProvider", data.eaAlarmProvider);
                    updateFieldWithOriginal("#w4gSiteId", data.w4gSiteId);

                    updateFieldWithOriginal("#unifiUsername", data.unifiUsername);
                    updateFieldWithOriginal("#unifiPassword", data.unifiPassword);
                    updateFieldWithOriginal("#unifiControllerIP", data.unifiControllerIP);

                    // display memberships content
                    if(data.memberships && data.memberships.length) {
                      $.sExternalIntegrations.base.membershipsData = data.memberships;
                      await $.sExternalIntegrations.base.createMembershipsContent($.sExternalIntegrations.base.membershipsData, $('#club-memberships__items'));
                      generateMembershipsFilter($.sExternalIntegrations.base.membershipsData);
                    } else {
                      $('#memberships-filter').hide();
                    }
                    $('#club-memberships__preloader').hide();
                    $('#club-memberships__items').show().removeClass('isLoading');

                    // When it loads - if a username/password is set - run the "test unifi conroller"
                    if(data.unifiUsername && data.unifiPassword) {
                        $.sExternalIntegrations.base.testUnifiController();
                    } else {
                        $('#unifi-status-message').html('');
                    }
                }

                $(".club-details, .club-integrations-container").removeClass('isLoading');
                $('#memberships-filter').removeClass('isLoading');
                $.sExternalIntegrations.base.initialPopulationComplete = true;
            },
            error: function(xhr, textStatus, errorThrown){
                console.log(xhr, textStatus, errorThrown);
                window.location.replace("/401.html");
                $.sExternalIntegrations.base.initialPopulationComplete = true;
                $('#memberships-filter').hide();
            }
        });

        function updateFieldWithOriginal(selector, value) {
            const $el = $(selector);
            $el.val(value).attr('data-original', value);
            if (selector === "#notificationEmails") {
                $.sExternalIntegrations.base.renderNotificationEmails();
            }
        }

        function updateSelectWithOriginal(selector, value) {
            const $el = $(selector);
            $el.selectpicker('val', value).attr('data-original', value).trigger('change');
        }

        function filterMemberships(memberships) {
          // const searchTerm = $('.club-integrations__search-field').val().toLowerCase();

          const filteredMemberships = memberships.filter(membership => {
            const { name = '', pricedescription = '', onlinecash, archived, accessType } = membership;

            const payingMatch = $.sExternalIntegrations.base.membershipsFilter.paying === 'all' || pricedescription.toLowerCase() === $.sExternalIntegrations.base.membershipsFilter.paying;
            const statusMatch = $.sExternalIntegrations.base.membershipsFilter.status === 'all' || 
                                ($.sExternalIntegrations.base.membershipsFilter.status === 'sell online' && onlinecash) ||
                                ($.sExternalIntegrations.base.membershipsFilter.status === 'archived' && archived);
            const accessMatch = $.sExternalIntegrations.base.membershipsFilter.accessType === 'all' || 
                                ($.sExternalIntegrations.base.membershipsFilter.accessType === 'extended access' && accessType === 'extended access') ||
                                ($.sExternalIntegrations.base.membershipsFilter.accessType === 'regular access' && accessType !== 'extended access');
            // const searchMatch = searchTerm === '' || name.toLowerCase().includes(searchTerm);

            const membershipMatch = $.sExternalIntegrations.base.membershipsFilter.membership === 'all' || name.toLowerCase() === $.sExternalIntegrations.base.membershipsFilter.membership;

            return payingMatch && statusMatch && accessMatch && membershipMatch;
          });

          // Clear existing content
          $('#club-memberships__items').empty();

          // Render filtered memberships
          $.sExternalIntegrations.base.createMembershipsContent(filteredMemberships, $('#club-memberships__items'));
        }

        function generateMembershipsFilter(memberships) {

          if ($('#memberships-filter').length === 0) {
              setTimeout(() => {
                  generateMembershipsFilter(memberships);
              }, 1000);
              return;
          }

          const payingOptions = ['All'];
          const statusOptions = ['All', 'Sell Online', 'Archived'];
          const accessOptions = ['All', 'With Extended Access', 'Without Extended Access'];
          const membershipOptions = ['All'];

          memberships.forEach(membership => {
            const { accessType = '', pricedescription, onlinecash, archived, name } = membership;

            if (pricedescription && !payingOptions.includes(pricedescription)) {
              payingOptions.push(pricedescription);
            }

            if (onlinecash && !statusOptions.includes('Sell Online')) {
              statusOptions.push('Sell Online');
            }

            if (archived && !statusOptions.includes('Archived')) {
              statusOptions.push('Archived');
            }

            if (accessType === 'extended access' && !accessOptions.includes('With Extended Access')) {
              accessOptions.push('With Extended Access');
            } else if (accessType !== 'extended access' && !accessOptions.includes('Without Extended Access')) {
              accessOptions.push('Without Extended Access');
            }

            if (name && !membershipOptions.includes(name)) {
              membershipOptions.push(name);
            }
          });

          new PageFilters(
              '#memberships-filter',
              [
                  {
                      id: 'paying',
                      label: t('Paying'),
                      options: payingOptions.map(option => ({
                        label: t(option),
                        value: option.toLowerCase(),
                        selected: $.sExternalIntegrations.base.membershipsFilter['paying'] === option.toLowerCase(),
                      })),
                      optional: false,
                      visible: true,
                      closedValue: 'all',
                  },
                  {
                      id: 'status',
                      label: t('Status'),
                      options: statusOptions.map(option => ({
                        label: t(option),
                        value: option.toLowerCase(),
                        selected: $.sExternalIntegrations.base.membershipsFilter['status'] === option.toLowerCase(),
                      })),
                      optional: false,
                      visible: true,
                      closedValue: 'all',
                  },
                  {
                    id: 'accessType',
                    label: t('Access'),
                    options: accessOptions.map(option => ({
                      label: t(option),
                      value: option === 'With Extended Access' ? 'extended access' : 
                             option === 'Without Extended Access' ? 'regular access' : 'all',
                      selected: $.sExternalIntegrations.base.membershipsFilter['accessType'] === (option === 'With Extended Access' ? 'extended access' : 
                                option === 'Without Extended Access' ? 'regular access' : 'all'),
                    })),
                    optional: false,
                    visible: true,
                    closedValue: 'all',
                },
                {
                  id: 'membership',
                  label: t('Membership'),
                  options: membershipOptions.map(option => ({
                    label: option,
                    value: option.toLowerCase(),
                    selected: $.sExternalIntegrations.base.membershipsFilter['membership'] === option.toLowerCase(),
                  })),
                  optional: false,
                  visible: true,
                  closedValue: 'all',
                  searchBox: true,
                },
              ],
              (filters) => {
                  $.sExternalIntegrations.base.membershipsFilter = { ...$.sExternalIntegrations.base.membershipsFilter, ...filters };
                  filterMemberships(memberships);
              },
              true
          );

          $('#memberships-filter').show().removeClass('isLoading');

          // Add event listener for search field
          $('.club-integrations__search-field').on('input', function() {
              filterMemberships(memberships);
          });
        }

        // $(".preload-text").hide();
        callback(true);

    },
    destroy: function() {
        console.log('destroy bindings for sExternalIntegrations')
        // Unbind all events before re-initialization
        $('.b-test-unifi').off('click');
        $('.b-open-router').off('click');
        $('.toggle-password').off('click');
        $('.pinned-footer.form-save .btn-cancel').off('click');
        $("form").off('submit');
        $(window).off('keydown');
        $('#gymMasterDomain').off('paste');
        $('#club-integrations').off('change input', 'input:not(.club-integrations__search-field), select, textarea');
        $('.nav-tabs a').off('show.bs.tab');
        $(".viewSampleLeadForwardingEmail").off('click');
        
    }
}

