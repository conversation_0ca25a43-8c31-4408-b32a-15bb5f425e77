let defaultSelectedReviewTag = false, reviewTimelineGraph = null, reviewSourcesChart = null, reviewsTable = null;

const loadSocialMediaReviews = () => {

    let reviewsSettings = null;
    let clubVariables = {};

    // save reviews data
    const reviewsData = {}
    const saveReviewsData = (reviews) => {
        reviews.map(review => {
            // set the value needed for our input mapping
            reviewsData[review.id] = {
                reviewerClub: `${selectedClubObj.brand} ${selectedClubObj.name}`, // reviewerClub "SUB_HD"
                reviewerName: review.name, // reviewerName "H2"
                reviewTitle: t("Member Review"), // reviewTitle "H1"
                reviewerBodyContents: review.content, // reviewerBodyContents "BODY_COPY"
                reviewPublishState: review.publishState || {},
            }
        })
    } 

    // filter value from dashboard saved in localStorage
    const viewUnResponded = localStorage.getItem('noResponse') === 'true' 

    var dtSearch;
    // default filters value
    const tableFilters = {
        published:  true,
        unPublished: true,
        responded: true,
        unResponded: true,
    }

    // update filters value if no response config is true when initialized
    // viewUnResponded value will use value from localStorage
    const filterUnresponded = (viewUnResponded) => {
        if(viewUnResponded) {
            tableFilters.published =   false;
            tableFilters.unPublished = false;
            tableFilters.responded =  false;
            tableFilters.unResponded = true;
        }  
    }
    filterUnresponded(viewUnResponded)

    const loadData = async () => {
        // loadingOverlay('.load-on-init');
        toggleLoading($('.load-on-init'), true);
        $("#reviews-table-container").html('');
        $(".reviews-overview").show();
        $(".reviews-tags").show();
        $('#reviews-by-source-chart')[0].getContext('2d').reset();
        $('#reviews-by-date-graph')[0].getContext('2d').reset();

        // Fetch page data
        try {
            const [{ reviews, tags }, { settings }, variableRes] = await Promise.all([
                fetchClubReviews(selectedID),
                fetchClubReviewsSettings(selectedID),
                listClubVariablesAPI(selectedID),
            ]);

            if (!variableRes[0]) {
                throw new Error('Error fetching Facility Variables');
            }

            clubVariables = variableRes[1].Values;

            reviewsSettings = settings;

            $('#settingsJSON').val(JSON.stringify(settings));

            if(reviews.length == 0) {
                $(".reviews-overview").hide();
                $(".reviews-tags").hide();
                $("#reviews-table-container").html(`

                    <div class="no-reviews">
                        <div class="d-flex justify-center">
                            <div class="alert alert-blue" style="max-width:800px;" role="alert">
                                <div>
                                    <i class="fa fa-bell" style="color: #2262b3;" aria-hidden="true"></i>
                                    <b>${t('There are no reviews for this club yet')}</b><br><br>
                                    <p>${t('Need assistance?')}</p>
                                    <ul>
                                        <li>${t("Check your club page's Facebook & Google pages are configured in 'Facility Details'")}"</li>
                                        <li>${t("Check your Facebook & Google pages are linked to the Performance Hub 'Master Account'")}</li>
                                        <li>${t("Submit a ticket / contact support for additional troubleshooting")}</li>
                                    </ul>
                                    <p>
                                        ${t('Note if you are a new Club, it may take 2-3 weeks before your business is published on Google My Business and other directory services.')}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-center">
                            <div class="reviews-example-placeholder">
                                <img src="/assets/images/placeholders/reviews-example.png" style="width: 100%;">
                            </div>
                        </div>
                    </div>

                `);
            } else {
                $(".reviews-overview").show();
                $(".reviews-tags").show();

                renderReviewsTable(reviews, settings);
                renderReviewsOverview(reviews, settings);
                renderTags(tags);
                saveReviewsData(reviews)
                showAutoPublishNotif();

                if(defaultSelectedReviewTag) {
                    $(".search-bar").val(defaultSelectedReviewTag)
                    dtSearch(defaultSelectedReviewTag)
                }
            }

        } catch (error) {
            console.error(error);
            
            $('#reviews-table tbody').html(
                `<tr><td colspan="2" style="text-align: center;">${t("Club's digital profile not set.")}</td></tr>`
            );
        }
        
        getDesignList()
        // $('.load-on-init').LoadingOverlay('hide');
        toggleLoading($('.load-on-init'), false);



        $(document).on('click', '.enable-auto-publish', () => {
            confirmDialog(null, t("Removing all manually published reviews, and re-enabling the auto-publishing feature is not something that can be undone. Please confirm you wish to remove any manually user selected/published reviews to your Club page."), async () => {
                const [status] = await reEnableAutoPublishSettingAPI(selectedID);
                if (!status) {
                    instantNotification(
                        t("An error occured while trying to re-enable published reviews. Please contact HQ to further investigate this issue."),
                        'error',
                        10000
                    );   
                }
                loadData();
            })
        });
    }

    loadData();

    const renderTags = (tags) => {
        $('.tags > span').unbind();
        $('.tags').html('');

        tags.map(tag => {
            $('.tags').append(`
                <span class="badge use-skeleton ${ (tag.sentiment !== undefined && tag.sentiment === 'negative') ? 'badge-red' : 'badge-green-outline' }">
                    <span>${tag.tag}</span>
                    <span class="count">(${tag.count})</span>
                </span>
            `);
        })

        $('.tags > span').click(function() {
            $(".search-bar").val($(this).find("span").html())
            dtSearch($(this).find("span").html());
        });

    }

    const renderReviewsOverview = (reviews, settings) => {
        const noResponses = reviews.filter(review => review.responses.length <= 0);
        const totalReviewsMessage = `${noResponses.length} ${t("Still Require Response" + (noResponses.length == 1) ? '' : 's')}`;
        $('#total-reviews .number').html(reviews.length);

        $('#total-reviews .small-stats').html('');

        if(Number(totalReviewsMessage) > 0) {
          $('#total-reviews .small-stats').html(`
            <span
                class="clickable filter-out-responded ph-badge badge badge-red use-skeleton"
                style="max-width: 100%; text-overflow: ellipsis; overflow: hidden;"
                title="${totalReviewsMessage}"
            >
                ${totalReviewsMessage} ${t('Require Responses')} <svg class="badge__icon" style="margin-left: 4px" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M55.3419 44.0478L55.342 44.048C55.6852 44.6364 55.8672 45.3049 55.8696 45.9861C55.8721 46.6673 55.6949 47.3371 55.356 47.928C55.0171 48.5188 54.5284 49.01 53.9392 49.3518C53.35 49.6936 52.681 49.8741 51.9999 49.875H41.7975H41.6955L41.6751 49.975C41.2194 52.2046 40.0076 54.2085 38.2447 55.6476C36.4817 57.0867 34.2758 57.8727 32 57.8727C29.7243 57.8727 27.5184 57.0867 25.7554 55.6476C23.9925 54.2085 22.7807 52.2046 22.325 49.975L22.3046 49.875H22.2025H12.0001C11.3187 49.8745 10.6495 49.6943 10.06 49.3527C9.47042 49.011 8.98139 48.52 8.64219 47.929C8.30299 47.338 8.12561 46.6681 8.12795 45.9867C8.13029 45.3053 8.31226 44.6366 8.65551 44.048L8.65564 44.0478C10.0577 41.6327 12.125 34.8489 12.125 26C12.125 20.7288 14.219 15.6735 17.9463 11.9463C21.6736 8.21897 26.7289 6.125 32 6.125C37.2712 6.125 42.3265 8.21897 46.0538 11.9463C49.7811 15.6735 51.875 20.7288 51.875 26C51.875 34.8464 53.9399 41.6327 55.3419 44.0478ZM26.345 49.875H26.1683L26.2272 50.0417C26.6493 51.2355 27.431 52.2692 28.4648 53.0005C29.4987 53.7317 30.7337 54.1246 32 54.125H32.0001C33.2664 54.1246 34.5014 53.7317 35.5352 53.0005C36.569 52.2692 37.3508 51.2355 37.7729 50.0417L37.8318 49.875H37.655H26.345ZM11.892 45.9372L11.7827 46.125H12H52H52.2174L52.1081 45.9371C50.1929 42.6452 48.125 34.9993 48.125 26C48.125 21.7234 46.4262 17.6219 43.4021 14.5979C40.3781 11.5739 36.2766 9.875 32 9.875C27.7234 9.875 23.622 11.5739 20.5979 14.5979C17.5739 17.6219 15.875 21.7234 15.875 26C15.875 35.0068 13.8021 42.6527 11.892 45.9372Z" fill="currentColor" stroke="currentColor" stroke-width="0.25"/></svg>
            </span>
        `);
        }

        // Get average rating
        const reviewsWithRatings = reviews.filter(review => typeof review.rating === 'number')
        const totalRating = reviewsWithRatings.reduce((acc, review) => acc + review.rating, 0);
        const averageRating = Math.trunc(totalRating / reviewsWithRatings.length * 10) / 10;
        $('#average-rating .number').html(averageRating);
        $('#average-rating .stars').html(getRatingStars(averageRating));

        renderSourcesGraph(reviews);
        initReviewsRatingsGraph(reviewsWithRatings, settings);
    }

    const renderSourcesGraph = (data) => {

        const sources = data.reduce((acc, review) => {
            acc[review.source] = (acc[review.source] || 0) + 1;
            return acc;
        }, {});

        let chartLabels = [], chartData = [], reviewLegends = document.querySelector('.review-source-chart__legends');

        reviewLegends.innerHTML = '';

        const colors = [
          '#ff6384',
          '#37a2eb',
          '#f46dcd',
          '#ffcd56'
        ];
        let count = 0;
        const totalEvents = Object.values(sources).reduce((prev, curr) => prev + curr, 0);

        for (let [key, value] of Object.entries(sources)) {
            chartLabels.push(mapSourceNames(key));
            chartData.push(value);
            const listData = document.createElement('li');

            listData.classList.add('lead-events-data__values-grp');
            listData.style = `color: ${colors[count]}`;
            listData.innerHTML = `
              <span class="lead-events-data__values-grp__pill rounded--full use-skeleton">
                ${((value / totalEvents) * 100).toFixed(2)}%
              </span>
              <span class="lead-events-data__values-grp__label rounded use-skeleton">${mapSourceNames(key)}</span>
              <strong class="rounded use-skeleton">${value}</strong>
            `;

            reviewLegends.append(listData);

            count++;
        }

        function mapSourceNames(src) {

            switch(src) {
              case 'FACEBOOK':
                return 'Facebook'
                break;
              case 'GOOGLEMYBUSINESS':
                return 'Google'
                break;
              default:
                return src.capitalize();
            }
        }

        let chartConfig = {
            type: 'pie',
            data: {
                labels: chartLabels,
                datasets: [{
                    data: chartData,
                    backgroundColor: colors.map( color => `${color}5c`),
                    hoverBorderColor: colors,
                    hoverOffset: 10,
                    borderWidth: 1
                }],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        ...tooltipUI,
                    }
                },
                layout: {
                    padding: 10
                },
            }
        };

        // reviewTimelineGraph is already loaded ... perform an update...
        try {
            if(typeof reviewSourcesChart === 'object') reviewSourcesChart.destroy();
        } catch(e) {}

        let ctx = document.getElementById('reviews-by-source-chart').getContext('2d');
        reviewSourcesChart = new Chart(ctx, chartConfig);
    }

    const renderReviewsTable = (reviews, settings) => {
        $('#reviews-table').DataTable().clear();
        $('#reviews-table').DataTable().destroy();
        $.fn.dataTable.ext.search = [];
    
        $("#reviews-table-container").empty();
        $("#reviews-table-container").html(`
            <table id="reviews-table" class="table ph-table table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('Date')}</th>
                        <th>${t('Site')}</th>
                        <th>${t('Rating')}</th>
                        <th>${t('Review')}</th>
                        <th>${t('Response')}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);
    
        if(reviews.length <= 0) {
            $('.empty-table').removeClass('d-none');
        } else {
            $('.empty-table').addClass('d-none');
            reviews.forEach((review) => {
                const reviewRow = getReviewRow(review, settings);
                $("#reviews-table tbody").append(reviewRow);
            });
            const exportFormat = {
                columns: ':visible',
                format: {
                    body: formatExportBody
                }
            }
    
            let dtConfig = {
                responsive: true,
                iDisplayLength: 50,
                dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
                order: [[ 0, "desc" ]],
                bAutoWidth: false, 
                aoColumns : [
                  { sWidth: '53.33px' },
                  { sWidth: '10px' },
                  { sWidth: '80px' },
                  { sWidth: '' },
                  { sWidth: '' },
                  { sWidth: '200px' },
                ],
                buttons: [
                    {
                        text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                        extend: 'copy',
                        className: 'btn btn-sm btn-default',
                        exportOptions: exportFormat
                    },
                    {
                        text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                        extend: 'print',
                        title: 'Review Management',
                        className: 'btn btn-sm btn-default',
                        exportOptions: exportFormat
                    },
                    {
                        text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                        extend: 'csv',
                        className: 'btn btn-sm btn-default',
                        title: 'Review-Management__' + (new Date().toISOString()),
                        extension: '.csv',
                        exportOptions: exportFormat
                    },
                    {
                        text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                        extend: 'pdf',
                        className: 'btn btn-sm btn-default',
                        title: 'Review-Management__' + (new Date().toISOString()),
                        extension: '.pdf',
                        exportOptions: exportFormat
                    },
                ],
                language: {
                    paginate: {
                        previous: t('Previous'),
                        next: t('Next'),
                    },
                    infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
                    info: t('Showing') + ' _START_ ' + t('to') + ' _END_ ' + t('of') + ' _TOTAL_ ' + t('entries'),
                    emptyTable: t('No data available in table'),
                    zeroRecords: t('No matching records found'),
                    infoFiltered: '(' + t('filtered from') + ' _MAX_ ' + t('total entries') + ')',
                },
                
            }
    
            reviewsTable = $('#reviews-table').DataTable(dtConfig);
            wrapTableWithScrollableDiv('#reviews-table');

            // Add response table filters to search
            $.fn.dataTable.ext.search.push(
                function(settings, data, dataIndex) {

                    const responseColVal = data[4] ? data[4].trim() : ""

                    const unResponded = tableFilters.unResponded && responseColVal === '' 
                    const responded = tableFilters.responded && responseColVal !== ''
                    
                    
                    // published filter
                    const publishedInput = $(data[5]).find('input');
                    const isPublished = publishedInput && publishedInput.attr('checked');

                    const published = tableFilters.published && isPublished
                    const unPublished = tableFilters.unPublished && !isPublished

                    const results = {
                        unResponded,
                        responded,
                        published,
                        unPublished
                    }

                    const activeFilters = Object.keys(tableFilters).filter( key => tableFilters[key])

                    // if there's no active / checked filter return all
                    if(!activeFilters.length) return true

                    // return true if activeFilters returns true
                    return activeFilters.some(filter => results[filter])
                }
            );

            dtSearch = (val) => {
                reviewsTable.search(val).draw();
            }

            $("div.toolbar").html('<h5>Manage/Export Data</h5>');

            // render review table with applied filters
            reviewsTable.draw();
            updatetableFilterBadge();
        }
        
        // publish/un-publish review
        $(document).on('change', '#reviews-table .publish-button', (e) => {
            const element = $(e.currentTarget);
            publishUnpublishReview(element.closest('tr'));
        });

        // Respond to review
        $(document).on('click', '.respond-to-review', (e) => {
            e.preventDefault();
            const element = $(e.currentTarget).closest('tr');

            reviewResponseDialog(element);
        });

        // Filter out responded reviews
        $(document).on('click', '.filter-out-responded', () => {
            filterUnresponded(true)
            dtSearch("")
            $(".search-bar").val("")
            $('#reviews-table').DataTable().draw();
            updatetableFilterBadge();
        });
    }
    
    const formatExportBody = (data, row, column, node) => {
        if (column === 2) {
            const rating = $(node).data('sort');
            return rating ? `${rating} ${rating === 1 ? t('star') : t('stars')}` : '';
        }

        if (column === 5) {
            const tempDiv = $('<div>').html(data);
            tempDiv.find('.demo-switch').remove();
            tempDiv.find('.btn.print-review').remove();
            return tempDiv.text().trim();
        }

        return $('<div>').html(data).text();
    };

    const getReviewRow = (review, settings) => {
        const date = moment(new Date(review.date));
        const response = review.responses ? review.responses[0] : null;
        const sourceLogos = {
            'GOOGLEMYBUSINESS': '<img src="/assets/images/social_media_logos/google-my-business.svg" title="maps.google.com" style="width: 37px"></img>',
            'FACEBOOK': '<img src="/assets/images/social_media_logos/facebook.svg" title="facebook.com" style="width: 37px"></img>'
        }

        const reviewLengthIsValid = (review.content || '').split(' ').length >= (settings.minimumWords || 20);
        const canBePublished = reviewLengthIsValid;
    
        return (
            `<tr data-id="${review.id}" data-author-name="${review.name}">
                <td data-hj-allow class="selectable" data-order="${ date.unix() }" data-date="${ date }">
                    <div data-hj-allow>
                        <strong style="color: #4d6272">
                            ${date.format('ddd')}
                            <br>
                            ${date.format('YYYY-MM-DD')}
                        </strong>
                        <em style="color: #889681; font-size: 12px">${date.format('hh:mm A')}</em>
                    </div>
                </td>
                <td class="selectable" data-sort="${review.source}" style="text-align: center">${sourceLogos[review.source] || review.source}</td>
                <td class="selectable" data-sort="${review.rating}">${getRatingStars(review.rating)}</td>
                <td class="selectable" data-review="${review.content}">
                    <div class="d-flex align-center" style="gap: 1rem;">
                        <div>
                            <div><b>${review.name}</b></div>
                            <div>${review.title ? review.title : ''}</div>
                        </div>
                    </div>
                    ${review.content ? (
                        `<div style="margin-top: 1rem;">${review.content}</div>`
                    ) : ''}

                </td>
                <td class="selectable">
                    <div class="response-container">
                        ${response ? response.content : ''}
                    </div>
                </td>
                <td class="selectable" data-sort="${review.publish}" style="text-align: right;">
                    <div class="demo-switch">
                        <div class="switch ph-switch">
                            <label>
                                ${!(review.publishState && review.publishState.published) && !canBePublished ? `
                                    <small style="color: red; font-style: italic;">${t(`Review is shorter than ${settings.minimumWords} words - publishing to Club Page disabled`)}</small>
                                ` : `
                                    ${t('Publish on Facility Page')}
                                    <input
                                        class="publish-button"
                                        type="checkbox"
                                        ${review.publishState && review.publishState.published ? 'checked="true"' : ''}
                                    >
                                    <span class="lever lever switch-col-blue"></span>
                                `}
                            </label>
                        </div>
                    </div>

                    <button class="waves-effect btn ${response ? '' : 'btn-primary'} respond-to-review" ${response ? 'disabled="true"' : ''}>${t(`${response ? t('Responded') : t('Respond')} to Review`)}</button>
                    <button style="margin-top: 10px;" class="waves-effect btn btn-primary btn-outline btn-xs print-review clickable" type="button" data-review-id="${review.id}" disabled="disabled">
                        <i class="material-icons print-review-loading">loop</i> ${t('Export via Marketing and Print')}
                    </button>

                </td>
            </tr>`
        );
    };
    
    const reviewResponseDialog = (element) => {
        const { id: reviewId, authorName } = element.data();
        const settings = JSON.parse($('#settingsJSON').val());
    
        // Build html content for popup...
        const div = document.createElement("div");
        div.style.display = "flex";
        div.style.flexDirection = "column";

        $(div).addClass('review-response-dialog');
        $(div).html(`
            <div class="row">
                <div class="col-sm-3" style="text-align: right;">
                    ${t('Canned Responses')}:
                </div>
                <div class="col-sm-9">
                    <select title="${t('Select Canned Response')}..." data-live-search="true" data-width="100%" data-size="10" class="use-canned-response form-control show-tick selectpicker">
                        <optgroup label="${t('HQ Templates')}">
                            ${(settings.globalCannedResponses || []).map(response => (
                               `<option>${response}</option>`
                            )).join('')}
                        </optgroup>
                        <optgroup label="${t('Club Templates')}">
                            ${(settings.cannedResponses || []).map(response => (
                               `<option>${response}</option>`
                            )).join('')}
                        </optgroup>
                    </select>
                </div>
            </div>
            <div class="row" style="margin-top: 10px;">
                <div class="col-sm-12">
                    <div class="form-group">
                        <div class="form-line">
                            <textarea
                                data-hj-allow
                                class='form-control review-response-value'
                                type='text'
                                name='response'
                                rows="4"
                                placeholder="${t('Type a custom response, or select a Canned Response.')}"
                            ></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `);

        $(div).find('.use-canned-response').selectpicker('refresh');
        $(div).find('.use-canned-response').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {

            const fullName = parseFullName(authorName);
            let text = $(e.currentTarget).val();

            // deprecated variables
            text = text.replace(/\{\{first_name\}\}/, fullName.first);
            text = text.replace(/\{\{last_name\}\}/, fullName.last);
            text = text.replace(/\{\{full_name\}\}/, authorName);
            text = text.replace(/\{\{club_name\}\}/, selectedID);
            text = text.replace(/\{\{club_brand\}\}/, selectedBrand);
            text = text.replace(/\{\{club_phone\}\}/, selectedPhone);

            // reserved variables
            text = text.replace(/\{\{FirstName\}\}/, fullName.first);
            text = text.replace(/\{\{LastName\}\}/, fullName.last);
            text = text.replace(/\{\{FullName\}\}/, authorName);

            text = replaceTextVariables(text, clubVariables)

            if(text && text !== "") $(div).find('textarea[name=response]').val(text);

        });
    
        let responseContent;
        swalWithBootstrapButtons.fire({
            title: t('Respond to Review'),
            html: div,
            width: 800,
    
            //true means show cancel button, with default values
            showCancelButton: true,
            reverseButtons: true,
            confirmButtonText: t('Send'),
            cancelButtonText: t('Cancel'),
            onBeforeOpen: () => {},
            preConfirm: () => {
                let error = null;
                const response = $(div).find('textarea[name=response]').val();
                
                if (response.replace(/\s/, '') === '') {
                    error = 'Please add a response';
                }
    
                if (error) {
                    Swal.showValidationMessage(error);
                }
            },
            onClose: () => {
                responseContent = $(div).find('textarea[name=response]').val();
            }
        }).then(response => {
            if (response.value) {
                $('.save-overlay').show();
    
                $.ajax({
                    url: `/api/clubs/${selectedID}/reviews/${reviewId}/respond`,
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ responseContent }),
                    success: ({ response }) => {
                        $('.save-overlay').hide();
                        instantNotification(`Response sent!`, 'success', 10000);
    
                        const newResponse = response.content;
                        const container = element.find('.response-container');
                        container.append(newResponse);
                    },
                    error: () => {
                        $('.save-overlay').hide();
                        instantNotification(
                            t("An error occured while trying to save the publish reviews. Please contact HQ to further investigate this issue."),
                            'error',
                            10000
                        );
                    }
                });
            }
        });
    }
    
    const getRatingStars = (rating) => {
        const stars = [];
        
        for (let i = 0; i < 5; i++) {
            const color = rating > i ? 'color: gold;' : '';
            stars.push(`<span class="material-icons use-skeleton" style="font-size: 1.2rem; ${color}">star</span>`);
        }
    
        return stars.join('');
    };
    
    const publishUnpublishReview = async (element) => {
        const { id } = element.data();
        const toPublish = element.find('.publish-button').is(':checked');
        const [status] = await publishUnpublishReviewAPI(selectedID, id, toPublish);
        
        if (status) {
            reviewsData[id].reviewPublishState.published = toPublish;
            showAutoPublishNotif();
        } else {
            instantNotification(
                t("An error occured while trying to save the published reviews. Please contact HQ to further investigate this issue."),
                'error',
                10000
            );
        }
    }

    const getCannedResponseListItem = (response, isGlobal) => {
        return (
            `<li class="canned-responses list-group-item">` +
                `<div class="d-flex align-start justify-between gap-1">` +
                    `<span class="shorten-text clickable canned-response-text d-flex align-center break-all" ${isGlobal ? 'is-global="true"' : ''}>` +
                        response +
                    `</span>` +
                    (isGlobal ? (
                        ''
                    ) : (
                        `<button class="btn btn-link delete-canned-response">x</button>`
                    )) +
                `</div>` +
            `</li>`
        );
    }

    const reviewFilterDialog = () => {
        const html = document.createElement('div');
        $(html).addClass('reviews-filter-dialog');
        $(html).html(`
            <form id="reviews-filter-form" class="ph-dialog-v2" style="padding: 20px;">
                <fieldset>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group form-group-no-margin">
                                <div class="form-check styled-checkbox">
                                    <input
                                        type="checkbox"
                                        class="form-check-input filled-in chk-col-blue"
                                        id="published-toggle"
                                        name="published" ${tableFilters.published ? 'checked' : ''}
                                    />
                                    <label
                                        class="form-check-label"
                                        for="published-toggle"
                                    >
                                        ${t('Published')}
                                    </label>
                                </div>
                            </div>
                            <div class="form-group form-group-no-margin">
                                <div class="form-check styled-checkbox">
                                    <input
                                        type="checkbox"
                                        class="form-check-input filled-in chk-col-blue"
                                        id="un-published-toggle"
                                        name="unPublished" ${tableFilters.unPublished ? 'checked' : ''}
                                    />
                                    <label
                                        class="form-check-label"
                                        for="un-published-toggle"
                                    >
                                        ${t('Un-published')}
                                    </label>
                                </div>
                            </div>
                            <div class="form-group form-group-no-margin">
                                <div class="form-check styled-checkbox">
                                    <input
                                        type="checkbox"
                                        class="form-check-input filled-in chk-col-blue"
                                        id="responded-toggle"
                                        name="responded" ${tableFilters.responded ? 'checked' : ''}
                                    />
                                    <label
                                        class="form-check-label"
                                        for="responded-toggle"
                                    >
                                        ${t('With Response')}
                                    </label>
                                </div>
                            </div>
                            <div class="form-group form-group-no-margin">
                                <div class="form-check styled-checkbox">
                                    <input
                                        type="checkbox"
                                        class="form-check-input filled-in chk-col-blue"
                                        id="un-responded-toggle"
                                        name="unResponded" ${tableFilters.unResponded ? 'checked' : ''}
                                    />
                                    <label
                                        class="form-check-label"
                                        for="un-responded-toggle"
                                    >
                                        ${t('No Response')}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>
                <div class="row">
                    <div class="d-flex justify-center gap-1" style="margin-top: 3rem">
                        <button type="button" class="btn waves-effect btn-default close-dialog">${t('Cancel')}</button>
                        <button type="submit" class="btn waves-effect btn-primary">${t('Apply')}</button>
                    </div>
                </div>
            </form>
        `);

        $(html).on('submit', 'form#reviews-filter-form', async (e) => {
            e.preventDefault();

            const data = $(html).find('form').serializeArray();

            Object.keys(tableFilters).forEach(filter => {
                const value = data.find(dataFilter => dataFilter.name === filter);
                tableFilters[filter] = !!value;
            });

            
            $('#reviews-table').DataTable().draw();
            updatetableFilterBadge();
            swal.close();
        });

        $(html).on('click', '.close-dialog', () => {
            swal.close();
        });
    
        swalWithBootstrapButtons.fire({
            html,
            title: t('Filter Reviews'),
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
        });
    }

    const updatetableFilterBadge = () => {
        const selected = Object.keys(tableFilters).filter(key => tableFilters[key]).length;
        if (selected < 1) {
            $('.reviews-filter .badge').hide();
        } else {
            $('.reviews-filter .badge').show();
            $('.reviews-filter .badge').html(selected);
        }
    }

    const getManuallyPublishedReviewsCount = () => Object.keys(reviewsData).reduce((acc, key) => {
        const { modifiedBy, published } = reviewsData[key].reviewPublishState || {};
        if (modifiedBy === 'ph-user' && published) return acc + 1;
        return acc
    }, 0)

    const showAutoPublishNotif = () => {
        const clubHasReviews = Object.keys(reviewsData).length > 0;
        const has5OrMorePublishedReviews = getManuallyPublishedReviewsCount() >= 5;
        const { publishAutomatically } = reviewsSettings;
        if (clubHasReviews && has5OrMorePublishedReviews && publishAutomatically) {
            $('.auto-publish-notif').removeClass('d-none');
        } else {
            $('.auto-publish-notif').addClass('d-none');
        }
    }

    const reviewSettingsDialog = () => {
        const settings = JSON.parse($('#settingsJSON').val());
        const html = document.createElement('div');
        const disableAutoPublish = getManuallyPublishedReviewsCount() >= 5;

        $(html).addClass('reviews-settings-dialog');
        $(html).html(`
            <form id="reviews-settings-form" class="ph-dialog-v2 ph-form" style="padding: 20px;">
                <fieldset class="ph-fieldset">
                    <legend>${t('Review Publishing & Goals')}</legend>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group form-group-no-margin">
                                <div class="form-check styled-checkbox">
                                    <input type="checkbox" class="form-check-input filled-in chk-col-blue" id="publish-reviews-automatically" ${settings.publishAutomatically && !disableAutoPublish ? 'checked' : ''} ${disableAutoPublish ? 'disabled' : ''} />
                                    <label class="form-check-label" for="publish-reviews-automatically">${t('Auto-publish 5 star reviews to Facility Page')}</label>
                                </div>
                            </div>

                            <hr style="margin: 5px; border-top: 2px dashed #eeee;">

                            <div class="form-group form-group-no-margin" disabled>
                                <div
                                    id="minimum-words-container"
                                    class="form-check"
                                    ${settings.publishAutomatically ? '' : 'disabled'}
                                >
                                    <label
                                        class="form-check-label"
                                        for="set-minimum-words"
                                    >
                                        ${t('Set the minimum number of words for a review to be auto-published')}
                                    </label>
                                    <input
                                        type="number"
                                        data-hj-allow
                                        class="form-control"
                                        id="minimum-words"
                                        min="5"
                                        value="${settings.minimumWords || 20}"
                                        ${settings.publishAutomatically ? '' : 'disabled'}
                                    />
                                </div>
                            </div>

                            <div class="form-group form-group-no-margin">
                                <div class="form-check">
                                    <label class="form-check-label" for="set-minimum-reviews">${t('Set a target goal of reviews per month')} <i>(${t('Sets goal in monthly review count graph')})</i></label>
                                    <input type="number" data-hj-allow class="form-control" id="minimum-reviews" min="0" value="${settings.minimumReviews}" />
                                </div>
                            </div>

                        </div>
                    </div>
                </fieldset>
                <fieldset class="ph-fieldset">
                    <legend>${t('Canned Responses')}</legend>
                    <div class="row">
                        <div class="col-sm-12">
                            <small>
                                ${t('Canned responses are predetermined responses to common questions or reviews.')}
                            </small>
                            <br>
                            <small>${t('You can refer to Facility Variables by enclosing them in double braces, such as')} <code>{{ClubName}}</code>, ${t('as listed on the')} <a target="_blank" href="/facility-variables">${t('Facility Variables')}</a> ${t('page')}.</small>
                        </div>
                    </div>
                    <hr style="margin-bottom:0;margin-top:0;">
                    <div class="row" style="padding-top:10px;">
                        <div class="col-sm-12">
                            <b>${t('HQ Templates')}:</b>
                            <ul class="list-group global-canned-response-list canned-response-list-container">
                                ${(settings.globalCannedResponses || []).map((response) => getCannedResponseListItem(response, true)).join('')}
                            </ul>
                            <b>${t('Facility Templates')}:</b>
                            <ul class="list-group canned-response-list canned-response-list-container">
                                ${(settings.cannedResponses || []).map((response) => getCannedResponseListItem(response, false)).join('')}
                            </ul>
                        
                            <div class="d-flex align-start gap-1">
                                <div class="form-group">
                                    <div class="form-line">
                                        <textarea
                                            type="text"
                                            class="form-control"
                                            rows="3"
                                            name="new-response"
                                            placeholder="${t('Add a new Facility Template')}..."
                                            data-hj-allow
                                        ></textarea>
                                    </div>
                                </div>
                                <button class="btn waves-effect btn-primary" id="add-canned-response">${t('Add')}</button>
                            </div>
                        </div>
                    </div>
                </fieldset>
                <div class="row">
                    <div class="d-flex justify-end gap-1 pr-6" style="margin-top: 3rem">
                        <button type="button" class="btn waves-effect btn-default close-dialog">${t('Cancel')}</button>
                        <button type="submit" class="btn waves-effect btn-primary">${t('Save/Update')}</button>
                    </div>
                </div>
            </form>
        `);

        $(html).on('change', '#publish-reviews-automatically', () => {
            const isDisabled = $(html).find('#minimum-words').attr('disabled');
            
            if (!isDisabled) {
                $(html).find('#minimum-words').attr('disabled', 'disabled');
                $(html).find('#minimum-words-container').attr('disabled', 'disabled');
            } else {
                $(html).find('#minimum-words').removeAttr('disabled');
                $(html).find('#minimum-words-container').removeAttr('disabled');
            }
        });

        $(html).on('submit', 'form#reviews-settings-form', async (e) => {
            e.preventDefault();

            const data = {
                publishAutomatically: $('#publish-reviews-automatically').is(':checked'),
                minimumReviews: +$('#minimum-reviews').val(),
                minimumWords: +$('#minimum-words').val(),
            }

            try {
                await updateReviewsSettings(data);

                instantNotification(
                    'Settings updated successfully',
                    'success',
                    10000
                )

                loadSocialMediaReviews();
            } catch (error) {
                instantNotification(
                    t('An error occured while trying to save the reviews settings. Please contact HQ to further investigate this issue.'),
                    'error',
                    10000
                );
            }
        });

        $(html).on('click', '.close-dialog', () => {
            swal.close();
        });

        $(html).on('click', '.canned-response-text', (e) => {
            const element = $(e.currentTarget);

            if (element.hasClass('shorten-text')) {
                element.removeClass('shorten-text');
            } else {
                element.addClass('shorten-text');
            }
        });

        // When add button is clicked
        $(html).on('click', '#add-canned-response', async (e) => {
            const button = $(e.currentTarget);
            button.attr('disabled', 'disabled');
            const element = $(html).find('textarea[name=new-response]');
            const allResponses = getAllCannedResponses();
            const data = {
                cannedResponses: [...allResponses, element.val()],
            }

            try {
                await updateReviewsSettings(data);
                $(html).find('ul.canned-response-list').append(getCannedResponseListItem(element.val()));
                $(html).find('textarea[name=new-response]').val('');
                updateLocalSettings(data);
            } catch (error) {
                console.error(error);
            }

            button.removeAttr('disabled');
        });

        // when delete button is clicked
        $(html).on('click', '.delete-canned-response', async(e) => {
            $(e.currentTarget).attr('disabled', 'disabled');
            const index = $(e.currentTarget).closest('li').index();
            const allResponses = getAllCannedResponses();
            allResponses.splice(index, 1);
            const data = { cannedResponses: allResponses };

            try {
                await updateReviewsSettings(data);
                $(e.currentTarget).closest('li').remove();
                updateLocalSettings(data);
            } catch (error) {
                console.error(error);
            }

            $(e.currentTarget).removeAttr('disabled'); 
        });

        const getAllCannedResponses = () => {
            const responses = [];

            $(html).find('.canned-response-text').map((_, element) => {
                if (!$(element).attr('is-global')) {
                    responses.push($(element).html());
                }
            });

            return responses;
        }
    
        swalWithBootstrapButtons.fire({
            html,
            width: '900px',
            title: t('Review Management Settings'),
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
        });
    }

    const updateReviewsSettings = (data) => {
        return $.ajax({
            url: `/api/clubs/${selectedID}/reviews-settings`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });
    }

    const updateLocalSettings = (settingsParams) => {
        const currentSettings = JSON.parse($('#settingsJSON').val());
        const newSettings = { ...currentSettings, ...settingsParams };
        $('#settingsJSON').val(JSON.stringify(newSettings));
    }

    // feature to filter the input mapping data
    const availableFeatures = 'ReviewManagement'

    // get review details to prefill
    const defaultDesignObj = { inputMapping: { } }
    const getPrefillContent = (reviewsData, design = defaultDesignObj, feature = "") => {
        // get mapping keys and values
        const mapping = design.inputMapping[feature]

        const result = {}

        // get input mapping values
        for(const item in mapping) {
            result[mapping[item]] = reviewsData[item]
        }
        return result
    }

    const printDesignHeader = t('Select Design to Prefill')
    // use the design data from marketing and print
    const useDesignData = (data, reviewId) => {

        const designs = {}

        // remove all for the buttons
        $('.print-template').unbind()
 
        // prepare modal with the design options
        const modalContent = $(`
            <div class="review-designs-content designer"></div>
        `);

        // create the ist for the designs
        const designList = $(`<div class="design-list print-design-list ph-dialog-v2" style="padding: 20px;"></div>`)
        data.map((designItem) => {

            const {designId, design, ...extraFields} = designItem

            designs[designId] = {
                ...design,
                ...extraFields
            }

            const thumbnail = (design.thumbnails !== undefined) ? design.thumbnails.filter(file => { return file.type === 'thumbnail.sm' })[0].location : (design.thumbnail === undefined) ? 'about:blank' : design.thumbnail;

            const item = $(`
                  <button class="design-link print-template" data-designId="${designId}">
                      <div class="design-objs waves-effect waves-block">
                          <span class="flex items-center">
                              <img src="${thumbnail}" />
                              <div class="design-text">
                                  <span class="block h-4 sm-h-2 m-0 title-stack truncate">${design.name}</span>
                              </div>
                          </span>
                      </div>
                  </button>`);

            designList.append(item);
        });
        modalContent.append(designList)

        let selectedDesign = null

        // show modal
        swalWithBootstrapButtons
            .fire({
                html: modalContent.html(),
                width: 500,
                animation: false,
                showCloseButton: true,
                showCancelButton: false,
                showConfirmButton: false,
                cancelButtonText: t("Cancel"),
                title: t('Select Design to Prefill'),
                onOpen: () => {
                    // remove selected design when new modal is open
                    selectedDesign = null

                    // add event when the designs are clicked
                    $('.print-template').click(function() {
                        const designId = $(this).data('designid')
                        selectedDesign = designId;
                        // add the design to the global storeDesigns variable
                        storedDesigns[designId] = {
                            ...storedDesigns[designId],
                            ...designs[designId]
                        }
                        swal.close()
                    })

                    const swalElement = Swal.getPopup();
                    $(swalElement).draggable({
                        handle: '.swal2-header',  // Makes the title bar the drag handle
                    });
                    // Only change the cursor to move for the title bar
                    $(swalElement).find('.swal2-header').css('cursor', 'move');

                },
                onClose: function () {
                    // trigger quickRender if there's a selected design id
                    const prefillValues = getPrefillContent(reviewsData[reviewId], designs[selectedDesign], availableFeatures)
                    console.log('prefillValues', prefillValues)
                    if(selectedDesign) quickRenderDocument(selectedDesign, false, prefillValues)
                },
            })

    }

    // get designs from marketing and print
    const getDesignList = () => {

        const printBtn = $('.print-review')

        $.ajax({ 
            url: `/api/marketing/designs/input-mapping/${selectedID}?availableFeatures=${availableFeatures}`, 
            method: 'GET' ,
            success: (response) => {
                if(!response.length) {
                    printBtn.fadeOut()
                    return
                }

                printBtn.html(`
                    <i class="fa fa-share" aria-hidden="true"></i> ${t('Export via Marketing and Print')}
                `)

                // enable the print review button
                printBtn.removeAttr('disabled')
                
                // trigger the modal when print button click
                printBtn.click(function(){
                    const reviewId = $(this).data('reviewId')
                    useDesignData(response, reviewId)
                })
            },
            error: () => {
                // hide all print review buttons when there's an error
                printBtn.fadeOut()
            }
        })

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => reviewsTable && reviewsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => reviewsTable && reviewsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => reviewsTable && reviewsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => reviewsTable && reviewsTable.button(3).trigger() },
        ];

        const buttons = [
            { label: t('Filters'), icon: 'filter_list', onClick: reviewFilterDialog },
            ...exportButtons,
        ];

        searchbarHeader(
            '.social-reviews-nav',
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'review-management-export-buttons',
                },
                settingsBtn: reviewSettingsDialog,
            }
        );

        $('.review-management .search-bar').on('input', function () {
            dtSearch(this.value)
        });
    }
    
}

const initReviewsRatingsGraph = (reviews, settings) => {
    $('#reviews-ratings-graph').html('');

    let start = moment().subtract(12, 'months').startOf('month');
    let end = moment().endOf('month');
    
    function setDateRange(start, end) {
        $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
    }

    const rangeObject = {};
    rangeObject[t('Last 7 Days')] = [moment().subtract(6, 'days'), moment()];
    rangeObject[t('This Month')] = [moment().startOf('month'), moment().endOf('month')];
    rangeObject[t('Last 2 Months')] = [moment().subtract(2, 'month').startOf('month'), moment().endOf('month')];
    rangeObject[t('Last 4 Months')] = [moment().subtract(4, 'month').startOf('month'), moment().endOf('month')];
    rangeObject[t('Last 6 Months')] = [moment().subtract(6, 'month').startOf('month'), moment().endOf('month')];
    rangeObject[t('Last 12 Months')] = [moment().subtract(12, 'month').startOf('month'), moment().endOf('month')];
    rangeObject[t('Last 2 Years')] = [moment().subtract(2, 'years').startOf('month'), moment().endOf('month')];

    $('.review-report-date-range').daterangepicker({
        startDate: start,
        endDate: end,
        maxDate: end,
        ranges: rangeObject,
        drops: 'down',
        opens: 'left',
        locale: {
    
            applyLabel: t('Apply'),
            cancelLabel: t('Cancel'),
            customRangeLabel: t('Custom Range'),
            monthNames: [
                t('Jan'),
                t('Feb'),
                t('Mar'),
                t('Apr'),
                t('May'),
                t('Jun'),
                t('Jul'),
                t('Aug'),
                t('Sep'),
                t('Oct'),
                t('Nov'),
                t('Dec'),
            ],
            daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
        },
    }, setDateRange);

    $('#reportrange').on('apply.daterangepicker', function(ev, picker) {
        const from = picker.startDate.format('YYYY-MM-DD');
        const to = picker.endDate.format('YYYY-MM-DD');

        // Trigger chart draw...
        renderReviewsRatingsGraph(reviews, settings, moment(from, 'YYYY-MM-DD'), moment(to, 'YYYY-MM-DD'));
    });

    renderReviewsRatingsGraph(reviews, settings, start,end);
    setDateRange(start, end);
}

const renderReviewsRatingsGraph = (reviews, settings, from, to) => {
    // Decide whether to group by month, weeks, or days

    const duration = moment.duration(to.diff(from)).asDays();
    const groupBy = duration < 7 ?
        'days' : duration <= 62 ?
        'weeks' : 'months';

    // Generate data
    let ranges = [{ date: from, reviews: {} }];

    while (ranges[ranges.length - 1].date.isBefore(to)) {
        const lastDate = ranges[ranges.length - 1].date;
        const newDate = lastDate.clone().add(1, groupBy);

        // This represents the total number of ratings (per stars) over the current date range.
        let r = { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 }

        for (const item of getReviewsForRange(reviews, lastDate, newDate)){
            (r[item.rating] === undefined) ? r[item.rating] = 1 : r[item.rating]++;
        }

        ranges.push({ date: newDate, reviews: r});
    }


    ranges = ranges.map((range) => ({ 
        date: range.date.format(groupBy === 'months' ? 'MMM YY' : 'Do MMM YY'),
        reviews: range.reviews,
    }));

    ranges.shift();
    $('#reviews-ratings-graph').html('');

    let chartConfig = {
        type: 'bar',
        data: {
            datasets: [
                {
                    label: t('5 Stars'),
                    backgroundColor: [ '#37a2eb5c' ],
                    borderColor: [ '#37a2eb' ],
                    borderWidth: 1,
                    data: ranges,
                    parsing: {
                        yAxisKey: 'reviews.5'
                    }
                },
                {
                    label: t('4 Stars'),
                    backgroundColor: [ '#b192f35c' ],
                    borderColor: [ '#b192f3' ],
                    borderWidth: 1,
                    data: ranges,
                    parsing: {
                        yAxisKey: 'reviews.4'
                    }
                },
                {
                    label: t('3 Stars'),
                    backgroundColor: [ '#ff77c45c' ],
                    borderColor: [ '#ff77c4' ],
                    borderWidth: 1,
                    data: ranges,
                    parsing: {
                        yAxisKey: 'reviews.3'
                    }
                },
                {
                    label: t('2 Stars'),
                    backgroundColor: [ '#ffa6005c' ],
                    borderColor: [ '#ffa600' ],
                    borderWidth: 1,
                    data: ranges,
                    parsing: {
                        yAxisKey: 'reviews.2'
                    }
                },
                {
                    label: t('1 Star'),
                    backgroundColor: [ '#ff7a715c' ],
                    borderColor: ['#ff7a71'],
                    borderWidth: 1,
                    data: ranges,
                    parsing: {
                        yAxisKey: 'reviews.1'
                    },
                }
            ],
        },
        options: {
            parsing: {
                xAxisKey: 'date',
            },
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                },
                y: {
                    stacked: true
                }
            },
            plugins: {            
                annotation: {
                    annotations: [{
                        type: 'line',
                        mode: 'horizontal',
                        yMin: (settings.minimumReviews === undefined) ? 4 : settings.minimumReviews,
                        yMax: (settings.minimumReviews === undefined) ? 4 : settings.minimumReviews,
                        borderColor: '#ff0000',
                        borderDash: [5,10],
                        borderWidth: 2,
                        label: {
                            enabled: true,
                            backgroundColor: 'rgba(255,255,255,255.25)',
                            color: '#000000',
                            position: 'start',
                            content: t('Target # new reviews (monthly)')
                        }
                    }]
                }
            }
        }
    };

    // reviewTimelineGraph is already loaded ... perform an update...
    try {
        if(typeof reviewTimelineGraph === 'object') reviewTimelineGraph.destroy();
    } catch(e) {}

    let ctx = document.getElementById('reviews-by-date-graph').getContext('2d');
    reviewTimelineGraph = new Chart(ctx, chartConfig);
}

const getReviewsForRange = (reviews, from, to) => {
    return reviews.filter(review => {
        const date = moment(new Date(review.date));
        return date.isSameOrAfter(from) && date.isSameOrBefore(to);
    });
}

const fetchClubReviews = (clubID) => {
    return $.ajax({ url: `/api/clubs/${clubID}/reviews`, method: 'GET' });
}

const fetchClubReviewsSettings = (clubID) => {
    return $.ajax({ url: `/api/clubs/${clubID}/reviews-settings`, method: 'GET' });
}

