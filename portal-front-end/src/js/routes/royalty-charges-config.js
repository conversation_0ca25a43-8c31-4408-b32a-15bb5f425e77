function sRoyaltyChargesConfig() {
    let countries = [];
    let clubs = [];
    let blocks = [];

    // used in block creation
    let availableClubs = [];
    let availableRegions = [];

    async function init() {
        $('.empty-state-container').addClass('hidden');
        renderSearchNav();
        loadBlockSkeletons();
        const [clubsRes, chargesConfigRes, currenciesRes] = await Promise.all([
            listClubsAPI(),
            listAdminRoyaltyChargesConfigAPI(),
            listCurrenciesAPI(),
        ]);

        $('.searchbar-header').removeClass('isLoading');
        if (!clubsRes[0]) return instantNotification('Failed to fetch clubs!', 'error');
        if (!chargesConfigRes[0]) return instantNotification('Failed to fetch charges config!', 'error');
        if (!currenciesRes[0]) return instantNotification('Failed to fetch currencies!', 'error');

        clubs = clubsRes[1];
        blocks = chargesConfigRes[1];
        currencies = currenciesRes[1];

        listClubCountries();
        loadConfigBlocks();
    }

    function renderSearchNav() {
        searchbarHeader(
            '.search-nav-container',
            [{ label: 'New Fees Configuration Block', icon: 'fa-plus', onClick: configBlockDialog }],
            { isLoading: true }
        );

        $('.search-bar').on('input', (e) => loadConfigBlocks($(e.currentTarget).val()));
    }

    const listClubCountries = () => {
        let result = clubs.reduce((acc, club) => {
            const isoCode = club.localisation.country.iso_code;
            if (isoCode && !acc.some((a) => a.iso_code === isoCode)) {
                return [...acc, club.localisation.country];
            }

            return acc;
        }, []);

        result = [{ iso_code: 'GLOBAL', full_name: 'GLOBAL' }, ...result];
        countries = result.sort((a, b) => a.iso_code.localeCompare(b.iso_code));
    };

    function loadBlockSkeletons() {
        $('.royalty-blocks-container').html('');
        new Array(3).fill(0).forEach(() => {
            $('.royalty-blocks-container').append(
                renderConfigBlock(
                    [
                        {
                            tags: ['tag1', 'tag2', 'tag3'],
                            fees: [
                                { percentage: 10, fixed: 10 },
                                { percentage: 10, fixed: 10 },
                            ],
                        },
                    ],
                    false,
                    true
                )
            );
        });
    }

    const renderConfigBlock = (blockGroup, isLog = false, isSkeleton) => {
        const block = isLog ? blockGroup : blockGroup[0];
        const isLoading = isSkeleton ? 'isLoading' : '';
        const cardClass = !isLog ? 'card' : '';

        const actionButtons = !isLog
            ? `
              <div class="action-buttons">
                  <a class="btn-edit-config-block action-btn">
                      <i class="material-icons">edit</i>
                  </a>
                  <a class="btn-view-logs action-btn">
                      <i class="material-icons">text_snippet</i>
                  </a>
              </div>
          `
            : '';

        const addFeeButton = !isLog
            ? `
              <a class="btn-new-block-fee action-btn" style="margin-left: 5px;">
                  <i class="material-icons">add_circle</i>
              </a>
          `
            : '';

        const renderFeeElement = (fee) => {
            const renderTax = (tax) =>
                `<small style="color: #006908">${tax.percentage}% + ${tax.fixed.formatCurrency(
                    2
                )} (${tax.type.toUpperCase()})</small>`;

            const taxes =
                fee.taxes
                    ?.filter((tax) => tax.percentage > 0 || tax.fixed > 0)
                    .map(renderTax)
                    .join('') || '';

            return `
                <div class="btn-edit-block-fee d-flex gap-1 justify-between" data-id="${fee.id}">
                    <span class="use-skeleton">${fee.name}</span>
                    <span class="d-flex flex-column" style="min-width: fit-content;">
                        <div class="use-skeleton" style="font-weight: bold;">
                            ${fee.percentage}% + ${fee.fixed.formatCurrency(2)}
                        </div>
                        ${taxes}
                    </span>
                </div>
          `;
        };

        return `
            <div class="${cardClass} ${isLoading} config-block" data-id="${block.id}">
                <div class="body">
                    <div><h4 class="use-skeleton" style="margin: 0px; text-align: start;">${block.name}</h4></div>
                    <div class="use-skeleton" style="text-align: start;">
                        ${block.restrictionType}: ${block.restrictedTo}
                    </div>
                    <div class="d-flex" style="gap: 3px;">
                        ${block.tags
                            ?.map((tag) => `<span class="badge badge-outline-dark use-skeleton">${tag}</span>`)
                            .join('')}
                    </div>
                    ${actionButtons}
                    <div class="d-flex" style="margin-top: 1rem;">
                        <span class="use-skeleton" style="font-weight: bold;">
                            ${block.fees.length > 0 ? 'Fees' : '- No Fees added -'}
                        </span>
                        ${addFeeButton}
                    </div>
                    <div>${block.fees.map(renderFeeElement).join('')}</div>
                </div>
            </div>
      `;
    };

    const loadConfigBlocks = (searchTerm = '') => {
        $('.royalty-blocks-container').html('');

        updateCreateBlockAvailability();

        // group blocks by restrictedTo
        const groupedBlocks = blocks.reduce((acc, block) => {
            acc[block.restrictedTo] = [...(acc[block.restrictedTo] || []), block].sort(
                (a, b) => b.dateEffective - a.dateEffective
            );
            return acc;
        }, {});

        // filter groups using search term
        const filteredBlocks = Object.keys(groupedBlocks).filter((key) => {
            const block = groupedBlocks[key][0];
            const expression = new RegExp(searchTerm, 'igm');

            const terms = [
                block.name,
                block.restrictedTo,
                block.restrictionType,
                (block.tags || []).map((tag) => tag).join(' '),
                (block.fees || []).map((fee) => `${fee.name} ${fee.fixed} ${fee.percentage}`).join(' '),
            ].join(' ');

            return expression.test(terms);
        });

        filteredBlocks.forEach((key) => {
            $('.royalty-blocks-container').append(renderConfigBlock(groupedBlocks[key]));
        });

        if (filteredBlocks.length <= 0) {
            $('.empty-state-container').removeClass('hidden');
        } else {
            $('.empty-state-container').addClass('hidden');
        }

        $('.btn-edit-config-block').on('click', (e) => {
            e.preventDefault();

            const { id } = $(e.currentTarget).closest('.config-block').data();
            const block = blocks.find((b) => b.id === id);

            configBlockDialog(block);
        });

        $('.btn-view-logs').on('click', (e) => {
            e.preventDefault();

            const { id } = $(e.currentTarget).closest('.config-block').data();
            const block = blocks.find((b) => b.id === id);

            blockLogsDialog(block);
        });

        $('.btn-new-block-fee').on('click', (e) => {
            e.preventDefault();

            const { id } = $(e.currentTarget).closest('.config-block').data();
            const block = blocks.find((b) => b.id === id);

            blockFeeDialog(block);
        });

        $('.btn-new-block-tax').on('click', (e) => {
            e.preventDefault();

            const { id } = $(e.currentTarget).closest('.config-block').data();
            const block = blocks.find((b) => b.id === id);

            blockTaxDialog(block);
        });

        $('.btn-edit-block-fee').on('click', (e) => {
            const { id: blockId } = $(e.currentTarget).closest('.config-block').data();
            const block = blocks.find((b) => b.id === blockId);
            const { id: feeId } = $(e.currentTarget).data();
            const fee = block.fees.find((f) => f.id === feeId);

            blockFeeDialog(block, fee);
        });

        $('.btn-edit-block-tax').on('click', (e) => {
            const { id: blockId } = $(e.currentTarget).closest('.config-block').data();
            const block = blocks.find((b) => b.id === blockId);

            blockTaxDialog(block);
        });
    };

    const updateCreateBlockAvailability = () => {
        const usedClubsAndRegions = blocks.reduce((acc, block) => {
            if (acc.includes(block.restrictedTo)) return acc;
            return [...acc, block.restrictedTo];
        }, []);

        availableClubs = clubs.filter((club) => !usedClubsAndRegions.includes(club.id));
        availableRegions = countries.filter((cty) => !usedClubsAndRegions.includes(cty.iso_code));

        if ([...availableClubs, ...availableRegions].length <= 0) {
            $('.btn-new-config-block').attr('disabled', 'disabled');
        } else {
            $('.btn-new-config-block').removeAttr('disabled');
        }
    };

    function configBlockDialog(block) {
        let lastAutoGenName = '';
        const html = document.createElement('div');
        $(html).addClass('config-block-dialog ph-dialog-v2');

        const regionOptions = () => {
            return (block ? countries : availableRegions)
                .map((country) => {
                    const selected = country.iso_code === block && block.restrictedTo ? 'selected' : '';
                    return `<option value="${country.iso_code}" ${selected}>${country.full_name}</option>`;
                })
                .join('');
        };

        const clubOptions = () => {
            return (block ? clubs : availableClubs)
                .map((club) => {
                    const selected = club.id === block && block.restrictedTo ? 'selected' : '';
                    return `<option value="${club.id}" ${selected}>${club.name}</option>`;
                })
                .join('');
        };

        const selectRegionByDefault = !block || block.restrictionType === 'region';

        $(html).html(`
          <div class="body">
              <form id="config-block-form">
                  <div class="d-flex flex-column gap-1">
                      <div class="d-flex gap-1">
                          <div class="form-group form-group-no-margin">
                              <label for="restriction-type">Restricted to</label>
                              <div class="input-field s12">
                                  <select class="form-control" id="restriction-type" title="Please Select" ${
                                      block ? 'disabled' : ''
                                  }>
                                      <option value="region" ${selectRegionByDefault ? 'selected' : ''}>Regions</option>
                                      <option value="club" ${selectRegionByDefault ? '' : 'selected'}>Clubs</option>
                                  </select>
                              </div>
                          </div>

                          <div class="form-group form-group-no-margin">
                              <label for="restricted-to">Select region</label>
                              <div class="input-field s12">
                                  <select data-live-search="true" class="form-control" id="restricted-to" title="Please Select" ${
                                      block ? 'disabled' : ''
                                  }>
                                      ${selectRegionByDefault ? regionOptions() : clubOptions()}
                                  </select>
                              </div>
                          </div>
                      </div>
                      
                      <div class="form-group form-group-no-margin">
                          <label for="name">Name</label>
                          <div class="form-line">
                              <input id="name" name="name" class="form-control" type="text" value="${
                                  block && block.name ? block.name : ''
                              }" placeholder="Config Block 1">
                          </div>
                      </div>
                      
                      <div class="form-group form-group-no-margin">
                          <label for="name">Tags</label>
                          <div class="form-line">
                              <input class="form-control" id="tags" type="text" value="${
                                  block && block.tags ? block.tags.join(',') : ''
                              }" data-role="tagsinput" placeholder="tag1, tag2, tag3...">
                          </div>
                      </div>
                  </div>
              </form>
          </div>
      `);

        $(html).find('select#restricted-to, select#restriction-type').selectpicker();
        $(html).find('input#tags').tagsinput();

        $(html)
            .find('select#restriction-type')
            .on('change', (e) => {
                const type = $(e.currentTarget).val();

                if (type === 'region') {
                    $(html).find('select#restricted-to').html(regionOptions());
                    $(html).find('select#restricted-to').closest('.form-group').find('label').text('Select Region');
                } else {
                    $(html).find('select#restricted-to').html(clubOptions());
                    $(html).find('select#restricted-to').closest('.form-group').find('label').text('Select Club');
                }

                $(html).find('select#restricted-to').selectpicker('refresh');
            });

        $(html)
            .find('select#restricted-to')
            .on('change', (e) => {
                const value = $(e.currentTarget).val();

                if ($(html).find('input#name').val() === lastAutoGenName) {
                    if ($(html).find('select#restriction-type').val() === 'region') {
                        const country = countries.find((c) => c.iso_code === value);
                        lastAutoGenName = `${country.full_name} config`;
                        $(html).find('input#name').val(lastAutoGenName);
                    } else {
                        const club = clubs.find((c) => c.id === value);
                        lastAutoGenName = `${club.name} config`;
                        $(html).find('input#name').val(lastAutoGenName);
                        $(html).find('input#tags').tagsinput('add', club.brand);
                    }
                }
            });

        async function onSubmit() {
            const data = block
                ? {
                      ...block,
                      name: $(html).find('input#name').val(),
                      tags: $(html).find('input#tags').tagsinput('items'),
                  }
                : {
                      restrictedTo: $(html).find('select#restricted-to').val(),
                      restrictionType: $(html).find('select#restriction-type').val(),
                      name: $(html).find('input#name').val(),
                      tags: $(html).find('input#tags').tagsinput('items'),
                      fees: [],
                  };

            const createResponse = await createChargesConfigAPI(data);
            if (!createResponse[0]) {
                instantNotification('Failed to create config block!', 'error');
                return false;
            }

            swal.close();

            blocks = [...blocks, createResponse[1]];
            loadConfigBlocks();
            instantNotification('Config block saved!', 'success');
            return false;
        }

        swalWithBootstrapButtons.fire({
            title: `${block ? 'Edit' : 'New'} Config Block`,
            html,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: true,
            confirmButtonText: 'Save/Update',
            preConfirm: onSubmit,
        });
    }

    const blockFeeDialog = (block, fee) => {
        const html = document.createElement('div');
        $(html).addClass('block-fee-dialog ph-dialog-v2');

        const taxRuleCheck = (rule = null) => {
            if (fee && fee.taxRule == rule) return 'checked';
            return '';
        };

        const collapseShow = () => {
            if (fee && fee.taxRule) return 'show';
            return '';
        };

        $(html).html(`
            <form id="block-fee-form">
                <div class="body">
                    <div class="d-flex flex-column gap-1">
                        ${textField({ label: t('Name'), name: 'name', value: fee?.name ?? '' })}
                        ${selectpicker({
                            label: t('Transaction Type'),
                            name: 'transaction-type',
                            options: [
                                { label: 'Successful Transactions', value: 'success' },
                                { label: 'Failed (Dishonoured) Transactions', value: 'failed' },
                                { label: 'Chargebacks', value: 'chargeback' },
                            ],
                            value: fee?.transactionType ?? 'success',
                            liveSearch: false,
                        })}
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group form-group-no-margin">
                                    <label for="percentage">Percentage Amount</label>
                                    <div class="form-line">
                                        <input id="percentage" name="percentage" class="form-control masked-input" value="${
                                            fee && fee.percentage ? fee.percentage.formatCurrency(2) : '0.00'
                                        }">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group form-group-no-margin">
                                    <label for="fixed">Fixed Amount</label>
                                    <div class="form-line">
                                        <input id="fixed" name="fixed" class="form-control masked-input" value="${
                                            fee && fee.fixed ? fee.fixed.formatCurrency(2) : '0.00'
                                        }">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-column">
                            <label>Tax Rule</label>
                            
                            <input
                                name="tax-rule"
                                type="radio"
                                id="tax-rule-none"
                                class="with-gap radio-col-blue"
                                ${taxRuleCheck(null)}
                            />
                            <label class="tax-rule-label" for="tax-rule-none">None</label>
                            
                            <input
                                data-rule="net"
                                name="tax-rule"
                                type="radio"
                                id="tax-rule-net"
                                class="with-gap radio-col-blue"
                                ${taxRuleCheck('net')}
                            />
                            <label class="tax-rule-label" for="tax-rule-net">Calculate Tax Net of Fee</label>
                            <em class="tax-rule-example">
                                payout => $100 (charge) &times; 10% (fee) => $10 <br/>
                                tax% => $10 (payout) &times; 10% (tax) => $1
                            </em>
                            
                            <input
                                data-rule="gross"
                                name="tax-rule"
                                type="radio"
                                id="tax-rule-gross"
                                class="with-gap radio-col-blue"
                                ${taxRuleCheck('gross')}
                            />
                            <label class="tax-rule-label" for="tax-rule-gross">Calculate Tax Gross of Fee</label>
                            <em class="tax-rule-example">
                                fee% => $100 (charge) &times; 10% (fee) => $10 <br/>
                                tax% => $10 (fee%) &times; 10% (tax) => $1 <br />
                                payout => $10 (fee%) &plus; $1 (tax%) => $11
                            </em>
                        </div>

                        <div id="tax-inputs" class="collapse ${collapseShow()}">
                            <div class="d-flex gap-1">
                                <div class="form-group form-group-no-margin">
                                    <label for="restriction-type">Type</label>
                                    <div class="input-field s12">
                                        <select class="form-control" id="tax-type" title="Please Select">
                                            <option value="vat" ${
                                                !fee ||
                                                !fee.taxes ||
                                                ((fee && fee.taxes ? fee.taxes : [])[0] || {}).type === 'vat'
                                                    ? 'selected'
                                                    : ''
                                            }>VAT</option>
                                            <option value="gst" ${
                                                ((fee && fee.taxes ? fee.taxes : [])[0] || {}).type === 'gst'
                                                    ? 'selected'
                                                    : ''
                                            }>GST</option>
                                        </select>
                                    </div>
                                </div>
                            
                                <div class="form-group form-group-no-margin">
                                    <label for="tax-percentage">Percentage Amount</label>
                                    <div class="form-line">
                                        <input id="tax-percentage" name="tax-percentage" class="form-control masked-input" value="${
                                            (
                                                ((fee && fee.taxes ? fee.taxes : [])[0] || {}).percentage || 0
                                            ).formatCurrency(2) || '0.00'
                                        }">
                                    </div>
                                </div>

                                <div class="form-group form-group-no-margin">
                                    <label for="tax-fixed">Fixed Amount</label>
                                    <div class="form-line">
                                        <input id="tax-fixed" name="tax-fixed" class="form-control masked-input" value="${
                                            (((fee ? fee.taxes : [])[0] || {}).fixed || 0).formatCurrency(2) || '0.00'
                                        }">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr style="margin: 0px;" />
                <div class="d-flex ${fee ? 'justify-between' : 'justify-end'}" style="padding: 10px 20px;">
                    ${fee ? '<button type="button" class="btn btn-danger btn-delete-fee">Delete</button>' : ''}
                    <div class="d-flex justify-center gap-1">
                        <button type="submit" class="btn btn-primary">Save/Update</button>
                        <button type="button" class="btn btn-default close-dialog">Cancel</button>
                    </div>
                </div>
            </form>
      `);

        $(html).find('#clubs-select,select[name=transaction-type]').selectpicker();

        const maskConfig = {
            mask: '9{1,}.99',
            greedy: false,
            definitions: {
                '*': {
                    validator: '[0-9]',
                },
            },
        };

        $(html).find('.masked-input').inputmask(maskConfig);

        $(html).on('input', '.masked-input', (e) => {
            const input = $(e.currentTarget);
            const formLine = input.closest('.form-line');

            formLine.removeClass('error');

            if (!input.inputmask('isComplete')) {
                formLine.addClass('warning');
            } else {
                formLine.removeClass('warning');
            }
        });

        $(html).on('click', '.close-dialog', () => {
            swal.close();
        });

        $(html).on('click', '.btn-delete-fee', () => {
            deleteBlockFee();
        });

        $(html).on('change', 'input[name=tax-rule]', () => {
            const rule = $(html).find('input[name=tax-rule]:checked').data('rule');
            const taxInputs = $(html).find('#tax-inputs');
            taxInputs.removeClass('show');

            if (rule) {
                taxInputs.collapse('show');
            } else {
                taxInputs.collapse('hide');
            }
        });

        $(html).on('submit', '#block-fee-form', async (e) => {
            e.preventDefault();

            if (!formIsValid()) return;

            const data = {
                name: $(e.currentTarget).find('input[name=name]').val(),
                fixed: +$(e.currentTarget).find('#fixed').val() ?? 0,
                percentage: +$(e.currentTarget).find('#percentage').val() ?? 0,
                taxRule: $(e.currentTarget).find('input[name=tax-rule]:checked').data('rule') || null,
                transactionType: $(e.currentTarget).find('select[name=transaction-type]').val() ?? 'success',
            };

            if (data.taxRule) {
                data.taxes = [
                    {
                        type: $(e.currentTarget).find('#tax-type').val(),
                        fixed: +$(e.currentTarget).find('#tax-fixed').val(),
                        percentage: +$(e.currentTarget).find('#tax-percentage').val(),
                    },
                ];
            }

            const fees = fee ? block.fees.map((f) => (f.id === fee.id ? { ...f, ...data } : f)) : [...block.fees, data];
            const createResponse = await createChargesConfigAPI({ ...block, fees });

            if (!createResponse[0]) return;

            swal.close();
            blocks = [...blocks, createResponse[1]];
            loadConfigBlocks();
        });

        $(html).on('changed.bs.select', 'select[name=transaction-type]', (e) => {
            handleTransactionType(e.currentTarget.value);
        });

        handleTransactionType(fee?.transactionType);

        function handleTransactionType(transactionType = 'success') {
            if (transactionType !== 'success') {
                $(html).find('input[name=percentage]').val('0.00').attr('disabled', 'disabled');
                $(html).find('label[for=tax-rule-none]').trigger('click');
                $(html).find('#tax-rule-none').attr('disabled', 'disabled');
                $(html).find('#tax-rule-net').attr('disabled', 'disabled');
                $(html).find('#tax-rule-gross').attr('disabled', 'disabled');
                return;
            }

            $(html).find('input[name=percentage]').removeAttr('disabled');
            $(html).find('#tax-rule-none').removeAttr('disabled');
            $(html).find('#tax-rule-net').removeAttr('disabled');
            $(html).find('#tax-rule-gross').removeAttr('disabled');
        }

        const formIsValid = () => {
            let isValid = true;
            const maskedInputs = $(html).find('.masked-input').toArray();

            maskedInputs.forEach((input) => {
                const inputIsValid = $(input).inputmask('isComplete');

                if (!inputIsValid) {
                    isValid = false;
                    $(input).closest('.form-line').removeClass('warning');
                    $(input).closest('.form-line').addClass('error');
                }
            });

            return isValid;
        };

        const deleteBlockFee = () => {
            swal.close();
            confirmDeleteDialog('Delete config fee?', '', async () => {
                const fees = block.fees.filter((f) => f.id !== fee.id);
                const createResponse = await createChargesConfigAPI({ ...block, fees });
                if (!createResponse[0]) return;

                blocks = [...blocks, createResponse[1]];
                loadConfigBlocks();
            });
        };

        swalWithBootstrapButtons.fire({
            title: `${fee ? 'Edit' : 'New'} Block Fee`,
            html,
            showCloseButton: false,
            showConfirmButton: false,
            showCancelButton: false,
        });
    };

    const blockLogsDialog = (block) => {
        const group = blocks
            .filter((b) => b.restrictedTo === block.restrictedTo)
            .sort((a, b) => b.dateEffective - a.dateEffective);

        const html = document.createElement('div');
        $(html).addClass('block-logs-dialog ph-dialog-v2');

        $(html).html(`
            <div class="list-group" style="margin: 0px;">
                ${group
                    .map(
                        (log) => `
                        <div class="block-log list-group-item list-group-item-action" style="padding: 0px;">
                            <div class="d-flex justify-between" data-toggle="collapse" data-target="#${
                                log.id
                            }" aria-expanded="false" style="padding: 1.25rem;">
                                <span>${log.user}</span>
                                <span>${moment(log.dateEffective * 1000).format('YYYY-MM-DD HH:mm')}</span>
                            </div>

                            <div class="collapse" id="${log.id}" style="padding: 0px;">
                                ${renderConfigBlock(log, true)}
                            </div>
                        </div>
                    `
                    )
                    .join('')}
            </div>
      `);

        swalWithBootstrapButtons.fire({
            title: 'Change Log',
            html,
            showCloseButton: true,
            showConfirmButton: false,
            showCancelButton: false,
        });
    };

    init();
}
