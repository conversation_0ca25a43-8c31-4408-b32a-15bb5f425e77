let displayDataSourcesTable = null;

async function sDisplayDataSources() {
    const root = $('#display-admin #display-data-sources');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');

    const query = getQueryStringsObject();

    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('DISPLAY_ADMIN_FILTERS_DISPLAY_DATA_SOURCES');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }

    // Use query params first, then localStorage, then defaults
    let search = query.search || savedFilters.search || '';
    let displayDataSources = [];
    let calendarTypes = [];

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const [
            displayDataSourcesRes,
            calendarTypesRes,
        ] = await Promise.all([
            listAllDisplayDataSourcesAPI(),
            listAllCalendarTypes(),
        ]);

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!displayDataSourcesRes[0]) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        displayDataSources = displayDataSourcesRes[1];
        calendarTypes = calendarTypesRes[0] ? calendarTypesRes[1] : [];

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => displayDataSourcesTable && displayDataSourcesTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => displayDataSourcesTable && displayDataSourcesTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => displayDataSourcesTable && displayDataSourcesTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => displayDataSourcesTable && displayDataSourcesTable.button(3).trigger() },
        ];

        const buttons = [
            { label: t('Add Data Source'), icon: 'fa-add', onClick: () => displayDataSourceDialog() },
            ...exportButtons,
        ];

        searchbarHeader(
            root.find('.search-bar-container'),
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'display-data-sources-export-buttons',
                },
            }
        );

        root.find('.search-bar').val(search);

        root.find('.search-bar').on('input', function () {
            search = $(this).val();
            updateHrefQuery({ search });

            // Save search filter to localStorage
            try {
                localStorage.setItem('DISPLAY_ADMIN_FILTERS_DISPLAY_DATA_SOURCES', JSON.stringify({ search }));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }

            renderDisplayDataSourcesTable();
        });

        renderDisplayDataSourcesTable();
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>

            <div class="d-flex align-center gap-3" style="width: 100%; height: 44.8px;">
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 20%;"></div>
                    <div class="d-flex" style="height: 16px; width: 50%; gap: 2px;">
                        <div class="use-skeleton" style="width: 30%;"></div>
                        <div class="use-skeleton" style="width: 30%;"></div>
                        <div class="use-skeleton" style="width: 10%;"></div>
                    </div>
                </div>
            `);
        });
    }

    async function renderDisplayDataSourcesTable() {
        const exportFilename = 'Display_Data_Sources__' + (new Date().toISOString());

        displayDataSourcesTable?.clear();
        displayDataSourcesTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('ID')}</th>
                        <th>${t('Source Name')}</th>
                        <th>${t('Source Type')}</th>
                        <th>${t('Data')}</th>
                        <th>${t('Language')}</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        displayDataSources
            .filter((source) => {
                const searchRegex = new RegExp(search, 'igm');
                return (
                    source.id.match(searchRegex) ||
                    source.name.match(searchRegex) ||
                    source.langShort.match(searchRegex)
                );
            })
            .forEach((source) => {
                const isPHCalendar = source?.type === 'Performance Hub Calendar';

                const matchCalendar = source?.calendarId
                    ? calendarTypes.find((ct) => ct.id === source?.calendarId)
                    : '';

                tableBodyEl.append(`
                    <tr data-id="${source.id}">
                        <td><code>${source.id.split('-').shift()}</code></td>
                        <td>${source.name}</td>
                        <td>${source.type}${isPHCalendar ? `&nbsp;<span>(${matchCalendar?.name})</span><br><code>${source?.calendarId}</code>` : ''}</td>
                        <td style="max-width: 150px">
                            ${isPHCalendar
                                ? 'Data comes from the calendar schedule'
                                : `<span class="json-preview">${source.data}</span>`
                            }
                        </td>
                        <td>${source.langShort}</td>
                    </tr>
                `);
            });

        displayDataSourcesTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [[0, 'asc']],
            autoWidth: false,
            iDisplayLength: 50,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Display Data Sources',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: exportFilename,
                    extension: '.pdf'
                },
            ],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const selectedSourceID = $(this).data('id');
                    const source = displayDataSources.find((src) => src.id === selectedSourceID);
                    displayDataSourceDialog(source);
                });
            },
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));

        search && displayDataSourcesTable.search(search).draw();
    }

    function displayDataSourceDialog(source) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2');

        $(div).html(`
            <div class="body">
                <div class="d-flex flex-column gap-3">
                    ${textField({ label: t('Source Name'), name: 'name', value: source?.name })}

                    <div class="d-flex flex-column">
                        <label class="form-label">${t('Source Type')}</label>
                        <div class="d-flex gap-4">
                            <div style="margin-top:5px;" class="form-check ph-form-check">
                                <input
                                    id="type-ph-calendar"
                                    class="form-check-input"
                                    type="radio"
                                    name="source-type"
                                    value="Performance Hub Calendar"
                                    checked=checked
                                />
                                <label class="form-check-label ph-form-check-label" for="type-ph-calendar">${t('Performance Hub Calendar')}</label>
                            </div>

                            <div style="margin-top:5px;" class="form-check ph-form-check">
                                <input
                                    id="type-static"
                                    class="form-check-input"
                                    type="radio"
                                    name="source-type"
                                    value="Static"
                                    xchecked=checked
                                />
                                <label class="form-check-label ph-form-check-label" for="type-static">${t('Static')}</label>
                            </div>
                        </div>
                    </div>

                    <div name="calendar-field">
                        ${selectpicker({
                            label: t('Calendar Source'),
                            name: 'calendar-source',
                            // TODO! Edit data source lose calendar on select options
                            options: [
                                { value: '', label: t('Select Calendar Source') },
                                ...calendarTypes
                                    // Filter out already used
                                    .filter((ct) => {
                                        return !displayDataSources.some((src) => src.calendarId === ct.id);
                                    }).map((ct) => ({
                                        value: ct.id,
                                        label: ct.name,
                                    }))
                            ],
                            value: source?.calendarId,
                        })}
                    </div>

                    <div name="data-field">
                        ${textArea({
                            label: t('Source Data'),
                            name: 'data',
                            value: source?.data,
                            placeholder: 'Enter source data (typically a JSON string)',
                        })}
                    </div>

                    ${textField({ label: t('Language'), name: 'langShort', value: source?.langShort || 'English' })}
                </div>
            </div>
        `);

        $(div).find('select[name="calendar-source"]').selectpicker();

        function onSourceTypeChange() {
            const selectedType = $(div).find('input[name="source-type"]:checked').val();
            if (selectedType === 'Performance Hub Calendar') {
                $(div).find('div[name="calendar-field"]').show();
                $(div).find('div[name="data-field"]').hide();
            }

            if (selectedType === 'Static') {
                $(div).find('div[name="calendar-field"]').hide();
                $(div).find('div[name="data-field"]').show();
            }
        }

        onSourceTypeChange();
        $(div).find('input[name="source-type"]').on('change', onSourceTypeChange);

        async function preConfirm() {
            const langShort = $(div).find('input[name=langShort]').val();

            const doc = {
                id: source?.id,
                name: $(div).find('input[name=name]').val(),
                type: $(div).find('input[name="source-type"]:checked').val(),
                calendarId: $(div).find('select[name="calendar-source"]').val(),
                data: $(div).find('textarea[name=data]').val(),
                langShort,
                lang: getLanguageCodeFromName(langShort),
            };

            if (doc.type === 'Performance Hub Calendar') {
                delete doc.data;
            } else if (doc.type === 'Static') {
                delete doc.calendarId;
            }

            const response = !source?.id ? await createDisplayDataSourceAPI(doc) : await updateDisplayDataSourceAPI(doc);

            if (!response[0]) Swal.showValidationMessage(response[1]);
            return response[1];
        }

        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 600,
                title: !source ? t('Add Data Source') : t('Edit Data Source'),
                showConfirmButton: true, // TODO: Only show when all required fields have value
                showCancelButton: true,
                allowOutsideClick: false,
                showCloseButton: true,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                preConfirm,
            })
            .then((result) => {
                if (result.value) return init();
            });
    }

    init();
}

function getLanguageCodeFromName(name) {
    const display = new Intl.DisplayNames(['en'], { type: 'language' });

    const knownLangCodes = [
        'en', 'fr', 'de', 'es', 'it', 'pt', 'ru', 'zh', 'ja', 'ko',
        'ar', 'nl', 'sv', 'fi', 'no', 'pl', 'tr', 'cs', 'el', 'he',
        'hi', 'th', 'vi', 'id', 'ro', 'hu', 'uk'
    ];

    return knownLangCodes.find(
        code => display.of(code)?.toLowerCase() === name.toLowerCase()
    ) || null;
}
