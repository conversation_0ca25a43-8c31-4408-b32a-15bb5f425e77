let adminOrganisationsTable = null;

async function sOrganisationsManagement() {
    const root = $('#admin-portal #organisations');
    const pageSkeleton = root.find('.page-skeleton');
    const searchbarContainer = root.find('.search-bar-container');
    const filtersContainer = root.find('.filters-container');
    const tableContainer = root.find('.table-container');

    const query = getQueryStringsObject();
    
    // Try to load filters from localStorage
    let savedFilters = {};
    try {
        const savedFiltersString = localStorage.getItem('ADMIN_FILTERS_ORGANISATIONS');
        if (savedFiltersString) {
            savedFilters = JSON.parse(savedFiltersString);
        }
    } catch (error) {
        console.error('Error loading filters from localStorage:', error);
    }
    
    // Use query params first, then localStorage, then defaults
    let search = query.search || savedFilters.search || '';
    let organisations = [];
    let modules = [];

    async function init() {
        renderLoadingSkeleton();

        pageSkeleton.show();
        tableContainer.hide();
        searchbarContainer.hide();
        filtersContainer.hide();

        const [organisationsRes, modulesRes] = await Promise.all([listOrganisationsAPI(['brands']), listModulesAPI()]);

        pageSkeleton.hide();
        tableContainer.show();
        searchbarContainer.show();
        filtersContainer.show();

        if (!organisationsRes[0] || !modulesRes[0]) {
            tableContainer.html('<em>Something went wrong. Please reload the page.</em>');
            return;
        }

        organisations = organisationsRes[1];
        modules = modulesRes[1];

        const exportButtons = [
            { label: 'COPY', icon: 'fa-copy', onClick: () => adminOrganisationsTable && adminOrganisationsTable.button(0).trigger() },
            { label: 'PRINT', icon: 'fa-print', onClick: () => adminOrganisationsTable && adminOrganisationsTable.button(1).trigger() },
            { label: 'CSV', icon: 'fa-file-csv', onClick: () => adminOrganisationsTable && adminOrganisationsTable.button(2).trigger() },
            { label: 'PDF', icon: 'fa-file-pdf', onClick: () => adminOrganisationsTable && adminOrganisationsTable.button(3).trigger() },
        ];
        const buttons = [
            { label: t('Add Organisation'), icon: 'fa-add', onClick: () => organisationDialog() },
            ...exportButtons,
        ];
        searchbarHeader(
            root.find('.search-bar-container'),
            buttons,
            {
                usePhTheme: true,
                expandableButtons: {
                    startIndex: 1,
                    count: exportButtons.length,
                    label: t('Export'),
                    icon: 'fa-ellipsis-v',
                    id: 'organisations-export-buttons',
                },
            }
        );

        root.find('.search-bar').val(search);

        root.find('.search-bar').on('input', function () {
            search = $(this).val();
            updateHrefQuery({ search });
            
            // Save search filter to localStorage
            try {
                localStorage.setItem('ADMIN_FILTERS_ORGANISATIONS', JSON.stringify({ search }));
            } catch (error) {
                console.error('Error saving filters to localStorage:', error);
            }
            
            renderOrganisationsTable();
        });

        renderOrganisationsTable();
    }

    function renderLoadingSkeleton() {
        pageSkeleton.empty();

        pageSkeleton.append(`
            <div class="use-skeleton" style="width: 100%; height: 46px;"></div>

            <div class="d-flex align-center gap-3" style="width: 100%; height: 44.8px;">
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
                <div class="use-skeleton" style="height: 16px; width: 15%;"></div>
            </div>
        `);

        Array.from({ length: 6 }).forEach((_, i) => {
            pageSkeleton.append(`
                <div class="d-flex align-center justify-between" style="width: 100%; height: 44.8px;">
                    <div class="use-skeleton" style="height: 16px; width: 10%;"></div>
                    <div class="use-skeleton" style="height: 16px; width: 20%;"></div>
                    <div class="d-flex" style="height: 16px; width: 50%; gap: 2px;">
                        <div class="use-skeleton" style="width: 30%;"></div>
                        <div class="use-skeleton" style="width: 30%;"></div>
                        <div class="use-skeleton" style="width: 10%;"></div>
                    </div>
                </div>
            `);
        });
    }

    async function renderOrganisationsTable() {
        let export_filename = 'Organisations__' + (new Date().toISOString());
        adminOrganisationsTable?.clear();
        adminOrganisationsTable?.destroy();

        tableContainer.html(`
            <table class="table ph-table ph-text table-hover dataTable">
                <thead>
                    <tr>
                        <th>${t('Name')}</th>
                        <th>${t('Brands')}</th>
                        <th>${t('Modules')}</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);

        const tableBodyEl = tableContainer.find('tbody');

        organisations
            .filter((organisation) => {
                const searchRegex = new RegExp(search, 'igm');
                return (
                    organisation.organisationId.match(searchRegex) ||
                    organisation.name.match(searchRegex) ||
                    organisation.brands?.some((brand) => brand.name.match(searchRegex)) ||
                    organisation.modules?.some((module) => module.name.match(searchRegex))
                );
            })
            .forEach((organisation) => {
                const maxItems = 2;

                const brands = organisation.brands?.filter((_, i) => i < maxItems);
                const brandsText = brands.map((brand) => brand.name).join(', ') || '';
                const brandsLeft = Math.max(0, organisation.brands?.length - maxItems);
                const brandsTextFull = brandsLeft ? `${brandsText} + ${brandsLeft} more` : brandsText;

                const moduleBadges = getModuleBadges({ modules: organisation.modules, allModules: modules, search });

                tableBodyEl.append(`
                    <tr data-id="${organisation.organisationId}">
                        <td>${organisation.name}</td>
                        <td>${brandsTextFull || '<div class="text-muted">No brands</div>'}</td>
                        <td>
                            <div class="d-flex flex-wrap" style="gap: 2px;">
                                ${moduleBadges}
                            </div>
                        </td>
                    </tr>
                `);
            });

        adminOrganisationsTable = tableContainer.find('table').DataTable({
            responsive: true,
            dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
            order: [[0, 'asc']],
            autoWidth: false,
            iDisplayLength: 50,
            buttons: [
                {
                    text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
                    extend: 'copy',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
                    extend: 'print',
                    title: 'Organisations',
                    className: 'btn btn-sm btn-default'
                },
                {
                    text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
                    extend: 'csv',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.csv'
                },
                {
                    text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
                    extend: 'pdf',
                    className: 'btn btn-sm btn-default',
                    title: export_filename,
                    extension: '.pdf'
                },
            ],
            createdRow: function (row) {
                $(row).on('click', function () {
                    const organisationId = $(this).data('id');
                    const organisation = organisations.find((org) => org.organisationId === organisationId);
                    organisationDialog(organisation);
                });
            },
        });

        wrapTableWithScrollableDiv(tableContainer.find('table'));

        search && adminOrganisationsTable.search(search).draw();
    }

    function organisationDialog(organisation) {
        const div = document.createElement('div');
        $(div).addClass('ph-dialog-v2 organisation-dialog');

        const changeLogoText = organisation?.logo ? t('Change Logo') : t('Add Logo');
        const logo = organisation?.logo?.s3Key
            ? `/api/admin/secure-upload/public/${organisation.logo.s3Key}`
            : organisation?.logo?.url
            ? organisation.logo.url
            : '/assets/images/default-org-logo.svg';

        const orgIdDisplay = organisation?.organisationId
            ? `
                <div class="org-id-container">
                    <label class="logo-label">${t('Organisation ID')}</label>
                    <div><code>${organisation.organisationId}</code></div>
                </div>
            `
            : '';

        $(div).html(`
            <div class="body">
                <ul class="nav nav-tabs ph-nav-tabs tab-nav-right" role="tablist" style="min-width: max-content">
                    <li role="presentation" class="active">
                        <a href="#org-profile" data-toggle="tab">${t('Organisation Profile')}</a>
                    </li>
                    <li role="presentation">
                        <a href="#modules-enabled" data-toggle="tab">${t('Modules Enabled')}</a>
                    </li>
                </ul>
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane in active" id="org-profile">
                        <div class="d-flex flex-column gap-2">
                            <div class="logo-container">
                                <label class="logo-label">${t('Logo')}</label>
                                <div class="logo-preview d-flex align-center gap-2">
                                    <div class="logo-wrapper use-skeleton">
                                        <img src="${logo}" onerror="this.src = '/assets/images/default-org-logo.svg'" />
                                    </div>
                                    <input type="file" name="logo" accept="image/*" class="hidden" />
                                    <button class="btn btn-primary auto-fetch-btn">
                                        ${t('Automatically Fetch Logo')}
                                    </button>
                                    <button class="btn btn-secondary upload-btn">
                                        ${changeLogoText}
                                    </button>
                                </div>
                            </div>
                            ${orgIdDisplay}
                            ${textField({ label: t('Name'), name: 'name', value: organisation?.name })}
                            ${textField({
                                label: t('Website Domain'),
                                name: 'websiteDomain',
                                value: organisation?.websiteDomain?.replace('https://', ''),
                            })}
                            ${textField({
                                label: t('Primary Contact Name'),
                                name: 'primaryContactName',
                                value: organisation?.primaryContactName,
                            })}
                            ${textField({
                                label: t('Primary Contact Phone'),
                                name: 'primaryContactPhone',
                                value: organisation?.primaryContactPhone,
                            })}
                            ${textField({
                                label: t('Registered Address'),
                                name: 'registeredAddress',
                                value: organisation?.registeredAddress,
                            })}
                            ${selectpicker({
                                label: t('Organisation Type'),
                                name: 'organisationType',
                                options: [
                                    { value: '', label: t('Select Organisation Type') },
                                    { value: 'franchise', label: t('Franchise') },
                                    { value: 'companyOwned', label: t('Company Owned') },
                                    { value: 'jointVenture', label: t('Joint Venture') },
                                    { value: 'licensed', label: t('Licensed') },
                                    { value: 'affiliate', label: t('Affiliate') },
                                    { value: 'distributor', label: t('Distributor') },
                                    { value: 'independentOperator', label: t('Independent Operator') },
                                    { value: 'partnership', label: t('Partnership') },
                                    { value: 'soleTrader', label: t('Sole Trader') },
                                    { value: 'notForProfit', label: t('Not-for-Profit') },
                                    { value: 'government', label: t('Government') },
                                    { value: 'other', label: t('Other') },
                                ],
                                value: organisation?.organisationType,
                            })}
                            ${textField({
                                label: t('Organisation Type Other'),
                                name: 'organisationTypeOther',
                                value:
                                    organisation?.organisationType === 'other'
                                        ? organisation?.organisationTypeOther
                                        : '',
                            })}
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="modules-enabled">
                        <div class="module-groups d-flex flex-column gap-2 ph-scrollbar"></div>
                    </div>
                </div>
            </div>
        `);

        const maskConfig = {
            regex: `https://([a-zA-Z0-9-]+)+\\.([a-zA-Z]{2,6})`,
        };

        $(div).find('input[name=websiteDomain]').inputmask(maskConfig);
        new google.maps.places.Autocomplete($(div).find('[name=registeredAddress]')[0]);
        addClipboardCopyButtons($(div).find('.org-id-container'));
        const moduleGroupPicker = renderModuleGroupPickers($(div).find('.module-groups'), modules, organisation?.modules || []);
        $(div).find('select').selectpicker();

        $(div)
            .find('.upload-btn')
            .on('click', function () {
                $(div).find('input[name="logo"]').trigger('click');
            });

        let autoFetchedLogo = null;
        let fileToUpload = null;

        $(div)
            .find('.auto-fetch-btn')
            .on('click', async function () {
                const websiteDomain = $(div).find('input[name=websiteDomain]').val();

                if (!websiteDomain) {
                    return Swal.showValidationMessage(t('Please enter a website domain.'));
                }

                $(div).find('.logo-container').addClass('isLoading');
                const [ok, data] = await getWebsiteIconAPI(websiteDomain);
                $(div).find('.logo-container').removeClass('isLoading');

                if (!ok) {
                    return Swal.showValidationMessage(t('Failed to fetch logo. Please try again.'));
                }

                autoFetchedLogo = data;
                fileToUpload = null;
                $(div).find('.logo-preview img').attr('src', data);
            });

        $(div)
            .find('input[name="logo"]')
            .on('change', function (e) {
                const file = e.target.files[0];
                if (!file) return;

                fileToUpload = file;
                autoFetchedLogo = null;
                const reader = new FileReader();
                reader.onload = function (event) {
                    $(div).find('.logo-preview img').attr('src', event.target.result);
                    $(div).find('.logo-preview img').removeClass('img-not-found');
                };
                reader.readAsDataURL(file);
            });

        $(div).find('[name="organisationTypeOther"]').closest('.form-group').hide();
        if (organisation?.organisationType === 'other') {
            $(div).find('[name="organisationTypeOther"]').closest('.form-group').show();
        }

        $(div)
            .find('select[name="organisationType"]')
            .on('change', function () {
                const selectedValue = $(this).val();
                if (selectedValue === 'other') {
                    $(div).find('[name="organisationTypeOther"]').closest('.form-group').show();
                } else {
                    $(div).find('[name="organisationTypeOther"]').closest('.form-group').hide();
                }
            });

        async function handleUploadFile(file) {
            const tokenRes = await getSecureUploadTokenAPI(
                file.name,
                true,
                organisation?.organisationId || 'Super Admin'
            );
            if (!tokenRes[0]) return tokenRes;

            const { url, fields, key } = tokenRes[1];
            const formData = new FormData();
            Object.entries(fields).forEach(([k, value]) => {
                formData.append(k, value);
            });
            formData.append('file', file);

            const s3Resp = await uploadToS3API(url, formData);
            if (!s3Resp[0]) return s3Resp;

            const fileType = key.split('/')[0];

            return saveSecureUploadAPI({
                key,
                name: file.name,
                size: file.size,
                type: fileType,
                public: true,
                hideFromSearch: true,
                organisationId: organisation?.organisationId || 'Super Admin',
            });
        }

        async function preConfirm() {
            const websiteDomainIsValid = $(div).find('input[name=websiteDomain]').inputmask('isComplete');
            if (!websiteDomainIsValid) {
                return Swal.showValidationMessage(t('Please enter a valid website domain.'));
            }

            const doc = {
                organisationId: organisation?.organisationId,
                name: $(div).find('input[name=name]').val(),
                websiteDomain: $(div).find('input[name=websiteDomain]').val(),
                primaryContactName: $(div).find('input[name=primaryContactName]').val(),
                primaryContactPhone: $(div).find('input[name=primaryContactPhone]').val(),
                registeredAddress: $(div).find('input[name=registeredAddress]').val(),
                organisationType: $(div).find('select[name=organisationType]').val(),
                organisationTypeOther: $(div).find('input[name=organisationTypeOther]').val(),
                modules: [],
            };

            if (!doc.name) return Swal.showValidationMessage(t('Please enter a name.'));
            if (!doc.primaryContactName) return Swal.showValidationMessage(t('Please enter a primary contact name.'));
            if (!doc.primaryContactPhone) return Swal.showValidationMessage(t('Please enter a primary contact phone.'));
            if (!doc.registeredAddress) return Swal.showValidationMessage(t('Please enter a registered address.'));
            if (!doc.organisationType) return Swal.showValidationMessage(t('Please select an organisation type.'));
            if (doc.organisationType === 'other' && !doc.organisationTypeOther) {
                return Swal.showValidationMessage(t('Please enter an organisation type.'));
            }

            if (fileToUpload) {
                const result = await handleUploadFile(fileToUpload);
                if (!result[0]) return Swal.showValidationMessage(t('Failed to upload logo. Please try again.'));

                doc.logo = result[1];
            } else if (autoFetchedLogo) {
                doc.logo = { url: autoFetchedLogo };
            }

            const selectedModuleIds = [];
            $(div).find('input[name="modules"]').each(function() {
                const value = $(this).val();
                selectedModuleIds.push(value);
            });
            
            doc.modules = selectedModuleIds;

            const response = await upsertOrganisationAPI(doc);

            if (!response[0]) Swal.showValidationMessage(response[1]);
            return response[1];
        }

        swalWithBootstrapButtons
            .fire({
                html: div,
                width: 600,
                title: !organisation ? t('Add Organisation') : t('Edit Organisation'),
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false,
                showCloseButton: true,
                confirmButtonText: t('Save'),
                cancelButtonText: t('Cancel'),
                preConfirm,
            })
            .then((result) => {
                if (result.value) return init();
            });
    }

    init();
} 
