let USERS_SIGNED_TNA = [];
const DEFAULT_AGREEMENT = {
  name: '',
  content: '',
  clubId: '',
  region: '',
  type: '',
  template: false,
  templateId: '',
  createdByAdmin: false
}

let currentClubId = null; // the current club id
let ClubInfo = null;    // club information
let globalTna = [];     // global or region agreements template created by admin
let clubTna = [];       // club agreements
let cuaConfigList = []; // agreements that are used by clubs (ex. free trials plan)
let agreementType = ''; // agreement type (ex. waiver)
let clubVariables = {};
let cuaTable = null;

const loadMemberAgreements = (clubId) => {
  $(document).ready( function () {
      loadPage(clubId);
  });

  const loadPage = async (clubId) => {
    // Define export buttons with appropriate handlers
    const exportButtons = [
      { label: 'COPY', icon: 'fa-copy', onClick: () => cuaTable && cuaTable.button(0).trigger() },
      { label: 'PRINT', icon: 'fa-print', onClick: () => cuaTable && cuaTable.button(1).trigger() },
      { label: 'CSV', icon: 'fa-file-csv', onClick: () => cuaTable && cuaTable.button(2).trigger() },
      { label: 'PDF', icon: 'fa-file-pdf', onClick: () => cuaTable && cuaTable.button(3).trigger() },
    ];

    const buttons = [
      ...exportButtons,
    ];

    searchbarHeader('.agreements-search-container', buttons, {
      usePhTheme: true,
      expandableButtons: {
        startIndex: 0,
        count: exportButtons.length,
        label: t('Export'),
        icon: 'fa-ellipsis-v',
        id: 'member-agreements-export-buttons',
      },
    });

    $('#main-settings-btn').append(`<a href="#agreements-container" data-toggle="tab" class="nav-tab-item settings-icon-outline">${settingsIcon()}</a>`);

    currentClubId = clubId; // set the current club id
    $("#member-agreements-table-container").empty();
    $("#member-agreements-table-container").prepend(
      '<table id="cua-table" class="table table-hover ph-table dataTable">' +
          '<thead>' +
              '<tr>' +
                  '<th>'+t('Name')+'</th>' +
                  '<th>'+t('Phone')+'</th>' +
                  '<th>'+t('Email')+'</th>' +
                  '<th>'+t('Date Last Signed')+'</th>' +
                  '<th>'+t('Date Created')+'</th>' +
                  '<th>'+t('Agreement Name')+'</th>' +
                  '<th>'+t('Membership')+'</th>' +
              '</tr>' +
          '</thead>' +
          '<tbody></tbody>' +
      '</table>'
    );

    $("#agreements-table-container").prepend(
      '<table id="agreement-table" class="ph-table table table-hover dataTable">' +
          '<thead>' +
              '<tr>' +
                  '<th>'+t('Title')+'</th>' +
                  '<th class="text-center">'+t('Has Users')+'</th>' +
                  '<th>'+t('Date Created')+'</th>' +
                  '<th>&nbsp;</th>' +
                  '<th>&nbsp;</th>' +
              '</tr>' +
          '</thead>' +
          '<tbody></tbody>' +
      '</table>' +
      '<div class="hidden" id="empty-agreement-data" style="text-align: center;">'+t('There are no configured agreements for now.')+'</div>'
    );

    wrapTableWithScrollableDiv('#cua-table');

    loadingOverlay('#member-agreements-table-container');
    loadingOverlay('#agreements-table-container');
    $('#user-agreement-form').attr('disabled', true);
    $('#configure-agreement-form').attr('disabled', true);

    await Promise.all([
      getClubInfo(clubId),
      fetchClubVariables(clubId),
      getGlobalTna(clubId),
      getDefaultPlanTna(clubId),
    ])

    await Promise.all([
      fetchAgreements(renderAgreementList, clubId),
      fetchMemberAgreements(renderMembersAgreement, clubId),
    ]);

    loadingOverlay('#member-agreements-table-container', false);
    loadingOverlay('#agreements-table-container', false);
    $('#user-agreement-form').attr('disabled', false);
    $('#configure-agreement-form').attr('disabled', false);

    $('#signed-container input.search-bar').on('input', (e) => {
      const search = $(e.currentTarget).val();
      const filteredAgreements = USERS_SIGNED_TNA.filter(agreement => {
        const regex = new RegExp(search, 'i');
        return regex.test(agreement.signedBy) || regex.test(agreement.phone) || regex.test(agreement.signedEmail);
      });

      renderMembersAgreement(filteredAgreements, clubId);
    });
  }
}

/** members agreement table renderer **/
const renderMembersAgreement = (data) => {
  cuaTable?.clear()?.destroy();

  if (data && data.length) {
    $('#cua-table tbody').empty();

    data
      .forEach((uSigned, index) => {
        $('#cua-table tbody').append(getUserAgreementRow(index, uSigned));
      });

    $('#empty-user-signed').remove();
  } else {
    $('#cua-table tbody').empty();
    if ($('#empty-user-signed').length === 0) {
      $('#member-agreements-table-container').html(
        '<div id="empty-user-signed" style="text-align: center;">' +
          t('There are no accepted agreements by any of your members at this time.') +
        '</div>'
      );
    }
  }

  const export_filename = 'Member-Agreements__' + (new Date().toISOString());

  cuaTable = $('#cua-table').DataTable({
    responsive: true,
    iDisplayLength: 50,
    dom: 'r<"ph-table-body ph-scrollbar"t><"ph-table-footer"ip>',
    order: [[ 4, "desc" ]],
    bAutoWidth: false,
    buttons: [
      {
        text: '<i class="fa fa-lg fa-clipboard"></i><span class="btn-text">' + t('Copy') + '</span>',
        extend: 'copy',
        className: 'btn btn-sm btn-default'
      },
      {
        text: '<i class="fa fa-lg fa-print"></i> <span class="btn-text">' + t('Print') + '</span>',
        extend: 'print',
        title: 'Member Agreements',
        className: 'btn btn-sm btn-default'
      },
      {
        text: '<i class="fa fa-lg fa-file-text-o"></i> <span class="btn-text">' + t('CSV') + '</span>',
        extend: 'csv',
        className: 'btn btn-sm btn-default',
        title: export_filename,
        extension: '.csv'
      },
      {
        text: '<i class="fa fa-lg fa-file-pdf-o"></i> <span class="btn-text">' + t('PDF') + '</span>',
        extend: 'pdf',
        className: 'btn btn-sm btn-default',
        title: export_filename,
        extension: '.pdf'
      },
    ],
    language: {
      "paginate": {
        "previous": t("Previous"),
        "next": t("Next")
      },
      infoEmpty: t('Showing') + ' 0 ' + t('to') + ' 0 ' + t('of') + ' 0 ' + t('entries'),
      info: t('Showing') + ' _START_ ' + t('to') + ' _END_ ' + t('of') + ' _TOTAL_ ' + t('entries'),
      emptyTable: t('No data available in table')
    },
  });

  $("#cua-table tbody tr").on('click', function () {
    const found = data.find(d => d.id == $(this).data('cuaid'));
    const loadOnView = async (found) => {
      const globalTemplates = getGlobalTemplatesForClub();
      const tnas = [...globalTemplates, ...clubTna];

      if (found) {
        loadingOverlay('#member-agreements-table-container');
        const membershipDialogContainer = document.createElement("div");
        const waiver = tnas.find(tna => tna.id === found.clubUserAgreementId);
        if (waiver) waiver.content = replaceTextVariables(waiver.content || '', clubVariables)
        const agreement = found.agreement || waiver;
        
        let metaData = getMetaData(found.meta);

        const userAgent = _get(metaData, 'userAgent', '');
        const parsedUA = (new UAParser(userAgent)).getResult();
        let shortenedUserAgent = userAgent;
        let seeMoreButton = `<button class="btn btn-link btn-sm expand-user-agent ml-1">${t('See more')}</button>`;
        
        if (parsedUA.device.vendor) {
          shortenedUserAgent = `${parsedUA.device.vendor} ${parsedUA.device.model}, ${parsedUA.os.name} ${parsedUA.os.version}`;
        } else if (parsedUA.browser.name) {
          shortenedUserAgent = `${parsedUA.browser.name} ${parsedUA.browser.version}, ${parsedUA.os.name} ${parsedUA.os.version}`;
        } else {
          seeMoreButton = '';
        }

        let rows = [
          { label: t('Full name'), value: found.signedBy },
          { label: t('Phone / Email'), value: `${found.phone}${found.signedEmail ? ` / ${found.signedEmail}` : ''}` },
          { label: t('Date signed'), value: `${found.signedDate ? unixTimeFormat((new Date(found.signedDate)).getTime() / 1000) : ''}` },
          { label: t('IP address'), value: _get(metaData, 'sourceIp', '').split(',')[0].trim() },
          { label: t('User agent'), value: `<span class="shortened-user-agent">${shortenedUserAgent}${seeMoreButton}</span><span class="hidden expanded-user-agent">${userAgent}</span>` },
        ];

        let locationRow = [
          { label: t('Country'), key: 'country_name', value: 'N/A' },
          { label: t('Country Code'), key: 'country_code', value: 'N/A' },
          { label: t('City'), key: 'city', value: 'N/A' },
          { label: t('ZIP'), key: 'postal', value: 'N/A' },
          { label: t('Latitude'), key: 'latitude', value: 'N/A' },
          { label: t('Longitude'), key: 'longitude', value: 'N/A' },
        ];

        let html = `
            <div class="ph-dialog-v2" style="padding: 20px;" style="min-height: 480px;">
                ${found.user_id ? `
                  <div class="d-flex justify-between align-center" style="margin: 5px 0;">
                    <div class="d-flex align-center gap-1">
                    </div>
                    <span id="copy-user-id-clipboard" class="copy-clipboard"><code>${found.user_id}</code></span>
                  </div>
                  ` : ''
                }
                <ul class="nav nav-tabs tab-nav-right" role="tablist">
                  <li role="presentation" class="active">
                    <a href="#signed-information-container" data-toggle="tab">
                      ${t('Information')}
                    </a>
                  </li>
                  ${agreement ? `
                    <li role="presentation">
                      <a href="#signed-agreement-container" data-toggle="tab">
                        ${t('Accepted Agreements')}
                      </a>
                    </li>` : ''
                  }
                  <li role="presentation">
                    <a id="mebership-tab" href="#signed-membership-container" data-toggle="tab">
                      ${t('Membership Details')}
                    </a>
                  </li>
                </ul>
                <div class="tab-content">
                  <div role="tabpanel" class="tab-pane in active" id="signed-information-container">
                    <div class="grow-1">
                      <div class="d-flex py-1 flex-column">
                        <table>
                        ${rows.map(({ label, value }) => {
                          return (`
                            <tr>
                              <td style="min-width: 150px;">${label}</td>
                              <td>${value}</td>
                            </tr>
                          `)
                        }).join('')}
                        </table>
                      </div>
                    </div>
                    <div class="d-flex gap-2">
                      <div class="grow-1">
                        <h1 style="font-size: 20px; font-weight: bold;">
                          ${t('Geolocation Information')}
                        </h1>
                        <hr class="m-0"/>
                        <div class="geo-info-container"></div>
                        <div class="d-flex align-start gap-1 py-1">
                          <p id="geoip-message" class="text-center hidden">${t('Loading GeoIP data')}...</p>
                          <table id="geoip-location-table">
                            <tbody></tbody>
                          </table>
                          <div id="geoip-map" class="hidden" style="width: 100%; height: 200px;"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div role="tabpanel" class="tab-pane fade" id="signed-membership-container">
                    <div class="grow-1">
                      <p id="membership-loading" class="text-center">${t('Please wait')}...</p>
                      <p id="membership-message" class="text-center hidden">${t('Detailed member information is not yet available for this user')}</p>
                      <div id="membership-table-container" class="d-flex py-1"></div>
                    </div>
                  </div>
                  ${agreement ? `
                    <div role="tabpanel" class="tab-pane fade" id="signed-agreement-container" style="max-height: 75vh; overflow: auto;">
                      <div class="grow-1">
                        <div class="py-1">
                          <div style="text-align: center;"><h4>${agreement.name}</h4></div>
                          <div>${agreement.content}</div>
                        </div>
                      </div>
                    </div>` : ''
                  }
                </div>
            </div>
        `;

        $(membershipDialogContainer).addClass('record-info-dialog');
        $(membershipDialogContainer).html(html);

        // when membership tab is clicked
        $(membershipDialogContainer).find('#mebership-tab').on('click', function () {
          const processMembershipRequest = async () => {
            $(membershipDialogContainer).find('#membership-loading').removeClass('hidden');
            const clubpass = await getClubpassInfoByEmailOrPhone(currentClubId, found.phone);
            const {
              hasMembership,
              membershipDetails,
              clubPassInfo,
            } = getMembershipDetails(clubpass, currentClubId);
            const memTable = getMembershipTable(hasMembership, membershipDetails, clubPassInfo);
            $(membershipDialogContainer).find('#membership-table-container').empty().append(memTable); // append the table
            $(membershipDialogContainer).find('#membership-loading').addClass('hidden');
            if (hasMembership === false) {
              $(membershipDialogContainer).find('#membership-message').removeClass('hidden');
            }
          }
          processMembershipRequest();
        })

        addClipboardCopyButtons($(membershipDialogContainer).find('.copy-clipboard'));
        loadingOverlay('#member-agreements-table-container', false);
        $(membershipDialogContainer).on('click', '.expand-user-agent', () => {
          $('.shortened-user-agent').addClass('hidden');
          $('.expanded-user-agent').removeClass('hidden');
        })

        swalWithBootstrapButtons.fire({
          html: membershipDialogContainer,
          showCloseButton: true,
          animation: false,
          width: '700px',
          title: t('User signed agreement'),
          showCancelButton: false,
          showConfirmButton: false,
          allowEscapeKey : false,
          onBeforeOpen: () => {
            // if there are multiple ip addresses, take the first one
            const ipAddress = _get(metaData, 'sourceIp', '').split(',')[0].trim();

            if (ipAddress) {
              $(membershipDialogContainer).find('#geoip-message').removeClass('hidden');
              getGeoIpLocation(currentClubId, ipAddress)
              .then((response) => {
                const geoLocation = response
                if (geoLocation !== false) {
                  let updatedLocationRow = locationRow.map(row => {
                    if (row.key in geoLocation && geoLocation[row.key]) {
                      const rightValue = geoLocation[row.key];
                      return {
                        ...row,
                        value: rightValue,
                      }
                    }
                    return row
                  })

                  const locationRowHTML = `
                    ${updatedLocationRow.map(({ label, value }) => {
                      return (`
                        <tr>
                          <td style="min-width: 120px; max-width: 120px;">${label}</td>
                          <td>${value}</td>
                        </tr>
                      `)
                    }).join('')}
                  `;
                  $(membershipDialogContainer).find('#geoip-location-table tbody').empty().append(locationRowHTML)
                  $(membershipDialogContainer).find('#geoip-message').addClass('hidden');

                  if (_get(geoLocation, 'latitude') && _get(geoLocation, 'longitude')) {
                    $(membershipDialogContainer).find('#geoip-map').removeClass('hidden');
                    const clubLatLng = new google.maps.LatLng(geoLocation.latitude, geoLocation.longitude);
                    const mapOptions = { zoom: 8, center: clubLatLng, disableDefaultUI: true };
                    new google.maps.Map($('#geoip-map')[0], mapOptions);
                  }

                  $('.geo-info-container').html('<div class="alert alert-blue">'+t('Geolocation Information is based on best effort data from the user\'s IP address and should be used as a guideline only and does not represent the users exact location.')+'</div>')
                } else {
                  $(membershipDialogContainer).find('#geoip-message').text(t('Not found'));
                }
              })
              .catch(err => {
                $(membershipDialogContainer).find('#geoip-message').addClass('hidden');
                console.log('error fetching geoip location', err);
              })
            } else {
              $(membershipDialogContainer).find('#geoip-message').text('Not found').removeClass('hidden');
            }
          }
        })
      }
    }
    loadOnView(found);
  });
}

const getMembershipTable = (hasMembership, membershipDetailsRow, clubPassInfo) => {
  if (!hasMembership) {
    return '';
  }

  const gymMasterDomain = ClubInfo.gymMasterDomain;
  const membershipTableHtml = `
    <table>
      ${membershipDetailsRow.map(({ label, key, value }) => {
        
        return (`
          <tr>
            <td style="min-width: 150px; max-width: 150px;">${label}</td>
            <td>
              ${hasMembership && key === 'status' ? `<div class="badge ${value === 'Expired' ? `badge-red` : `badge-green`}">${t(value)}</div>` : `${value}`}
            </td>
          </tr>
        `)
      }).join('')}
      ${Object.keys(clubPassInfo).length && 'gmID' in clubPassInfo ? `
          <tr>
            <td colspan="2" class="pt-1">
              <a
                href="https://${gymMasterDomain}/member/view/${clubPassInfo.gmID}"
                target="_blank"
                data-gymid="${clubPassInfo.gmID}"
                class="btn btn-xs btn-default waves-effect btn-show-all-grouped-payouts"
              >
                ${t('Edit in GymMaster')}
              </a>
            </td>
          </tr>` : ''
        }
    </table>
  `;

  return membershipTableHtml;
}

const getMetaData = (meta = null) => {
  if (meta == '') {
    return null
  }

  return JSON.parse(meta)
}

const getGlobalTemplatesForClub = () => {
  const region = ClubInfo.placeData.country.full_name;
  const templates = globalTna.filter(tna => {
    const regions = parseAgreementRegionsString(tna.region);
    return !tna.template && tna.type === 'waiver' && tna.region && regions.some(r => r === region)
  });
  if (templates.length > 0) return templates;
  const globalTemplates = globalTna.filter(tna => tna.region === 'global' && !tna.template && tna.type === 'waiver');
  return globalTemplates;
}

const renderAgreementList = (data, clubId) => {
  const globalTemplates = getGlobalTemplatesForClub();
  const tnas = [...globalTemplates, ...(data || [])];
  const hasSelected = cuaConfigList.some(item => tnas.some(tna => tna.id === item.clubUserAgreementId)); 

  if (tnas.length) {
    $('#empty-agreement-data').addClass('hidden');
    $('#agreement-table tbody').empty();
    tnas.forEach((tna, index) => {
      $('#agreement-table tbody').append(getAgreementRow(index, tna, hasSelected));
    });

  } else {
    $('#empty-agreement-data').removeClass('hidden');
    $('#agreement-table tbody').empty();
  }

  $('#configure-agreement-form').on('click', function() {
    openConfigureAgreement(clubId, data);
  })

  // show create agreement form
  $('#user-agreement-form').on('click', function () {
    showSelectType(clubId);
  })

  // show edit agreement form
  $('#agreement-table tbody tr .onEdit').on('click', function () {
    showForm(clubId, {id: $(this).data('editagrid')});
  })

  // show confirm delete
  $('#agreement-table tbody tr .onDeleteAgreement').on('click', function () {
      const forDelete = data.find(d => d.id == $(this).data('deleteagrid'));
      if (forDelete) {
  
          const html = `<p>${t('Are you sure you want to delete')} <strong>${forDelete.name}</strong></p>`;
  
          confirmDeleteDialog(
              t('Delete Agreement'),
              html,
              async () => {
                  await deleteAgreement(clubId, forDelete.id, { name: forDelete.name, content: forDelete.content });
                  loadingOverlay('#agreements-table-container');
                  await fetchAgreements(renderAgreementList, clubId);
                  loadingOverlay('#agreements-table-container', false);
              },
          );
      }
  })

  // view agreement
  $("#agreement-table tbody tr .view").on('click', function () {
    const forView = tnas.find(d => d.id == $(this).data('agreementid'));
    if (forView) {
      const agreementContainer = document.createElement("div");
      $(agreementContainer).html(`<div class="ph-dialog-v2" style="text-align: left; overflow-y: scroll; padding: 20px; max-height: 600px; min-height: 400px; margin-bottom: 10px;">${replaceTextVariables(forView.content, clubVariables)}</div>`)

      swalWithBootstrapButtons.fire({
        title: forView.name,
        html: agreementContainer,
        showCloseButton: true,
        animation: false,
        width: '700px',
        showCancelButton: false,
        showConfirmButton: false,
        allowEscapeKey : false,
      })
    }
  });
}

/** This is to select which agreement to used **/
const openConfigureAgreement = (clubId, data=[]) => {
  let adminTna = getGlobalTemplatesForClub();
  let options = [...adminTna, ...data];
  let formSelect = `
      <div class="ph-dialog-v2" style="padding: 20px;">
        <div class="row clearfix">
          <div class="text-left col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
              <div class="body">
                <h2 class="card-inside-title">
                  ${t('Default Facility Waiver')}
                  <small>${t('A waiver must be accepted by members before performing a workout, and is different to your Contract.')}</small>
                </h2>
                <div class="row display-flex">
                  <div class="col-sm-12">
                    <div class="form-group">
                      <div class="form-line">
                        <select id="agreement_free_trial" class="form-control show-tick selectpicker">
                          <option value="default">${t('Select Waiver')}</option>
                          ${options.map(option => {
                            return `<option value="${option.id}">${option.name}</option>`
                          })}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="action-buttons-wrapper"></div>
    </div>
  `;

  async function handleSubmit() {
    const freeTrialValue = $('#agreement_free_trial').val();
    if (freeTrialValue === 'default') return;
    
    loadingOverlay('#agreements-table-container');
    await setDefaultClubWaiver(clubId, freeTrialValue);
    await fetchAgreements(renderAgreementList, clubId);
    loadingOverlay('#agreements-table-container', false);
    Swal.close();
  }

  const div = document.createElement("div");
  $(div).html(formSelect);
  $(div).find('.action-buttons-wrapper').html(swalActionButtons(handleSubmit))

  swalWithBootstrapButtons.fire({
    html: div,
    showCloseButton: true,
    showConfirmButton: false,
    allowEscapeKey : false,
    allowOutsideClick: false,
    title: t('Default Links & Signup Waiver'),
    width: '500px',
    onBeforeOpen: async () => {
      $('#agreement_free_trial').selectpicker({ noneSelectedText: t('Nothing selected')});
      const cuaConfig = cuaConfigList.find(config => config.type === 'freetrial');
      // if already configured, set the value of config
      if (cuaConfig && 'clubUserAgreementId' in cuaConfig) {
        $('#agreement_free_trial').val(cuaConfig.clubUserAgreementId);
      } else {
        $('#agreement_free_trial').val(options[0].id);
      }

      $('#agreement_free_trial').selectpicker('refresh');
    }
  });
}

const setDefaultClubWaiver = async (clubId, id) => {
  const payload = {
    type: 'freetrial',
    title: 'Free Trial',
    clubUserAgreementId: id,
  }

  await setDefaultPlanTna(clubId, payload);
  await getDefaultPlanTna(clubId);
}

const showSelectType = (clubId) => {
  let intro = `
    <div class="panel-accordian-group ph-dialog-v2" style="padding: 20px;">
      <h4>${t('What type of Agreement would you like to create?')}</h4>
      <div class="panel-group full-body" id="accordion_5" role="tablist" aria-multiselectable="true">
        <div class="panel">
          <div class="panel-heading" role="tab" id="agreementTypeSelect">
            <div class="inputGroup">
                <input id="agreement_type_waiver" name="select_agreement_type" type="radio" checked="checked" />
                <label for="agreement_type_waiver" role="button" href="#description_type_contract" aria-controls="collapseOne_5" class="collapsed">${t('Waiver')}</label>
            </div>
          </div>
          <div id="description_type_contract" class="panel-collapse in" role="tabpanel" aria-labelledby="agreementTypeSelect">
            <div class="panel-body">
                <p>${t('A Waiver must be accepted by members before accessing/using your facility and is not a Contract - Think of this as a "Disclaimer", and is generally agreed to during Signup, or when following the Links address for free trials etc.')} <br><a style="margin-top: 10px;" href="https://ubx.fit/lnk/?${selectedID}" class="btn btn-xs btn-secondary btn-outline" target="_blank">${t('View Links Page')}</a></p>
            </div>
          </div>
        </div>
        <div class="panel panel-disabled">
          <div class="panel-heading" role="tab" id="agreementTypeContract">
            <div class="inputGroup">
                <input id="agreement_type_contract" name="select_agreement_type" type="radio" />
                <label for="agreement_type_contract" role="button" href="#description_type_contract" aria-controls="collapseOne_5" class="collapsed">${t('Contract')}</label>
            </div>
          </div>
          <div id="description_type_contract" class="panel-collapse in" role="tabpanel" aria-labelledby="agreementTypeContract">
            <div class="panel-body">
                <p>${t('A Contract may be assigned to a specific membership type. At this point in time, Contracts must be directly configured int he Membership Management System (GymMaster)')} </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
  const formIntroContainer = document.createElement("div");
  $(formIntroContainer).html(intro);

  swalWithBootstrapButtons.fire({
    title: t('Create New Agreement'),
    html: formIntroContainer,
    showCloseButton: true,
    showConfirmButton: true,
    showCancelButton: true,
    allowEscapeKey : false,
    allowOutsideClick: false,
    confirmButtonText: t('Continue'),
    cancelButtonText: t('Cancel'),
    width: '600px',
  }).then(result => {
    if(result.value) {
      if ($('#agreement_type_waiver').is(":checked")) {
        agreementType = 'waiver';
      }
      showForm(clubId);
    }
  })
}

const showForm = (clubId, inData = null) => {
  let templateOptions = globalTna.filter(tna => tna.type === 'waiver' && tna.template);
  let agreementId = 'INVALID-ID';
  let mode = 'Create';
  let editData = null;

  const agreementDialog = `
    <div class="ph-dialog-v2" style="padding: 20px;">
      <div class="form-group">
        <label for="agrTitle">${t('Use Template?')}</label>
        <select id="agreement_templates" class="form-control show-tick selectpicker">
          <option value="default">${t('Select Template')}</option>
          ${templateOptions.map(option => {
            return `<option value="${option.id}">${option.name}</option>`
          })}
        </select>
        <i class="template-info"></i>
      </div>
      <div class="form-group">
        <label for="agrTitle">${t('Agreement Title')}</label>
        <input type="text" class="form-control" id="agrTitle" placeholder="${t('Enter title')}">
      </div>
      <div class="form-check styled-checkbox" style="margin-bottom: 25px;">
        <input type="checkbox" class="form-check-input filled-in chk-col-blue" id="setAsActive">
        <label
          class="form-check-label"
          for="setAsActive"
        >${t("Set this as the current 'Default' Waver/Contract?")}</label>
      </div>
      <div>
        <label for="agreementContentEditor" style="margin: 0px;">${t('Agreement Body')}</label><br>
        <p style="line-height: 1;"><small>${t("Please note, you may use any of the templating variables (&quot;Facility Variables&quot;) by enclosing them in double braces, such as <code>{{ClubName}}</code> to use your Facilities Name in the Agreement Body. A detailed list of Variables can be found on the <a target=\"_blank\" href=\"/facility-variables\">Facility Variables</a> page.")}</small></p>
        <div id="agreementContentEditor"></div>
      </div>
    </div>
  `;

  const formContainer = document.createElement("div");
  $(formContainer).addClass('text-left create-agreement-dialog');
  $(formContainer).html(agreementDialog);
  $(formContainer).append(swalActionButtons(handleSubmit))

  if (inData && 'id' in inData) {
    mode = 'Edit';
    agreementId = inData.id;
  }

  async function handleSubmit() {
    let region = getClubRegionFormat()
    const templateId = $('#agreement_templates').val();
    const isSelected = $('#setAsActive').is(':checked');
    const contentText = $('#agreementContentEditor').trumbowyg('html');
    const updatedContentText = replaceTextVariables(contentText, clubVariables)

    $('.save-btn').prop('disabled', true);
    let responseData = null;

    if (mode == 'Edit') {
      const editParams = {
        ...editData,
        name: $('#agrTitle').val(),
        content: updatedContentText,
        templateId,
      }
      const response = await updateAgreement(clubId, agreementId, editParams);
      if (response[0]) responseData = response[1].data;
    } else {
      const dataParams = {
        ...DEFAULT_AGREEMENT,
        name: $('#agrTitle').val(),
        content: updatedContentText,
        type: agreementType,
        templateId,
        region,
      }
      const response = await createAgreement(clubId, dataParams);
      if (response[0]) responseData = response[1].data;
    }

    loadingOverlay('#agreements-table-container');

    if (isSelected && responseData) {
      await setDefaultClubWaiver(clubId, responseData.id);
    }

    await fetchAgreements(renderAgreementList, clubId);
    loadingOverlay('#agreements-table-container', false);

    Swal.close();
  }

  // agreement form
  swalWithBootstrapButtons.fire({
    title: t(`${mode} Agreement`),
    html: formContainer,
    showConfirmButton: false,
    allowEscapeKey : false,
    allowOutsideClick: false,
    showCloseButton: true,
    width: '870px',
    onBeforeOpen: async () => {
      
      // Move down the 'ok/save' buttons to suit the new v2 dialog styles...
      $('.ph-dialog-v2').next().css('padding', '20px');

      if (mode == 'Edit') {
        loadingOverlay(formContainer, true, true);
        const agreement = await apiCall(`/api/member-agreements/contracts-tna/${clubId}/${agreementId}`); 
        if (agreement && Object.keys(agreement).length && 'status' in agreement && agreement.status === 'success') {
          editData = agreement.data;
        }
        loadingOverlay(formContainer, false);
      }

      $('#agreement_templates').selectpicker();

      $('#agreementContentEditor').trumbowyg({
        svgPath: '/assets/images/trumbowyg.svg',
        btns: [
          ['viewHTML'],
          ['formatting'],
          ['strong', 'em', 'del'],
          ['superscript', 'subscript'],
          ['link'],
          ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
          ['unorderedList', 'orderedList'],
          ['horizontalRule'],
          ['removeformat'],
        ]
      });

      // selecting the template on change
      $('#agreement_templates').change(() => {
        const templateId = $('#agreement_templates').val();

        if (templateId === 'default') {
          $('#agrTitle').val('');
          $('#agreementContentEditor').trumbowyg('html', '');

          $('#agrTitle').prop('disabled', false);
          $('#agreementContentEditor').trumbowyg('enable');
          $('.template-info').html('');
          return;
        }

        const template = templateOptions.find(template => template.id === templateId);
        
        if (template) {
          $('#agrTitle').val(template.name);
          $('#agreementContentEditor')
            .trumbowyg('html', replaceTextVariables(template.content, clubVariables));
        }
      });

      if (editData) {
        const isSelected = cuaConfigList.some(item => item.clubUserAgreementId === editData.id); 


        $('#agrTitle').val(editData.name);
        $('#agreementContentEditor').trumbowyg('html', editData.content);
        $('#agreement_templates').val(editData.templateId);
        $('#agreement_templates').selectpicker('refresh');
        $('#setAsActive').prop('checked', isSelected);
      }
    }
  })
}

/** user - row **/
const getUserAgreementRow = (index, row) => {
  const globalTemplates = getGlobalTemplatesForClub();
  const tnas = [...globalTemplates, ...clubTna];
  const waiver = tnas.find(tna => tna.id === row.clubUserAgreementId);
  const signedDate = _get(row, 'signedDate');
  const signedDateText = signedDate ? unixTimeFormat(signedDate) : '';

  const badgeMap = {
    true: `<span class="badge badge-blue">${t('GymMaster')}: <i>${t('Prospect')}</i></span>`,
    false: {
      true: `<span class="badge badge-green">${t('GymMaster')}: <i>${t('Paying Membership')}</i></span>`,
      false: `<span class="badge badge-orange">${t('GymMaster')}: <i>${t('No Paying Ship')}</i></span>`
    }
  };

  const membershipBadge = row.mms ? badgeMap[row.mms.prospect][row.mms.paying] : '';

  return (
      '<tr' +
        ` data-index="${index}"` +
        ` data-created="${row.created_at}"` +
        ` data-phone="${row.phone}"` +
        ` data-cuaid="${row.id}"` +
        ` style="cursor: pointer;" ` +
      '>' +
          `<td>${row.signedBy ? row.signedBy : ''}</td>` +
          `<td>${row.phone}</td>` +
          `<td>${row.signedEmail ? row.signedEmail : ''}</td>` +
          `<td data-sort="${signedDate}">${signedDateText}</td>` +
          `<td data-hj-allow data-sort="${ (row.created_at === undefined) ? '' : row.created_at }">${unixTimeFormat(row.created_at)}</td>` +
          // `<td class="text-center" data-hj-allow>${row.agreed > 0 ? 'yes' : 'no'}</td>` +
          `<td class="selectable" data-hj-allow>${_get(row, 'agreement.name', _get(waiver, 'name', ''))}</td>` +
          `<td>${membershipBadge}</td>` +
      '</tr>'
  );
}

/** agreement configuration - row **/
const getAgreementRow = (index, row, hasSelected) => {
  const isSelected = (!hasSelected && index === 0) || cuaConfigList.some(item => item.clubUserAgreementId === row.id); 
  const disableActions = row.hasUsers || row.clubId === 'Global';

  return (`
    <tr
        data-index="${index}"
        data-created="${row.created_at}"
        data-name="${row.name}"
        data-agreementid="${row.id}"
        style="cursor: pointer;"
    >
        <td class="view" data-agreementid="${row.id}">${row.name ? row.name : ''}</td>
        <td class="view text-center" data-agreementid="${row.id}">${row.hasUsers ? t('yes') : t('no')}</td>
        <td class="view" data-agreementid="${row.id}">
          ${('created_at' in row && row.created_at !== 'undefined' && row.created_at) ? unixTimeFormat(row.created_at) : ''}
        </td>
        <td class="view min" data-agreementid="${row.id}">
          ${isSelected ? '<div class="badge badge-blue">'+t('Active')+'</div>' : ''}
        </td>
        <td class="min">
          <div class="text-center ${disableActions ? 'hidden' : ''}">
            <button type="button" data-editagrid="${row.id}" class="btn btn-info onEdit" ${disableActions ? 'disabled' : ''}>${t('Edit')}</button>
            <button type="button" data-deleteagrid="${row.id}" class="btn btn-default onDeleteAgreement" ${disableActions || isSelected ? 'disabled' : ''}><i class="fa-solid fa-trash-can mr-0" style="font-size:15px;"></i></button>
          </div>
        </td>
    </tr>
  `);
}

// human display of region name
const displayRegionNameInClub = (region) => {
  if (region === 'global') return (region).capitalize();
  if (region) {
      let regionNames = [];
      let regions = region.split(',');
      regions.forEach(reg => {
          regionNames.push(reg.split('|').length > 1 ? reg.split('|')[1] : reg);
      })

      return regionNames.join(', ');
  }

  return '';
}

const fetchMemberAgreements = async (cb, clubId) => {
  const result = await apiCall(`/api/member-agreements/${clubId}`);
  if (result && 'status' in result && result.status == 'success') {
    USERS_SIGNED_TNA = result.data;
    cb(result.data, clubId);
  } else {
    instantNotification(
      t('An error occurred while trying to fetch the signed agreements for the Club ID:') + ' [' + selectedID + ']. '+t('Please reload the page to try again.'),
      'error',
    );
  }
};

// fetch all agreements by clubId
const fetchAgreements = async (cb, clubId) => {
  const result = await apiCall(`/api/member-agreements/contracts-tna/${clubId}`);
  if (result && 'status' in result && result.status == 'success') {
    let tnaList = result.data.map(tna => {
      let users = USERS_SIGNED_TNA.find(signed => signed.clubUserAgreementId === tna.id);
      return {
        ...tna,
        hasUsers: users ? true : false
      };
    });
    clubTna = tnaList;
    cb(tnaList, clubId);
    return tnaList;
  }
};

// create agreement
const createAgreement = async (clubId, dataParams) => {
  loadingOverlay('#agreements-table-container');
  const result = await apiCall(
    `/api/member-agreements/contracts-tna/${clubId}`,
    'POST',
    { ...dataParams, organisationIds: [selectedOrgID] }
  );
  loadingOverlay('#agreements-table-container', false);
  if (result && 'status' in result && result.status == 'success') {
    return [true, result.data];
  }
  return [false];
}

// update agreement
const updateAgreement = async (clubId, id, dataParams) => {
  loadingOverlay('#agreements-table-container');
  const result = await apiCall(`/api/member-agreements/contracts-tna/${clubId}/${id}`, 'PATCH', dataParams);
  loadingOverlay('#agreements-table-container', false);
  if (result && 'status' in result && result.status == 'success') {
    return [true, result.data];
  }
  return [false];
}

// delete the agreement
const deleteAgreement = async (clubId, id, dataParams) => {
  loadingOverlay('#agreements-table-container');
  const result = await apiCall(`/api/member-agreements/contracts-tna/${clubId}/${id}`, 'DELETE', dataParams);
  loadingOverlay('#agreements-table-container', false);
  if (result && 'status' in result && result.status == 'success') {
    return true;
  }
  return false;
}

// get club information
const getClubInfo = async (clubId) => {
  const result = await apiCall(`api/clubs/clubpage/clubdata/${clubId}`);
  if (result && 'status' in result && result.status == 'success') {
    ClubInfo = result.data;
  }
}

const fetchClubVariables = async (clubId) => {
  const result = await listClubVariablesAPI(clubId);
  if (!result[0]) return;
  clubVariables = result[1].Values;
}

// get global country code and name from club info
const getClubRegionFormat = () => {
  if (_get(ClubInfo, 'placeData.country')) {
    const ccode = ClubInfo.placeData['country']['iso_code'];
    const cname = ClubInfo.placeData['country']['full_name'];
    if (ccode && cname) {
      return `${ccode}|${cname}`;
    }
  }
  return "";
}

// get the global (or region of the club) terms and conditions
const getGlobalTna = async (clubId) => {
  let url = `api/member-agreements/global-tna/${clubId}?organisationId=${selectedOrgID}`;
  let region = getClubRegionFormat()
  if (region) {
    url += '&region=' + encodeURIComponent(`${region}`);
  }
  const result = await apiCall(url);
  if (result && 'status' in result && result.status == 'success') {
    globalTna = result.data;
  }
}

// the default config of club tna
const getDefaultPlanTna = async (clubId, params=null) => {
  let url = `api/member-agreements/default-tna/${clubId}`;
  const result = await apiCall(url, 'GET', params);
  if (result && 'status' in result && result.status == 'success') {
    cuaConfigList = result.data;
  }
}

// set the default config of tna (ex. plan)
const setDefaultPlanTna = async (clubId, payload) => {
  let url = `api/member-agreements/default-tna/${clubId}`;
  const result = await apiCall(url, 'POST', payload);
  if (result && 'status' in result && result.status == 'success') {
    return true;
  }
  return false;
}

// get geolocation of the user using ip
const getGeoIpLocation = async (clubId, ip) => {
  let url = `api/geo/ip-location/${clubId}?ip=${ip}`;
  const result = await apiCall(url, 'GET');
  if (result && 'status' in result && result.status == 'success' && 'data' in result) {
    const { data } = result;
    return data;
  }
  return false;
}