// ------------------------------------------------------------------------
// Use the GA Query Explorer to create custom graphs & metrics:
// https://ga-dev-tools.appspot.com/query-explorer/
// ------------------------------------------------------------------------

const { startDate, endDate } = getQueryStringsObject();
let chartDateStart = startDate ? moment(startDate * 1000) : moment().subtract(30, 'days');
let chartDateEnd = endDate ? moment(endDate * 1000) : moment();

function injectGAClient() {
    const { startDate, endDate } = getQueryStringsObject();
    chartDateStart = startDate ? moment(startDate * 1000) : moment().subtract(30, 'days');
    chartDateEnd = endDate ? moment(endDate * 1000) : moment();

    // Initialise the date-range picker that's used to select the reporting time-range.
    $(function () {
        function setDateRange(start, end) {
            $('#page-daterange .date-string').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
        }

        const ranges = {};
        ranges[t('Today')] = [moment(), moment()];
        ranges[t('Yesterday')] = [moment().subtract(1, 'days'), moment().subtract(1, 'days')];
        ranges[t('Last 7 Days')] = [moment().subtract(6, 'days'), moment()];
        ranges[t('Last 30 Days')] = [moment().subtract(29, 'days'), moment()];
        ranges[t('This Month')] = [moment().startOf('month'), moment().endOf('month')];
        ranges[t('Last Month')] = [
            moment().subtract(1, 'month').startOf('month'),
            moment().subtract(1, 'month').endOf('month'),
        ];

        $('#page-daterange .selectbox').daterangepicker(
            {
                startDate: chartDateStart,
                endDate: chartDateEnd,
                maxDate: moment(),
                ranges,
                drops: 'down',
                opens: 'left',
                locale: {
                    format: 'MMMM DD, YYYY',
                    applyLabel: t('Apply'),
                    cancelLabel: t('Cancel'),
                    customRangeLabel: t('Custom Range'),
                    monthNames: [
                        t('Jan'),
                        t('Feb'),
                        t('Mar'),
                        t('Apr'),
                        t('May'),
                        t('Jun'),
                        t('Jul'),
                        t('Aug'),
                        t('Sep'),
                        t('Oct'),
                        t('Nov'),
                        t('Dec'),
                    ],
                    daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
                },
            },
            setDateRange
        );

        $('#page-daterange').on('apply.daterangepicker', function (ev, picker) {
            const diffInDays = picker.endDate.diff(picker.startDate, 'days');
            if (diffInDays <= 60) return changeDateRange();

            extendedDateConfirmDialog();

            function cancelDateRangeChange() {
                $('#page-daterange').data('daterangepicker')?.setStartDate(chartDateStart);
                $('#page-daterange').data('daterangepicker')?.setEndDate(chartDateEnd);
                setDateRange(chartDateStart, chartDateEnd);
            }

            function changeDateRange(day60 = false) {
                chartDateStart = picker.startDate;
                chartDateEnd = picker.endDate;

                if (day60) {
                    chartDateStart = chartDateEnd.clone().subtract(60, 'days');
                    setDateRange(chartDateStart, chartDateEnd);
                    $('#page-daterange').data('daterangepicker').setStartDate(chartDateStart);
                    $('#page-daterange').data('daterangepicker').setEndDate(chartDateEnd);
                }

                updateHrefQuery({
                    startDate: chartDateStart.unix(),
                    endDate: chartDateEnd.unix(),
                });

                drawCharts();
            }

            function extendedDateConfirmDialog() {
                const html = document.createElement('div');
                $(html).html(`
                    <div class="ph-dialog-v2 py-5 px-8">
                        ${t(
                            'Selecting a custom range of more than 60 days may take a long time to generate as the data is fetched from google servers directly. Proceed?'
                        )}

                        <div class="d-flex justify-between gap-1" style="padding-top: 2rem;">
                            <button type="button" class="btn btn-default waves-effect cancel-btn">${t('Cancel')}</button>
                            <div class="d-flex gap-1">
                                <button type="button" class="btn btn-primary waves-effect 60-day-btn">${t(
                                    'Select 60 days instead'
                                )}</button>
                                <button type="button" class="btn btn-warning waves-effect proceed-btn">${t(
                                    'Proceed'
                                )}</button>
                            </div>
                        </div>
                    </div>
                `);

                $(html)
                    .find('.cancel-btn')
                    .click(() => {
                        cancelDateRangeChange();
                        Swal.close();
                    });

                $(html)
                    .find('.60-day-btn')
                    .click(() => {
                        changeDateRange(true);
                        Swal.close();
                    });

                $(html)
                    .find('.proceed-btn')
                    .click(() => {
                        changeDateRange();
                        Swal.close();
                    });

                swalWithBootstrapButtons
                    .fire({
                        title: t('Extended Date Range May Delay Processing'),
                        html,
                        width: 500,
                        showConfirmButton: false,
                    })
                    .then((result) => {
                        if (result.dismiss) {
                            cancelDateRangeChange();
                        }
                    });
            }
        });

        setDateRange(chartDateStart, chartDateEnd);
    });
}

async function drawTotalPageViewsChart() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#pageview-graph-container').html('');
    
    toggleLoading($('.analytics-pageview-section'), true);
    const response = await getClubTotalPageViewsAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-pageview-section'), false);

    if (!response[0]) {
        return $('#pageview-graph-container').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    $('#pageview-graph-container').html('<canvas id="pageview-graph"></canvas>');

    new Chart(document.getElementById('pageview-graph'), {
        type: 'line',
        data: {
            labels: response[1].map((pv) => moment(pv.date, 'YYYY-MM-DD').format('MMM D')),
            datasets: [
                {
                    label: t('Total Page Views'),
                    data: response[1].map((pv) => pv.count),
                    borderColor: 'rgb(33, 150, 243)',
                    backgroundColor: 'rgba(33, 150, 243, 0.2)',
                    borderWidth: 1,
                    fill: true,
                },
            ],
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false,
                },
            },
            scales: {
                y: {
                    grid: {
                        color: '#EBEEF1',
                        borderColor: '#EBEEF1',
                    },
                },
                x: {
                    ticks: {
                        font: {
                            size: 10,
                        },
                        maxRotation: 0,
                        color: '#6A7383',
                    },
                    grid: {
                        color: '#fff',
                        borderColor: '#fff',
                    },
                },
            },
        },
    });
}

async function drawLeadsPieChart() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#ga_lead_events').html('');
    
    toggleLoading($('.analytics-leads-section'), true);
    const response = await getClubLeadEventsAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-leads-section'), false);

    if (!response[0]) {
        return $('#ga_lead_events').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    $('#ga_lead_events').html('<canvas id="ga_lead_events_chart"></canvas>');

    new Chart(document.getElementById('ga_lead_events_chart'), {
        type: 'pie',
        data: {
            labels: Object.keys(response[1]).map(t),
            datasets: [
                {
                    label: t('Total Page Views'),
                    data: Object.values(response[1]),
                    hoverOffset: 4,
                    backgroundColor: [
                        'rgb(190, 59, 27)',
                        'rgb(130, 0, 149)',
                        'rgb(231, 152, 32)',
                        'rgb(72, 102, 199)',
                        'rgb(85, 149, 45)',
                        'rgb(86, 152, 195)',
                        'rgb(191, 70, 117)',
                        'rgb(191, 70, 85)',
                        'rgb(191, 70, 70)',
                        'rgb(70, 191, 97)',
                    ],
                },
            ],
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false,
                },

                datalabels: {
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map((data) => {
                            sum += data;
                        });
                        let percentage = ((value * 100) / sum).toFixed(2) + '%';
                        return percentage;
                    },
                    color: '#fff',
                },
            },
        },
    });
}

async function drawPageViewTables() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#ga_pageview_tables').html('');
    
    toggleLoading($('.analytics-pageview-table-section'), true);
    const response = await getClubPageViewsAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-pageview-table-section'), false);

    if (!response[0]) {
        return $('#ga_pageview_tables').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    const hasV3Data = response[1].v3Data.headers.length > 0;
    const hasV4Data = response[1].v4Data.headers.length > 0;

    if (hasV3Data) {
        const table = createPageViewTable(response[1].v3Data, hasV4Data);
        $('#ga_pageview_tables').html(table);
    }

    if (hasV4Data) {
        const table = createPageViewTable(response[1].v4Data, hasV3Data);
        $('#ga_pageview_tables').append(table);
    }
}

function createPageViewTable({ range, headers, rows }, hasOtherData) {
    const rangeHtml = hasOtherData
        ? `<div class="text-muted">${moment(range[0] * 1000).format('MMM DD')} - ${moment(range[1] * 1000).format(
              'MMM DD'
          )}</div>`
        : ``;

    const table = $(`
        ${rangeHtml}
        <div style="max-height: 300px; overflow: auto;">
            <table class="table ph-table table-striped table-bordered table-hover">
                <thead style="position: sticky; top: 0px; background: #fff;"><tr></tr></thead>
                <tbody></tbody>
            </table>
        </div>
    `);

    headers.forEach((header) => $(table).find('thead tr').append(`<th>${t(header)}</th>`));
    rows.forEach((row) =>
        $(table)
            .find('tbody')
            .append(`<tr>${row.map((cell) => `<td>${cell}</td>`)}</tr>`)
    );

    return table;
}

async function drawReferrersTable() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#ga_referrers').html('');
    
    toggleLoading($('.analytics-ga-referrers'), true);
    const response = await getClubReferrersAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-ga-referrers'), false);

    if (!response[0]) {
        return $('#ga_referrers').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    if (response[1].length < 1) return;
    const table = $(`
        <div style="max-height: 300px; overflow-y: auto;">
            <table class="table ph-table table-striped table-bordered table-hover">
                <thead style="position: sticky; top: 0px; background: #fff;">
                    <tr>
                        <th>${t('Source')}</th>
                        <th>${t('Page Views')}</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    `);

    Object.keys(response[1]).forEach((referrer) => {
        $(table).find('tbody').append(`
            <tr>
                <td>${referrer}</td>
                <td>${response[1][referrer]}</td>
            </tr>
        `);
    });

    $('#ga_referrers').html(table);
}

async function drawTrafficGraph() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#ga_channels').html('');
    
    toggleLoading($('.analytics-ga-channels'), true);
    const response = await getClubTrafficAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-ga-channels'), false);

    if (!response[0]) {
        return $('#ga_channels').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    $('#ga_channels').html('<canvas id="ga_channels_chart"></canvas>');

    new Chart(document.getElementById('ga_channels_chart'), {
        type: 'bar',
        data: {
            labels: Object.keys(response[1]),
            datasets: [
                {
                    label: t('Total Page Views'),
                    data: Object.values(response[1]),
                    hoverOffset: 4,
                    backgroundColor: ['rgb(5, 141, 199)'],
                },
            ],
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false,
                },
            },
            scales: {
                y: {
                    beginAtZero: true,
                    display: false,
                },
            },
        },
    });
}

async function drawTopCitiesDonut() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#ga_cities').html('');
    
    toggleLoading($('.analytics-ga-cities'), true);
    const response = await getClubTopCitiesAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-ga-cities'), false);

    if (!response[0]) {
        return $('#ga_cities').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    $('#ga_cities').html('<canvas id="ga_cities_chart"></canvas>');

    new Chart(document.getElementById('ga_cities_chart'), {
        type: 'doughnut',
        data: {
            labels: Object.keys(response[1]),
            datasets: [
                {
                    label: t('Total Page Views'),
                    data: Object.values(response[1]),
                    hoverOffset: 4,
                    backgroundColor: [
                        'rgb(190, 59, 27)',
                        'rgb(130, 0, 149)',
                        'rgb(231, 152, 32)',
                        'rgb(72, 102, 199)',
                        'rgb(85, 149, 45)',
                        'rgb(86, 152, 195)',
                        'rgb(191, 70, 117)',
                        'rgb(191, 70, 85)',
                        'rgb(191, 70, 70)',
                        'rgb(70, 191, 97)',
                    ],
                },
            ],
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false,
                },

                datalabels: {
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map((data) => {
                            sum += data;
                        });
                        let percentage = ((value * 100) / sum).toFixed(2) + '%';
                        return percentage;
                    },
                    color: '#fff',
                },
            },
        },
    });
}

async function drawSocialOverview() {
    const fromDateUnix = chartDateStart.format('YYYY-MM-DD');
    const toDateUnix = chartDateEnd.format('YYYY-MM-DD');

    $('#ga_social_overview').html('');
    
    toggleLoading($('.analytics-ga-social-overview'), true);
    const response = await getClubSocialsAPI(selectedID, fromDateUnix, toDateUnix);
    toggleLoading($('.analytics-ga-social-overview'), false);

    if (!response[0]) {
        return $('#ga_social_overview').html(
            '<div class="alert alert-danger">Something went wrong. Please try again later.</div>'
        );
    }

    const hasV3Data = response[1].v3.headers.length > 0;
    const hasV4Data = response[1].v4.headers.length > 0;

    if (hasV3Data) {
        const table = createPageViewTable(response[1].v3, hasV4Data);
        $('#ga_social_overview').html(table);
    }

    if (hasV4Data) {
        const table = createPageViewTable(response[1].v4, hasV3Data);
        $('#ga_social_overview').append(table);
    }
}

function drawCharts() {
    switch (selectedBrand.toUpperCase()) {
        case 'UBX':
            $('#clubPageURL').html(
                `ubxtraining.com/gym/${selectedID} <a target="_blank" href="http://ubxtraining.com/gym/${selectedID}"><i class="fa fa-external-link"></i></a>`
            );
            break;
        default:
            // code block
            $('#clubPageURL').html(``);
    }

    $('.club-page-name').html(selectedID);

    drawTotalPageViewsChart();
    drawLeadsPieChart();
    drawPageViewTables();
    drawReferrersTable();
    drawTrafficGraph();
    drawTopCitiesDonut();
    drawSocialOverview();
}
