// agreements.js

async function sAgreements(loadAgreementID=false, sAgreementCB) {
    let allAgreements = [];
    let agreementThumbnailCache = await getSessionStorage('agreementThumbnailCache');

    if (agreementThumbnailCache) {
        agreementThumbnailCache = JSON.parse(agreementThumbnailCache);
    } else {
        agreementThumbnailCache = {};
    }

    const contractFiltersDefaults = {
        'contracts-acknowledgement': 'all',
        'contracts-sort': 'date-effective',
        'contracts-requirement': 'all',
        'contracts-status': 'active',
    };

    let contractFilters = { ...contractFiltersDefaults };
    const queryStrings = getQueryStringsObject();
    contractFilters = { ...contractFilters, ...queryStrings };

    renderNavRightFilters();
    renderContractsTabFilters();
    getAllAgreements();

    function renderContractsTabFilters() {
        new PageFilters(
            '#contracts .filters-container', 
            [
                {
                    id: 'contracts-status',
                    label: 'Status',
                    options: [
                        {
                            label: 'All',
                            value: 'all',
                            selected: contractFilters['contracts-status'] === 'all',
                        },
                        {
                            label: 'Active',
                            value: 'active',
                            selected: contractFilters['contracts-status'] === 'active',
                        },
                        {
                            label: 'Archived',
                            value: 'archived',
                            selected: contractFilters['contracts-status'] === 'archived',
                        },
                    ],
                    optional: true,
                    visible: true,
                    closedValue: 'active'
                },
                {
                    id: 'contracts-requirement',
                    label: 'Requirement',
                    options: [
                        {
                            label: 'All',
                            value: 'all',
                            selected: contractFilters['contracts-requirement'] === 'all',
                        },
                        {
                            label: 'Signature',
                            value: 'signature',
                            selected: contractFilters['contracts-requirement'] === 'signature',
                        },
                        {
                            label: 'Popup',
                            value: 'popup',
                            selected: contractFilters['contracts-requirement'] === 'popup',
                        },
                        {
                            label: 'I Agree Checkbox',
                            value: 'checkbox',
                            selected: contractFilters['contracts-requirement'] === 'checkbox',
                        },
                        {
                            label: 'None',
                            value: 'none',
                            selected: contractFilters['contracts-requirement'] === 'none',
                        }
                    ],
                    optional: true,
                    visible: true,
                },
                {
                    id: 'contracts-acknowledgement',
                    label: 'Acknowledgement',
                    options: [
                        {
                            label: 'All',
                            value: 'all',
                            selected: contractFilters['contracts-acknowledgement'] === 'all',
                        },
                        {
                            label: 'Acknowledged',
                            value: 'acknowledged',
                            selected: contractFilters['contracts-acknowledgement'] === 'acknowledged',
                        },
                        {
                            label: 'Unacknowledged',
                            value: 'unacknowledged',
                            selected: contractFilters['contracts-acknowledgement'] === 'unacknowledged',
                        },
                    ],
                    optional: true,
                    visible: true,
                }
            ],
            (filters) => {
                contractFilters = { ...contractFilters, ...filters };
                getAllAgreements();
            }
        );
    }

    async function outstandingAgreementsBanner() {
        $(".alerts-container").html('');
        if(sAgreementCB == undefined || typeof sAgreementCB !== "function") {

            var req = $.ajax({
                url: '/api/agreements/agreements-outstanding',
                method: 'GET',
                contentType: 'application/json',
                success: function(data){
                    if((data === undefined) ? false : data) {
                        let agreementList = '', agreementClubsOutstanding = [];
                        data.forEach(function(agreement) {
                            agreementList += `<li><b>${agreement.club}</b>: ${agreement.title} - ${t('Action By')}: ${moment(agreement.dateEffective * 1000).format('DD MMM YYYY')}</li>`
                            if(agreementClubsOutstanding.indexOf(agreement.club) === -1) {
                                agreementClubsOutstanding.push(agreement.club);
                            }
                        });

                        if(data.length > 0) {
                            $(".alerts-container").html(`
                                <div class="custom-alert custom-alert--danger" role="alert">
                                    <div class="custom-alert__content">
                                        <div class="custom-alert__icon">
                                            <i class="warning-icon custom-icon"></i>
                                        </div>

                                        <div class="custom-alert__body">
                                            <h6>${t(`You have multiple agreements that need to be viewed &amp; signed for your club${ (agreementClubsOutstanding.length == 1) ? '' : 's' }`)}</h6>
                                            <ul style="padding: 0px; padding-left: 30px;">${agreementList}</ul>
                                            ${ (agreementClubsOutstanding.length == 1) ? "" : `<p>${t('You will need to switch clubs using the \'club selector\' in the top right corner of the Performance Hub to sign agreements for more than one club.')}</p>` }
                                        </div>
                                    </div>
                                </div>
                            `);

                        } else {
                            // No agreements to sign!
                            $(".alerts-container").html(`
                                <div class="custom-alert" role="alert">
                                    <div class="custom-alert__content">
                                        <div class="custom-alert__icon">
                                            <i class="bell-icon custom-icon"></i>
                                        </div>

                                        <div class="custom-alert__body">
                                            <h6>${t(`Thanks for reviewing contracts and policies, It's important stuff!`)}</h6>
                                            ${t(`This is where you'll find information about how we protect your members privacy, what you can and can't do with our SaaS, billing, and more. Note that the individual contracts & agreements between your members and your facilty can be found in your Member Management System. Waivers for Free Trials can be find in the 'Member Agreements' section of Performance Hub.`)}
                                        </div>
                                    </div>
                                </div>
                            `);
                        }
                    }
                },
                error: function(xhr, textStatus, errorThrown){
                    console.error(xhr, textStatus, errorThrown);
                }
            });

        }
    }

    function renderNavRightFilters() {
        new PageFilters(
            '.nav-right-options > .filters-container',
            [
                {
                    id: 'contracts-sort',
                    label: 'Sorting',
                    options: [
                        {
                            label: 'Date Effective',
                            value: 'date-effective',
                            selected: contractFilters['contracts-sort'] === 'date-effective',
                        },{
                            label: 'Alphabetical',
                            value: 'alphabetical',
                            selected: contractFilters['contracts-sort'] === 'alphabetical',
                        }
                    ]
                },
            ],
            (filters) => {
                contractFilters = { ...contractFilters, ...filters };
                getAllAgreements();
            }
        );
    }

    async function getAllAgreements() {
        $("#contracts-container").html('');
        noAgreements(false);
        await outstandingAgreementsBanner();
        
        const tempAgreement = {
            summary: '',
            clubIds: [],
            agreementType: 'ubx',
            agreementid: 'temp-agreement-id',
            dateEffective: null,
            agreementHtml: '',
            agreementAttachments: [],
            title: '',
            requiredAcknowledgement: "popup",
            dateUpdated: null,
            dateDisplayed: null,
            agreementHtmlTitle: "Popup required",
            skeleton: true,
        };

        // render loading skeleton...
        const skeletonCount = allAgreements.length > 0 ? allAgreements.length : 6;
        renderAgreements(new Array(skeletonCount).fill(tempAgreement));

        const [data] = await Promise.all([
            listClubAgreementsAPI(selectedID, contractFilters),
            new Promise((resolve) => setTimeout(resolve, 500)),
        ]);

        if (!data[0] || data[1].length < 1) return noAgreements();
        allAgreements = data[1];
        renderAgreements(data[1]);

        function noAgreements(hide = true) {
            if (hide) {
                $("#contracts-container").addClass('hidden');
                $(".no-agreements-info").removeClass('hidden');
            } else {
                $("#contracts-container").removeClass('hidden');
                $(".no-agreements-info").addClass('hidden');
            }
        }

        function renderAgreements(data) {
            // Unbind any click/view agreement events...
            $("#contracts-container .agreement-item").off('click');
            $("#contracts-container").empty();

            data.forEach(function(agreement) {
                // If there's an API request to view the agreement...
                if(loadAgreementID && agreement.agreementid == loadAgreementID) viewAgreement(agreement)

                // ------------------------------------------------------------------------

                var signatureItem = '';
                if (agreement.requiredAcknowledgement !== 'none' && !agreement.skeleton) {
                    let signedAgreement = false;
                    if(agreement.signedBy == undefined) {
                        // User has no signature data - Agreement is not signed
                        signedAgreement = false;
                    } else {
                        if(agreement.signedBy.agreed == undefined || agreement.signedBy.agreed == false) {
                            signedAgreement = false;
                        } else {
                            signedAgreement = true;
                        }
                    }

                    if(signedAgreement) {
                        signatureItem = `<span class="badge badge-blue">${t('Acknowledged')}</span>`;
                    } else if (!agreement.deleted) {
                        signatureItem = `<span class="badge badge-red">${t('Requires Action')}</span>`;
                    }
                } else if (agreement.skeleton) {
                    signatureItem = `<div class="temp-box" style="width: 92px;"></div>`;
                }

                if (agreement.deleted) {
                    signatureItem += `<span class="badge badge-grey">${t('Archived')}</span>`;
                }

                const dateEffective = !agreement.skeleton
                    ? `
                        <div class="date-text">
                            <b>${t('Effective')}:</b>
                            ${moment(agreement.dateEffective * 1000).format('DD MMM YYYY')}
                        </div>
                    ` : '<div class="temp-box" style="width: 110px;"></div>';

                const fileIcon = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M13.265 5.235L9.765 1.735C9.69474 1.66465 9.59942 1.62509 9.5 1.625H3.5C3.26794 1.625 3.04538 1.71719 2.88128 1.88128C2.71719 2.04538 2.625 2.26794 2.625 2.5V13.5C2.625 13.7321 2.71719 13.9546 2.88128 14.1187C3.04538 14.2828 3.26794 14.375 3.5 14.375H12.5C12.7321 14.375 12.9546 14.2828 13.1187 14.1187C13.2828 13.9546 13.375 13.7321 13.375 13.5V5.5C13.3749 5.40058 13.3353 5.30526 13.265 5.235ZM9.875 2.905L12.095 5.125H9.875V2.905ZM12.5 13.625H3.5C3.46685 13.625 3.43505 13.6118 3.41161 13.5884C3.38817 13.5649 3.375 13.5332 3.375 13.5V2.5C3.375 2.46685 3.38817 2.43505 3.41161 2.41161C3.43505 2.38817 3.46685 2.375 3.5 2.375H9.125V5.5C9.125 5.59946 9.16451 5.69484 9.23483 5.76516C9.30516 5.83549 9.40054 5.875 9.5 5.875H12.625V13.5C12.625 13.5332 12.6118 13.5649 12.5884 13.5884C12.5649 13.6118 12.5332 13.625 12.5 13.625Z" fill="#6A7383"/>
                    </svg>
                `;

                const title = agreement.skeleton
                    ? `<div class="temp-box" style="height: 14px; width: 210px;"></div>`
                    : `
                        <h2 title="${t(agreement?.title ?? 'Untitled Agreement')}">
                            ${t(agreement?.title ?? 'Untitled Agreement')}
                        </h2>
                    `;

                const agreementItem = `
                    <div
                        class="agreement-item ${agreement.skeleton ? 'skeleton-section' : ''}"
                        data-id="${agreement.agreementid}"
                    >
                        <div class="header">
                            <div class="preview-html skeleton-section"></div>
                        </div>
                        <div class="body">
                            <div>${fileIcon}</div>
                            <div style="overflow: hidden; flex-grow: 1;">
                                ${title}
                                ${dateEffective}
                                <div class="d-flex gap-1 align-start">
                                    ${signatureItem}
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $("#contracts-container").append(agreementItem);
                $(`#contracts-container .agreement-item[data-id=${agreement.agreementid}]`).on('click', () => {
                    !agreement.skeleton && viewAgreement(agreement)
                });

                const attachThumbnail = (thumbnail) => {
                    $(`.agreement-item[data-id=${agreement.agreementid}] .header`).html(`<img src="${thumbnail}" />`);
                    $(`.agreement-item[data-id=${agreement.agreementid}] .header`).removeClass('skeleton-section');
                }

                if (agreement?.agreementAttachments?.length > 0 && agreementThumbnailCache[agreement.agreementid]) {
                    attachThumbnail(agreementThumbnailCache[agreement.agreementid]);
                } else if (agreement?.agreementAttachments?.length > 0) {
                    getPdfThumbnail(agreement.agreementAttachments[0].fileURL)
                        .then((thumbnail) => {
                            agreementThumbnailCache[agreement.agreementid] = thumbnail;
                            setSessionStorage('agreementThumbnailCache', JSON.stringify(agreementThumbnailCache));
                            attachThumbnail(thumbnail);
                        })
                        .catch((err) => {
                            console.error(err);
                            $(`.agreement-item[data-id=${agreement.agreementid}] .header`)
                                .html(`<div class="preview-html">No Preview</div>`)
                                .removeClass('skeleton-section');
                        });
                } else if (agreement?.agreementHtml) {
                    $(`.agreement-item[data-id=${agreement.agreementid}] .preview-html`)
                        .html(`<iframe data-hj-allow-iframe=""></iframe>`)
                        .removeClass('skeleton-section');

                    const iframe = $(`.agreement-item[data-id=${agreement.agreementid}] .preview-html > iframe`)[0];
                    writeOnIframe(iframe, agreement.agreementHtml, `body { font-family: Arial, Helvetica, sans-serif; padding: 1rem; zoom: 0.5; overflow: hidden }`);
                } else {
                    $(`.agreement-item[data-id=${agreement.agreementid}] .preview-html`).removeClass('skeleton-section');
                }
            });
        }
    }

    function viewAgreement(agreement) {
        var signedBadges = '', agreementSigned;
        if(agreement.requiredAcknowledgement !== 'none') {
            if (agreement.signedBy == undefined) {
                agreementSigned = false;
            } else {
                if(agreement.signedBy.agreed == undefined || agreement.signedBy.agreed == false) {
                    agreementSigned = false;
                } else {
                    agreementSigned = true;
                }
            }

            if (agreementSigned) {
                signedBadges = `<span class="badge badge-blue">${t('Signed on')}: ${ moment(agreement.signedBy.signedDate * 1000).format('DD MMM YYYY') }</span>`;
            } else if (agreement.deleted) {
                signedBadges = `<span class="badge badge-grey">${t('Archived')}</span>`;
            } else {
                signedBadges = `<span class="badge badge-red">${t('Requires Action')}</span>`;
            }
        } else {
            agreementSigned = true;
        }

        const badges = `
            <span class="badge badge-grey">${t('Effective From')}: ${ moment(agreement.dateEffective * 1000).format('DD MMM YYYY')}</span>
            ${signedBadges}
        `

        const agreementsDialog = `
            <div class="view-agreement-dialog ph-dialog-v2">
                <div class="body">
                    <div style="position: relative; margin-bottom: 32px;">
                        <ul class="nav nav-tabs" role="tablist"></ul>
                        <div class="badges">${badges}</div>
                    </div>
                    <div class="tab-content"></div>
                </div>
            </div>
        `;

        const confirmButtonText = loadAgreementID
            ? t('Continue to the Performance Hub')
            : agreementSigned
            ? t('Download File')
            : t('Proceed');

        const cancelButtonText = !agreementSigned && !agreement.deleted
            ? t('Cancel')
            : t('Close');

        const showCloseButton = agreementSigned || !(loadAgreementID && agreement.dateEffective <= moment().unix());

        swalWithBootstrapButtons.fire({
            html: agreementsDialog,
            title: t(agreement?.title ?? 'Untitled Agreement'),
            width: 800,
            showCloseButton: showCloseButton,
            showConfirmButton: true,
            showCancelButton: showCloseButton,
            confirmButtonText,
            cancelButtonText,
            allowEscapeKey : showCloseButton,
            allowOutsideClick: showCloseButton,

            onBeforeOpen: () => {

                if (!agreementSigned && !agreement.deleted && agreement.requiredAcknowledgement === 'checkbox') {                    
                    $('.view-agreement-dialog').append(`

                        <div class="form-check d-flex justify-center align-center mb-1 styled-checkbox rounded">
                            <input id="i-agree-checkbox" class="filled-in chk-col-blue" type="checkbox">
                            <label for="i-agree-checkbox" class="checkbox-label">${t('I have read and agree to the agreement outlined above')}</label>
                        </div>
                    `);

                    $('.swal2-confirm').attr('disabled', true);

                    $('#i-agree-checkbox').on('change', (e) => {
                        const checked = $(e.currentTarget).is(':checked');
                        $('.swal2-confirm').attr('disabled', !checked);
                    });
                }

                $('.close-btn').on('click', () => {
                    loadAgreementID = false;
                    Swal.close();
                    sAgreementCB?.();
                });

                if(agreement.agreementAttachments !== undefined) {
                    agreement.agreementAttachments.forEach(function (attachment, item) {
                        const fileName =  (attachment?.fileName ?? '').trunc(35);
                        $(".view-agreement-dialog .nav-tabs").append(`
                            <li role="presentation" class="agreement-attachment-tabs"><a href="#attachment-${item}" aria-controls="attachment-${item}" role="tab" data-toggle="tab">${fileName}</a></li>
                        `);

                        $(".view-agreement-dialog .tab-content").append(`
                            <div role="tabpanel" class="tab-pane" id="attachment-${item}">
                                <div class="agreement-pdfobj" id="attachment-obj-${item}"></div>
                            </div>
                        `);

                        var options = {
                            fallbackLink: "<iframe data-hj-allow-iframe='' class='fallback-agreement-iframe' src='[url]#zoom=50'></iframe>",
                            pdfOpenParams: {
                                pagemode: "thumbs",
                                navpanes: 0,
                                toolbar: 0,
                                statusbar: 0,
                                // view: "FitV"
                            }
                        };
                        var attachmentItem = PDFObject.embed((attachment == undefined || attachment.fileURL == undefined) ? "" : attachment.fileURL, `#attachment-obj-${item}`, options);

                    });
                }

                if(agreement.agreementHtml !== undefined && agreement.agreementHtml !== "") {
                    $(".view-agreement-dialog .nav-tabs").append(`
                        <li role="presentation" class="agreement-attachment-html-tabs"><a href="#attachment-html" aria-controls="attachment-html" role="tab" data-toggle="tab">${ (!agreement?.agreementHtmlTitle) ? t("Please Read") : agreement.agreementHtmlTitle }</a></li>
                    `);
                    $(".view-agreement-dialog .tab-content").append(`
                        <div role="tabpanel" class="tab-pane " id="attachment-html"><iframe data-hj-allow-iframe="" class="agreement-iframe" id="agreementHTMLContent"></iframe></div>
                    `);

                    const iframe = $('#agreementHTMLContent')[0];
                    writeOnIframe(iframe, agreement.agreementHtml, 'body { font-family: Arial, Helvetica, sans-serif; padding: 1rem; }');
                }

                if(agreementSigned && agreement.requiredAcknowledgement !== 'none') {
                    $(".view-agreement-dialog .nav-tabs").append(`
                        <li role="presentation" class=""><a href="#signed-log" aria-controls="signed-log" role="tab" data-toggle="tab">${t('Document Log')}</a></li>
                    `);

                    $(".view-agreement-dialog .tab-content").append(`
                        <div role="tabpanel" class="tab-pane " id="signed-log"><div></div></div>
                    `);

                    renderSignedLog(agreement, '.view-agreement-dialog #signed-log');
                } else if (!agreement?.agreementHtml && !agreement?.agreementHtmlTitle && agreement?.agreementAttachments?.length < 1) {
                    $(".agreement-attachment-html-tabs").hide();
                }

                // Set first tab as visible...
                $(".nav-tabs li:first-child a").trigger( "click" );

            },
        }).then((response) => {
            if (agreementSigned && response.value) {
                return downloadAgreementWithSignature(agreement);
            }

            if (agreementSigned || !response.value) {
                return !appStarted && sAgreementCB?.();
            }

            if (agreement.requiredAcknowledgement === 'signature') {
                return signAgreementUI(agreement);
            }

            ownerOutstandingAgreements = ownerOutstandingAgreements.filter(
                (a) => !(a.agreementid === agreement.agreementid && a.club === selectedID)
            );
            
            lockOutSidebarAndBanner();

            const agreementObj = {
                club: selectedID,
                agreementid: agreement.agreementid,
                signatureData: null,
                signedBy: null,
                agreed: true,
            }
                
            postSignedAgreement(agreementObj);
            loadAgreementID = false;
            sAgreementCB?.();
        });
    }

    async function downloadAgreementWithSignature(agreement) {
        showLoadingDialog();

        // fix for html2canvas resolution issue: https://stackoverflow.com/questions/22803825/html2canvas-generates-blurry-images
        const scaleBy = 3;
        const canvasWidth = 595.28;
        const canvasHeight = 841.89;
        const canvasScale = 1;

        try {
            const pdf = new jspdf.jsPDF({
                orientation: 'portrait',
                unit: 'pt',
                format: 'a4'
            });

            if (agreement.agreementAttachments.length > 0) {
                await addAttachmentPages();
            } else {
                await addHTMLPages();
            }

            await addDocumentLogPage();
            Swal.close();
            instantNotification('Agreement with signature will be downloaded shortly', 'success', 5000);

            const pdfName = [
                agreement.title?.toLowerCase().split(' ').join('-') ?? 'untitled-agreement',
                selectedID,
                agreement.signedBy ? 'signed' : null,
                agreement.signedBy
                    ? moment.unix(agreement.signedBy.signedDate).format('DD-MM-YYYY')
                    : moment.unix(agreement.dateEffective).format('DD-MM-YYYY'),
            ].filter(x => x).join('-');

            pdf.save(`${pdfName}.pdf`);

            async function addDocumentLogPage() {
                if (!agreement.signedBy) return;
                pdf.addPage();
                const documentLogHeader = '<h2>Document Log</h2>';
                const logIframe = createIframe(documentLogHeader + renderSignedLog(agreement));
                const logCanvasBase = getCanvasForResolutionFix();
                const logCanvas = await html2canvas(logIframe.contentWindow.document.body, { canvas: logCanvasBase, scale: canvasScale });
                addCanvasToPDF(logCanvas);
                logIframe.remove();
            }

            function createIframe(htmlText) {
                const iframe = document.createElement('iframe');

                iframe.style.width = `${canvasWidth}px`;
                iframe.style.height = `${canvasHeight}px`;

                document.body.appendChild(iframe);
                writeOnIframe(iframe, htmlText, 'body { font-family: Arial, Helvetica, sans-serif; padding: 1rem; }');
                return iframe;
            }

            function getCanvasForResolutionFix() {
                const canvas = document.createElement('canvas');
                canvas.width = canvasWidth * scaleBy;
                canvas.height = canvasHeight * scaleBy;
                canvas.style.width = `${canvasWidth}px`;
                canvas.style.height = `${canvasHeight}px`;
                const ctx = canvas.getContext('2d');
                ctx.scale(scaleBy, scaleBy);
                return canvas;
            }

            function addCanvasToPDF(canvas) {
                const imgData = canvas.toDataURL('image/png');
                return pdf.addImage(imgData, 'PNG', 0, 0, canvas.width / scaleBy, canvas.height / scaleBy);
            }

            async function addAttachmentPages() {
                const pdfDoc = await pdfjsLib.getDocument(agreement.agreementAttachments[0].fileURL).promise;

                const unOrderedCanvases = Array(pdfDoc.numPages).fill(null).map(async (_, index) => {
                    const pageIndex = index + 1;
                    const page = await pdfDoc.getPage(pageIndex);
                    const canvas = getCanvasForResolutionFix();
                    const ctx = canvas.getContext('2d');
        
                    const viewport = page.getViewport({ scale: scaleBy });
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
        
                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport
                    };
                    
                    await page.render(renderContext).promise;
                    return [pageIndex, canvas];
                });

                const canvases = await Promise.all(unOrderedCanvases);
                const sortedCanvases = canvases.sort(([a], [b]) => a - b).map(([, canvas]) => canvas);
                sortedCanvases.forEach((canvas, index) => {
                    index !== 0 && pdf.addPage();
                    addCanvasToPDF(canvas);
                });
            }
        
            async function addHTMLPages() {
                const docIframe = createIframe(agreement.agreementHtml);
                const canvas = await html2canvas(docIframe.contentWindow.document.body, { scale: 1.5 });

                const pageHeight = pdf.internal.pageSize.getHeight() + 200;
                const pageWidth = pdf.internal.pageSize.getWidth();
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;

                let y = 0;

                while (y < imgHeight) {
                    const canvasSlice = document.createElement('canvas');
                    canvasSlice.width = imgWidth;
                    canvasSlice.height = Math.min(pageHeight, imgHeight - y);
                    const sliceContext = canvasSlice.getContext('2d');
                  
                    // Draw the segment of the main canvas onto the slice
                    sliceContext.drawImage(canvas, 0, y, imgWidth, canvasSlice.height, 0, 0, imgWidth, canvasSlice.height);
                  
                    // Convert canvas slice to image
                    const imageData = canvasSlice.toDataURL('image/jpeg');
                    y !== 0 && pdf.addPage();

                    // Add image to PDF
                    pdf.addImage(imageData, 'JPEG', 0, 50, pageWidth, canvasSlice.height * (pageWidth / imgWidth));
                    y += pageHeight;
                  }

                docIframe.remove();
            }
        } catch (error) {
            console.error(error);
            instantNotification('Failed to download agreement with signature', 'error', 5000);
        }

        function showLoadingDialog() {
            swalWithBootstrapButtons.fire({
                width: 500,
                html: `
                    <h3>Generating PDF...</h3>
                    <div class="preloader pl-size-xl" style="padding: 10px">
                        <img src="/assets/images/tail-spin.svg" />
                    </div>
                `,
                showCloseButton: false,
                showCancelButton: false,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
            });
        }
    }

    function renderSignedLog(agreement, parentElement) {
        if (!agreement.signedBy) return '';

        // css needs to be added to the iframe to render the html correctly
        const containerStyle = 'display: grid; grid-template-columns: 1fr 3fr; grid-gap: 1rem; grid-auto-rows: max-content;';
        const labelStyle = 'font-size: 11px; font-weight: 600px; line-height: 20px; text-transform: uppercase; color: #6A7383; text-align: left;';
        const valueStyle = 'color: #6A7383; font-size: 16px; font-weight: 400; line-height: 24px;';
        
        let collection = `<div style="${containerStyle}">`;
        [
            ['SIGNED BY', agreement.signedBy.signedBy],
            ['ON BEHALF OF', agreement.signedBy.club],
            ['ACKNOWLEDGED ON', moment(agreement.signedBy.signedDate * 1000).format('DD MMM YYYY')],
            ['PERFORMANCE HUB ACCOUNT', agreement.signedBy.signedEmail],
            ['LOGGED IP ADDRESS', agreement.signedBy.signedIP],
            ['SIGNATURE', agreement.signedBy.signatureData ? `<img src="${agreement.signedBy.signatureData}">` : null],
        ]
        .filter(([label, value]) => value !== null)
        .forEach(([label, value]) => {
            const htmlText = `<div class="label" style="${labelStyle}">${t(label)}</div><div class="value" style="${valueStyle}">${value}</div>`;
            collection += htmlText;
        });
        collection += '</div>';
        parentElement && $(parentElement).append(collection);
        return collection;
    }

    function signAgreementUI(agreement) {
        const agreementsDialog = `
            <div class="sign-agreement-dialog ph-dialog-v2">
                <div class="body">
                    <div class="row">
                        <div class="col-sm-12 disclaimer-text">
                            ${t('By digitally signing & accepting this screen, you hereby agree to the agreement titled:')} &quot;<u>${ (agreement == undefined || agreement.title == undefined) ? t("Untitled Agreement") : agreement.title }</u>&quot; ${t('and any additional provided attachments/appendices supplied.')}
                        </div>
                    </div>
                    <hr />
                    <div class="d-flex gap-2 align-center" style="margin-bottom: 1rem;">
                        <span class="">
                            ${t('Full Name')}:
                        </span>
                        <span class="grow-1">
                            <div class="form-group" style="margin-bottom: 0px;">
                                <div class="form-line">
                                    <input class="form-control momospace" id="signedName" type="text" value="" placeholder="${t('Type your First, Middle, and Surname')}">
                                </div>
                            </div>
                        </span>
                    </div>
                    <div class="" style="margin-bottom: 10px;">
                        <div class="signature-col">
                            ${t('Signature: <i>(Sign with your mouse or finger if using touch device)</i>')}
                        </div>
                        <div class="">
                            <div class="signature-pad-body" id="signature-pad">
                                <canvas></canvas>
                            </div>
                            <button type="button" class="resetSignature btn btn-xs btn-default waves-effect">${t('Clear Signature')}</button>
                        </div>
                    </div>
                    <div class="signing-metadata">
                        <div class="">
                            ${t('Signed On')}:
                        </div>
                        <div class="">
                            <span class="signedOnDate momospace"></span>
                        </div>
                    </div>
                    <div class="signing-metadata">
                        <div class="">
                            ${t('IP Address')}:
                        </div>
                        <div class="">
                            <span class="signedIP momospace"></span>
                        </div>
                    </div>
                    <div class="signing-metadata">
                        <div class="">
                            ${t('Logged User')}:
                        </div>
                        <div class="">
                            <span class="signedInUser momospace"></span>
                        </div>
                    </div>
                    <div>
                    <div class="signing-metadata">
                        <div class="">
                            ${t('On Behalf Of')}:
                        </div>
                        <div class="">
                            <span class="signingForClub momospace"></span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        let signatureData, agreementObj;
        swalWithBootstrapButtons.fire({
            title: t('Sign Agreement'),
            html: agreementsDialog,
            // width: '80vw',
            showCloseButton: false,
            showCancelButton: true,
            allowEscapeKey : false,
            confirmButtonText: t('Submit Signature'),
            cancelButtonText: t('Cancel'),
            // heightMax: 300,
            allowOutsideClick: false,

            onBeforeOpen: () => {
                // Agreements JS Elements...
                let wrapper = document.getElementById("signature-pad");
                let canvas = wrapper.querySelector("canvas");

                window.onresize = resizeCanvas;
                // when using devicePixelRatio, the size is correct, but the drawing is inaccurate, see #514 https://github.com/szimek/signature_pad/issues/514
                resizeCanvas();
                let signaturePad = new SignaturePad(canvas);

                canvas.width = '406';
                canvas.height = '150';

                $(".resetSignature").on('click', () => {
                    signaturePad.clear();
                    validate();
                });

                $("#signedName").on('input', validate);
                signaturePad.addEventListener('endStroke', validate);

                $(".signedOnDate").html(moment().format('DD MMM YYYY'));
                $(".signedInUser").html(signedinUser);
                $(".signingForClub").html(`${selectedBrand} ${selectedID}`);
                
                validate();
                getUserIP(function(ip) {
                    $(".signedIP").html(ip);
                })

                function resizeCanvas() {
                    // When zoomed out to less than 100%, for some very strange reason,
                    // some browsers report devicePixelRatio as less than 1
                    // and only part of the canvas is cleared then.
                    var ratio =  Math.max(window.devicePixelRatio || 1, 1);
                    canvas.width = canvas.offsetWidth * ratio;
                    canvas.height = canvas.offsetHeight * ratio;
                    canvas.getContext("2d").scale(ratio, ratio);
                }
                function validate() {
                    var isValid = true;
                    if(! $("#signedName").val()) isValid = false;
                    if(signaturePad.isEmpty()) isValid = false;

                    if(isValid) {
                        $('.swal2-actions .swal2-confirm').prop('disabled', false);
                        signatureData = getSignatureDataSVG()
                    } else {
                        $('.swal2-actions .swal2-confirm').prop('disabled', true);
                    }

                }
                function getSignatureDataSVG() {
                    var svgStr = atob(signaturePad.toDataURL('image/svg+xml').replace(/^[^,]+,/, ''));
                    svgStr = svgStr.replace(/(viewBox)="[^"]+"/i, '$1="0 0 ' + canvas.width + ' ' + canvas.height + '"');
                    svgStr = svgStr.replace(/(<svg[^>]+width)="[^"]+"/i, '$1="' + canvas.width + '"');
                    svgStr = svgStr.replace(/(<svg[^>]+height)="[^"]+"/i, '$1="' + canvas.height + '"');
                    return 'data:image/svg+xml;base64,' + btoa(svgStr);
                }
            },
            onClose: () => {
                agreementObj = {
                    club: selectedID,
                    agreementid: agreement.agreementid,
                    signatureData: signatureData,
                    signedBy: $("#signedName").val(),
                    agreed: true,
                }
            }
        }).then(response => {
            // Clicked sign
            if (response.value && response.value == true) {
                instantNotification(
                    // Message...
                    `${t('Signature details submitted for agreement:')} ${ (agreement == undefined || agreement.title == undefined) ? t("Untitled Agreement") : agreement.title }`
                );
                postSignedAgreement(agreementObj)

                ownerOutstandingAgreements = ownerOutstandingAgreements.filter(
                    (a) => !(a.agreementid === agreement.agreementid && a.club === selectedID)
                );
                
                lockOutSidebarAndBanner();
            }

            loadAgreementID = false;
            sAgreementCB?.()
        });

    }

    function postSignedAgreement(agreementObj) {

        var req = $.ajax({
            url: `/api/agreements/sign/${agreementObj.club}/${agreementObj.agreementid}`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(agreementObj),
            success: function(data){
                if((data === undefined) ? false : data !== false) {
                    getAllAgreements();
                } else {

                    instantNotification(
                        // Message...
                        t('<b>FAILED TO UPDATE AGREEMENT!</b><br><br>Could not save signature at this time. Please contact HQ for assistance.'),
                        // Type...
                        'error',
                        // Timeout (1m)...
                        60000
                    );
                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
                instantNotification(
                    // Message...
                    t('<b>FAILED TO UPDATE AGREEMENT!</b><br><br>Could not save signature at this time. Please contact HQ for assistance.'),
                    // Type...
                    'error',
                    // Timeout (1m)...
                    60000
                );

            }
        });

    }


    function getUserIP(callback) {
        var req = $.ajax({
            url: `https://uapi.gymsystems.co/api/v1/geo/ip`,
            method: 'GET',
            contentType: 'application/json',
            success: function(data){
                if((data === undefined) ? false : data !== false) {
                    callback(data.clientInformation.source_ip)
                } else {
                    callback("")
                }
            },
            error: function(xhr, textStatus, errorThrown){
                console.error(xhr, textStatus, errorThrown);
                callback("");
            }
        });
    }

}

function sCheckOutstandingAgreements(callback) {
    var req = $.ajax({
        url: '/api/agreements/agreements-outstanding',
        method: 'GET',
        contentType: 'application/json',
        success: (data) => {
            ownerOutstandingAgreements = data.filter(a => {
                return a.requiredAcknowledgement !== 'none' && a.dateEffective <= moment().unix();
            });

            lockOutSidebarAndBanner();

            if (data.length < 1) {
                return callback();
            } else if (data.length >= 2) {
                handleChangeClub(data[0].club);
                routeToAgreementsPage();
                showMultipleAgreementsOutstandingMessage(data, callback);
            } else if (data.length == 1) { 
                handleChangeClub(data[0].club);
                sAgreements(data[0].agreementid, callback)
                return callback();
            }
        },
        error: function(xhr, textStatus, errorThrown){
            console.error(xhr, textStatus, errorThrown);
        }
    });

    function renderManagerNoticeDialog() {
        let emails = ownerOutstandingAgreements.reduce((acc, agreement) => {
            acc = [...acc, ...agreement.owners];
            return acc;
        }, []);

        emails = Array
            .from(new Set(emails))
            .map((email) => `<a href="mailto:${email}">${email}</a>`)
            .join(', ');

        const html = `
            <div class="ph-dialog-v2">
                <div class="body">
                    <p>${t('Your access to the platform is temporarily restricted as the account owner has an outstanding agreement that requires acknowledgement.')}</p>
                    <p>${t('To maintain system access')}, ${t('please request the account owner at')} ${emails} ${t('to log in and acknowledge the agreement.')}</p>
                    <p>${t('We apologize for any inconvenience and appreciate your cooperation.')}</p>
                    <p>${t('Thank you.')}</p>
                </div>
            </div>
        `;

        swalWithBootstrapButtons.fire({
            title: t('Temporary Account Restriction'),
            html,
            width: 500,
            showCloseButton: true,
            showConfirmButton: true,
            showCancelButton: false,
            allowEscapeKey : false,
            allowOutsideClick: false,
            confirmButtonText: t('Close'),
        });
    }

    function routeToAgreementsPage() {
        firstLoad = true; sammy.quiet = false;
        sammy.setLocation("/agreements");
        firstLoad = false;
    }

    function showMultipleAgreementsOutstandingMessage(clubs, cb) {
        let clubList = ''
        clubs.forEach(function(club) {
            clubList += `<li><b>${club.club}</b>: ${club.title} - Action By: ${moment(club.dateEffective * 1000).format('DD MMM YYYY')}</li>`
        });

        swalWithBootstrapButtons.fire({
            title: 'Outstanding Agreements',
            html: `
                You have multiple agreements outstanding for one or more of your clubs which require immediate action:
                <hr>
                <ul style="text-align: left;">
                    ${clubList}
                </ul>
                <hr>
                Please View &amp; Sign these agreements for each of your clubs to proceed.
            `,
            width: 500,
            showCloseButton: false,
            showConfirmButton: false,
            showCancelButton: true,
            allowEscapeKey : false,
            cancelButtonText: t('Continue'),
            allowOutsideClick: false

        }).then(cb);
    }
}