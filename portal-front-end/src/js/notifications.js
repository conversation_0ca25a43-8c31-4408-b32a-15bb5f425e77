let sidebarNotifications = [];

// this will apply click event to each notification in the list
$(document).on('click', '.notif-row', (e) => {
    const notifId = $(e.currentTarget).find('.notification-item').data('notification-id');
    const notification = [...notificationMasterList, ...sidebarNotifications].find(message => (message.id === notifId));
    if (notification) userViewedNotification(notification);
});

$(document).on('click', '.notification-view-all-btn', (e) => {
    e.preventDefault();
    toggleNotificationBar(false);
    sammy.quiet = false;
    sammy.setLocation('/notifications');
});

// show who has seen the announcement
$(document).on('click', '.notif-views', (e) => {
    e.stopPropagation();
    const { rowId } = $(e.currentTarget).data();
    whoViews(rowId);
});

function sHeaderNotifications() {

        // get the notification categories from api
        uniApiConCall('/api/notification/categories')
        .then(({ data }) => {
            if (Array.isArray(data)) {
                notificationCategories = data;
            }
        }).catch(err => {
            instantNotification('Error in notification categories', 'error');
        });
}

function toggleNotificationBar(open = null) {
    const { notifications: notifs } = userPreferences;
    const isOpen = $('.notifications-side-menu').hasClass('open');
    open = typeof open === 'boolean' ? open : !isOpen;
    const pinned = _get(notifs, 'pinned', false);
    const smallScreen = $(window).width() < 991;

    if (open && !isOpen) {
        $('.notifications-side-menu').addClass('open');
        if ($(window).width() < 768) {
            $('body').addClass('swal2-shown');
        } else {
            $('body').removeClass('swal2-shown');
        }
    } else if (!open && isOpen && (!pinned || smallScreen)) {
        $('.notifications-side-menu').removeClass('open');
        $('body').removeClass('swal2-shown');
    }
}

// render notification in ride side bar
const renderSideBarNotificationList = (data) => {
    updateNotificationBell(data);
    if (data && Array.isArray(data) && data.length) {
        $('#notifications-right-side-list').empty();
        data
            .forEach((notif, index) => {
                $('#notifications-right-side-list').append(getNotificationRow(index, notif, true));
            });
    } else {
        $('#notifications-right-side-list').empty().append(`
            <div style="text-align: center; padding: 1.5rem;">
                There are no notifications for now
            </div>
        `);
    }
}

const getNotificationRow = (index, row, sidebar=false) => {
    const thumbnail = 'data' in row ? row.data.thumbnail : null;
    const timestamp = 'created' in row ? Math.floor(row.created * 1000) : Date.now();
    const truncateName = row.name.length > 30 ? `${row.name.substring(0, 30).trim()}...` : row.name;
    const category = 'category' in row ? row.category : '';

    let viewers = 'viewers' in row ? row.viewers : [];
    let unread = !viewers.includes(signedinUser);

    return (`
      <div class="notif-row ${unread ? 'notif-unread-background' : ''} ${sidebar === false ? "main-notif-listItem" : ""}" data-row-index=${index}>
        <div class="d-flex gap-1" style="width: 100%;">
          <div class="flex-column" style="flex-grow: 1;">
            <div class="notification-item notif-title ${unread ? 'notif-unread-text' : ''}" data-notification-id=${row.id}>
              ${sidebar ? truncateName : row.name}
            </div>
            <p style="color: #898989">${row.snippet || ''}</p>
            <div class="d-flex flex gap-1 notif-time-origin">
              <span>${timeAgo(timestamp)}</span>
              <span style="font-size: 5px;"><i class="fa fa-circle" aria-hidden="true"></i></span>
              <span>${notificationSourceNames(category)}</span>
            </div>
            ${row.target === 'clubs' && viewers.length > 0 ? `
              <div class="notif-views" data-row-id="${row.id}">
                <span style="margin-right: 5px;">
                  <i class="fa fa-eye" aria-hidden="true"></i>
                </span>
                <span>${viewers.length} views</span>
              </div>
            ` : ''}
          </div>
          ${thumbnail ? `
            <div class="notif-thumbnail">
              <img src="${thumbnail}" />
            </div>
          ` : ''}
          ${unread ? `
            <div class="flex-column">
              <span class="notif-unread-dot">
                <i class="fa fa-circle" aria-hidden="true"></i>
              </span>
            </div>
          ` : ''}
        </div>
      </div>
    `);
}

const fetchSideBarNotifications = async (clubId) => {
    let listEntries = [];

    $('.navbar-notifications-container').addClass('loading');
    $('.notifications-side-menu').addClass('loading');
    // /** get club info if empty or null **/
    // if (!clubDataInfo) {
    //     clubDataInfo = await getClubInformation(clubId);
    //     clubRegion = getClubRegion(clubDataInfo);
    // }

    let queryString = {
        startDate: filterStartDateUnix,
        endDate: filterEndDateUnix,
    };

    // let clubRegionQuery = encodeURIComponent(JSON.stringify([clubRegion.name]));

    const notifQuery = async (url, lastKey) => {
        const response = await uniApiConCall(url + (lastKey ? `&LastEvaluatedKey=${JSON.stringify(lastKey)}` : ''));
        if (_get(response, 'status') !== 'success') return;
        if (!_get(response, 'data.Items')) return;
        listEntries = [...listEntries, ...response.data.Items]
        
        const nextKey = _get(response, 'data.LastEvaluatedKey');
        const isSameKey = _get(lastKey, 'id') === _get(nextKey, 'id');
        const nextKeyCreatedAt = _get(nextKey, 'created');
        const nextKeyIsAfterStartDate = nextKeyCreatedAt && nextKeyCreatedAt > queryString.startDate;

        if (nextKey && !isSameKey && nextKeyIsAfterStartDate) {
            await notifQuery(url, nextKey);
        }
    }

    const [clubNotif, usersNotif, regionNotif, globalNotif] = await Promise.all([
        notifQuery(`${notifApiBaseUri}/${clubId}${stringifyQueryStringsObject(queryString)}&target=clubs`),
        notifQuery(`${notifApiBaseUri}/${clubId}${stringifyQueryStringsObject(queryString)}&target=users`),
        notifQuery(`${notifApiBaseUri}/${clubId}${stringifyQueryStringsObject(queryString)}&target=region`),
        notifQuery(`${notifApiBaseUri}/${clubId}${stringifyQueryStringsObject(queryString)}&target=global`),
    ]);

    $('.navbar-notifications-container').removeClass('loading');
    $('.notifications-side-menu').removeClass('loading');
    $('#notifications-list').LoadingOverlay("hide");

    if (clubNotif && 'status' in clubNotif && clubNotif.status == 'success') {
        const { data } = clubNotif
        const clubItems = 'Items' in data ? data.Items : null;
        listEntries = [...listEntries, ...clubItems];
    }

    if (usersNotif && 'status' in usersNotif && usersNotif.status == 'success') {
        const { data } = usersNotif
        const userItems = 'Items' in data ? data.Items : null;
        listEntries = [...listEntries, ...userItems];
    }

    if (regionNotif && 'status' in regionNotif && regionNotif.status == 'success') {
        const { data } = regionNotif
        const regionItems = 'Items' in data ? data.Items : null;
        listEntries = [...listEntries, ...regionItems];
    }

    if (globalNotif && 'status' in globalNotif && globalNotif.status == 'success') {
        const { data } = globalNotif
        const globalItems = 'Items' in data ? data.Items : null;
        listEntries = [...listEntries, ...globalItems];
    }

    sidebarNotifications = listEntries.sort((a, b) => {
        return new Date(b.created) - new Date(a.created);
    });

    renderSideBarNotificationList(sidebarNotifications, clubId);
}

// This will update the user preferences under notifications
function notificationSettings() {
    const showNotificationSettings = () => {
        const inputElement = (conf) => {
            return `
                <div class="col-sm-8 text-left" style="margin: 1rem 0;">
                    <span style="
                        font-family: 'Source Sans Pro', sans-serif;
                        font-style: normal;
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 15px;
                        display: flex;
                        align-items: flex-end;
                        color: #1C2A4B;
                    ">${t(conf.name)}</span>
                </div>
                <div class="col-sm-4" style="margin: 1rem 0;">
                    <div class="demo-switch">
                        <div class="switch">
                            <label>
                                <input id="${conf.source}" data-hj-allow type="checkbox" ${conf.enabled ? 'checked' : '' } />
                                <span class="lever lever switch-col-blue"></span>
                            </label>
                        </div>
                    </div>
                </div>
            `;
        }

        let muted = [];
        if (
            userPreferences
            && 'notifications' in userPreferences
            && 'muted' in userPreferences.notifications
        ) {
            muted = userPreferences.notifications.muted;
        }

        const html = document.createElement("div");
        const config1 = notificationCategories.slice(0, 4).map(item => ({...item, enabled: !muted.includes(item.source)}));
        const config2 = notificationCategories.slice(4, 8).map(item => ({...item, enabled: !muted.includes(item.source)}));
        const configLeft = config1.map(conf => (inputElement(conf)));
        const configRight = config2.map(conf => (inputElement(conf)));
        const elements = `
            <div class="ph-dialog-v2" style="padding: 20px;">
                <div class="alert alert-blue">
                    ${t('Configure which functionality/modules in the Performance Hub you would like to receive notifications from')}
                </div>
                <div id="notification-settings-checkbox" class="d-flex gap-2">
                    <div style="width: 50%; padding: 20px;">
                        <div class="row">${configLeft.join('')}</div>
                    </div>
                    <div style="width: 50%; padding: 20px;">
                        <div class="row">${configRight.join('')}</div>
                    </div>
                </div>
            </div>
        `;
        $(html).html(elements);

        swalWithBootstrapButtons.fire({
            title: t('Notification Settings'),
            html,
            showCloseButton: true,
            animation: false,
            width: '550px',
            showCancelButton: true,
            showConfirmButton: true,
            allowEscapeKey : false,
            confirmButtonText: t('Save/Update'),
            cancelButtonText: t('Cancel')
        }).then(async (response) => {
            if (response.value) {
                let notifMuted = [];
                const configuration = [...config1, ...config2];
                // get the ids of checkbox that is checked
                configuration.forEach(config => {
                    let element = $(html).find(`#${config.source}`);
                    if (!element.is(':checked')) {
                        notifMuted.push(config.source);
                    }
                });
                let notifSettings = {};
                if ('notifications' in userPreferences) {
                    notifSettings = userPreferences.notifications;
                }
                let params = {
                    notifications: {
                        ...notifSettings,
                        muted: notifMuted,
                    }
                }
                await updateUserPreferences(params);
                fetchSideBarNotifications(selectedID);
                fetchNotificationsByFilter();
            }
        });
    }

    showNotificationSettings();
}

// This will show who view the message within the club
function whoViews(notifId) {
    const showWhoViews = (id) => {
        const viewers = (row) => {
            return `
                <div class="d-flex gap-1" style="
                    border-bottom: 1px solid #eee;
                    justify-content: space-between;
                ">
                    <div style="margin: 1rem 0;">
                        <span style="
                            font-family: 'Source Sans Pro', sans-serif;
                            font-style: normal;
                            font-weight: 700;
                            font-size: 14px;
                            line-height: 18px;
                            display: flex;
                            align-items: flex-end;
                            color: #1C2A4B;
                        ">${row.email}</span>
                    </div>
                    <div style="margin: 1rem 0; text-align: right;">
                        ${timeAgo(row.created * 1000)}
                    </div>
                </div>
            `;
        }
        const html = document.createElement("div");
        const contentElements = `
            <fieldset style="height: 300px; overflow-y: scroll;">
                <legend>Viewers</legend>
                <div id="viewer-list" style="padding: 16px;"></div>
            </fieldset>
        `;

        $(html).addClass('views-info-dialog');
        $(html).html(contentElements);
        swalWithBootstrapButtons.fire({
            html,
            showCloseButton: true,
            animation: false,
            width: '480px',
            showCancelButton: true,
            showConfirmButton: false,
            allowEscapeKey : false,
            cancelButtonText: `Close`,
            onBeforeOpen: () => {
                // get the consumed viewers data and append it
                let url = `/api/club/notification/consumed/${selectedID}?notificationId=${id}`;
                uniApiConCall(url)
                .then(response => {
                    const { status } = response;
                    if (status === 'success' && 'data' in response) {
                        const viewRows = response.data.map(row => (viewers(row)));
                        if (response.data.length) {
                            $(html).find('#viewer-list').empty().append(viewRows.join(''));
                        } else {
                            $('#viewer-list').empty().append(`
                                <div class="row">
                                    <div class="col-sm-12">
                                    <center>
                                        <h4>No viewers found.</h4>
                                    </center>
                                    </div>
                                </div>
                            `);
                        }
                    } else {
                        $('#viewer-list').empty().append(`
                            <div class="row">
                                <div class="col-sm-12">
                                <center>
                                    <h4>No viewers found.</h4>
                                </center>
                                </div>
                            </div>
                        `);
                    }
                })
                .catch(err => {
                    console.log('Error fetching the consumed notifications', err);
                })
            },
        })
        .then(async (response) => {
            if (response.value) {}
        });
    }

    showWhoViews(notifId);
}

/**
 * This will update notification listing.
 * The notification will trigger base on user preferences
 * TODO: need to refactor
 **/
 const notificationReceived = async (data=null) => {
    if (data) {
        let muted = [];
        let notificationPreferences = [];
        let title = 'New Notification';
        let category = '';
        let target = 'target' in data ? data.target : 'global';
        let targetReference = 'targetReference' in data ? data.targetReference : [];

        clubDataInfo = await getClubInformation(selectedID);
        clubRegion = getClubRegion(clubDataInfo);

        if (target === 'clubs' && !targetReference.includes(selectedID)) {
            return;
        }

        if (target === 'users' && !targetReference.includes(signedinUser)) {
            return;
        }

        if (target === 'region' && clubRegion && !targetReference.includes(clubRegion.code)) {
            return;
        }

        if ('name' in data) {
            title = data.name;
        }
    
        if ('category' in data) {
            category = data.category;
        }
    
        const response = await getUserPreferencesAPI();
        if (response[0]) {
            if ('notifications' in response[1]) {
                notificationPreferences = response[1].notifications;
                if ('muted' in notificationPreferences) {
                    muted = notificationPreferences.muted;
                }
            }
        }
    
        if (muted.includes(category)) return;

        instantNotification(title, 'success', 5000);

        await resetDateRangeUnix(); // reset the timestamp so the end date will be refreshed
        fetchSideBarNotifications(selectedID);
    }
}

/** just to update the counter in bell **/
const updateNotificationBell = (dataList=[]) => {
    let notices = [];
    dataList.forEach(notifMaster => {
        let viewers = 'viewers' in notifMaster ? notifMaster.viewers : [];
        if (!viewers.includes(signedinUser)) {
            notices.push(notifMaster.id)
        }
    });

    /** unread counter **/
    let unread = [...new Set(notices)];
    $(".navbar-notifications-container .label-count").html(`${unread.length < 10 ? unread.length < 1 ? '' : unread.length : 9 + '+'}`);
}

// pinned notification window
function pinnedNotification() {
    let pinned = true;
    let params = {...userPreferences};
    if ('notifications' in params) {
        pinned = 'pinned' in params.notifications ? !params.notifications.pinned : true;
        params = {
            ...params,
            notifications: {
                ...params.notifications,
                pinned
            }
        }
    } else {
        params = {
            ...params,
            notifications: {
                pinned,
                muted: []
            }
        }
    }

    if (pinned) {
        $('#notification-pin').addClass('fa-rotate-90');
        $('#notif-bar-close').css('opacity', 0.4);
    } else {
        $('#notification-pin').removeClass('fa-rotate-90');
        $('#notif-bar-close').css('opacity', 1);
    }

    updateUserPreferences(params);
}

/** update the views **/
const userViewedNotification = async (notification) => {
    const notificationId = notification.id;
    const data = notification && 'data' in notification ? notification.data : {};
    const target = notification && 'target' in notification ? notification.target : '';

    const processView = async () => {
        const viewerList = notification && 'viewers' in notification ? notification.viewers : [];
        const newViewersList = [...new Set([...viewerList, signedinUser])];

        if (newViewersList.some(viewer => !viewerList.includes(viewer))) {
            loadingOverlay('#notifications-list');
            uniApiConCall(`/api/club/notification/consumed/${selectedID}`, 'post', { notificationId, target });

            const viewersData = {
                id: notificationId,
                viewers: [...new Set([...viewerList, signedinUser])]
            }

            uniApiConCall(`/api/club/notification/messages/${selectedID}`, 'patch', viewersData);
            $('#notifications-list').LoadingOverlay("hide");
        }

        sidebarNotifications = sidebarNotifications.map(n => {
            if (n.id === notificationId) {
                n.viewers = [...new Set([..._get(n, 'viewers', []), signedinUser])];
            }
            return n;
        });

        renderSideBarNotificationList(sidebarNotifications);
    }

    processView();
    sammy.quiet = false;

    if (data?.mpRenderData) {
        const responseData = data.mpRenderData;
        sammy.setLocation(`/designer/exports?id=${responseData.metadata.renderID}`);
    } else if (data?.link) {
        sammy.setLocation(data.link);
    } else {
        sammy.setLocation(`notifications/${notificationId}`);
    }
    
    toggleNotificationBar(false);
}

/** list posible viewers of notifications **/
const getNotificationViewers = async (cb) => {
    let viewersUrl = `/api/club/notification/viewers/${selectedID}`
    let urlParams = ['target=clubs'];

    if (filterStartDateUnix && filterEndDateUnix) {
        urlParams.push(`startDate=${filterStartDateUnix}`);
        urlParams.push(`endDate=${filterEndDateUnix}`);
    }

    if (urlParams.length) {
        viewersUrl += '?' + urlParams.join('&');
    }

    const result = await await uniApiConCall(viewersUrl);
    if (result && 'status' in result && result.status == 'success') {
        let viewerList = result.data
        cb(viewerList);
    }
}

/** the viewers list **/
const renderViewers = async (data=[]) => {
    if (data && data.length < 1) {
        $('#notif-viewed-by-filter').empty().append(`
            <li style="padding-top: 5px;">
                <div style="padding: 5px 25px; text-align: center">No viewers found</div>
            </li>
        `);
        return;
    }

    $('#notif-viewed-by-filter').empty();

    /** if greater than one add the checkbox all **/
    if (data.length > 1) {
        $('#notif-viewed-by-filter').append(`
            <li style="padding-top: 5px;">
                <div style="padding: 5px 25px;" class="styled-checkbox">
                <input
                    id="viewer-select-all"
                    type="checkbox"
                    class="filled-in chk-col-blue"
                    data-notification-viewer="viewer-select-all"
                />
                <label style="font-size: 12px; display: block" for="viewer-select-all">All</label>
                </div>
            </li>
        `);
    }

    $('#notif-viewed-by-filter').append(data.map(viewer => {
        return (`
          <li class="viewer-checkbox-listItem" style="padding-top: 5px;">
            <div style="padding: 5px 25px;" class="styled-checkbox">
              <input
                id="viewer-${viewer.replace(/[^a-zA-Z ]/g, "")}"
                type="checkbox"
                class="notif-viewer-checkbox filled-in chk-col-blue"
                data-notification-viewer="${viewer}"
              />
              <label style="font-size: 12px; display: block" for="viewer-${viewer.replace(/[^a-zA-Z ]/g, "")}">${viewer}</label>
            </div>
          </li>
        `);
    }).join(''));

    /** toggle select all viewer **/
    $('#viewer-select-all').on('click', async () => {
        const isAllViewer = $('#viewer-select-all').prop('checked');
        $('#notif-viewed-by-filter input:checkbox').prop('checked', isAllViewer);
        await assembleCategory()
        callDebounceQuery();
    });

    /** begin to request if any viewer is click **/
    $('.viewer-checkbox-listItem input:checkbox').on('click', async () => {
        let isAllChecked = $('.notif-viewer-checkbox:checked').length === $('.notif-viewer-checkbox:checkbox').length
        $('#viewer-select-all').prop('checked', isAllChecked);
        await assembleCategory();
        callDebounceQuery();
    });
}

/** get the checked viewers for filter query **/
const getFilteredViewers = () => {
    let filterdViewers = [];
    let viewerCheck = $('#notif-viewed-by-filter').find('input:checkbox');
    viewerCheck.each(function() {
      if ($(this).data('notification-viewer') !== 'viewer-select-all' && $(this).prop('checked')) {
        filterdViewers.push($(this).data('notification-viewer'));
      }
    });

    return filterdViewers;
}

/** get the notification categories from api by viewers **/
const fetchNotifCategoriesBySignedInUser = async (viewers=[]) => {
    let userCategoriesUrl = `/api/club/notification/user-categories/${selectedID}`
    let viewersParam = viewers.length ? encodeURIComponent(JSON.stringify(viewers)) : '';
    let urlParams = [];

    if (viewersParam) {
        urlParams.push(`viewers=${viewersParam}`);
        userCategoriesUrl += `viewers=${viewersParam}`;
    }

    if (filterStartDateUnix && filterEndDateUnix) {
        urlParams.push(`startDate=${filterStartDateUnix}`);
        urlParams.push(`endDate=${filterEndDateUnix}`);
    }

    if (urlParams.length) {
        userCategoriesUrl += '?' + urlParams.join('&');
    }

    const categoryResult = await uniApiConCall(userCategoriesUrl);
    if (categoryResult && 'status' in categoryResult && categoryResult.status == 'success') {
        /** this variable is updated but you can use the return also **/
        notifCategoriesByUser = categoryResult.data

        return categoryResult.data
    }

    return [];
}