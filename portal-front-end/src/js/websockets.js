function setupWSClient() {
    // Enable detailed socketio debugging...
    // localStorage.debug = '*';

    // Simple JS Set to store hashes of recently received websocket messages (to prevent duplicates)
    const processedUIDs = new Set();

    socket.on('connect', () => {
        console.log(`WS Connect: ${socket.id}`);
        socket.emit('ping');

        // Socket connected/reconnected (did performance hub restart)?
        // Check the current codebase in memory is the latest build...
        sCheckNewFrontEndUpdates();
    });

    /*
        socket.on("disconnect", () => {
          console.log(`WS: Disconnect: ${socket.id}`);
        });
    */

    // Basic de-dupe front-end hashing functionality...
    async function generateMessageUID(data) {
        // Convert the data object to a string
        const dataStr = JSON.stringify(data);

        // Encode the string as a Uint8Array
        const buffer = new TextEncoder().encode(dataStr);

        // Use the SubtleCrypto API to hash the data
        const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);

        // Convert the buffer to a hexadecimal string
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

        return hashHex;
    }

    function addUIDWithTTL(uid, ttl) {
        processedUIDs.add(uid);
        setTimeout(() => {
            processedUIDs.delete(uid);
            // console.log(`UID ${uid} has expired and been removed.`);
        }, ttl);
    }

    // Handle incoming base64 screenshot data...
    !socket.hasListeners('deviceResponse') &&
        socket.on('deviceResponse', async (data) => {

            // console.log(`[message] Data received from server: ${event.data}`);
            if (data) {

                let messageUID;
                const url = window.location.pathname;

                // Attempt to generate a hash for the inbound message to prevent duplicates
                try {
                    messageUID = await generateMessageUID(data);
                } catch (error) {
                    console.warn("Browser does not support crypto API for hashing or an error occurred. Falling back to allowing duplicates.", error);
                    messageUID = false;
                }

                // Proceed with deduplication logic
                if ((messageUID == false) || (!processedUIDs.has(messageUID))) {
                    addUIDWithTTL(messageUID, 5000);

                    switch (data.command) {
                        case 'screenshotData': {
                            
                            if ($.sDevices.base.currentScreenshot == data.args) {
                                // If the UUID of the Screenshot matches what this tab created - show the screenshot, otherwise ignore...
                                // console.log('screenshotData', data)
                                $.sDevices.base.previewICSScreenshot(`data:image/jpg;base64, ${data.screenshotData}`, data?.balenaDeviceID);
                            }
                            break;
                        }

                        case 'cctvCameraAvailableConfig': {
                            console.log('camera-capabilities', data.configOptions);
                            break;
                        }

                        case 'newNetworkDetails': {
                            if(data.error !== undefined) {
                                instantNotification(
                                    `<div style="width: 250px;">${t('Failed to change Wifi Network - Please check the Password (PSK) is correct and the Wifi Network is still reachable. Network settings have been reverted to your previous configuration.')}</div>`,
                                    'error',
                                    20000
                                );
                            } else {
                                instantNotification(t('Network configuration successfully changed to new network: ') + (data.newNetworkDetails.newNetwork !== undefined) ? data.newNetworkDetails.newNetwork : '' );
                            }
                            break;
                        }

                        case 'detailedWifi': {
                            // Only show if we're on the /device-management page...
                            if (url.startsWith('/device-management')) {
                                $.sDevices.base.deviceWifiDetailsModel(data.balenaDeviceID, data.wifiConfiguration)
                            }
                            break;
                        }

                        case 'cctvSingleFrame': {
                            // Only show if we're on the /device-management page...
                            if (url.startsWith('/device-management')) {
                                $.sDevices.base.deviceCCTVMotionZoneConfig(data.balenaDeviceID, data.imageData)
                            }
                            break;
                        }

                        // This logic has now moved to be directly in the api request|response rather than via websockets.
                        // case 'routerTunnel': {
                        //     // Only show if we're on the /device-management page...
                        //     if (url.startsWith('/device-management')) {

                        //         const isWithinLastThreeMinutes = (() => {
                        //             try {
                        //                 const lastRequested = $.sDevices?.base?.lastRequestedRouterTunnel;
                        //                 return lastRequested && (Math.floor(Date.now() / 1000) - lastRequested <= 180);
                        //             } catch (error) {
                        //                 console.error("Error checking lastRequestedRouterTunnel:", error);
                        //                 return false;
                        //             }
                        //         })();

                        //         setTimeout(() => {
                        //             $(".btn-open-router").removeAttr("disabled");
                        //         }, 2000);

                        //         if (isWithinLastThreeMinutes) {

                        //             // Open the first URL after a 2-second delay
                        //             const firstUrl = data.routerTunnelDetails?.urls?.[0];
                        //             if (firstUrl) {
                        //                 setTimeout(() => {
                        //                     window.open(firstUrl, '_blank');
                        //                     Swal.close();
                        //                 }, 2000);
                        //             } else {
                        //                 console.warn("No URLs available to open");
                        //                 genericDialog(
                        //                     t('Error'),
                        //                     t('We could not establish a tunnel to the remote router at this time.')
                        //                 );
                        //             }
                        //         }
                        //     }
                        //     break;
                        // }


                        case 'deviceRealtimeData': {
                            if (url.startsWith('/device-management')) {
                                pushEntryToLogsDialog(data?.data?.deviceLogs ?? '')
                            }
                            break;
                        }

                        case 'relayDeviceStates': {
                            // The SWAL dialog watches for this custom event and updates the status when its received.
                            let event = new CustomEvent('relayDeviceStates', { detail: data });
                            document.dispatchEvent(event);
                            break;
                        }

                        //Clear the processedUIDs Set to prevent memory leaks...
                        if (processedUIDs.size > 1000) {
                            processedUIDs.clear();
                        }
                    }
                }
            }
        });

    
    !socket.hasListeners('notifications') &&
        socket.on('notifications', (data) => {
            console.log('new render response', data);
            const mpRenderData = _get(data, 'data.mpRenderData', null);
            const mpSessionId = _get(mpRenderData, 'metadata.sessionId', null);
            const mpSessionIds = JSON.parse(getSessionStorage('mpSessionIds') || '[]');

            const { pathname } = window.location;

            // Only 'download' the file if the marketing & print editor is open...
            if (mpRenderData && /^\/designer/.exec(pathname) && mpSessionIds.includes(mpSessionId)) {
                mp_downloadFile(
                    mpRenderData.files.filter((file) => {
                        return file.type === 'render';
                    })[0].location,
                    mpRenderData.metadata
                );
            }
    
            notificationReceived(data);
        });
}

function changeClubRoom() {
    if (!socket) return;
    socket.emit('change-club-room', { prev: prevSelectedID, cur: selectedID });
    socket.emit('change-region-room', { prev: prevSelectedRegion, cur: selectedClubObj.localisation.country.iso_code });
}
