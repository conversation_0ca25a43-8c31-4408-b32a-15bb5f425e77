function getDashboardIcons(iconType) {
    const icons = {
        'current-members': currentMembersIcon(),
        'new-members': newMembersIcon(),
        'ubx-gym': ubxGymIcon(),
        'on-hold': onHoldIcon(),
        'non-visiting': nonVisitingIcon(),
    };

    return iconType in icons ? icons[iconType] : '';
}

function currentMembersIcon() {
    return `
		<?xml version="1.0" encoding="UTF-8"?>
		<svg class="icon-primary icon-stroke" fill="none" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
			<circle cx="10" cy="10" r="3.3333"/>
			<ellipse cx="20" cy="6.6667" rx="3.3333" ry="3.3333"/>
			<ellipse cx="30" cy="10" rx="3.3333" ry="3.3333"/>
			<path d="m18.333 9.1668v0.98965c0 0.4089-0.2489 0.7766-0.6286 0.9285l-2.969 1.1876c-0.3492 0.1396-0.5905 0.4634-0.6245 0.8379l-0.7589 8.348c-0.0125 0.1374 0.0036 0.2759 0.0472 0.4068l0.7672 2.3015m-5.8334-11.667v1.0486c0 0.3788-0.214 0.725-0.55278 0.8944l-2.2278 1.1139c-0.33879 0.1694-0.55279 0.5157-0.55279 0.8944v9.1459c0 0.1553 0.03615 0.3084 0.10557 0.4473l0.62219 1.2443c0.06943 0.1389 0.10557 0.292 0.10557 0.4472v5.4306c0 0.5523 0.44772 1 1 1h5.6667"/>
			<path d="m21.667 9.1668v0.98965c0 0.4089 0.2489 0.7766 0.6286 0.9285l2.969 1.1876c0.3492 0.1396 0.5905 0.4634 0.6245 0.8379l0.7589 8.348c0.0125 0.1374-0.0036 0.2759-0.0472 0.4068l-0.7672 2.3015m5.8334-11.667v1.0486c0 0.3788 0.214 0.725 0.5528 0.8944l2.2277 1.1139c0.3388 0.1694 0.5528 0.5157 0.5528 0.8944v9.1459c0 0.1553-0.0361 0.3084-0.1056 0.4473l-0.6222 1.2443c-0.0694 0.1389-0.1055 0.292-0.1055 0.4472v5.4306c0 0.5523-0.4477 1-1 1h-5.6667"/>
			<path class="icon-primary icon-fill icon-stroke--none" d="m29.167 23.334c0.2811 0.2378 0.3161 0.6584 0.0783 0.9395l-9.204 10.878-5.8743-5.8742c-0.2603-0.2604-0.2603-0.6825 0-0.9428 0.2603-0.2604 0.6825-0.2604 0.9428 0l4.8496 4.8496 8.268-9.7713c0.2379-0.2811 0.6585-0.3162 0.9396-0.0783z" clip-rule="evenodd" fill-rule="evenodd"/>
			<path class="icon-primary icon-fill icon-stroke--none" d="m12.334 30c0-4.2342 3.4325-7.6667 7.6667-7.6667 2.6935 0 5.0626 1.3891 6.43 3.4899 0.2554 0.3923 0.8314 0.4629 1.1311 0.1033 0.1709-0.2051 0.199-0.4957 0.0566-0.7215-1.5937-2.5266-4.4097-4.205-7.6177-4.205-4.9706 0-9 4.0294-9 9s4.0294 9 9 9c4.9705 0 9-4.0294 9-9 0-0.5379-0.0472-1.0649-0.1377-1.5768-0.0969-0.5485-0.773-0.6872-1.1328-0.262-0.1417 0.1674-0.1963 0.3905-0.1613 0.607 0.0648 0.401 0.0985 0.8125 0.0985 1.2318 0 4.2342-3.4325 7.6667-7.6667 7.6667s-7.6667-3.4325-7.6667-7.6667z" clip-rule="evenodd" fill-rule="evenodd"/>
		</svg>
		`;
}

function newMembersIcon() {
    return `
		<svg width="41" height="40" viewBox="0 0 41 40" fill="none" class="icon-primary icon-stroke" xmlns="http://www.w3.org/2000/svg">
			<g clip-path="url(#clip0_94_9077)">
			<ellipse cx="10.2" cy="23.3333" rx="3.33333" ry="3.33333" stroke-width="0.8"/>
			<circle cx="20.2" cy="20" r="3.33333" stroke-width="0.8"/>
			<circle cx="30.2" cy="23.3333" r="3.33333" stroke-width="0.8"/>
			<path d="M18.5333 22.5V23.4896C18.5333 23.8985 18.2844 24.2662 17.9047 24.4181L14.9357 25.6057C14.5865 25.7454 14.3452 26.0691 14.3112 26.4437L13.5523 34.7917C13.5398 34.9291 13.5559 35.0676 13.5995 35.1984L14.3667 37.5M8.53335 25.8333V26.882C8.53335 27.2607 8.31934 27.607 7.98056 27.7764L5.7528 28.8903C5.41401 29.0597 5.20001 29.4059 5.20001 29.7847V38.9306C5.20001 39.0858 5.23616 39.239 5.30559 39.3778L5.92777 40.6222C5.9972 40.761 6.03335 40.9142 6.03335 41.0694V46.5C6.03335 47.0523 6.48106 47.5 7.03335 47.5H12.7" stroke-width="0.9"/>
			<path d="M21.8667 22.5V23.4896C21.8667 23.8985 22.1156 24.2662 22.4953 24.4181L25.4643 25.6057C25.8135 25.7454 26.0548 26.0691 26.0888 26.4437L26.8477 34.7917C26.8602 34.9291 26.8442 35.0676 26.8005 35.1984L26.0333 37.5M31.8667 25.8333V26.882C31.8667 27.2607 32.0807 27.607 32.4195 27.7764L34.6472 28.8903C34.986 29.0597 35.2 29.4059 35.2 29.7847V38.9306C35.2 39.0858 35.1639 39.239 35.0944 39.3778L34.4723 40.6222C34.4028 40.761 34.3667 40.9142 34.3667 41.0694V46.5C34.3667 47.0523 33.919 47.5 33.3667 47.5H27.7" stroke-width="0.9"/>
			<path d="M8.95001 17.9166V14.1666H5.20001L11.45 7.91663L17.7 14.1666H13.95V17.9166" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/>
			<path d="M16.45 12.9166V7.91663H13.95L20.2 1.66663L26.45 7.91663H23.95V12.9166" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/>
			<path d="M31.45 17.9166V16.6666V14.1666H35.2L28.95 7.91663L22.7 14.1666H26.45V17.9166" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/>
			</g>
			<defs>
			<clipPath id="clip0_94_9077">
			<rect width="40" height="40" fill="white" transform="translate(0.200012)"/>
			</clipPath>
			</defs>
		</svg>
	`;
}

function ubxGymIcon() {
    return `
		<svg width="41" height="40" viewBox="0 0 41 40" fill="none" class="icon-primary icon-fill" xmlns="http://www.w3.org/2000/svg">
			<path d="M25.6265 28.1669L29.7642 21.9314C29.9747 21.612 30.403 21.5249 30.7223 21.7354C31.0417 21.946 31.1289 22.3742 30.9183 22.6936L26.7807 28.9291C26.65 29.1324 26.4323 29.234 26.2072 29.234C26.0766 29.234 25.9459 29.1977 25.8298 29.1179C25.5031 28.9073 25.416 28.4791 25.6265 28.1669ZM34.7801 13.2569V31.7529H35.2665C35.644 31.7529 35.9561 32.065 35.9561 32.4425V34.8743C35.9561 35.2517 35.644 35.5639 35.2665 35.5639H5.53356C5.15609 35.5639 4.84395 35.2517 4.84395 34.8743V32.4425C4.84395 32.065 5.15609 31.7529 5.53356 31.7529H6.01991V13.2569H5.53356C5.35208 13.2569 5.17786 13.1843 5.0472 13.0537C4.91654 12.9302 4.84395 12.7488 4.84395 12.5673V8.85068C4.84395 8.47321 5.14883 8.16108 5.53356 8.16108L13.3806 8.1393V5.13406C13.3806 4.7566 13.6927 4.44446 14.0702 4.44446H26.7299C27.1074 4.44446 27.4195 4.7566 27.4195 5.13406V8.16108H35.2665C35.644 8.16108 35.9561 8.47321 35.9561 8.85068V12.5673C35.9561 12.9448 35.644 13.2569 35.2665 13.2569H34.7801ZM26.7299 14.7523H24.3489V17.9317H33.4009V13.2569H27.4195V14.0627C27.4195 14.4401 27.1146 14.7523 26.7299 14.7523ZM24.3562 19.3109V31.7529H33.4082V19.3109H24.3562ZM14.7598 13.3731H23.6593H26.0403V5.82367H14.7598V13.3731ZM22.977 17.9317V14.7523H17.8303V17.9317H22.977ZM17.8303 19.3109V31.7529H22.977V19.3109H17.8303ZM13.3806 14.0627V13.2351L7.39912 13.2496V17.9317H16.4511V14.7523H14.0702C13.6927 14.7523 13.3806 14.4401 13.3806 14.0627ZM7.39912 19.3109V31.7529H16.4511V19.3109H7.39912ZM6.22316 11.8777L13.3806 11.8559V9.51851L6.22316 9.54029V11.8777ZM34.5769 33.1321H6.22316V34.1847H34.5769V33.1321ZM34.5769 9.54029H27.4195V11.8777H34.5769V9.54029ZM8.95255 26.4683C9.0687 26.5482 9.19936 26.5845 9.33002 26.5845C9.55505 26.5845 9.77282 26.4756 9.90348 26.2796L11.8489 23.3469C12.0594 23.0275 11.9723 22.5993 11.6529 22.3888C11.3335 22.1782 10.9052 22.2653 10.6947 22.5847L8.7493 25.5174C8.54605 25.8295 8.63316 26.2578 8.95255 26.4683ZM30.8603 24.581L28.9149 27.5136C28.7043 27.833 28.7914 28.2613 29.1108 28.4718C29.227 28.5517 29.3577 28.588 29.4883 28.588C29.7133 28.588 29.9311 28.4791 30.0618 28.2831L32.0072 25.3504C32.2177 25.031 32.1306 24.6028 31.8112 24.3922C31.4991 24.1817 31.0708 24.2616 30.8603 24.581ZM10.0487 29.1179C10.1648 29.1977 10.2955 29.234 10.4261 29.234C10.6512 29.234 10.8689 29.1251 10.9996 28.9291L15.1372 22.6936C15.3477 22.3742 15.2606 21.946 14.9412 21.7354C14.6218 21.5249 14.1936 21.612 13.9831 21.9314L9.84541 28.1669C9.64216 28.4791 9.72927 28.9073 10.0487 29.1179ZM19.4709 28.1234C19.8483 28.1234 20.1605 27.8112 20.1605 27.4338V25.002C20.1605 24.6245 19.8483 24.3124 19.4709 24.3124C19.0934 24.3124 18.7813 24.6245 18.7813 25.002V27.4338C18.7813 27.8185 19.0861 28.1234 19.4709 28.1234Z" />
			<path class="icon-stroke--none icon-fill" d="M17.8591 9.79429C17.8591 10.3242 17.5688 10.6944 16.9808 10.6944C16.3856 10.6944 16.0952 10.3315 16.0952 9.79429V7.52222H15.2677V9.82332C15.2677 10.7597 15.8121 11.4203 16.9881 11.4203C18.1422 11.4203 18.6939 10.767 18.6939 9.82332V7.52222H17.8664L17.8591 9.79429ZM22.0621 10.3169C22.0621 9.83058 21.7355 9.4386 21.329 9.38052C21.6919 9.30793 21.9895 8.98128 21.9895 8.50218C21.9895 7.99405 21.6193 7.52948 20.8861 7.52948H18.8681V11.355H20.9442C21.6774 11.3477 22.0621 10.8904 22.0621 10.3169ZM19.6812 8.21908H20.7047C20.9805 8.21908 21.1547 8.40056 21.1547 8.64011C21.1547 8.88691 20.9805 9.06839 20.7047 9.06839H19.6812V8.21908ZM20.7337 10.6581H19.6812V9.75799H20.7337C21.0531 9.75799 21.2273 9.96851 21.2273 10.2081C21.2273 10.4839 21.0458 10.6581 20.7337 10.6581ZM24.1454 9.38052L25.4448 7.52222H24.4648L23.5792 8.80706L23.1147 9.47489L21.7717 11.355H22.7372L23.6591 10.0411L24.552 11.355H25.5319L24.1454 9.38052ZM22.7227 7.52222H21.75L22.9985 9.30793L23.5284 8.72721L22.7227 7.52222Z" />
		</svg>
	`;
}

function onHoldIcon() {
	return `
		<svg width="41" height="40" viewBox="0 0 41 40" fill="none" class="icon-primary icon-stroke" xmlns="http://www.w3.org/2000/svg">
			<path d="M8.37782 24.4445V12.2223C8.37782 11.6329 8.61194 11.0677 9.02869 10.6509C9.44544 10.2342 10.0107 10 10.6 10C11.1894 10 11.7546 10.2342 12.1714 10.6509C12.5881 11.0677 12.8223 11.6329 12.8223 12.2223V20V7.77782C12.8223 7.18845 13.0564 6.62322 13.4731 6.20647C13.8899 5.78972 14.4551 5.5556 15.0445 5.5556C15.6339 5.5556 16.1991 5.78972 16.6158 6.20647C17.0326 6.62322 17.2667 7.18845 17.2667 7.77782V18.8889V5.5556C17.2667 4.96623 17.5008 4.401 17.9176 3.98425C18.3343 3.5675 18.8996 3.33337 19.4889 3.33337C20.0783 3.33337 20.6435 3.5675 21.0603 3.98425C21.477 4.401 21.7111 4.96623 21.7111 5.5556V18.8889M21.7111 18.9584V8.88893C21.7111 8.29956 21.9453 7.73433 22.362 7.31758C22.7788 6.90083 23.344 6.66671 23.9334 6.66671C24.5227 6.66671 25.088 6.90083 25.5047 7.31758C25.9215 7.73433 26.1556 8.29956 26.1556 8.88893V24.4445" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" />
			<path d="M8.37782 24.097C8.37782 32.4815 12.8218 36.6666 18.9322 36.6666C25.0426 36.6666 27.5222 33.8385 28.931 30.3818L32.591 20.0975C33.0535 18.8084 32.8438 17.6129 31.7668 16.9837C30.6892 16.3538 29.2734 16.678 28.6901 17.8179L26.1536 24.097" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" />
		</svg>
	`
}

function nonVisitingIcon() {
	return `
		<svg width="41" height="40" viewBox="0 0 41 40" fill="none" class="icon-primary icon-stroke" xmlns="http://www.w3.org/2000/svg">
			<path d="M25.3703 31.74H6.45205C5.78514 31.74 5.24451 31.1993 5.24451 30.5324V6.11315C5.24451 5.44624 5.78514 4.90561 6.45205 4.90561H8.06212C8.72903 4.90561 9.26966 4.36497 9.26966 3.69806V3.22846C9.26966 2.6727 9.7202 2.22217 10.276 2.22217V2.22217C10.8317 2.22217 11.2822 2.6727 11.2822 3.22846V7.25361C11.2822 7.80937 10.8317 8.2599 10.276 8.2599V8.2599C9.7202 8.2599 9.26966 7.80937 9.26966 7.25361V6.91818" stroke-width="1.2" stroke-linecap="round"/>
			<path d="M33.4205 4.90564H34.8964C35.5633 4.90564 36.104 5.44628 36.104 6.11319V23.6897" stroke-width="1.2"/>
			<path d="M12.624 4.90561H28.8588C29.5257 4.90561 30.0664 4.36497 30.0664 3.69806V3.42971C30.0664 2.7628 30.607 2.22217 31.2739 2.22217H31.5423C32.2092 2.22217 32.7498 2.76281 32.7498 3.42972V7.05236C32.7498 7.71927 32.2092 8.2599 31.5423 8.2599H31.2739C30.607 8.2599 30.0664 7.71927 30.0664 7.05236V6.91818" stroke-width="1.2" stroke-linecap="round"/>
			<path d="M40.2001 29.7274C40.2001 33.8421 36.8645 37.1778 32.7498 37.1778C28.6351 37.1778 25.2995 33.8421 25.2995 29.7274C25.2995 25.6127 28.6351 22.2771 32.7498 22.2771C36.8645 22.2771 40.2001 25.6127 40.2001 29.7274Z" stroke-width="1.2"/>
			<mask id="mask0_94_9295" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="24" y="21" width="17" height="17">
			<ellipse cx="32.7498" cy="29.7274" rx="8.05032" ry="8.05031" fill="#D9D9D9"/>
			</mask>
			<g mask="url(#mask0_94_9295)">
			<circle cx="32.7497" cy="28.3857" r="2.68344" stroke-width="0.8"/>
			<path d="M31.4078 30.3982V30.9224C31.4078 31.4161 31.1072 31.8602 30.6488 32.0435L28.7407 32.8068C28.319 32.9754 28.0277 33.3664 27.9865 33.8186L27.4056 40.2095C27.3905 40.3754 27.4099 40.5427 27.4626 40.7007L28.0535 42.4737" stroke-width="0.8"/>
			<path d="M34.0914 30.3982V30.9224C34.0914 31.4161 34.392 31.8602 34.8505 32.0435L36.7586 32.8068C37.1802 32.9754 37.4716 33.3664 37.5127 33.8186L38.0937 40.2095C38.1088 40.3754 38.0894 40.5427 38.0367 40.7007L37.4457 42.4737" stroke-width="0.8"/>
			</g>
			<line x1="5.91528" y1="11.835" x2="35.4331" y2="11.835" stroke-width="0.9"/>
			<path d="M8.57782 14.4445L11.9112 17.7778M11.9112 14.4445L8.57782 17.7778" stroke-width="0.75" stroke-linecap="round"/>
			<path d="M15.2445 14.4445L18.5778 17.7778M18.5778 14.4445L15.2445 17.7778" stroke-width="0.75" stroke-linecap="round"/>
			<path d="M21.9112 14.4445L25.2445 17.7778M25.2445 14.4445L21.9112 17.7778" stroke-width="0.75" stroke-linecap="round"/>
			<path d="M28.5778 14.4445L31.9112 17.7778M31.9112 14.4445L28.5778 17.7778" stroke-width="0.75" stroke-linecap="round"/>
		</svg>
	`
}

function settingsIcon() {
	return `
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5">
			<path d="M12.0666 7.33334C9.46657 7.33334 7.3999 9.40001 7.3999 12C7.3999 14.6 9.46657 16.6667 12.0666 16.6667C14.6666 16.6667 16.7332 14.6 16.7332 12C16.7332 9.40001 14.6666 7.33334 12.0666 7.33334ZM12.0666 15.3333C10.1999 15.3333 8.73324 13.8667 8.73324 12C8.73324 10.1333 10.1999 8.66668 12.0666 8.66668C13.9332 8.66668 15.3999 10.1333 15.3999 12C15.3999 13.8667 13.9332 15.3333 12.0666 15.3333Z" fill="currentColor"></path>
			<path d="M21.8666 9.80001L19.9999 9.20001L19.5999 8.20001L20.5333 6.46668C20.7333 6.06668 20.6666 5.53334 20.3333 5.20001L18.7333 3.60001C18.3999 3.26668 17.8666 3.20001 17.4666 3.40001L15.7333 4.33334L14.7333 3.93334L14.1333 2.06668C13.9999 1.66668 13.5999 1.33334 13.1333 1.33334H10.8666C10.3999 1.33334 9.99992 1.66668 9.93325 2.13334L9.33325 4.00001C8.93325 4.06668 8.59992 4.20001 8.26659 4.40001L6.53325 3.46668C6.13325 3.26668 5.59992 3.33334 5.26659 3.66668L3.66659 5.26668C3.33325 5.60001 3.26659 6.13334 3.46659 6.53334L4.33325 8.20001C4.19992 8.53334 4.06659 8.93334 3.93325 9.26668L2.06659 9.86668C1.66659 10 1.33325 10.4 1.33325 10.8667V13.1333C1.33325 13.6 1.66659 14 2.13325 14.1333L3.99992 14.7333L4.39992 15.7333L3.46659 17.4667C3.26659 17.8667 3.33325 18.4 3.66659 18.7333L5.26659 20.3333C5.59992 20.6667 6.13325 20.7333 6.53325 20.5333L8.26659 19.6L9.26659 20L9.86659 21.9333C9.99992 22.3333 10.3999 22.6667 10.8666 22.6667H13.1333C13.5999 22.6667 13.9999 22.3333 14.1333 21.9333L14.7333 20L15.7333 19.6L17.4666 20.5333C17.8666 20.7333 18.3999 20.6667 18.7333 20.3333L20.3333 18.7333C20.6666 18.4 20.7333 17.8667 20.5333 17.4667L19.5999 15.7333L19.9999 14.7333L21.9333 14.1333C22.3333 14 22.6666 13.6 22.6666 13.1333V10.8667C22.6666 10.4 22.3333 9.93334 21.8666 9.80001V9.80001ZM21.3333 12.9333L18.9333 13.6667L18.8666 14L18.2666 15.4L18.0666 15.7333L19.2666 17.9333L17.9333 19.2667L15.7333 18.0667L15.3999 18.2667C14.9333 18.5333 14.4666 18.7333 13.9999 18.8667L13.6666 18.9333L12.9333 21.3333H11.0666L10.3333 18.9333L9.99992 18.8667L8.59992 18.2667L8.26659 18.0667L6.06659 19.2667L4.73325 17.9333L5.93325 15.7333L5.73325 15.4C5.46659 14.9333 5.26659 14.4667 5.13325 14L5.06659 13.6667L2.66659 12.9333V11.0667L4.93325 10.4L5.06659 10.0667C5.19992 9.53334 5.39992 9.06668 5.66659 8.60001L5.86659 8.26668L4.73325 6.06668L6.06659 4.73334L8.19992 5.93334L8.53325 5.73334C8.99992 5.46668 9.46659 5.26668 9.99992 5.13334L10.3333 5.00001L11.0666 2.66668H12.9333L13.6666 5.00001L13.9999 5.13334C14.4666 5.26668 14.9333 5.46668 15.3999 5.73334L15.7333 5.93334L17.9333 4.73334L19.2666 6.06668L18.0666 8.26668L18.2666 8.60001C18.5333 9.06668 18.7333 9.53334 18.8666 10L18.9333 10.3333L21.3333 11.0667V12.9333V12.9333Z" fill="currentColor"></path>
		</svg>
	`;
}