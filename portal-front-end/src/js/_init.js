var selectedID = "", selectedOrgID = "", lastSearchedClubTerm = null,
    selectedBrand = "UBX",
    selectedPhone = "", gymMasterDomain = "", noAssociatedClubs = true,
    signedinUser = null, listAuthLoaded = false,
    selectedClubObj = {}, ClubsObj = [], devStage = false,
    smallNavUserSet = false,
    currentBuildVersion = null,
    userPreferences = {},
    notificationCategories= []
    usersNotifLastKeyQuery = null,
    clubsNotifLastKeyQuery = null,
    regionNotifLastKeyQuery = null,
    globalNotifLastKeyQuery = null,
    clubDataInfo = null,
    clubRegion = null, 
    prevSelectedID = "",
    prevSelectedRegion = "",
    quickNavagateRoutes = [],
    showUnpublishedClubs = false,
    fontsBase64 = {},
    disclaimerAlert = false,
    signedInUserData = null,
    imageCache = {},
    sideMenuAdminMode = false;

const socket = io('', {
    path: '/ws',
    transports: ['websocket']
});

// initialize pdfjs worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorkerFile;

// Global store object
const appConfig = {
    clubCurrency: 'USD',
    clubCurrencySymbol: '$',
    activeUnitOfMeasurement: readCookie('activeUnitOfMeasurement') || 'metric',
    smartClubData: [],
    selectedClubObj: {}
};

// global store handler
const globalStoreHandler = (target, property, value) => {
    target[property] = value;

    if(property === 'selectedClubObj') {
        $('#checkin-history-link').attr('href', `https://${value.gymMasterDomain}/visitors`
        );
        if(typeof handleChangeClub !== 'undefined') hasChangedClub = true;
        return true;
    }
    
    // rerender any sections that that uses
    if(property === 'smartClubData') {
        displaySmartClub(value);
        return true;
    }

    return true;
}

// Create the proxy
var globalStore = useProxyState(appConfig, globalStoreHandler);

// Swap out the Performance Hub Logo for internationally recognised holidays!
function setPerformanceHubLogoBasedOnKnownHolidays() {
    
    let currentDate = moment().format('MM-DD');

    // Set the default PH logo...
    let logoPath = '';

    // KEY AUSTRALIAN HOLIDAYS
    // --------------------------------------------------------
    if (selectedClubObj?.localisation?.country?.iso_code === 'AU') {
        switch (currentDate) {
            case '01-26': // Australia Day
                logoPath = 'logo-doodles/ph-logo-australia-day.svg';
                break;
            case '04-25': // Anzac Day
                logoPath = 'logo-doodles/ph-logo-anzac-day.svg';
                break;
            case moment().year() + '-11-01': // Melbourne Cup Day (First Tuesday in November)
                if (moment().day() === 2) { // Check if today is Tuesday
                    logoPath = 'logo-doodles/ph-logo-melbourne-cup.svg';
                }
                break;
            default:
                // Continue with other cases or default
                break;
        }
    }

    // Check for UK holidays
    // --------------------------------------------------------

    if (selectedClubObj?.localisation?.country?.iso_code === 'GB') {
        switch (currentDate) {
            case moment().earlyMayBankHoliday().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-early-may.svg';
                break;
            case moment().springBankHoliday().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-spring-bank.svg';
                break;
            case moment().summerBankHoliday().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-summer-bank.svg';
                break;
        }
    }

    // Check for Japanese holidays
    // --------------------------------------------------------

    if (selectedClubObj?.localisation?.country?.iso_code === 'JP') {
        switch (currentDate) {
            case moment().showaDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-showa-day.svg';
                break;
            case moment().constitutionMemorialDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-constitution-day.svg';
                break;
            case moment().greeneryDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-greenery-day.svg';
                break;
            case moment().childrensDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-childrens-day.svg';
                break;
            case moment().tanabata().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-tanabata.svg';
                break;
            case moment().hanami().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-hanami.svg';
                break;
            case moment().obonFestival().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-obon.svg';
                break;
            case moment().nationalFoundationDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-foundation-day.svg';
                break;
            case moment().marineDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-marine-day.svg';
                break;
            case moment().respectForTheAgedDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-respect-aged.svg';
                break;
            case moment().cultureDay().format('MM-DD'):
                logoPath = 'logo-doodles/ph-logo-culture-day.svg';
                break;


        }
    }

    // USA Specific Holidays
    // --------------------------------------------------------
    if (selectedClubObj?.localisation?.country?.iso_code === 'US') {
        //switch (currentDate) {
        //    case moment().martinLutherKingJrDay().format('MM-DD'):
        //        logoPath = 'logo-doodles/ph-logo-mlk-day.svg';
        //        break;
        //    case moment().memorialDay().format('MM-DD'):
        //        logoPath = 'logo-doodles/ph-logo-memorial-day.svg';
        //        break;
        //    case '07-04': // Independence Day
        //        logoPath = 'logo-doodles/ph-logo-independence-day.svg';
        //        break;
        //    case moment().laborDay().format('MM-DD'):
        //        logoPath = 'logo-doodles/ph-logo-labor-day.svg';
        //        break;
        //    case '11-11': // Veterans Day
        //        logoPath = 'logo-doodles/ph-logo-veterans-day.svg';
        //        break;
        //    case moment().thanksgivingDay().format('MM-DD'): // Assuming you have a plugin for Thanksgiving Day
        //        logoPath = 'logo-doodles/ph-logo-thanksgiving.svg';
        //        break;
        //    // ... any other cases ...
        //}
    }



    // GLOBALLY ACCEPTED HOLIDAYS
    // --------------------------------------------------------

    // Calculate Easter date and the four preceding days (this is because Easter is not a fixed date)
    const easterDate = moment().easter();
    const easterDates = [
        easterDate.clone().subtract(4, 'days').format('MM-DD'),
        easterDate.clone().subtract(3, 'days').format('MM-DD'),
        easterDate.clone().subtract(2, 'days').format('MM-DD'),
        easterDate.clone().subtract(1, 'days').format('MM-DD'),
        easterDate.format('MM-DD')
    ];

    switch(currentDate) {
        case '12-31':
        case '01-01':
            logoPath = 'logo-doodles/ph-logo-new-year.svg';
            break;
        case '02-14':
            logoPath = 'logo-doodles/ph-logo-valentines.svg';
            break;
        case '03-17':
            logoPath = 'logo-doodles/ph-logo-st-patricks.svg';
            break;

        // Easter dates
        case easterDates[0]:
        case easterDates[1]:
        case easterDates[2]:
        case easterDates[3]:
        case easterDates[4]:
            logoPath = 'logo-doodles/ph-logo-easter.svg';
            break;

        case '10-31':
            logoPath = 'logo-doodles/ph-logo-halloween.svg';
            break;

        // Christmas Lead-up (2 weeks prior)
        case '12-11':
        case '12-12':
        case '12-13':
        case '12-14':
        case '12-15':
        case '12-16':
        case '12-17':
        case '12-18':
        case '12-19':
        case '12-20':
        case '12-21':
        case '12-22':
        case '12-23':
        case '12-24':
        case '12-25':
            logoPath = 'logo-doodles/ph-logo-christmas.svg';
            break;
        default:
            break;
    }

    // default to organisation logo if no special holiday logo is set
    const organisation = signedInUserData?.organisations?.find(org => org.organisationId === selectedOrgID);
    if (logoPath) {
        logoPath = `/assets/images/${logoPath}`;
    } else if (organisation?.logo?.s3Key) {
        logoPath = `/api/admin/secure-upload/public/${organisation.logo.s3Key}`;
    } else if (organisation?.logo?.url) {
        logoPath = organisation.logo.url;
    } else {
        logoPath = '/assets/images/default-org-logo.svg';
    }

    const cachedLogo = imageCache[logoPath];
    if (!cachedLogo) return $('.org-logo').attr('src', logoPath);

    $(cachedLogo).addClass('org-logo hide-tablet');
    $('.org-logo').replaceWith(cachedLogo);
}

$(document).ready(function(){
    setPerformanceHubLogoBasedOnKnownHolidays();

    function handleVisibilityOrFocusChange() {
        if (!document.hidden) {
            // Check is necessary because the Performance Hub is often left open for days
            setPerformanceHubLogoBasedOnKnownHolidays();
        }
    }

    // Listen for visibility change
    document.addEventListener('visibilitychange', handleVisibilityOrFocusChange);

    // Listen for window focus
    window.addEventListener('focus', handleVisibilityOrFocusChange);

});

function setupStageMode() {    
    if (location.hostname === "localhost" || location.hostname === "127.0.0.1" || location.hostname === "app.stage.performancehub.co") {

        Sammy.Application.prototype.debug = true
        devStage = true;

        if(location.hostname === "app.stage.performancehub.co") $(".stage-text").html(t(`You're viewing the Performance Hub in the Staging Account`))

        /*
            // Disclaimer for those pesky users ;)
            if (["<EMAIL>", "<EMAIL>"].includes(signedinUser) && !disclaimerAlert) {
                alert(`By clicking "OK," you agree not to contact the developer team about features currently in development or testing without first obtaining explicit permission. Unauthorized communication regarding these features will be considered a breach of this agreement, and may result in responses that are dismissive, sarcastic, or nonexistent. Your cooperation is appreciated.`);
                disclaimerAlert = true;
            }
        */

        $(".navbar-stage").css('visibility', 'visible');
        $("#main-body-container").css('padding-top', '36px');
        $("#leftsidebar").css('margin-top', '36px');
        $("#main-body-container").addClass('stage-environment');

    }
}

// Basic Cookie Manipulation
function createCookie(name, value, days) {
    var expires;

    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = "; expires=" + date.toGMTString();
    } else {
        expires = "";
    }
    document.cookie = encodeURIComponent(name) + "=" + encodeURIComponent(value) + expires + "; path=/";
}

function readCookie(name) {
    var nameEQ = encodeURIComponent(name) + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) === ' ')
            c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0)
            return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    return null;
}

function eraseCookie(name) {
    createCookie(name, "", -1);
}

function setGMDomainHyperlink(gymMasterDomain) {
    // Change the side nav GymMaster URL
    $("#nav-gm-portal a").attr("href", "https://" + gymMasterDomain + "/login");
    if(gymMasterDomain !== false) { $("#nav-gm-portal").show(); } else { $("#nav-gm-portal").hide(); }
}

function fireClubSelectEvents(selectedID, gymMasterDomain, sammyRefresh=true) {

    //console.log('fireClubSelectEvents');
    setGMDomainHyperlink(gymMasterDomain);
    lockOutSidebarAndBanner();

    // Fire off logic to change the performance hub logo if we're changing to a region that has special holidays...
    setPerformanceHubLogoBasedOnKnownHolidays();

    // If we're on the club-details page -- load the data for that club...
    if (sammy.getLocation().includes('/club-details')) {
        loadClubData();
    } else if (sammy.getLocation().includes('/club-integrations')) {
        $.sExternalIntegrations.base.loadExternalIntegrationsData(selectedID, function(response) { });
    } else if (sammy.getLocation().includes('/club-analytics')) {
        drawCharts();
    } else if (sammy.getLocation().includes('/store-ui')) {
        reloadStoreFrameBasedOnClubID();
    } else if (sammy.getLocation().includes('/wiki')) {
        reloadWikiFrameBasedOnClubID(selectedID, "");
    } else if(sammy.getLocation().includes('/dashboard')) {
        if(noAssociatedClubs == false) {
            loadDashboardAlerts();
            loadDashboardWidgets(selectedID);
            loadDashboardRevenue(selectedID);
        } else {
            setDashboardNoClubs()
        }
    } else if (sammy.getLocation().includes('/holiday-management')) {
        loadCountryHolidays();
    } else if (sammy.getLocation().includes('/review-management')) {
        loadSocialMediaReviews();
    } else if(sammy.getLocation().includes('/cctv-ui')) {
        reloadCCTViframe();
    } else if(sammy.getLocation().includes('/audio-control-ui')) {
        reloadAudioControliframe();
    } else if(sammy.getLocation().includes('/coaching-screens-ui')) {
        reloadcoachingScreensiframe();
    } else if(sammy.getLocation().includes('/cctv-ui')) {
        reloadCCTViframe();
    } else if(sammy.getLocation().includes('/mms-ui')) {
        reloadMMSiframe();
    } else if (sammy.getLocation().includes('/member-payments')) {
        initMemberPayments();
    } else if (sammy.getLocation().includes('/club-kyc/contact-details')) {
        initKYCContactDetails();
    } else if (sammy.getLocation().includes('/club-kyc')) {
        initClubKYC();
    } else if (sammy.getLocation().includes('/facility-variables')) {
        initClubVariables();
    } else if (sammy.getLocation().includes('/support')) {
        const supportId = sammy.getLocation().split('/').pop();
        const ticketNumber = supportId.includes('status-') ? supportId : undefined;
        $.sTicketing.base.init(ticketNumber);
        if (!supportId.includes('support') && !supportId.includes('status-') && !supportId.includes('?')) $.sTicketing.getTicketInfo.load(supportId);
    } else {
        if(sammyRefresh) sammy.refresh();
    }
}

function bindClubSelectChange(fireEvents) {
    // console.log('bindClubSelectChange');

    function handleClubSelectKeydown(event) {
      // Check if 'J' is pressed along with the Meta key (CMD on Mac) or Ctrl key
      if (event.keyCode === 74 && (event.metaKey || event.ctrlKey)) {
            
        // Check if currently focused element is an input or a textarea (if it is we don't want t fire shortcuts)
        let activeElement = document.activeElement.tagName.toLowerCase();
        
        if ((activeElement !== 'input' && activeElement !== 'textarea') || event.target.classList.contains('aa-Input')) {
            $('.club-spoof-select').selectpicker('toggle');
            event.preventDefault(); // Prevent the default CMD+K / Ctrl+J behavior
        }
      }
    }

    document.addEventListener('keydown', handleClubSelectKeydown);
    window.addEventListener('message', function(event) {
        // Check for the expected message
        if (event.data === 'triggerClubSelect') {
            $('.club-spoof-select').selectpicker('toggle');
        }
    });

    // Add a tooltip explaining there is a shortcut - ie CMD+J to open the bar...
    // setShortcutText();
    // function setShortcutText() {
    //     let osName = "Ctrl+";
    //     switch (getOS()) {
    //         case 'mac':
    //             osName = "⌘"; // Use the command symbol for Mac
    //             break;
    //         case 'Android':
    //             osName = "";
    //             break;
    //         case 'iOS':
    //             osName = "";
    //             break;
    //     }
    //     const result = osName + 'J';
    //     // Query all elements with the class .keyboard-shortcut and set their text
    //     $('.club-spoof-select .country-flag img').each(function() {
    //         console.log(this)
    //         this.setAttribute('data-toggle', 'tooltip');
    //         this.setAttribute('data-placement', 'right');
    //         this.setAttribute('title', `${t('Use the shortcut keys')} ${result} ${t('to open/close the Club selector')}`);
    //     });
    //     return result;
    // }


    $("select.club-spoof-select").off('change').on('change', function() {
        // Set the last searched term in the session if possible...
        lastSearchedClubTerm = $(this).parent().find('.dropdown-menu').find('input').val();

        // Focus text search area...
        $(this).parent().find('.dropdown-menu').find('input').focus();

        const newClubId = $(this).find(':selected').val();
        handleChangeClub(newClubId);
    });


    // ------------------------------------------------------------------------------------------------
    //
    // Load the page data for the first time if we're on a page that supports the club querystring...
    //

    const clubQueryString = (getUrlVars().club === undefined) ? false : getUrlVars().club;
    prevSelectedID = clubQueryString || readCookie("ActiveClub") || 'TestClub';
    selectedID = ClubsObj.some(club => club.id === prevSelectedID) ? prevSelectedID : ClubsObj[0]?.id;
    handleChangeClub(selectedID, fireEvents);

    // ------------------------------------------------------------------------------------

    setupStageMode();
    buildSideMenu();
    // ------------------------------------------------------------------------------------
}

async function getAuthedClubs(newOrgID) {
    const [userRes, userPrefRes] = await Promise.all([
        listAuthorisedAPI(),
        getUserPreferencesAPI()
    ]);

    if (!userRes[0] || !userPrefRes[0]) {
        listAuthLoaded = true;
        buildSideMenu();
        return setDashboardNoClubs();
    }
    
    const { club: clubQs } = getQueryStringsObject();
    const cookieOrgID = readCookie("ActiveOrg");
    const orgIdToUse = newOrgID || userPrefRes[1].defaultOrganisationId || cookieOrgID;
    selectedOrgID = userRes[1].organisations.find((org) => org.organisationId === orgIdToUse)?.organisationId;
    if (!selectedOrgID) selectedOrgID = userRes[1].organisations[0]?.organisationId ?? null;

    if (!newOrgID && clubQs) {
        const facility = userRes[1].clubs.find((club) => club.id === clubQs);
        selectedOrgID = facility?.organisationId || selectedOrgID;
    }

    if(_get(userRes[1], 'clubs', []).length >= 2) $('.club-spoof-wrapper').show();

    ClubsObj = _get(userRes[1], 'clubs', [])
        .filter(({ organisationId }) => userPrefRes[1].showAllFacilities || organisationId === selectedOrgID)
        .sort(dynamicSort("id"));

    signedInUserData = userRes[1];
    signedinUser = _get(userRes[1], 'signedin', 'User Not Found');

    const sideMenuModeCache = getSessionStorage('sideMenuAdminMode') === 'true';
    sideMenuAdminMode = signedInUserData.isGlobalAdmin && sideMenuModeCache;

    // Set Change Org button visibility
    $('.change-org-btn').parent().addClass('hidden');
    if (signedInUserData.organisations.length > 1 && !userPrefRes[1].showAllFacilities) {
        $('.change-org-btn').parent().removeClass('hidden');
    }

    // preload organisation logos
    const orgsWithLogos = signedInUserData.organisations.filter(org => org.logo?.s3Key);

    if (!orgsWithLogos.length || !orgsWithLogos.some(org => org.organisationId === selectedOrgID)) {
        orgLogosCached = true;
    }

    orgsWithLogos.forEach(async (org) => {
        const logoUrl = `/api/admin/secure-upload/public/${org.logo.s3Key}`;
        await preloadImage(logoUrl);

        // if initial logo is set, start the app
        if (org.organisationId === selectedOrgID) {
            orgLogosCached = true;
        }
    });

    $('select.club-spoof-select').empty();

    if (!ClubsObj.length) {
        $('.navbar-notifications-container').addClass('hidden');
    }

    const orgGroups = ClubsObj.reduce((acc, facility) => {
        const orgId = facility.organisationId || 'other';
        
        if (!acc[orgId]) {
            acc[orgId] = {
                organisationId: orgId,
                name: facility.organisation || 'Other',
                facilities: [],
            };
        }

        acc[orgId].facilities.push(facility);
        return acc;
    }, {});

    const orgGroupsArray = Object
        .values(orgGroups)
        .filter((orgGroup) => orgGroup.facilities.length > 0)
        .sort((a, b) => {
            if (a.organisationId === 'other') return 1;
            if (b.organisationId === 'other') return -1;
            return a.name.localeCompare(b.name);
        });

    if (orgGroupsArray.length > 0) noAssociatedClubs = false;

    let facilitySelectOptions = '';
    orgGroupsArray.forEach((orgGroup) => {        
        const facilityOptions = orgGroup.facilities
        .sort((a, b) => {
            if (a.clubPublished && !b.clubPublished) return -1;
            if (!a.clubPublished && b.clubPublished) return 1;
            return a.name.localeCompare(b.name);
        })
        .map((facility) => {
            const { localisation } = facility;
            const tokens = [
                localisation?.country?.full_name,
                localisation?.country?.iso_code,
                localisation?.province?.abbreviation,
                localisation?.province?.full_name,
                facility.id,
                orgGroup.name,
                orgGroup.organisationId,
            ].filter(Boolean).join('\n');

            const isoCode = localisation?.country?.iso_code;
            const flag = !isoCode ? '' : `
                <span class='country-flag'>
                    <img style='height: 20px; width: 20px;' src='/assets/images/flags/${isoCode.toUpperCase()}.svg'>
                </span>
            `;

            const publishedIndicator = facility.clubPublished
              ? `` // no icon for locations that are published
              : `<span class='published-indicator unpublished-location-tooltip'></span>`;

            const publishedClx = facility.clubPublished ? 'published-club' : 'unpublished-club';

            return `
                <option
                    class='${publishedClx}'
                    data-tokens="${tokens}"
                    data-content="<span class='facility-name-wrapper'>${publishedIndicator}${facility.name}</span>${flag}"
                    value="${facility.id}"
                >${facility.name}</option>
            `;
        }).join('');

        facilitySelectOptions += `<optgroup label="${orgGroup.name}">${facilityOptions}</optgroup>`;
    });

    $('select.club-spoof-select').append(facilitySelectOptions);

    // Sidebar configuration  based on if they are admin user or not..
    $(".profile-dropdown .signed-in-user").html(signedinUser);
    if (signedInUserData?.isGlobalAdmin) {
        $(".profile-dropdown .signed-in-user-role").html(t('Super Admin'));
    }

    $('.club-spoof-select').off('shown.bs.select');
    $('.club-spoof-select').on('shown.bs.select', function() {

        // This logic is used to create a tooltip within the data-html content
        if (!document.getElementById('global-spoof-select-tooltip')) {
          $('body').append(`
            <div id="global-spoof-select-tooltip" class="facility-select-custom-tooltip" style="display: none;">
              <div class="tooltip-content"></div>
              <div class="tooltip-arrow"></div>
            </div>
          `);
        }
  
        // Now wire up hover events for the dots
        $('.dropdown-menu .unpublished-location-tooltip').on('mouseenter', function () {
          const $tooltip = $('#global-spoof-select-tooltip');
          const offset = $(this).offset();
          const tooltipTop = offset.top - 45;
          // const tooltipLeft = offset.left;
    
          const dotWidth = $(this).outerWidth();
          const tooltipWidth = $('#global-spoof-select-tooltip').outerWidth();
          const tooltipLeft = offset.left + dotWidth / 2 - tooltipWidth / 2;

          $tooltip.find('.tooltip-content').text(t('Unpublished Location'));
          $tooltip.css({
            top: tooltipTop,
            left: tooltipLeft,
            display: 'block',
            opacity: 1
          });
        });
  
        $('.dropdown-menu .unpublished-location-tooltip').on('mouseleave', function () {
          $('#global-spoof-select-tooltip').css({
            display: 'none',
            opacity: 0
          });
        });



        // only have the 'show unblished clubs' button if there is more than say 25 clubs
        if (ClubsObj.length < 25 || showUnpublishedClubs) {
            return $('.unpublished-club').show();
        }

        $(this).find('.unpublished-club').hide();
        if ($('.dropdown-menu.open div.show-hidden-container').length > 0) return;

        $('.dropdown-menu.open').append('<div class="show-hidden-container text-center" style="border-top: 1px solid #e9e9e9; padding: 10px;"><a class="btn btn-xs btn-primary">Show Unpublished Facilities</a></div>');
        
        // Attach event handler to the button, ensuring it's bound only once by delegating from '.dropdown-menu.open'
        $('.dropdown-menu.open').off('click', '.show-hidden-container a').on('click', '.show-hidden-container a', function(e) {
            e.preventDefault(); // Prevents the default action
            e.stopPropagation(); // Stops the event from bubbling up to parent elements
            showUnpublishedClubs = true;
            $('.unpublished-club').show();
            $('.show-hidden-container a').hide();
        });
    });

    $('.club-spoof-select').on('hidden.bs.select', function () {
      $('#global-spoof-select-tooltip').remove();
    });


    $('.club-spoof-select').selectpicker('refresh');
    $('.club-spoof-select').find('input').attr("placeholder", t('Search Club Name, Country, or Province'));

    // Bind event to change the 'club' when active club selection changes...
    bindClubSelectChange(!!newOrgID);

    $('.club-spoof-select').off('show.bs.select');
    $('.club-spoof-select').on('show.bs.select', function (e, clickedIndex, isSelected, previousValue) {
        // console.log(lastSearchedClubTerm);
        $(this).parent().find('.dropdown-menu').find('input').val(lastSearchedClubTerm).trigger('propertychange')
        $(this).parent().find('.dropdown-menu').find('input').select();

        // toggle dropdown-overlay
        $('.dropdown-overlay').addClass('active');
        $('.club-spoof-wrapper').css("z-index", 1000)
    });

    listAuthLoaded = true;

    // Lastly - refresh the select picker one last time
    setTimeout(function() {
        $('.club-spoof-select').selectpicker('refresh');
    }, 5000);
}

if (top.location.pathname !== '/401.html'){
    (async  () => {
        await getAuthedClubs();
        setupStageMode();
    })();

}

// Function to 'close' the bootstrap dropdown menus (if existing and clicked)...
// There's a bug somewhere with BS3's dropdown menu, and it's been fixed in bs4 but not this version.
$("body").click(function() {
    $('a').closest(".dropdown-menu").prev().closest(".open").removeClass('open');
    $('.bootstrap-select').removeClass('open');

    $('.dropdown-overlay').removeClass('active');
    $('.club-spoof-wrapper').css("z-index", 'auto')
});

// If we're already on the wiki page - reload the iframe, to return to the homepage of openkb...
$("#nav-club-operations").click(function(){
    if(sammy.getLocation() == "/wiki") {
        sammy.refresh();
    }
});

// Functionality to 'toggle' the navigation menu to be "small/thin" or normal..
$("#toggleNavSmallBtn").click(function() {
    smallNavUserSet = true;
    toggleSmallNav();
})

$(function () {
    // Check if we're on a small screen...
    smallHeightWidthEvents();
})

$(window).resize(function() {
    smallHeightWidthEvents();
});

function smallHeightWidthEvents() {

    if ($(window).height() < 700) {
        $("#leftsidebar > .legal").hide();
    } else {
        $("#leftsidebar > .legal").show();
    }

    // Auto make side bar menu smaller for low res devices...
    if(!smallNavUserSet) {
        if ($(window).width() < 1300 && $(window).width() > 991) {
            if(!smallSidebar) toggleSmallNav()
        }

        // Auto make side bar larger...
        if ($(window).width() > 1700) {
            if(smallSidebar) toggleSmallNav()
        }
    }

    // Hamburger menu logic for devices smaller than 991...
    // (menu should be larger for hamburger menu)

    $(".navbar-logo").unbind("click");
    if ($(window).width() < 991) {

        if(smallSidebar) toggleSmallNav();

        // On mobile devices, change it so if you click the logos - it opens the menu...
        $(".navbar-logo").attr('href', 'javascript:');

        // Unbind any previously bound events...
        $(".navbar-logo").click((e) => {
            e.stopPropagation();
            setTimeout(function() {
                $.AdminBSB.navbar.toggleOpen();
            }, 100)
        });

    } else {
        $(".navbar-logo").attr('href', '/');
    }

    // Notif sidebar pin should be disabled on mobile devices...
    const notifPinned = _get(userPreferences, 'notifications.pinned', false);
    const notifIsOpen = $('.notifications-side-menu').hasClass('open');
    if ($(window).width() < 991) {
        $('#notif-bar-close').css('opacity', '1');
    } else {
        $('#notif-bar-close').css('opacity', notifPinned ? '0.4' : '1');
        if (notifPinned && !notifIsOpen) toggleNotificationBar(true);
    }

    // apply swal css to notif sidebar on mobile
    if ($('.notifications-side-menu').hasClass('open')) {
        if ($(window).width() < 768) {
            $('body').addClass('swal2-shown');
        } else {
            $('body').removeClass('swal2-shown');
        }
    }

}

function sCheckNewFrontEndUpdates() {
    $.getJSON("/assets/metadata/build-version.json", function(metadata) {
        if(metadata.build !== undefined) {
            if(currentBuildVersion !== null) {
                if(currentBuildVersion !== metadata.build) {
                    console.log(`+ New front-end release available: ${metadata.build}`);
                    pinRefreshMessage();
                }
            }
            currentBuildVersion = metadata.build;
        }
    })

    function pinRefreshMessage() {
        $("application").append(`
            <div class="pinned-notification">
                <div class="notification-container">
                    <div>
                        <span><i class="fa fa-lightbulb-o" aria-hidden="true"></i> <b>${t('Note!')}</b></span>
                        <span class="notification-text">
                            ${t("We've updated things &amp; the version of the Performance Hub you are viewing is out of date.")}
                            <div class="d-flex justify-center mt-1"><a onclick="location.reload();" class="btn btn-small btn-primary btn-outline waves-effect refresh-page">${t('Refresh Page')}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        `)
    }
}

// --------------------------------------------------------------------------------------------------------

sCheckNewFrontEndUpdates();
sHeaderNotifications();

// Setup websockets client...
if (typeof io == 'undefined') {

    var script = document.createElement('script');
    script.onload = function () {
        setupWSClient();
    };

    script.src = '/ws/socket.io.js';
    document.head.appendChild(script);
} else {
    setupWSClient();
}


// Localisation initialization...
setDefaultLanguage = async function () {
    try {
        const preferences = await $.ajax('/api/user-preferences');
        const language = preferences.preferredLanguage ? preferences.preferredLanguage : navigator.language;
        // set cookie to be used on iframe
        createCookie("UserLang", language, 1080);
        return language;
    } catch (error) {
        console.error(error);
        return [false];
    }
};

$(document).ready(async function () {
    const defaultLanguage = await setDefaultLanguage();

    if (!/^en/.test(defaultLanguage)) {
        await init(`/assets/translations/${defaultLanguage}.json`, setDefaultLanguage);
    }

    $('#loadingPerformanceHub').html(t('Loading Performance Hub'))
    
    setTimeout( function() {
        // Load language fonts for data-tables (delayed as this is a lower priority - that way rest of the UI loads first)
        initializePdfMakeFonts(defaultLanguage);
    }, 5000)

    $('#leftsidebar *:not(:has(*)), #notifications-right-side *:not(:has(*)), #navigation-bar *:not(:has(*))').each(function () {
        const text = $(this)
            .text()
            .trim()
            .replace(/&(.*?);/g, '');

        $(this).text(t(text));
    });

    //add this for cases that cant be covered by the above
    $('.to-translate').each(function(){
        const cloneEl = $(this).clone().children() 
        .remove()
        .end();
        
        const originalText = cloneEl.text()
                 .trim()
                 .replace(/&(.*?);/g, '');
      
        const translatedHtml = $(this).html().replace(originalText, t(originalText));

        $(this).html(translatedHtml);
    })

    $('*').each(function () {
        if ($(this).attr('title')) {
            const title = $(this)
                .prop('title')
                .trim()
                .replace(/&(.*?);/g, '');

            $(this).prop('title', t(title));
        }
    });

    $('*').each(function () {
        if ($(this).attr('placeholder')) {
            const placeholder = $(this)
                .prop('placeholder')
                .trim()
                .replace(/&(.*?);/g, '');

            $(this).prop('placeholder', t(placeholder));
        }
    });
   
    defineMomentLocale(defaultLanguage)
}); 


const defineMomentLocale = async (defaultLanguage) => {
    moment.defineLocale(defaultLanguage, {
        months: [
            t('January'),
            t('February'),
            t('March'),
            t('April'),
            t('May'),
            t('June'),
            t('July'),
            t('August'),
            t('September'),
            t('October'),
            t('November'),
            t('December'),
        ],
        monthsShort: [
            t('Jan'),
            t('Feb'),
            t('Mar'),
            t('Apr'),
            t('May'),
            t('Jun'),
            t('Jul'),
            t('Aug'),
            t('Sep'),
            t('Oct'),
            t('Nov'),
            t('Dec'),
        ],
        weekdays: [t('Sunday'), t('Monday'), t('Tuesday'), t('Wednesday'), t('Thursday'), t('Friday'), t('Saturday')],
        weekdaysShort : [t("Sun"), t("Mon"), t("Tue"), t("Wed"), t("Thu"), t("Fri"), t("Sat")],
        weekdaysMin : [t("Su"), t("Mo"), t("Tu"), t("We"), t("Th"), t("Fr"), t("Sa")],
        meridiem : function (hour, minute, isLowercase) {
            if (hour < 12) {
                return t('AM')
            } else {
                return t('PM')
            }
        }
    });  

    moment().locale(defaultLanguage)

}

renderProfileDropdown();
renderNotificationBtn();

function enableModalBackdrop(iframeWrapper) {
    if (!iframeWrapper) {
        return;
    }

    $('#modalBackdrop').hide();
    iframeWrapper.removeClass('iframe-placeholder-zindexOn');

    window.addEventListener('message', function (event) {
        if (typeof event.data == 'object') return;

        $('#modalBackdrop').hide();
        iframeWrapper.removeClass('iframe-placeholder-zindexOn');

        if (event.data) {

            if(event.data === 'stopCCTVLoader') {
              $('.cctv-skeleton-loader').fadeOut();
            }

            try {
                const dataObj = JSON.parse(event.data);

                if (dataObj.showModalBackdrop) {
                    $('#modalBackdrop').show();
                    iframeWrapper.addClass('iframe-placeholder-zindexOn');
                    return;
                }

                if (dataObj.hideModalBackdrop) {
                    $('#modalBackdrop').hide();
                    iframeWrapper.removeClass('iframe-placeholder-zindexOn');
                }

                if(dataObj.routeChange && dataObj.route) {
                    firstLoad = true;
                    sammy.quiet = false;
                    sammy.setLocation(dataObj.route);
                    firstLoad = false;
                }

            } catch (error) {
                console.log('not a json object', error);
            }
        }
    });
}

// Function to fetch a font file and convert it to Base64
async function fetchFontToBase64(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to load font: ${response.statusText}`);
        }
        const buffer = await response.arrayBuffer();
        // Convert buffer to Base64
        const base64 = btoa(new Uint8Array(buffer).reduce(function (data, byte) {
            return data + String.fromCharCode(byte);
        }, ''));
        return base64;
    } catch (error) {
        console.error('Error fetching font:', error);
        return null;
    }
}

async function fetchImageToBase64(url, format='data:image/png') {
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to load image: ${response.statusText}`);
        }
        const buffer = await response.arrayBuffer();
        const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
        return `${format};base64,${base64}`; // Adjust the MIME type according to the image format when calling function if required
    } catch (error) {
        console.error('Error fetching image:', error);
        return null;
    }
}


// Initialize fonts for pdfMake with error handling
async function initializePdfMakeFonts(language) {
    try {
        // Convert language to lowercase
        const lang = (language || '').toLowerCase();
        
        let notoSansRegular, notoSansBold, notoSansItalic, notoSansBoldItalic;

        switch (lang) {
            case 'ar':
                // Load Japanese fonts
                notoSansRegular = await fetchFontToBase64('/assets/fonts/NotoSansArabicEnglish-Regular.ttf');
                notoSansBold = await fetchFontToBase64('/assets/fonts/NotoSansArabicEnglish-Bold.ttf');
                notoSansItalic = await fetchFontToBase64('/assets/fonts/NotoSansArabicEnglish-Regular.ttf');
                notoSansBoldItalic = await fetchFontToBase64('/assets/fonts/NotoSansArabicEnglish-Bold.ttf')
                break;

            case 'ja':
                // Load Japanese fonts
                notoSansRegular = await fetchFontToBase64('/assets/fonts/NotoSansJP-Regular.otf');
                notoSansBold = await fetchFontToBase64('/assets/fonts/NotoSansJP-Bold.otf');
                notoSansItalic = await fetchFontToBase64('/assets/fonts/NotoSansJP-Regular.otf');
                notoSansBoldItalic = await fetchFontToBase64('/assets/fonts/NotoSansJP-Bold.otf')
                break;

            default:
                // Load English fonts as fallback
                notoSansRegular = await fetchFontToBase64('/assets/fonts/NotoSans-Regular.ttf');
                notoSansBold = await fetchFontToBase64('/assets/fonts/NotoSans-Bold.ttf');
                notoSansItalic = await fetchFontToBase64('/assets/fonts/NotoSans-Italic.ttf');
                notoSansBoldItalic = await fetchFontToBase64('/assets/fonts/NotoSans-BoldItalic.ttf');
                break;
        }

        // Ensure all fonts are successfully loaded
        if (!notoSansRegular || !notoSansBold || !notoSansItalic || !notoSansBoldItalic) {
            throw new Error("Failed to load one or more fonts.");
        }

        // Define the virtual file system with Base64 encoded fonts
        pdfMake.vfs = {
            'NotoSans-Regular.ttf': notoSansRegular,
            'NotoSans-Bold.ttf': notoSansBold,
            'NotoSans-Italic.ttf': notoSansItalic,
            'NotoSans-BoldItalic.ttf': notoSansBoldItalic
        };

        // Define the fonts in pdfMake
        pdfMake.fonts = {
            NotoSans: {
                normal: 'NotoSans-Regular.ttf',
                bold: 'NotoSans-Bold.ttf',
                italics: 'NotoSans-Italic.ttf',
                bolditalics: 'NotoSans-BoldItalic.ttf'
            },
            Roboto: {
                // Using NotoSans as a placeholder because we dont actually have roboto fonts bundled...
                normal: 'NotoSans-Regular.ttf',
                bold: 'NotoSans-Bold.ttf',
                italics: 'NotoSans-Italic.ttf',
                bolditalics: 'NotoSans-BoldItalic.ttf'
            }
        };

        // console.log('Fonts loaded and set in pdfMake:', pdfMake.fonts);
    } catch (error) {
        console.error('Error initializing pdfMake fonts:', error);
    }
}

// close the bootstrap select when click target is not the select
// $(document).on('click', function (event) {
//     if (!$(event.target).closest('.bootstrap-select').length) {
//         $('.bootstrap-select').removeClass('open');
//     }
// });

$(document).on('click', 'iframe', function (event) {
    console.log({ eventIframe: event });
})


// Close page filters popup when clicking outside of the filter options
// keeping this here so it doesn't duplicate in every page
$(document).on('click', (e) => {
    const targetClass = e.target.classList;

    const voidClasses = ['filter__search', 'filter__search-input', 'filter__no-result'];
    const isVoidClass = voidClasses.some((className) => targetClass?.contains(className));
    if (isVoidClass) return;

    $('.filter__options').removeClass('filter__options--active');
});

let checkSSOInterval = 10 * 60 * 1000; // 10 minutes
// let checkSSOInterval = 10 * 1000; // For testing purposes
let checkSSOTimeout = null;
let ssoInacticeSwalInstance = null; // Track the state of the Swal dialog

// Continuously check if the user's SSO token is still valid...
async function checkSSOToken() {
    console.info('Checking SSO token...');
    checkSSOTimeout && clearTimeout(checkSSOTimeout);

    const [ok] = await getOAuthUserInfoAPI();

    if (ok) {
        // Close only the active SSO Swal dialog if it exists
        if (ssoInacticeSwalInstance && Swal.isVisible()) {
            Swal.close(); // Close the current Swal instance
            ssoInacticeSwalInstance = null; // Reset the tracker
        }

        checkSSOTimeout = setTimeout(checkSSOToken, checkSSOInterval);
        return;
    }

    const html = document.createElement('div');
    $(html).html(`<div class="body">${t('Your session has expired. Please log in again to continue.')}</div>`);

    // Track the specific Swal dialog instance
    ssoInacticeSwalInstance = swalWithBootstrapButtons.fire({
        title: t('You have been signed out'),
        html,
        icon: 'warning',
        showCancelButton: false,
        confirmButtonText: t('Log In'),
        customClass: {
            popup: 'ph-dialog-v2',
            confirmButton: 'btn btn-primary waves-effect',
        },
        allowOutsideClick: false,
        allowEscapeKey: false,
        preConfirm: async () => {
            window.open('/oauth2/start', '_blank');

            return new Promise((resolve) => {
                const interval = setInterval(async () => {
                    const [ok] = await getOAuthUserInfoAPI();
                    if (ok) {
                        clearInterval(interval);

                        // Close only the active SSO Swal dialog
                        if (ssoInacticeSwalInstance && Swal.isVisible()) {
                            Swal.close();
                            ssoInacticeSwalInstance = null; // Reset the tracker
                        }

                        resolve();
                    }
                }, 5 * 1000); // Check every 5 seconds
            });
        },
    }).then(() => {
        // Close and keep checking
        checkSSOTimeout = setInterval(checkSSOToken, checkSSOInterval);
    });
}

checkSSOTimeout = setTimeout(checkSSOToken, checkSSOInterval);