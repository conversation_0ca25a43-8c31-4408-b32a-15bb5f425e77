let currentQuery = '';
let mpTotalHits = 0;
let storeTotalHits = 0;
let globalAlgoliaSearch = null;

function handleCmdK(event) {
    // Check if 'K' is pressed along with the Meta key (CMD on Mac) or Ctrl key
    if (event.keyCode === 75 && (event.metaKey || event.ctrlKey)) {
        
    // Check if currently focused element is an input or a textarea
    let activeElement = document.activeElement.tagName.toLowerCase();
    
    if ((activeElement !== 'input' && activeElement !== 'textarea') || event.target.classList.contains('aa-Input')) {
        openGlobalAlgoliaSearch();
        event.preventDefault(); // Prevent the default CMD+K / Ctrl+K behavior
    }
    }
}

function openGlobalAlgoliaSearch() {
    if (!globalAlgoliaSearch) return;

    if(currentQuery !== '') {
        globalAlgoliaSearch.setQuery('');
    }

    globalAlgoliaSearch.setIsOpen(true);

    currentQuery = '';
    mpTotalHits = 0;
    storeTotalHits = 0;

    setTimeout(() => $("#autocomplete-0-input").focus(), 100);
}

$(document).on('keydown', handleCmdK);

window.addEventListener('message', function(event) {
    // Check for the expected message
    if (event.data === 'triggerSearch') {
        openGlobalAlgoliaSearch();
    }
});

$(".faux-text-searchbar, .search-toggle-mobile").on('click', function() {
    openGlobalAlgoliaSearch();
});

// Create a debounced function using lodash's debounce
const postMessageToIframe = (personID) => {
  const iframe = $('#cctvContainer').get(0);
  if(!iframe || !personID) return;
  iframe.contentWindow.postMessage({
      type: 'navigate-cctv',
      personID,
  }, '*');
  console.log('sent message to iframe', personID, iframe);
};


// This should be a global function because it's called directly from the kb.hbs file...
function trackViewedArticle(article) {
    console.log('trackViewedArticle', article)
    const MAX_ARTICLES = 10;
    const viewedArticles = JSON.parse(localStorage.getItem(`recent-articles-${selectedOrgID}`) || '[]');
    
    // Check if the article is already in the list
    const existingIndex = viewedArticles.findIndex(a => a.url === article.url);
    if (existingIndex !== -1) {
        viewedArticles.splice(existingIndex, 1);
    }
    // Add the new article to the start of the list
    viewedArticles.unshift(article);
    // Keep only the last MAX_ARTICLES viewed
    localStorage.setItem(`recent-articles-${selectedOrgID}`, JSON.stringify(viewedArticles.slice(0, MAX_ARTICLES)));
}

// ----------------------------------------
async function loadAlgolia() {
    // Remove any duplicates from our 'pages'
    let availablePageRoutes = _.uniq(quickNavagateRoutes, function(item) {
        return item.title + item.url;
    });
    // Sorting the unique array in alphabetical order by 'title'
    availablePageRoutes.sort(function(a, b) {
        if (a.title < b.title) {
            return -1;
        }
        if (a.title > b.title) {
            return 1;
        }
        return 0;
    });

    // Try to localise the search placeholder
    $(".search-placeholder").find("span").html(t("Search Performance Hub"))

    const algoliaConfigRes = await getAlgoliaConfigAPI(selectedID);
    const algoliaConfig = algoliaConfigRes[1];

    const searchClient = algoliasearch(algoliaConfig.appId, algoliaConfig.apiKey);
    // Set the default 'keyboard shortcut'...
    setShortcutText();
    const shortStr = setShortcutText();
    function setShortcutText() {
        let osName = "Ctrl+";
        switch (getOS()) {
            case 'mac':
                osName = "⌘"; // Use the command symbol for Mac
                break;
            case 'Android':
                osName = "";
                break;
            case 'iOS':
                osName = "";
                break;
        }
        const result = osName + 'K';
        // Query all elements with the class .keyboard-shortcut and set their text
        let shortcutElements = document.querySelectorAll('.keyboard-shortcut');
        shortcutElements.forEach(function(element) {
            element.innerHTML = result; // Set the text to Cmd+K or Ctrl+K

            element.setAttribute('data-toggle', 'tooltip');
            element.setAttribute('data-placement', 'right');
            element.setAttribute('title', `${t('Use the shortcut keys')} ${result} ${t('to open/close the Performance Hub Search Bar')}`);
        });
        return result;
    }

    aa('reset');
    aa('init', { appId: algoliaConfig.appId, apiKey: algoliaConfig.apiKey, useCookie: true });
    
    globalAlgoliaSearch = autocomplete({
        container: '#algoliaInstantSearch',
        placeholder: t('Search Performance Hub'),
        detachedMediaQuery: '',
        openOnFocus: true,
        enterKeyHint: 'enter',
        //defaultActiveItemId: 0,
        translations: {
            clearButtonTitle: t('Clear'),
            detachedCancelButtonText: t('Cancel'),
            submitButtonTitle: t('Search Performance Hub'),
        },
        getSources({query}) {
            return debounced([
                // PERFORMANCE HUB FEATURES / ROUTE TO PAGE FUNCTIONALITY
                {
                    sourceId: 'functions',
                    getItems({ query }) {
                        // Don't return responses for empty searches
                        if (!query) return [];
                        // Filtering
                        let filteredResults = _.filter(availablePageRoutes, ({ title }) => {
                            let modifiedTitle = title.toLowerCase().replace(/&/g, 'and');
                            let modifiedQuery = query.toLowerCase().replace(/&/g, 'and');
                            return modifiedTitle.includes(modifiedQuery);
                        });
                        // Sorting by relevance
                        let sortedResults = _.sortBy(filteredResults, ({ title }) => {
                            let modifiedTitle = title.toLowerCase().replace(/&/g, 'and');
                            let modifiedQuery = query.toLowerCase().replace(/&/g, 'and');
                            return modifiedTitle.indexOf(modifiedQuery);
                        });
                        // Limiting to the first 3 items
                        let results = _.first(sortedResults, 3)
                        return (!results) ? [] : results
                        
                    },
                    onSelect({ item }) {
                        sammy.quiet = false;
                        sammy.setLocation(item.url);
                    },
                    templates: {
                        header({ html }) {
                            return html`<span class="aa-SourceHeaderTitle">${t('Performance Hub Features')}</span>
                                <div class="aa-SourceHeaderLine" />`;
                        },
                        item({ item, components, html }) {
                            
                            // <div class="aa-ItemIcon"><img src="${item.icon}" alt="icon" /></div>
                            // href="${'#'}" onclick="event.preventDefault(); (${item.clickAction})();"
                            return html`<a class="aa-ItemLink">
                                <div class="aa-ItemContent">
                                  <div class="aa-ItemContentBody">
                                    <div class="aa-ItemContentTitle">
                                      ${item.title}
                                    </div>
                                  </div>
                                </div>
                              </a>`;
                        },
                    },
                },
                // RECENT KNOWLEDGE BASE SEARCHES
                {
                    getItems({ query }) {
                        if (!query.trim()) {
                            // Fetch the recently viewed articles and slice the array to get the first 4 items
                            return getRecentlyViewedArticles()
                        }
                        return [];
                    },
                    onSelect({ item }) {
                        sammy.quiet = false;
                        sammy.setLocation(`/wiki/#${encodeURIComponent(item.url)}`);
                    },
                    templates: {
                        header({ html }) {
                            return html`<span class="aa-SourceHeaderTitle">${t('Recently Viewed Knowledge Base Resources')}</span>
                                <div class="aa-SourceHeaderLine" />`;
                        },
                        item({ item, components, html }) {
                            // Convert camelCase string to space-separated words
                            const formatTopicAndSubtopicString = (str) => {
                                // Convert camelCase to space-separated words
                                let spacedStr = str.replace(/([a-z])([A-Z])/g, '$1 $2');
                                
                                // Replace &amp; with &
                                return spacedStr.replace(/&amp;/g, '&');
                            };
                            // Build breadcrumb
                            const breadcrumbs = ['Home'];
                            if(item.topic) breadcrumbs.push(formatTopicAndSubtopicString(item.topic));
                            if(item.subsopic) breadcrumbs.push(formatTopicAndSubtopicString(item.subsopic));
                            const breadcrumbStr = breadcrumbs.join(' > ');
                            
                            return html`<a class="aa-ItemLink">
                                <div class="aa-ItemContent">
                                  <div class="aa-ItemIcon">
                                      <svg class="" aria-hidden="true"  viewBox="0 0 16 16"  xmlns="http://www.w3.org/2000/svg"><path d="M7 0v5a2 2 0 0 0 2 2h5v7a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2zm2 0 5 5H9z" fill="currentColor"></path></svg>
                                  </div>
                                  <div class="aa-ItemContentBody">
                                    <div class="aa-ItemContentTitle">
                                      ${decodeHtmlEntities(item.label)}
                                    </div>
                                    <div class="aa-ItemContentBreadcrumb" style="font-size: 0.8em; color: #888;">
                                      ${breadcrumbStr}
                                    </div>
                                  </div>
                                </div>
                              </a>`;
                        },
                    },
                },
                // TOPICS AND CATEGORIES?
                {
                    sourceId: 'topics',
                    async getItems({ query }) {
                        // Return an empty array if the query is empty or just whitespace
                        // This way when you open the search - you don't see all the topics at the top of the list until you start typing...
                        if (!query.trim()) {
                            return Promise.resolve([]); 
                        }

                        // Fetch the topics from the API
                        let results = [];
                        try {
                            const response = await fetch('/openkb/api/topics');
                            const { data } = await response.json();
                            
                            data.forEach(topic => {
                                // For some reason the algolia search bugs out when trying to render a SVG inline - so we need to display the SVG as a base64 image...
                                let base64Icon = btoa(unescape(encodeURIComponent(topic.svg)));
                                topic.subtopics.forEach(subtopic => {
                                    results.push({
                                        topic: topic.topic,
                                        subtopic: subtopic.subtopic,
                                        icon: `data:image/svg+xml;base64,${base64Icon}`,
                                        label: `${topic.topic} - ${subtopic.subtopic}`,
                                        url: `/openkb/topic/${topic._id}/articles?subtopic=${subtopic.id}`
                                    });
                                });
                            });
                        } catch (error) {
                            console.error('Error processing topics:', error);
                        }
                        return results.filter(({ label }) =>
                            label.toLowerCase().includes(query.toLowerCase())
                        );
                    },
                    onSelect({ item }) {
                        sammy.quiet = false;
                        sammy.setLocation(`/wiki/#${encodeURIComponent(item.url)}`);
                    },
                    templates: {
                        header({ html }) {
                            return html`<span class="aa-SourceHeaderTitle">${t('Knowledge Base Topics & Categories')}</span>
                                <div class="aa-SourceHeaderLine" />`;
                        },
                        item({ item, components, html }) {
                            
                            return html`<a class="aa-ItemLink">
                                <div class="aa-ItemContent">
                                  <div class="aa-ItemIcon"><img src="${item.icon}" alt="icon" /></div>
                                  <div class="aa-ItemContentBody">
                                    <div class="aa-ItemContentTitle">
                                      ${item.subtopic}
                                    </div>
                                    <div class="aa-ItemContentDescription" style="font-size: 0.8em; color: #888;">
                                      ${item.topic}
                                    </div>
                                  </div>
                                </div>
                              </a>`;
                        },
                    },
                },
                // SEARCH RESULTS FROM ALGOLIA KNOWLEDGE BASE INDEX
                {
                    sourceId: 'hits',
                    getItems: function({ query }) {
                        const recentlyViewedArticles = getRecentlyViewedArticles();
                        // If the query is empty or just whitespace, retrieve the top 3 Knowledge Base articles
                        if (!query.trim() && recentlyViewedArticles.length) {
                            const numToRequest = Math.max(0, 6 - recentlyViewedArticles?.length);
                            let filters = `NOT objectID:${recentlyViewedArticles.map(article => article.id).join(' AND NOT objectID:')}`
                            return getAlgoliaResults({
                                searchClient,
                                queries: [{
                                    indexName: algoliaConfig.wikiIndex,
                                    params: {
                                        hitsPerPage: numToRequest,
                                    },
                                    // Add filters to exclude specific ID's that are already showing in the "recently viewed" results...
                                    filters: filters
                                }],
                            });
        
                        // Otherwise if it's a genuine search - return up to 6 results.
                        } else {
                            const club = $('#club').val();
                        
                            const algoliaArgs = {
                                highlightPreTag: "__aa-highlight__",
                                highlightPostTag: "__/aa-highlight__",
                                hitsPerPage: 6,
                                attributesToHighlight: ['kb_title'],
                                attributesToSnippet: ['kb_body'],
                                attributesToRetrieve: ['_id', 'kb_regional_visibility', 'kb_title', 'kb_viewcount', 'kb_seo_description', 'kb_body']
                            }
                            const data = {
                                searchTerms: query,
                                algoliaArgs,
                            }
                            // ID of the club (selectedID) is required to localise any algolia search results based on the selected club.
                            if(selectedID && selectedID != '') data.club = selectedID
                            if( club ) {
                                data.club = club;
                            }

                            return fetch('/api/algolia/search-wiki/', {
                                method: 'POST',
                                headers: {
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json'
                                  },
                                body: JSON.stringify(data)
                            })
                            .then(response => response.json())
                            .then(( result) => {
                                return result.suggestedArticles
                            });
                        }
                    },
                    onSelect({ item }) {
                        sammy.quiet = false;
                        sammy.setLocation(`/wiki/#${encodeURIComponent(`/openkb/kb/${item._id}`)}`);
                    },                    
                    templates: {
                        header({ html, state }) {
                            const title = (!state.query.trim()) ? t('Top Knowledge Base Resources') : t('Knowledge Base Resources');
                            return html `<span class="aa-SourceHeaderTitle">${title}</span>
                                <div class="aa-SourceHeaderLine" />`;
                        },
                        item({ item, components, html }) {
                            return html`<a class="aa-ItemLink">
                              <div class="aa-ItemContent">
                                <div class="aa-ItemIcon">
                                    <svg class="" aria-hidden="true"  viewBox="0 0 16 16"  xmlns="http://www.w3.org/2000/svg"><path d="M7 0v5a2 2 0 0 0 2 2h5v7a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2zm2 0 5 5H9z" fill="currentColor"></path></svg>
                                </div>
                                <div class="aa-ItemContentBody">
                                  <div class="aa-ItemContentTitle">
                                    ${components.Highlight({ hit: item, attribute: 'kb_title' })}
                                  </div>
                                  <div class="aa-ItemContentDescription">
                                    ${components.Snippet({ hit: item, attribute: 'kb_body'})}
                                  </div>
                                </div>
                              </div>
                            </a>`;
                        },
                        footer({ html }) {
                            return html`
                              <div class="aa-grouped-shortcut">
                                <div class="aa-footer-icon">
                                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11 16L8 19M8 19L5 16M8 19V5M13 8L16 5M16 5L19 8M16 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>                                
                                  <span>${t('Navigate')}</span>
                                </div>
                                <div class="aa-footer-icon">
                                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.7 17.3C9.3134 17.6866 8.6866 17.6866 8.3 17.3L3 12L8.3 6.7C8.6866 6.3134 9.3134 6.3134 9.7 6.7V6.7C10.0866 7.0866 10.0866 7.7134 9.7 8.1L6.8 11H19V8C19 7.44772 19.4477 7 20 7V7C20.5523 7 21 7.44772 21 8V12C21 12.5523 20.5523 13 20 13H6.8L9.7 15.9C10.0866 16.2866 10.0866 16.9134 9.7 17.3V17.3Z" fill="currentColor"/>
                                  </svg>                                
                                  <span>${t('Select')}</span>
                                </div>
                              </div>
                              <div class="aa-footer-icon">                              
                                <span class="keyboard-shortcut">${shortStr}</span>
                              </div>
                            `;
                          },
                    },
                },
                // SEARCH RESULTS FROM ALGOLIA STORE INDEX
                {
                    sourceId: 'store-products',
                    getItems: function({ query }) {
                        return fetch(`/api/store/clubs/${selectedID}/products?search=${query}`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            },
                        })
                        .then((response) => response.json())
                        .then(({ data }) => {
                            storeTotalHits = data?.totalItems ?? 0;
                            return data?.products ?? [];
                        })
                    },
                    onSelect({ item }) {
                        const objectID = encodeURIComponent(item.id);
                        sammy.quiet = false;
                        sammy.setLocation(`/store-ui/#%2Fstore%2F%3FsearchTerm%3D${objectID}`);
                    },                
                    templates: {
                        header({ html, state }) {
                            const title = t('STORE PRODUCTS');
                            return html`
                                <span class="aa-SourceHeaderTitle">${title}</span>
                                <div class="aa-SourceHeaderLine" />
                                ${state.query ? html`
                                    <a href="/store-ui/#%2Fstore%2F%3FsearchTerm%3D${encodeURIComponent(currentQuery)}"
                                      class="btn btn-default waves-effect view-design-search" 
                                      data-search="${state.query}">
                                        ${t('Search in Store')}
                                        <i class="material-icons">east</i>
                                    </a>
                                ` : ''}
                            `;
                        },
                        item({ item, components, html }) {
                            const formatDesc = (item) => {
                                const str = item.description;
                                const filtered = str.replace(/<(?!\/?mark\b)[^>]+>/g, '');
                                const maxLength = 200;
                                return filtered.length > maxLength
                                    ? filtered.substring(0, maxLength) + '...'
                                    : filtered;
                            }

                            return html`<a class="aa-ItemLink aa-ItemLink--store-product">
                              <div class="aa-ItemContent">
                                <div class="aa-designImage">
                                    <img data-src="${item?.images?.[0]}" width="40" class="img-loading" />
                                </div>
                                <div class="aa-ItemContentBody">
                                  <div class="aa-ItemContentTitle">
                                    ${html([item.title])}
                                  </div>
                                  <div class="aa-ItemContentBadges aa-ItemContentDescription">
                                    <div class="aa-badge">${item.brand}</div>
                                    <div class="aa-badge">${item.category}</div>
                                  </div>
                                  <div class="aa-ItemContentDescription">
                                    ${html([formatDesc(item)])}
                                  </div>
                                </div>
                              </div>
                            </a>`;
                        },
                        footer({ html }) {
                          return storeTotalHits > 3 ? html`<a class="aa-ItemLink show-more-results" href="/store-ui/#%2Fstore%2F%3FsearchTerm%3D${encodeURIComponent(currentQuery)}">
                              <div class="aa-ItemContent">
                                  <i class="material-icons">east</i>
                                  <div class="aa-ItemContentBody">
                                      <div class="aa-ItemContentTitle">
                                          ${t('Show')} <strong>${storeTotalHits - 3}</strong> ${t('more Store results for')} <strong>"${currentQuery}"</strong>
                                      </div>
                                  </div>
                              </div>
                          </a>` : null;
                        }
                    },
                },
                // MARKETING & PRINT TOOL
                {
                    sourceId: 'marketingprint',
                    getItems: function({ query }) {
                      if(!query.trim()) {
                          return [];
                      }
                  
                      return fetch('/api/algolia/search-marketing-print', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                          searchTerms: query,
                          club: selectedID,
                          designType: null,
                          adminQuery: localStorage.getItem('designerAllDesigns') === 'admin',
                          hitsPerPage: 3
                        })
                      })
                      .then(response => response.json())
                      .then(( result) => {
                        const totalHits = result?.pagination?.totalHits || 0;
                        const totalTagHits = result?.tags?.length || 0;
                        mpTotalHits = totalHits + totalTagHits;
                        return result.designs
                      });
                    },
                    onSelect({ item }) {
                        const id = encodeURIComponent(item.id);
                        const tag = encodeURIComponent(item.tags[0]);
                        sammy.quiet = false;
                        sammy.setLocation(`/designer/designs?tag=${tag}&designId=${id}`);  
                    },
                    templates: {
                        header({ html, state }) {
                            const title = t('Marketing & Print');
                            return html`
                                <span class="aa-SourceHeaderTitle">${title}</span>
                                <div class="aa-SourceHeaderLine" />
                                ${state.query ? html`
                                    <a href="/designer/designs-search?designSearch=${encodeURIComponent(currentQuery)}"
                                        class="btn btn-default waves-effect view-design-search" 
                                        data-search="${state.query}">
                                        ${t('Search in Marketing & Print')}
                                        <i class="material-icons">east</i>
                                    </a>
                                ` : ''}
                            `;
                        },
                        item({ item, components, html }) {
                            return html`<a class="aa-ItemLink">
                              <div class="aa-ItemContent">
                                <div class="aa-designImage">
                                    <img data-src="${item.thumbnails?.find(t => t.type === 'thumbnail.sm')?.location || item.thumbnail}" 
                                          alt="${item.name}" 
                                          width="75" 
                                          class="img-loading" />
                                </div>
                                <div class="aa-ItemContentBody">
                                  <div class="aa-ItemContentTitle">
                                    ${html([item._highlightResult?.name?.value || item.name])}
                                  </div>
                                  <div class="aa-ItemContentDescription">
                                    ${(() => {
                                        const searchTerm = currentQuery?.toLowerCase();
                                        const tag = item.tags[0];
                                        if (!searchTerm) return tag;
                                        
                                        // Split search term into individual words
                                        const searchWords = searchTerm.split(' ').filter(word => word.length > 0);
                                        let result = tag;
                                        
                                        // Process each search word
                                        searchWords.forEach(word => {
                                            const regex = new RegExp(`(${word})`, 'gi');
                                            result = result.replace(regex, (match) => `<mark>${match}</mark>`);
                                        });
                                        
                                        return html([result]);
                                    })()}
                                  </div>
                                </div>
                              </div>
                            </a>`;
                        },
                        footer({ html }) {
                            return mpTotalHits > 3 ? html`<a class="aa-ItemLink show-more-results" href="/designer/designs-search?designSearch=${encodeURIComponent(currentQuery)}">
                                <div class="aa-ItemContent">
                                    <i class="material-icons">east</i>
                                    <div class="aa-ItemContentBody">
                                        <div class="aa-ItemContentTitle">
                                            ${t('Show')} <strong>${mpTotalHits - 3}</strong> ${t('more Marketing & Print results for')} <strong>"${currentQuery}"</strong>
                                        </div>
                                    </div>
                                </div>
                            </a>` : null;
                        }

                    },
                },
                // CCTV PEOPLE
                {
                  sourceId: 'cctv-people',
                  getItems: function({ query }) {
                    if(!query.trim()) {
                        return [];
                    }
                
                    return fetch(`/api/cctv/people/${selectedID}?search=${query}`, {
                      method: 'GET',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                    })
                    .then(response => response.json())
                    .then(( result) => {
                      console.log('cctv people result', result);
                      return result.data
                    });
                  },                  
                  onSelect({ item }) {
                      sammy.quiet = false;
                      sammy.setLocation(`/cctv-ui/#${encodeURIComponent(`/cctv/people/${item.personID}`)}`);
                      postMessageToIframe(item.personID);
                  },
                  templates: {
                      header({ html, state }) {
                          const title = t('Activity Logs');
                          return html`
                              <span class="aa-SourceHeaderTitle">${title}</span>
                              <div class="aa-SourceHeaderLine" />`;
                      },
                      item({ item, components, html }) {
                          return html`
                            <a class="aa-ItemLink aa-itemLink--cctv-people" data-person-id="${item?.personID}">
                              <div class="aa-ItemContent">
                                <div class="aa-designImage">
                                    <img data-src="${item?.avatar || item?.shadowPhoto}" 
                                        alt="${item?.firstName} ${item?.lastName}" 
                                        width="40" 
                                        class="img-loading" />
                                </div>
                                <div class="aa-ItemContentBody">
                                  <div class="aa-ItemContentTitle">
                                    ${item?.firstName ? `${item?.firstName} ${item?.lastName}` : item?.personID}
                                  </div>
                                  <div class="aa-ItemContentDescription">
                                    ${item?.clubID} <mark class="badge badge-primary">${item?.membershipStatus}</mark>
                                  </div>
                                </div>
                              </div>
                            </a>
                          `;
                      },
                  },
                },
            ]);
        },
        renderNoResults({ render, html, state }, root) {
          render(
            html`
              ${
                state.query === '' ? html`<div class="isLoading">
                  <div class="aa-skeletonLoader">
                    <span class="use-skeleton rounded aa-skeletonLoader__icon">16</span>
                    <div class="aa-skeletonLoader__content">
                      <div class="use-skeleton rounded aa-skeletonLoader__title">16</div>
                      <div class="use-skeleton rounded aa-skeletonLoader__subtitle">14</div>
                    </div>
                  </div>
                  <div class="aa-skeletonLoader">
                    <span class="use-skeleton rounded aa-skeletonLoader__icon">16</span>
                    <div class="aa-skeletonLoader__content">
                      <div class="use-skeleton rounded aa-skeletonLoader__title">16</div>
                      <div class="use-skeleton rounded aa-skeletonLoader__subtitle">14</div>
                    </div>
                  </div>
                </div>
                ` : html`
                  <div class="aa-PanelLayout aa-Panel--scrollable aa-no-results">${t('There were no results found for the search term:')} "${state.query}".</div>
                `
              }
            `,root
          )
        },
        onStateChange({ state }) {
            currentQuery = state.query;
        },
    });
    function getRecentlyViewedArticles() {
        return JSON.parse(localStorage.getItem(`recent-articles-${selectedOrgID}`) || '[]').slice(0, 4);
    }
    function decodeHtmlEntities(str) {
        const entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&#x27;': "'",
            '&nbsp;': ' ',
            '&cent;': '¢',
            '&pound;': '£',
            '&yen;': '¥',
            '&euro;': '€',
            '&copy;': '©',
            '&reg;': '®',
            // ... you can add more as needed
        };
        for (let entity in entities) {
            str = str.split(entity).join(entities[entity]);
        }
        return str;
    }

    function searchKeyword (keyword) {
        
        globalAlgoliaSearch.setIsOpen(true);
        globalAlgoliaSearch.setQuery(keyword);
        globalAlgoliaSearch.refresh();
    }

    $('#search-result-wrap').on('click', '.search-results-tags', function (e){
        e.preventDefault();
        searchKeyword($(this).data('item'));
    });
    $('.related-article').on('click', function (e){
        e.preventDefault();
        searchKeyword($(this).data('item'));
    });
    function debouncePromise(fn, time) {
        let timerId = undefined;
        return function debounced(...args) {
            if (timerId) {
                clearTimeout(timerId);
            }
            return new Promise((resolve) => {
                timerId = setTimeout(() => resolve(fn(...args)), time);
            });
        };
    }
      
    const debounced = debouncePromise((items) => Promise.resolve(items), 300);

    // Add click handler after initialization
    $(document).on('click', '.view-design-search, .show-more-results', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        globalAlgoliaSearch.setIsOpen(false);
        sammy.quiet = false;
        sammy.setLocation(url);
        setTimeout(() => $('.search-bar').trigger('focus'), 300);
    });

    // Add this after autocomplete initialization
    const observer = new MutationObserver((mutations) => {
        mutations.forEach(() => {
            setTimeout(() => {
              const images = document.querySelectorAll('.aa-designImage img');
              
              images?.forEach(img => {
                const hasSrc = img.hasAttribute('src') && img.src === img.dataset.src;
                if (hasSrc) return;
                const dataSrc = img.dataset.src;

                img.src = dataSrc;
                img.onload = function() {
                    this.classList.remove('img-loading');
                    this.classList.add('img-loaded')
                };
                img.onerror = function() {
                    this.classList.add('img-not-found');
                    this.classList.remove('img-loading');
                };
              });
            }, 500)
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: false
    });
}