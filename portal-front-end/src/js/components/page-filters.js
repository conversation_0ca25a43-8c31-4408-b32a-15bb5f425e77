/*
    type FilterOption = {
        label: string;
        value: string;
        selected: boolean;
    }

    type FilterGroup = {
        id: string;
        label: string;
        options: FilterOption[];
        optional?: boolean; // if true, the filter can be removed by clicking the x
        visible?: boolean; // if true, the optional filter is visible
        closedValue?: string; // if set, the filter will be closed to this value
        searchBox?: boolean; // if true, a search box will be rendered for the filter
    }

    type parentElement = string | jQueryElement;
*/

class PageFilters {
    constructor(parentElement, filterGroups, onChange, disableQueryUpdate = false) {
        this.filterGroups = (filterGroups || []).map((group) => ({
            ...group,
            defaultValue: group.closedValue ?? group.options.find((option) => option.selected).value,
        }));

        this.parentElement = $(parentElement);
        this.onChange = onChange;
        this.disableQueryUpdate = disableQueryUpdate;

        try {
            this.validateFilterGroups(this.filterGroups);
            this.render();
        } catch (error) {
            console.error(error);
        }
    }

    validateFilterGroups(filterGroups) {
        const hasIds = filterGroups.every((group) => group.id);
        if (!hasIds) throw new Error('All filter groups must have an id');

        const hasLabels = filterGroups.every((group) => group.label);
        if (!hasLabels) throw new Error('All filter groups must have a label');

        const hasOptions = filterGroups.every((group) => group.options);
        if (!hasOptions) throw new Error('All filter groups must have options');

        const hasOptionLabels = filterGroups.every((group) => group.options.every((option) => option.label));
        if (!hasOptionLabels) throw new Error('All filter options must have a label');

        const hasOptionValues = filterGroups.every((group) => group.options.every((option) => option.value));
        if (!hasOptionValues) throw new Error('All filter options must have a value');

        const hasOptionSelected = filterGroups.every((group) => group.options.some((option) => option.selected));
        if (!hasOptionSelected) throw new Error('All filter groups must have a selected option');

        return true;
    }

    getValue(id) {
        const selected = this.filterGroups.find((group) => group.id === id)?.options.find((option) => option.selected);
        if (selected) return selected.value;

        const first = this.filterGroups.find((group) => group.id === id)?.options[0];
        if (first) return first.value;

        return null;
    }

    selectOption(id, value, overrideQueryUpdate = undefined) {
        this.filterGroups = this.filterGroups.map((group) => {
            if (group.id !== id) return group;

            const options = group.options.map((option) => {
                const selected = option.value == value; // type coercion intended
                return { ...option, selected };
            });

            return { ...group, options };
        });

        const filterObject = { [id]: value };
        this.render();
        // call onChange after render in case you want to re initialize the filter on change
        this.onChange?.(filterObject);
        if ((!this.disableQueryUpdate && overrideQueryUpdate === undefined) || overrideQueryUpdate) {
            updateHrefQuery(filterObject);
        }
    }

    selectAddFilter(id) {
        this.filterGroups = this.filterGroups.map((group) => {
            if (group.id !== id) return group;
            return { ...group, visible: true };
        });
        this.render();
    }

    renderFilterGroup({ id, label, options, optional, visible, searchBox }) {
        const filterGroup = document.createElement('div');
        $(filterGroup).addClass('filter-grp use-skeleton');
        $(filterGroup).attr('id', id);

        const selected = options.find((o) => o.selected) ?? options[0];

        if (optional && !visible) {
            $(filterGroup).addClass('hidden');
        }

        if (optional) {
            $(filterGroup).append(`<i class="fa fa-times filter-grp__remove" data-id="${id}"></i>`);
        }

        $(filterGroup).append(`
            <p class="filter-label">${t(label)}</p>
            <span class="filter-grp__divider"></span>
            <div class="filter">
                <button type="button" class="filter__selected">${t(selected.label)}</button>
                <ul class="filter__options" data-filter="${id}"></ul>
            </div>
        `);

        if (searchBox) {
            const filterOptionsEl = $(filterGroup).find('.filter__options');

            filterOptionsEl.append(
                `<li class="filter__search">
              <input type="search" class="filter__search-input" placeholder="${t('Search')} ${label}...">
            </li>`
            );

            filterOptionsEl.append(
                `<li class="filter__no-result hidden">
              <span>${t('No results found')}</span>
            </li>`
            );

            // render filtered options based on search input
            const searchInput = $(filterGroup).find('.filter__search-input');
            searchInput.on('input', (e) => {
                const value = e.target.value.toLowerCase();
                const filteredOptions = $(filterGroup).find('.filter__option');

                // hide no result
                $('.filter__no-result').addClass('hidden');

                filteredOptions.removeClass('hidden');
                let hiddenOptionCount = 0;

                filteredOptions.each((index, option) => {
                    const optionLabel = $(option).text().trim().toLowerCase();

                    const valueLowercase = value.toLowerCase();

                    if (!optionLabel.includes(valueLowercase)) {
                        hiddenOptionCount++;
                        $(option).addClass('hidden');
                    }
                });

                // if all fields are hidden
                if (filteredOptions.length === hiddenOptionCount) {
                    $('.filter__no-result').removeClass('hidden');
                }
            });
        }

        // render all options
        options.forEach((option) => this.renderFilterOption(filterGroup, id, option));

        $(filterGroup)
            .find('.filter__selected')
            .on('click', (e) => {
                // hide all existing filter options that are open
                $('.filter__options').removeClass('filter__options--active');
                e.stopPropagation();
                $(e.currentTarget).siblings('.filter__options').toggleClass('filter__options--active');
            });

        $(filterGroup)
            .find('.filter-grp__remove')
            .on('click', (e) => {
                e.stopPropagation();
                this.filterGroups = this.filterGroups.map((group) => {
                    if (group.id !== id) return group;

                    const options = group.options.map((option) => {
                        const selected = option.value === group.defaultValue;
                        return { ...option, selected };
                    });

                    return { ...group, visible: false, options };
                });

                const defaultValue = this.filterGroups.find((group) => group.id === id)?.defaultValue;
                this.onChange?.({ [id]: defaultValue });
                updateHrefQuery({ [id]: undefined });
                this.render();
            });

        return filterGroup;
    }

    renderFilterOption(filterGroup, id, { label, value, selected }) {
        const active = selected ? 'filter__option--active' : '';

        const filterOptionsEl = $(filterGroup).find('.filter__options');

        filterOptionsEl.append(`
            <li class="filter__option ${active}" data-value="${value}">
                ${t(label)}
            </li>
        `);

        $(filterGroup)
            .find(`.filter__option[data-value="${value}"]`)
            .on('click', (e) => {
                e.stopPropagation();
                if (!$(filterGroup).hasClass('add-filter')) {
                    this.selectOption(id, value);
                } else {
                    this.selectAddFilter(value);
                }
            });
    }

    renderAddFilterButton() {
        const groupId = 'add-filter';
        const filterGroup = document.createElement('div');
        $(filterGroup).addClass('filter-grp add-filter');
        $(filterGroup).attr('id', groupId);

        $(filterGroup).append(`
            <i class="fa fa-circle-plus filter-grp__add"></i>
            <p class="filter-label">${t('ADD FILTER')}</p>
            <ul class="filter__options" data-filter="range"></ul>
        `);

        const options = this.filterGroups
            .filter((group) => !group.visible && group.optional)
            .map((group) => ({
                label: group.label,
                value: group.id,
                selected: false,
            }));

        options.forEach((option) => this.renderFilterOption(filterGroup, groupId, option));

        if (options.length < 1) {
            $(filterGroup).addClass('disabled');
        }

        $(filterGroup).on('click', (e) => {
            e.stopPropagation();
            $(e.currentTarget).find('.filter__options').toggleClass('filter__options--active');
        });

        return filterGroup;
    }

    render() {
        this.parentElement.html('');
        this.filterGroups.forEach((group) => this.parentElement.append(this.renderFilterGroup(group)));

        if (this.filterGroups.some((group) => group.optional)) {
            this.parentElement.append(this.renderAddFilterButton());
        }
    }
}
