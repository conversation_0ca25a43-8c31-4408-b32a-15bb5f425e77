// Some routes are allowed access without a club selected...
let globallyAllowedRoutes = ['/notifications'];

function setMenuToggleEvent() {
    $('.menu-toggle').on('click', function (e) {
        const content = $(this).next();
        setTimeout(() => $('.dropdown-overlay').addClass('active'), 300);

        if (smallSidebar) {
            $(this).addClass('toggled');
            $(this).next().addClass('active');
            content.slideDown(320);
        } else {
            $(this).toggleClass('toggled');
            $(this).next().toggleClass('active');
            content.slideToggle(320);
        }
    });
}

function buildSideMenu(forcedRoutes) {
    const root = $('#leftsidebar > .menu');
    $('.menu-toggle').off().removeClass('no-toggle');
    $('.menu-toggle:not(.toggled)').next().hide();

    if (!sideMenuAdminMode) {
        const modules = (selectedClubObj?.modules || []).filter(
            (module) => !forcedRoutes || forcedRoutes.includes(module.baseUIPath)
        );

        root.find('a,.header').addClass('hidden');

        globallyAllowedRoutes.forEach((route) => {
            const link = root.find(`a[href="${route}"]`);
            if (!link.length) return;
            link.removeClass('hidden');
            link.closest('.ml-menu').siblings('.menu-toggle').removeClass('hidden');
            link.closest('.group').find('.header').removeClass('hidden');
        });

        modules.forEach(({ moduleId, baseUIPath }) => {
            const link =
                moduleId === 'gymmaster-mms' ? root.find('#nav-gm-portal a') : root.find(`a[href="${baseUIPath}"]`);

            link.removeClass('hidden');
            const menu = link.closest('.ml-menu').siblings('.menu-toggle');
            menu.removeClass('hidden');

            menu.closest('.group').find('.header').removeClass('hidden');
        });

        if (modules.length > 8) {
            setMenuToggleEvent();
        } else {
            $('.menu-toggle').addClass('no-toggle');
            $('.menu-toggle').next().show();
        }
    } else {
        root.find('a,.header').removeClass('hidden');
        setMenuToggleEvent();
    }

    signedInUserData?.isGlobalAdmin
        ? $('#leftsidebar .admin-mode').removeClass('hidden')
        : $('#leftsidebar .admin-mode').addClass('hidden');

    $('#leftsidebar .admin-mode').off('click');
    $('#leftsidebar .admin-mode').find('input').prop('checked', sideMenuAdminMode);
    $('#leftsidebar .admin-mode').on('click', function () {
        sideMenuAdminMode = $(this).find('input').is(':checked');
        setSessionStorage('sideMenuAdminMode', sideMenuAdminMode);
        buildSideMenu();

        if (
            !sideMenuAdminMode &&
            ['/', '/dashboard'].includes(window.location.pathname) &&
            selectedClubObj.redirectDashboard &&
            selectedClubObj.redirectDashboard !== '/dashboard'
        )
            return sammy.setLocation(selectedClubObj.redirectDashboard);
    });

    quickNavagateRoutes = [];

    // Cycle through our navagation bar - and just push whatever pages are visible to this user into Algolia as "shortcuts"
    $('#leftsidebar ul > li a, #leftsidebar ul > div > li a').each(function () {
        $(this)
            .parent()
            .find('ul > li > a')
            .each(function () {
                if ($(this).hasClass('hidden')) return;

                // If this route is not hidden - push into our mapping that is used in the "Search/Navigate Performance Hub CMD+K"
                quickNavagateRoutes.push({
                    title: $(this)
                        .contents()
                        .filter(function () {
                            return this.nodeType == 3; // Node type 3 is a text node
                        })
                        .text()
                        .trim(),
                    url: $(this).attr('href'),
                });
            });
    });

    // Lastly - add items into the nav that are "first level" / not hard coded...
    quickNavagateRoutes.push({ title: t('Home'), url: '/' });
    quickNavagateRoutes.push({ title: t('Dashboard'), url: '/' });
    quickNavagateRoutes.push({ title: t('Performance Hub Store'), url: '/store-ui' });
}
