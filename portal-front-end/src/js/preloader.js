let ownerOutstandingAgreements = null;
let saasBillingLockOut = null;
let orgLogosCached = null;

$(async () => {
    // Set menu state (small/large) based on previous session...
    setTimeout(function () {
        firstLoadSetSmallNavState();
    }, 500);

    // wait for user to be set
    await new Promise((resolve) => {
        const interval = setInterval(() => {
            if (!signedInUserData || !orgLogosCached) return;
            clearInterval(interval);
            resolve();
        }, 300);
    });

    sCheckOutstandingAgreements(() => {
        isClubAccessRestrictedByBilling(() => {
            $('firstLoader').hide();
            $('application').show();
            startApp();
        });
    });
});

async function lockOutSidebarAndBanner() {
    const hasOutstandingAgreements =
        !signedInUserData.isGlobalAdmin &&
        (ownerOutstandingAgreements?.filter((agreement) => agreement.club === selectedID) ?? []) > 0;

    if (hasOutstandingAgreements || saasBillingLockOut) {
        const allowedRoutes = ['/saas-billing', '/support', '/club-kyc', '/agreements'];

        // hide the logo, notifications, and bars
        $('.navbar-logo').css('pointer-events', 'none');
        $('#notifications-right-side').addClass('hidden');
        $('.navbar-notifications-container').addClass('hidden');
        $('.bars').addClass('hidden');
        $('.locked-out-banner').removeClass('hidden');

        // This moves the content "down" so the banner does not show over the navbar etc
        $('#main-body-container').css('padding-top', '36px');
        $('#leftsidebar').css('margin-top', '36px');
        $('#main-body-container').addClass('stage-environment');

        // banner text depends on the user's permission
        const membersRes = await getClubOwnersAPI(selectedID);

        const ownerEmails = (membersRes[0] ? membersRes[1] : []).reduce((acc, owner) => {
            const altEmails = owner.alternativeEmails || [];
            return [...acc, owner.email, ...altEmails];
        }, []);

        if (ownerEmails.includes(signedinUser)) {
            const message =
                'Your account currently has restricted access. Please check you have no outstanding agreements to be signed, your billing details are up to date, and you have no outstanding invoices. Please contact support for more information.';
            $('.locked-out-banner .locked-out-text').html(t(message));
        } else {
            const message = `Your account currently has restricted access. Please login with one of the following accounts to resolve:`;
            $('.locked-out-banner .locked-out-text').html(
                t(message) + '&nbsp;' + ownerEmails.map((email) => email).join(', ')
            );
            $('.locked-out-banner .actions').html('');
        }

        const { pathname } = window.location;
        const routeIsAllowed = allowedRoutes.some((route) => pathname.includes(route) && !pathname.includes('admin'));
        sammy.quiet = false;

        if (!routeIsAllowed) {
            // prioritize club agreements over saas billing
            sammy.setLocation(hasOutstandingAgreements ? '/agreements' : '/saas-billing/payment-settings');
        } else if (!routeIsAllowed) {
            sammy.setLocation('/support');
        }

        // use default hidden route logic and ignore the allowedRoutes array
        buildSideMenu(allowedRoutes);
        return;
    }

    // show the logo, notifications, and bars
    $('.locked-out-banner').addClass('hidden');
    $('.navbar-logo').css('pointer-events', 'unset');
    $('#notifications-right-side').removeClass('hidden');
    $('.navbar-notifications-container').removeClass('hidden');
    $('.bars').removeClass('hidden');

    buildSideMenu();
    firstLoadSetSmallNavState();
}
