let apiCache = {};

// Available via: window.browserInfo

(function (window) {
    {
    /* test cases
        alert(
            'browserInfo result: OS: ' + browserInfo.os +' '+ browserInfo.osVersion + '\n'+
                'Browser: ' + browserInfo.browser +' '+ browserInfo.browserVersion + '\n' +
                'Mobile: ' + browserInfo.mobile + '\n' +
                'Cookies: ' + browserInfo.cookies + '\n' +
                'Screen Size: ' + browserInfo.screen
        );
    */
        var unknown = 'Unknown';

        // screen
        var screenSize = '';
        if (screen.width) {
            width = (screen.width) ? screen.width : '';
            height = (screen.height) ? screen.height : '';
            screenSize += '' + width + " x " + height;
        }

        //browser
        var nVer = navigator.appVersion;
        var nAgt = navigator.userAgent;
        var browser = navigator.appName;
        var version = '' + parseFloat(navigator.appVersion);
        var majorVersion = parseInt(navigator.appVersion, 10);
        var nameOffset, verOffset, ix;

        // Opera
        if ((verOffset = nAgt.indexOf('Opera')) != -1) {
            browser = 'Opera';
            version = nAgt.substring(verOffset + 6);
            if ((verOffset = nAgt.indexOf('Version')) != -1) {
                version = nAgt.substring(verOffset + 8);
            }
        }
        // MSIE
        else if ((verOffset = nAgt.indexOf('MSIE')) != -1) {
            browser = 'Microsoft Internet Explorer';
            version = nAgt.substring(verOffset + 5);
        }

        //IE 11 no longer identifies itself as MS IE, so trap it
        //http://stackoverflow.com/questions/17907445/how-to-detect-ie11
        else if ((browser == 'Netscape') && (nAgt.indexOf('Trident/') != -1)) {

            browser = 'Microsoft Internet Explorer';
            version = nAgt.substring(verOffset + 5);
            if ((verOffset = nAgt.indexOf('rv:')) != -1) {
                version = nAgt.substring(verOffset + 3);
            }

        }

        // Chrome
        else if ((verOffset = nAgt.indexOf('Chrome')) != -1) {
            browser = 'Chrome';
            version = nAgt.substring(verOffset + 7);
        }
        // Safari
        else if ((verOffset = nAgt.indexOf('Safari')) != -1) {
            browser = 'Safari';
            version = nAgt.substring(verOffset + 7);
            if ((verOffset = nAgt.indexOf('Version')) != -1) {
                version = nAgt.substring(verOffset + 8);
            }

            // Chrome on iPad identifies itself as Safari. Actual results do not match what Google claims
            //  at: https://developers.google.com/chrome/mobile/docs/user-agent?hl=ja
            //  No mention of chrome in the user agent string. However it does mention CriOS, which presumably
            //  can be keyed on to detect it.
            if (nAgt.indexOf('CriOS') != -1) {
                //Chrome on iPad spoofing Safari...correct it.
                browser = 'Chrome';
                //Don't believe there is a way to grab the accurate version number, so leaving that for now.
            }
        }
        // Firefox
        else if ((verOffset = nAgt.indexOf('Firefox')) != -1) {
            browser = 'Firefox';
            version = nAgt.substring(verOffset + 8);
        }
        // Other browsers
        else if ((nameOffset = nAgt.lastIndexOf(' ') + 1) < (verOffset = nAgt.lastIndexOf('/'))) {
            browser = nAgt.substring(nameOffset, verOffset);
            version = nAgt.substring(verOffset + 1);
            if (browser.toLowerCase() == browser.toUpperCase()) {
                browser = navigator.appName;
            }
        }
        // trim the version string
        if ((ix = version.indexOf(';')) != -1) version = version.substring(0, ix);
        if ((ix = version.indexOf(' ')) != -1) version = version.substring(0, ix);
        if ((ix = version.indexOf(')')) != -1) version = version.substring(0, ix);

        majorVersion = parseInt('' + version, 10);
        if (isNaN(majorVersion)) {
            version = '' + parseFloat(navigator.appVersion);
            majorVersion = parseInt(navigator.appVersion, 10);
        }

        // mobile version
        var mobile = /Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(nVer);

        // cookie
        var cookieEnabled = (navigator.cookieEnabled) ? true : false;

        if (typeof navigator.cookieEnabled == 'undefined' && !cookieEnabled) {
            document.cookie = 'testcookie';
            cookieEnabled = (document.cookie.indexOf('testcookie') != -1) ? true : false;
        }

        // system
        var os = unknown;
        var clientStrings = [
            {s:'Windows 3.11', r:/Win16/},
            {s:'Windows 95', r:/(Windows 95|Win95|Windows_95)/},
            {s:'Windows ME', r:/(Win 9x 4.90|Windows ME)/},
            {s:'Windows 98', r:/(Windows 98|Win98)/},
            {s:'Windows CE', r:/Windows CE/},
            {s:'Windows 2000', r:/(Windows NT 5.0|Windows 2000)/},
            {s:'Windows XP', r:/(Windows NT 5.1|Windows XP)/},
            {s:'Windows Server 2003', r:/Windows NT 5.2/},
            {s:'Windows Vista', r:/Windows NT 6.0/},
            {s:'Windows 7', r:/(Windows 7|Windows NT 6.1)/},
            {s:'Windows 8.1', r:/(Windows 8.1|Windows NT 6.3)/},
            {s:'Windows 8', r:/(Windows 8|Windows NT 6.2)/},
            {s:'Windows NT 4.0', r:/(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/},
            {s:'Windows ME', r:/Windows ME/},
            {s:'Android', r:/Android/},
            {s:'Open BSD', r:/OpenBSD/},
            {s:'Sun OS', r:/SunOS/},
            {s:'Linux', r:/(Linux|X11)/},
            {s:'iOS', r:/(iPhone|iPad|iPod)/},
            {s:'Mac OS X', r:/Mac OS X/},
            {s:'Mac OS', r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},
            {s:'QNX', r:/QNX/},
            {s:'UNIX', r:/UNIX/},
            {s:'BeOS', r:/BeOS/},
            {s:'OS/2', r:/OS\/2/},
            {s:'Search Bot', r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}
        ];
        for (var id in clientStrings) {
            var cs = clientStrings[id];
            if (cs.r.test(nAgt)) {
                os = cs.s;
                break;
            }
        }

        var osVersion = unknown;

        if (/Windows/.test(os)) {
            osVersion = /Windows (.*)/.exec(os)[1];
            os = 'Windows';
        }

        switch (os) {
            case 'Mac OS X':
                osVersion = /Mac OS X (10[\.\_\d]+)/.exec(nAgt)[1];
                break;

            case 'Android':
                osVersion = /Android ([\.\_\d]+)/.exec(nAgt)[1];
                break;

            case 'iOS':
                osVersion = /OS (\d+)_(\d+)_?(\d+)?/.exec(nVer);
                osVersion = osVersion[1] + '.' + osVersion[2] + '.' + (osVersion[3] | 0);
                break;

        }

        // prevent dropdowns from causing div overflow
        $(document).on('show.bs.dropdown', '.dropdown.container-body', (e) => {
            const dropdown = $(e.currentTarget);
            $('body').append(dropdown.css({
                position: 'absolute',
                left: dropdown.offset().left,
                top: dropdown.offset().top
            }).detach());
        });
    }

    window.browserInfo = {
        screen: screenSize,
        browser: browser,
        browserVersion: version,
        mobile: mobile,
        os: os,
        osVersion: osVersion,
        cookies: cookieEnabled
    };
}(this));

// Basic function to only push unique items into array...
Array.prototype.pushUnique = function (item){
    if(this.indexOf(item) == -1) {
    //if(jQuery.inArray(item, this) == -1) {
        this.push(item);
        return true;
    }
    return false;
}

// Capatalise a string...
String.prototype.capitalize = function() {
    return this.replace(/(?:^|\s)\S/g, function(a) { return a.toUpperCase(); });
};

// Basic currency formatting
Number.prototype.formatCurrency = function(n, x) {
    var re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\.' : '$') + ')';
    return this.toFixed(Math.max(0, ~~n)).replace(new RegExp(re, 'g'), '$&,');
};

Number.prototype.formatStripeCurrency = function (currency = 'AUD', addSymbol = true) {
    const [decimalPlaces, multiplier] = getStripeCurrencyDecimalPlaces(currency);
    const fixedAmount = (this / multiplier).toFixed(decimalPlaces);

    const re = '\\d(?=(\\d{3})+' + (decimalPlaces > 0 ? '\\.' : '$') + ')';
    const formattedAmount = fixedAmount.replace(new RegExp(re, 'g'), '$&,');

    const symbol = getCurrencySymbol(currency);
    return addSymbol ? `${symbol}${formattedAmount}` : formattedAmount;
}

function getStripeCurrencyDecimalPlaces(currency = 'aud') {
    const zeroDecimalCurrencies = [
        'BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'
    ];

    const threeDecimalCurrencies = ['BHD', 'JOD', 'KWD', 'LYD', 'OMR', 'TND'];

    if (zeroDecimalCurrencies.includes(currency.toUpperCase())) {
        return [0, 1];
    }

    if (threeDecimalCurrencies.includes(currency.toUpperCase())) {
        return [3, 1000];
    }

    return [2, 100];
}

String.prototype.removeHTML = function() {
    return this.replace(/<\/?[^>]+(>|$)/g, "");
};

Number.prototype.between = function(a, b) {
  var min = Math.min.apply(Math, [a, b]),
    max = Math.max.apply(Math, [a, b]);
  return this > min && this < max;
};

String.prototype.splice = function(idx, rem, str) {
    return this.slice(0, idx) + str + this.slice(idx + Math.abs(rem));
};

// Basic Time formatting...
function secondsToDhms(seconds) {
    seconds = Number(seconds);
    var d = Math.floor(seconds / (3600*24));
    var h = Math.floor(seconds % (3600*24) / 3600);
    var m = Math.floor(seconds % 3600 / 60);
    var s = Math.floor(seconds % 60);

    var dDisplay = d > 0 ? d + (d == 1 ? `${t('d')} ` : `${t('d')} `) : "";
    var hDisplay = h > 0 ? h + (h == 1 ? `${t('h')} ` : `${t('h')} `) : "";
    var mDisplay = m > 0 ? m + (m == 1 ? `${t('m')} ` : `${t('m')} `) : "";
    var sDisplay = s > 0 ? s + (s == 1 ? `${t('s')}` : `${t('s')} `) : "";
    return dDisplay + hDisplay + mDisplay + sDisplay;
}

function getManufacturerNameFromAssetCode(code) {
    const manufacturers = {
        "ACI": "Asus (ASUSTeK Computer Inc.)",
        "ACR": "Acer America Corp.",
        "ACT": "Targa",
        "ADI": "ADI Corporation",
        "AMW": "AMW",
        "AOC": "AOC International (USA) Ltd.",
        "API": "Acer America Corp.",
        "APP": "Apple Computer, Inc.",
        "ART": "ArtMedia",
        "AST": "AST Research",
        "AUO": "AU Optronics",
        "BMM": "BMM",
        "BNQ": "BenQ Corporation",
        "BOE": "BOE Display Technology",
        "CPL": "Compal Electronics, Inc. / ALFA",
        "CPQ": "COMPAQ Computer Corp.",
        "CTX": "CTX / Chuntex Electronic Co.",
        "DEC": "Digital Equipment Corporation",
        "DEL": "Dell Computer Corp.",
        "DPC": "Delta Electronics, Inc.",
        "DWE": "Daewoo Telecom Ltd",
        "ECS": "ELITEGROUP Computer Systems",
        "EIZ": "EIZO",
        "EPI": "Envision Peripherals, Inc.",
        "FCM": "Funai Electric Company of Taiwan",
        "FUS": "Fujitsu Siemens",
        "GSM": "LG Electronics Inc. (GoldStar Technology, Inc.)",
        "GWY": "Gateway 2000",
        "HEI": "Hyundai Electronics Industries Co., Ltd.",
        "HIQ": "Hyundai ImageQuest",
        "HIT": "Hitachi",
        "HSD": "Hannspree Inc",
        "HSL": "Hansol Electronics",
        "HTC": "Hitachi Ltd. / Nissei Sangyo America Ltd.",
        "HWP": "Hewlett Packard",
        "IBM": "IBM PC Company",
        "ICL": "Fujitsu ICL",
        "IFS": "InFocus",
        "IQT": "Hyundai",
        "IVM": "Idek Iiyama North America, Inc.",
        "KDS": "KDS USA",
        "KFC": "KFC Computek",
        "LEN": "Lenovo",
        "LGD": "LG Display",
        "LKM": "ADLAS / AZALEA",
        "LNK": "LINK Technologies, Inc.",
        "LPL": "LG Philips",
        "LTN": "Lite-On",
        "MAG": "MAG InnoVision",
        "MAX": "Maxdata Computer GmbH",
        "MEI": "Panasonic Comm. & Systems Co.",
        "MEL": "Mitsubishi Electronics",
        "MIR": "Miro Computer Products AG",
        "MTC": "MITAC",
        "NAN": "NANAO",
        "NEC": "NEC Technologies, Inc.",
        "NOK": "Nokia",
        "NVD": "Nvidia",
        "OQI": "OPTIQUEST",
        "PBN": "Packard Bell",
        "PCK": "Daewoo",
        "PDC": "Polaroid",
        "PGS": "Princeton Graphic Systems",
        "PHL": "Philips Consumer Electronics Co.",
        "PRT": "Princeton",
        "REL": "Relisys",
        "SAM": "Samsung",
        "SEC": "Seiko Epson Corporation",
        "SMC": "Samtron",
        "SMI": "Smile",
        "SNI": "Siemens Nixdorf",
        "SNY": "Sony Corporation",
        "SPT": "Sceptre",
        "SRC": "Shamrock Technology",
        "STN": "Samtron",
        "STP": "Sceptre",
        "TAT": "Tatung Co. of America, Inc.",
        "TRL": "Royal Information Company",
        "TSB": "Toshiba, Inc.",
        "UNM": "Unisys Corporation",
        "VSC": "ViewSonic Corporation",
        "WTC": "Wen Technology",
        "ZCM": "Zenith Data Systems"
    };

    return manufacturers[code] || "Unknown";
}



// Friendly formatting of bytes (device management)...
function humanFileSize(bytes, si=false, dp=1) {
    const thresh = si ? 1000 : 1024;

    if (Math.abs(bytes) < thresh) {
        return bytes + ' B';
    }

    const units = si ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'] : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
    let u = -1;
    const r = 10**dp;

    do {
        bytes /= thresh;
        ++u;
    } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);

    return bytes.toFixed(dp) + ' ' + units[u];
}

// Generate UUID on the front-end...
function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

function timeDifference(current, previous) {
    var msPerMinute = 60 * 1000;
    var msPerHour = msPerMinute * 60;
    var msPerDay = msPerHour * 24;
    var msPerMonth = msPerDay * 30;
    var msPerYear = msPerDay * 365;

    var elapsed = current - previous;

    if (elapsed < msPerMinute) {
         return Math.round(elapsed/1000) + ' '+t('Seconds ago');
    }

    else if (elapsed < msPerHour) {
         return Math.round(elapsed/msPerMinute) + ` ${t(`Minute${ (Math.round(elapsed/msPerMinute) > 1) ? 's' : ''} ago`)}`;
    }

    else if (elapsed < msPerDay ) {
         return Math.round(elapsed/msPerHour ) + ` ${t(`Hour${ (Math.round(elapsed/msPerHour) > 1) ? 's' : ''} ago`)}`;
    }

    else if (elapsed < msPerMonth) {
        return '' + Math.round(elapsed/msPerDay) + ` ${t(`Day${ (Math.round(elapsed/msPerDay) > 1) ? 's' : ''} ago`)}`;
    }

    else if (elapsed < msPerYear) {
        return '' + Math.round(elapsed/msPerMonth) + ` ${t(`Month${ (Math.round(elapsed/msPerMonth) > 1) ? 's' : ''} ago`)}`;
    }

    else {
        return '' + Math.round(elapsed/msPerYear ) + ` ${t(`Year${ (Math.round(elapsed/msPerYear) > 1) ? 's' : ''} ago`)}`;
    }
}

function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

function getPercentageChange(oldNumber, newNumber){
    var decreaseValue = Number(oldNumber) - Number(newNumber);
    var result = Math.round( ((decreaseValue / Number(oldNumber)) * 100)  * 100) / 100;
    return isFinite(result) ? result : 0
}

function getDifferenceChange(oldNumber, newNumber){
    var differenceValue = Number(oldNumber) - Number(newNumber);
    return differenceValue
}

function getOS() {
    var userAgent = window.navigator.userAgent,
        platform = window.navigator.platform,
        macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'],
        windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'],
        iosPlatforms = ['iPhone', 'iPad', 'iPod'],
        os = null;

    if (macosPlatforms.indexOf(platform) !== -1) {
        os = 'mac';
    } else if (iosPlatforms.indexOf(platform) !== -1) {
        os = 'ios';
    } else if (windowsPlatforms.indexOf(platform) !== -1) {
        os = 'windows';
    } else if (/Android/.test(userAgent)) {
        os = 'android';
    } else if (!os && /Linux/.test(platform)) {
        os = 'linux';
    }

    return os;
}

// Read a page's GET URL variables and return them as an object.
function getUrlVars() {

    var vars = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');

    for(var i = 0; i < hashes.length; i++) {
        hash = hashes[i].split('=');
        vars.push(hash[0]);
        vars[hash[0]] = hash[1];
    }
    if(vars[0] == window.location.href) {
        return false;
    } else {
        return vars;
    }
}


// Basic string filtering to get the filename from a string/uri...
String.prototype.filename=function(extension){
    var s= this.replace(/\\/g, '/');
    s= s.substring(s.lastIndexOf('/')+ 1);
    return extension? s.replace(/[?#].+$/, ''): s.split('.')[0];
}

String.prototype.trunc = function(n) {
    return this.substr(0,n-1)+(this.length>n?'&hellip;':'');
};

String.prototype.toTitleCase = function () {
    return this.replace(/\w\S*/g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();});
};


// check if an element exists in array using a comparer function
// comparer : function(currentElement)
Array.prototype.inArray = function(comparer) {
    for(var i=0; i < this.length; i++) {
        if(comparer(this[i])) return true;
    }
    return false;
};

// Simple function to better format currency...
Number.prototype.format = function(n, x) {
    var re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\.' : '$') + ')';
    return this.toFixed(Math.max(0, ~~n)).replace(new RegExp(re, 'g'), '$&,');
};


// Dashboard formatting...
function humaniseDays (diff) {

    // The string we're working with to create the representation
    var str = '';
    // Map lengths of `diff` to different time periods
    var values = [[' year', 365], [' month', 30], [' day', 1]];

    // Iterate over the values...
    for (var i=0;i<values.length;i++) {
        var amount = Math.floor(diff / values[i][1]);

        // ... and find the largest time value that fits into the diff
        if (amount >= 1) {
           // If we match, add to the string ('s' is for pluralization)
           str += amount + t(values[i][0] + (amount > 1 ? 's' : '')) + ' ';

           // and subtract from the diff
           diff -= amount * values[i][1];
        }
    }

    return t(str);
}

// Date formatting to yyyy-mm-dd...
function formatDateYMD(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2)
        month = '0' + month;
    if (day.length < 2)
        day = '0' + day;

    return [year, month, day].join('-');
}

function unixTimeFormat(UNIX_timestamp){
    var a = new Date(UNIX_timestamp * 1000);
    var months = [t('Jan'), t('Feb'), t('Mar'), t('Apr'), t('May'), t('Jun'), t('Jul'), t('Aug'), t('Sep'), t('Oct'), t('Nov'), t('Dec')];
    var year = a.getFullYear();
    var month = months[a.getMonth()];
    var date = a.getDate();
    var hour = a.getHours();
    var min = (a.getMinutes() < 10 ? '0' : '') + a.getMinutes();
    var sec = (a.getSeconds() < 10 ? '0' : '') + a.getSeconds();
    var time = `${month}${month !== 'May' ? '.' : ''} ${date}, ${year}`;

    return time;
}

function reverseForIn(obj, f) {
    var arr = [];
    for (var key in obj) {
        // add hasOwnPropertyCheck if needed
        arr.push(key);
    }
    for (var i=arr.length-1; i>=0; i--) {
        f.call(obj, arr[i]);
    }
}

function instantNotification(message='Success!', type='success', timeout=5000) {
    const Notify = Swal.mixin({
        toast: true,
        position: 'top-end',
        animation: false,
        showConfirmButton: false,
        timer: timeout,
        showCloseButton: true,
    });

    Notify.fire({
        type,
        title: message
    })
}

function addClipboardCopyButtons(baseElement, {childEl = 'code'} = {}) {

    CLIPBTN = `
        <div class="copy-clipboard ph-copy-clipboard" data-toggle="tooltip" title="${t('Copy To Clipboard')}">
            <svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="copy" class="svg-inline--fa fa-copy fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M433.941 65.941l-51.882-51.882A48 48 0 0 0 348.118 0H176c-26.51 0-48 21.49-48 48v48H48c-26.51 0-48 21.49-48 48v320c0 26.51 21.49 48 48 48h224c26.51 0 48-21.49 48-48v-48h80c26.51 0 48-21.49 48-48V99.882a48 48 0 0 0-14.059-33.941zM266 464H54a6 6 0 0 1-6-6V150a6 6 0 0 1 6-6h74v224c0 26.51 21.49 48 48 48h96v42a6 6 0 0 1-6 6zm128-96H182a6 6 0 0 1-6-6V54a6 6 0 0 1 6-6h106v88c0 13.255 10.745 24 24 24h88v202a6 6 0 0 1-6 6zm6-256h-64V48h9.632c1.591 0 3.117.632 4.243 1.757l48.368 48.368a6 6 0 0 1 1.757 4.243V112z"></path></svg>
        </div>
    `;
    $(baseElement).find(childEl).each(function(index) {
        $(this).after(CLIPBTN);
        $(this).parent().on('click','.copy-clipboard', function(){
            navigator.clipboard.writeText($(this).parent().find('code').text());

            // Show copied notification...
            $(`
                <div
                    style="top:${ $(this).position().top }px; left:${ $(this).position().left }px"
                    class="copied-tooltip top"
                >
                    <div class="ttip">
                        <p>${t('Copied!')}</p>
                    </div>
                </div>
            `).appendTo(this).delay(1500).queue(function() { $(this).remove() })

        });
    })

    $('[data-toggle="tooltip"]').tooltip({ container: 'body' });

}

// https://stackoverflow.com/questions/1129216/sort-array-of-objects-by-string-property-value
function dynamicSort(property) {
    var sortOrder = 1;
    if(property[0] === "-") {
        sortOrder = -1;
        property = property.substr(1);
    }
    return function (a,b) {
        /* next line works with strings and numbers,
         * and you may want to customize it to your needs
         */
        var result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
        return result * sortOrder;
    }
}

$.fn.putCursorAtEnd = function() {

  return this.each(function() {
    // Cache references
    var $el = $(this),
        el = this;

    // Only focus if input isn't already
    if (!$el.is(":focus")) {
     $el.focus();
    }

    // If this function exists... (IE 9+)
    if (el.setSelectionRange) {

      // Double the length because Opera is inconsistent about whether a carriage return is one character or two.
      var len = $el.val().length * 2;
      // Timeout seems to be required for Blink
      setTimeout(function() {
        el.setSelectionRange(len, len);
      }, 1);
    } else {
      // As a fallback, replace the contents with itself
      // Doesn't work in Chrome, but Chrome supports setSelectionRange
      $el.val($el.val());
    }

    // Scroll to the bottom, in case we're in a tall textarea
    // (Necessary for Firefox and Chrome)
    this.scrollTop = 999999;
  });

};

function getCurrencySymbol(currency, locale = 'en-US') {
    try{
        return (0).toLocaleString(
          locale,
          {
            style: 'currency',
            currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          }
        ).replace(/\d/g, '').trim()
    } catch (error) {
        console.error(error);
        return '$';
    }
}

const toastSpinnerModal = (title, options = {}, onOpen = () => null, onClose = () => null) => {
    const defaultOptions = {
        toast: true,
        position: 'bottom-end',
        showConfirmButton: false,
        showCancelButton: false,
        timer: 10000,
        onOpen: (toast) => {
            Swal.toggleTimer();
            console.log('Timer toggled');
            onOpen(toast);
        },
        willClose: () => {
            console.log('Dialog closed');
            onClose();
        }
    };

    const mergedOptions = { ...defaultOptions, ...options };

    const html = `
        <div style="max-width: 250px; text-align: left;">
            <div style="float: left; width: 100%; font-weight: bold; font-size: 15px; padding: 10px;">
                <img style="float: left; margin-top: 10px; height:18px; margin-right: 5px;" src="/assets/images/tail-spin.svg">
                <span style="margin-left: 10px;">${title || t('Progress')}</span>
            </div>
        </div>
    `;

    const progressInstance = Swal.mixin(mergedOptions);

    progressInstance.fire({
        html: html,
    });
};

// toastSpinnerModal(
//     t('Connecting to router...'),
//     {
//         timer: 15000, // Custom timer for this instance
//         onOpen: () => console.log('Custom open logic executed'),
//         onClose: () => console.log('Custom close logic executed')
//     }
// );

const confirmDialog = (title, htmlEl, cb, onCancel = () => null, swalClass) => {
    const swalInstance = (swalClass && swalClass !== '') ? swalClass : swalWithBootstrapButtons;
    const html = document.createElement('div');
    $(html).addClass('ph-dialog-v2');

    $(html).html(`<div class="body">${t('Are you sure you want to proceed?')}</div>`);
    if (htmlEl) $(html).find('.body').html(htmlEl);

    swalInstance.fire({
        title: title || t('Confirm'),
        html: html,
        width: 500,
        showCancelButton: true,
        confirmButtonText: t('Confirm'),
        cancelButtonText: t('Cancel'),
    }).then(response => {
        response.value ? cb() : onCancel();
    });
}

const confirmDeleteDialog = (title, htmlEl, cb, confirmText = 'CONFIRM') => {
    const html = document.createElement('div');
    $(html).addClass('ph-dialog-v2');

    $(html).html(`<div class="body">${t('Are you sure you want to delete this item?')}</div>`);
    if (htmlEl) $(html).find('.body').html(htmlEl);

    swalWithBootstrapButtons.fire({
        title: title || t('Confirm Delete'),
        html,
        width: 500,
        showCancelButton: true,
        confirmButtonText: t(confirmText),
        cancelButtonText: t('Cancel'),
        allowOutsideClick: false,
        allowEscapeKey: false,
        customClass: {
            popup: 'device-logs-dialog',
            confirmButton: 'btn btn-danger waves-effect',
            cancelButton: 'btn btn-default waves-effect'
        }
    }).then(response => {
        if (response.value) {
            cb();
        }
    });
}

const genericDialog = (title, innerHtml) => {
    
    const html = document.createElement('div');
    $(html).addClass('ph-dialog-v2');
    $(html).html(`<div class="body"></div>`);
    if (innerHtml) {
        $(html).find('.body').html(innerHtml);
    } else {
        $(html).find('.body').html(t('Details not provided.'));
    }

    swalWithBootstrapButtons.fire({
        title: title || t('Information'),
        html: html,
        width: 500,
        confirmButtonText: t('OK'),
        showCancelButton: false,
        allowOutsideClick: true,
        allowEscapeKey: true,
    });
}

const deviceLogsDialog = (title, additionalHTML) => {
    swalWithBootstrapButtons.fire({
        title: title || t('Device Logs'),
        html: `
            <div class="ph-dialog-v2 device-logs-terminal" style="padding: 20px;">

                <div class="terminal" id="deviceLogs">
                    <!-- Example log line -->
                    <div class="terminal-line">${t('Waiting for logs from device')}... <span class="cursor"></span></div>
                    <!-- Logs will be appended here via JavaScript -->
                </div>
                ${ additionalHTML ?? '' }

            </div>
        `,
        // width: 500,
        confirmButtonText: t('Close'),
        showCancelButton: false,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showCloseButton: true,
        customClass: {
            popup: 'device-logs-dialog',
            confirmButton: 'btn btn-primary waves-effect',
            cancelButton: 'btn btn-default waves-effect'
        }

    });
}


function pushEntryToLogsDialog(entry) {
    if (Swal.isVisible() && entry && entry !== '') {
        const logContainer = $('#deviceLogs');
        if (logContainer.length) {
            // Hide previous cursors
            logContainer.find('.cursor').css('display', 'none');

            // Create new log line with cursor
            const newLogLine = $('<div/>', {
                'class': 'terminal-line',
                'html': `${entry} <span class="cursor" style="display: inline-block;"></span>`
            });

            // Append new log line
            logContainer.append(newLogLine);

            // Ensure the new log line is visible within the dialog
            newLogLine.get(0).scrollIntoView({ behavior: 'smooth', block: 'end' });
        }
    }
}

const swalWithBootstrapButtons = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary waves-effect',
        cancelButton: 'btn btn-default waves-effect'
    },
    buttonsStyling: false,
    animation: false,
    focusConfirm: false,
    // Note that because this is a mixin - any other SWAL dialogs that reference this, that use `onOpen` will overwrite this logic.
    onOpen: (popup) => {
        const swalElement = Swal.getPopup();
        $(swalElement).draggable({
            handle: '.swal2-header',  // Makes the title bar the drag handle
        });
        // Only change the cursor to move for the title bar
        $(swalElement).find('.swal2-header').css('cursor', 'move');

        // If there's a custom onOpen function, execute it
        if (popup && typeof popup.onOpen === 'function') {
         popup.onOpen();
        }
    }
});

function makeSwalDraggable() {
    const swalElement = Swal.getPopup();
        $(swalElement).draggable({
            handle: '.swal2-header',  // Makes the title bar the drag handle
        });
        // Only change the cursor to move for the title bar
        $(swalElement).find('.swal2-header').css('cursor', 'move');
}

const swalWithDangerBootstrapButtons = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-danger waves-effect',
      cancelButton: 'btn btn-default waves-effect'
    },
    buttonsStyling: false,
    animation: false,
});

const swalActionButtons = (cb) => {
    const div = document.createElement('div');
    $(div).addClass('d-flex justify-center gap-1');
    $(div).html(`
        <button type="button" class="btn btn-primary waves-effect save-btn">${t('Save')}</button>
        <button type="button" class="btn btn-default waves-effect cancel-btn">${t('Cancel')}</button>
    `);
    $(div).find('.cancel-btn').on('click', () => Swal.close());
    $(div).find('.save-btn').on('click', () => cb());
    return div;
}

/** get membership details from gymmaster **/
const getMembershipDetails = (clubpass=null, clubId) => {
    let clubPassInfo = {};
    let hasMembership = false;
    let membershipDetails = [
        { label: t('First name'), key: 'firstname', value: 'N/A' },
        { label: t('Last name'), key: 'surname', value: 'N/A' },
        { label: t('Gender'), key: 'gender', value: 'N/A' },
        { label: t('Email'), key: 'email', value: 'N/A' },
        { label: t('Birthdate'), key: 'dob', value: 'N/A' },
        { label: t('Club'), key: 'clubID', value: 'N/A' },
        { label: t('Company'), key: 'company_name', value: 'N/A' },
        { label: t('Date joined'), key: 'joindate', value: 'N/A' },
        { label: t('Status'), key: 'status', value: 'N/A' },
        { label: t('GM ID'), key: 'gmID', value: 'N/A' },
    ];

    if (clubpass) {
        const exists = 'exists' in clubpass ? clubpass.exists : false;
        const clubMembership = 'clubMembership' in clubpass ? clubpass.clubMembership : {clubs: []};
        if (exists && clubMembership.clubs.length) {
            const clubs = clubMembership.clubs.sort((a, b) => {
                return new Date(b.created) - new Date(a.created);
            }).filter(club => club.clubID === clubId);

            if (clubs.length) {
                clubPassInfo = clubs.reduce((a, b) => {
                    return new Date(b.occurred_iso) > new Date(a.occurred_iso) ? b : a;
                });
            }

            if (Object.keys(clubPassInfo).length) {
                hasMembership = true;
                membershipDetails = membershipDetails.map(gmRow => {
                    if (gmRow.key in clubPassInfo && clubPassInfo[gmRow.key]) {
                        const gmValue = clubPassInfo[gmRow.key];
                        return {
                            ...gmRow,
                            value: gmValue,
                        }
                    }
                    return gmRow
                });
            }
        }
    }

    return {
        hasMembership,
        membershipDetails,
        clubPassInfo,
    };
}

const loadingOverlay = (e, show = true, forModal = false) => {
    $(e).LoadingOverlay(show ? "show" : "hide", {
        size: 50,
        maxSize: 75,
        minSize: 75,
        zIndex: forModal ? 2082 : 1061,

        // No animations - we'll use the animations within the SVG
        imageAnimation: false,
        fontawesomeAnimation: false,
        customAnimation: false,
        textAnimation: false,

        imageClass: false,
        imageColor: false,

        image: '/assets/images/tail-spin.svg',
    });
};

const validatePhoneNumber = (inputNumber) => {
    var re = /^[+]*[(]{0,1}[0-9]{1,3}[)]{0,1}[-\s\./0-9]*$/g;
    return re.test(inputNumber);
}

// get club information
const getClubInformation = async (clubId) => {
    const result = await uniApiConCall(`api/clubs/clubpage/clubdata/${clubId}`);
    if (result && 'status' in result && result.status == 'success') {
      return result.data;
    }
    return null;
}

const getClubRegion = (ClubInfo) => {
    let region = {code: '', name: ''}
    if (_get(ClubInfo, 'placeData.country')) {
      ccode = ClubInfo.placeData['country']['iso_code'];
      cname = ClubInfo.placeData['country']['full_name'];
      if (ccode && cname) {
        region.code = ccode;
        region.name = cname;
      }
    }
    return region;
}

/** parse the phone using api **/
const phoneParser = async (phone, region, allowInternational=false) => {
    let url = `api/phone/parser`;
    const result = await uniApiConCall(url, 'POST', {phone, region, allowInternational});
    if (result && 'status' in result && result.status == 'success' && 'data' in result) {
      const { data } = result;
      return data;
    }

    return false;
}

// get club pass membership using phone
const getClubpassUserInfoByPhone = async (clubId, phone) => {
    let url = `api/clubpass/user/phone/exist/${clubId}?phone=${encodeURIComponent(phone)}`;
    const result = await uniApiConCall(url, 'GET');
    if (result && 'status' in result && result.status == 'success' && 'data' in result) {
      const { data } = result;
      return data;
    }
    return false;
}

// get club pass membership using email
const getClubpassUserInfoByEmail = async (clubId, email) => {
    let url = `api/clubpass/user/email/exist/${clubId}?email=${encodeURIComponent(email)}`;
    const result = await uniApiConCall(url, 'GET');
    if (result && 'status' in result && result.status == 'success' && 'data' in result) {
      const { data } = result;
      return data;
    }
    return false;
}

// select the priority membership
const getThePriorityMembership = (membershipInfoByPhone, membershipInfoByEmail) => {
    if (membershipInfoByPhone.exists === true && membershipInfoByEmail.exists === true) {
        return membershipInfoByPhone;
    }
    if (membershipInfoByPhone.exists === true && membershipInfoByEmail.exists === false) {
        return membershipInfoByPhone;
    }
    if (membershipInfoByPhone.exists === false && membershipInfoByEmail.exists === true) {
        return membershipInfoByEmail;
    }
    if (membershipInfoByPhone.exists === false && membershipInfoByEmail.exists === false) {
        return null;
    }
    return null
}

// clubpass default
const getClubpassMembershipDefault = () => {
    return {
        exists: false,
        has12RndAccount: false,
        hasClubMembership: false,
        clubMembership: {
            exists: false
        }
    }
}

// get clubpass info or process it by email or phone
const getClubpassInfoByEmailOrPhone = async (clubId, phone, email='') => {
    const defaultClubpass = getClubpassMembershipDefault();
    let membershipInfoByPhone = {...defaultClubpass};
    let membershipInfoByEmail = {...defaultClubpass};

    // get the locally session record if any
    const cleanPhone = underscoreString(phone);
    const cleanEmail = underscoreString(email);
    const phoneMembershipInSession = await getSessionStorage(cleanPhone);
    const emailMembershipInSession = await getSessionStorage(cleanEmail);

    // return and get the session save membership by email or phone
    if (phoneMembershipInSession || emailMembershipInSession) {
        if (phoneMembershipInSession) {
            const parsePhoneMembership = JSON.parse(phoneMembershipInSession);
            membershipInfoByPhone = {...membershipInfoByPhone, ...parsePhoneMembership};
        }
        if (emailMembershipInSession) {
            const parseEmailMembership = JSON.parse(emailMembershipInSession);
            membershipInfoByEmail = {...membershipInfoByEmail, ...parseEmailMembership};
        }

        return await getThePriorityMembership(membershipInfoByPhone, membershipInfoByEmail);
    }

    // or else get it from api
    let promiseAll = [getClubpassUserInfoByPhone(clubId, phone)];
    if (email) {
        promiseAll.push(getClubpassUserInfoByEmail(clubId, email));
        const [ clubpassByPhone, clubpassByEmail ] = await Promise.all(promiseAll);
        membershipInfoByPhone = {...membershipInfoByPhone, ...clubpassByPhone};
        membershipInfoByEmail = {...membershipInfoByEmail, ...clubpassByEmail};

        await setSessionStorage(cleanPhone, JSON.stringify(membershipInfoByPhone));
        await setSessionStorage(cleanEmail, JSON.stringify(membershipInfoByEmail));

        return await getThePriorityMembership(membershipInfoByPhone, membershipInfoByEmail);
    }

    const [ clubpassByPhone ] = await Promise.all(promiseAll);

    membershipInfoByPhone = {...membershipInfoByPhone, ...clubpassByPhone}
    await setSessionStorage(cleanPhone, JSON.stringify(membershipInfoByPhone));

    return membershipInfoByPhone;
}

// save session storage in browser
const setSessionStorage = (key, value) => {
    try {
        if (Storage !== void(0)) {
            sessionStorage.setItem(key, value);
            return true;
        }
    } catch (error) {
        console.log('browser storage', e)
    }
    return false;
}

// get session storage in browser
const getSessionStorage = (key) => {
    if (Storage !== void(0)) {
        return sessionStorage.getItem(key);
    }
    return null;
}

const removeSessionStorage = (key) => {
    if (Storage !== void(0)) {
        return sessionStorage.removeItem(key);
    }
    return null;
}

// make string underscore
const underscoreString = (string) => {
    return string.replace(/[^a-zA-Z0-9]/g,'_');
}

const getClubTimezone = (club) => {
    let tz = 'UTC';

    if (club.timeZone === undefined) {
        return tz;
    }

    if ('name' in club.timeZone) {
        tz = club.timeZone.name;
    }
    return tz;
}

// get the notification source name
const notificationSourceNames = (key='performance-hub') => {
    const categories = notificationCategories
    if (typeof categories !== 'undefined') {
        const category = categories.find(cat => cat.source === key);
        if (category) {
            return category.name;
        }
        return key;
    }

    return 'Performance Hub';
}

/** fb date format **/
const timeAgo = (time) => {
    let date = new Date(time);
    let diff = (((new Date()).getTime() - date.getTime()) / 1000);
    let day_diff = Math.floor(diff / 86400);

    if (isNaN(day_diff) || day_diff < 0) {
        return '1m ago';
    }

    if (day_diff >= 31) {
        return moment(new Date(time)).format("MMMM D, YYYY");
    }

    return day_diff == 0 && (
            diff < 60 && "just now" ||
            diff < 120 && "1m ago" ||
            diff < 3600 && Math.floor( diff / 60 ) + "m ago" ||
            diff < 7200 && "1h ago" ||
            diff < 86400 && Math.floor( diff / 3600 ) + "h ago") ||
        day_diff == 1 && "Yesterday" ||
        day_diff < 7 && day_diff + " days ago" ||
        day_diff < 31 && Math.ceil( day_diff / 7 ) + ` week${Math.ceil( day_diff / 7 ) > 1 ? 's' : ''} ago`;
}

const updateUserPreferences = async (userData) => {
    // update the user preferences with notifications data
    const result = await uniApiConCall(`/api/user-preferences`, 'PUT', userData);
    const { status } = result;
    // if success, get the new user preferences
    if (status) {
        const preferences = await getUserPreferencesAPI();
        if (preferences[0]) {
            userPreferences = preferences[1];
        }
    }
    return result;
}

const uniApiConCall = (url, verb='GET', dataObj=null) => {
    const promise = new Promise((resolve, reject) => {
      let config = {
        url,
        method: verb,
        headers: {
          Accept: "application/json",
        },
      };

      if (dataObj) {
        config = {
          ...config,
          contentType: 'application/json',
          data: JSON.stringify(dataObj),
        }
      }

      $.ajax({
        ...config,
        success: (result) => {
          resolve({ status: 'success', data: result})
        },
        error: (xhr, textStatus, errorThrown) => {
          console.error(xhr, textStatus, errorThrown);
          reject({ status: 'fail', data: errorThrown})
        },
      });
    });

    return promise.then(response => response).catch(error => error)
}

function getQueryStringsObject(search) {
    return (search || location.search).replace('?', '').split('&').reduce((acc, query) => {
        if (query.replace(/\s/, '') === '') {
            return acc;
        }

        const splitQuery = query.split('=');
        return { ...acc, [splitQuery[0]]: splitQuery[1] };
    }, {});
}

function stringifyQueryStringsObject(obj) {
    return Object.keys(obj).reduce((acc, key) => {
        if (!obj[key]) return acc;
        if (acc.length > 0) {
            return `${acc}&${key}=${obj[key]}`;
        } else {
            return `?${key}=${obj[key]}`;
        }
    }, '');
}

function updateHrefQuery(obj, url = location.pathname, updateHistory = true) {
    try {
        const [pathname, search] = url.split('?');
        const queryObject = getQueryStringsObject(search);
        const newQueryObject = { ...queryObject, ...obj };
        const stringifiedQuery = stringifyQueryStringsObject(newQueryObject);
        const newURL = `${pathname}${stringifiedQuery}`;
        return updateHistory
            ? window.history.pushState({ filterChange: true }, "", newURL)
            : newURL;
    } catch (error) {
        console.error(error);
        return path;
    }
}

function getCountriesOfClubsAvailableToUser() {
    return (ClubsObj || []).reduce((acc, club) => {
        const { iso_code: code, full_name: name } = club.localisation.country;
        if (!code || !name || acc.some(c => c.code === code)) return acc;
        return [...acc, { code, name }];
    }, [])
}

function writeOnIframe(iframe, content, css = '') {
    const iframeContent = iframe?.contentWindow || ( iframe?.contentDocument.document || iframe?.contentDocument);
    iframeContent?.document.open();
    iframeContent?.document.write(`<style>${css}</style>`);
    iframeContent?.document.write(content);
    iframeContent?.document.close();
}


/*
* Backbone.js & Underscore.js Natural Sorting
*
* <AUTHOR> Jantzer <https://gist.github.com/kjantzer/7027717>
* @since 2013-10-17
*
* NOTE: make sure to include the Natural Sort algorithm by Jim Palmer (https://github.com/overset/javascript-natural-sort)
*/

// add _.sortByNat() method
_.mixin({

    sortByNat: function(obj, value, context) {
        var iterator = _.isFunction(value) ? value : function(obj){ return obj[value]; };
        return _.map(_.map(obj, function(value, index, list) {
          return {
            value: value,
            index: index,
            criteria: iterator.call(context, value, index, list)
          };
        }).sort(function(left, right) {
          var a = left.criteria;
          var b = right.criteria;
          return naturalSort(a, b);
        }), 'value');
    }
});

/*
* Natural Sort algorithm for Javascript - Version 0.7 - Released under MIT license
* Author: Jim Palmer (based on chunking idea from Dave Koelle)
* https://github.com/overset/javascript-natural-sort
*/
function naturalSort (a, b) {
    var re = /(^-?[0-9]+(\.?[0-9]*)[df]?e?[0-9]?$|^0x[0-9a-f]+$|[0-9]+)/gi,
        sre = /(^[ ]*|[ ]*$)/g,
        dre = /(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,
        hre = /^0x[0-9a-f]+$/i,
        ore = /^0/,
        i = function(s) { return naturalSort.insensitive && (''+s).toLowerCase() || ''+s },
        // convert all to strings strip whitespace
        x = i(a).replace(sre, '') || '',
        y = i(b).replace(sre, '') || '',
        // chunk/tokenize
        xN = x.replace(re, '\0$1\0').replace(/\0$/,'').replace(/^\0/,'').split('\0'),
        yN = y.replace(re, '\0$1\0').replace(/\0$/,'').replace(/^\0/,'').split('\0'),
        // numeric, hex or date detection
        xD = parseInt(x.match(hre)) || (xN.length != 1 && x.match(dre) && Date.parse(x)),
        yD = parseInt(y.match(hre)) || xD && y.match(dre) && Date.parse(y) || null,
        oFxNcL, oFyNcL;
    // first try and sort Hex codes or Dates
    if (yD)
        if ( xD < yD ) return -1;
        else if ( xD > yD ) return 1;
    // natural sorting through split numeric strings and default strings
    for(var cLoc=0, numS=Math.max(xN.length, yN.length); cLoc < numS; cLoc++) {
        // find floats not starting with '0', string or 0 if not defined (Clint Priest)
        oFxNcL = !(xN[cLoc] || '').match(ore) && parseFloat(xN[cLoc]) || xN[cLoc] || 0;
        oFyNcL = !(yN[cLoc] || '').match(ore) && parseFloat(yN[cLoc]) || yN[cLoc] || 0;
        // handle numeric vs string comparison - number < string - (Kyle Adams)
        if (isNaN(oFxNcL) !== isNaN(oFyNcL)) { return (isNaN(oFxNcL)) ? 1 : -1; }
        // rely on string comparison if different types - i.e. '02' < 2 != '02' < '2'
        else if (typeof oFxNcL !== typeof oFyNcL) {
            oFxNcL += '';
            oFyNcL += '';
        }
        if (oFxNcL < oFyNcL) return -1;
        if (oFxNcL > oFyNcL) return 1;
    }
    return 0;
}

// extend Array to have a natural sort
Array.prototype.sortNat = function(){
    return Array.prototype.sort.call(this, naturalSort)
}

function getSerializedObjectFromForm(form) {
    return $(form).serializeArray().reduce(function(a, x) {
        if (!a[x.name]) {
            a[x.name] = x.value;
        } else if (typeof a[x.name] === 'string') {
            a[x.name] = [a[x.name], x.value];
        } else {
            a[x.name].push(x.value);
        }
        return a;
    }, {});
}

function replaceTextVariables(text = '', clubVariables = {}) {
    const textVariables = text.match(/{{[^}]+}}/g);

    if (textVariables) {
        textVariables.forEach(textVariable => {
            const [variableName, blockKey] = textVariable.replace('{{', '').replace('}}', '').split('_');
            const variable = clubVariables[variableName];
            const hasHourBlocks = ['ClubHours', 'ExtendedHours'].includes(variableName);
            let value = _get(variable, 'value', '');

            if (hasHourBlocks && blockKey) {
                const blocks = value.split('<br>').reduce((acc, block) => {
                    const key = _get(block.match(/^<b>(.*?):<\/b>/), '1');
                    const value = _get(block.match(/<\/b> (.*)$/), '1');
                    return { ...acc, [key]: value };
                }, {});

                const block = blocks[blockKey.toUpperCase()];
                value = block || '';
            }

            text = text.replace(textVariable, value);
        });
    }
    return text;
}

function textField({ label, name, value, type, required, props, info, disabled, placeholder, classNames = '' }) {
    const infoElement = info ? infoPopup(info) : '';
    return `
        <div class="form-group ${classNames}" style="margin-bottom: 0px;">
            ${label ? `<label class="form-label">${required ? '*' : ''}${label}${infoElement}</label>` : ''}
            <div class="form-line">
                <input
                    class="form-control"
                    value="${value ?? ''}"
                    name="${name}"
                    type="${type || 'text'}"
                    placeholder="${placeholder || ''}"
                    ${required && !disabled ? 'required="true"' : ''}
                    ${disabled ? 'disabled="true"' : ''}
                    ${props || ''}
                >
            </div>
            <em class="text-danger"></em>
        </div>
    `
}

function textArea({ label, name, value, props, rows, placeholder }) {
    return `
        <div class="form-group" style="margin-bottom: 0px;">
            ${label ? `<label class="form-label">${label}</label>` : ''}
            <div class="form-line">
                <textarea
                    class="form-control"
                    rows="${rows || 2}"
                    name="${name}"
                    style="resize: vertical;"
                    placeholder="${placeholder || ''}"
                    ${props || ''}
                >${value || ''}</textarea>
            </div>
            <em class="text-danger"></em>
        </div>
    `
}

function selectpicker({ label, name, value, options, liveSearch, multiple, required, selectClx, info, usePhTheme = false }) {
    const liveSearchProp = liveSearch !== false ? 'data-live-search="true"' : '';
    const multipleProp = multiple ? 'multiple' : '';
    const requiredProp = required ? 'required="true"' : '';
    const nameProp = name ? `name="${name}"` : '';
    const infoElement = info ? infoPopup(info) : '';
    options = options || [];

    const optionsList = options.map((option) => {
        const selected = Array.isArray(value) ? value.includes(option.value) : value === option.value;
        return `<option value="${option.value}" ${selected ? 'selected' : ''}>${option.label}</option>`
    })

    return `
        <div class="form-group form-group-no-margin d-flex flex-column select-picker ph-select-picker">
            ${label ? `<label class="form-label">${label}${infoElement}</label>` : ''}
            <select class="${selectClx || 'selectpicker'} ${usePhTheme ? 'ph-select-picker' : ''}" data-width="100%" ${nameProp} ${liveSearchProp} ${multipleProp} ${requiredProp}>
                ${optionsList.join('')}
            </select>
        </div>
    `
}

function _get(obj, path = '', defaultValue = undefined) {
    if (typeof obj !== 'object' || obj === null || typeof path !== 'string' || path === '') {
        return defaultValue;
    }
    return path.split('.').reduce((result, key) => result && result[key], obj) || defaultValue;
}

function _set(obj, path, value) {
    if (typeof obj !== 'object' || obj === null || typeof path !== 'string' || path === '') {
      throw new Error('Invalid arguments');
    }

    const keys = path.split('.');
    const lastKey = keys.pop();
    const parent = keys.reduce((result, key) => {
      if (typeof result !== 'object' || result === null) {
        throw new Error('Invalid path');
      }
      return result[key];
    }, {...obj});

    parent[lastKey] = value;

    return { ...obj };
}

function infoPopup(content, userConfig = {}) {
    const defaultConfig = {
        style:
            userConfig.style ??
            'font-size: 14px; position: absolute; transform: translate(2.5px, -2.5px); cursor: default;',
        class: userConfig.class ?? '',
    };

    return `
        <i
            class="material-icons info-icon ${defaultConfig.class}"
            data-toggle="tooltip"
            title="${content}"
            style="${defaultConfig.style}"
        >info_outline</i>`;
}

function isEuropeanCountry(countryCode) {
    const europeanCountries = ['AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'GB'];
    return europeanCountries.includes(countryCode);
}

function switchLever(label1, label2, name, checked = false, classNames = '') {
    return `
        <div class="demo-switch ${classNames}">
            <div class="switch ph-switch">
                <label class="use-skeleton" style="min-width: max-content;">
                    ${label1 || ''}
                    <input data-hj-allow type="checkbox" name="${name}" ${checked ? 'checked' : ''}>
                    <span class="lever lever switch-col-blue"></span>
                    ${label2 || ''}
                </label>
            </div>
        </div>
    `;
}

function checkBox({ id, label, name, checked = false }) {
    const checkedProp = checked ? 'checked' : '';

    return `
        <div class="form-check styled-checkbox" style="margin-top:5px;">
            <input id="${id}" type="checkbox" class="filled-in chk-col-blue" name="${name}" ${checkedProp}>
            <label for="${id}">${t(label)}</label>
        </div>
    `
}

function breadcrumbsComponent(element, breadcrumbs = [], preserveQueryParams = false) {
    const breadcrumbItem = ({ label, url, active }) => {
        const urlProp = url ? `href="${url}"` : '';
        const activeClass = active ? 'active' : '';
        return (`
            <li class="breadcrumb-item">
                <a ${urlProp} class="${activeClass}">${label}</a>
            </li>
        `)
    };

    $(element).html(`
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                ${breadcrumbs.map(breadcrumbItem).join('')}
            </ol>
        </nav>
    `);

    $(element).on('click', 'a', (e) => {
        e.preventDefault();
        const url = $(e.currentTarget).attr('href');
        if (!url) return;

        if (preserveQueryParams) {
            const currentSearch = window.location.search;
            // If there are query params and the target URL doesn't already have them
            if (currentSearch && !url.includes('?')) {
                sammy.setLocation(`${url}${currentSearch}`);
            } else {
                sammy.setLocation(url);
            }
        } else {
            sammy.setLocation(url);
        }
        
        firstLoad = false;
    });
}

function openUploadFileDialog(title = 'Upload File', cb = () => null, config = {}) {
    const url = _get(config, 'url', '/api/asset-upload');
    const maxFiles = _get(config, 'maxFiles', 10);
    const acceptedFiles = _get(config, 'acceptedFiles', '.jpeg,.jpg,.png,.pdf');
    const uploadData = [];

    const html = document.createElement('div');
    $(html).addClass('ph-dialog');
    $(html).html(`
        <fieldset>
            <legend>${title}</legend>
            <div class="dz-container dropzone"></div>
        </fieldset>
    `);

    $(html).append(
        swalActionButtons(() => {
            if (uploadData.length <= 0) return;
            cb(uploadData);
            swalWithBootstrapButtons.close();
        })
    );

    $(html).find('.save-btn').attr('disabled', 'disabled');

    $(html)
        .find('.dz-container')
        .dropzone({
            url,
            maxFilesize: 50, // MB
            maxFiles,
            parallelUploads: 1,
            acceptedFiles,
            dictDefaultMessage: t("Drop files here to upload"),
            dictFallbackText: t("Please use the fallback form below to upload your files like in the olden days."),
            dictFileTooBig: t("File is too big") + "({{filesize}}MiB)." + t("Max filesize") + ": {{maxFilesize}}MiB.",
            dictInvalidFileType: t("You can't upload files of this type."),
            dictResponseError: t("Server responded with") + "{{statusCode}}" + t("code."),
            dictCancelUpload: t("Cancel upload"),
            dictUploadCanceled: t("Upload canceled."),
            dictCancelUploadConfirmation: t("Are you sure you want to cancel this upload?"),
            dictRemoveFile: t("Remove file"),
            dictMaxFilesExceeded: t("You can not upload any more files."),
            success: function (file, response) {
                if (
                    response !== undefined &&
                    response.location !== undefined &&
                    response.originalname !== undefined
                ) {
                    uploadData.push(response);
                }
            },
            complete: function (file) {
                $('.save-btn').removeAttr('disabled');
            },
        });

    swalWithBootstrapButtons.fire({
        html,
        showConfirmButton: false,
    });
}

function renderDropZone(element, config = {}) {
    $(element).dropzone({
        maxFiles: config.maxFiles ?? 10,
        url: config.url ?? `/api/asset-upload`,
        parallelUploads: config.parallelUploads ?? 5,
        acceptedFiles: config.acceptedFiles,
        addRemoveLinks: true,
        timeout: 3600000,
        dictDefaultMessage: t('Drop files here to upload'),
        dictFallbackText: t(
            'Please use the fallback form below to upload your files like in the olden days.'
        ),
        dictFileTooBig:
            t('File is too big') + '({{filesize}}MiB).' + t('Max filesize') + ': {{maxFilesize}}MiB.',
        dictInvalidFileType: t("You can't upload files of this type."),
        dictResponseError: t('Server responded with') + '{{statusCode}}' + t('code.'),
        dictCancelUpload: t('Cancel upload'),
        dictUploadCanceled: t('Upload canceled.'),
        dictCancelUploadConfirmation: t('Are you sure you want to cancel this upload?'),
        dictRemoveFile: t('Remove file'),
        dictMaxFilesExceeded: t('You can not upload any more files.'),
        addedfiles: config.addedfiles,
        queuecomplete: config.queuecomplete,
        success: (_, response) => {
            if (!response?.location || !response.originalname) return;
            config.success?.(response.location);
        },
        error: config.error,
        sending: (_, xhr) => {
            xhr.ontimeout = (e) => {
                console.info('Server Timeout');
            };
        },
    });
}

function getLanguageNameFromCode(code, langOrigin) {
    const displayNames = new Intl.DisplayNames([langOrigin || 'en'], { type: 'language' });
    return displayNames.of(code || 'en');
}

function getCountryNameFromCode(code, langOrigin) {
    const displayNames = new Intl.DisplayNames([langOrigin || 'en'], {type: 'region'});
    return displayNames.of(code.toUpperCase());
}

function searchbarHeader(html, buttons = [], config = {class: '', usePhTheme: false, expandableButtons: {}}) {
    const placeholder = _get(config, 'placeholder', t('Filter Results...'));
    const expandConfig = _get(config, 'expandableButtons', {
        label: t('Options'),
        startIndex: 0,
        count: 0,
        id: null,
    });
    let expandableButtonsEl = [];

    if (expandConfig.startIndex >= 0 && expandConfig.count > 0) {
        expandableButtonsEl = buttons.splice(expandConfig.startIndex, expandConfig.count);
    }

    const searchbarElement = `
        <div class="search-container">
            <input data-hj-allow type="search" class="form-control search-bar use-skeleton" placeholder="${t(placeholder)}">
            <i class="material-icons">search</i>
        </div>
    `;

    const settingsBtnText = config.settingsBtn
        ? `<button class="btn btn-square waves-effect settings-btn" style="background: transparent;"><i class="fa fa-cog"></i></button>`
        : '';

    const loadingClx = config.isLoading ? 'isLoading' : '';

    $(html).off('click', '.expand-btn');
    $(html).off('click', '.settings-btn');

    $(html).html(`
        <div class="searchbar-header ${loadingClx} ${config.class} ${config.usePhTheme ? 'ph-searchbar-header' : ''}">
            <span class="d-flex gap-1 buttons-container"></span>
            <div class="expandable-buttons-container ml-auto" ${expandConfig.id ? `id="${expandConfig.id}"` : ''}>
            </div>
            ${expandableButtonsEl.length > 0 ? genButton({
                label: expandConfig.label,
                icon: 'fa-chevron-left',
                className: 'expand-btn',
                iconPosition: 'left',
                iconSize: 'sm',
            }, { strOutput: true }) : ''}
            <div class="d-flex gap-1 search-container-wrapper">
                ${searchbarElement}
                ${settingsBtnText}
            </div>
        </div>
    `);

    if (expandableButtonsEl.length > 0) {
        $(html).on('click', '.expand-btn', (e) => {
            const targetEl = expandConfig.id ? `#${expandConfig.id}` : '.expandable-buttons-container';
            console.log('expand btn clicked')
            e.preventDefault();
            e.stopPropagation();
            
            const $container = $(html).find(targetEl);
            
            $container.toggleClass('expanded');
        });

        expandableButtonsEl.forEach((buttonProps) => {
            renderBarButtons($(html).find('.expandable-buttons-container'), buttonProps);
        });
    }

    buttons.forEach((buttonProps) => {
        renderBarButtons($(html).find('.buttons-container'), buttonProps);
    });

    if (config.settingsBtn) {
        $(html).on('click', '.settings-btn', config.settingsBtn);
    }
}

const toggleLoading = (element, loading = true) => {
    if(!element) return;

    if(loading)
     return element.addClass('isLoading');

    element.removeClass('isLoading');
}


function convertCelsiusToFahrenheit(temp) {
    return (temp * 9/5) + 32;
}

function formatTemperature(temp, unitOfMeasurement = 'metric') {
    return unitOfMeasurement === 'metric' ? temp + '°C' : convertCelsiusToFahrenheit(temp) + '°F';
}

function convertSearchToObject(search) {

    if(!search) return {}

    const params = search.substring(1).split('&');

    const obj = {};

    for (let i = 0; i < params.length; i++) {
      const pair = params[i].split('=');
      const key = decodeURIComponent(pair[0]);
      const value = decodeURIComponent(pair[1]);

      if (obj.hasOwnProperty(key)) {
        if (Array.isArray(obj[key])) {
          obj[key].push(value);
        } else {
          obj[key] = [obj[key], value];
        }
      } else {
        obj[key] = value;
      }
    }

    return obj;
}

function useProxyState(config = {}, callback = function(target, property, value) {
    console.log({ target, property, value })
}) {
    // Proxy handler
    const storeHandler = {
        get: function (target, property) {
            return target[property];
        },
        set: function (target, property, value) {
            target[property] = value;

            if(callback) {
                return callback(target, property, value);
            }

            return true;
        },
        deleteProperty: function (target, property) {
            delete target[property];
            return true;
        },
    };

    // Create the proxy
    const store = new Proxy(config, storeHandler);

    return store
}

function nFormatter(num, digits) {
    const lookup = [
      { value: 1, symbol: "" },
      { value: 1e3, symbol: "k" },
      { value: 1e6, symbol: "M" },
      { value: 1e9, symbol: "G" },
      { value: 1e12, symbol: "T" },
      { value: 1e15, symbol: "P" },
      { value: 1e18, symbol: "E" }
    ];
    const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
    var item = lookup.slice().reverse().find(function(item) {
      return num >= item.value;
    });
    return item ? (num / item.value).toFixed(digits).replace(rx, "$1") + item.symbol : "0";
}

function wrapTableWithScrollableDiv(table) {
    const wrapper = $('<div/>').css('overflow', 'auto');
    $(table).wrap(wrapper);
}

function isAppleDevice() {
  return /Macintosh|iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
}

function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

const defaultPlyrOptions = {
  captions: { active: true, update: true, language: 'en' },
  quality: {
      default: isMobileDevice() ? 720 : 1080,
      options: [1080, 720, 360],
      forced: true,
  }
};

function streamPlayer(video, src) {
  if (isAppleDevice()) return hlsPlayer(video, src);
  dashPlayer($(video)[0], src);
}

function dashPlayer(video, src) {
  let firstLoad = true;
  const dash = dashjs.MediaPlayer().create();
  const parent = $(video).parent();

  const options = { ...defaultPlyrOptions };
  options.quality.onChange = function (quality) {
      if (firstLoad) return;
      const t = options.quality.options.findIndex((q) => q === quality);
      dash.setQualityFor("video", t);
  };

  const player = new Plyr($(video)[0], options);
  parent.find('.plyr').css('width', '100%')

  player.on('play', () => {
      if (!firstLoad) return;
      firstLoad = false;
      dash.initialize($(video)[0], src, false);
  });

}

function hlsPlayer(video, src) {
  let firstLoad = true;
  const options = { ...defaultPlyrOptions };
  const parent = $(video).parent();

  if (!Hls.isSupported()) {
      $(video)[0].src = src;
      options.quality = null;
      return new Plyr($(video)[0], options);
  }

  const hls = new Hls();

  options.quality.onChange = function (quality) {
      hls.levels.forEach((level, levelIndex) => {
          if (level.height !== quality) return;
          hls.currentLevel = levelIndex;
      });
  };

  const player = new Plyr($(video)[0], options);
  parent.find('.plyr').css('width', '100%')

  player.on('play', () => {
      if (!firstLoad) return;
      firstLoad = false;
      hls.loadSource(src);
      hls.attachMedia($(video)[0]);
  });
}

// format number to have commas
function formatNumberWithCommas(x) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

async function getPdfThumbnail(pdfUrl) {
  const pdf = await pdfjsLib.getDocument(pdfUrl).promise;
  const page = await pdf.getPage(1);
  const viewport = page.getViewport({ scale: 1 });
  const canvas = document.createElement('canvas');
  const canvasContext = canvas.getContext('2d');
  canvas.height = viewport.height;
  canvas.width = viewport.width;
  await page.render({ canvasContext, viewport }).promise;
  return canvas.toDataURL('image/png');
}


function silentRouteChange(url) {
  firstLoad = true;
  sammy.quiet = false;
  sammy.setLocation(url);
  firstLoad = false;
}

$('body').on('click', '.silent-route', function(e) {
  e.preventDefault();
  const url = $(this).attr('href');
  silentRouteChange(url);
});

// -- Moment.js functions to calculate key holidays...

// Moment.js function to calculate the eater date
moment.fn.easter = function() {
    const year = this.year();
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;

    return moment([year, month - 1, day]);
};

// -- UK KEY HOLIDAY CALCULATIONS --

// Early May Bank Holiday (First Monday in May)
moment.fn.earlyMayBankHoliday = function() { return this.clone().month('May').startOf('month').isoWeekday(1); };
// Spring Bank Holiday (Last Monday in May)
moment.fn.springBankHoliday = function() { return this.clone().month('May').endOf('month').isoWeekday(-1); };
// Summer Bank Holiday (Last Monday in August for England, Wales, Northern Ireland)
moment.fn.summerBankHoliday = function() { return this.clone().month('August').endOf('month').isoWeekday(-1); };

// -- JAPAN KEY HOLIDAY CALCULATIONS --

// Showa Day - April 29
moment.fn.showaDay = function() { return this.clone().month('April').date(29); };
// Constitution Memorial Day - May 3
moment.fn.constitutionMemorialDay = function() { return this.clone().month('May').date(3); };
// Greenery Day - May 4
moment.fn.greeneryDay = function() { return this.clone().month('May').date(4); };
// Children's Day - May 5
moment.fn.childrensDay = function() { return this.clone().month('May').date(5); };
// Tanabata (July 7th):
moment.fn.tanabata = function() { return this.clone().month('July').date(7); };
// Cherry Blossom Viewing doesn't have a fixed date and varies across Japan, but it's typically around early April. For simplicity, let's use April 8th as a placeholder:
moment.fn.hanami = function() { return this.clone().month('April').date(8); };
// The date for the Obon Festival varies, but it's commonly observed around August 15th. Again, for simplicity, let's use August 15th:
moment.fn.obonFestival = function() { return this.clone().month('August').date(15); };
// National Foundation Day (February 11th):
moment.fn.nationalFoundationDay = function() { return this.clone().month('February').date(11); };
// Marine Day (Umi no Hi)
moment.fn.marineDay = function() { return this.clone().month('July').startOf('month').isoWeekday(15); };
// Respect for the Aged Day (Keiro no Hi)
moment.fn.respectForTheAgedDay = function() { return this.clone().month('September').startOf('month').isoWeekday(15); };
// Culture Day (Bunka no Hi):
moment.fn.cultureDay = function() { return this.clone().month('November').date(3); };

// updat classes for container-fluid
function updateContainerClasses(container, width) {
  if(!container || !width) return;

  container.removeClass('container-sm container-md');

  if (width <= 768) {
      container.addClass('container-sm');
  } else if (width <= 1024) {
      container.addClass('container-md');
  }
}

// listen to screen resize and add specific classes on container
function addContainerClasses(el) {
  const container = $(el).find('.container-fluid');
  if (container.length === 0) {
      console.log('No container-fluid element found');
      return; // Exit if the element is not found
  }

  let width = container.width();

  // remove all classes
  updateContainerClasses(container, width);

  const targetNode = container.get(0);

  // Check if targetNode is not undefined
  if (!targetNode) {
      console.log('No DOM node found for the container');
      return; // Exit if the DOM node is not found
  }

  // ResizeObserver callback
  const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
          const newWidth = $(entry.target).width();
          if (newWidth !== width) {
              width = newWidth; // Update the width

              updateContainerClasses(container, width);
          }
      }
  });

  // Start observing the target node
  resizeObserver.observe(targetNode);

  console.log({ container, el: $(el) });
}

function datePicker(element, config = {}, cb = () => null) {
    $(element).daterangepicker({
        timePicker: config.timePicker ?? false,
        singleDatePicker: config.singleDatePicker ?? false,
        showDropdowns: true,
        startDate: config.startDate ?? moment(),
        endDate: config.endDate ?? moment(),
        minDate: config.minDate,
        ranges: config.ranges,
        locale: {
            format: config.format ?? 'YYYY-MM-DD',
            applyLabel: t('Apply'),
            cancelLabel: t('Cancel'),
            customRangeLabel: t('Custom Range'),
            monthNames: [t('Jan'), t('Feb'), t('Mar'), t('Apr'), t('May'), t('Jun'), t('Jul'), t('Aug'), t('Sep'), t('Oct'), t('Nov'), t('Dec')],
            daysOfWeek: [t('Su'), t('Mo'), t('Tu'), t('We'), t('Th'), t('Fr'), t('Sa')],
        }
    }, cb);
}

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

function addFilterOptions(arr, options, filter, filterArr) {
  if (!arr || !options || !filter) return;
  options.forEach((option) => {
      arr.push({
          label: option,
          value: option,
          selected: filterArr[filter] === option,
      });
  });
}
function slugify(text) {
  return text
      .toString()                   // Convert to string
      .toLowerCase()                // Convert to lowercase
      .trim()                       // Trim whitespace from both sides
      .normalize('NFD')             // Normalize to NFD form (decompose combined characters)
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics (accents)
      .replace(/[^a-z0-9 -]/g, '')  // Remove all non-alphanumeric characters except spaces and hyphens
      .replace(/\s+/g, '-')         // Replace spaces with hyphens
      .replace(/-+/g, '-');         // Replace multiple hyphens with a single hyphen
}

function createOverviewSidebarLinks(contentWrapper, sidebarListSel, containerFluid) {

  if(!contentWrapper || !sidebarListSel || !containerFluid) return;

  const allHeadings = contentWrapper.find('h1, h2, h3, h4, h5, h6');
  const sidebarList = $(sidebarListSel);
  sidebarList.html('');

  allHeadings.each(function() {
    const currentText = $(this).text();
    const sectionId = currentText.toLowerCase().replace(/[^a-z0-9]/g, '-');
    sidebarList.append(`<li><a href="#${sectionId}">${currentText}</a></li>`);
    $(this).attr('id', sectionId);
  });

  const isInViewport = function (elem) {
    const bounding = elem.getBoundingClientRect();
    return (
      bounding.top >= 0 &&
      bounding.left >= 0 &&
      bounding.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      bounding.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  };

  containerFluid.on('scroll', function() {

    // exist each when first item is found
    allHeadings.each(function() {
      if (isInViewport(this)) {
        sidebarList.find('a').removeClass('active');
        sidebarList.find(`a[href="#${$(this).attr('id')}"]`).addClass('active');
        return false;
      }
    });
  });


  sidebarList.find('a').on('click', function(e) {
    e.preventDefault();

    console.log(containerFluid)

    const href = $(this).attr('href');
    const target = $(href);
    const offsetTop = target.position().top + containerFluid.scrollTop();
    containerFluid.animate({ scrollTop: offsetTop }, 500);
    sidebarList.find('a').removeClass('active');
    $(this).addClass('active');
  });

  containerFluid.scrollspy({ target: sidebarListSel })
}

function generateRandomNameEmailAndPhone(defaultLanguage = 'en') {
    const namesByLanguage = {
        en: {
            firstNames: ['John', 'Jane', 'Michael', 'Emily', 'Chris', 'Sarah', 'David', 'Laura', 'Robert', 'Emma'],
            lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'],
            countryCode: '+1', // USA
        },
        es: {
            firstNames: ['Carlos', 'María', 'José', 'Carmen', 'Luis', 'Ana', 'Miguel', 'Lucía', 'Juan', 'Sofía'],
            lastNames: ['González', 'Rodríguez', 'Pérez', 'García', 'López', 'Martínez', 'Sánchez', 'Ramírez', 'Torres', 'Flores'],
            countryCode: '+34', // Spain
        },
        fr: {
            firstNames: ['Pierre', 'Marie', 'Jean', 'Sophie', 'Louis', 'Isabelle', 'Jacques', 'Catherine', 'Michel', 'Julie'],
            lastNames: ['Martin', 'Bernard', 'Dubois', 'Durand', 'Lefevre', 'Moreau', 'Garnier', 'Girard', 'Francois', 'Rousseau'],
            countryCode: '+33', // France
        },
        de: {
            firstNames: ['Hans', 'Anna', 'Peter', 'Laura', 'Klaus', 'Maria', 'Thomas', 'Sabine', 'Wolfgang', 'Petra'],
            lastNames: ['Müller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann'],
            countryCode: '+49', // Germany
        },
        it: {
            firstNames: ['Giovanni', 'Maria', 'Luca', 'Sofia', 'Marco', 'Giulia', 'Matteo', 'Francesca', 'Paolo', 'Elena'],
            lastNames: ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Gallo', 'Costa', 'Fontana', 'Conti'],
            countryCode: '+39', // Italy
        },
        zh: {
            firstNames: ['Wei', 'Mei', 'Jie', 'Yan', 'Ming', 'Li', 'Hua', 'Ping', 'Tao', 'Xiao'],
            lastNames: ['Wang', 'Li', 'Zhang', 'Liu', 'Chen', 'Yang', 'Huang', 'Zhao', 'Wu', 'Zhou'],
            countryCode: '+86', // China
        },
        ja: {
            firstNames: ['Haruto', 'Yui', 'Souta', 'Riko', 'Yuto', 'Aoi', 'Takumi', 'Mei', 'Hiroshi', 'Sakura'],
            lastNames: ['Sato', 'Suzuki', 'Takahashi', 'Tanaka', 'Watanabe', 'Ito', 'Yamamoto', 'Nakamura', 'Kobayashi', 'Kato'],
            countryCode: '+81', // Japan
        },
        ar: {
            firstNames: ['Mohammed', 'Fatima', 'Ahmed', 'Aisha', 'Ali', 'Zainab', 'Hassan', 'Mariam', 'Omar', 'Layla'],
            lastNames: ['Al-Farsi', 'Al-Rashid', 'Haddad', 'Nasr', 'Sayeed', 'Zaki', 'Mansour', 'Darwish', 'Shaheen', 'Nasser'],
            countryCode: '+971', // UAE (most common Arabic-speaking country code)
        }
        // Add more languages as needed
    };

    const defaultLang = 'en';
    const selectedLanguage = namesByLanguage[defaultLanguage] || namesByLanguage[defaultLang];

    const getRandomElement = arr => arr[Math.floor(Math.random() * arr.length)];

    const firstName = getRandomElement(selectedLanguage.firstNames);
    const lastName = getRandomElement(selectedLanguage.lastNames);
    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`;
    const phoneNumber = generatePhoneNumber(selectedLanguage.countryCode);

    return { name: `${firstName} ${lastName}`, email, phone: phoneNumber, firstName: firstName, lastName: lastName };
}

function generatePhoneNumber(countryCode) {
    const randomDigits = () => Math.floor(Math.random() * 90000000) + 10000000; // Generates an 8-digit number
    return `${countryCode} ${randomDigits()}`;
}

/**
 * Builds a query string from an object, properly handling arrays and encoding values. There are other shared functions here that handle query strings but no support for arrays.
 * @param {Object} params - Object containing query parameters
 * @param {boolean} [includeQuestionMark=true] - Whether to include the leading question mark
 * @returns {string} Formatted query string
 * 
 * @example
 * Basic usage
 * buildQueryParams({ season: '2023', filter: 'active' })
 * Returns: "?season=2023&filter=active"
 * 
 * @example
 * With arrays
 * buildQueryParams({ userIds: ['123', '456'], season: '2023' })
 * Returns: "?userIds=123&userIds=456&season=2023"
 * 
 * @example
 * Without question mark
 * buildQueryParams({ season: '2023' }, false)
 * Returns: "season=2023"
 */
function buildQueryParams(params) {
    // Return empty string for null or undefined params
    if (params == null) return '';
    
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
        if (value === null || value === undefined) return;
        
        if (Array.isArray(value)) {
            value.forEach(item => {
                if (item !== null && item !== undefined) {
                    searchParams.append(key, item);
                }
            });
        } else {
            searchParams.append(key, value);
        }
    });
    
    const queryString = searchParams.toString();
    
    // Return empty string if there are no parameters
    if (!queryString) return '';
    
    return `?${queryString}`;
}


/**
 * Formats a Member Management System (MMS) user address object into a single, comma-separated string.
 * 
 * @param {Object} addressData - The address data object to be formatted
 * @param {string} [addressData.address1] - First line of address
 * @param {string} [addressData.address2] - Second line of address
 * @param {string} [addressData.city] - City name
 * @param {string} [addressData.state] - State or province
 * @param {string} [addressData.zip] - Postal or ZIP code
 * @param {string} [addressData.country] - Country name
 * @returns {string|null} A formatted address string with components joined by commas, or null if no address data provided
 */
function parseMMSUserAddress(addressData) {
    if (addressData) {
        const formattedAddr = [
            addressData.address1,
            addressData.address2,
            addressData.city,
            addressData.state,
            addressData.zip,
            addressData.country,
        ]
            .filter((part) => part)
            .join(', ');

        return formattedAddr;
    }

    return null;
}

function genAlertBox({ title, description, type = 'info', icon }) {
    const classes = [];

    if (type) {
        classes.push(`ph-alert_${type}`);
    }

    return `
        <div class="ph-alert ${classes.join(' ')}">
            <div class="ph-alert__title">${icon ? `<i class="material-icons">${icon}</i>` : ''} ${title}</div>
            <div class="ph-alert__description">${description}</div>
        </div>
    `;
}

function condAlertBox(callback, condition, setErrorInfo, defaultErrorInfo, errorCallback) {
    if (condition) {
        callback();
        return false;
    }

    if (errorCallback) {
        errorCallback();
    }

    return {...defaultErrorInfo, ...setErrorInfo};
}

/**
 * Formats a date string into "Month Day, Year" format with abbreviated month followed by a dot
 * @param {string} dateString - The date string in ISO format (e.g., "2023-09-15")
 * @returns {string} Formatted date string (e.g., "Sept. 15, 2023")
 * @example
 * formatDateMDY("2023-09-15") // Returns "Sept. 15, 2023"
 * formatDateMDY("2024-01-01") // Returns "Jan. 1, 2024"
 */
function formatDateMDY(dateString) {
    try {
        const date = new Date(dateString);
        
        const formatted = date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
        
        // Add a dot after the month abbreviation
        return formatted.replace(/^([A-Za-z]+)/, '$1.');
    } catch (e) {
        return dateString;
    }
}


// Helper function to parse TTL values like "1m", "30s", "2h"
const parseTTL = (ttlString) => {
  if (typeof ttlString === 'number') return ttlString; // Allow direct milliseconds
  const unit = ttlString.slice(-1);
  const value = parseInt(ttlString, 10);

  const multipliers = {
    s: 1000,       // seconds
    m: 60000,      // minutes
    h: 3600000,    // hours
  };

  return multipliers[unit] ? value * multipliers[unit] : 0; // Default to 0 if invalid
};


/**
 * Makes an API request and optionally caches the response.
 * 
 * By default, caching is disabled. If a TTL (time-to-live) is provided, 
 * the response is cached and reused for subsequent requests made within the TTL duration.
 * 
 * @param {string} url - The API endpoint URL.
 * @param {string} [verb='GET'] - HTTP method (GET, POST, PUT, DELETE, etc.).
 * @param {Object|null} [dataObj=null] - Request payload for POST/PUT requests.
 * @param {string|number|null} [cacheTTL=null] - Optional cache duration. 
 *        Supports human-readable formats ("30s", "1m", "2h") or milliseconds.
 * 
 * @returns {Promise<Object>} - A Promise resolving to the API response object.
 * 
 * @example
 * // Basic request with no caching
 * apiCall('https://api.example.com/data')
 *   .then(response => console.log(response));
 * 
 * @example
 * // Request with caching for 1 minute
 * apiCall('https://api.example.com/data', 'GET', null, '1m')
 *   .then(response => console.log(response));
 */
const apiCall = (url, verb = 'GET', dataObj = null, cacheTTL = null) => {
  const ttlMs = cacheTTL ? parseTTL(cacheTTL) : 0; // Convert TTL to milliseconds
  const cacheKey = `${verb}:${url}:${JSON.stringify(dataObj)}`;
  const now = Date.now();

  // Check cache only if TTL is set
  if (ttlMs > 0 && apiCache[cacheKey] && (now - apiCache[cacheKey].timestamp) < apiCache[cacheKey].ttl) {
    return Promise.resolve(apiCache[cacheKey].response);
  }

  return new Promise((resolve, reject) => {
    let config = {
      url,
      method: verb,
      headers: {
        Accept: "application/json",
      },
    };

    if (dataObj) {
      config = {
        ...config,
        contentType: 'application/json',
        data: JSON.stringify(dataObj),
      };
    }

    $.ajax({
      ...config,
      success: (result) => {
        const response = { status: 'success', data: result };

        // Store in cache only if TTL is specified
        if (ttlMs > 0) {
          apiCache[cacheKey] = {
            response,
            timestamp: now,
            ttl: ttlMs,
          };
        }

        resolve(response);
      },
      error: (xhr, textStatus, errorThrown) => {
        console.error(xhr, textStatus, errorThrown);
        reject({ status: 'fail', data: errorThrown });
      },
    });
  });
};

/**
 * Normalizes a timestamp to seconds based on its length compared to current epoch time
 * Handles various timestamp precisions (seconds, milliseconds, microseconds, etc.)
 *
 * @param {number} timestamp - The timestamp to normalize
 * @param {number} [referenceEpochLength] - Optional reference epoch length to use instead of calculating current time
 * @returns {number} - The normalized timestamp in seconds
 */
const normalizeTimestampToSeconds = (timestamp, referenceEpochLength) => {
    if (!timestamp) return timestamp;

    const currentEpochLength = referenceEpochLength || Math.floor(Date.now() / 1000).toString().length;
    const lengthDifference = timestamp.toString().length - currentEpochLength;

    if (lengthDifference > 0) {
        // Calculate the divisor based on the difference in length
        const divisor = Math.pow(10, lengthDifference);

        return Math.floor(timestamp / divisor);
    }

    return timestamp;
};

/**
 * Calculates and clamps the offset for the tooltip arrow based on axis dimensions.
 * Includes validation for input parameters.
 * @private
 * @param {number} triggerPosValue - Trigger position (left or top).
 * @param {number} triggerDimValue - Trigger dimension (width or height).
 * @param {number} tooltipPosValue - Tooltip position (left or top).
 * @param {number} tooltipDimValue - Tooltip dimension (width or height).
 * @param {number} arrowSize - The size of the arrow.
 * @param {number} edgeOffset - Minimum distance from the tooltip edge.
 * @returns {number} The calculated and clamped arrow offset, or a fallback value if validation fails.
 */
const _calculateArrowOffset = (triggerPosValue, triggerDimValue, tooltipPosValue, tooltipDimValue, arrowSize, edgeOffset) => {
    // Input validation
    if (typeof triggerPosValue !== 'number' || isNaN(triggerPosValue) ||
        typeof triggerDimValue !== 'number' || isNaN(triggerDimValue) ||
        typeof tooltipPosValue !== 'number' || isNaN(tooltipPosValue) ||
        typeof tooltipDimValue !== 'number' || isNaN(tooltipDimValue)) {

        console.error("Tooltip arrow helper error: Invalid input dimensions/position.", {
            triggerPosValue, triggerDimValue, tooltipPosValue, tooltipDimValue
        });
        // Return a fallback value (clamped center)
        const fallbackOffset = tooltipDimValue / 2 - arrowSize;
        return Math.max(edgeOffset, Math.min(tooltipDimValue - (arrowSize * 2) - edgeOffset, fallbackOffset));
    }

    try {
        const triggerCenter = triggerPosValue + triggerDimValue / 2;
        let offset = triggerCenter - tooltipPosValue - arrowSize;

        // Clamp the offset
        offset = Math.max(edgeOffset, offset);
        offset = Math.min(tooltipDimValue - (arrowSize * 2) - edgeOffset, offset);
        return offset;
    } catch (e) {
        console.error("Error during tooltip arrow offset calculation:", e);
        // Return a fallback value (clamped center)
        const fallbackOffset = tooltipDimValue / 2 - arrowSize;
        return Math.max(edgeOffset, Math.min(tooltipDimValue - (arrowSize * 2) - edgeOffset, fallbackOffset));
    }
};


/**
 * Handles the 'open' event for jQuery UI tooltips to dynamically
 * position an arrow based on the trigger's data-placement attribute.
 * Adjusts the tooltip's position and sets a CSS Custom Property '--arrow-offset' on the tooltip element.
 * Accounts for tooltip flipping due to collision detection.
 *
 * @param {Event} event - The event object.
 * @param {Object} ui - The jQuery UI object, containing ui.tooltip.
 */
function handleArrowTooltipOpen(event, ui) {
    var $trigger = $(this);
    var initialPlacement = $trigger.data('placement'); // Original intended placement
    var tooltip = ui.tooltip;

    if (!tooltip || !initialPlacement) {
        return;
    }

    const arrowSize = 6;
    const triggerTooltipGap = 4 // in px
    const dist = arrowSize + triggerTooltipGap // (Distance between trigger element and tooltip content)


    let positionOptions = {
        my: `center bottom-${dist}`,
        at: "center top",
        collision: "flipfit"
    };

    // Determine jQuery UI Position options based on the *initial* intended placement
    switch (initialPlacement) {
        case 'top': // Tooltip should be above trigger
            positionOptions.my = `center bottom-${dist}`;
            positionOptions.at = "center top";
            break;
        case 'bottom': // Tooltip should be below trigger
            positionOptions.my = `center top+${dist}`;
            positionOptions.at = "center bottom";
            break;
        case 'left': // Tooltip should be to the left of trigger
            positionOptions.my = `right-${dist} center`;
            positionOptions.at = "left center";
            break;
        case 'right': // Tooltip should be to the right of trigger
            positionOptions.my = `left+${dist} center`;
            positionOptions.at = "right center";
            break;
    }

    /**
     * Apply the calculated positioning using jQuery UI Position utility.
     * This ensures the tooltip container is placed correctly based on 'initialPlacement'
     * *before* we calculate the arrow's offset in the setTimeout below.
     * Collision detection might flip the actual position here.
     */
    try {
        tooltip.position({
            ...positionOptions,
            of: $trigger // Position relative to the trigger element
        });
    } catch (e) {
        console.error("Error applying tooltip position:", e);
        // Fallback positioning if error occurs
        tooltip.position({ of: $trigger, my: "center", at: "center" });
    }

    tooltip.addClass('ph-tooltip');
    // We'll add the directional class inside setTimeout after determining final placement
    tooltip.removeClass('ui-tooltip-top ui-tooltip-bottom ui-tooltip-left ui-tooltip-right');

    // Use setTimeout to calculate arrow offset AFTER positioning is applied and potentially flipped
    try {
        // Use setTimeout with 0 delay to allow the browser to apply the position first
        setTimeout(function() {
            if (!tooltip.is(":visible") || !$trigger.length) {
                return;
            }

            const edgeOffset = 6; // Min distance from tooltip edge in pixels
            const fallbackOffsetStyle = `calc(50% - ${arrowSize}px)`; // Default centered fallback
            let arrowOffset = 0;
            let finalPlacement = initialPlacement; // Start assuming no flip occurred

            try {
                // Get trigger and tooltip dimensions/positions *after* potential flip
                const triggerPos = $trigger.offset();
                const triggerWidth = $trigger.outerWidth();
                const triggerHeight = $trigger.outerHeight();
                const tooltipPos = tooltip.offset();
                const tooltipWidth = tooltip.outerWidth();
                const tooltipHeight = tooltip.outerHeight();

                // Determine Final Placement based *only* on final positions relative to trigger center points
                let determinedPlacement = initialPlacement; // Default

                if (triggerPos && tooltipPos) {
                    const triggerCenterY = triggerPos.top + triggerHeight / 2;
                    const tooltipCenterY = tooltipPos.top + tooltipHeight / 2;
                    const triggerCenterX = triggerPos.left + triggerWidth / 2;
                    const tooltipCenterX = tooltipPos.left + tooltipWidth / 2;

                    const dy = tooltipCenterY - triggerCenterY; // Positive if tooltip center is below trigger center
                    const dx = tooltipCenterX - triggerCenterX; // Positive if tooltip center is right of trigger center

                    // Determine dominant axis of displacement
                    if (Math.abs(dy) > Math.abs(dx)) { // Primarily vertical
                        determinedPlacement = (dy < 0) ? 'top' : 'bottom';
                    } else { // Primarily horizontal (or equal displacement)
                        determinedPlacement = (dx < 0) ? 'left' : 'right';
                    }
                } else {
                    console.error("Could not determine final tooltip placement due to missing offset data.");
                    // Stick with initialPlacement as a fallback
                }

                finalPlacement = determinedPlacement; 

                // If initial was bottom/top but dominant axis suggested left/right due to large horizontal shift,
                // check if it's *actually* still below/above and correct the finalPlacement if needed.
                if (triggerPos && tooltipPos) {
                    if ((initialPlacement === 'bottom') && (finalPlacement === 'left' || finalPlacement === 'right') && (tooltipPos.top > triggerPos.top)) {
                        finalPlacement = 'bottom'; // Override: It's still visually below
                    } else if ((initialPlacement === 'top') && (finalPlacement === 'left' || finalPlacement === 'right') && (tooltipPos.top < triggerPos.top + triggerHeight)) {
                        finalPlacement = 'top'; // Override: It's still visually above
                    }
                }


                // Add the correct directional class based on the *final* placement
                tooltip.removeClass('ui-tooltip-top ui-tooltip-bottom ui-tooltip-left ui-tooltip-right');
                tooltip.addClass('ui-tooltip-' + finalPlacement);


                // Calculate offset based on the AXIS determined by finalPlacement
                // Use horizontal calculation for top/bottom final placement
                if (finalPlacement === 'top' || finalPlacement === 'bottom') {
                    arrowOffset = _calculateArrowOffset(
                        triggerPos?.left,
                        triggerWidth,
                        tooltipPos?.left,
                        tooltipWidth,
                        arrowSize,
                        edgeOffset
                    );
                // Use vertical calculation for left/right final placement
                } else { // finalPlacement === 'left' || finalPlacement === 'right'
                     arrowOffset = _calculateArrowOffset(
                        triggerPos?.top,
                        triggerHeight,
                        tooltipPos?.top,
                        tooltipHeight,
                        arrowSize,
                        edgeOffset
                    );
                }

                const arrowOffsetVar = arrowOffset + 'px';
                tooltip.css('--arrow-offset', arrowOffsetVar);

            } catch (calcError) {
                 console.error("Error calculating or applying tooltip arrow offset:", calcError);
                 tooltip.css('--arrow-offset', fallbackOffsetStyle);
                 // Ensure atleast the base class and potentially the initially intended class are present on error
                 if (!tooltip.is('[class*="ui-tooltip-"]')) {
                    tooltip.addClass('ui-tooltip-' + initialPlacement);
                 }
            }

        }, 0);

    } catch (e) {
        console.error("Error setting up tooltip arrow calculation (setTimeout):", e);
         tooltip.css('--arrow-offset', `calc(50% - ${arrowSize}px)`);
         // Ensure base and initial classes on outer error
         tooltip.addClass('ph-tooltip ui-tooltip-' + initialPlacement);
    }
}

/**
 * Creates a linear gradient for a Chart.js dataset background based on its border color.
 * @param {object} context - The Chart.js context object.
 * @returns {CanvasGradient|null} A linear gradient or null if chartArea is not available.
 */
function createChartGradient(context, borderColorDef = 'rgb(33, 150, 243)') {
    const chart = context.chart;
    const { ctx, chartArea } = chart;

    if (!chartArea) {
        return null;
    }

    const borderColor = context.dataset?.borderColor || borderColorDef;
    const transparentColor = borderColor.replace('rgb(', 'rgba(').replace(')', ', 0.035)');
    const topColor = borderColor.replace('rgb(', 'rgba(').replace(')', ', 0.3)');

    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
    gradient.addColorStop(0, transparentColor); 
    gradient.addColorStop(1, topColor);      

    return gradient;
}

function setChartGridColor(gridOptions = {}) {
    return {
        ...gridOptions,
        color: 'rgba(228, 239, 245, 0.45)',
        borderColor: 'rgba(228, 239, 245, 0.45)',
    }
}

function setChartDataset(dataset = {}) {
    return {
        ...dataset,
        backgroundColor: createChartGradient,
        tension: 0.3,
        borderWidth: 1,
    }
}

function preloadImage(url) {
    if (imageCache[url]) return;

    return new Promise((resolve) => {
        const img = new Image();
        img.src = url;
        img.onload = function() {
            imageCache[url] = img;
            resolve(img);
        };
        img.onerror = function() {
            this.src = '/assets/images/default-org-logo.svg';
            resolve(img);
        };
    });
}

/**
 * ModuleGroupPicker - A reusable class for rendering grouped module selection interface
 * with accordion-style groups, individual chip selection, and group-level controls.
 *
 * @example
 * const picker = new ModuleGroupPicker(
 *     $('.module-container'),
 *     modules,
 *     selectedModules,
 *     {
 *         groupProperty: 'category', // Custom property to group by (default: 'group')
 *         onSelectionChange: (selectedIds) => console.log('Selection changed:', selectedIds)
 *     }
 * );
 */
class ModuleGroupPicker {
    constructor(container, modules, selectedModules, options = {}) {
        this.container = $(container);
        this.modules = modules || [];
        this.selectedModuleIds = new Set((selectedModules || []).map(m => m.moduleId || m));

        this.options = {
            groupProperty: 'group',
            defaultGroupName: 'Other',
            onSelectionChange: null,
            hiddenInputName: 'modules',
            expandAllText: t('Expand All'),
            collapseAllText: t('Collapse All'),
            selectAllText: t('Select All'),
            deselectAllText: t('Deselect All'),
            selectedText: t('selected'),
            noModulesText: t('No items available'),
            ...options
        };

        this.moduleGroups = this.groupModules();

        this.render();
        this.bindEvents();
    }

    groupModules() {
        if (!this.modules || this.modules.length === 0) {
            return {};
        }

        return this.modules.reduce((acc, module) => {
            const group = module[this.options.groupProperty] || this.options.defaultGroupName;
            if (!acc[group]) acc[group] = [];
            acc[group].push(module);
            return acc;
        }, {});
    }

    generateControlsTemplate() {
        return `
            <div class="ph-accordion-controls" style="margin-bottom: 1rem;">
                <button type="button" class="btn ph-btn-xs ph-btn btn-secondary expand-all-toggle">
                    <i class="fa fa-expand"></i> ${this.options.expandAllText}
                </button>
            </div>
        `;
    }

    generateGroupTemplate(group, modules, index) {
        const accordionItemId = `collapse-group-${index}`;
        const selectedCount = modules.filter(m => this.selectedModuleIds.has(m.moduleId)).length;
        const isAllSelected = selectedCount === modules.length;

        return `
            <div class="panel" data-group="${group}">
                <div class="panel-heading" role="tab">
                    <a data-toggle="collapse" data-target="#${accordionItemId}" 
                       aria-expanded="false" aria-controls="${accordionItemId}" class="panel-title">
                        <div><h6 class="group-title">${t(group)}</h6></div>
                        <div class="header-controls">
                            <span class="selected-count badge ph-badge ph-badge--bgless">
                                ${selectedCount} of ${modules.length} ${this.options.selectedText}
                            </span>
                            <button type="button" class="btn ph-btn ph-btn-xs select-all-group ${isAllSelected ? '--selected btn-primary' : 'btn-secondary'}"
                                    data-group="${group}">
                                <i class="material-icons">${isAllSelected ? 'indeterminate_check_box' : 'library_add_check'}</i> ${isAllSelected ? this.options.deselectAllText : this.options.selectAllText}
                            </button>
                            <i class="fa fa-chevron-down accordion-chevron"></i>
                        </div>
                    </a>
                </div>
                <div id="${accordionItemId}" class="panel-collapse collapse">
                    <div class="panel-body">
                        <div class="chips-container chips" data-group-chips="${group}">
                            ${this.generateChipsTemplate(modules, group)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateChipsTemplate(modules, group) {
        return modules.map(module => {
            const isSelected = this.selectedModuleIds.has(module.moduleId);
            const selectedClass = isSelected ? 'selected' : '';
            return `<div class="module-chip ${selectedClass}" data-module-id="${module.moduleId}" data-group="${group}">
                        ${t(module.name)}
                    </div>`;
        }).join('');
    }

    render() {
        const groups = Object.keys(this.moduleGroups);

        if (groups.length < 1) {
            this.container.html(`<div class="ph-text"><div class="text-muted" style="opacity: 0.5;">${this.options.noModulesText}</div></div>`);
            return;
        }

        const html = `
            ${this.generateControlsTemplate()}
            <div class="panel-group ph-panel-group module-groups-accordion">
                ${groups.map((group, index) =>
                    this.generateGroupTemplate(group, this.moduleGroups[group], index)
                ).join('')}
            </div>
        `;

        this.container.html(html);
        this.cacheElements();
        // Fix: Create hidden inputs for initially selected modules
        this.updateHiddenInputs();
    }

    cacheElements() {
        this.elements = {
            accordion: this.container.find('.module-groups-accordion'),
            expandToggle: this.container.find('.expand-all-toggle'),
            panels: this.container.find('.panel-collapse')
        };
    }

    updateSelection(moduleId, isSelected) {
        if (isSelected) {
            this.selectedModuleIds.add(moduleId);
        } else {
            this.selectedModuleIds.delete(moduleId);
        }
        
        this.updateGroupCounts();
        this.updateHiddenInputs();

        if (this.options.onSelectionChange) {
            this.options.onSelectionChange(Array.from(this.selectedModuleIds));
        }
    }

    updateGroupCounts() {
        Object.keys(this.moduleGroups).forEach(group => {
            const modules = this.moduleGroups[group];
            const selectedCount = modules.filter(m => this.selectedModuleIds.has(m.moduleId)).length;
            const isAllSelected = selectedCount === modules.length;
            const groupEl = this.container.find(`[data-group="${group}"]`);
            const selectAllButton = groupEl.find('.select-all-group');
            selectAllButton.html(`<i class="material-icons">${isAllSelected ? 'indeterminate_check_box' : 'library_add_check'}</i> ${isAllSelected ? this.options.deselectAllText : this.options.selectAllText}`);
            selectAllButton.toggleClass('--selected', isAllSelected);
            selectAllButton.toggleClass('btn-primary', isAllSelected);
            selectAllButton.toggleClass('btn-secondary', !isAllSelected);
            groupEl.find('.selected-count').text(`${selectedCount} of ${modules.length} ${this.options.selectedText}`);
        });
    }

    updateHiddenInputs() {
        this.container.find('.hidden-module-inputs').remove();
        this.selectedModuleIds.forEach(moduleId => {
            this.container.append(`<input type="hidden" name="${this.options.hiddenInputName}" value="${moduleId}" class="hidden-module-inputs">`);
        });
    }

    toggleGroupSelection(groupName) {
        const modules = this.moduleGroups[groupName];
        const allSelected = modules.every(m => this.selectedModuleIds.has(m.moduleId));
        const chipsContainer = this.container.find(`[data-group-chips="${groupName}"]`);

        modules.forEach(module => {
            const chip = chipsContainer.find(`[data-module-id="${module.moduleId}"]`);

            if (allSelected) {
                this.selectedModuleIds.delete(module.moduleId);
                chip.removeClass('selected');
            } else {
                this.selectedModuleIds.add(module.moduleId);
                chip.addClass('selected');
            }
        });

        this.updateGroupCounts();
        this.updateHiddenInputs();

        if (this.options.onSelectionChange) {
            this.options.onSelectionChange(Array.from(this.selectedModuleIds));
        }
    }

    bindEvents() {
        this.container.on('click', '.module-chip', (e) => {
            e.stopPropagation();
            const chip = $(e.currentTarget);
            const moduleId = chip.data('module-id');
            const isSelected = chip.hasClass('selected');

            chip.toggleClass('selected');
            this.updateSelection(moduleId, !isSelected);
        });

        // Group select all/deselect all
        this.container.on('click', '.select-all-group', (e) => {
            e.stopPropagation();
            e.preventDefault();
            const groupName = $(e.currentTarget).data('group');
            $(e.currentTarget).toggleClass('--selected');
            this.toggleGroupSelection(groupName);
        });

        // Expand/collapse all
        this.elements.expandToggle.on('click', (e) => {
            const isExpanded = $(e.currentTarget).hasClass('expanded');

            if (isExpanded) {
                this.elements.panels.collapse('hide');
                $(e.currentTarget).removeClass('expanded').html(`<i class="fa fa-expand"></i> ${this.options.expandAllText}`);
            } else {
                this.elements.panels.collapse('show');
                $(e.currentTarget).addClass('expanded').html(`<i class="fa fa-compress"></i> ${this.options.collapseAllText}`);
            }
        });
    }

    getSelectedModules() {
        return Array.from(this.selectedModuleIds);
    }

    setSelectedModules(moduleIds) {
        this.selectedModuleIds = new Set(moduleIds);
        this.render();
    }

    addModule(moduleId) {
        this.selectedModuleIds.add(moduleId);
        this.updateSelection(moduleId, true);
    }

    removeModule(moduleId) {
        this.selectedModuleIds.delete(moduleId);
        this.updateSelection(moduleId, false);
    }

    destroy() {
        this.container.off();
        this.container.empty();
    }
}

/**
 * Creates a new instance of the ModuleGroupPicker class for rendering grouped module selection interface.
 * This is a global helper function for easy access across the application.
 *
 * @param {jQuery} el - Container element where the module picker will be rendered
 * @param {Array} modules - Array of available modules
 * @param {Array} selectedModules - Array of currently selected modules
 * @param {Object} options - Configuration options for the picker
 * @returns {ModuleGroupPicker} Instance of the ModuleGroupPicker class
 */
function renderModuleGroupPickers(container, modules, selectedModules, options = {}) {
    return new ModuleGroupPicker(container, modules, selectedModules, options);
}

const genButton = (buttonConfig, { strOutput = false } = {}) => {
    const { label, icon, onClick, hidden, id, data, className, disabled, iconPosition = 'right', iconSize = 'md' } = buttonConfig;

    const hiddenClass = hidden ? 'hidden' : '';
    const idProp = id ? `id="${id}"` : '';
    const dataProps = data ? Object.keys(data).map(key => `data-${key}="${data[key]}"`).join(' ') : '';
    const disabledProp = disabled ? 'disabled' : '';
    const iconSizeMap = {
        sm: 'font-size: 14px;',
        md: 'font-size: 16px;',
        lg: 'font-size: 18px;',
    }
    const iconSizeStyle = iconSizeMap[iconSize] || '';
    const iconHtml = icon.includes('fa-') ? `<i class="fa ${icon}" style="${iconSizeStyle}"></i>` : `<i class="material-icons" style="${iconSizeStyle}">${icon}</i>`;
    const buttonHtml = `
        <button ${idProp} class="btn waves-effect use-skeleton ${className || ''} ${hiddenClass}" ${dataProps} style="background: transparent;" ${disabledProp}>
            ${iconPosition === 'left' ? iconHtml : ''}
            <span>${t(label)}</span>
            ${iconPosition === 'right' ? iconHtml : ''}
        </button>
    ` 

    if (strOutput) {
        return buttonHtml;
    }

    const button = $(buttonHtml);
    if (onClick) {
        button.on('click', () => onClick());
    }

    return button;
}

const renderBarButtons = (targetElement, buttonConfig) => {
    const button = genButton(buttonConfig);
    
    $(targetElement).append(button);
    
    return button;
};
