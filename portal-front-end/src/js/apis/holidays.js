async function listCountryHolidaysAPI(country) {
    try {
        const { holidays } = await $.ajax({
            method: 'GET',
            url: `/api/admin/country/${country}/holidays`,
        });
        return [true, holidays];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false];
    }
}

async function listClubUnReviewedHolidaysAPI(clubId) {
    try {
        const { holidays } = await $.ajax({
            method: 'GET',
            url: `/api/reporting/club/${clubId}/unreviewed-holidays`,
        });
        return [true, holidays];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false];
    }
}
