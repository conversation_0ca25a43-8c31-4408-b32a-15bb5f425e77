async function createSaasBillingAdditionalItemAPI(data) {
    try {
        const response = await $.ajax({
            method: 'POST',
            url: '/api/saas-billing/additional-line-items',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function updateSaasBillingAdditionalItemAPI(id, data) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/saas-billing/additional-line-items/${id}`,
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function listClubUpcomingAdditionalItemsAPI(clubId, endDate) {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/saas-billing/additional-line-items`,
            data: { endDate },
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}
