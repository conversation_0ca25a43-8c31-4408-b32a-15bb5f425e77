async function listClubFeesConfigAPI(clubID, paramString = '') {
    try {
        const data = await $.ajax({ url: `/api/club/${clubID}/charges-config${paramString || ''}`, method: 'GET' });
        return [true, data];
    } catch (error) {
        console.error(error);
        instantNotification('Failed to fetch config block!', 'error');
        return [false, error.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function listAdminRoyaltyChargesConfigAPI() {
    try {
        const response = await $.ajax({ url: '/api/admin/charges-config', method: 'GET' });
        return [true, response.blocks];
    } catch (error) {
        console.error(error);
        return [false, error.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function createChargesConfigAPI(data) {
    try {
        const response = await $.ajax({
            method: 'POST',
            contentType: 'application/json',
            url: '/api/admin/charges-config',
            data: JSON.stringify(data),
        });

        return [true, response.createdBlock];
    } catch (error) {
        console.error(error);
        instantNotification('Failed to create config block', 'error');
        return [false];
    }
}

async function listSaasBillingConfigBlocksAPI() {
    try {
        const response = await $.ajax({ url: '/api/admin/saas-billing/charges-config', method: 'GET' });
        return [true, response];
    } catch (error) {
        console.error(error);
        return [false, error.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function updateSaasBillingConfigBlockAPI(target, charges) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            contentType: 'application/json',
            url: `/api/admin/saas-billing/charges-config/${target}`,
            data: JSON.stringify(charges),
        });

        return [true, response];
    } catch (error) {
        console.error(error);
        return [false, error.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function deleteSaasBillingConfigBlockAPI(target) {
    try {
        const response = await $.ajax({ url: `/api/admin/saas-billing/charges-config/${target}`, method: 'DELETE' });
        return [true, response];
    } catch (error) {
        console.error(error);
        return [false, error.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function getClubBillingChargeBreakdownAPI(clubID) {
    try {
        const response = await $.ajax({ url: `/api/saas-billing/charges-config/${clubID}`, method: 'GET' });
        return [true, response];
    } catch (error) {
        console.error(error);
        return [false, error.responseJSON?.message ?? 'Something went wrong'];
    }
}
