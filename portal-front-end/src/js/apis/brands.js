async function listBrandsAvailableToClubAPI(clubID) {
    try {
        const response = await $.ajax({ method: 'GET', url: `/api/clubs/${clubID}/brands` });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function listBrandsAPI() {
    try {
        const response = await $.ajax({ method: 'GET', url: '/api/brands' });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function createBrandAPI(data) {
    try {
        const response = await $.ajax({ method: 'POST', url: '/api/brands', data });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}
async function updateBrandAPI(brandId, data) {
    try {
        const response = await $.ajax({ method: 'PUT', url: `/api/brands/${brandId}`, data });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}
