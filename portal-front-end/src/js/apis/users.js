const listUsersAPI = async () => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: '/api/admin/users/listusers',
        });

        if (!response) return [false];
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

async function listAuthorisedAPI() {
    try {
        const user = await $.ajax({
            method: 'GET',
            url: '/api/user/listauthorised',
        });

        return [true, user];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function getMyOrgAccessForModuleAPI(moduleId) {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/user/me/modules/${moduleId}/org-access`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function addUserAPI(user) {
    try {
        const response = await $.ajax({
            method: 'POST',
            url: '/api/users/',
            contentType: 'application/json',
            data: JSON.stringify(user),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function upsertUserRoleAPI(userId, roleId, facilities, regions) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/users/${userId}/roles`,
            contentType: 'application/json',
            data: JSON.stringify({ roleId, facilities, regions }),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function removeUserRoleAPI(userId, roleId) {
    try {
        const response = await $.ajax({
            method: 'DELETE',
            url: `/api/users/${userId}/roles/${roleId}`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function removeUserAPI(userId) {
    try {
        const response = await $.ajax({
            method: 'DELETE',
            url: `/api/users/${userId}`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}
