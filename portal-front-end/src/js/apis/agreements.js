async function listClubAgreementsAPI(clubId, queries) {
    const queryString = Object.entries(queries).reduce((acc, [key, value]) => {
        return `${acc}&${key}=${value}`;
    }, '');

    try {
        const data = await $.ajax({
            url: `/api/agreements/list-agreements/${clubId}?${queryString}`,
            method: 'GET',
            contentType: 'application/json',
        });

        return [true, data];
    } catch (error) {
        console.error(error.responseJSON);
        return [false, error.responseJSON];
    }
}

async function createAgreementAPI(data) {
    try {
        const response = await $.ajax({
            url: '/api/admin/agreements/save',
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON);
        return [false, error.responseJSON];
    }
}
