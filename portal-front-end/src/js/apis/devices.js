const listClubDevicesAPI = async (clubID) => {
    try {
        const timeoutPromise = new Promise((resolve, reject) => {
            setTimeout(() => {
                reject(new Error('Request timed out'));
            }, 5000); // 5 seconds timeout
        });

        const requestPromise = $.ajax({
            method: 'GET',
            url: `/api/devices/list/${clubID}`,
        });

        const response = await Promise.race([requestPromise, timeoutPromise]);

        return [true, response];
    } catch (err) {
        console.error(_get(err, 'responseJSON', err));
        return [false, _get(err, 'responseJSON.message', 'Something went wrong')];
    }
};


const listClubLowBatteryDuressAPI = async (clubID) => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubID}/smart-club/health-check/low-battery-duress`,
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
};
