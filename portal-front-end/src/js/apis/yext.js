const listClubYextListingsAPI = async (clubId) => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/club/${clubId}/yext/listings`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const updateClubYextEntity = async (clubId) => {
    try {
        await $.ajax({
            method: 'PUT',
            url: `/api/club/${clubId}/yext`,
        });
        instantNotification('Directories updated', 'success');
    } catch (err) {
        const message = err.responseJSON?.errors?.[0]?.message || 'Please contact HQ for assistance.';
        instantNotification(`Failed to push to directories. ${message}`, 'error');
    }
};
