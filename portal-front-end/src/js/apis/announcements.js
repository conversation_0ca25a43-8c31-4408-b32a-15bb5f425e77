async function upsertAnnouncementAPI(data) {
    try {
        const response = await $.ajax({
            url: '/api/announcements/save',
            type: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (error) {
        console.error('Error saving announcement:', error);
        return [false, error?.responseJSON?.message || 'Something went wrong'];
    }
}
