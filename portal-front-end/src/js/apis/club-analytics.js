async function getClubTotalPageViewsAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/analytics/total-page-views?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}

async function getClubPageViewsAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/analytics/page-views?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}

async function getClubLeadEventsAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/lead-events?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}

async function getClubReferrersAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/analytics/referrers?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}

async function getClubTrafficAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/analytics/traffic?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}

async function getClubTopCitiesAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/analytics/top-cities?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}

async function getClubSocialsAPI(clubId, fromDate, toDate) {
    const queryString = new URLSearchParams({ fromDate, toDate }).toString();

    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/analytics/socials?${queryString}`,
        });
        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message || err);
        return [false, err.responseJSON?.message || 'Something went wrong. Please try again later.'];
    }
}
