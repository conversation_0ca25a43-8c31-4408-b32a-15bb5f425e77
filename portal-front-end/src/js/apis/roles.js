async function listRolesAvailableToClubAPI(clubID) {
    try {
        const response = await $.ajax({ method: 'GET', url: `/api/clubs/${clubID}/roles` });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function listRolesAPI() {
    try {
        const response = await $.ajax({ method: 'GET', url: '/api/roles' });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function createRoleAPI(data) {
    try {
        const response = await $.ajax({ method: 'POST', url: '/api/roles', data });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}
async function updateRoleAPI(roleId, data) {
    try {
        const response = await $.ajax({ method: 'PUT', url: `/api/roles/${roleId}`, data });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function deleteRoleAPI(roleId) {
    try {
        await $.ajax({ method: 'DELETE', url: `/api/roles/${roleId}` });
        return [true];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}
