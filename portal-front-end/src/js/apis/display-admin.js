// Display UI Options
async function listAllDisplayUIOptionsAPI() {
    try {
        const response = await $.ajax({ method: 'GET', url: '/api/ics/ui-options' });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function createDisplayUIOptionAPI(option) {
    try {
        const payload = option;
        const response = await $.ajax({ method: 'POST', url: '/api/ics/ui-options', data: payload });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function updateDisplayUIOptionAPI(option) {
    try {
        const payload = option;
        const response = await $.ajax({ method: 'PUT', url: `/api/ics/ui-options`, data: payload });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

// Display Data Sources
async function listAllDisplayDataSourcesAPI() {
    try {
        const response = await $.ajax({ method: 'GET', url: '/api/ics/data-sources' });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function createDisplayDataSourceAPI(source) {
    try {
        const payload = source;
        const response = await $.ajax({ method: 'POST', url: '/api/ics/data-sources', data: payload });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function updateDisplayDataSourceAPI(source) {
    try {
        const payload = source;
        const response = await $.ajax({ method: 'PUT', url: `/api/ics/data-sources`, data: payload });
        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}

async function listAllCalendarTypes() {
    try {
        const types = await $.ajax({ method: 'GET', url: '/api/programs/calendars/calendar-types' });
        return [true, types];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message || 'Something went wrong'];
    }
}
