async function listLandingPages() {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: '/api/admin/landing-pages',
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function addLandingPage(data) {
    console.log('DATA', data);
    try {
        const response = await $.ajax({
            method: 'POST',
            url: '/api/admin/landing-pages',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function updateLandingPage(data) {
    try {
        const response = await $.ajax({
            method: 'PATCH',
            url: '/api/admin/landing-pages',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function deleteLandingPage(data) {
    try {
        const response = await $.ajax({
            method: 'DELETE',
            url: '/api/admin/landing-pages',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
}
