async function listOrganisationsAPI(populate = []) {
    try {
        const query = {};

        if (populate.includes('brands')) {
            query.includeBrands = true;
        }

        if (populate.includes('facilities')) {
            query.includeFacilities = true;
        }

        if (populate.includes('regions')) {
            query.includeRegions = true;
        }

        const queryString = stringifyQueryStringsObject(query);

        const response = await $.ajax({
            method: 'GET',
            url: `/api/organisations${queryString}`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message ?? 'Error fetching organisations'];
    }
}

async function upsertOrganisationAPI(organisation) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/organisations`,
            data: JSON.stringify(organisation),
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false, err.responseJSON?.message ?? 'Error updating organisation'];
    }
}
