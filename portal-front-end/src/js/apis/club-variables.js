const listClubVariablesAPI = async (clubId) => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/marketing/variables/${clubId}/list`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const saveClubVariablesAPI = async (clubId, variables) => {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/marketing/variables/${clubId}/save`,
            contentType: 'application/json',
            data: JSON.stringify(variables),
        });

        if (!response) return [false];

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const deleteClubVariableAPI = async (clubId, variable) => {
    try {
        const response = await $.ajax({
            method: 'DELETE',
            url: `/api/marketing/variables/${clubId}/delete`,
            contentType: 'application/json',
            data: JSON.stringify({ variable }),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const createClubVariableAPI = async (clubId, doc) => {
    try {
        const response = await $.ajax({
            method: 'POST',
            url: `/api/marketing/variables/${clubId}/new`,
            contentType: 'application/json',
            data: JSON.stringify(doc),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};
