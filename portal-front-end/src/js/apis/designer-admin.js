async function listDesignerCustomPagesAPI(organisationId, facilityId) {
    try {
        const response = await $.ajax({
            url: `/api/marketing/custom-pages?organisationId=${organisationId}&facilityId=${facilityId}`,
            method: 'GET',
        });
        return [true, response];
    } catch (error) {
        console.error('Error fetching designer custom pages:', error);
        return [false, error.responseJSON?.message || 'Something went wrong. Please try again.'];
    }
}

async function listFacilityDesignerCustomPagesAPI(facilityId) {
    try {
        const response = await $.ajax({
            url: `/api/facilities/${facilityId}/marketing/custom-pages`,
            method: 'GET',
        });
        return [true, response];
    } catch (error) {
        console.error('Error fetching designer custom pages:', error);
        return [false, error.responseJSON?.message || 'Something went wrong. Please try again.'];
    }
}

async function upsertDesignerCustomPageAPI(data) {
    try {
        const response = await $.ajax({
            url: '/api/marketing/custom-pages',
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });
        return [true, response];
    } catch (error) {
        console.error('Error upserting designer custom page:', error);
        return [false, error.responseJSON?.message || 'Something went wrong. Please try again.'];
    }
}
