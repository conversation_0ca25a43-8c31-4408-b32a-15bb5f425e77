async function getAlgoliaConfigAPI(facilityId) {
    try {
        const response = await $.ajax({
            url: `/api/facilities/${facilityId}/algolia/config`,
            type: 'GET',
        });

        return [true, response];
    } catch (error) {
        console.error('Error fetching Algolia config:', error);
        return [false, { message: 'Failed to fetch Algolia configuration.' }];
    }
}
