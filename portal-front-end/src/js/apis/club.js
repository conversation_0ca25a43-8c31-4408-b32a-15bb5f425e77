const getClubDetailsAPI = async (clubId) => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/clubpage/clubdata/${clubId}`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const updateClubAPI = async (clubId, data, updateAll = false) => {
    try {
        const response = await $.ajax({
            method: 'POST',
            url: `/api/clubs/clubpage/updateclub/${clubId}`,
            contentType: 'application/json',
            data: JSON.stringify({ ...data, updateAll }),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const updateClubIntegration = async (clubId, data) => {
    try {
        const response = await $.ajax({
            method: 'POST',
            url: `/api/clubs/integrations/update/${clubId}`,
            contentType: 'application/json',
            data: JSON.stringify({ ...data }),
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

async function listClubsAPI() {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: '/api/clubs/listclubs',
        });

        if (!response) return [false];

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

const getDefaultClubPhotoAPI = async (clubId, type) => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/default-images/${type}`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

function reviewClubHolidaysAPI(clubID) {
    try {
        $.ajax({
            url: `/api/reporting/club/${clubID}/review-holidays`,
            method: 'PUT',
        });

        return true;
    } catch (err) {
        console.error(err?.responseJSON?.message ?? 'Error reviewing club holidays');
        return false;
    }
}

async function getClubOwnersAPI(clubID) {
    try {
        const data = await $.ajax({
            url: `/api/clubs/${clubID}/owners`,
            method: 'GET',
        });

        return [true, data];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Error getting club owners'];
    }
}

async function createClubAPI(data, orgId) {
    try {
        const res = await $.ajax({
            url: '/api/admin/clubs/addclub?organisationId=' + orgId,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, res];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong. Please try again.'];
    }
}

async function upsertAdminNoteToFacilityAPI(facilityId, note) {
    try {
        const res = await $.ajax({
            url: `/api/facilities/${facilityId}/admin-notes`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(note),
        });

        return [true, res];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong. Please try again.'];
    }
}

async function listFacilityAdminNotesAPI(facilityId) {
    try {
        const res = await $.ajax({
            url: `/api/facilities/${facilityId}/admin-notes`,
            method: 'GET',
        });

        return [true, res];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong. Please try again.'];
    }
}

async function deleteAdminNoteFromFacilityAPI(facilityId, noteId) {
    try {
        const res = await $.ajax({
            url: `/api/facilities/${facilityId}/admin-notes/${noteId}`,
            method: 'DELETE',
        });

        return [true, res];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong. Please try again.'];
    }
}
