const publishUnpublishReviewAPI = async (clubID, reviewID, publish) => {
    try {
        await $.ajax({
            url: `/api/clubs/${clubID}/reviews/${reviewID}/${publish ? 'publish' : 'un-publish'}`,
            method: 'PUT',
            contentType: 'application/json',
        });
        return [true];
    } catch (error) {
        console.error(error.responseJSON.message);
        return [false];
    }
};
