const acceptClubStripeAccountTOSAPI = async (clubID) => {
    try {
        await $.ajax({
            method: 'GET',
            url: `/api/club/${clubID}/stripe/accept-account-tos`,
        });

        return [true];
    } catch (err) {
        console.error(err.responseJSON);
        return [false, err.responseJSON.message];
    }
};

const getStripeConnectedAccountDetailsAPI = async (clubID) => {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/club/${clubID}/stripe/connect-account-details`,
        });

        return [true, data];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const generateStripeKYCTokenAPI = async (clubID) => {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/club/${clubID}/stripe/generate-kyc-token`,
        });

        return [true, data];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

async function getStripePublishableKeyAPI() {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: '/api/stripe/publishable-key',
        });
        return [true, data];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function getPlatformConnectedAccountIdAPI() {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: '/api/stripe/platform-connected-account-id',
        });
        return [true, data];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function enableApplePayGooglePay(clubID) {
    try {
        const data = await $.ajax({
            method: 'POST',
            url: `/api/stripe/enable-apple-pay-google-pay/${clubID}`,
        });

        console.log(data);
        return [true, data];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function getStripePaymentIntentAPI(paymentIntentId) {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/stripe/payment-intents/${paymentIntentId}`,
        });

        return [true, data];
    } catch (err) {
        console.error(err);
        return [false];
    }
}

async function cancelStripePaymentIntentAPI(clubID, paymentIntentId) {
    try {
        const data = await $.ajax({
            method: 'get',
            url: `/api/club/${clubID}/stripe/payment-intents/${paymentIntentId}/cancel`,
        });

        return [true, data];
    } catch (err) {
        return [false, err.responseJSON.message];
    }
}