async function getSecureUploadTokenAPI(fileName, public, orgID) {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: `/api/admin/secure-upload/token?fileName=${fileName}&${
                public ? 'public=true' : ''
            }&organisationId=${orgID}`,
        });
        return [true, data];
    } catch (err) {
        console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
        return [false];
    }
}

async function listSecureUploadsAPI(orgId) {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: '/api/admin/secure-upload/files?organisationId=' + orgId,
        });
        return [true, data];
    } catch (err) {
        console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
        return [false];
    }
}

async function listLegacyUploadsAPI() {
    try {
        const data = await $.ajax({
            method: 'GET',
            url: '/api/admin/secure-upload/old-assets',
        });
        return [true, data];
    } catch (err) {
        console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
        return [false];
    }
}

async function saveSecureUploadAPI(params, orgID) {
    try {
        const data = await $.ajax({
            method: 'POST',
            url: `/api/admin/secure-upload/files?organisationId=${orgID}`,
            data: params,
        });
        return [true, data];
    } catch (err) {
        console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
        return [false];
    }
}

const uploadToS3API = async (tokenUrl, formData, onUpdate) => {
    try {
        const response = await $.ajax({
            url: tokenUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: onUpdate,
        });
        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false];
    }
};

async function deleteSecureUploadAPI(key, permanent, orgID) {
    const query = permanent ? `?permanent=true&organisationId=${orgID}` : `?organisationId=${orgID}`;
    try {
        const data = await $.ajax({
            method: 'DELETE',
            url: `/api/admin/secure-upload/${key}${query}`,
        });
        return [true, data];
    } catch (err) {
        console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
        return [false];
    }
}
