async function getClubSaasBillingMonthlyReportAPI(clubId, month, year) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/saas-billing/extended-access/monthly-usage-report/${clubId}?month=${month}&year=${year}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function getClubSaasBillingPeriodicalReportAPI(
    clubId,
    start = moment().startOf('month').format('YYYY-MM'),
    end = moment().endOf('month').format('YYYY-MM')
) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/saas-billing/extended-access/monthly-usage-report/${clubId}?periodStart=${start}&periodEnd=${end}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function importClubSaasBillingMonthlyReportsAPI(clubId, reports) {
    try {
        const response = await $.ajax({
            type: 'PUT',
            url: `/api/saas-billing/extended-access/usage-report/${clubId}`,
            data: { reports },
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function listImportedReportsAPI() {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/saas-billing/extended-access/usage-report/imported`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function deleteImportedReportAPI(reportId) {
    try {
        const response = await $.ajax({
            type: 'DELETE',
            url: `/api/saas-billing/extended-access/monthly-usage-report/${reportId}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function listDailyReportsOfMonthAPI(clubId, month, year) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/saas-billing/extended-access/monthly-usage-report/${clubId}/daily-reports?month=${month}&year=${year}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function paginateMonthlyBillingRecordsAPI(filters = {}) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/saas-billing/monthly-records`,
            data: { filters },
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function manuallyBillClubAPI(clubId, billingMonth) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/admin/tasks/monthly-biller?clubID=${clubId}&forceBilling=true&billingMonth=${billingMonth}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function manuallyRunBillingCronAPI(unixTime) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/admin/tasks/monthly-biller?simulationTime=${unixTime}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function getClubMonthlyRecordAPI(recordID, month = moment().format('YYYY-MM')) {
    try {
        const response = await $.ajax({
            type: 'GET',
            url: `/api/saas-billing/monthly-records/${recordID}?month=${month}`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function updateClubMonthlyRecordAPI(recordID, data) {
    try {
        const response = await $.ajax({
            type: 'PUT',
            url: `/api/saas-billing/monthly-records/${recordID}`,
            data,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function addNoteToClubMonthlyRecordAPI(recordID, note) {
    try {
        const response = await $.ajax({
            type: 'POST',
            url: `/api/saas-billing/monthly-records/${recordID}/notes`,
            data: { note },
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}

async function validateMonthlyRecordStatusAPI(clubID, recordID) {
    try {
        const response = await $.ajax({
            type: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/monthly-records/${recordID}/validate`,
        });
        return [true, response];
    } catch (error) {
        console.error(error.responseJSON?.message || error);
        return [false, error.responseJSON?.message || 'Something went wrong'];
    }
}