const getClubKycAPI = async (clubId) => {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubId}/kyc`,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};

const updateClubKycAPI = async (clubId, data) => {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubId}/kyc`,
            data,
        });

        return [true, response];
    } catch (err) {
        console.error(err);
        return [false];
    }
};
