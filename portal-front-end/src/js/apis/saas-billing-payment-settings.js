async function getClubSaasBillingPaymentSettingsAPI(clubID) {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function addClubSaasBillingPaymentMethodAPI(clubID, data) {
    try {
        const response = await $.ajax({
            method: 'POST',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings/payment-methods`,
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function updateClubSaasBillingPaymentMethodAPI(clubID, cardID, data) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings/payment-methods/${cardID}`,
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function deleteClubSaasBillingPaymentMethodAPI(clubID, paymentMethodID) {
    try {
        const response = await $.ajax({
            method: 'DELETE',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings/payment-methods/${paymentMethodID}`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function setPMAsPrimaryAPI(clubID, paymentMethodID) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings/payment-methods/${paymentMethodID}/primary`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function getEarliestBilledMonthAPI() {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/saas-billing/payment-settings/earliest-billed-month`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function updateBillingProfileAPI(clubID, data) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings/billing-profile`,
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function syncClubPaymentMethodsAPI(clubID) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings/payment-methods`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function listClubFlaggedRecordsAPI(clubID) {
    try {
        const response = await $.ajax({
            method: 'GET',
            url: `/api/clubs/${clubID}/saas-billing/flagged`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function retryClubFailedPaymentsAPI(clubID) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/retry-failed-payments`,
            contentType: 'application/json',
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}

async function updateClubSaasBillingPaymentSettings(clubID, data) {
    try {
        const response = await $.ajax({
            method: 'PUT',
            url: `/api/clubs/${clubID}/saas-billing/payment-settings`,
            contentType: 'application/json',
            data: JSON.stringify(data),
        });

        return [true, response];
    } catch (err) {
        console.error(err.responseJSON?.message ?? err);
        return [false, err.responseJSON?.message ?? 'Something went wrong'];
    }
}
