<section id="saas-billing-admin" class="content full-height-content is-relative">
    <div class="container-fluid d-flex flex-column">
        <div class="page-notifications"></div>

        <ul class="nav nav-tabs tab-nav-right" role="tablist">
            <li role="presentation">
                <a href="#charges-config" data-toggle="tab" style="width: max-content;">{{Charges Configuration}}</a>
            </li>
            <li role="presentation">
                <a href="#stripe-customer-mapping" data-toggle="tab" style="width: max-content;">{{Club Configuration}}</a>
            </li>
            <li role="presentation">
                <a href="#invoice-history" data-toggle="tab" style="width: max-content;">
                    {{Invoice History}}
                </a>
            </li>
            <li role="presentation" style="margin-left: auto;">
                <a href="#billing-imports" data-toggle="tab" style="width: max-content;">
                    <i class="fa fa-upload"></i> {{Import Billing Records}}
                </a>
            </li>
        </ul>

        <!-- Tab panes -->
        <div class="tab-content grow-1">
            <div role="tabpanel" class="tab-pane in active" id="charges-config">
                <div class="search-nav-container mb-1"></div>
                <legend style="font-size: 16px;">
                    {{Per Product Charges}}
                    <br><small>
                        {{These are calculated automatically as part of the core SaaS features, and can be configured per Region, Club, or Globally.}}
                    </small>
                </legend>
                <div class="blocks-container saas-blocks-container mb-2"></div>
                <legend class="mb-1" style="font-size: 16px;">
                    {{Base Charges}}
                    <br><small>
                        {{These are additional line items that are added to the monthly invoice (either recurring or as a one off charge) that are not calculated automatically by the SaaS usage.}}
                    </small>
                </legend>

                <div class="d-flex gap-1 justify-between">
                    <div class="filters-container d-flex gap-1"></div>
                    <div class="filters-right d-flex gap-1">
                        <div class="filter-grp use-skeleton charges-configuration-filters-right-container"></div>
                        <div class="filter-grp use-skeleton">
                            <p class="filter-label">{{Date Effective}}</p>
                            <span class="filter-grp__divider"></span>
                            <div class="filter"><button type="button"
                                    class="filter__selected date-effective-filter">{{2022/01/01}} - {{2022/12/31}}</button></div>
                        </div>
                    </div>
                </div>
                <table id="additional-charges-table" class="table table-hover dataTable" style="width: 100%;">
                    <thead>
                        <tr>
                            <th></th>
                            <th>{{Date Effective}}</th>
                            <th>{{Frequency}}</th>
                            <th>{{Target}}</th>
                            <th>{{Description}}</th>
                            <th>{{Total}}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="stripe-customer-mapping">
                <div class="search-nav-container mb-1"></div>
                <table id="stripe-club-settings-table" class="table table-hover dataTable" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="sorting" rowspan="1" colspan="1">{{Club}}</th>
                            <th class="sorting" rowspan="1" colspan="1">{{Customer ID}}</th>
                            <th class="sorting" rowspan="1" colspan="1">{{Primary Payment Method}}</th>
                            <th class="sorting" rowspan="1" colspan="1">{{Automatic Billing}}</th>
                            <th class="sorting" rowspan="1" colspan="1">{{Locked Out}}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tbody id="stripe-club-settings-loader" class="isLoading">
                    </tbody>
                </table>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="invoice-history">
                <div class="search-nav-container mb-1"></div>
                <div class="invoice-filters d-flex gap-1 justify-between flex-wrap">
                    <div class="invoice-filters-left d-flex gap-1 flex-wrap"></div>
                    <div class="invoice-filters-right">

                        <div class="table-control-wrapper">
                            <div class="invoice-filters-right-container"></div>
                            <button class="btn btn-primary retry-all-failed">
                                <i class="fa fa-redo"></i> {{Re-process Failed Payments}}
                            </button>
                        </div>

                    </div>
                </div>
                <table id="saas-billing-invoices-table" class="table table-hover dataTable" style="width: 100%;">
                    <thead>
                        <tr>
                            <th>{{ID}}</th>
                            <th>{{Billing Cycle}}</th>
                            <th>{{Club}}</th>
                            <th>{{Country}}</th>
                            <th>{{Last Attempt}}</th>
                            <th>{{Attempts}}</th>
                            <th>{{Payments}}</th>
                            <th>{{Amount}}</th>
                            <th>{{Status}}</th>
                            <th>{{Invoice}}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tbody class="isLoading table-loader"></tbody>
                </table>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="billing-imports">
                <div class="search-nav-container mb-1"></div>
                <div class="imports-filters d-flex gap-1"></div>
                <table class="table table-hover dataTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Club</th>
                            <th>Feature</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<script src="https://js.stripe.com/v3/"></script>