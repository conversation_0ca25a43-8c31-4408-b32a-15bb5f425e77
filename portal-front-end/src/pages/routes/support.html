<section class="content ticketing full-height-content">
  <div id="divTicketDetailsSection" class="support-page__single-ticket isLoading">
    <div class="support-page__single-ticket__content support-page__item--full support-page__item" data-ticketnumber="487385">
      <div class="support-page__sticky-data">
        <div class="support-page__sticky-data__actions">
          <button class="support-page__item-btn" type="button" id="btnBackToAllTickets">
            <span class="use-skeleton"></span> <span class="use-skeleton">Go Back</span>
          </button>
        </div>
        <div class="support-page__item__status-section">
          <div class="support-page__item__status-section-col">
            <div class="support-page__item__status-wrapper">
              <div class="support-page__item__ticket-id use-skeleton">
                <span class="use-skeleton">Ticket #487385</span>
                <span class="use-skeleton"></span>
              </div>
              <div class="support-page__item__ticket-status use-skeleton">Status: Closed</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="support-page__item__status-section">
        <div class="support-page__item__status-section-col">
          <div class="support-page__item__title-wrapper">
            <h6 class="support-page__item__title "><span class="use-skeleton">Re: Enquiry: Membership enquiries</span></h6>
            <div class="support-page__item__meta">
              <span class="support-page__item__meta-item">
                <span class="use-skeleton"></span>
                <span class="use-skeleton">test content</span>
              </span>
              <span class="support-page__item__meta-item">
                <span class="use-skeleton"></span>
                <span class="use-skeleton">test content</span>
              </span>
              <span class="support-page__item__meta-item">
                <span class="use-skeleton"></span>
                <span class="use-skeleton">test content</span>
              </span>
            </div>
          </div>
        </div>
        <div class="support-page__item__ticket-participants-section-col text-right">
          <div class="support-page__item__ticket-participants-section__label">
            <span class="use-skeleton">
              Department: <strong>Member Support</strong>
            </span>
            <span class="use-skeleton"></span>
          </div>
          <div class="support-page__item__ticket-participants-section__label">
            <span class="use-skeleton">
              Category: <strong>General Member Support</strong>
            </span>
            <span class="use-skeleton"></span>
          </div>
        </div>
      </div>

      <div class="support-page__item__ticket-participants-section">
        <div class="support-page__item__ticket-participants-section-row">
          <div class="support-page__item__ticket-participants-section__label">
            <span class="use-skeleton"></span>
            <span class="use-skeleton">Participants</span>
          </div>
          <div class="support-page__item__ticket-participants-wrapper">
            <span class="support-page__item__ticket-badge use-skeleton">
              Test Content
            </span>

            <span data-toggle="tooltip" data-placement="right" title="Verified Support/Staff Member" class="support-page__item__ticket-badge  support-page__item__ticket-badge--badge-staff-member use-skeleton">
              Admin User
            </span>
          </div>
        </div>
        <div class="support-page__item-actions">
          <button class="waves-effect button use-skeleton" type="button" style="width: 24px; height: 24px">
          </button>
          <button class="waves-effect button use-skeleton" type="button" style="width: 24px; height: 24px">
          </button>
        </div>
      </div>

      <div class="support-page__ticket-divider">
        
      </div>
  
      <div class="support-page__single-ticket__threads">
        <div id="divTicketDetailsBody">
          <div class="support-page__ticket-msg-item support-page__ticket-msg-item--ticketPoster">
            <div class="support-page__ticket-msg-item__meta">
              <span data-toggle="tooltip" data-placement="right" title="Verified Support/Staff Member" class="use-skeleton">Milton Gym</span>
            </div>
            <div class="header ticketThreadItem expanded use-skeleton" style="width: 300px; max-width: 100%; height: 150px !important;"></div>
          </div>
        </div>
      </div>
  
      <div class="new-support-ticket-dialog new-support-ticket-dialog--closed">
        <div class="replyContainer">
          <div class="body">
  
  
  
            <center class="re-open-ticket-container">
              <p>
                <span class="use-skeleton">This ticket has been closed.</span><br>
                <span class="use-skeleton">Please create a new ticket if you require support to an unrelated issue</span>
              </p>
              <p>
                <button type="button" class="button waves-effect use-skeleton" style="width: 200px;height: 32px;">
                </button>
              </p>
            </center>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="support-page container-fluid">
    <!-- <h1 class="support-page__heading">Support &amp; ticketing <button class="support-page__btn btn waves-effect add-new-ticket visible-md" style="background: transparent;">
      <span>{{New Support Ticket}}</span>
      <i class="material-icons">add</i>
    </button></h1> -->
    <div class="support-page__tabs-wrapper">
      <button class="support-page__btn btn waves-effect add-new-ticket visible-md" style="background: transparent; margin-bottom: 10px;">
        <span>{{New Support Ticket}}</span>
        <i class="material-icons">add</i>
      </button>
      <!-- Nav tabs -->
      <div style="overflow-x: auto; overflow-y: hidden; position: relative;">
        <ul class="nav nav-tabs tab-nav-right" id="support-page-tabs" role="tablist" style="min-width: max-content">
          <li role="presentation" class="active">
            <a href="#support-tickets" data-toggle="tab">{{Tickets}}</a>
          </li>
          <li role="presentation">
            <a href="#status-performance-hub" data-toggle="tab">{{Performance Hub Status}}
            </a>
          </li>
          <li role="presentation">
            <a href="#status-member-services" data-toggle="tab">{{Member Services Status}}
            </a>
          </li>
        </ul>
        <button class="support-page__btn btn waves-effect add-new-ticket visible-lg" style="background: transparent;">
          <span>{{New Support Ticket}}</span>
          <i class="material-icons">add</i>
        </button>
      </div>
    </div>
    <div class="support-page__body">


      <!-- Tab panes -->
      <div class="tab-content">
        <div role="tabpanel" class="tab-pane in active isRelative" id="support-tickets">
          <div class="support-page__tab-content">
            <div class="filter-section">
              <div class="filters-container isLoading" id="tickets-filter">
                <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
              </div>
              <div class="filters-container isLoading" id="tickets-sorting">
                <div class="support-page__search use-skeleton">
                  <input type="text" class="support-page__search-field" placeholder="{{Search Tickets}}">
                  <button class="support-page__search-field__clear">
                    <i class="material-icons">close</i>
                  </button>
                  <svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5Zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14Z" fill="currentColor"/></g></svg>
                </div>
                <div class="filter-grp sort-items use-skeleton" id="sort-created">
                  <p class="filter-label">{{Date Created}}</p>
                  <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.333 10.96V4.5a.5.5 0 0 0-1 0v6.46l-1.48-1.48a.5.5 0 1 0-.706.707l2.333 2.333a.5.5 0 0 0 .707 0l2.333-2.333a.502.502 0 0 0 .013-.72.5.5 0 0 0-.72.013l-1.48 1.48Zm-11-6.127a.5.5 0 0 1 .5-.5h6.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.334a.5.5 0 0 1 .5-.5H5.5a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.333a.5.5 0 0 1 .5-.5h2.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Z" fill="currentColor"/></svg>
                </div>
                <div class="filter-grp sort-items use-skeleton active" id="sort-lastreply">
                  <p class="filter-label">{{Last Reply}}</p>
                  <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.333 10.96V4.5a.5.5 0 0 0-1 0v6.46l-1.48-1.48a.5.5 0 1 0-.706.707l2.333 2.333a.5.5 0 0 0 .707 0l2.333-2.333a.502.502 0 0 0 .013-.72.5.5 0 0 0-.72.013l-1.48 1.48Zm-11-6.127a.5.5 0 0 1 .5-.5h6.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.334a.5.5 0 0 1 .5-.5H5.5a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.333a.5.5 0 0 1 .5-.5h2.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Z" fill="currentColor"/></svg>
                </div>
              </div>
            </div>
            <div class="support-page__sysmessage-wrapper">
              <div class="supportSystemsBox isLoading isCollapsed">
                <div class="supportSystemsBox__header">
                  <span class="saas-issue-icon use-skeleton"></span>
                  <h4>
                    <span class="saas-issue-text use-skeleton">{{SaaS Issue}}?</span>
                  </h4>
                  <span class="use-skeleton supportSystemsBox__collapse">
                    <svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.29 8.71 6.7 13.3a.997.997 0 0 0 1.41 1.41L12 10.83l3.88 3.88a.997.997 0 1 0 1.41-1.41L12.7 8.71a.996.996 0 0 0-1.41 0Z" fill="currentColor" opacity=".5"/></svg>
                  </span>
                </div>
                <div class="body">
                  <small class="use-skeleton">{{Please check the status of our various systems using the status links below, before
                    submitting any ticket's related to outages}}</small>
                  <div class="status-pages">
                    <ul>
                      <li class="use-skeleton"><a role="tab" aria-controls="status-performance-hub" data-target="#status-performance-hub">{{Performance Hub &
                          Club Services}}</a></li>
                      <li class="use-skeleton"><a role="tab" aria-controls="status-member-services" data-target="#status-member-services">{{Public Apps, Websites &
                          Consumer Services}}</a></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div class="support-page__tab-content__data">
              <div class="support-page__items isLoading">
                <a class="support-page__item support-page__item--Closed" data-ticketnumber="344-651-01">
                  <div class="support-page__item__status-wrapper">
                    <div class="support-page__item__ticket-id use-skeleton">Ticket #344-651-01</div>
                    <div class="support-page__item__ticket-status use-skeleton">Status: Open</div>
                  </div>
                  <div class="support-page__item__title-wrapper">
                    <h6 class="support-page__item__title use-skeleton">Test Support Ticket</h6>
                    <div class="support-page__item__meta use-skeleton"><span class="support-page__item__meta-author"><svg width="11" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M8.043 5.25c.944.275 2.243 1.205 2.243 4.252 0 1.38-1.025 2.498-2.284 2.498H2.283C1.024 12 0 10.882 0 9.502 0 6.456 1.299 5.525 2.243 5.25a3.392 3.392 0 0 1-.529-1.821A3.435 3.435 0 0 1 5.143 0 3.435 3.435 0 0 1 8.57 3.429a3.39 3.39 0 0 1-.528 1.821ZM5.143.857a2.572 2.572 0 1 0 .001 5.144A2.572 2.572 0 0 0 5.143.857Zm2.86 10.286c.783 0 1.426-.73 1.426-1.64 0-2.11-.71-3.43-2.036-3.496a3.394 3.394 0 0 1-2.25.85 3.394 3.394 0 0 1-2.25-.85C1.567 6.074.857 7.393.857 9.502c0 .911.643 1.64 1.426 1.64h5.72Z" fill="currentColor"></path></g></svg> Test Club</span> <span class="support-page__item__meta-item"><svg width="12" height="12" fill="none" xmlns="http://www.w3.org/2000/svg"><g><path d="M1.143 11.143h9.428V4.286H1.143v6.857ZM3.714 3V1.071A.212.212 0 0 0 3.5.857h-.429a.212.212 0 0 0-.214.214V3c0 .12.094.214.214.214H3.5c.12 0 .214-.094.214-.214Zm5.143 0V1.071a.212.212 0 0 0-.214-.214h-.429A.212.212 0 0 0 8 1.071V3c0 .12.094.214.214.214h.429c.12 0 .214-.094.214-.214Zm2.572-.429v8.572a.863.863 0 0 1-.858.857H1.143a.863.863 0 0 1-.857-.857V2.57c0-.468.388-.857.857-.857H2v-.643C2 .482 2.482 0 3.071 0H3.5c.59 0 1.071.482 1.071 1.071v.643h2.572v-.643C7.143.482 7.625 0 8.214 0h.429c.59 0 1.071.482 1.071 1.071v.643h.857c.47 0 .858.389.858.857Z" fill="currentColor"></path></g></svg> Mar 01, 2024 3:41AM</span></div>
                  </div>
                  <div class="support-page__item__excerpt use-skeleton">Test ParagraphTest Paragraph 2Test Paragraph 3 ...
                  </div>
                  <div class="support-page__item-details">
                    <div class="support-page__item__ticket-participants-wrapper">
                      <div class="support-page__item__ticket-message-count use-skeleton">1 <svg width="19" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.866 10.417h2.656c.628 0 1.139-.477 1.139-1.063V2.98c0-.586-.511-1.062-1.139-1.062H2.656c-.627 0-1.138.476-1.138 1.062V13.25l4.047-2.833h4.301Zm-6.83-.071.117-.011-.117.082v-.071Z" fill="currentColor"></path><path d="M15.558 6.167h-.38v4.25c0 .78-.677 1.412-1.51 1.416H6.071v.354c0 .586.511 1.063 1.139 1.063h5.44l4.046 2.833V7.23c0-.586-.51-1.062-1.138-1.062Z" fill="currentColor"></path></svg></div>
                      <div class="support-page__item__ticket-participants use-skeleton">Test Club</div>
                    </div>
                    <div class="support-page__item__ticket-departments">
                      <span class="use-skeleton"><svg width="19" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><path d="M5.036 4.75A1.21 1.21 0 0 0 3.82 3.536 1.21 1.21 0 0 0 2.607 4.75a1.21 1.21 0 0 0 1.214 1.214A1.21 1.21 0 0 0 5.036 4.75Zm10.122 5.464c0 .323-.133.636-.35.854l-4.659 4.668c-.228.218-.54.35-.863.35-.323 0-.636-.132-.854-.35L1.65 8.943C1.165 8.469.786 7.548.786 6.875V2.929c0-.665.55-1.215 1.214-1.215h3.946c.674 0 1.594.38 2.078.864l6.783 6.773c.218.228.351.54.351.863Zm3.643 0c0 .323-.133.636-.351.854l-4.658 4.668c-.228.218-.541.35-.863.35-.493 0-.74-.227-1.063-.56l4.459-4.458a1.222 1.222 0 0 0 0-1.717L9.542 2.578c-.484-.484-1.404-.864-2.078-.864H9.59c.674 0 1.594.38 2.078.864L18.45 9.35c.*************.35.863Z" fill="#D5DBE1"></path></g></svg></span>
                      <div class="support-page__item__ticket-badge use-skeleton">Accounts</div>
                      <div class="support-page__item__ticket-badge use-skeleton">Accounting Software / Fathom</div>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>

        </div>
        <div role="tabpanel" class="tab-pane in isRelative status-iframe-wrapper" id="status-performance-hub">
          <iframe data-hj-allow-iframe="" width=100% height=100%
        src="https://status.performancehub.co/status/performancehub" frameborder="0" allowfullscreen allowtransparency="true" scrolling="yes"></iframe>
        </div>
        <div role="tabpanel" class="tab-pane in isRelative status-iframe-wrapper" id="status-member-services">
          <iframe data-hj-allow-iframe="" width=100% height=100%
        src="https://status.performancehub.co/status/consumer" frameborder="0" allowfullscreen allowtransparency="true" scrolling="yes"></iframe>
        </div>
      </div>
    </div>
  </div>
</section>