<section class="content full-height-content is-relative mp-designer">

  <!--     <style>
          @import url("https://content.gymsystems.co/hub/design-assets/fonts/hub/stylesheet.css");
      </style> -->
  
      <div class="container-fluid">
  
          <div class="row clearfix">
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                  <div class="card" style="margin-bottom: 0;">
  
                      <div class="body designer">
                          <!-- Nav tabs -->
                          <div class="mp-designer__tab-wrapper">
                            <ul class="mp-designer-tabs nav nav-tabs tab-nav-right" role="tablist" style="display: flex; flex-wrap: no-wrap;">
                                <li role="presentation"  id="nav-designs-tab">
                                    <a href="#designs" data-function="designs" data-toggle="tab" aria-expanded="true">{{Files}}</a>
                                </li>
                                <li role="presentation">
                                    <a href="#exports" data-function="exports" data-toggle="tab">
                                        {{Exports}}
                                    </a>
                                </li>
                                <li role="presentation" class="hidden-xs hidden-sm hidden" id="nav-designs-search-tab">
                                  <a href="#designs-search" data-function="designs-search" data-toggle="tab">
                                      <i class="fa fa-search" aria-hidden="true"></i>
                                      {{Search Designs}}
                                  </a>
                                </li>
                            </ul>
                            <div style="display: flex; flex-wrap: no-wrap;">
                                <ul class="nav nav-tabs tab-nav-right" role="tablist" style="display: block;">
                                    <li role="presentation" class="hidden-xs hidden-sm" id="nav-asset-manager-tab">
                                        <a href="#asset_manager" data-toggle="tab">
                                            <i class="fa fa-lock" aria-hidden="true"></i>
                                            {{Asset Manager}}
                                        </a>
                                    </li>
                                    <li role="presentation" class="pull-right" id="nav-editor-tab">
                                        <a href="#editor" data-toggle="tab">
                                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                            {{Editor}}
                                        </a>
                                    </li>
                                </ul>
                                <button class="btn waves-effect mp-designer__new-design btn-border">
                                  <span class="use-skeleton rounded">{{New Design}}</span>
                                  <i class="material-icons use-skeleton rounded">add</i>
                                </button>
                                <button class="btn btn-xs btn-icon mp-designer__tab-helper" style="width: 30px; height: 30px;" role="button" data-toggle="tooltip" title="{{View Support Guides Marketing and Print in Knowledge Base}}">
                                  <img src="/assets/images/search_black_24dp.svg" />
                                </button>
                            </div>
                          </div>
  
                          <!-- Tab panes -->
                          <div class="tab-content">
                              <div role="tabpanel" class="tab-pane" id="designs">

                                <div id="designs-filter-section" class="mp-designer__filter-section isLoading">
                                  <div class="mp-designer__filter-section__contents">
                                    <div class="mp-designer__filter-and-sort">
                                      <div class="mp-designer__filter-wrapper">
                                        <div class="mp-designer__filter-label">{{Filters}}:</div>
                                        <div class="mp-designer__filters-container filters-container mp-designer__filters-container__filters" id="designs-filter">
                                          <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                                          <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                                        </div>
                                      </div>
  
                                      <div class="mp-designer__filter-wrapper mp-designer__filter-wrapper--sort">
                                        <div class="mp-designer__filter-label">{{Sort By}}:</div>
                                        <div class="mp-designer__filters-container filters-container" id="designs-sort">
                                          <!-- <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                                          <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div> -->
                                          <button type="button" class="mp-designer__sort-by mp-designer__sort-by--active use-skeleton" data-sort="date updated">
                                            <span>{{Date Updated}}</span> 
                                            <span class="mp-designer__sort-by__icon">
                                              <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.333 10.96V4.5a.5.5 0 0 0-1 0v6.46l-1.48-1.48a.5.5 0 1 0-.706.707l2.333 2.333a.5.5 0 0 0 .707 0l2.333-2.333a.5.5 0 1 0-.707-.707l-1.48 1.48Zm-11-6.127a.5.5 0 0 1 .5-.5h6.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.334a.5.5 0 0 1 .5-.5H5.5a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.333a.5.5 0 0 1 .5-.5h2.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Z" fill="currentColor"/></svg>
                                            </span>
                                          </button>
                                          <button type="button" class="mp-designer__sort-by use-skeleton" data-sort="name">
                                            <span>{{File/Folder Name}}</span> 
                                            <span class="mp-designer__sort-by__icon">
                                            </span>
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="mp-designer__view-selection">
                                      <div class="searchbar-header">
                                        <form class="designs-search-form d-flex gap-1 search-container-wrapper">
                                          <div class="search-container">
                                              <input data-hj-allow="" type="search" class="form-control search-bar use-skeleton" placeholder="{{Search Design...}}">
                                              <button type="submit" class="search-bar__submit"><i class="material-icons">search</i></button>
                                          </div>
                                        </form>
                                      </div>
                                      <button class="btn waves-effect rounded use-skeleton mp-designer__view__option" id="mp-designer__thumbnail" data-view="grid">
                                        <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 2.5h6.667v6.667H2.5V2.5Zm0 8.333h6.667V17.5H2.5v-6.667ZM10.833 2.5H17.5v6.667h-6.667V2.5Zm0 8.333H17.5V17.5h-6.667v-6.667Z" fill="currentColor"/></svg>
                                      </button>
                                      <button class="btn waves-effect rounded use-skeleton mp-designer__view__option" id="mp-designer__list" data-view="table">
                                        <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.333 8.75c-.691 0-1.25.558-1.25 1.25s.559 1.25 1.25 1.25c.692 0 1.25-.558 1.25-1.25s-.558-1.25-1.25-1.25Zm0-5c-.691 0-1.25.558-1.25 1.25s.559 1.25 1.25 1.25c.692 0 1.25-.558 1.25-1.25s-.558-1.25-1.25-1.25Zm0 10c-.691 0-1.25.567-1.25 1.25s.567 1.25 1.25 1.25A1.26 1.26 0 0 0 4.583 15c0-.683-.558-1.25-1.25-1.25Zm3.334 2.083h10A.836.836 0 0 0 17.5 15a.836.836 0 0 0-.833-.833h-10a.836.836 0 0 0-.834.833c0 .458.375.833.834.833Zm0-5h10A.836.836 0 0 0 17.5 10a.836.836 0 0 0-.833-.833h-10a.836.836 0 0 0-.834.833c0 .458.375.833.834.833ZM5.833 5c0 .458.375.833.834.833h10A.836.836 0 0 0 17.5 5a.836.836 0 0 0-.833-.833h-10A.836.836 0 0 0 5.833 5Z" fill="currentColor"/></svg>
                                      </button>
                                    </div>
                                  </div>
                                </div>
  
                                <div class="designs-nav mb-1"></div>
                                <div id="designs-list" class="mp-designer__items">
                                  <div class="mp-designer__items__thumb-view">
                                    <div class="mp-designer__divider-text divider-text">
                                      <span class="use-skeleton rounded">{{Pinned}}</span>
                                    </div>
                                    <div class="mp-designer__thumb">
                                      <span class="mp-designer__thumb__count use-skeleton rounded--full">45</span>
                                      <div class="mp-designer__thumb__images use-skeleton rounded">

                                      </div>
                                      <div class="mp-designer__thumb__details">
                                        <p class="mp-designer__thumb__details__title use-skeleton rounded">
                                          Club Essentials
                                        </p>
                                        <div class="mp-designer__thumb__details__meta mp-designer__thumb__details__meta--highlight">
                                          <span class="mp-designer__thumb__details__meta-icon use-skeleton rounded"></span>
                                          <span class="mp-designer__thumb__details__meta-text use-skeleton rounded">Meta text goes here</span>
                                        </div>
                                        <div class="mp-designer__thumb__details__tags">
                                          <span class="mp-designer__thumb__details__tag use-skeleton">Tag 1</span>
                                          <span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--not-approved use-skeleton">Tag 2</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              <div role="tabpanel" class="tab-pane fade" id="exports">
                                  <div class="row">
                                      <div class="col-sm-12">
                                          <div class="design-exports"></div>
                                      </div>
                                  </div>
                              </div>
                              
                              <div role="tabpanel" class="grapejs tab-pane fade flex-column" id="editor">
                                  <div class="editor-close-container">
                                      <button class="btn btn-xs btn-square btn-danger waves-effect btn-gjs-cancel" role="button" data-toggle="tooltip" title="{{Close design}}">
                                          <i class="material-icons">clear</i>
                                      </button>
                                  </div>
                                  <div id="gjs"></div>
                                  <div id="gjs-footer-nav">
                                      <button class="btn btn-xs btn-primary waves-effect btn-gjs-save" type="button" data-toggle="tooltip" title="{{Save Changes}}">
                                          <i class="material-icons">save</i><span>{{Save}}</span>
                                      </button>
                                      <button class="btn btn-xs btn-default btn-square waves-effect btn-gjs-docsetup" type="button" data-toggle="tooltip" title="{{Document / Print Setup}}">
                                          <i class="material-icons">select_all</i>
                                      </button>
                                  </div>
                              </div>

                              <div role="tabpanel" class="tab-pane fade" id="designs-search">

                                <div id="designs-search-filter-section" class="mp-designer__filter-section isLoading">
                                  <div class="mp-designer__filter-section__contents">
                                    <div class="mp-designer__filter-and-sort">
                                      <div class="mp-designer__filter-wrapper">
                                        <div class="mp-designer__filter-label">{{Filters}}:</div>
                                        <div class="mp-designer__filters-container filters-container mp-designer__filters-container__filters" id="designs-search-filter">
                                          <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                                        </div>
                                      </div>
  
                                      <div class="mp-designer__filter-wrapper mp-designer__filter-wrapper--sort">
                                        <div class="mp-designer__filter-label">{{Sort By}}:</div>
                                        <div class="mp-designer__filters-container filters-container">
                                          <!-- <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                                          <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div> -->
                                          <button type="button" class="mp-designer__sort-by mp-designer__sort-by--active use-skeleton" data-sort="date updated">
                                            <span>{{Date Updated}}</span> 
                                            <span class="mp-designer__sort-by__icon">
                                              <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.333 10.96V4.5a.5.5 0 0 0-1 0v6.46l-1.48-1.48a.5.5 0 1 0-.706.707l2.333 2.333a.5.5 0 0 0 .707 0l2.333-2.333a.5.5 0 1 0-.707-.707l-1.48 1.48Zm-11-6.127a.5.5 0 0 1 .5-.5h6.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.334a.5.5 0 0 1 .5-.5H5.5a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Zm0 3.333a.5.5 0 0 1 .5-.5h2.334a.5.5 0 0 1 0 1H1.833a.5.5 0 0 1-.5-.5Z" fill="currentColor"/></svg>
                                            </span>
                                          </button>
                                          <button type="button" class="mp-designer__sort-by use-skeleton" data-sort="name">
                                            <span>{{File/Folder Name}}</span> 
                                            <span class="mp-designer__sort-by__icon">
                                            </span>
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="mp-designer__view-selection">
                                      <div class="searchbar-header">
                                        <form class="designs-search-form d-flex gap-1 search-container-wrapper">
                                          <div class="search-container">
                                              <input data-hj-allow="" type="search" class="form-control search-bar use-skeleton" placeholder="{{Search Design...}}">
                                              <button type="submit" class="search-bar__submit"><i class="material-icons">search</i></button>
                                          </div>
                                        </form>
                                      </div>
                                      <button class="btn waves-effect rounded use-skeleton mp-designer__view__option" id="mp-designer__thumbnail" data-view="grid">
                                        <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 2.5h6.667v6.667H2.5V2.5Zm0 8.333h6.667V17.5H2.5v-6.667ZM10.833 2.5H17.5v6.667h-6.667V2.5Zm0 8.333H17.5V17.5h-6.667v-6.667Z" fill="currentColor"/></svg>
                                      </button>
                                      <button class="btn waves-effect rounded use-skeleton mp-designer__view__option" id="mp-designer__list" data-view="table">
                                        <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.333 8.75c-.691 0-1.25.558-1.25 1.25s.559 1.25 1.25 1.25c.692 0 1.25-.558 1.25-1.25s-.558-1.25-1.25-1.25Zm0-5c-.691 0-1.25.558-1.25 1.25s.559 1.25 1.25 1.25c.692 0 1.25-.558 1.25-1.25s-.558-1.25-1.25-1.25Zm0 10c-.691 0-1.25.567-1.25 1.25s.567 1.25 1.25 1.25A1.26 1.26 0 0 0 4.583 15c0-.683-.558-1.25-1.25-1.25Zm3.334 2.083h10A.836.836 0 0 0 17.5 15a.836.836 0 0 0-.833-.833h-10a.836.836 0 0 0-.834.833c0 .458.375.833.834.833Zm0-5h10A.836.836 0 0 0 17.5 10a.836.836 0 0 0-.833-.833h-10a.836.836 0 0 0-.834.833c0 .458.375.833.834.833ZM5.833 5c0 .458.375.833.834.833h10A.836.836 0 0 0 17.5 5a.836.836 0 0 0-.833-.833h-10A.836.836 0 0 0 5.833 5Z" fill="currentColor"/></svg>
                                      </button>
                                    </div>
                                  </div>
                                </div>
  
                                <div class="designs-nav mb-1"></div>
                                <div id="designs-search-results" class="mp-designer__items isLoading">
                                  <div class="mp-designer__items__thumb-view">
                                    <div class="mp-designer__divider-text divider-text">
                                      <span class="use-skeleton rounded">{{Pinned}}</span>
                                    </div>
                                    <div class="mp-designer__thumb">
                                      <span class="mp-designer__thumb__count use-skeleton rounded--full">45</span>
                                      <div class="mp-designer__thumb__images use-skeleton rounded">

                                      </div>
                                      <div class="mp-designer__thumb__details">
                                        <p class="mp-designer__thumb__details__title use-skeleton rounded">
                                          Club Essentials
                                        </p>
                                        <div class="mp-designer__thumb__details__meta mp-designer__thumb__details__meta--highlight">
                                          <span class="mp-designer__thumb__details__meta-icon use-skeleton rounded"></span>
                                          <span class="mp-designer__thumb__details__meta-text use-skeleton rounded">Meta text goes here</span>
                                        </div>
                                        <div class="mp-designer__thumb__details__tags">
                                          <span class="mp-designer__thumb__details__tag use-skeleton">Tag 1</span>
                                          <span class="mp-designer__thumb__details__tag mp-designer__thumb__details__tag--not-approved use-skeleton">Tag 2</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
  
                              <!--
                              <div role="tabpanel" class="tab-pane assets fade" id="assets">
                                  <h2 class="card-inside-title">Digital Assets &amp; Brand Guidelines
                                      <small>
                                          <p>
                                              Here you'll find a collection of assets ranging from Logos to Colours to Fonts that are easy to download, and share. When downloading and using assets, ensure you've read the associated documentation and/or comments and guidelines to be sure you're adhering to the 12RND and UBX brands. For a more detailed perspective on the brand, please download the full Brand Guidelines.
                                          </p>
                                          <p>
                                              Don't see what you're looking for? Get in touch with HQ and we'll point you in the right direction!
                                          </p>
                                      </small>
                                  </h2>
  
                                  <a href="/assets/documents/12RND & UBX - Brand Guidelines.pdf" target="_blank" class="btn btn-primary" style="margin: 10px;">Download Brand Guidelines</a>
  
                                  <a class="btn btn-default waves-effect btn-contact-hq">Or contact HQ for support</a>
  
                                  <hr>
                              -->
  
                              <!--
                                  <h2 class="card-inside-title">Logos</h2>
  
                                  <div class="row assets-details">
                                      <div class="col-sm-6 col-md-4 col-lg-3 col-xl-2">
                                          <div class="thumbnail">
                                              <img src="/assets/images/brand_logos/ubx_primary_logo_preview.jpg">
                                              <div class="caption">
                                                  <h3>UBX Primary Logo</h3>
                                                  <ul>
                                                      <li>Minimum size 25mm wide</li>
                                                      <li>Do not use on dark backgrounds</li>
                                                      <li>Prefer SVG use where possible</li>
                                                  </ul>
                                                  <div class="downloads">
                                                      <h4>Download as:</h4>
                                                      <p>
                                                          <a href="/assets/documents/brand_logos/ubx_primary_logo/UBX_PRIMARY.svg" download class="btn btn-primary waves-effect btn-xs" role="button">SVG</a>
                                                          <a href="/assets/documents/brand_logos/ubx_primary_logo/UBX_PRIMARY.png" download class="btn btn-default waves-effect btn-xs" role="button">PNG</a>
                                                          <a href="/assets/documents/brand_logos/ubx_primary_logo/UBX_PRIMARY.jpg" download class="btn btn-default waves-effect btn-xs" role="button">JPG</a>
                                                          <a href="/assets/documents/brand_logos/ubx_primary_logo/UBX_PRIMARY.eps" download class="btn btn-default waves-effect btn-xs" role="button">EPS</a>
                                                      </p>
                                                  </div>
                                              </div>
                                          </div>
                                      </div>
  
                                      <div class="col-sm-6 col-md-4 col-lg-3 col-xl-2">
                                          <div class="thumbnail">
                                              <img src="/assets/images/brand_logos/ubx_monochrome_light_logo_preview.jpg">
                                              <div class="caption">
                                                  <h3>UBX Light Logo</h3>
                                                  <ul>
                                                      <li>Minimum size 25mm wide</li>
                                                      <li>Do not use on light backgrounds</li>
                                                  </ul>
                                                  <div class="downloads">
                                                      <h4>Download as:</h4>
                                                      <p>
                                                          <a href="/assets/documents/brand_logos/ubx_primary_logo/UBX_MONOCHROME_LIGHT.svg" download class="btn btn-primary waves-effect btn-xs" role="button">SVG</a>
                                                          <a href="/assets/documents/brand_logos/ubx_primary_logo/UBX_MONOCHROME_LIGHT.png" download class="btn btn-default waves-effect btn-xs" role="button">PNG</a>
                                                      </p>
                                                  </div>
                                              </div>
                                          </div>
                                      </div>
                                  </div>
                                  <hr>
                                  <h2 class="card-inside-title">Colours</h2>
                                  <div class="row colours-details">
  
                                      <div class="col-xs-6 col-sm-4 col-md-3 col-lg-3 col-xl-2">
                                          <div class="demo-color-box bg-black">
                                              <div class="color-name">BLACK</div>
                                              <div class="color-code">#000000</div>
                                              <div class="color-class-name">
                                                  rgb(0, 0, 0)<br>
                                                  cmyk(0%, 0%, 0%, 100%)
                                              </div>
                                          </div>
                                      </div>
                                      <div class="col-xs-6 col-sm-4 col-md-3 col-lg-3 col-xl-2">
                                          <div class="demo-color-box bg-white">
                                              <div class="color-name">WHITE</div>
                                              <div class="color-code">#FFFFFF</div>
                                              <div class="color-class-name">
                                                  rgb(255, 255, 255)<br>
                                                  cmyk(0%, 0%, 0%, 0%)
                                              </div>
                                          </div>
                                      </div>
                                      <div class="col-xs-6 col-sm-4 col-md-3 col-lg-3 col-xl-2">
                                          <div class="demo-color-box bg-dark-grey">
                                              <div class="color-name">DARK GREY</div>
                                              <div class="color-code">#999999</div>
                                              <div class="color-class-name">
                                                  rgb(153, 153, 153)<br>
                                                  cmyk(0%, 0%, 0%, 40%)
                                              </div>
                                          </div>
                                      </div>
                                      <div class="col-xs-6 col-sm-4 col-md-3 col-lg-3 col-xl-2">
                                          <div class="demo-color-box bg-logo-green">
                                              <div class="color-name">HIGHLIGHT GREEN</div>
                                              <div class="color-code">#73B944</div>
                                              <div class="color-class-name">
                                                  rgb(115, 185, 68)<br>
                                                  cmyk(38%, 0%, 63%, 27%)
                                              </div>
                                          </div>
                                      </div>
                                  </div>
                                  <hr>
                                  <h2 class="card-inside-title">Fonts</h2>
                                  <div class="row fonts-details">
                                      <div class="col-xs-12">
                                          <h3 class="gotham-bold">GOTHAM</h3>
                                          <a href="/assets/documents/fonts/gotham.zip" target="_blank" class="btn btn-primary">Download Font Files</a>
  
                                          <div class="font-inspector">
  
                                              <input id="file" type="file">
                                              <span class="info" id="font-name">Gotham-Bold</span>
                                              <div id="message"></div>
  
                                              <div>
                                                  Glyphs <span id="pagination"></span>
                                                  <br>
                                                  <div id="glyph-list-end"></div>
                                              </div>
                                              <div style="position: relative">
                                                  <div id="glyph-display">
                                                      <canvas id="glyph-bg" width="500" height="500"></canvas>
                                                      <canvas id="glyph" width="500" height="500"></canvas>
                                                  </div>
                                                  <div id="glyph-data"></div>
                                                  <div style="clear: both"></div>
                                              </div>
                                              <hr>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                              -->
  
                              <div role="tabpanel" class="tab-pane assets fade" id="asset_manager">
                                  <div id="asset_manager-upload"></div>
                                  <div id="asset_manager-list"></div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
  