<div class="notifications-page">
  <section class="content">
    <div class="container-fluid">
      <div class="block-header">
        <div class="d-flex gap-4 notif-header-container">
          <div class="notif-heading">{{NOTIFICATIONS}}</div>
          <div style="flex-grow: 1;"><hr /></div>
          <div class="notif-settings">
            <button onclick="notificationSettings();">
              <span class="notif-settings-text">{{Settings}}</span>
              <span class="notif-settings-icon"><i class="fa fa-cog" aria-hidden="true"></i></span>
            </button>
          </div>
        </div>
      </div>

      <div class="notifs-container">
        <div class="grow-1">
          <div class="card">
            <div class="notifications-list-container">
              <div id="notifications-list" class="notifications-list" style="min-height: 100px;"></div>
            </div>
          </div>
        </div>

        <div class="notification-filters">
          <div class="menu">
            <ul class="list">
              <li>
                <a id="notif-filter-daterange" href="javascript:void(0);" class="menu-toggle waves-effect waves-block" data-tooltip-navitem="tooltip" title="Date Filter">
                  <i class="material-icons">calendar_today</i>
                  <span></span>
                </a>
              </li>
              <li>
                <a href="javascript:void(0);" class="notif-menu-list-item menu-toggle waves-effect waves-block" data-tooltip-navitem="tooltip" title="Category">
                  <span>{{Category}}</span>
                </a>
                <ul id="notif-category-filter" class="notif-sub-menu ml-menu collapse"></ul>
              </li>
              <li>
                <a href="javascript:void(0);" class="notif-menu-list-item menu-toggle waves-effect waves-block" data-tooltip-navitem="tooltip" title="Viwed By">
                  <span>{{Viewed By}}</span>
                </a>
                <ul id="notif-viewed-by-filter" class="notif-sub-menu ml-menu collapse"></ul>
              </li>
              <li id="notif-read-filters">
                <button class="btn btn-primary waves-effect" data-value="all">{{All}}</button>
                <button class="btn btn-default waves-effect" data-value="unread">{{Unread}}</button>
                <button class="btn btn-default waves-effect" data-value="opened">{{Opened}}</button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>