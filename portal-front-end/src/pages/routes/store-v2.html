<section class="full-height-content content is-relative" id="storeV2">
  <div class="container-fluid">
    <div class="store-v2-sl isLoading">
      <div class="store-v2-sl__nav">
        <div class="store-v2-sl__nav__item use-skeleton rounded"></div>
        <div class="store-v2-sl__nav__item use-skeleton rounded"></div>
        <div class="store-v2-sl__nav__item use-skeleton rounded"></div>
      </div>
      <div class="store-v2-sl__main">
        <div class="store-v2-sl__products">
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
          <div class="store-v2-sl__product">
            <div class="store-v2-sl__product__image use-skeleton"></div>
            <div class="store-v2-sl__product__name use-skeleton"></div>
            <div class="store-v2-sl__product__price use-skeleton"></div>
            <div class="store-v2-sl__product__btn use-skeleton"></div>
          </div>
        </div>
        <div class="store-v2-sl__sidebar">
          <div class="store-v2-sl__search use-skeleton rounded"></div>
          <div class="store-v2-sl__range">
            <div class="store-v2-sl__range__label use-skeleton rounded"></div>
            <div class="store-v2-sl__range-bar">
              <div class="store-v2-sl__range-bar__indicator use-skeleton rounded--full"></div>
              <div class="store-v2-sl__range-bar__progress use-skeleton"></div>
              <div class="store-v2-sl__range-bar__indicator use-skeleton rounded--full"></div>
            </div>
          </div>
          <div class="store-v2-sl__range__label-2 use-skeleton rounded"></div>
  
          <div class="store-v2-sl__filter__label use-skeleton rounded"></div>
          <div class="store-v2-sl__filter">
            <div class="store-v2-sl__filter__check use-skeleton rounded--full"></div>
            <div class="store-v2-sl__filter__text use-skeleton rounded"></div>
          </div>
          <div class="store-v2-sl__filter">
            <div class="store-v2-sl__filter__check use-skeleton rounded--full"></div>
            <div class="store-v2-sl__filter__text use-skeleton rounded"></div>
          </div>
          <div class="store-v2-sl__filter">
            <div class="store-v2-sl__filter__check use-skeleton rounded--full"></div>
            <div class="store-v2-sl__filter__text use-skeleton rounded"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="card noboxshadow iframe-placeholder">
      <iframe data-hj-allow-iframe="" onload="sStoreURI()" id="storeContainer" name="storeContainer" width="100%"
        height="100%" src="" frameborder="0" allowfullscreen allowtransparency="true" scrolling="yes"></iframe>
    </div>
  </div>
</section>
<div id="modalBackdrop"></div>