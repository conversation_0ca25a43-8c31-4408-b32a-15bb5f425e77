<section class="content dashboard">
    <div class="container-fluid member-container">
        
        <div class="dashboard-alerts-banner"></div>
        <div id="dashboardSelectWizard"></div>
        <div id="dashboardSelectMoreWizard"></div>

        <div class="d-flex gap-1 align-end justify-between" style="margin-bottom: 10px;">
            <div class="filter-section dashboard-section" style="padding-bottom: 0px;">
                <div class="filter-grp" id="filter-dashboard-range">
                    <p class="filter-label">{{Reporting Range}}:</p>
                    <span class="filter-grp__divider"></span>
                    <div class="filter">
                        <button type="button" class="filter__selected">{{Fortnight}}</button>
                        <ul class="filter__options" data-filter="reportingRange">
                            <li
                                class="filter__option filter__option--active"
                                data-value="14"
                            >
                                {{Fortnight}}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>


            <a
                href="/wiki/#%2Fopenkb%2Fkb%2FmRl2HuIeU5mlBeRG"
                class="btn btn-outline normal-case waves-effect use-skeleton social-media-calendar-btn"
                style="height: 28px; padding: 0px 40px;"
            >
                {{Social Media Calendar}}
            </a>
        </div>

        <div class="ph-the-info-box__wrapper the-info-box__wrapper">
            <div class="container-gm-api ph-the-info-box__row the-info-box__row">
                <!-- Info boxes will be dynamically generated by JS (renderDashboardInfoBoxes) -->
            </div>
        </div>

        <div class="dashboard-section ph-dashboard-section revenue__section">
            <div class="revenue__wrapper">
                <div class="gm-api-revenue ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--lg">
                        <h2 class="use-skeleton rounded">
                            {{Revenue History}}
                            <i 
                                class="the-info-box__info"
                                data-toggle="tooltip"
                                data-placement="bottom"
                                title="{{Breakdown of your revenue (relative to the time/period selected)}}"
                                >
                                <span class="use-skeleton use-skeleton--absolute rounded--full"></span>
                            </i>
                        </h2>
                        <div class="the-info-box__header__btn-grp">
                            <a
                                href="#"
                                class="btn btn-outline btn-danger normal-case waves-effect btn-failed-payments use-skeleton"
                            >
                                    <span class="btn__badge btn__badge--failed failed-count"></span>
                                    <span>{{View failed payments in the last 14 days}}</span>
                            </a>
                            <a
                                href="/member-payments"
                                class="btn btn-outline normal-case waves-effect btn-revenue-history use-skeleton"
                            >
                                    <i class="btn__icon custom-icon payment-icon" aria-hidden="true"></i>
                                    <span>&nbsp;{{Member Payments}} &amp; {{Royalties}}</span>
                            </a>
                        </div>
                    </div>
                    <div class="the-info-box__body revenue-chart-section">
                       
                        <div class="the-info-box__chart-section">
                            <div class="the-info-box__chart-wrapper--labels">
                                <div class="the-info-box__chart-wrapper the-info-box__chart-wrapper use-skeleton">
                                    <canvas id="revenue_chart_labels" class="graph-chart">charts js</canvas>
                                </div>
                            </div>
                            <div class="the-info-box__chart-wrapper use-skeleton">
                                <canvas id="revenue_chart" class="graph-chart">charts js</canvas>
                            </div>
                        </div>

                        <div class="revenue-outlook">
                            <h2 class="heading-w-badge">
                                <span class="use-skeleton rounded">{{Outlook}}</span> <span class="reporting-period custom-badge custom-badge--success display-period use-skeleton rounded"></span>
                            </h2>
                            
                            <dl class="revenue-stats">
                                <div class="revenue-stat">
                                    <dt class="use-skeleton rounded">{{TOTAL PAYMENTS}}</dt>
                                    <dd>
                                        <span id="totalPayments" class="use-skeleton rounded"></span>
                                    </dd>
                                </div>

                                <div class="revenue-stat">
                                    <dt class="use-skeleton rounded">{{AVG WEEKLY PAYOUT}}</dt>
                                    <dd>
                                        <span id="totalPaymentsAvg" class="use-skeleton rounded"></span>
                                    </dd>
                                </div>

                                <div class="revenue-stat">
                                    <dt class="use-skeleton rounded">
                                        <span>{{AVERAGE WEEKLY YIELD}}</span>
                                        <i
                                            class="material-icons info-icon"
                                            data-toggle="tooltip"
                                            data-placement="bottom"
                                            title="{{This shows the average weekly membership price of your weekly, fortnightly, or monthly billed members}}"
                                            >info_outline</i
                                        >
                                    </dt>
                                    <dd>
                                        <span id="avgYield" class="use-skeleton rounded inline-block"></span>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bookings__wrapper isLoading">
                <div class="upcoming-bookings-widget ph-the-info-box the-info-box the-info-box--lg ">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Upcoming Bookings}}</span>
                                    <span class="reporting-period btn__badge btn__badge--sm btn__badge--default use-skeleton">{{7 Days}}</span>
                                </h3>
                                <a
                                    href="/workout-booking-leads"
                                    class="btn btn-border btn--mini waves-effect normal-case use-skeleton rounded"
                                    >{{View All}}
                                </a>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="bottom" title="{{An overview of the number of upcoming workout bookings for the next 7 days.}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                        
                    </div>
                    <div class="the-info-box__body body">
                        <div class="upcoming-bookings-widget-chart use-skeleton rounded">
                            <canvas id="upcoming_bookings_chart" class="graph-chart" height="150">charts js</canvas>
                        </div>
                        <div class="bookings-nav-section use-skeleton rounded">
                            <button type="button" class="bookings-nav" id="bookings-prev" data-direction="prev">
                                <span class="sr-only">{{previous day booking}}</span>
                                <i class="fa fa-angle-left" aria-hidden="true"></i>
                            </button>
                            <ul id="todays-bookings" class="list-group list-group-flush"></ul>
                            <button type="button" class="bookings-nav" id="bookings-next" data-direction="next">
                                <span class="sr-only">{{next day booking}}</span>
                                <i class="fa fa-angle-right" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="review-management__wrapper isLoading">
                <div class="social-overview ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Review Management}}</span>
                                    <span class="reporting-period btn__badge btn__badge--sm btn__badge--default use-skeleton">{{Last 12 months}}</span>
                                </h3>
                                <a
                                    href="/review-management"
                                    class="btn btn-border btn--mini waves-effect normal-case use-skeleton rounded"
                                    >{{View All}}
                                </a>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="bottom" title="{{This shows your average rating and number of reviews for your digital profile}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                        
                    </div>
                    <div class="the-info-box__body body">
                        <div class="reviews-ratings-graph use-skeleton rounded">
                            <canvas id="reviews-ratings-graph" class="bar"></canvas>
                        </div>
                        <div class="review-management__content body">
                            <div>
                                <ul class="dashboard-stat-list reviews-summary use-skeleton rounded"></ul>
                                <div class="reviews-without-responses use-skeleton rounded"></div>
                            </div>
                            <div class="review-management__tags-section__wrapper rounded">
                                <div class="review-management__tags-section">
                                    <h5 class="use-skeleton rounded">{{What members are talking about}}</h5>
                                    <div class="reviews-tag-cloud use-skeleton rounded"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-section membership-peak-time__section">
            <div class="lead-events__wrapper container-gm-api w isLoading">
                <div class="lead-events-widget ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Key Website Lead Events}}</span>
                                    <span class="reporting-period custom-badge display-period use-skeleton rounded">1 day</span>
                                </h3>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="bottom" title="{{The number of 'leads' that have been sent to your club (either via Email, or to your MMS) from our website's pages (such as free trial landing pages, or clicking the 'free trial' button on your club's page, etc).}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                    </div>
                    <div class="the-info-box__body body">
                        <div class="lead-events__content">
                            <div class="lead-events-data__wrapper">
                                <div class="lead-events-data__chart use-skeleton rounded-full">
                                    <canvas id="lead-events-data-chart"></canvas>
                                </div>
                                <div class="lead-events-data">
                                    <p class="lead-events-data__title use-skeleton rounded">{{lead events}}</p>
                                    <p class="lead-events-data__count use-skeleton rounded">100,000</p>

                                    <ul class="lead-events-data__values">
                                        <li class="lead-events-data__values-grp">
                                            <span class="lead-events-data__values-grp__pill use-skeleton rounded--full">16.2%</span>
                                            <span class="lead-events-data__values-grp__label use-skeleton rounded">{{Select Content}}</span>
                                            <strong class="use-skeleton rounded">12</strong>
                                        </li>
                                        <li class="lead-events-data__values-grp">
                                            <span class="lead-events-data__values-grp__pill use-skeleton rounded--full">32.4%</span>
                                            <span class="lead-events-data__values-grp__label use-skeleton rounded">{{Online Joint Start}}</span>
                                            <strong class="use-skeleton rounded">24</strong>
                                        </li>
                                        <li class="lead-events-data__values-grp">
                                            <span class="lead-events-data__values-grp__pill use-skeleton rounded--full">33.8%</span>
                                            <span class="lead-events-data__values-grp__label use-skeleton rounded">{{Lead Booking}}</span>
                                            <strong class="use-skeleton rounded">25</strong>
                                        </li>
                                    </ul>

                                    <a
                                        href="/club-analytics"
                                        class="btn btn-outline normal-case waves-effect use-skeleton"
                                    >
                                        <i class="btn__icon custom-icon chart-icon" aria-hidden="true"></i>
                                        <span>{{View Analytics}}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="smart-club__wrapper container-gm-api w isLoading">
                <div class="smart-club-widget w ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Smart Technology}}</span>
                                </h3>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="bottom" title="{{Overview of IoT / Smart Devices in your Club and the states of the various devices and environment.}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                    </div>
                    <div class="the-info-box__body body">
                        <div class="smart-club__content">
                            <ul class="smart-club__values">
                                <li>
                                    <a href="#">
                                        <span class="smart-club__values-icon__wrapper use-skeleton rounded">
                                            <i class="smart-club__values-icon custom-icon screen-icon"></i>
                                        </span>
                                        <div>
                                            <p class="smart-club__label"><span class="use-skeleton rounded">{{Coaching Screens}}</span></p>
                                            <p class="smart-club__value">
                                                <span id="screens-online" class="use-skeleton rounded">{{Devices Online}}</span>
                                            </p>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <span class="smart-club__values-icon__wrapper use-skeleton">
                                            <i class="smart-club__values-icon custom-icon audio-icon"></i>
                                        </span>
                                        <div>
                                            <p class="smart-club__label use-skeleton rounded">{{Audio Control}}</p>
                                            <p class="smart-club__value smart-club__value--fatal" >
                                                <span id="audio-device-count" class="use-skeleton rounded">{{No Devices Configured}}</span>
                                            </p>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <span class="smart-club__values-icon__wrapper use-skeleton rounded">
                                            <i class="smart-club__values-icon custom-icon duress-icon"></i>
                                        </span>
                                        <div>
                                            <p class="smart-club__label"><span class="use-skeleton rounded">{{Duress Buttons}}</span></p>
                                            <p class="smart-club__value" >
                                                <span id="panic-btn-count" class="use-skeleton rounded">{{buttons}}</span>
                                                <span class="custom-badge custom-badge--fatal normal-case use-skeleton" id="panic-btn-recharge"></span>
                                            </p>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <span class="smart-club__values-icon__wrapper use-skeleton rounded">
                                            <i class="smart-club__values-icon custom-icon myzone-icon"></i>
                                        </span>
                                        <div>
                                            <p class="smart-club__label use-skeleton rounded">{{MyZone}}</p>
                                            <p class="smart-club__value">
                                                <span id="myzone-active-user" class="use-skeleton rounded">{{Active Users}}</span>
                                            </p>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <span class="smart-club__values-icon__wrapper use-skeleton rounded">
                                            <i class="smart-club__values-icon custom-icon cctv-icon"></i>
                                        </span>
                                        <div>
                                            <p class="smart-club__label"><span class="use-skeleton rounded">{{CCTV}}</span></p>
                                            <p class="smart-club__value " >
                                                <span id="cameras-online" class="use-skeleton rounded">{{Cameras Online}}</span>
                                            </p>
                                            <p class="smart-club__size" style="font-size: 12px;">
                                                <span class="smart-club__size-value use-skeleton rounded" id="camera-storage-consumed">***** ***** ***</span> 
                                            </p>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <span class="smart-club__values-icon__wrapper use-skeleton rounded">
                                            <i class="smart-club__values-icon custom-icon temp-icon"></i>
                                        </span>
                                        <div>
                                            <p class="smart-club__label use-skeleton rounded">{{Facility temperature}}</p>
                                            <p class="smart-club__value">
                                                <span id="club-temperature" class="use-skeleton rounded">{{loading}}</span>
                                            </p>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="members-split__wrapper isLoading">
                <div class="members-split-widget ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Membership Split}}</span>
                                </h3>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="right" title="{{Number of male vs female members currently in your club.}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                    </div>
                    <div class="the-info-box__body body">
                        <div class="members-split__content">
                            <div id="member_split_legend"></div>
                            <div class="members-split-chart__wrapper use-skeleton rounded--full">
                                <div class="members-split-chart">
                                    <canvas id="member_split_chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="checkin-history__wrapper container-gm-api w">
                <div class="checkin-history-widget ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Check-in History}}</span>
                                </h3>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="bottom" title="{{The number of 'leads' that have been sent to your club (either via Email, or to your MMS) from our website's pages (such as free trial landing pages, or clicking the 'free trial' button on your club's page, etc).}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                    </div>
                    <div class="the-info-box__body body">
                        <div class="checkin-history__content">
                            <div class="checkin-history__table-wrapper">
                                <table class="checkin-history__table">
                                    <thead>
                                        <tr>
                                            <th>
                                                <span class="use-skeleton rounded">{{Name}}</span>
                                            </th>
                                            <th class="visible-sm visible-lg">
                                                <span class="use-skeleton rounded">{{Type}}</span>
                                            </th>
                                            <th class="hidden-xs text-right">
                                                <span class="use-skeleton rounded">{{Check-in Date/Time}}</span>
                                            </th>
                                            <th class="hidden-xs">
                                                <span class="use-skeleton rounded">{{Status}}</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <!-- clones -->
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge use-skeleton rounded">
                                                        {{extended hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge use-skeleton rounded">{{extended hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--fatal use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--denied use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Denied}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td>
                                                <p>
                                                    <span class="use-skeleton rounded">
                                                        {{John Doe}}
                                                    </span>
                                                </p>
                                                <p class="hidden visible-xs visible-md">
                                                    <span class="custom-badge custom-badge--success use-skeleton rounded">
                                                        {{club hours}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="visible-sm visible-lg">
                                                <p class="hidden-xs"><span class="custom-badge custom-badge--success use-skeleton rounded">{{club hours}}</span></p>
                                            </td>
                                            <td class="text-right">
                                                <span class="access-time use-skeleton">
                                                    10/07 | 8:00 {{AM}}
                                                </span>
                                                <p class="hidden visible-xs">
                                                    <span class="custom-badge custom-badge--bordered custom-badge--success use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                            <td class="hidden-xs">
                                                <p class="access-status">
                                                    <span class="access-status__indicator access-status__indicator--online use-skeleton rounded--full"></span>
                                                    <span class="use-skeleton">
                                                        {{Allowed}}
                                                    </span>
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="checkin-history__btn-wrapper">
                                <a href="/club-kyc" target="_blank" class="btn btn-primary custom-alert__btn waves-effect use-skeleton" id="checkin-history-link">{{View More}}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="peak-time__wrapper">
                <div class="peak-time-widget ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__header the-info-box__header--bordered">
                        <div class="the-info-box__header__content">
                            <div class="the-info-box__header__content-col-left">
                                <h3>
                                    <span class="use-skeleton rounded">{{Peak Time Insights}}</span>
                                </h3>
                            </div>
                            <div class="the-info-box__header__content-col-right">
                                <i class="the-info-box__info the-info-box__info--relative" data-toggle="tooltip" data-placement="bottom" title="{{This data is pulled from multiple sources, such as Google's Maps API and our own Member Check-in data to provide the most accurate representation possible.}}"><span class="use-skeleton use-skeleton--absolute rounded--full"></span></i>
                            </div>
                        </div>
                    </div>
                    <div class="the-info-box__body body">
                        <div id="popular-times-tab" class="popular-times-tab use-skeleton">
                            <div id="busy-hours-chart" class="hidden">
                                <!-- chart to display the details -->
                                <ul class="peak-time-nav"></ul>
                                <div class="peak-time-chart">
                                    <div class="peak-time-chart__wrapper">
                                        <canvas id="peak-time-chart" class="graph-chart" height="220">charts js</canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-section">
            <div class="no-gm-api__wrapper container-no-gm-api">
                <div class="ph-the-info-box the-info-box the-info-box--lg">
                    <div class="the-info-box__body body text-center">
                        <h4>{{Could not access GymMaster at this time}}</h4>
                        <p>
                            {{Please check that your GymMaster API is configured}}, <br />
                            {{and your server is}}
                            <a href="https://status.performancehub.co/" target="_blank">on-line</a> {{and functioning}}.
                        </p>
                        <a class="btn btn-default" style="margin: 20px" href="/club-integrations">{{Configure Now}}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid no-clubs-associated-container" style="display: none">
        <div class="row clearfix">
            <div class="col-xs-12 col-sm-12 col-lg-6 col-lg-offset-3">
                <div class="card">
                    <div class="header">
                        <h3>{{Welcome to the Performance Hub}}</h3>
                        <hr>
                        <p style="margin-top: 10px">
                            {{You do not currently have any locations associated with your account.}}
                        </p>
                        <p>
                            {{This means, that while you will have some basic access to high level functions of the performance hub}}
                            <b>{{you will not be able to access location specific functionality}}</b>
                        </p>
                        <center>
                            <img src="/assets/images/ph-onboarding.jpg" style="max-width: 300px; width: 100%;">
                        </center>
                    </div>
                </div>

                <div class="card no-clubs-nav-options">
                    <div class="row">
                        <div class="col-xs-12">
                            <h3>{{Navigation}}</h3>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <a class="btn btn-lg btn-default btn-outline s-store">
                                <i class="fa-solid fa-cart-shopping"></i>
                                {{Performance Hub Store}}
                            </a>
                            <a class="btn btn-lg btn-default btn-outline s-wiki">
                                <i class="fa-brands fa-wikipedia-w"></i>
                                {{Knowledge Base}}
                            </a>
                            <a class="btn btn-lg btn-default btn-outline s-support">
                                <i class="fa-solid fa-headset"></i>
                                {{Support & Ticketing}}
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card w linking-request">
                    <div class="header">
                        <h3>{{Request to link/setup your account with a Club}}</h3>
                    </div>
                    <div style="margin: 10px;">
                        <form name="linking-request">
                            <div class="row clearfix">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <div class="form-line">
                                            <input
                                                type="text"
                                                name="link-member-name"
                                                class="form-control"
                                                placeholder="{{Your Name (Full Name)}}"
                                            />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-line">
                                            <input
                                                type="text"
                                                name="link-member-mobile"
                                                class="form-control"
                                                placeholder="{{Contact Phone (Mobile Number, Include Country Code)}}"
                                            />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-line">
                                            <input
                                                type="text"
                                                name="link-select-club"
                                                class="form-control"
                                                placeholder="{{Name/Location of Club (Include Country)}}"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12" style="text-align: right; padding-bottom: 20px;">
                                    <button type="button" class="btn btn-primary btn-lg m-l-15 waves-effect">
                                        {{Request}}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!--
        <div class="row clearfix">
            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                <div class="card">
                    <div class="header">
                        <h2>New Club - Self Assessment Checklist</h2>
                        <ul class="header-dropdown m-r--5">
                            <li class="dropdown">
                                <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                                    <i class="material-icons">more_vert</i>
                                </a>
                                <ul class="dropdown-menu pull-right">
                                    <li><a href="javascript:void(0);">Request Support</a></li>
                                    <li><a href="javascript:void(0);">View in Project Management System</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                    <div class="body">
                        <div class="table-responsive">
                            <table class="table table-hover dashboard-task-infos">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Task</th>
                                        <th>Status</th>
                                        <th>Cards Remaining</th>
                                        <th>Progress</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>Premise, Location, Lease & Approval</td>
                                        <td><span class="label bg-light-blue">In-progress</span></td>
                                        <td>Source Location, Sign Lease, Approval from 12RND HQ</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-light-blue" role="progressbar" aria-valuenow="62" aria-valuemin="0" aria-valuemax="100" style="width: 62%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>Web Presence & Social Media</td>
                                        <td><span class="label bg-light-blue">In-progress</span></td>
                                        <td>Setup Social Media, Add Website Listing, Setup Landing Pages</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-light-blue" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 40%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>Floorplan, Signage & Fitout</td>
                                        <td><span class="label bg-orange">Awaiting Approval</span></td>
                                        <td>Finalise Floorplan</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-orange" role="progressbar" aria-valuenow="72" aria-valuemin="0" aria-valuemax="100" style="width: 72%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>Equipment</td>
                                        <td><span class="label bg-green">Completed</span></td>
                                        <td>NONE</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-green" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100" style="width: 95%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>Pre-launch preparation</td>
                                        <td>
                                            <span class="label bg-light-blue">In-progress</span>
                                        </td>
                                        <td>Pre-launch mail-out</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-light-blue" role="progressbar" aria-valuenow="87" aria-valuemin="0" aria-valuemax="100" style="width: 87%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="row clearfix">
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                <div class="card">
                    <div class="body bg-pink">
                        <div class="sparkline" data-type="line" data-spot-Radius="4" data-highlight-Spot-Color="rgb(233, 30, 99)" data-highlight-Line-Color="#fff"
                             data-min-Spot-Color="rgb(255,255,255)" data-max-Spot-Color="rgb(255,255,255)" data-spot-Color="rgb(255,255,255)"
                             data-offset="90" data-width="100%" data-height="92px" data-line-Width="2" data-line-Color="rgba(255,255,255,0.7)"
                             data-fill-Color="rgba(0, 188, 212, 0)">
                            12,10,9,6,5,6,10,5,7,5,12,13,7,12,11
                        </div>
                        <ul class="dashboard-stat-list">
                            <li>
                                TODAY
                                <span class="pull-right"><b>1 200</b> <small>USERS</small></span>
                            </li>
                            <li>
                                YESTERDAY
                                <span class="pull-right"><b>3 872</b> <small>USERS</small></span>
                            </li>
                            <li>
                                LAST WEEK
                                <span class="pull-right"><b>26 582</b> <small>USERS</small></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                <div class="card">
                    <div class="body bg-cyan">
                        <div class="m-b--35 font-bold">LATEST SOCIAL TRENDS</div>
                        <ul class="dashboard-stat-list">
                            <li>
                                #socialtrends
                                <span class="pull-right">
                                    <i class="material-icons">trending_up</i>
                                </span>
                            </li>
                            <li>
                                #materialdesign
                                <span class="pull-right">
                                    <i class="material-icons">trending_up</i>
                                </span>
                            </li>
                            <li>#adminbsb</li>
                            <li>#freeadmintemplate</li>
                            <li>#bootstraptemplate</li>
                            <li>
                                #freehtmltemplate
                                <span class="pull-right">
                                    <i class="material-icons">trending_up</i>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                <div class="card">
                    <div class="body bg-teal">
                        <div class="font-bold m-b--35">ANSWERED TICKETS</div>
                        <ul class="dashboard-stat-list">
                            <li>
                                TODAY
                                <span class="pull-right"><b>12</b> <small>TICKETS</small></span>
                            </li>
                            <li>
                                YESTERDAY
                                <span class="pull-right"><b>15</b> <small>TICKETS</small></span>
                            </li>
                            <li>
                                LAST WEEK
                                <span class="pull-right"><b>90</b> <small>TICKETS</small></span>
                            </li>
                            <li>
                                LAST MONTH
                                <span class="pull-right"><b>342</b> <small>TICKETS</small></span>
                            </li>
                            <li>
                                LAST YEAR
                                <span class="pull-right"><b>4 225</b> <small>TICKETS</small></span>
                            </li>
                            <li>
                                ALL
                                <span class="pull-right"><b>8 752</b> <small>TICKETS</small></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
 -->