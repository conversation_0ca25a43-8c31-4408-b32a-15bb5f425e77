---
title: Website Management
---

<div class="website-management">
    <section class="content">
        <div class="container-fluid">
            <div class="block-header">
                <h2><strong>Website Management</strong></h2>
            </div>

            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="card">
                        <div class="body">
                            <ul class="nav nav-tabs tab-nav-right website-management-tabs" role="tablist">
                                <li role="presentation" class="active">
                                    <a href="#faqs" aria-controls="faqs" role="tab" data-toggle="tab">{{FAQs}}</a>
                                </li>
                                <li role="presentation">
                                    <a href="#press" aria-controls="press" role="tab" data-toggle="tab">{{Press}}</a>
                                </li>
                                <li role="presentation">
                                    <a href="#mappings" aria-controls="mappings" role="tab" data-toggle="tab"
                                        >{{Email Mapping}}</a
                                    >
                                </li>
                                <li role="presentation">
                                  <a href="#club-lp" aria-controls="club-lp" role="tab" data-toggle="tab"
                                      >{{Club Landing Pages}}</a
                                  >
                              </li>
                            </ul>

                            <div class="files-toolbar">
                                <div class="row">
                                    <div class="col-sm-8 col-xs-12">
                                        <div class="icon-search search-bar-wrapper">
                                            <div class="input-group form-group">
                                                <span class="input-group-addon">
                                                    <i class="material-icons">search</i>
                                                </span>
                                                <div class="form-line">
                                                    <input
                                                        data-hj-allow
                                                        type="search"
                                                        id="filter-items"
                                                        class="form-control search-bar"
                                                        placeholder="{{Filter Items}}"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12 col-sm-4 text-right">
                                        <div class="new-design-wrapper hidden-xs hidden-sm">
                                            <button
                                                type="button"
                                                class="btn btn-xs btn-primary btn-outline btn-new-design waves-effect add-item"
                                            >
                                                <i class="material-icons">add</i> <span>{{Add Item}}</span>
                                            </button>
                                        </div>

                                        <div class="admin-designs-wrapper styled-checkbox">
                                            <input
                                                type="checkbox"
                                                id="show-published-items"
                                                class="filled-in chk-col-blue"
                                                checked
                                            />
                                            <label for="show-published-items" class="checkbox-label"> {{All Items}} </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="no-search-results text-center" style="display: none">
                                No results based on your filter
                            </div>

                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="faqs">
                                    <!-- faqs display -->
                                    <div class="faqs-display">
                                        <form id="faqs-filter" class="faqs-filter">
                                            <!-- <a href="#faqs-form" class="btn btn-default btn-default float-right add-faq arial">
                        <span class="glyphicon fa-plus-circle" aria-hidden="true"></span> Add FAQ
                      </a> -->

                                            <!-- faq filter form -->
                                            <div class="input-group hidden">
                                                <div class="input-group-btn">
                                                    <button
                                                        type="button"
                                                        class="btn btn-default dropdown-toggle"
                                                        data-toggle="dropdown"
                                                        aria-haspopup="true"
                                                        aria-expanded="false"
                                                    >
                                                        <span id="faq-selected-category">Category</span>
                                                        <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <button
                                                                type="button"
                                                                class="faq-category"
                                                                data-faq-category="club"
                                                            >
                                                                club
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                class="faq-category"
                                                                data-faq-category="train-on-demand"
                                                            >
                                                                train on-demand
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                class="faq-category"
                                                                data-faq-category="store"
                                                            >
                                                                store
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                class="faq-category"
                                                                data-faq-category="club-pass"
                                                            >
                                                                club pass
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                class="faq-category"
                                                                data-faq-category="franchise"
                                                            >
                                                                franchise
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <!-- /btn-group -->
                                                <input
                                                    type="text"
                                                    class="form-control"
                                                    aria-label="..."
                                                    placeholder="Search for faqs..."
                                                />
                                                <span class="input-group-btn">
                                                    <button class="btn btn-primary" type="button">Filter Faqs</button>
                                                </span>
                                            </div>
                                        </form>

                                        <div class="clearfix"></div>

                                        <h6 class="faqs-filter-summary" style="display: none">
                                            Showing <strong class="faq-result-count">20</strong> results
                                            <span class="faq-keyword">with keyword "blah blah"</span>
                                            <span class="faq-result-category"
                                                >for "club" <strong>category</strong></span
                                            >
                                        </h6>

                                        <div id="faqs-items" class="faq-items" style="display: none"></div>
                                    </div>

                                    <!-- faqs add form -->
                                    <form
                                        id="faqs-form"
                                        class="faq-add-form form-wrap"
                                        action="/website-management/faqs/add"
                                        style="display: none"
                                    >
                                        <h6 class="faq-form-headline form-title">Add New Faq Item</h6>

                                        <!-- location -->
                                        <div class="fld faq-selectpicker">
                                            <label for="faq-location" class="fld-label" id="fld-label-location"
                                                >Location:</label
                                            >
                                            <select
                                                class="form-control"
                                                id="faq-location"
                                                data-live-search="true"
                                                multiple
                                            ></select>
                                        </div>

                                        <!-- language -->
                                        <div class="fld faq-selectpicker">
                                            <label for="faq-language" class="fld-label" id="fld-label-language" data-skip="true"
                                                >Language:</label
                                            >
                                            <select
                                                class="form-control"
                                                id="faq-language"
                                                data-live-search="true"
                                               
                                            ></select>
                                        </div>

                                        <!-- categories -->
                                        <label for="faq-categories" class="fld-label" id="fld-label-categories"
                                            >Categories:</label
                                        >

                                        <div id="faq-categories" class="faq-categories">
                                            <div class="faq-category-option styled-checkbox">
                                                <input
                                                    type="checkbox"
                                                    id="faq-club"
                                                    value="club"
                                                    class="filled-in chk-col-blue faq-category-input"
                                                />
                                                <label for="faq-club" class="faq-category-label"
                                                    >Membership / Club</label
                                                >
                                            </div>

                                            <div class="faq-category-option styled-checkbox">
                                                <input
                                                    type="checkbox"
                                                    id="faq-train-on-demand"
                                                    value="train-on-demand"
                                                    class="filled-in chk-col-blue faq-category-input"
                                                />
                                                <label for="faq-train-on-demand" class="faq-category-label"
                                                    >TRAIN: On Demand</label
                                                >
                                            </div>

                                            <div class="faq-category-option styled-checkbox">
                                                <input
                                                    type="checkbox"
                                                    id="faq-store"
                                                    value="store"
                                                    class="filled-in chk-col-blue faq-category-input"
                                                />
                                                <label for="faq-store" class="faq-category-label"
                                                    >Store &amp; Ordering</label
                                                >
                                            </div>

                                            <div class="faq-category-option styled-checkbox">
                                                <input
                                                    type="checkbox"
                                                    id="faq-clubpass"
                                                    value="clubpass"
                                                    class="filled-in chk-col-blue faq-category-input"
                                                />
                                                <label for="faq-clubpass" class="faq-category-label"
                                                    >Clubpass &amp; Other Apps</label
                                                >
                                            </div>

                                            <div class="faq-category-option styled-checkbox">
                                                <input
                                                    type="checkbox"
                                                    id="faq-trainingcamp"
                                                    value="the-training-camp"
                                                    class="filled-in chk-col-blue faq-category-input"
                                                />
                                                <label for="faq-trainingcamp" class="faq-category-label"
                                                    >The Training Camp</label
                                                >
                                            </div>

                                            <div class="faq-category-option styled-checkbox">
                                                <input
                                                    type="checkbox"
                                                    id="faq-franchise"
                                                    value="franchise"
                                                    class="filled-in chk-col-blue faq-category-input"
                                                />
                                                <label for="faq-franchise" class="faq-category-label"
                                                    >Own a Franchise</label
                                                >
                                            </div>
                                        </div>

                                        <div class="fld fld-question">
                                            <!-- question -->
                                            <label for="faq-question" class="fld-label" id="fld-label-question"
                                                >Question:</label
                                            >
                                            <div id="faq-question" placeholder="Question"></div>
                                        </div>

                                        <div class="fld">
                                            <!-- answer -->
                                            <label for="faq-answer" class="fld-label" id="fld-label-answer"
                                                >Answer:</label
                                            >
                                            <div id="faq-answer" placeholder="Answer"></div>
                                        </div>

                                        <!-- faq status -->
                                        <div class="fld faq-selectpicker">
                                            <label for="faq-status" class="fld-label" id="fld-label-status"
                                                >Status:</label
                                            >
                                            <div class="faq-status">
                                                <div class="faq-status-option styled-checkbox">
                                                    <input
                                                        type="checkbox"
                                                        id="faq-published"
                                                        value="published"
                                                        class="filled-in chk-col-blue"
                                                        name="faq-status"
                                                    />
                                                    <label for="faq-published" class="checkbox-label faq-status-label">
                                                        Published
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <p class="form-btn-container">
                                            <button
                                                type="button"
                                                class="faq-add-cancel btn btn-default waves-effect btn-cancel"
                                            >
                                                Cancel
                                            </button>
                                            <button type="submit" class="btn btn-primary" id="faq-submit">
                                                Submit
                                            </button>
                                        </p>
                                    </form>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="press">
                                    <div class="press-content">
                                        <!-- press add/edit form -->
                                        <form
                                            class="press-form form-wrap"
                                            action="/website-management"
                                            id="press-form"
                                            style="display: none"
                                        >
                                            <h6 class="press-form-headline form-title">Add New Item</h6>

                                            <!-- location -->
                                            <div class="fld press-selectpicker">
                                                <label
                                                    for="press-location"
                                                    class="fld-label"
                                                    data-id="fld-label-location"
                                                >
                                                    Location:
                                                </label>
                                                <select
                                                    class="form-control"
                                                    id="press-location"
                                                    data-live-search="true"
                                                    multiple
                                                ></select>
                                            </div>

                                            <!-- press title -->
                                            <div class="fld">
                                                <label for="press-title" class="fld-label" id="fld-label-title"
                                                    >Title:</label
                                                >
                                                <input
                                                    type="text"
                                                    id="press-title"
                                                    name="press-title"
                                                    class="fld-input"
                                                    placeholder="Enter title..."
                                                />
                                            </div>

                                            <!-- press publisher -->
                                            <div class="fld">
                                                <label for="press-publisher" class="fld-label" id="fld-label-publisher"
                                                    >Publisher:</label
                                                >
                                                <input
                                                    type="text"
                                                    id="press-publisher"
                                                    name="press-publisher"
                                                    class="fld-input"
                                                    placeholder="Enter publisher name..."
                                                />
                                            </div>

                                            <!-- press date -->
                                            <div class="fld">
                                                <label for="press-date" class="fld-label" id="fld-label-date"
                                                    >Date:</label
                                                >
                                                <input
                                                    type="date"
                                                    id="press-date"
                                                    name="press-date"
                                                    class="fld-input"
                                                    placeholder="Enter date..."
                                                />
                                            </div>

                                            <!-- press quote -->
                                            <div class="fld">
                                                <label for="press-quote" class="fld-label" id="fld-label-quote"
                                                    >Quote:</label
                                                >
                                                <textarea
                                                    id="press-quote"
                                                    name="press-quote"
                                                    class="fld-input"
                                                    cols="30"
                                                    rows="10"
                                                    placeholder="Enter quote..."
                                                ></textarea>
                                            </div>

                                            <!-- press imageSrc -->
                                            <div class="fld">
                                                <label for="press-imageSrc" class="fld-label" id="fld-label-imageSrc"
                                                    >Image Source:</label
                                                >

                                                <div class="fld-drop"></div>
                                            </div>

                                            <!-- press url -->
                                            <div class="fld">
                                                <label for="press-url" class="fld-label" id="fld-label-url"
                                                    >Press url:</label
                                                >
                                                <input
                                                    type="text"
                                                    id="press-url"
                                                    name="press-url"
                                                    class="fld-input"
                                                    placeholder="Enter image url here..."
                                                />
                                            </div>

                                            <!-- press status -->
                                            <div class="fld">
                                                <label for="press-status" class="fld-label" data-id="fld-label-status"
                                                    >Status:</label
                                                >
                                                <div class="press-status">
                                                    <div class="press-status-option styled-checkbox">
                                                        <input
                                                            type="checkbox"
                                                            id="press-status"
                                                            value="published"
                                                            class="filled-in chk-col-blue"
                                                            name="press-status"
                                                        />
                                                        <label
                                                            for="press-status"
                                                            class="checkbox-label press-status-label"
                                                        >
                                                            Published
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-btn-container">
                                                <button
                                                    type="button"
                                                    class="press-action-cancel press-action btn btn-default waves-effect btn-cancel"
                                                    id="press-cancel"
                                                >
                                                    Cancel
                                                </button>
                                                <button
                                                    type="submit"
                                                    class="press-action-submit press-action btn btn-primary"
                                                    id="press-submit"
                                                >
                                                    Submit
                                                </button>
                                            </div>
                                        </form>

                                        <!-- press display section -->
                                        <div class="press-display press-grid" id="press-display">
                                            <div class="press-items press-grid"></div>
                                        </div>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="mappings">
                                    <!-- email mapping add/edit form -->
                                    <form
                                        class="mappings-form form-wrap"
                                        action="/website-management"
                                        id="mappings-form"
                                        style="display: none"
                                    >
                                        <h6 class="mappings-form-headline form-title">Add Press Item</h6>

                                        <div class="mappings-fields">
                                            <div class="fld">
                                                <label for="mappings-id" class="fld-label" id="fld-label-id">Id:</label>
                                                <input
                                                    type="text"
                                                    id="mappings-id"
                                                    name="mappings-id"
                                                    class="fld-input"
                                                    placeholder="Enter id..."
                                                />
                                            </div>
                                            
                                            <!-- language -->
                                        <div class="fld mappings-fields fld-selectpicker">
                                            <label for="mappings-language" class="fld-label" id="fld-label-language" data-skip="true">Language:</label>
                                            <select class="form-control" id="mappings-language" data-live-search="true"></select>
                                        </div>

                                            <div class="fld">
                                                <label for="mappings-label" class="fld-label" id="fld-label-label"
                                                    >Label:</label
                                                >
                                                <input
                                                    type="text"
                                                    id="mappings-label"
                                                    name="mappings-label"
                                                    class="fld-input"
                                                    placeholder="Enter label..."
                                                />
                                            </div>

                                            <div class="fld">
                                                <label
                                                    for="mappings-email"
                                                    class="fld-label"
                                                    id="fld-label-email"
                                                    data-skip="true"
                                                    >Email:</label
                                                >
                                                <input
                                                    type="email"
                                                    id="mappings-email"
                                                    name="mappings-email"
                                                    class="fld-input"
                                                    placeholder="Enter email..."
                                                />
                                            </div>

                                            <div class="fld">
                                                <label
                                                    for="mappings-notice"
                                                    class="fld-label"
                                                    id="fld-label-notice"
                                                    data-skip="true"
                                                    >Notice:</label
                                                >
                                                <div id="mappings-notice" placeholder="Answer"></div>
                                            </div>
                                        </div>

                                        <div class="form-btn-container">
                                            <button
                                                type="button"
                                                class="press-action-cancel press-action btn btn-default waves-effect btn-cancel"
                                                id="mapping-cancel"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                class="press-action-submit press-action btn btn-primary"
                                                id="mapping-submit"
                                            >
                                                Submit
                                            </button>
                                        </div>
                                    </form>

                                    <div id="mappings-display" class="mappings-display">
                                        <div class="mappings" id="mappings-container"></div>
                                    </div>
                                </div>
                                
                                <!-- device management class is used here to recycle the styling from the table -->
                                <div role="tabpanel" class="tab-pane device-management-screen" id="club-lp">

                                  <div class="club-lp-table__wrapper dataTables_wrapper form-inline dt-bootstrap no-footer" id="ics-devices-container">
                                    <table id="club-lp-table" width="100%" class="table table-hover devices-table dataTable no-footer">
                                      <thead>
                                          <tr>
                                              <th>
                                                  <div class="device-screen-table__header">
                                                      <span class="use-skeleton">{{Page Name}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                              <th class="hidden-xs">
                                                  <div class="device-screen-table__header">
                                                      <span class="use-skeleton">{{Slug}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                              <th class="visible-xl">
                                                  <div class="device-screen-table__header">
                                                      <span class="use-skeleton">{{Regions}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                              <th class="visible-xl">
                                                  <div class="device-screen-table__header">
                                                      <span class="use-skeleton">{{Clubs}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                              <th class="hidden-sm hidden-xs">
                                                  <div class="device-screen-table__header">
                                                      <span class="use-skeleton">{{Dates Available}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                              <th class="visible-xl">
                                                  <div class="device-screen-table__header">
                                                      <span class="use-skeleton">{{Status}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                              <th class="text-right">
                                                  <div class="device-screen-table__header justify-end">
                                                      <span class="use-skeleton">{{Actions}}</span>
                                                      <span class="device-screen-table__header__icon use-skeleton"></span>
                                                  </div>
                                              </th>
                                          </tr>
                                      </thead>
                                      <tbody></tbody>
                                    </table>
                                  </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="save-overlay">
        <div class="spinner-container">
            <h3 class="overlay-text">One moment please...</h3>
            <div class="preloader pl-size-xl" style="padding: 10px">
                <img src="/assets/images/tail-spin.svg" />
            </div>
        </div>
    </div>

    <!-- delete confirmation modal -->
    <div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title text-center" id="myModalLabel">
                        Are you sure you want to delete this item?
                    </h6>
                </div>
                <div class="modal-body text-center">
                    <button type="button" class="btn btn-primary" id="proceed-delete">Confirm Delete</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
