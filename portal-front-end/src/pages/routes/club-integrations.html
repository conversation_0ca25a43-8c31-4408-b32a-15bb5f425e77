<div id="club-integrations">
  <section class="content full-height-content is-relative">
    <div class="container-fluid">
      <div class="alert-header-block"></div>
      <div class="roclearfix">
        <form>
          <div class="club-integrations-container">
            <div class="body">
              <div class="tab__wrapper">
                <div style="overflow-x: auto; overflow-y: hidden; padding-bottom: 2rem">
                  <ul class="nav nav-tabs ph-nav-tabs tab-nav-right" role="tablist" style="min-width: max-content">
                    <li role="presentation" class="active"><a href="#mms" data-toggle="tab"
                        aria-expanded="true">{{Member Management System}}</a></li>
                    <li role="presentation"><a href="#lead-forwarding" data-toggle="tab"
                        aria-expanded="false">{{Lead Forwarding (CRM)}}</a></li>
                    <li role="presentation"><a href="#myzone-api" data-toggle="tab"
                      aria-expanded="false">{{Myzone API}}</a></li>
                    <li role="presentation"><a href="#stripe-account" data-toggle="tab"
                      aria-expanded="false">{{Stripe Account}}</a></li>
                    <li role="presentation"><a href="#extended-access" data-toggle="tab"
                      aria-expanded="false">{{Smart Technology}}</a></li>
                  </ul>
                </div>
              </div>
              <div class="tab-content tab-content-overview__content">
                <div role="tabpanel" class="tab-pane active" id="mms">
                  <div class="card-inside-title">
                      <p class="use-skeleton rounded">
                        {{To ensure seamless integration of your facilities operations with the Performance Hub, it's important to connect your Member Management System (MMS) via its API. This integration enables synchronization of membership data, automates billing, and streamlines various administrative tasks within our platform. Your API key is typically provided during setup. If you require assistance with manual configuration, your Account Manager or your MMS provider's support team can help guide you through the process.}}
                      </p>
                  </div>

                  <hr>

                  <div class="row" style="">
                    <div class="col-md-4" style="">
                      <label for="mmsSystemType" class="use-skeleton rounded">{{MMS Provider}}</label>
                      <div class="input-group">
                        <div class="form-line">
                          <select id="mmsSystemType" name="mmsSystemType" class="use-skeleton show-tick selectpicker">
                            <option value="GymMaster">{{GymMaster}}</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-4">
                      <label for="gymMasterDomain" class="use-skeleton rounded">{{MMS API Endpoint}}</label>
                      <div class="input-group">
                        <div class="form-line">
                          <span class="use-skeleton rounded isBlock">
                            <input type="text" id="gymMasterDomain" class="form-control"
                              placeholder="&lt;your-club-name&gt;.gymmasteronline.com">
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <label for="gymMasterAPI" class="use-skeleton rounded">{{MMS API Key}}</label>
                      <div class="input-group">
                        <div class="form-line">
                          <span class="use-skeleton rounded isBlock">
                            <input type="text" id="gymMasterAPI" class="form-control" placeholder="c7964837-4bae-456f-8edd-ccc151862a59">
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <hr>

                  <h4 class="use-skeleton">{{Membership Mapping}}</h4>
                  <div class="card-inside-title">
                    <p class="use-skeleton rounded">
                      {{This allows you to assign specific membership types to predefined categories, enabling tailored access to different areas of the facility or by defining whether members can use facilities during staffed or unstaffed hours, etc.}}
                    </p>
                  </div>

                  <hr>

                  <div class="filter-section">
                    <div class="filters-container isLoading" id="memberships-filter">
                      <div class="use-skeleton" style="width: 171px; height: 24px; border-radius: 24px;"></div>
                      <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                      <div class="use-skeleton" style="width: 100px; height: 24px; border-radius: 24px;"></div>
                    </div>
                  </div>

                  <div class="club-memberships isLoading" id="club-memberships__preloader"> 
                    <div class="club-memberships__item">
                      <div class="club-memberships__item__title-section">
                        <p class="club-memberships__item__title use-skeleton">
                          Winter Warm Up
                        </p>
                        <p class="club-memberships__item__price use-skeleton">
                          $55
                        </p>
                      </div>

                      <div class="club-memberships__item__tags">
                        <span class="club-memberships__item__tag club-memberships__item__tag--ea btn__badge btn__badge--semi btn__badge--sm btn__badge--success use-skeleton">
                          EA
                        </span>
                        <span class="club-memberships__item__tag btn__badge btn__badge--semi btn__badge--sm btn__badge--stroked use-skeleton">
                          Monthly by Billing
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--success btn__badge btn__badge--semi btn__badge--highlight use-skeleton">
                          Sell Online
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--archived btn__badge btn__badge--semi btn__badge--sm use-skeleton">
                          Archived
                        </span>
                      </div>

                      <div class="club-memberships__item__content">
                        <p class="use-skeleton">Stay active this winter and save! That's just $29/Week 🥊 [LIMITED TIME ONLY]...</p>
                      </div>

                      <div class="club-memberships__item__more">
                        <hr />
                        <a href="/" class="use-skeleton">Show more</a>
                      </div>

                      <div class="club-memberships__item__dropdown">
                        <div class="form-group use-skeleton rounded">
                          <div class="form-line use-skeleton">
                              <select name="access-options" class="form-control" required>
                                  <option value="extended access">{{With Extended Access}}</option>
                                  <option value="regular access">{{Without Extended Access}}</option>
                              </select>
                          </div>
                        </div>
                      </div>
                      
                    </div>
                    <div class="club-memberships__item">
                      <div class="club-memberships__item__title-section">
                        <p class="club-memberships__item__title use-skeleton">
                          Winter Warm Up
                        </p>
                        <p class="club-memberships__item__price use-skeleton">
                          $55
                        </p>
                      </div>

                      <div class="club-memberships__item__tags">
                        <span class="club-memberships__item__tag club-memberships__item__tag--ea btn__badge btn__badge--semi btn__badge--sm btn__badge--success use-skeleton">
                          EA
                        </span>
                        <span class="club-memberships__item__tag btn__badge btn__badge--semi btn__badge--sm btn__badge--stroked use-skeleton">
                          Monthly by Billing
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--success btn__badge btn__badge--semi btn__badge--highlight use-skeleton">
                          Sell Online
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--archived btn__badge btn__badge--semi btn__badge--sm use-skeleton">
                          Archived
                        </span>
                      </div>

                      <div class="club-memberships__item__content">
                        <p class="use-skeleton">Stay active this winter and save! That's just $29/Week 🥊 [LIMITED TIME ONLY]...</p>
                      </div>

                      <div class="club-memberships__item__more">
                        <hr />
                        <a href="/" class="use-skeleton">Show more</a>
                      </div>

                      <div class="club-memberships__item__dropdown">
                        <div class="form-group use-skeleton rounded">
                          <div class="form-line use-skeleton">
                              <select name="access-options" class="form-control" required>
                                  <option value="extended access">{{With Extended Access}}</option>
                                  <option value="regular access">{{Without Extended Access}}</option>
                              </select>
                          </div>
                        </div>
                      </div>
                      
                    </div>
                    <div class="club-memberships__item">
                      <div class="club-memberships__item__title-section">
                        <p class="club-memberships__item__title use-skeleton">
                          Winter Warm Up
                        </p>
                        <p class="club-memberships__item__price use-skeleton">
                          $55
                        </p>
                      </div>

                      <div class="club-memberships__item__tags">
                        <span class="club-memberships__item__tag club-memberships__item__tag--ea btn__badge btn__badge--semi btn__badge--sm btn__badge--success use-skeleton">
                          EA
                        </span>
                        <span class="club-memberships__item__tag btn__badge btn__badge--semi btn__badge--sm btn__badge--stroked use-skeleton">
                          Monthly by Billing
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--success btn__badge btn__badge--semi btn__badge--highlight use-skeleton">
                          Sell Online
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--archived btn__badge btn__badge--semi btn__badge--sm use-skeleton">
                          Archived
                        </span>
                      </div>

                      <div class="club-memberships__item__content">
                        <p class="use-skeleton">Stay active this winter and save! That's just $29/Week 🥊 [LIMITED TIME ONLY]...</p>
                      </div>

                      <div class="club-memberships__item__more">
                        <hr />
                        <a href="/" class="use-skeleton">Show more</a>
                      </div>

                      <div class="club-memberships__item__dropdown">
                        <div class="form-group use-skeleton rounded">
                          <div class="form-line use-skeleton">
                              <select name="access-options" class="form-control" required>
                                  <option value="extended access">{{With Extended Access}}</option>
                                  <option value="regular access">{{Without Extended Access}}</option>
                              </select>
                          </div>
                        </div>
                      </div>
                      
                    </div>
                    <div class="club-memberships__item">
                      <div class="club-memberships__item__title-section">
                        <p class="club-memberships__item__title use-skeleton">
                          Winter Warm Up
                        </p>
                        <p class="club-memberships__item__price use-skeleton">
                          $55
                        </p>
                      </div>

                      <div class="club-memberships__item__tags">
                        <span class="club-memberships__item__tag club-memberships__item__tag--ea btn__badge btn__badge--semi btn__badge--sm btn__badge--success use-skeleton">
                          EA
                        </span>
                        <span class="club-memberships__item__tag btn__badge btn__badge--semi btn__badge--sm btn__badge--stroked use-skeleton">
                          Monthly by Billing
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--success btn__badge btn__badge--semi btn__badge--highlight use-skeleton">
                          Sell Online
                        </span>
                        <span class="club-memberships__item__tag club-memberships__item__tag--archived btn__badge btn__badge--semi btn__badge--sm use-skeleton">
                          Archived
                        </span>
                      </div>

                      <div class="club-memberships__item__content">
                        <p class="use-skeleton">Stay active this winter and save! That's just $29/Week 🥊 [LIMITED TIME ONLY]...</p>
                      </div>

                      <div class="club-memberships__item__more">
                        <hr />
                        <a href="/" class="use-skeleton">Show more</a>
                      </div>

                      <div class="club-memberships__item__dropdown">
                        <div class="form-group use-skeleton rounded">
                          <div class="form-line use-skeleton">
                              <select name="access-options" class="form-control" required>
                                  <option value="extended access">{{With Extended Access}}</option>
                                  <option value="regular access">{{Without Extended Access}}</option>
                              </select>
                          </div>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                  <div class="club-memberships isLoading" id="club-memberships__items">
                  </div>

                </div>
                <div role="tabpanel" class="tab-pane" id="lead-forwarding">
                  <div class="card-inside-title">
                      <p class="use-skeleton rounded">
                        {{Lead Forwarding automatically forwards leads, such as contact submissions and free trial bookings, to specified email addresses. These emails are then parsed by your CRM software, which maps the relevant fields into your CRM system for seamless tracking and follow-up.}}
                      </p>
                      <p class="use-skeleton rounded">
                        {{You can also create a Zapier account and set up an Email Parser to connect your free trial bookings and leads from your facility to a wide range of third-party systems. This allows you to automate workflows and streamline data handling across different platforms outside of the Performance Hub. For step-by-step instructions, please refer to the following guide}} : <a href="https://help.zapier.com/hc/en-us/articles/*************-Set-up-your-Email-Parser-account-in-Zapier#1-sign-up-for-an-email-parser-account-0-1" target="_blank">{{Email Parsing in Zapier}}</a>
                      </p>
                      <a href="" class="btn btn-default viewSampleLeadForwardingEmail">{{View Sample Email}}</a>
                  </div>

                  <hr>

                  <div class="row">
                    <div class="col-md-12">
                      <label for="notificationEmails" class="use-skeleton rounded">{{Lead Forwarding <i>(forwards on
                          'free trial' & facility page leads)}}</i></label>
                      <div class="input-group use-skeleton rounded">
                        <div class="form-line notificationEmails-placeholder">
                          <span>
                            <input type="text" id="notificationEmails" class="form-control" placeholder="<EMAIL>">
                          </span>
                        </div>
                      </div>
                      <div>
                        <small>{{Use a comma}} <code>,</code> {{to separate emails and/or add the email into the
                          list.}}</small>
                      </div>
                    </div>
                  </div>
                </div>
                <div role="tabpanel" class="tab-pane" id="myzone-api">
                  <div class="card-inside-title">
                      <p class="use-skeleton rounded">
                        {{Our system integrates with Myzone to sync Myzone Effort Points (MEP) data for your members directly into our Training Camp and Train on Demand apps. This integration allows members' workout efforts to be seamlessly tracked and reflected within your facilities digital ecosystem.}}
                      </p>
                      <p class="use-skeleton rounded">
                        {{To enable this integration between Myzone and the Performance Hub, your facility must be configured with a "Data level 4" access. This ensures that the necessary permissions are in place for the secure transfer and synchronization of member data. For more details on data levels and configuration, please refer to}}
                        <a href="https://l.myzone.org/b2b/how-to-comply-with-gdpr-using-myzone-a-guide-for-club-operators" target="_blank">{{Myzone's GDPR Agreement and Member Data Guide.}}</a>
                      </p>
                  </div>

                  <hr>

                  <div class="row">
                    <div class="col-md-4">
                      <label for="gymMasterAPI" class="use-skeleton rounded">{{Myzone API Key}}</label>
                      <div class="input-group">
                        <div class="form-line">
                          <span class="use-skeleton rounded isBlock">
                            <input type="text" id="myzoneAPI" class="form-control" placeholder="{{Your API Key will look like: b9a66500-9bab-49f2-82fd-850eafdbff24}}">
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div role="tabpanel" class="tab-pane" id="stripe-account">
                  <div class="card-inside-title">
                      <p class="use-skeleton rounded">
                        {{The Performance Hub integrates seamlessly with Stripe to process and track all payments made by members to your facility. This integration ensures that all transactions are securely handled and efficiently managed within the Performance Hub, providing real-time visibility into your facilities financial activity. By leveraging Stripe's robust payment infrastructure, you can easily manage subscriptions, one-time payments, and other financial interactions directly through the platform.}}
                      </p>
                  </div>

                  <hr>

                  <div class="row">
                    <div class="col-md-4">
                      <label for="stripeConnectedAccountID" class="use-skeleton rounded">{{Stripe Account ID}}</label>
                      <div class="input-group">
                        <div class="form-line">
                          <span class="use-skeleton rounded isBlock">
                            <input type="text" id="stripeConnectedAccountID" class="form-control" placeholder="{{Your Account will look like: acct_XXXXXXXXXXXXXXXX}}">
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="alert alert-blue">
                    {{Stripe enforces strict Know Your Customer (KYC) requirements to comply with financial regulations and ensure secure transactions. Within the Performance Hub, these requirements can be managed through the 'Business Profile (KYC)' section. This section allows you to easily submit and update the necessary business and personal information to meet Stripe's compliance standards, ensuring uninterrupted payment processing and financial management for your facility.}}
                    <br>
                    <a style="margin-top: 5px; font-weight: bold" href="/club-kyc">{{Manage KYC}}.</a>
                  </div>

                </div>

                <div role="tabpanel" class="tab-pane" id="extended-access">
                  <h4 class="use-skeleton">{{Extended Access}}</h4>
                  <div class="card-inside-title">
                      <p class="use-skeleton rounded">
                        {{Note, EA integration configurations are required to be set
                          to enable 'Extended Access Hours' on your facilities web page. Please refer to the support
                          documentation on how to configure this.}}
                      </p>
                  </div>

                  <div class="row" style="margin-bottom: -20px;">
                    <div class="col-md-4" style="margin-bottom: 0px;">
                      <label for="eaAlarmIntegrationType" class="use-skeleton rounded">{{Alarm Integration Type}}</label>
                      <div class="input-group">
                        <span class="input-group-addon">
                          <i class="fa-solid fa-network-wired use-skeleton"></i>
                        </span>
                        <div class="form-line">
                          <select id="eaAlarmIntegrationType" name="eaAlarmIntegrationType"
                            class="show-tick selectpicker use-skeleton" required>
                            <option value="none">{{None}}</option>
                            <option value="cid">{{TCP/IP CID Call}}</option>
                            <option value="relay">{{Hard Wired Relay}}</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4" style="margin-bottom: 0px;">
                      <label for="eaAlarmIntegrationProvider" class="use-skeleton rounded">{{Provider}}</label>
                      <div class="input-group">
                        <span class="input-group-addon">
                          <i class="fa-solid fa-building use-skeleton"></i>
                        </span>
                        <div class="form-line">
                          <select id="eaAlarmIntegrationProvider" name="eaAlarmIntegrationProvider"
                            class="show-tick selectpicker use-skeleton" disabled>
                            <option value="none">{{None}}</option>
                            <option value="b2b">{{Back2base}}</option>
                            <option value="w4g">{{W4G Security}}</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <hr>

                  <div id="eaNoneContainer">
                    <div class="alert alert-yellow">
                      {{EA Alarms to external monitoring system will not be enabled at this time}}
                    </div>
                  </div>

                  <div id="eaRelayContainer">
                    <div class="row">
                      <div class="col-md-12">
                        <b>{{Device Type}}:</b>
                        <p>{{To purchase a device that can integrate with the EA Duress Buttons please refer to the
                          Performance Hub Store. For documentation around configuring hard wired relay devices please
                          refer to the Knowledge Base.}}</p>
                        <select id="eaRelayDeviceType" class="show-tick selectpicker" required>
                          <option value="none">{{None}}</option>
                          <option value="numato-rl80001">{{Numato RL80001}}</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div id="eaW4GConfigContainer">
                      <p class="use-skeleton rounded">
                        {{Please note that the Site ID will be provided by W4G once your account
                        has been provisioned by W4G.}}
                      </p>
                      <div class="alert alert-blue">
                        {{To initiate a new request with W4G please complete the}} <a style="font-weight: bold;" href="https://forms.monday.com/forms/f50edb56ad84dab3ae6589b793117c00?r=use1">{{EA Onboarding Form}}</a>
                    </div>
                    <div class="row">
                      <div class="col-md-4">
                        <label for="w4gSiteId" class="use-skeleton rounded">{{W4G Site ID}}</label>
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="material-icons use-skeleton rounded">vpn_key</i>
                          </span>
                          <div class="form-line">
                            <span class="use-skeleton rounded isBlock">
                              <input type="text" id="w4gSiteId" class="form-control" placeholder="">
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div id="eaCidContainer">
                      <p class="use-skeleton rounded">
                        {{Please note that the Back2Base Alarm ID needs to be provided by Back2Base once your account
                        has been provisioned by Back2Base.}}
                      </p>
                      <div class="alert alert-blue">
                        {{To initiate a new request with Back2Base please complete the}} <a style="font-weight: bold;" href="https://forms.monday.com/forms/f50edb56ad84dab3ae6589b793117c00?r=use1">{{EA Onboarding Form}}</a>
                    </div>
                    <div class="row">
                      <div class="col-md-4">
                        <label for="back2BaseAlarmID" class="use-skeleton rounded">{{Back2Base Alarm ID}}</label>
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="material-icons use-skeleton rounded">vpn_key</i>
                          </span>
                          <div class="form-line">
                            <span class="use-skeleton rounded isBlock">
                              <input type="text" id="back2BaseAlarmID" class="form-control" placeholder="">
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <hr>

                  <h4 class="use-skeleton">{{Ubiquiti Unifi Network Configuration}}</h4>
                  <div class="row">
                    <div class="col-md-12 use-skeleton">
                      <p>{{If your network is equipped with a Unifi Express or similar Ubiquiti Unifi Network controller, please input the details to allow integration to your Smart Technology}}</p>
                    </div>
                  </div>
                  <div class="row mt-1">
                    <div class="col-md-4">
                      <div class="form-group use-skeleton">
                        <label for="unifiUsername">{{Username}}:</label>
                        <input type="text" class="form-control use-skeleton" id="unifiUsername" name="unifiUsername" placeholder="{{Enter Username}}">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group use-skeleton" style="position: relative;">
                        <label for="unifiPassword">{{Password}}:</label>
                        <input type="password" class="form-control use-skeleton" id="unifiPassword" name="unifiPassword" placeholder="{{Enter Password}}" style="padding-right: 40px;">
                        <i class="fa-solid fa-eye-slash toggle-password" data-target="unifiPassword" style="margin-top: 15px; position: absolute; right: 10px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #999;"></i>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group use-skeleton">
                        <label for="unifiControllerIP">{{Unifi Controller Host IP}}:</label>
                        <input type="text" class="form-control use-skeleton" id="unifiControllerIP" name="unifiControllerIP" placeholder="{{Leave blank to auto detect}}">
                      </div>
                    </div>
                  </div>
  
                  <div class="card p-3 mt-4 shadow-sm border-0">
                      <h5 class="font-weight-bold mb-3">{{Test Unifi Configuration}}</h5>
                      <p class="text-muted use-skeleton">
                          {{This test will check connectivity between the Performance Hub and your Unifi Network Controller. No settings will be changed on your device.}}
                          <br>
                          {{Ensure your credentials are correct before testing.}}
                      </p>
  
                      <div class="d-flex align-items-center use-skeleton">
                          <a class="btn btn-primary b-test-unifi me-3">
                              <span class="unifi-status-icon"></span>
                              {{Test Unifi Configuration}}
                          </a>
                          <a class="btn btn-default b-open-router me-3 ml-4">
                              <span class="router-status-icon"></span>
                              {{Open Router Webpage}}
                          </a>
                      </div>
  
                      <!-- Status message container -->
                      <div id="unifi-status-message" class="mt-3"></div>
                  </div>

                </div> 
              </div>
            </div>
          </div>

          <div class="pinned-footer form-save">
            <a class="btn btn-default waves-effect btn-cancel use-skeleton rounded" href="/club-integrations"
              role="button">{{Cancel}}</a>
            <button class="btn btn-primary waves-effect btn-save use-skeleton rounded" type="submit">{{Save
              Changes}}</button>
          </div>
        </form>
      </div>
    </div>
  </section>
</div>