<section class="coaching-screens-ui__content full-height-content content is-relative" id="coaching-screens">
    <div class="container-fluid">
      <div class="cs-skeleton-skeleton-loader isLoading">
        <div class="cs-skeleton-skeleton-loader__header">
          <div class="cs-skeleton-skeleton-loader__header__menu">
            <div class="cs-skeleton-skeleton-loader__header__menu-item use-skeleton rounded"></div>
            <div class="cs-skeleton-skeleton-loader__header__menu-item use-skeleton rounded"></div>
            <div class="cs-skeleton-skeleton-loader__header__menu-item use-skeleton rounded"></div>
          </div>
          <div class="cs-skeleton-skeleton-loader__header__settings">
            <div class="cs-skeleton-skeleton-loader__header__settings-cog use-skeleton rounded--full"></div>
            <div class="cs-skeleton-skeleton-loader__header__settings-cog use-skeleton rounded--full"></div>
          </div>
        </div>
        <div class="cs-skeleton-skeleton-loader__main">
          <div class="cs-skeleton-skeleton-loader__widgets">
            <div class="cs-skeleton-skeleton-loader__widget">
              <div class="cs-skeleton-skeleton-loader__widget-title use-skeleton rounded"></div>
              <div class="cs-skeleton-skeleton-loader__widget-thumb use-skeleton rounded"></div>
            </div>
            <div class="cs-skeleton-skeleton-loader__widget">
              <div class="cs-skeleton-skeleton-loader__widget-title use-skeleton rounded"></div>
              <div class="cs-skeleton-skeleton-loader__widget-thumb use-skeleton rounded"></div>
            </div>
            <div class="cs-skeleton-skeleton-loader__widget">
              <div class="cs-skeleton-skeleton-loader__widget-title use-skeleton rounded"></div>
              <div class="cs-skeleton-skeleton-loader__widget-thumb use-skeleton rounded"></div>
            </div>
          </div>
          <div class="cs-skeleton-skeleton-loader__preview">
            <div class="cs-skeleton-skeleton-loader__preview-heading">
              <div class="cs-skeleton-skeleton-loader__preview-heading__title">
                <div class="cs-skeleton-skeleton-loader__preview-heading__title-text use-skeleton rounded"></div>
                <div class="cs-skeleton-skeleton-loader__preview-heading__title-span use-skeleton rounded"></div>
              </div>
              <div class="cs-skeleton-skeleton-loader__preview-heading__btn use-skeleton rounded"></div>
            </div>
            <div class="cs-skeleton-skeleton-loader__preview-program use-skeleton rounded"></div>
          </div>
        </div>
      </div>
      <div class="card noboxshadow iframe-placeholder">
          <iframe
              data-hj-allow-iframe=""
              onload="scoachingScreensURI()"
              id="coachingScreensContainer"
              name="coachingScreensContainer"
              width="100%"
              height="100%"
              src="/coaching-screens/"
              frameborder="0"
              allowfullscreen
              allowtransparency="true"
              scrolling="yes"
          ></iframe>
      </div>
    </div>
</section>
<div id="modalBackdrop"></div>
