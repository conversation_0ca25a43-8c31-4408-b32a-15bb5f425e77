<section class="content screens-main-container device-management-screen full-height-content enable-scroll">
  <div class="body ics">
    <!-- Nav tabs -->
    <ul class="nav nav-tabs tab-nav-right" role="tablist">
      <li role="presentation" class="active">
        <a class="devices-config-tab" href="#devices" data-toggle="tab">{{Devices}}</a>
      </li>
      <!-- HEALTH CHECK will be hidden until ready -->
      <li role="presentation" class="hidden">
        <a class="devices-config-tab" href="#health-check" data-toggle="tab">{{Health Check}}</a>
      </li>
    </ul>

    <!-- Tab panes -->
    <div class="tab-content">
      <div class="nav-help-container">
        <button class="btn btn-xs btn-icon" style="width: 30px; height: 30px" role="button" data-toggle="tooltip"
          title="{{View Support Guides for Technology in Knowledge Base}}">
          <img src="/assets/images/search_black_24dp.svg" />
        </button>
      </div>

      <div role="tabpanel" class="tab-pane active" id="devices">
        <div class="search-and-filters-container">
          <div class="search-bar-container"></div>


          <div class="filter-section isLoading" id="device-filters">
            <div class="filters">
              <div class="filter-grp use-skeleton" id="filter-range">
                <p class="filter-label">{{Last Seen}}</p>
                <span class="filter-grp__divider"></span>
                <div class="filter">
                  <button type="button" class="filter__selected">{{This week}}</button>
                  <ul class="filter__options" data-filter="range">
                    <li class="filter__option filter__option--active" data-value="this week">
                      {{This week}}
                    </li>
                  </ul>
                </div>
              </div>

              <div class="filter-grp use-skeleton" id="filter-device-type">
                <p class="filter-label">{{Device Type}}</p>
                <span class="filter-grp__divider"></span>
                <div class="filter">
                  <button type="button" class="filter__selected">{{All}}</button>
                  <ul class="filter__options" data-filter="type">
                    <li class="filter__option filter__option--active" data-value="all">
                      {{All}}
                    </li>
                  </ul>
                </div>
              </div>

              <div class="filter-grp use-skeleton" id="filter-connection">
                <p class="filter-label">{{Network Source}}</p>
                <span class="filter-grp__divider"></span>
                <div class="filter">
                  <button type="button" class="filter__selected">{{All}}</button>
                  <ul class="filter__options" data-filter="connection">
                    <li class="filter__option filter__option--active" data-value="all">
                      {{All}}
                    </li>
                  </ul>
                </div>
              </div>
              <button class="filter-reset use-skeleton" id="reset-filter">
                {{Reset Filters}}
              </button>
            </div>
            <div class="internet-stats"></div>
          </div>
        </div>

        <table id="skeleton-loader" width="100%" class="isLoading table devices-table">
          <thead>
              <tr>
                  <th>
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Device}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded">X</span>
                      </div>
                  </th>
                  <th class="hidden-xs">
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Metadata}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
                  <th class="visible-xl">
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Local IP}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
                  <th class="hidden-sm hidden-xs">
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Signal Strength}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
                  <th class="visible-xl">
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Activity Down/Up}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
                  <th class="hidden-xs">
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Temperature}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
                  <th class="hidden-xs">
                      <div class="device-screen-table__header">
                          <span class="use-skeleton rounded">{{Uptime}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
                  <th class="visible-xl">
                      <div class="device-screen-table__header device-screen-table__header--justify-end">
                          <span class="use-skeleton rounded">{{Serial Number}}</span>
                          <span class="device-screen-table__header__icon use-skeleton rounded"></span>
                      </div>
                  </th>
              </tr>
          </thead>
          <tbody>
            <tr class="device d-status-true odd">
              <td class="device-status-badges device-cell sorting_1" data-order="2" data-search="Bathroom duress">
                  <div class="device-cell__device">
                      <div class="device-cell__device__icon duress use-skeleton rounded">
                          <span>{{duress}}</span>
                      </div>
                      <div class="device-cell__device__details">
                          <p class="device-cell__device__details__name">
                            <span class="use-skeleton rounded">{{Duress Button}}</span>
                          </p>
                          <div class="device-cell__device__details__status">
                              
                            <div class="device-cell__tag device-cell__tag--success use-skeleton rounded">
                                <span>{{Online}}</span>
                            </div>
                          </div>
                      </div>
                  </div>
              </td>
              
              <td class="hidden-xs device-cell" data-order="undefined">
                  <div class="device-cell__meta">
                      <span class="use-skeleton rounded">{{Room: Bathroom}}</span>
                  </div>
              </td>
              <td class="visible-xl device-cell">
                  <div class="device-cell__ip">
                      <span class="use-skeleton rounded">{{N/A}}</span>
                  </div>
              </td>
              <td class="hidden-sm hidden-xs device-cell" data-order="0">
                  <div class="device-cell__signal">
                      <p class="device-cell__signal__wifi">
                          <span class="use-skeleton rounded">{{Bluetooth Mesh}}</span>
                      </p>
                      <div class="device-cell__signal-grp">
                        <div class="wifi-experience-container-sm device-cell__signal__bar signal-ok use-skeleton rounded" data-signal="0">
                          <div class="device-cell__signal__quality" style="width: 93%"></div>
                        </div>
                        <span class="use-skeleton rounded">{{93%}}</span>
                      </div>
                  </div>
              </td>
              <td class="visible-xl device-cell" data-order="0">
                  <div class="device-cell__activity">
                      <span class="use-skeleton rounded">{{0 B / 0 B}}</span>
                  </div>
              </td>
              <td class="hidden-xs device-cell" data-order="23.75">
                  <div class="device-cell__temperature">
                      <span class="use-skeleton rounded">{{23.8°C}}</span>
                  </div>
              </td>
              <td class="hidden-xs device-cell" data-order="1700041501364">
                  <div class="device-cell__uptime">
                    <span class="use-skeleton rounded">{{Updated: 4 Minutes ago}}</span>
                  </div>
              </td>
              <td class="visible-xl device-cell device-cell--right">
                <div class="device-cell__serial">
                  <span class="use-skeleton rounded">{{F00241D69999}}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Array of devices & stats -->
        <div id="ics-devices-container"></div>
      </div>

      <!-- <div role="tabpanel" class="tab-pane iframe-wrapper" id="health-check"> -->
        <!-- <iframe -->
            <!-- data-hj-allow-iframe="" -->
            <!-- id="healthCheckContainer" -->
            <!-- name="healthCheckContainer" -->
            <!-- width="100%" -->
            <!-- height="100%" -->
            <!-- src="/health-check/" -->
            <!-- frameborder="0" -->
            <!-- allowfullscreen -->
            <!-- allowtransparency="true" -->
            <!-- scrolling="yes" -->
        <!-- ></iframe> -->
      <!-- </div> -->


    </div>
  </div>
</section>