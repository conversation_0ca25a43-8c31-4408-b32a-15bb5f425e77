<section class="cctv-ui__content full-height-content content is-relative" id="cctv">
  <div class="container-fluid">

    <div class="cctv-skeleton-loader isLoading">
      <div class="cctv-skeleton-loader__header">
        <div class="cctv-skeleton-loader__header__menu-wrapper">
          <div class="cctv-skeleton-loader__header__menu-item use-skeleton rounded"></div>
          <div class="cctv-skeleton-loader__header__menu-item use-skeleton rounded"></div>
          <div class="cctv-skeleton-loader__header__menu-item use-skeleton rounded"></div>
          <div class="cctv-skeleton-loader__header__menu-item use-skeleton rounded"></div>
          <div class="cctv-skeleton-loader__header__menu-item use-skeleton rounded"></div>
        </div>
        <div class="cctv-skeleton-loader__header__cog use-skeleton rounded--full"></div>
      </div>
      <div class="cctv-skeleton-loader__main-content">
        <div class="cctv-skeleton-loader__sidebar">
          <div class="cctv-skeleton-loader__sidebar__section-title use-skeleton rounded"></div>
          <div class="cctv-skeleton-loader__sidebar__progress-wrapper">
            <div class="cctv-skeleton-loader__sidebar__progress use-skeleton rounded"></div>
            <div class="cctv-skeleton-loader__sidebar__progress-label-wrapper">
              <div class="cctv-skeleton-loader__sidebar__progress-label use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__sidebar__progress-label use-skeleton rounded"></div>
            </div>
          </div>

          <div class="cctv-skeleton-loader__sidebar__item">
            <div class="cctv-skeleton-loader__sidebar__item__content">
              <div class="cctv-skeleton-loader__sidebar__item__title use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__sidebar__item__subtitle use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__sidebar__item__icon use-skeleton rounded"></div>
          </div>
          <div class="cctv-skeleton-loader__sidebar__item">
            <div class="cctv-skeleton-loader__sidebar__item__content">
              <div class="cctv-skeleton-loader__sidebar__item__title use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__sidebar__item__subtitle use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__sidebar__item__icon use-skeleton rounded"></div>
          </div>
          <div class="cctv-skeleton-loader__sidebar__item">
            <div class="cctv-skeleton-loader__sidebar__item__content">
              <div class="cctv-skeleton-loader__sidebar__item__title use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__sidebar__item__subtitle use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__sidebar__item__icon use-skeleton rounded"></div>
          </div>
          <div class="cctv-skeleton-loader__sidebar__item">
            <div class="cctv-skeleton-loader__sidebar__item__content">
              <div class="cctv-skeleton-loader__sidebar__item__title use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__sidebar__item__subtitle use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__sidebar__item__icon use-skeleton rounded"></div>
          </div>
          <hr class="cctv-skeleton-loader__hr" />
          <div class="cctv-skeleton-loader__sidebar__section-heading">
            <div class="cctv-skeleton-loader__sidebar__section-title use-skeleton rounded"></div>
            <div class="cctv-skeleton-loader__sidebar__section-subtitle use-skeleton rounded"></div>
          </div>
          <div class="cctv-skeleton-loader__sidebar__item-message">
            <div class="cctv-skeleton-loader__sidebar__item-message__title use-skeleton rounded"></div>
            <div class="cctv-skeleton-loader__sidebar__item-message__subtitle use-skeleton rounded"></div>
            <div class="cctv-skeleton-loader__sidebar__item-message__ip use-skeleton rounded"></div>
            <div class="cctv-skeleton-loader__sidebar__item-message__date use-skeleton rounded"></div>
          </div>
        </div>
        <div class="cctv-skeleton-loader__recording-wrapper">
          <div class="cctv-skeleton-loader__recordings">
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__recording">
              <div class="cctv-skeleton-loader__recording__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__recording__title use-skeleton rounded"></div>
            </div>
          </div>
          <div class="cctv-skeleton-loader__filters">
            <div class="cctv-skeleton-loader__filter">
              <div class="cctv-skeleton-loader__filter__icon use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__filter__content">
                <div class="cctv-skeleton-loader__filter__title use-skeleton rounded"></div>
                <div class="cctv-skeleton-loader__filter__subtitle use-skeleton rounded"></div>
              </div>
            </div>
            <div class="cctv-skeleton-loader__filter__divider"></div>
            <div class="cctv-skeleton-loader__filter cctv-skeleton-loader__filter--top">
              <div class="cctv-skeleton-loader__filter__content">
                <div class="cctv-skeleton-loader__filter__title use-skeleton rounded"></div>
              </div>
            </div>
          </div>
          <div class="cctv-skeleton-loader__timeline-wrapper">
            <div class="cctv-skeleton-loader__timeline">
              <div class="cctv-skeleton-loader__timeline__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__timeline__title use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__timeline__bar use-skeleton rounded"></div>
            </div>
            <div class="cctv-skeleton-loader__timeline">
              <div class="cctv-skeleton-loader__timeline__thumb use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__timeline__title use-skeleton rounded"></div>
              <div class="cctv-skeleton-loader__timeline__bar use-skeleton rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card noboxshadow iframe-placeholder" id="cctvContainerParent">
      <iframe data-hj-allow-iframe="" onload="sCCTVURI()" id="cctvContainer" name="cctvContainer" width=100% height=100%
        src="/cctv/" frameborder="0" allowfullscreen allowtransparency="true" scrolling="yes"></iframe>
    </div>

  </div>
</section>
<div id="modalBackdrop"></div>