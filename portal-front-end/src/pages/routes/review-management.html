<div class="review-management">
    <section class="ph-page">
        <div class="">
            <div class="block-header d-flex justify-between gap-2 align-end relative">
                <ul class="nav nav-tabs ph-nav-tabs tab-nav-right w-full" role="tablist">
                    <li role="presentation" class="active">
                        <a class="" href="" data-toggle="tab">{{Review Management}}</a>
                    </li>
                </ul>
                <div class="review-report-date-range">
                    <div id="reportrange" class="ph-daterange-picker">
                        <div class="selectbox">
                            <i class="material-icons">calendar_today</i>
                            <span></span>
                            <div class="daterange-arrow-icon">
                                <i class="material-icons">expand_more</i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="reviews-overview reviews-overview__row mt-6">
                      <div class="card ph-card ph-card load-on-init modified">
                          <div class="body">
                              <div id="total-reviews" class="stat-container">
                                  <label for="total-reviews">
                                    <span class="use-skeleton rounded">{{Total Reviews}}</span>
                                  </label>
                                  <span class="number use-skeleton rounded">0</span>
                                  <div class="small-stats" style="max-width: 100%;">
                                    <span class="use-skeleton">
                                      <span class="clickable filter-out-responded ph-badge badge badge-red" style="max-width: 100%; text-overflow: ellipsis; overflow: hidden;" title="69 ">
                                          69 
                                          <svg class="badge__icon" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M55.3419 44.0478L55.342 44.048C55.6852 44.6364 55.8672 45.3049 55.8696 45.9861C55.8721 46.6673 55.6949 47.3371 55.356 47.928C55.0171 48.5188 54.5284 49.01 53.9392 49.3518C53.35 49.6936 52.681 49.8741 51.9999 49.875H41.7975H41.6955L41.6751 49.975C41.2194 52.2046 40.0076 54.2085 38.2447 55.6476C36.4817 57.0867 34.2758 57.8727 32 57.8727C29.7243 57.8727 27.5184 57.0867 25.7554 55.6476C23.9925 54.2085 22.7807 52.2046 22.325 49.975L22.3046 49.875H22.2025H12.0001C11.3187 49.8745 10.6495 49.6943 10.06 49.3527C9.47042 49.011 8.98139 48.52 8.64219 47.929C8.30299 47.338 8.12561 46.6681 8.12795 45.9867C8.13029 45.3053 8.31226 44.6366 8.65551 44.048L8.65564 44.0478C10.0577 41.6327 12.125 34.8489 12.125 26C12.125 20.7288 14.219 15.6735 17.9463 11.9463C21.6736 8.21897 26.7289 6.125 32 6.125C37.2712 6.125 42.3265 8.21897 46.0538 11.9463C49.7811 15.6735 51.875 20.7288 51.875 26C51.875 34.8464 53.9399 41.6327 55.3419 44.0478ZM26.345 49.875H26.1683L26.2272 50.0417C26.6493 51.2355 27.431 52.2692 28.4648 53.0005C29.4987 53.7317 30.7337 54.1246 32 54.125H32.0001C33.2664 54.1246 34.5014 53.7317 35.5352 53.0005C36.569 52.2692 37.3508 51.2355 37.7729 50.0417L37.8318 49.875H37.655H26.345ZM11.892 45.9372L11.7827 46.125H12H52H52.2174L52.1081 45.9371C50.1929 42.6452 48.125 34.9993 48.125 26C48.125 21.7234 46.4262 17.6219 43.4021 14.5979C40.3781 11.5739 36.2766 9.875 32 9.875C27.7234 9.875 23.622 11.5739 20.5979 14.5979C17.5739 17.6219 15.875 21.7234 15.875 26C15.875 35.0068 13.8021 42.6527 11.892 45.9372Z" fill="currentColor" stroke="currentColor" stroke-width="0.25"></path></svg>
                                      </span>
                                    </span>
                                  </div>
                              </div>
                          </div>
                      </div>
                      
                      <div class="card ph-card load-on-init modified">
                          <div class="body">
                              <div id="average-rating" class="stat-container">
                                  <label for="average-rating">
                                    <span class="use-skeleton rounded">{{Average Rating}}</span>
                                  </label>
                                  <span class="number use-skeleton rounded">0</span>
                                  <span class="stars">
                                    <span class="material-icons use-skeleton" style="font-size: 1.2rem; color: gold;">star</span>
                                    <span class="material-icons use-skeleton" style="font-size: 1.2rem; color: gold;">star</span>
                                    <span class="material-icons use-skeleton" style="font-size: 1.2rem; color: gold;">star</span>
                                    <span class="material-icons use-skeleton" style="font-size: 1.2rem; color: gold;">star</span>
                                    <span class="material-icons use-skeleton" style="font-size: 1.2rem; color: gold;">star</span></span>
                              </div>
                          </div>
                      </div>

                      <div class="card ph-card load-on-init modified review-sources lg">
                          <div class="p-4">
                              <h5 class="agreements-sources-title" class="stat-container">
                                <span class="use-skeleton">{{Review Sources}}</span>
                              </h5>
                              <div class="review-source-chart">
                                <div style="height: 160px;" class="review-source-chart__wrapper">
                                    <div class="aspect-square rounded--full use-skeleton show-on-loading" style="margin-top: 10px"></div>
                                    <canvas width="140" id="reviews-by-source-chart"></canvas>
                                </div>
                                <ul class="review-source-chart__legends">
                                  <li class="lead-events-data__values-grp" style="color: rgb(255, 99, 132);">
                                    <span class="lead-events-data__values-grp__pill rounded--full use-skeleton">
                                      66.76%
                                    </span>
                                    <span class="lead-events-data__values-grp__label rounded use-skeleton">Google</span>
                                    <strong class="rounded use-skeleton">241</strong>
                                  </li>
                                  <li class="lead-events-data__values-grp" style="color: rgb(55, 162, 235);">
                                    <span class="lead-events-data__values-grp__pill rounded--full use-skeleton">
                                      33.24%
                                    </span>
                                    <span class="lead-events-data__values-grp__label rounded use-skeleton">Facebook</span>
                                    <strong class="rounded use-skeleton">120</strong>
                                  </li>
                                </ul>
                              </div>
                          </div>
                      </div>
                      
                      <div class="card ph-card load-on-init modified lg">
                          <div class="body">
                              <div class="d-flex flex-column">
                                  <div style="height: 160px;" class="use-skeleton">
                                      <canvas width="100%" id="reviews-by-date-graph"></canvas>
                                  </div>
                              </div>
                          </div>
                      </div>
                    </div>
                </div>
            </div>

            <div class="reviews-overview">
              <div class="card ph-card load-on-init modified member-topics"> 
                  <h4 class="text-center">
                    <span class="use-skeleton">{{What members are talking about}}</span>
                  </h4>
                  <div class="tags member-topics__tags">
                    <span class="badge use-skeleton">tags</span>
                    <span class="badge use-skeleton">tags</span>
                    <span class="badge use-skeleton">tags</span>
                    <span class="badge use-skeleton">tags</span>
                  </div>
              </div>
            </div>

            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="card ph-card load-on-init modified">
                        <div class="body">
                            <div class="social-reviews-nav mb-2"></div>
                            <div class="ph-alert ph-alert_info auto-publish-notif my-4 d-none">
                                <div class="ph-alert__title">
                                    <div class="ph-alert__icon">
                                        <i class="fa-solid fa-robot" style="color: #2262b3;" aria-hidden="true"></i>
                                    </div>
                                    {{Auto-publishing of reviews is disabled!}}
                                </div>
                                <div class="ph-alert__description d-flex align-center justify-between flex-gap-6">
                                    {{Reviews have been manually published to your Facilities Page. This will prevent the automatic publishing of any new reviews to your location}}
                                    <div class="d-flex flex-column gap-2 align-end">
                                        <div class="visible-xs visible-sm">
                                            <a class="btn btn-primary btn-outline enable-auto-publish"><b>{{Un-publish manually published reviews & re-enable auto-publishing}}</b></a>
                                        </div>
                                        <div class="pull-right hidden-xs hidden-sm">
                                            <a class="btn btn-primary btn-outline enable-auto-publish"><b>{{Un-publish manually published reviews & re-enable auto-publishing}}</b></a>
                                        </div>
                                    </div> 
                                </div>
                            </div>

                            <div id="reviews-table-container" class="ph-table-container --borderless"></div>
                            <div class="empty-table d-none">{{There are no reviews yet for this location.}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <input type="hidden" id="settingsJSON" />
    </section>

    <div class="save-overlay">
        <div class="spinner-container">
            <h3>One moment please...</h3>
            <div class="preloader pl-size-xl" style="padding: 10px">
                <img src="/assets/images/tail-spin.svg" />
            </div>
        </div>
    </div>
</div>
