<div class="club-management-page ph-page">
    <div class="save-overlay">
        <div class="spinner-container">
            <h3>{{One moment please}}...</h3>
            <div class="preloader pl-size-xl" style="padding: 10px">
                <img src="/assets/images/tail-spin.svg" />
            </div>
        </div>
    </div>

    <div class="club-details isLoading">
        <section> 
            <div>
                <div class="card card__no-ui">
                    <ul class="nav nav-tabs ph-nav-tabs tab-nav-right mb-6" role="tablist">
                        <li role="presentation" class="active">
                            <a class="" href="" data-toggle="tab">{{Facility Details}}</a>
                        </li>
                    </ul>
                    <form id="club-form" data-lpignore="true" autocomplete="off">
                        <div class="missing-club-kyc-config-alert-container"></div>
                        <div class="yext-notifications"></div>
                        <div class="shop-front-notifications"></div>
                        <div class="row clearfix">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ph-form">
                                <div class="row">
                                    <div class="col-sm-12 col-md-6">
                                        <h2 class="card-inside-title">
                                            <span class="use-skeleton rounded">{{Facility Status}}</span>
                                            <small class="use-skeleton rounded">
                                                {{You may un-publish your facility page from our website if it is not yet ready to
                                                be displayed, or has been temporarily closed.}}
                                            </small>
                                        </h2>
                                        <div class="demo-switch use-skeleton rounded">
                                            <div class="switch ph-switch">
                                                <label
                                                    >{{HIDDEN}}<input data-hj-allow type="checkbox" id="published" /><span
                                                        class="lever lever switch-col-blue use-skeleton rounded"
                                                    ></span
                                                    ><span class="use-skeleton rounded">{{PUBLISHED}}!</span></label
                                                >
                                            </div>
                                        </div>
                                        
                                        <div class="free-trial-grp">
                                          <div>
                                            <h2 class="card-inside-title use-skeleton rounded">
                                                {{Accept Free Trial Bookings}}
                                                <small>
                                                    {{Disable/Enable free trial bookings in the website.}}
                                                </small>
                                            </h2>
                                            <div class="demo-switch">
                                              <div class="switch ph-switch use-skeleton rounded">
                                                  <label
                                                      >{{DISABLE}}<input data-hj-allow type="checkbox" id="booking-widget" /><span
                                                          class="lever lever switch-col-blue"
                                                      ></span
                                                      >{{ENABLE}}</label
                                                  >
                                              </div>
                                            </div>
                                          </div>
                                          <div class="free-trial-grp__dropdown use-skeleton rounded">
                                            <label for="max_booking_days">{{Booking Window}} <i class="material-icons info-icon" data-toggle="tooltip" data-placement="right" title="{{Select the maximum time frame in advance for accepting bookings. This setting determines how far into the future prospects can schedule a workout/club booking.}}">info_outline</i></label>
                                            <div class="form-group ph-form use-skeleton rounded">
                                              <div class="form-line">
                                                  <select id="maxBookingDays" name="maxBookingDays" class="form-control show-tick" required>
                                                      <option value="5">{{5 days}}</option>
                                                      <option value="7">{{7 days}}</option>
                                                      <option value="10">{{10 days}}</option>
                                                      <option value="14">{{14 days (2 weeks)}}</option>
                                                      <option value="17">{{17 days (2.5 weeks)}}</option>
                                                      <option value="21">{{21 days (3 weeks)}}</option>
                                                  </select>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        
                                    </div>
                                    <div class="col-md-6">
                                        <div class="text-right hidden-xs hidden-sm" style="margin-right: 10px;">
                                            <span class="use-skeleton rounded">{{Facility Page}}:</span>
                                            <code id="clubPageURL" class="use-skeleton"></code>
                                        </div>
                                        <div class="text-right hidden-xs hidden-sm" style="margin-right: 10px;">
                                            <span class="use-skeleton rounded">{{Links App/Page}}:</span>
                                            <code id="clubLinksPageURL" class="use-skeleton"></code>
                                        </div>
                                        <div class="landing-pages-container text-right d-flex flex-column align-end" style="margin-right: 10px;">
                                            <hr class="ph-hr" />
                                        </div>
                                    </div>
                                </div>

                                <hr>
    
                                <div class="ph-form">
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Primary Details}}</span>
                                        <small class="use-skeleton rounded">
                                            {{Your main facilities listing details that are displayed on your brand's website, used in marketing assets, throughout the Performance Hub, and third party integrations.}}
                                        </small>
                                    </h2>
                                    <div class="row display-flex inputs-bottom">
                                        <div class="col-sm-4" style="min-height: 120px">
                                            <label for="club_brand"
                                                ><span class="use-skeleton rounded">{{Brand}}</span><br /><small
                                                    ><i
                                                        class="use-skeleton rounded">({{This will determine branding and your on-line presence}})</i
                                                    ></small
                                                ></label
                                            >
                                            <div class="form-group use-skeleton rounded">
                                                <div class="form-line use-skeleton rounded">
                                                    <select id="club_brand" class="form-control show-tick" required>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-sm-4" style="min-height: 120px">
                                            <label for="club_name"
                                                ><span class="use-skeleton rounded">{{Facility Name}}</span> <br /><small
                                                    ><i
                                                        class="use-skeleton rounded">({{Provide only the suburb/location of the businesses location}} - i.e.
                                                        &quot;Lower Manhattan&quot; or &quot;Soho&quot;)</i
                                                    ></small
                                                ></label
                                            >
                                            <div class="form-group use-skeleton rounded">
                                                <div class="form-line use-skeleton rounded">
                                                    <input
                                                        data-hj-allow
                                                        type="text"
                                                        id="club_name"
                                                        class="form-control"
                                                        placeholder="{{Example Location}}"
                                                        required
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-sm-4" style="min-height: 120px">
                                            <label for="email_address"
                                                ><span class="use-skeleton rounded">{{Primary Location Email}}</span> <br /><small
                                                    ><i
                                                        class="use-skeleton rounded">({{Appears on site, store orders & Marketing/Print
                                                        content}})</i
                                                    ></small
                                                ></label
                                            >
                                            <div class="form-group use-skeleton rounded">
                                                <div class="form-line use-skeleton rounded">
                                                    <input
                                                        data-hj-allow
                                                        type="text"
                                                        id="email_address"
                                                        class="form-control"
                                                        placeholder="{{<EMAIL>}}"
                                                        required
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row display-flex inputs-bottom">
                                        <div class="col-sm-4" style="min-height: 120px">
                                            <label for="club_phne"
                                                ><span class="use-skeleton rounded">{{Facility Phone}}</span> <br /><small
                                                    ><i
                                                        class="use-skeleton rounded">({{Number will be auto-formatted to E.164 - international
                                                        format}})</i
                                                    ></small
                                                ></label
                                            >
                                            <div class="form-group use-skeleton rounded">
                                                <div class="form-line use-skeleton rounded">
                                                    <input
                                                        data-hj-allow
                                                        type="text"
                                                        id="club_phne"
                                                        class="form-control mobile-phone-number"
                                                        placeholder="{{ ****** 456 7890 }}"
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-sm-4" style="min-height: 135px">
                                            <label
                                                ><span class="use-skeleton rounded">{{Facility ID &amp; Google Place ID}}</span><br /><small
                                                    class="use-skeleton rounded"><i>({{These IDs are for reference only and are used for internal purposes/systems}})</i></small
                                                ></label
                                            >
                                            <div class="form-group use-skeleton rounded">
                                                <div class="form-line">
                                                    <div class="d-flex flex-column gap-1">
                                                        <div class="club-id-container d-flex gap-1 align-center">
                                                        </div>
                                                        <div class="place-id-container d-flex gap-1 align-center">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-sm-4">
                                            <label for="secondaryEmails">
                                                <span class="use-skeleton rounded">{{URL Slug}}</span>
                                                    <br /><small class="use-skeleton rounded">
                                                        {{This is the URL slug for your facility page. It will be auto-generated based on the name of your facility, but can be changed to something more relevant.}}
                                                    </small>
                                            </label>
                                            <div class="input-group use-skeleton rounded">
                                                <div class="form-line slug-placeholder">
                                                    <input
                                                        type="text"
                                                        data-hj-allow
                                                        id="slug"
                                                        class="form-control"
                                                        placeholder=""
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-sm-4 admin-only">
                                            <label for="secondaryEmails">
                                                <span class="use-skeleton rounded">{{Other Associated Emails}}</span>
                                                    <br /><small class="use-skeleton rounded">
                                                        {{Use a comma}} <code>,</code> {{to separate emails}}, {{can only be Set/Visible to Administrators}}
                                                    </small>
                                            </label>
                                            <div class="input-group use-skeleton rounded">
                                                <span class="input-group-addon">
                                                    <i class="fa fa-inbox ph-icon-color"></i>
                                                </span>
                                                <div class="form-line secondaryEmails-placeholder">
                                                    <input
                                                        type="text"
                                                        data-hj-allow
                                                        id="secondaryEmails"
                                                        class="form-control"
                                                        placeholder=""
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <hr>
    
                                <div class="">
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Facility Location}}</span>
                                        <small class="use-skeleton rounded">
                                            {{Enter the exact address of your location - This will be displayed on the
                                            website, used in print material, Performance Hub store orders etc. You may
                                            manually adjust the Latitude & Longitude of your facilities
                                            "Marker" if it's incorrectly positioned.}}
                                        </small>
                                    </h2>

                                    <div class="row">
                                        <div class="col-lg-4 col-sm-12" style="min-height: 120px">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <p>
                                                        <b class="use-skeleton rounded">{{Address Type}}:</b>
                                                    </p>
                                                    <div class="switch ph-switch use-skeleton rounded">
                                                        <label>
                                                            {{AUTO DETECT}}
                                                            <input
                                                                data-hj-allow
                                                                type="checkbox"
                                                                id="manual-address-toggle"
                                                            />
                                                            <span class="lever lever switch-col-blue"></span>
                                                            {{MANUAL INPUT}}
                                                        </label>
                                                    </div>
                                                    <div class="card-inside-title --compact">
                                                        <small class="use-skeleton rounded">
                                                            {{Auto address type will use a Google autocomplete to fetch
                                                            location if your facility by default. Toggle to 'Manual Input' to
                                                            enter your address if exact address is not found via
                                                            Autocomplete.}}
                                                        </small>
                                                    </div>                                                    
                                                </div>
                                                <div class="col-sm-12">
                                                    <div class="collapse auto-address-inputs">
                                                        <label for="display_address"
                                                            ><span class="use-skeleton rounded">{{Display Address}}</span><br /><small
                                                                ><i
                                                                    class="use-skeleton rounded">({{Location is automatically set from address}})</i
                                                                ></small
                                                            ></label
                                                        >
                                                        <div class="form-group use-skeleton rounded">
                                                            <div class="form-line use-skeleton rounded">
                                                                <input
                                                                    data-hj-allow
                                                                    data-op-ignore
                                                                    type="text"
                                                                    id="display_address"
                                                                    class="form-control required"
                                                                    placeholder="{{Start typing your address}}"
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="collapse manual-address-inputs">
                                                        <div>
                                                            <label for="manual_address1"><span class="use-skeleton rounded">{{Address}}</span></label>
                                                            <div class="form-group use-skeleton rounded">
                                                                <div class="form-line">
                                                                    <input
                                                                        data-hj-allow
                                                                        data-op-ignore
                                                                        type="text"
                                                                        id="manual_address1"
                                                                        name="manual_address1"
                                                                        class="form-control required"
                                                                        placeholder="{{Start typing your address}}"
                                                                        autocomplete="off"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <label for="manual_address2"><span class="use-skeleton rounded">{{Address 2}}</span></label>
                                                            <div class="form-group use-skeleton rounded">
                                                                <div class="form-line">
                                                                    <input
                                                                        data-hj-allow
                                                                        data-op-ignore
                                                                        type="text"
                                                                        id="manual_address2"
                                                                        class="form-control"
                                                                        placeholder="{{Start typing your address}}"
                                                                        autocomplete="off"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="d-flex gap-1">
                                                            <div>
                                                                <label for="manual_address_country"><span class="use-skeleton rounded">{{Country}}</span></label>
                                                                <div class="form-group use-skeleton rounded">
                                                                    <div class="form-line">
                                                                        <input
                                                                            data-hj-allow
                                                                            data-op-ignore
                                                                            type="text"
                                                                            id="manual_address_country"
                                                                            class="form-control required"
                                                                            placeholder="{{Country}}"
                                                                            autocomplete="off"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label for="manual_address_state_long" class="use-skeleton">{{State/Province}}</label>
                                                                <div class="form-group use-skeleton rounded">
                                                                    <div class="form-line">
                                                                        <input
                                                                            data-hj-allow
                                                                            data-op-ignore
                                                                            type="text"
                                                                            id="manual_address_state"
                                                                            class="form-control required"
                                                                            placeholder="{{State}}"
                                                                            autocomplete="off"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="d-flex gap-1">
                                                            <div>
                                                                <label for="manual_address_city" class="use-skeleton">{{City}}</label>
                                                                <div class="form-group use-skeleton rounded">
                                                                    <div class="form-line">
                                                                        <input
                                                                            data-hj-allow
                                                                            data-op-ignore
                                                                            type="text"
                                                                            id="manual_address_city"
                                                                            class="form-control required"
                                                                            placeholder="{{City}}"
                                                                            autocomplete="off"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label for="manual_address_postcode" class="use-skeleton">{{Postcode}}</label>
                                                                <div class="form-group use-skeleton rounded">
                                                                    <div class="form-line">
                                                                        <input
                                                                            data-hj-allow
                                                                            data-op-ignore
                                                                            type="text"
                                                                            id="manual_address_postcode"
                                                                            class="form-control required"
                                                                            placeholder="{{Postcode}}"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-sm-12">
                                                    <label
                                                        ><span class="use-skeleton rounded">{{Marker Latitude / Longitude}}</span><br /><small
                                                            ><i
                                                                class="use-skeleton rounded">({{These can be adjusted by dragging the marker on the
                                                                map}})</i
                                                            ></small
                                                        ></label
                                                    >
                                                    <div class="form-group use-skeleton rounded">
                                                        <div class="form-line">
                                                            <input
                                                                data-hj-allow
                                                                type="text"
                                                                id="lat_lng"
                                                                class="form-control"
                                                                disabled
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-lg-8 col-sm-12 use-skeleton rounded" style="min-height: 120px">
                                            <div id="clubLocationMap"></div>
                                        </div>
                                    </div>
                                </div>

                                <hr>
    
                                <div class="club-hours-block">
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Facility Hours}}</span>
                                        <small class="use-skeleton rounded">
                                            {{Updates to facilities hours will automatically be published to third party
                                            directories via our Digital Profile & Reputation Management Solution.}}
                                        </small>
                                    </h2>
                                    <div class="demo-switch">
                                        <div class="switch ph-switch use-skeleton rounded">
                                            <label>
                                                {{CLOSED / OPENING SOON}}
                                                <input
                                                    data-hj-allow
                                                    type="checkbox"
                                                    id="isOpen"
                                                />
                                                <span class="lever lever switch-col-blue"></span>
                                                {{OPEN}}
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mt-2">
                                        <label for="club_timezone">
                                            <span class="use-skeleton rounded">{{Timezone}}</span>
                                            <i
                                                class="material-icons info-icon use-skeleton rounded--full"
                                                data-toggle="tooltip"
                                                data-placement="right"
                                                title="{{Timezone is auto-set from your location's Latitude and Longitude, unless overwritten here. The correct timezone for your Club's location is required to enable in-club automations such as powering on/off your Displays Sonos etc}}"
                                                >info_outline</i
                                            >
                                        </label>
                                        <div class="form-group use-skeleton rounded" style="max-width: 350px; width: 100%;">
                                            <div class="form-line">
                                                <input
                                                    data-hj-allow
                                                    id="club_timezone"
                                                    class="form-control autocomplete"
                                                    placeholder="{{Search Timezones}}..."
                                                    type="text"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="opening-soon-container">
                                        <h2 class="card-inside-title">
                                            <span class="use-skeleton rounded">{{Placeholder Text}}</span>
                                            <small class="use-skeleton rounded">
                                                {{If your Club is not yet open, you can add some placeholder text - By
                                                default it's "Opening Soon"}}
                                            </small>
                                        </h2>
                                        <div class="form-group use-skeleton rounded">
                                            <div class="form-line">
                                                <textarea
                                                    data-hj-allow
                                                    rows="4"
                                                    class="form-control no-resize"
                                                    id="placeholderText"
                                                    placeholder='{{Provide your message}}: &#10;&#10;&#8226; {{This can be left empty and it will default to "Opening Soon"}}...&#10;&#8226; {{You can also write a temporarily closed message here such as "Closed due to COVID-19"}}'
                                                ></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <ul class="nav nav-tabs ph-nav-tabs tab-nav-right club-hours-tab" role="tablist">
                                        <li role="presentation" class="active use-skeleton rounded">
                                            <a href="#club-hours" data-toggle="tab">{{Staffed Hours}}</a>
                                        </li>
                                        <li role="presentation" class="use-skeleton rounded">
                                            <a href="#extended-hours" data-toggle="tab">{{Extended Hours}}</a>
                                        </li>
                                        <li role="presentation" class="use-skeleton rounded">
                                            <a id="holiday-hours-tab" href="#holiday-hours" data-toggle="tab">{{Holiday Hours}}</a>
                                        </li>
                                    </ul>

                                    <div class="tab-content club-hours-tab-content">
                                        <div role="tabpanel" class="tab-pane in active" id="club-hours">
                                            <div class="opening-hours-container">
                                                <div id="club-opening-hours"></div>
                                                <h2 class="card-inside-title">
                                                    <span class="use-skeleton rounded">{{Additional Comments}}</span>
                                                    <small class="use-skeleton rounded">
                                                        {{Enter any optional text you wish to display under the hours section on your website, such as directions to the Facility (if in a Mall), or special comments such as 'Daylight Savings Hours Change Notice' etc}}...
                                                    </small>
                                                </h2>
                                                <div class="form-group use-skeleton rounded">
                                                    <div class="form-line">
                                                        <textarea
                                                            rows="4"
                                                            class="form-control no-resize"
                                                            id="clubHoursComments"
                                                            placeholder="{{Type any additional comments (can be left empty if none)}}..."
                                                            data-hj-allow
                                                        ></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div role="tabpanel" class="tab-pane fade" id="extended-hours">
                                            <div class="demo-switch" style="margin-bottom: 4px;">
                                                <div class="switch ph-switch use-skeleton rounded">
                                                    <label>
                                                        {{DISABLE}}
                                                        <input data-hj-allow type="checkbox" class="enable-switch" />
                                                        <span class="lever lever switch-col-blue"></span>
                                                        {{ENABLE}}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="alert alert-blue no-device-alert hidden">
                                                <i class="fa fa-bell use-skeleton rounded--full" style="color: #2262b3" aria-hidden="true"></i>
                                                {{To enable Extended Access Hours you must first setup your CCTV Devices,
                                                and ensure you have panic lanyards associated with your Club.}}
                                            </div>
                                            <div id="extended-access-hours"></div>
                                        </div>
                                        <div role="tabpanel" class="tab-pane fade" id="holiday-hours">
                                            <div id="default-club-blocks">
                                                <label><span class="use-skeleton rounded">{{Default Holiday Hours}}</span></label>
                                                <br />
                                                <small class="use-skeleton rounded">
                                                    {{Your Default Holiday hours will be automatically applied to any
                                                    upcoming holidays based on your region.}}
                                                </small>
                                                <div class="tags"></div>
                                                <ul class="block-list"></ul>
                                                <a href="#" class="input-default-hours use-skeleton rounded" data-name="Default hours">
                                                    <span>
                                                        <i class="material-icons">add</i>
                                                        <span>{{add hours}}</span>
                                                    </span>
                                                </a>
                                            </div>
                                            <hr />
                                            <label><span class="use-skeleton rounded">{{Upcoming Holidays}}</span></label>
                                            <br />
                                            <small class="use-skeleton rounded">
                                                {{Public holidays will be automatically added to your listing two
                                                months in advance. You may add custom hours to these holidays should
                                                you wish to use hours other than the Default Holiday Hours.}}
                                            </small>
                                            <div id="special-club-hours">
                                                <div class="add-special-hour-container">
                                                    <div class="card add-special-hour use-skeleton rounded">
                                                        <i class="material-icons">add_circle</i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <hr>
    
                                <div>
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Social URLs}}</span>
                                        <small class="use-skeleton rounded">
                                            {{URL's not provided will result in the corresponding buttons/elements on your facilities page from being hidden.}}
                                        </small>
                                    </h2>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="facebook_url" class="d-flex align-items-center flex-gap-2">
                                                <span class="use-skeleton rounded">{{Facebook Handle}}</span>
                                                <i
                                                    class="material-icons info-icon use-skeleton rounded--full ph-icon-color"
                                                    data-toggle="tooltip"
                                                    data-placement="left"
                                                    title="{{Please specify your Facebook URL 'Handle' not the full URL to the page}}"
                                                    >info_outline</i
                                                >
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-addon use-skeleton rounded">
                                                    <i class="fa fa-facebook ph-icon-color" style="margin-bottom: 30px"></i>
                                                </span>
                                                <div class="form-group use-skeleton rounded">
                                                    <div class="form-line">
                                                        <input
                                                            style="text-transform: lowercase"
                                                            data-hj-allow
                                                            type="text"
                                                            id="facebook_url"
                                                            class="form-control"
                                                            placeholder="{{EXAMPLE}}: ubxaustralia"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="instagram_url" class="d-flex align-items-center flex-gap-2">
                                                <span class="use-skeleton rounded">{{Instagram Handle}}</span>
                                                <i
                                                    class="material-icons info-icon use-skeleton rounded--full ph-icon-color"
                                                    data-toggle="tooltip"
                                                    data-placement="bottom"
                                                    title="{{Please specify your Instagram handle not the full URL}}"
                                                    >info_outline</i
                                                >
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-addon use-skeleton rounded">
                                                    <i class="fa fa-instagram ph-icon-color" style="margin-bottom: 30px"></i>
                                                </span>
                                                <div class="form-group use-skeleton rounded">
                                                    <div class="form-line">
                                                        <input
                                                            style="text-transform: lowercase"
                                                            data-hj-allow
                                                            type="text"
                                                            id="instagram_url"
                                                            class="form-control"
                                                            placeholder="{{EXAMPLE}}: ubxaustralia"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <hr>
    
                                <div class="digital-profile-config-container">
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Digital Profile Configuration}}</span>
                                        <small class="use-skeleton rounded">
                                            {{Information configured/uploaded here (along with your facilities core details)
                                            will published to our websites, Google & Apple map/business services and
                                            other directories.}}
                                        </small>
                                    </h2>

                                    <div class="ph-alert ph-alert_info use-skeleton rounded mb-6">
                                        <div class="ph-alert__title">
                                            <div class="__title-icon">
                                                <i class="fa fa-bell" aria-hidden="true"></i>
                                            </div>
                                            <span>{{Additional Listing Images}}</span>
                                        </div>
                                        <div class="ph-alert__description">
                                            {{"Additional Listing Images" will be added/appended to your profiles (Google My
                                            Business, Facebook, etc) and will not replace any existing photos uploaded.
                                            Changes to "Cover Images" will however update or replace your existing picture
                                            on various social networks.}}
                                        </div>
                                    </div>

                                    <div class="digital-profile-config">
                                        <div id="images" style="display: flex; flex-wrap: wrap; gap: 2rem">
                                            <div id="single-images">
                                                <div class="image-column">
                                                    <label><span class="use-skeleton rounded">{{Primary Logo}}</span></label>
                                                    <div class="image-container logo-image-container"></div>
                                                </div>
                                                <div class="image-column">
                                                    <label><span class="use-skeleton rounded">{{Shop Front}}</span></label>
                                                    <div class="image-container shop-front-image-container"></div>
                                                </div>
                                                <div class="image-column">
                                                    <label><span class="use-skeleton rounded">{{Cover Image}}</span></label>
                                                    <div class="image-container cover-image-container"></div>
                                                </div>
                                            </div>
                                            <div id="additional-images">
                                                <label><span class="use-skeleton rounded">{{Additional Listing Images}}</span></label>
                                                <div class="image-list">
                                                    <div class="image-container additional-image-container">
                                                        <div class="card image empty">
                                                            <i class="material-icons use-skeleton rounded--full">insert_photo</i>
                                                        </div>
                                                        <i class="material-icons update-image use-skeleton rounded--full">camera_alt</i>
                                                        <input
                                                            data-hj-allow
                                                            type="file"
                                                            data-type="{{ADDITIONAL}}"
                                                            style="display: none"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="digital-profile-settings"></div>
                                    </div>

                                    <div>
                                        <label> <span class="use-skeleton rounded">{{Accepted Payment Methods}}</span> </label>
                                        <br />
                                        <small class="use-skeleton rounded">
                                            {{This information is displayed on your Google listing, and other directories
                                            and not in any way linked to your Member Management system.}}
                                        </small>
                                        <div class="payment-methods"></div>
                                    </div>

                                    <div class="form-group use-skeleton rounded">
                                        <label for="description">{{About the Business}}</label>
                                        <div class="form-line">
                                            <textarea
                                                data-hj-allow
                                                id="description"
                                                class="form-control"
                                                rows="6"
                                                style="resize: vertical"
                                                minlength="200"
                                            ></textarea>
                                        </div>
                                    </div>

                                    <div class="form-group use-skeleton rounded">
                                        <label for="facebookAbout">{{About the Business}} (facebook)</label>
                                        <br />
                                        <small>
                                            {{Facebook requires its about info to be less than or equal 100 characters}}
                                        </small>
                                        <div class="form-line">
                                            <textarea
                                                data-hj-allow
                                                id="facebook-about"
                                                class="form-control"
                                                rows="6"
                                                style="resize: vertical"
                                                minlength="50"
                                                maxlength="100"
                                            ></textarea>
                                        </div>
                                    </div>
                                </div>

                                <hr>
    
                                <div class="custom-tracking">
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Custom Tracking Code & Pixels}}</span>
                                        <small class="use-skeleton rounded">
                                            {{You may add multiple tracking codes/pixels to your facilities website page. Code added, will be executed based on the order added/ordered here.}}
                                        </small>
                                        <small class="use-skeleton rounded">
                                            {{Users from countries that must adhere to GDPR requirements must consent to cookies before any custom tracing codes/pixels will be executed on your facility page.}}
                                        </small>
                                    </h2>
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <div class="tracking-contents" id="tracking-contents">
                                                <div>
                                                    <ol id="tracking-items" class="tracking-items"></ol>
                                                    <button
                                                        class="btn btn-block btn-primary btn-outline use-skeleton rounded"
                                                        type="button"
                                                        id="add-tracking"
                                                    >
                                                        <span>{{Add tracking}}</span>
                                                        <i class="material-icons">add_circle</i>
                                                    </button>
                                                </div>

                                                <div class="tracking-form ph-card" id="tracking-form">
                                                    <div class="fld-grp use-skeleton rounded">
                                                        <label for="tracking-name">{{Code/Pixel Name}}</label>
                                                        <input
                                                            data-hj-allow
                                                            type="text"
                                                            id="tracking-name"
                                                            class="form-control"
                                                            placeholder="{{Example: Google Ads Remarketing}}"
                                                        />
                                                    </div>
                                                    <div class="fld-grp use-skeleton rounded">
                                                        <label for="tracking-code">{{Tracking Code (HTML)}}</label><br>
                                                        <small>{{Copy and paste your HTML/Javascript below. Be sure to wrap Javascript snippets with}} &#x3C;script&#x3E;&#x3C;/script&#x3E;.</small>
                                                        <div id="tracking-code-wrapper">
                                                            <textarea data-hj-allow id="tracking-code" placeholder="{{Paste provided tracking code here}}" class=""></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="tracking-form-options">
                                                        <div class="tracking-form-status styled-checkbox use-skeleton rounded">
                                                            <input
                                                                type="checkbox"
                                                                id="tracking-status"
                                                                value="published"
                                                                class="filled-in chk-col-blue"
                                                                name="tracking-status"
                                                            />
                                                            <label for="tracking-status" class="checkbox-label">
                                                                {{Published}}
                                                            </label>
                                                        </div>
                                                        <div class="tracking-form-actions">
                                                            <button
                                                                id="cancel-form"
                                                                class="btn btn-default waves-effect use-skeleton rounded"
                                                                type="button"
                                                            >
                                                                {{Discard}}
                                                            </button>
                                                            <button
                                                                class="btn btn-primary waves-effect use-skeleton rounded"
                                                                type="button"
                                                                id="save-tracking"
                                                            >
                                                                {{Add}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div id="tracking-form-error">{{All fields are required}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <div class="line-chat-integration">
                                    <h2 class="card-inside-title">
                                        <span class="use-skeleton rounded">{{Line Chat Integration}}</span>
                                        <small class="use-skeleton rounded">
                                            {{You may toggle on/off the Line Chat Integration to display a QR code and a Button code on the website.}}
                                        </small>
                                    </h2>
                                    <div class="demo-switch">
                                        <div class="switch ph-switch use-skeleton rounded">
                                            <label>
                                                {{Disabled}}
                                                <input data-hj-allow type="checkbox" name="line-chat-enabled" />
                                                <span class="lever lever switch-col-blue"></span>
                                                {{Enabled}}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="input-collapse collapse">
                                        <div class="input-container"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="save-club-footer" class="pinned-footer form-save">
                            <a class="btn btn-default waves-effect btn-cancel use-skeleton rounded" href="/club-details" role="button">{{Cancel}}</a>
                            <button class="btn btn-primary waves-effect btn-save use-skeleton rounded" type="submit">{{Save/Publish Changes}}</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </div>

</div>