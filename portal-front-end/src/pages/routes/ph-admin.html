<div id="ph-admin">
    <section class="content">
        <div class="container-fluid">
            <div class="block-header">
                <h2>Performance Hub System Configuration</h2>
            </div>
            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">


                    <div class="card">
                        <div class="body">

                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs tab-nav-right" role="tablist">
                                <li role="presentation" class="active">
                                    <a href="#db-sync" data-toggle="tab">Database Sync</a>
                                </li>
                                <li role="presentation">
                                    <a href="#system-triggers" data-toggle="tab">System Triggers</a>
                                </li>
                            </ul>

                            <!-- Tab panes -->
                            <div class="tab-content">

                                <div role="tabpanel" class="tab-pane in active" id="db-sync">
                                    <h3>Production -> Stage | Stage -> Local Dev DB Table Sync</h3>
                                    <p>Note that when running locally (so local dev mode) this UI will let you sync STAGE tables to your LOCAL dev</p>
                                    <div class="db-table-list"></div>
                                </div>

                                <div role="tabpanel" class="tab-pane fade" id="system-triggers">
                                    <div class="">

                                        <h4><span style="color: red">WARNING: Do run any of these triggers without confirmation from the system administrator first!</span></h4>

                                        <ul class="list-group">
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="min-width: 300px;" href="/api/admin/tasks/sync-gm-data" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-database" aria-hidden="true"></i> Force Sync Member Data from GymMaster
                                                    </a>
                                                    &nbsp;
                                                    <a style="min-width: 300px;" href="/api/admin/tasks/sync-gm-revenue-history" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-database" aria-hidden="true"></i> Force Sync Revenue History from GymMaster
                                                    </a>
                                                </p>
                                                <p>
                                                    Manually trigger a complete sync from GymMaster (All Clubs)
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/sms-schedule" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-commenting-o" aria-hidden="true"></i> Run SMS Schedule NOW
                                                    </a>
                                                </p>
                                                <p>
                                                    Sends any system generated scheduled/queued messages for free workouts, booking reminders, automated updates to club hours, etc immediately 
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/update-all-clubs-yext-data" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <svg style="height:18px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 720 720">
                                                            <path d="M360 0C161.18 0 0 161.18 0 360s161.18 360 360 360 360-161.18 360-360S558.82 0 360 0zm0 691.2C177.08 691.2 28.8 542.92 28.8 360S177.08 28.8 360 28.8 691.2 177.08 691.2 360 542.92 691.2 360 691.2z"></path>
                                                            <path d="M370.8 399.6h64.8v129.6h28.8V399.6h64.8v-28.8H370.8zM332.43 367.2L270 429.64l-62.43-62.44-20.37 20.37L249.64 450l-62.44 62.43 20.37 20.37L270 470.36l62.43 62.44 20.37-20.37L290.36 450l62.44-62.43zM448.2 349.2c44.73 0 81-36.27 81-81h-28.8c0 28.83-23.37 52.2-52.2 52.2-8.23 0-16.01-1.91-22.93-5.3l69.83-69.83 21.08-21.08c-14.44-22.25-39.48-36.98-67.98-36.98-44.74 0-81 36.27-81 81s36.26 80.99 81 80.99zm0-133.2c10.12 0 19.56 2.89 27.56 7.88l-71.88 71.88c-4.99-8-7.87-17.44-7.87-27.56-.01-28.83 23.36-52.2 52.19-52.2zM270 259.58l-60.74-72.38-22.06 18.51 68.4 81.52v61.97h28.8v-61.97l68.4-81.52-22.06-18.51z"></path>
                                                            <g>
                                                            <path d="M648 633.6c-23.86 0-43.2 19.34-43.2 43.2S624.14 720 648 720s43.2-19.34 43.2-43.2-19.34-43.2-43.2-43.2zm0 82.94c-21.95 0-39.74-17.79-39.74-39.74 0-21.95 17.79-39.74 39.74-39.74 21.95 0 39.74 17.79 39.74 39.74 0 21.95-17.79 39.74-39.74 39.74z"></path>
                                                            <path d="M664.93 698.4h-3.96l-.03-.05-10.3-17.59H637.2v17.64h-3.44v-43.2h17.05c7.06 0 12.81 5.75 12.81 12.81 0 5.7-3.87 10.73-9.41 12.29l10.72 18.1zm-27.73-20.89h13.61c5.17 0 9.37-4.26 9.37-9.49 0-5.27-4.2-9.56-9.37-9.56H637.2v19.05z"></path>
                                                            </g>
                                                        </svg>
                                                         Update YEXT
                                                    </a>
                                                </p>
                                                <p>
                                                    Starts the schedule which publishes/pushes all club data such as location hours, etc to Yext
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/club-holidays" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa fa-calendar" aria-hidden="true"></i> Update Club Holiday Hours
                                                    </a>
                                                </p>
                                                <p>
                                                    Starts a check across all clubs to ensure no outstanding custom holiday hours are outstanding.
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/country-holidays" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-refresh" aria-hidden="true"></i> Sync Upcoming Holidays
                                                    </a>
                                                </p>
                                                <p>
                                                    Pulls any upcoming holidays (for all regions clubs reside in) from third party 'public holiday api'
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/published-reviews" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-globe" aria-hidden="true"></i> Auto Publish Reviews to Club Pages
                                                    </a>
                                                </p>
                                                <p>
                                                    Starts the schedule which publishes any new '5 star' reviews that meet the minimum length requirements to the main website
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/update-social-reviews" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-refresh" aria-hidden="true"></i> Pull new social reviews from YEXT
                                                    </a>
                                                </p>
                                                <p>
                                                    Fetches any new reviews from third party directories such as GMB or Facebook for all club locations
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="/api/admin/tasks/build-peak-time-insights" target="_blank" class="btn btn-default btn-lg waves-effect">
                                                        <i class="fa fa-database" aria-hidden="true"></i> Rebuild 'Peak Time Insights'
                                                    </a>
                                                </p>
                                                <p>
                                                    This will pull all the member visit history for the last 2 months from each club and then build the visit peak time graphs that are displayed on the clubs dashboard. 
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                                <p>
                                                    <a style="width: 300px;" href="#" class="btn btn-default btn-lg waves-effect monthly-biller-trigger">
                                                        <i class="fa fa-credit-card" aria-hidden="true"></i> Run Monthly Billing
                                                    </a>
                                                </p>
                                                <p>
                                                    This will run the monthly billing process for all clubs that are using the SaaS billing system.
                                                </p>
                                            </li>
                                            <li class="list-group-item">
                                              <p>
                                                  <a style="width: 300px;" href="#" class="btn btn-default btn-lg waves-effect marketing-print-index-trigger">
                                                    <i class="fa fa-refresh" aria-hidden="true"></i> Run Marketing Print Index
                                                  </a>
                                              </p>
                                              <p>
                                                  This will update the Marketing Print index in Algolia for all clubs.
                                              </p>
                                          </li>
                                        </ul>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
