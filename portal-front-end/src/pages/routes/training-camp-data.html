<div id="training-camp-member-info-page" class="ph-page">
	<section class="content screens-main-container full-height-content smallSidebarContent enable-scroll no-animate">
		<div class="breadcrumbs-container mb-4">
		</div>
		<div class="d-flex flex-column flex-gap-8 w-fit">
			<div class="ph-card max-w-lg w-auto">
				<div class="ph-card_header --header-surface">
					<h3>{{Member Information}}</h3>
				</div>
				<div class="ph-card_body">
					<div class="mem-info_body d-grid grid-cols-3">
						<div class="mem-info_item">
							<div class="mem-info_label">Name</div>
							<div class="mem-info_value">John <PERSON></div>
						</div>
						<div class="mem-info_item">
							<div class="mem-info_label">Name</div>
							<div class="mem-info_value"><PERSON></div>
						</div>
						<div class="mem-info_item">
							<div class="mem-info_label">Name</div>
							<div class="mem-info_value">John <PERSON></div>
						</div>
						<div class="mem-info_item">
							<div class="mem-info_label">Name</div>
							<div class="mem-info_value">John Doe</div>
						</div>
						<div class="mem-info_item">
							<div class="mem-info_label">Name</div>
							<div class="mem-info_value">John Doe</div>
						</div>
						<div class="mem-info_item">
							<div class="mem-info_label">Name</div>
							<div class="mem-info_value">John Doe</div>
						</div>
					</div>
				</div>
			</div>
			<div class="ph-card w-fit max-w-lg" id="training-camp-info-card">
				<div class="ph-card_header --header-surface">
					<h3>{{Training Camp}}</h3>
					<div id="training-camp-info-select-container"></div>
				</div>
				<div class="ph-card_body flex-column flex-gap-8">
					<div class="d-flex flex-column flex-gap-8 tc-info-body">
					</div>
				</div>
			</div>
		</div>
	</section>
</div>