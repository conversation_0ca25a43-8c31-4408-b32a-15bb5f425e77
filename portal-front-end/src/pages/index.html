<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-theme-classic" />

<script src="https://cdn.jsdelivr.net/npm/algoliasearch@4.20.0/dist/algoliasearch-lite.umd.js" integrity="sha256-DABVk+hYj0mdUzo+7ViJC6cwLahQIejFvC+my2M/wfM=" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-plugin-recent-searches"></script> -->

<script>
  const { autocomplete, getAlgoliaResults } = window['@algolia/autocomplete-js'];
  // const {createLocalStorageRecentSearchesPlugin} = window['@algolia/autocomplete-plugin-recent-searches'];
</script>

<script>
  var ALGOLIA_INSIGHTS_SRC = "https://cdn.jsdelivr.net/npm/search-insights@2.8.3/dist/search-insights.min.js";

  !function(e,a,t,n,s,i,c){e.AlgoliaAnalyticsObject=s,e[s]=e[s]||function(){
  (e[s].queue=e[s].queue||[]).push(arguments)},e[s].version=(n.match(/@([^\/]+)\/?/) || [])[1],i=a.createElement(t),c=a.getElementsByTagName(t)[0],
  i.async=1,i.src=n,c.parentNode.insertBefore(i,c)
  }(window,document,"script",ALGOLIA_INSIGHTS_SRC,"aa");
</script>

<main-container></main-container>

<!-- HTML5 Preconnect references for the various tabs in the portal... -->
<link href="https://calendar.google.com/calendar/embed" rel="preconnect" crossorigin>

<!-- Google APIs -->
<link href="https://maps.googleapis.com/" rel="preconnect" crossorigin>
<link href="https://apis.google.com/" rel="preconnect" crossorigin>