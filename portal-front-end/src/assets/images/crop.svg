<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="7.63mm" width="7.63mm" version="1.1" id="Layer_1" x="0px" y="0px" viewBox="0 0 21.6 21.6" style="enable-background:new 0 0 21.6 21.6;" xml:space="preserve">
<style type="text/css">
  .st0{clip-path:url(#SVGID_2_);fill:none;stroke:#FFFFFF;stroke-width:1.25;stroke-miterlimit:10;}
  .st1{clip-path:url(#SVGID_2_);fill:none;stroke:#000000;stroke-width:0.25;stroke-miterlimit:10;}
</style>
<g>
  <defs>
    <rect id="SVGID_1_" width="461.5" height="339.6"/>
  </defs>
  <clipPath id="SVGID_2_">
    <use xlink:href="#SVGID_1_" style="overflow:visible;"/>
  </clipPath>
  <path class="st0" d="M440.5,324.6v15 M440.5,15V0 M21,324.6v15 M21,15V0 M446.5,318.6h15 M15,318.6H0 M446.5,21h15 M15,21H0"/>
  <path class="st1" d="M440.5,324.6v15 M440.5,15V0 M21,324.6v15 M21,15V0 M446.5,318.6h15 M15,318.6H0 M446.5,21h15 M15,21H0"/>
</g>
</svg>