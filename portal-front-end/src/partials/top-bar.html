<nav id="navigation-bar" class="navbar">
    <div class="nav-container">
        <div class="navbar-header">
            <a href="javascript:void(0);" class="bars"></a>

            <div class="navbar-mobile-club-id">
                <button class="search-toggle-mobile">
                  <svg width="18px" height="18px" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.25 15.25L11.5 11.5M0.75 7C0.75 3.54822 3.54822 0.75 7 0.75C10.4518 0.75 13.25 3.54822 13.25 7C13.25 10.4518 10.4518 13.25 7 13.25C3.54822 13.25 0.75 10.4518 0.75 7Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                </button>
                <div class="navbar-notifications-container loading"></div>
                <div class="profile-dropdown-container-mobile"></div>
            </div>
            <a class="navbar-logo" href="/">
                <div class="d-flex gap-1">
                    <img class="hide-tablet org-logo" onerror="this.src='/assets/images/default-org-logo.svg'" />
                    <img src="/assets/images/ph-logo-sm.svg" class="hide-desktop" alt="Performance Hub" />
                    <img src="/assets/images/ph-logo.svg" class="hide-tablet ph-logo" alt="Performance Hub" />
                </div>
            </a>
        </div>

        <div class="faux-text-searchbar" id="algoliaInstantSearch"><svg width="15px" height="15px" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.25 15.25L11.5 11.5M0.75 7C0.75 3.54822 3.54822 0.75 7 0.75C10.4518 0.75 13.25 3.54822 13.25 7C13.25 10.4518 10.4518 13.25 7 13.25C3.54822 13.25 0.75 10.4518 0.75 7Z" stroke="#070D1D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
            <div class="search-placeholder"><span class="to-translate">Search Performance Hub</span>...</div><div class="keyboard-shortcut"></div>
        </div>

        <!-- <div id="autocomplete"></div> -->

        <ul class="nav navbar-nav navbar-right hide-mobile d-flex align-center">
            <li>
                <button class="search-toggle-mobile">
                    <svg width="18px" height="18px" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.25 15.25L11.5 11.5M0.75 7C0.75 3.54822 3.54822 0.75 7 0.75C10.4518 0.75 13.25 3.54822 13.25 7C13.25 10.4518 10.4518 13.25 7 13.25C3.54822 13.25 0.75 10.4518 0.75 7Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                </button>
            </li>
            <li class="navbar-notifications-container loading"></li>
            <li class="">
                <div class="club-spoof-wrapper">
                    <select class="form-control club-spoof-select" data-live-search="true"></select>
                </div>
            </li>
            <li class="">
                <button type="button" class="change-org-btn" data-toggle="tooltip" title="Change Organisation">
                    <i style="font-size: 20px;" class="fa-solid fa-sitemap"></i>
                </button>
            </li>
            <li class="profile-dropdown-container"></li>
        </ul>
    </div>
</nav>

<div class="navbar-stage" style="background-color: rgb(200, 72, 1); visibility: hidden; top: 70px;position: fixed;left: 0px;width: 100vw;z-index: 1081; overflow: hidden;">
    <div class="db-ChromeHeader-sandboxesBannerContent ⚙  as6 ⚙1411r8g" data-testid="test-data-banner-content">
        <div class="sn-1c37ise sn-1lcnaes sn-1l2bwae">
            <div clas="row" style="padding:8px; height: 36px;">
                <div class="col-sm-10">
                    <span style="color: white;" class="stage-text to-translate">You're viewing the Performance Hub in Dev Mode.</span>
                </div>
                <div class="col-sm-2 text-right hidden-xs hidden-sm">
                    <a  class="to-translate btn btn-xs btn-danger btn-outline" style="zoom: 0.85" href="https://app.performancehub.co/">View Production Environment</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="locked-out-banner hidden" style="background-color: rgb(200, 72, 1); top: 70px;position: fixed;left: 0px;width: 100vw;z-index: 1081; overflow: hidden;">
    <div class="sn-1c37ise sn-1lcnaes sn-1l2bwae">
        <div class="d-flex justify-between" style="padding:8px;">
            <span style="color: white;" class="locked-out-text"></span>
            <div class="d-flex gap-1 text-right hidden-xs hidden-sm actions">
                <a  class="to-translate btn btn-xs btn-danger btn-outline" style="zoom: 0.85" href="/saas-billing/payment-settings">Payment Settings</a>
                <a  class="to-translate btn btn-xs btn-danger btn-outline" style="zoom: 0.85" href="/agreements">Agreements</a>
            </div>
        </div>
    </div>
</div>
