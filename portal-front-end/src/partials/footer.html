<!-- Real User Monitoring (Graylog Beacon API) -->
<script defer src="/assets/js/rum.js" type="text/javascript"></script>

<!-- Load Application JS Files ...  -->
<script defer src="/assets/js/libraries.js" type="text/javascript"></script>
<script defer src="/assets/js/app.js" type="text/javascript"></script>

<!-- Adding the file location here so it will be cache-busted -->
<script>const pdfjsWorkerFile = '/assets/js/pdf.worker.min.js';</script>