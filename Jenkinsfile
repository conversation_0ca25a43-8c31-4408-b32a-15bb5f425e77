pipeline {
    agent any

    environment {

        REPO                    = "docker-registry.aws.gymsystems.co"
        IMAGE                   = "${REPO}/backoffice/wiki"

        TAG_ID                  = sh(returnStdout: true, script: "git log -1 --oneline --pretty=%h").trim()
        GIT_COMMITTER_NAME      = sh(returnStdout: true, script: "git show -s --pretty=%an").trim()
        GIT_MESSAGE             = sh(returnStdout: true, script: "git show -s --pretty=%s").trim()

        app                     = ""
    }

    stages {

        stage("Build Asset Image (file upload) service") {
            steps {
                script {
                    app = docker.build("${IMAGE}:${TAG_ID}", "-f Dockerfile .")
                    app.push()
                }
            }
        }

        stage("Push to registry") {
            parallel {
                stage('Staging') {
                    when {
                        branch 'master'
                    }
                    steps {
                        script {

                            // Push the same image we've tested and are about to deploy to lambda as ':latest' in our docker rep.
                            // We're using 'watchtower' to auto-deploy the container once it's built and pushed into the docker registry...
                            app.push("staging")
                        }
                    }
                }

                stage('Production') {
                    when {
                        branch 'production'
                    }
                    steps {
                        script {
                            app.push("production")
                            app.push("latest")
                        }
                    }
                }
            }

        }
    }

    post {
        success {
            slackSend color: "#03CC00", message: "*Build Succeeded (${env.BUILD_NUMBER})*\n Job: ${env.JOB_NAME}\n Commit: ${env.GIT_MESSAGE}\n Author: ${env.GIT_COMMITTER_NAME}\n <${env.RUN_DISPLAY_URL}|Open Jenkins Log>"
        }
        failure {
            slackSend color: "#FF0000", message: "*Build Failed (${env.BUILD_NUMBER})*\n Job: ${env.JOB_NAME}\n Commit: ${env.GIT_MESSAGE}\n Author: ${env.GIT_COMMITTER_NAME}\n <${env.RUN_DISPLAY_URL}|Open Jenkins Log>"
        }
    }
}