version: '3.4'
services:

  openkb-db:
    image: mongo:4.4
    container_name: openkb-db
    privileged: true
    # restart: always
    command: mongod --port 27019 --logpath /dev/null --oplogSize 128 --quiet
    networks:
      - portal-net
    ports:
      - 27019:27019
    volumes:
      - ./openkb-data:/data/db
      - ./openkb-data-dump:/dump

    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo openkb-db:27019/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s

  openkb:
    image: docker-registry.aws.gymsystems.co/backoffice/wiki:production
    container_name: openkb

    restart: always
    privileged: true

    # Now exposed via SSO service...
    ports:
      - "4444:4444"

    environment:
      # set to 'staging' on stage servers...
      - NODE_ENV=${NODE_ENV:-production}

    volumes:
      # Database files & uploads are to be persisted to parent host's disk for backup.
      - ./data:/var/openKB/data
      - ./uploads:/var/openKB/public/uploads

      # Wiki config should be mapped in, however themes should be build & deployed via jenkins now.
      # We only map the one config file in - otherwise the wiki-config/environment/* files won't be INSIDE the docker container which are required so config can be deployed via jenkins...
      - ./wiki-config/config.json:/var/openKB/config/config.json

    networks:
      - portal-net

    healthcheck:
      test: curl -sS http://127.0.0.1:4444/healthcheck || exit 1
      interval: 30s
      timeout: 10s
      retries: 3


networks:
  portal-net:
    external: true
