# 12RND Wiki - Developer Guide

## Project Overview

This is a customized version of openKB, a Node.js-based Markdown knowledge base/FAQ/Wiki application. The project is specifically configured for 12RND with custom theming and multi-tenant support.

### Key Technologies
- **Backend**: Node.js, Express.js
- **Database**: MongoDB (v4.4)
- **Frontend**: Handlebars templating, Bootstrap, SCSS
- **Search**: Algolia integration
- **AI**: OpenAI integration for content analysis
- **Process Management**: PM2
- **Build Tools**: Gulp, Docker
- **CI/CD**: Jenkins pipeline

## Project Structure

### Root Directory
- `src/` - Main application source code
- `wiki-config/` - Configuration files for different environments
- `wiki-themes/` - Custom themes (primarily 12rnd theme)
- `docker-compose-dev.yml` - Development Docker configuration
- `docker-compose-prd.yml` - Production Docker configuration
- `Dockerfile` - Multi-stage Docker build
- `Jenkinsfile` - CI/CD pipeline configuration

### Key Source Directories
- `src/app.js` - Main application entry point
- `src/routes/` - Express route handlers and API endpoints
- `src/lib/` - Core libraries (database, Algolia, migrations)
- `src/views/` - Handlebars templates
- `src/public/` - Static assets (CSS, JS, images)
- `src/locales/` - Internationalization files
- `src/migrate/` - Database migration scripts
- `src/utils/` - Utility functions (OpenAI service)

## Development Setup

### Prerequisites
- Docker Desktop
- Node.js (>=8.0.0)
- npm

### Quick Start
```bash
# Clone the repository
<NAME_EMAIL>:12rnd-UBX/wiki.git
cd wiki

# Start development environment
npm start

# Access the application
# Main app: http://localhost:8080/openkb
# Login: http://localhost:8080/openkb/login
# BrowserSync: http://localhost:7000 (with live reload)
```

### Development Commands
- `npm start` - Start development environment with Docker
- `npm run down` - Stop development environment
- `cd src && npm run build` - Build assets (SCSS compilation, translations)
- `cd src && npm run watch` - Watch mode with BrowserSync live reload

## Configuration

### Environment Configuration
Configuration is managed through YAML files in multiple locations:
- `wiki-config/config.json` - Main application settings
- `wiki-config/environment/` - Environment-specific configs
- `src/config/environment/` - Legacy environment configs

### Key Configuration Settings
- **Database**: MongoDB connection via `MONGO_CONN`, `MONGO_DBNAME`
- **Theme**: Set to "12rnd" for custom styling
- **Multi-tenant**: Supports tenant-specific databases
- **Algolia**: Search integration with tenant-specific indexes
- **OpenAI**: Content analysis and changelog generation

### Environment Variables
- `NODE_ENV` - Environment (development/staging/production)
- `MONGO_CONN` - MongoDB connection string
- `MONGO_DBNAME` - Database name
- `ALGOLIA_APP_ID`, `ALGOLIA_API_READ_KEY`, `ALGOLIA_API_WRITE_KEY` - Algolia search
- `OPENAI_API_KEY` - OpenAI integration
- `PERF_HUB_URL`, `PERF_HUB_API_KEY` - Performance hub integration

## Architecture

### Multi-Tenant Support
- Uses tenant-specific MongoDB databases
- Algolia indexes are tenant-prefixed
- Global store manages tenant context
- Database connections are cached with LRU cache

### Database Collections
- `kb` - Knowledge base articles
- `users` - User accounts
- `topics` - Article categorization
- `votes` - Article voting system
- `comments` - Article comments
- `pageviews` - Analytics data
- `searchtotals` - Search analytics
- `migration` - Database migration tracking

### Search Integration
- Algolia provides full-text search
- Articles are automatically synced to Algolia
- Regional visibility filtering supported
- Custom ranking by view count

## Development Best Practices

### Code Organization
- Use service pattern for business logic (`src/routes/services/`)
- Keep route handlers thin, delegate to services
- Centralize database operations in `src/lib/db.js`
- Use common utilities in `src/routes/common.js`

### Theming
- Custom theme located in `wiki-themes/12rnd/`
- SCSS files in `wiki-themes/12rnd/scss/`
- Handlebars templates in `wiki-themes/12rnd/views/`
- Use Gulp for SCSS compilation and asset building

### Database Operations
- Always use the `getDb()` function from `src/lib/db.js`
- Handle tenant context properly for multi-tenant operations
- Use proper MongoDB ObjectId handling with `common.getId()`

### Internationalization
- Translation files in `src/locales/`
- Use `application-translator` package for dynamic translations
- Build translations with `npx build-translations`

### Error Handling
- Use proper error logging with console.error
- Return appropriate HTTP status codes
- Handle database connection failures gracefully

## Build and Deployment

### Docker Build Process
1. **Builder stage**: Install dependencies with build tools
2. **Runtime stage**: Copy application files and dependencies
3. **Asset building**: Compile SCSS and build translations
4. **PM2 configuration**: Use ecosystem config for process management

### CI/CD Pipeline (Jenkins)
- **Build**: Docker image creation with git commit hash tagging
- **Deploy**: Automatic deployment to staging (master branch) and production (production branch)
- **Notifications**: Slack integration for build status

### Production Considerations
- Use PM2 for process management
- MongoDB runs on custom port 27019
- Health checks configured for both app and database
- Persistent volumes for data and uploads
- External network configuration for service communication

## File Naming Conventions

### Templates
- Use `.hbs` extension for Handlebars templates
- Layout files in `views/layouts/`
- Partial templates in `views/partials/`
- Theme-specific overrides in `wiki-themes/12rnd/views/`

### Assets
- SCSS files use underscore prefix for partials (`_utils.scss`)
- Compiled CSS goes to `wiki-themes/12rnd/css/`
- JavaScript files in `wiki-themes/12rnd/js/`
- Images in `wiki-themes/12rnd/images/`

### Services
- Service files follow pattern: `get[Entity]Service.js`
- Admin services in `src/routes/services/admin/`
- Use descriptive names that indicate the operation

## Common Gotchas

### Development Environment
- BrowserSync proxy runs on port 7000, app on 4444
- SCSS compilation requires Gulp watch mode
- Database runs on port 27019 (not default 27017)
- Volume mounts ensure data persistence

### Multi-Tenant Issues
- Always check tenant context when accessing database
- Algolia index names are tenant-specific
- Database connections are cached per tenant

### Asset Building
- SCSS changes require Gulp compilation
- Translation changes need `build-translations` command
- Production builds minify and optimize assets

### Configuration Management
- Environment configs override base config
- Docker environment variables take precedence
- Theme settings affect view resolution

## Testing and Debugging

### Local Development
- Use `docker-compose logs openkb` to view application logs
- MongoDB accessible at `localhost:27019`
- BrowserSync provides live reload at `localhost:7000`

### Production Debugging
- PM2 logs available in container
- Health check endpoints: `/healthcheck`
- Database health checks configured in Docker Compose

## Security Considerations

### Authentication
- Basic auth for API endpoints
- Session management with MongoDB store
- Password hashing with bcrypt

### API Security
- API token authentication for external access
- Basic auth credentials for Algolia sync
- Environment-specific API keys

### Data Protection
- Tenant isolation at database level
- Regional visibility controls for articles
- Secure file upload handling